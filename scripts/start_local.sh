#!/bin/bash
#
# Start local development environment
# Uses .env.local configuration for localhost services
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Local Development Environment${NC}"
echo "=============================================="
echo

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📂 Project root: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    print_error ".env.local not found!"
    echo "Creating .env.local from .env.docker template..."
    if [ -f ".env.docker" ]; then
        cp ".env.docker" ".env.local"
        # Update for local development
        sed -i.bak 's/redis:6379/localhost:6379/g' ".env.local"
        sed -i.bak 's/postgres:5432/localhost:5432/g' ".env.local"
        sed -i.bak 's/FLASK_ENV=production/FLASK_ENV=development/g' ".env.local"
        sed -i.bak 's/FLASK_DEBUG=false/FLASK_DEBUG=true/g' ".env.local"
        sed -i.bak 's/RATE_LIMIT_ENABLED=true/RATE_LIMIT_ENABLED=false/g' ".env.local"
        rm ".env.local.bak"
        print_status "Created .env.local for local development"
    else
        print_error "No template file found. Please create .env.local manually."
        exit 1
    fi
fi

print_status "Using .env.local for local development"

# Check if virtual environment is activated
if [ -z "$VIRTUAL_ENV" ]; then
    print_warning "Virtual environment not activated, attempting to activate..."
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
        print_status "Virtual environment activated"
    else
        print_error "Virtual environment not found at .venv/"
        echo "Please run: uv venv && source .venv/bin/activate"
        exit 1
    fi
else
    print_status "Virtual environment already activated"
fi

# Check if Redis is running
echo
echo "🔗 Checking Redis connection..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        print_status "Redis is running"
    else
        print_error "Redis is not responding"
        echo "Starting Redis with Docker:"
        docker run -d -p 6379:6379 --name redis-local redis:alpine
        sleep 2
        if redis-cli ping &> /dev/null; then
            print_status "Redis started successfully"
        else
            print_error "Failed to start Redis"
            exit 1
        fi
    fi
else
    print_warning "redis-cli not found, attempting to start Redis with Docker"
    docker run -d -p 6379:6379 --name redis-local redis:alpine
    sleep 2
fi

# Set environment to use local config
export FLASK_ENV=development
export ENV_FILE=".env.local"

echo
echo "🚀 Starting local development services..."
echo "   Environment: local development"
echo "   Config file: .env.local"
echo "   Redis: localhost:6379"
echo "   Database: SQLite (local)"
echo

# Start the application
print_status "Starting Flask application..."
echo "Access the application at: http://localhost:5001"
echo "Press Ctrl+C to stop all services"
echo

# Load environment from .env.local and start Flask
set -a
source .env.local
set +a

exec uv run python -m src.main