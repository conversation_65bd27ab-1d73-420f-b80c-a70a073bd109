#!/usr/bin/env python3
"""Quick test script to verify Azure API connection."""

import requests
from dotenv import load_dotenv

from src.features.sora_integration.client import SoraClient

load_dotenv()


def test_azure_connection():
    """Test Azure OpenAI API connection."""
    try:
        client = SoraClient()
        print(f"Endpoint: {client.endpoint}")
        print(f"Base URL: {client.base_url}")
        print(f"Deployment: {client.deployment}")
        print(f"Headers: {list(client.headers.keys())}")

        # Test basic connectivity
        health_url = f"{client.endpoint}/openai/deployments/{client.deployment}/models"
        print(f"\nTesting connectivity to: {health_url}")

        response = requests.get(health_url, headers=client.headers)
        print(f"Status: {response.status_code}")

        if response.status_code != 200:
            print(f"Error response: {response.text}")

        # Test video generation endpoint using SoraClient method
        print("\nTesting video endpoint using SoraClient...")

        # Try creating a job using the SoraClient
        try:
            job = client.create_video_job("A simple test video")
            print(f"Job creation successful: {job.status}")
            print(f"Job ID: {job.id}")
            if job.generation_id:
                print(f"Generation ID: {job.generation_id}")
            if job.error_message:
                print(f"Error message: {job.error_message}")
        except Exception as e:
            print(f"Job creation failed: {e}")

        # Direct test with proper API version
        video_url = f"{client.base_url}/jobs"
        print(f"\nDirect test to: {video_url}")

        payload = {
            "prompt": "test",
            "model": "sora",
            "width": 1280,
            "height": 720,
            "n_seconds": 5,
        }

        params = {"api-version": client.api_version}
        response = requests.post(
            video_url, json=payload, headers=client.headers, params=params
        )
        print(f"Direct test status: {response.status_code}")
        print(f"Direct test response: {response.text}")

        # Show retry information if rate limited
        if response.status_code == 429:
            retry_after = response.headers.get("Retry-After")
            if retry_after:
                print(f"Retry after: {retry_after} seconds")
            else:
                print("Rate limited - no retry-after header provided")

        # Show all response headers for debugging
        print("\nResponse headers:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"Connection test failed: {e}")


if __name__ == "__main__":
    test_azure_connection()
