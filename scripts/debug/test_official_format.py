#!/usr/bin/env python3
"""Test script that exactly matches the official Azure OpenAI Sora documentation."""

import os
import time

import requests
from dotenv import load_dotenv

load_dotenv()


def test_official_format():
    """Test using the exact format from Microsoft documentation."""

    # Exact format from docs
    endpoint = os.environ["AZURE_OPENAI_ENDPOINT"].rstrip("/")
    api_key = os.environ["AZURE_OPENAI_API_KEY"]
    api_version = "preview"

    headers = {"api-key": api_key, "Content-Type": "application/json"}

    print(f"Endpoint: {endpoint}")
    print(f"API Version: {api_version}")
    print(f"Headers: {list(headers.keys())}")

    # 1. Create video generation job (exact format from docs)
    create_url = (
        f"{endpoint}/openai/v1/video/generations/jobs?api-version={api_version}"
    )

    # Exact payload from Microsoft docs
    body = {
        "prompt": "A cat playing piano in a jazz bar.",
        "width": 480,
        "height": 480,
        "n_seconds": 5,
        "model": "sora",
    }

    print(f"\nCreating job at: {create_url}")
    print(f"Payload: {body}")

    try:
        response = requests.post(create_url, headers=headers, json=body)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code in [200, 201]:
            job_data = response.json()
            job_id = job_data["id"]
            print(f"✅ Job created successfully: {job_id}")

            # 2. Poll for status (from docs)
            status_url = f"{endpoint}/openai/v1/video/generations/jobs/{job_id}?api-version={api_version}"
            print(f"\nPolling status at: {status_url}")

            status = None
            poll_count = 0
            max_polls = 60  # 5 minutes of polling

            while (
                status not in ("succeeded", "failed", "cancelled")
                and poll_count < max_polls
            ):
                time.sleep(5)
                poll_count += 1

                status_response = requests.get(status_url, headers=headers)
                print(f"Poll {poll_count} - Status Code: {status_response.status_code}")

                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get("status")
                    print(f"Status: {status}")

                    if status == "succeeded":
                        print("✅ Job succeeded!")
                        print(f"Full response: {status_data}")

                        # 3. Try to get video (from docs)
                        generations = status_data.get("generations", [])
                        if generations:
                            generation_id = generations[0].get("id")
                            print(f"Generation ID: {generation_id}")

                            video_url = f"{endpoint}/openai/v1/video/generations/{generation_id}/content/video?api-version={api_version}"
                            print(f"Video URL: {video_url}")

                            video_response = requests.get(video_url, headers=headers)
                            print(
                                f"Video download status: {video_response.status_code}"
                            )

                            if video_response.ok:
                                print(
                                    f"✅ Video downloaded successfully! Size: {len(video_response.content)} bytes"
                                )
                                return True
                            else:
                                print(
                                    f"❌ Video download failed: {video_response.text}"
                                )
                        else:
                            print("❌ No generations in response")
                    elif status == "failed":
                        print(f"❌ Job failed: {status_data}")
                        return False
                else:
                    print(f"❌ Status poll failed: {status_response.text}")

            if poll_count >= max_polls:
                print("❌ Polling timeout")
                return False

        else:
            print(f"❌ Job creation failed: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


if __name__ == "__main__":
    success = test_official_format()
    print(f"\nOverall result: {'✅ SUCCESS' if success else '❌ FAILED'}")
