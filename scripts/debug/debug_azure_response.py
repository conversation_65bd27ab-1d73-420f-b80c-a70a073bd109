#!/usr/bin/env python3
"""Debug script to see what Azure API returns for a completed job."""

import json

from dotenv import load_dotenv

from src.features.sora_integration.client import SoraClient

load_dotenv()


def debug_azure_response():
    """Debug Azure API response format."""
    try:
        client = SoraClient()

        # Create a job first
        print("Creating a test job...")
        job = client.create_video_job("A simple debug test")
        print(f"Job created: {job.id}, Generation ID: {job.generation_id}")

        if job.status == "failed":
            print(f"Job failed: {job.error_message}")
            return

        print("Waiting for job to complete...")
        import time

        # Poll until completion
        for _i in range(20):  # Max 10 minutes
            time.sleep(30)  # Wait 30 seconds between polls

            # Get raw response to see the structure
            status_url = f"{client.base_url}/jobs/{job.generation_id}"
            print(f"\nPolling: {status_url}")

            response = client._make_request("GET", status_url)
            response_data = response.json()

            print(f"Raw response: {json.dumps(response_data, indent=2)}")

            status = response_data.get("status", "unknown")
            print(f"Status: {status}")

            if status in ["succeeded", "failed"]:
                print(f"Job completed with status: {status}")

                if status == "succeeded":
                    result = response_data.get("result")
                    print(
                        f"Result structure: {json.dumps(result, indent=2) if result else 'None'}"
                    )

                    if result and isinstance(result, dict):
                        url = result.get("url")
                        print(f"Video URL: {url}")

                        if url:
                            print("Video URL found! This should trigger download.")
                        else:
                            print("No video URL in result")
                    else:
                        print("No result object or result is not a dict")

                break

            print(f"Still {status}, waiting...")

    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    debug_azure_response()
