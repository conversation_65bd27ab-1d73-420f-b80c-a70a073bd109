#!/bin/bash
#
# Clean Celery worker startup script that resolves environment conflicts.
#
# This script ensures a clean environment before starting Celery workers,
# preventing configuration conflicts from exported environment variables.
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Clean Celery Worker Startup${NC}"
echo "=================================="
echo

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📂 Project root: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# Check for environment-specific .env file
ENV_FILE=".env.local"
if [ ! -f "$ENV_FILE" ]; then
    if [ -f ".env" ]; then
        print_warning ".env.local not found, using .env"
        ENV_FILE=".env"
    else
        print_error "No environment file found!"
        echo "Please create .env.local file for local development."
        echo "Example: cp .env.example .env.local"
        exit 1
    fi
fi

print_status "Using environment file: $ENV_FILE"

# Check if virtual environment is activated
if [ -z "$VIRTUAL_ENV" ]; then
    print_warning "Virtual environment not activated, attempting to activate..."
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
        print_status "Virtual environment activated"
    else
        print_error "Virtual environment not found at .venv/"
        echo "Please run: uv venv && source .venv/bin/activate"
        exit 1
    fi
else
    print_status "Virtual environment already activated"
fi

# Unset problematic environment variables that might override .env
print_status "Clearing environment variables that might conflict with .env..."

# List of Azure variables that should come from .env, not environment
AZURE_VARS=(
    "AZURE_OPENAI_API_VERSION"
    "AZURE_OPENAI_ENDPOINT" 
    "AZURE_OPENAI_API_KEY"
    "AZURE_OPENAI_DEPLOYMENT_NAME"
)

for var in "${AZURE_VARS[@]}"; do
    if [ ! -z "${!var}" ]; then
        print_warning "Unsetting $var (was: ${!var})"
        unset $var
    fi
done

# Also unset other common problematic variables
OTHER_VARS=(
    "FLASK_ENV"
    "CELERY_BROKER_URL"
    "CELERY_RESULT_BACKEND"
    "REDIS_HOST"
    "REDIS_PORT"
)

for var in "${OTHER_VARS[@]}"; do
    if [ ! -z "${!var}" ]; then
        print_warning "Unsetting $var (was: ${!var})"
        unset $var
    fi
done

print_status "Environment cleaned"

# Validate environment configuration
echo
echo "🔍 Validating configuration..."

# Check if validation script exists and run it
if [ -f "scripts/check_environment.py" ]; then
    if uv run python scripts/check_environment.py; then
        print_status "Configuration validation passed"
    else
        exit_code=$?
        if [ $exit_code -eq 2 ]; then
            print_warning "Configuration conflicts detected but resolved"
        else
            print_error "Configuration validation failed"
            exit 1
        fi
    fi
else
    print_warning "Environment validation script not found, skipping validation"
fi

# Test Redis connection
echo
echo "🔗 Testing Redis connection..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        print_status "Redis is running"
    else
        print_error "Redis is not responding"
        echo "Please start Redis:"
        echo "  redis-server"
        echo "  # or with Docker:"
        echo "  docker run -p 6379:6379 redis:alpine"
        exit 1
    fi
else
    print_warning "redis-cli not found, skipping Redis connection test"
fi

# Parse command line arguments
CONCURRENCY=${1:-4}
LOGLEVEL=${2:-info}
QUEUES=${3:-"video_generation,maintenance"}

echo
echo "🚀 Starting Celery worker with clean environment..."
echo "   Concurrency: $CONCURRENCY"
echo "   Log Level: $LOGLEVEL"
echo "   Queues: $QUEUES"
echo

# Start Celery worker with clean environment
print_status "Celery worker starting..."
exec uv run celery -A src.job_queue.celery_app worker \
    --loglevel="$LOGLEVEL" \
    --concurrency="$CONCURRENCY" \
    --queues="$QUEUES" \
    --hostname="worker@%h" \
    --time-limit=1800 \
    --soft-time-limit=1500