#!/usr/bin/env python3
"""
Setup script for real Google Veo3 API authentication and testing.

This script helps you configure and test real Google Veo3 API access.
"""

import json
import os
import sys
from pathlib import Path


def setup_service_account():
    """Interactive setup for service account authentication."""
    print("🔐 Google Veo3 Service Account Setup")
    print("=" * 50)

    print("\n📋 Prerequisites:")
    print("1. You need a service account JSON key file from Google Cloud Console")
    print("2. The service account should have 'AI Platform User' role")
    print("3. Project ID should be: ************")

    # Get service account key path
    key_path = input("\n📁 Enter path to your service account JSON key file: ").strip()

    if not key_path:
        print("❌ No path provided. Exiting.")
        return False

    # Expand user path
    key_path = os.path.expanduser(key_path)

    if not os.path.exists(key_path):
        print(f"❌ File not found: {key_path}")
        return False

    # Validate JSON key file
    try:
        with open(key_path) as f:
            key_data = json.load(f)

        required_fields = [
            "type",
            "project_id",
            "private_key_id",
            "private_key",
            "client_email",
        ]
        missing_fields = [field for field in required_fields if field not in key_data]

        if missing_fields:
            print(f"❌ Invalid service account key. Missing fields: {missing_fields}")
            return False

        if key_data.get("type") != "service_account":
            print(
                f"❌ Invalid key type. Expected 'service_account', got: {key_data.get('type')}"
            )
            return False

        project_id = key_data.get("project_id")
        client_email = key_data.get("client_email")

        print("✅ Valid service account key found")
        print(f"📋 Project ID: {project_id}")
        print(f"📋 Service Account: {client_email}")

        if project_id != "************":
            print(
                f"⚠️  Warning: Project ID mismatch. Expected: ************, Got: {project_id}"
            )
            proceed = input("Continue anyway? (y/N): ").strip().lower()
            if proceed != "y":
                return False

    except json.JSONDecodeError:
        print(f"❌ Invalid JSON file: {key_path}")
        return False
    except Exception as e:
        print(f"❌ Error reading key file: {e}")
        return False

    # Update .env.local
    env_local_path = Path(__file__).parent / ".env.local"

    print(f"\n📝 Updating {env_local_path}")

    # Read current .env.local
    env_lines = []
    if env_local_path.exists():
        with open(env_local_path) as f:
            env_lines = f.readlines()

    # Update relevant lines
    updated_lines = []
    credentials_set = False
    mock_set = False
    project_set = False

    for line in env_lines:
        line = line.strip()
        if line.startswith("GOOGLE_APPLICATION_CREDENTIALS="):
            updated_lines.append(f"GOOGLE_APPLICATION_CREDENTIALS={key_path}\n")
            credentials_set = True
        elif line.startswith("USE_MOCK_VEO="):
            updated_lines.append("USE_MOCK_VEO=false\n")
            mock_set = True
        elif line.startswith("GOOGLE_PROJECT_ID="):
            updated_lines.append(f"GOOGLE_PROJECT_ID={project_id}\n")
            project_set = True
        else:
            updated_lines.append(line + "\n" if not line.endswith("\n") else line)

    # Add missing variables
    if not credentials_set:
        updated_lines.append(f"GOOGLE_APPLICATION_CREDENTIALS={key_path}\n")
    if not mock_set:
        updated_lines.append("USE_MOCK_VEO=false\n")
    if not project_set:
        updated_lines.append(f"GOOGLE_PROJECT_ID={project_id}\n")

    # Write updated .env.local
    with open(env_local_path, "w") as f:
        f.writelines(updated_lines)

    print("✅ Environment configuration updated")
    print(f"📋 Service account key: {key_path}")
    print("📋 Mock mode: disabled")
    print(f"📋 Project ID: {project_id}")

    return True


def test_authentication():
    """Test Google Cloud authentication."""
    print("\n🔐 Testing Authentication...")

    try:
        from google.auth import default
        from google.auth.transport.requests import Request

        # Get default credentials
        credentials, project = default(
            scopes=["https://www.googleapis.com/auth/cloud-platform"]
        )

        print("✅ Credentials loaded successfully")
        print(f"📋 Project: {project}")
        print(f"📋 Service Account: {credentials.service_account_email}")

        # Test token refresh
        if not credentials.valid:
            print("🔄 Refreshing credentials...")
            credentials.refresh(Request())

        if credentials.valid and credentials.token:
            print("✅ Authentication successful!")
            print(f"📋 Token type: {type(credentials).__name__}")
            return True
        else:
            print("❌ Failed to get valid token")
            return False

    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False


async def test_veo3_api():
    """Test Google Veo3 API access."""
    print("\n🎬 Testing Google Veo3 API Access...")

    # Add src to path
    sys.path.insert(0, str(Path(__file__).parent / "src"))

    try:
        from src.features.video_generation import (
            VideoGenerationRequest,
            get_provider_factory,
        )

        # Create provider
        factory = get_provider_factory()
        provider = factory.create_provider("google_veo3")

        print(f"✅ Provider created: {provider.provider_name}")

        # Test health check
        health = await provider.health_check()
        print(f"📊 Health check: {health}")

        if health.get("service_healthy", False):
            print("✅ Google Veo3 API is accessible!")

            # Test simple video generation
            print("\n🎬 Testing video generation...")
            request = VideoGenerationRequest(
                prompt="A simple test video for API validation",
                duration=5,
                width=1280,
                height=720,
            )

            response = await provider.generate_video(request)
            print(f"📋 Generation ID: {response.generation_id}")
            print(f"📋 Status: {response.status}")

            if response.status != "failed":
                print("✅ Video generation started successfully!")
                return True
            else:
                print(f"❌ Video generation failed: {response.error_message}")
                return False
        else:
            print("❌ Google Veo3 API health check failed")
            return False

    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Main setup and test workflow."""
    print("🚀 Google Veo3 Real API Setup & Test")
    print("=" * 60)

    # Step 1: Setup service account
    if not setup_service_account():
        print("\n❌ Service account setup failed. Please check your JSON key file.")
        return

    # Step 2: Test authentication
    if not test_authentication():
        print(
            "\n❌ Authentication test failed. Please check your service account permissions."
        )
        return

    # Step 3: Test Veo3 API
    if not await test_veo3_api():
        print("\n❌ Veo3 API test failed. Please check your project configuration.")
        return

    print("\n🎉 SUCCESS! Google Veo3 Real API is configured and working!")
    print("\n🎯 Next Steps:")
    print("1. Run: python test_veo3_real_api.py")
    print("2. Try the web interface: python src/main.py")
    print("3. Generate videos with real Google Veo3 API!")


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
