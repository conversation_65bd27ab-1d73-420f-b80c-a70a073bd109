#!/usr/bin/env python3
"""
Environment validation script to detect and resolve configuration conflicts.

This script helps identify environment variables that might override .env file values,
preventing configuration issues in development and production environments.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# Add project root to Python path for imports
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))


def load_env_file(env_path: Path) -> Dict[str, str]:
    """Load .env file and return key-value pairs."""
    env_vars = {}
    if not env_path.exists():
        return env_vars

    with open(env_path) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                env_vars[key.strip()] = value.strip()

    return env_vars


def check_environment_conflicts() -> List[Tuple[str, str, str]]:
    """Check for conflicts between environment variables and .env file."""
    conflicts = []

    # Load environment-specific .env file
    project_root = Path(__file__).parent.parent

    # Priority order: .env.local -> .env -> .env.docker
    env_file = project_root / ".env.local"
    if not env_file.exists():
        env_file = project_root / ".env"
        if not env_file.exists():
            env_file = project_root / ".env.docker"

    env_vars = load_env_file(env_file)

    print("📂 Checking environment conflicts...")
    print(f"📄 .env file: {env_file}")
    print(f"📋 Found {len(env_vars)} variables in .env file")
    print()

    # Check each .env variable against environment
    for key, expected_value in env_vars.items():
        actual_value = os.getenv(key)

        if actual_value is not None and actual_value != expected_value:
            conflicts.append((key, expected_value, actual_value))
            print(f"⚠️  CONFLICT: {key}")
            print(f"   Expected (.env): {expected_value}")
            print(f"   Actual (env):    {actual_value}")
            print()

    return conflicts


def generate_fix_commands(conflicts: List[Tuple[str, str, str]]) -> List[str]:
    """Generate commands to fix environment conflicts."""
    commands = []

    if conflicts:
        print("🔧 Fix Commands:")
        print("Run these commands to resolve conflicts:")
        print()

        # Unset commands
        for key, expected, actual in conflicts:
            unset_cmd = f"export -n {key}"
            commands.append(unset_cmd)
            print(f"   {unset_cmd}")

        print()
        print("Or add to your shell profile (.bashrc/.zshrc) to make permanent:")
        for key, expected, actual in conflicts:
            print(f"   unset {key}")
        print()

    return commands


def validate_critical_config():
    """Validate critical configuration values."""
    print("🔍 Validating critical configuration...")

    critical_vars = {
        "AZURE_OPENAI_API_VERSION": "preview",
        "AZURE_OPENAI_ENDPOINT": "https://eric-mc4zkcb5-eastus2.openai.azure.com/",
        "AZURE_OPENAI_DEPLOYMENT_NAME": "sora",
    }

    all_good = True

    # Test with clean environment loading
    from src.config.service import ConfigurationService

    ConfigurationService.reload()

    for key, expected in critical_vars.items():
        actual = ConfigurationService.get(key, expected)
        if actual == expected:
            print(f"✅ {key}: {actual}")
        else:
            print(f"❌ {key}: Expected '{expected}', got '{actual}'")
            all_good = False

    print()
    return all_good


def main():
    """Main environment validation function."""
    print("🔧 Environment Configuration Validator")
    print("=" * 50)
    print()

    # Check for conflicts
    conflicts = check_environment_conflicts()

    if conflicts:
        print(f"⚠️  Found {len(conflicts)} environment conflicts!")
        print()

        # Generate fix commands
        fix_commands = generate_fix_commands(conflicts)

        # Validate configuration
        print("🧪 Testing configuration after potential fixes...")
        config_valid = validate_critical_config()

        if not config_valid:
            print("❌ Configuration validation failed!")
            print("Please run the fix commands above and try again.")
            sys.exit(1)
        else:
            print("⚠️  Configuration is valid but conflicts exist.")
            print("Run the fix commands to prevent future issues.")
            sys.exit(2)
    else:
        print("✅ No environment conflicts detected!")
        print()

        # Still validate configuration
        config_valid = validate_critical_config()

        if config_valid:
            print("🎉 All configuration is valid!")
            sys.exit(0)
        else:
            print("❌ Configuration validation failed!")
            sys.exit(1)


if __name__ == "__main__":
    main()
