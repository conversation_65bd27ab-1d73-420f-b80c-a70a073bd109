#!/usr/bin/env python3
"""
Database Synchronization Test Script

This script tests the synchronization between Flask job creation and Celery job processing
to identify the root cause of "Job not found for update" errors.

Usage:
    python scripts/test_database_sync.py
"""

import json
import sqlite3
import time
from datetime import datetime
from typing import Dict, List, Optional

import requests


class DatabaseMonitor:
    """Monitor database state changes in real-time."""

    def __init__(self, db_path: str = "sora_poc.db"):
        self.db_path = db_path
        self.monitoring = False

    def get_job_by_id(self, job_id: str) -> Optional[Dict]:
        """Get job by ID from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT id, prompt, status, created_at, completed_at, generation_id, 
                       error_message, file_path, session_id, priority, queue_position, 
                       retry_count, updated_at
                FROM video_jobs 
                WHERE id = ?
            """,
                (job_id,),
            )

            row = cursor.fetchone()
            if row:
                return dict(row)
            return None

        except sqlite3.Error as e:
            print(f"❌ Database error: {e}")
            return None
        finally:
            conn.close()

    def get_all_jobs(self) -> List[Dict]:
        """Get all jobs from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, prompt, status, created_at, completed_at, generation_id,
                       error_message, file_path, session_id, priority, queue_position,
                       retry_count, updated_at
                FROM video_jobs 
                ORDER BY created_at DESC
            """)

            return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"❌ Database error: {e}")
            return []
        finally:
            conn.close()

    def monitor_job_lifecycle(self, job_id: str, timeout: float = 30.0) -> Dict:
        """Monitor a job's lifecycle from creation to completion."""
        print(f"🔍 Monitoring job {job_id} lifecycle...")

        states = []
        start_time = time.time()

        while time.time() - start_time < timeout:
            job = self.get_job_by_id(job_id)
            current_time = time.time()

            state = {
                "timestamp": current_time,
                "elapsed": current_time - start_time,
                "job_found": job is not None,
                "job_data": job,
            }

            states.append(state)

            if job:
                print(f"📊 [{state['elapsed']:.2f}s] Job found: {job['status']}")
                if job["status"] in ["succeeded", "failed"]:
                    print(f"✅ Job {job_id} reached final state: {job['status']}")
                    break
            else:
                print(f"❌ [{state['elapsed']:.2f}s] Job not found in database")

            time.sleep(0.5)  # Check every 500ms

        return {
            "job_id": job_id,
            "states": states,
            "total_time": time.time() - start_time,
        }


class FlaskAPITester:
    """Test the Flask API endpoints."""

    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url
        self.session = requests.Session()

    def test_api_health(self) -> bool:
        """Test if the API is responding."""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False

    def create_video_job(self, prompt: str = "test horses") -> Optional[str]:
        """Create a video generation job and return the job ID."""
        try:
            payload = {
                "prompt": prompt,
                "duration": "2",
                "width": "1280",
                "height": "720",
                "model": "sora",
            }

            print(f"🚀 Creating video job: {prompt}")
            print(f"📤 Payload: {json.dumps(payload, indent=2)}")

            response = self.session.post(
                f"{self.base_url}/generate",
                data=payload,  # Use form data instead of JSON
                timeout=10,
            )

            print(f"📥 Response Status: {response.status_code}")
            print(f"📥 Response Headers: {dict(response.headers)}")

            if response.status_code == 200:
                data = response.json()
                print(f"📥 Response Data: {json.dumps(data, indent=2)}")

                if data.get("success") and data.get("data", {}).get("job_id"):
                    return data["data"]["job_id"]
                else:
                    print("❌ API returned success=False or missing job_id")
                    print(f"❌ Response structure: {data}")
                    return None
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"❌ Response: {response.text}")
                return None

        except requests.RequestException as e:
            print(f"❌ Request error: {e}")
            return None

    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get job status from the API."""
        try:
            response = self.session.get(f"{self.base_url}/status/{job_id}", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except requests.RequestException:
            return None


class SynchronizationTester:
    """Test database synchronization between Flask and Celery."""

    def __init__(self):
        self.db_monitor = DatabaseMonitor()
        self.api_tester = FlaskAPITester()
        self.results = []

    def test_single_job_sync(self, prompt: str = "test sync") -> Dict:
        """Test synchronization for a single job."""
        print("\n🧪 Testing single job synchronization...")
        print(f"📝 Prompt: {prompt}")

        # Record initial database state
        initial_jobs = self.db_monitor.get_all_jobs()
        print(f"📊 Initial jobs in database: {len(initial_jobs)}")

        # Create job via API
        job_creation_start = time.time()
        job_id = self.api_tester.create_video_job(prompt)
        job_creation_end = time.time()

        if not job_id:
            return {
                "success": False,
                "error": "Failed to create job via API",
                "job_id": None,
            }

        print(f"✅ Job created with ID: {job_id}")
        print(f"⏱️  Job creation took: {job_creation_end - job_creation_start:.3f}s")

        # Monitor job lifecycle
        lifecycle_data = self.db_monitor.monitor_job_lifecycle(job_id, timeout=60.0)

        # Analyze results
        result = {
            "success": True,
            "job_id": job_id,
            "prompt": prompt,
            "job_creation_time": job_creation_end - job_creation_start,
            "lifecycle_data": lifecycle_data,
            "analysis": self._analyze_lifecycle(lifecycle_data),
        }

        self.results.append(result)
        return result

    def _analyze_lifecycle(self, lifecycle_data: Dict) -> Dict:
        """Analyze job lifecycle data to identify synchronization issues."""
        states = lifecycle_data["states"]

        # Find when job first appeared in database
        first_found = None
        for state in states:
            if state["job_found"]:
                first_found = state
                break

        # Find status transitions
        status_transitions = []
        prev_status = None

        for state in states:
            if state["job_found"]:
                current_status = state["job_data"]["status"]
                if current_status != prev_status:
                    status_transitions.append(
                        {
                            "timestamp": state["timestamp"],
                            "elapsed": state["elapsed"],
                            "status": current_status,
                        }
                    )
                    prev_status = current_status

        # Calculate key metrics
        analysis = {
            "job_appeared_in_db": first_found is not None,
            "time_to_appear": first_found["elapsed"] if first_found else None,
            "status_transitions": status_transitions,
            "total_monitoring_time": lifecycle_data["total_time"],
            "final_status": states[-1]["job_data"]["status"]
            if states and states[-1]["job_found"]
            else None,
        }

        # Identify potential issues
        issues = []

        if not first_found:
            issues.append("Job never appeared in database")
        elif first_found["elapsed"] > 2.0:
            issues.append(
                f"Job took {first_found['elapsed']:.2f}s to appear in database (too slow)"
            )

        if not status_transitions:
            issues.append("No status transitions observed")
        elif len(status_transitions) < 2:
            issues.append("Job did not progress through expected states")

        analysis["issues"] = issues
        return analysis

    def test_concurrent_jobs(self, num_jobs: int = 3) -> List[Dict]:
        """Test synchronization with multiple concurrent jobs."""
        print(f"\n🧪 Testing concurrent job synchronization ({num_jobs} jobs)...")

        results = []

        for i in range(num_jobs):
            prompt = f"concurrent test {i + 1}"
            result = self.test_single_job_sync(prompt)
            results.append(result)

            # Brief pause between jobs
            time.sleep(1)

        return results

    def generate_report(self) -> str:
        """Generate a comprehensive test report."""
        report = []
        report.append("=" * 80)
        report.append("DATABASE SYNCHRONIZATION TEST REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Total Tests: {len(self.results)}")
        report.append("")

        successful_tests = [r for r in self.results if r["success"]]
        failed_tests = [r for r in self.results if not r["success"]]

        report.append(f"Successful: {len(successful_tests)}")
        report.append(f"Failed: {len(failed_tests)}")
        report.append("")

        # Analyze issues
        all_issues = []
        for result in successful_tests:
            if result["analysis"]["issues"]:
                all_issues.extend(result["analysis"]["issues"])

        if all_issues:
            report.append("IDENTIFIED ISSUES:")
            for issue in set(all_issues):
                count = all_issues.count(issue)
                report.append(f"  - {issue} (occurred {count} times)")
            report.append("")

        # Detailed results
        for i, result in enumerate(self.results, 1):
            report.append(f"TEST {i}: {result['prompt']}")
            report.append(f"  Job ID: {result['job_id']}")
            report.append(f"  Success: {result['success']}")

            if result["success"]:
                analysis = result["analysis"]
                report.append(f"  Job appeared in DB: {analysis['job_appeared_in_db']}")
                if analysis["time_to_appear"]:
                    report.append(
                        f"  Time to appear: {analysis['time_to_appear']:.3f}s"
                    )
                report.append(
                    f"  Status transitions: {len(analysis['status_transitions'])}"
                )
                report.append(f"  Final status: {analysis['final_status']}")

                if analysis["issues"]:
                    report.append(f"  Issues: {', '.join(analysis['issues'])}")
            else:
                report.append(f"  Error: {result['error']}")

            report.append("")

        return "\n".join(report)


def main():
    """Main test function."""
    print("🚀 Starting Database Synchronization Test")
    print("=" * 50)

    tester = SynchronizationTester()

    # Check if API is available
    if not tester.api_tester.test_api_health():
        print("❌ Flask API is not responding at http://localhost:5001")
        print(
            "   Please ensure the Flask app is running with: uv run python src/main.py"
        )
        return

    print("✅ Flask API is responding")

    # Test single job synchronization
    single_result = tester.test_single_job_sync("diagnostic test")

    # Test concurrent jobs
    concurrent_results = tester.test_concurrent_jobs(2)

    # Generate and display report
    report = tester.generate_report()
    print("\n" + report)

    # Save report to file
    with open("database_sync_test_report.txt", "w") as f:
        f.write(report)

    print("\n📄 Full report saved to: database_sync_test_report.txt")


if __name__ == "__main__":
    main()
