#!/bin/bash

# Create Clean Deployment Branch
# This script creates a deployment-only branch with essential files

set -e

echo "🚀 Creating clean deployment branch..."

# 1. Create new deployment branch from main
git checkout main

# Delete existing deployment-clean branch if it exists
if git show-ref --verify --quiet refs/heads/deployment-clean; then
    echo "🗑️ Removing existing deployment-clean branch..."
    git branch -D deployment-clean
fi

git checkout -b deployment-clean

# 2. Remove development/documentation folders
echo "🗑️ Removing development-only folders..."
rm -rf PRPs/
rm -rf PRDs/
rm -rf Archive/
rm -rf docs/
rm -rf htmlcov/
rm -rf test_screenshots/
rm -rf instance/
rm -rf tests/  # Keep co-located tests in src/

# 3. Remove development artifacts
echo "🧹 Cleaning development artifacts..."
rm -f coverage.xml
rm -f debug_config.py
rm -f *.log
rm -f dump.rdb
rm -f sora_poc.db
rm -f deployment_branch_git.txt
rm -f my-ui-refactoring-webhooks_tailwind.md
rm -f INITIAL.md

# 4. Remove Docker development files
echo "🐳 Cleaning Docker development artifacts..."
rm -f docker-compose.local.yml
rm -f fixed_stack.log
rm -f local_stack.log
rm -f test_stack.log

# 5. Clean uploads folder but keep structure
echo "📁 Cleaning uploads folder..."
rm -rf uploads/*
mkdir -p uploads
echo "# Video uploads will be stored here" > uploads/.gitkeep

# 6. Remove test UI files
rm -f test_ui_end_to_end.py

# 7. Update README.md for deployment focus
echo "📝 Creating deployment-focused README..."
cat > README_DEPLOYMENT.md << 'EOF'
# Sora Video Generation - Production Deployment

## 🚀 Quick Deployment

### Docker Deployment (Recommended)
```bash
# 1. Configure environment
cp .env.example .env
# Edit .env with your Azure OpenAI credentials

# 2. Deploy with Docker
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# 3. Access application
open http://localhost:8090
```

### Manual Deployment
```bash
# 1. Install dependencies
uv sync

# 2. Setup database
uv run flask --app src.main:create_app db upgrade

# 3. Start services
./scripts/dev-local.sh
```

## 📊 Health Checks
- Application: `curl http://localhost:8090/health`
- Database: `curl http://localhost:8090/health/database`
- Azure API: `curl http://localhost:8090/health/azure`

## 🔒 Security
- Debug endpoints automatically disabled in production
- Rate limiting enabled
- Secure session management
- Input validation via Pydantic

## 📚 Documentation
- [CLAUDE.md](./CLAUDE.md) - Development standards
- [DOCKER_TEST_PLAN.md](./DOCKER_TEST_PLAN.md) - Deployment validation

For complete documentation, see the main branch.
EOF

# 8. Create .gitignore for deployment
echo "🔧 Creating deployment .gitignore..."
cat > .gitignore << 'EOF'
# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Virtual environments
.venv/
venv/
.env.local
.env.docker

# Application data
*.db
*.log
dump.rdb
instance/

# Uploads (keep structure, ignore content)
uploads/*.mp4
uploads/*.webm
uploads/*.mov

# Testing
htmlcov/
coverage.xml
test_screenshots/

# Development
debug_config.py
.claude/

# OS
.DS_Store
Thumbs.db
EOF

# 9. Commit changes
echo "💾 Committing deployment branch..."
git add .
git commit -m "feat: Create clean deployment branch

🚀 Production-ready deployment branch with:
- Essential application code only
- Docker deployment configurations
- Removed development artifacts
- Removed documentation folders (PRPs, PRDs, Archive)
- Removed test artifacts and coverage reports
- Clean uploads folder structure
- Deployment-focused README

🧹 Removed development-only items:
- PRPs/, PRDs/, Archive/, docs/ folders
- Test artifacts and coverage reports
- Development database and log files
- Claude AI configuration
- Development Docker compose files

✅ Deployment branch is ready for production use

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"

echo "✅ Deployment branch 'deployment-clean' created successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review the changes: git log --oneline -5"
echo "2. Test deployment: docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d"
echo "3. Push to remote: git push origin deployment-clean"
echo ""
echo "📊 Branch size comparison:"
du -sh . | head -1
echo ""
echo "🚀 Deployment branch is ready!"