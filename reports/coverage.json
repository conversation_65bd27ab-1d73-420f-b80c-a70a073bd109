{"meta": {"format": 3, "version": "7.9.1", "timestamp": "2025-07-27T12:02:07.840897", "branch_coverage": false, "show_contexts": false}, "files": {"src/config/__init__.py": {"executed_lines": [1, 3, 4, 6], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 3, 4, 6], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 3, 4, 6], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/config/environment.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 19, 20, 21, 36, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 55, 56, 63, 66, 69, 70, 73, 74, 77, 80, 83, 89, 90, 92, 104, 107, 109, 110, 124, 125, 126, 127, 129, 131, 132, 146, 147, 148, 149, 151, 153, 154, 168, 175, 176, 182, 183, 189, 190, 197, 199, 200, 207, 208, 210, 219, 221, 222, 230, 231, 234, 239, 240, 243, 248, 249, 252, 253, 258, 266, 268, 269, 270, 271, 273, 276, 277, 284, 285, 295, 296, 299, 302, 303, 306, 307, 312, 317, 321, 324, 326, 327, 329, 331, 332, 337, 338, 344, 346, 347, 349, 351, 352, 357, 358, 364, 366, 367, 369, 371, 372, 378, 379, 386, 388, 389, 391, 393, 394, 400, 401, 408, 411, 418, 419, 421, 422, 423, 425, 450, 451, 453, 454, 455, 463, 465, 466, 469, 471, 472, 475, 477, 478, 481, 483, 485, 486, 489], "summary": {"covered_lines": 156, "num_statements": 158, "percent_covered": 98.73417721518987, "percent_covered_display": "99", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [259, 262], "excluded_lines": [], "functions": {"EnvironmentDetector.get_environment_info": {"executed_lines": [63, 66, 69, 70, 73, 74, 77, 80, 83, 89, 90, 92, 104, 107], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentDetector._determine_security_level": {"executed_lines": [124, 125, 126, 127, 129], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentDetector._determine_performance_profile": {"executed_lines": [146, 147, 148, 149, 151], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentDetector._get_environment_provider_defaults": {"executed_lines": [168, 175, 176, 182, 183, 189, 190, 197], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentDetector.validate_environment_compatibility": {"executed_lines": [207, 208, 210, 219, 221, 222, 230, 231, 234, 239, 240, 243, 248, 249, 252, 253, 258, 266, 268, 269, 270, 271, 273], "summary": {"covered_lines": 23, "num_statements": 25, "percent_covered": 92.0, "percent_covered_display": "92", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [259, 262], "excluded_lines": []}, "EnvironmentOptimizer.get_optimized_provider_config": {"executed_lines": [295, 296, 299, 302, 303, 306, 307, 312, 317, 321, 324], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentOptimizer._optimize_veo3_config": {"executed_lines": [329, 331, 332, 337, 338, 344], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentOptimizer._optimize_azure_config": {"executed_lines": [349, 351, 352, 357, 358, 364], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentOptimizer._apply_security_optimizations": {"executed_lines": [369, 371, 372, 378, 379, 386], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentOptimizer._apply_performance_optimizations": {"executed_lines": [391, 393, 394, 400, 401, 408], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_environment_summary": {"executed_lines": [418, 419, 421, 422, 423, 425, 450, 451, 453, 454, 455], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_production_environment": {"executed_lines": [465, 466], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_development_environment": {"executed_lines": [471, 472], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_docker_deployment": {"executed_lines": [477, 478], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_recommended_provider": {"executed_lines": [483, 485, 486, 489], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 19, 20, 21, 36, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 55, 56, 109, 110, 131, 132, 153, 154, 199, 200, 276, 277, 284, 285, 326, 327, 346, 347, 366, 367, 388, 389, 411, 463, 469, 475, 481], "summary": {"covered_lines": 45, "num_statements": 45, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"EnvironmentInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnvironmentDetector": {"executed_lines": [63, 66, 69, 70, 73, 74, 77, 80, 83, 89, 90, 92, 104, 107, 124, 125, 126, 127, 129, 146, 147, 148, 149, 151, 168, 175, 176, 182, 183, 189, 190, 197, 207, 208, 210, 219, 221, 222, 230, 231, 234, 239, 240, 243, 248, 249, 252, 253, 258, 266, 268, 269, 270, 271, 273], "summary": {"covered_lines": 55, "num_statements": 57, "percent_covered": 96.49122807017544, "percent_covered_display": "96", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [259, 262], "excluded_lines": []}, "EnvironmentOptimizer": {"executed_lines": [295, 296, 299, 302, 303, 306, 307, 312, 317, 321, 324, 329, 331, 332, 337, 338, 344, 349, 351, 352, 357, 358, 364, 369, 371, 372, 378, 379, 386, 391, 393, 394, 400, 401, 408], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 19, 20, 21, 36, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 55, 56, 109, 110, 131, 132, 153, 154, 199, 200, 276, 277, 284, 285, 326, 327, 346, 347, 366, 367, 388, 389, 411, 418, 419, 421, 422, 423, 425, 450, 451, 453, 454, 455, 463, 465, 466, 469, 471, 472, 475, 477, 478, 481, 483, 485, 486, 489], "summary": {"covered_lines": 66, "num_statements": 66, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/config/environments.py": {"executed_lines": [1, 3, 4, 5, 7, 10, 24, 27, 28, 35, 36, 41, 42, 45, 46, 47, 48, 49, 50, 53, 54, 55, 58, 59, 60, 62, 64, 65, 67, 68, 69, 71, 74, 75, 79, 80, 85, 86, 90, 91, 95, 96, 101, 102, 103, 104, 107, 110, 113, 114, 116, 118, 119, 121, 123, 124, 126, 127, 128, 129, 130, 131, 133, 134, 136, 139, 140, 142, 144, 145, 147, 150, 151, 156, 159, 162, 165, 168, 171, 174, 177, 182, 183, 186, 187, 192, 195, 200, 203, 205, 206, 213, 215, 218, 221, 226, 231, 232, 239, 246, 247, 253, 254, 255, 256, 259, 260, 264, 265, 267, 270, 271, 277, 278, 279, 282, 283, 284, 287, 288, 289, 290, 292, 293, 295, 298, 299, 305, 306, 309, 314, 315, 316, 319, 327, 330, 334, 335, 342, 344, 346, 349, 350, 352, 353, 355, 356, 362, 369, 383, 384, 386, 387, 388, 389, 394, 396, 399, 406, 407, 409], "summary": {"covered_lines": 156, "num_statements": 168, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [38, 77, 82, 88, 93, 98, 153, 216, 219, 222, 227, 347], "excluded_lines": [], "functions": {"safe_int_from_env": {"executed_lines": [24], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.SECRET_KEY": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [38], "excluded_lines": []}, "BaseConfig.get_database_url": {"executed_lines": [45, 46, 47, 48, 49, 50, 53, 54, 55, 58, 59, 60, 62], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.DATABASE_URL": {"executed_lines": [67], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.SQLALCHEMY_TRACK_MODIFICATIONS": {"executed_lines": [71], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.CELERY_BROKER_URL": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [77], "excluded_lines": []}, "BaseConfig.CELERY_RESULT_BACKEND": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [82], "excluded_lines": []}, "BaseConfig.REDIS_HOST": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [88], "excluded_lines": []}, "BaseConfig.REDIS_PORT": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [93], "excluded_lines": []}, "BaseConfig.REDIS_DB": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [98], "excluded_lines": []}, "BaseConfig.AZURE_OPENAI_ENDPOINT": {"executed_lines": [116], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.AZURE_OPENAI_API_KEY": {"executed_lines": [121], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.AZURE_OPENAI_API_VERSION": {"executed_lines": [126, 127, 128, 129, 130, 131], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.AZURE_OPENAI_DEPLOYMENT_NAME": {"executed_lines": [136], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.UPLOAD_FOLDER": {"executed_lines": [142], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.MAX_CONTENT_LENGTH": {"executed_lines": [147], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseConfig.PORT": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [153], "excluded_lines": []}, "BaseConfig.validate_required_settings": {"executed_lines": [213, 215, 218, 221, 226], "summary": {"covered_lines": 5, "num_statements": 9, "percent_covered": 55.55555555555556, "percent_covered_display": "56", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [216, 219, 222, 227], "excluded_lines": []}, "BaseConfig.to_dict": {"executed_lines": [239], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DevelopmentConfig.validate_required_settings": {"executed_lines": [267], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestingConfig.validate_required_settings": {"executed_lines": [295], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProductionConfig.validate_required_settings": {"executed_lines": [342, 344, 346, 349, 350, 352, 353, 355, 356], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [347], "excluded_lines": []}, "get_config": {"executed_lines": [383, 384, 386, 387, 388, 389, 394, 396], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_environment_info": {"executed_lines": [406, 407, 409], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 7, 10, 27, 28, 35, 36, 41, 42, 64, 65, 68, 69, 74, 75, 79, 80, 85, 86, 90, 91, 95, 96, 101, 102, 103, 104, 107, 110, 113, 114, 118, 119, 123, 124, 133, 134, 139, 140, 144, 145, 150, 151, 156, 159, 162, 165, 168, 171, 174, 177, 182, 183, 186, 187, 192, 195, 200, 203, 205, 206, 231, 232, 246, 247, 253, 254, 255, 256, 259, 260, 264, 265, 270, 271, 277, 278, 279, 282, 283, 284, 287, 288, 289, 290, 292, 293, 298, 299, 305, 306, 309, 314, 315, 316, 319, 327, 330, 334, 335, 362, 369, 399], "summary": {"covered_lines": 101, "num_statements": 101, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BaseConfig": {"executed_lines": [45, 46, 47, 48, 49, 50, 53, 54, 55, 58, 59, 60, 62, 67, 71, 116, 121, 126, 127, 128, 129, 130, 131, 136, 142, 147, 213, 215, 218, 221, 226, 239], "summary": {"covered_lines": 32, "num_statements": 43, "percent_covered": 74.4186046511628, "percent_covered_display": "74", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [38, 77, 82, 88, 93, 98, 153, 216, 219, 222, 227], "excluded_lines": []}, "DevelopmentConfig": {"executed_lines": [267], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestingConfig": {"executed_lines": [295], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProductionConfig": {"executed_lines": [342, 344, 346, 349, 350, 352, 353, 355, 356], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [347], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 7, 10, 24, 27, 28, 35, 36, 41, 42, 64, 65, 68, 69, 74, 75, 79, 80, 85, 86, 90, 91, 95, 96, 101, 102, 103, 104, 107, 110, 113, 114, 118, 119, 123, 124, 133, 134, 139, 140, 144, 145, 150, 151, 156, 159, 162, 165, 168, 171, 174, 177, 182, 183, 186, 187, 192, 195, 200, 203, 205, 206, 231, 232, 246, 247, 253, 254, 255, 256, 259, 260, 264, 265, 270, 271, 277, 278, 279, 282, 283, 284, 287, 288, 289, 290, 292, 293, 298, 299, 305, 306, 309, 314, 315, 316, 319, 327, 330, 334, 335, 362, 369, 383, 384, 386, 387, 388, 389, 394, 396, 399, 406, 407, 409], "summary": {"covered_lines": 113, "num_statements": 113, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/config/factory.py": {"executed_lines": [1, 3, 4, 5, 7, 8, 9, 15, 18, 19, 31, 32, 44, 46, 47, 59, 60, 62, 63, 70, 72, 73, 85, 87, 94, 95, 119, 120, 130, 132, 133, 136, 139, 140, 141, 142, 143, 147, 154, 155, 157, 158, 168, 170, 180, 181, 191, 192, 194, 195, 233, 236, 239, 240, 241, 244, 245, 246, 249, 250, 251, 252, 253, 254, 257, 263, 265, 278, 279, 291, 292, 294, 295, 302, 303, 305, 306, 338, 339, 341, 343, 346, 347, 348, 350, 351, 353, 355, 356, 358, 359, 362, 364, 365, 375, 376, 378, 379, 389, 391, 392, 408, 409, 410, 413, 416, 417, 425, 426, 445, 447, 448, 451, 453, 455, 460, 462, 468, 470, 477, 479, 481, 482, 492, 493, 496, 497, 498, 499, 500, 503, 505, 508, 509, 510, 512], "summary": {"covered_lines": 133, "num_statements": 137, "percent_covered": 97.08029197080292, "percent_covered_display": "97", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [116, 117, 258, 501], "excluded_lines": [], "functions": {"ConfigurationFactory.get_base_config": {"executed_lines": [44], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.get_video_config": {"executed_lines": [59, 60], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.clear_cache": {"executed_lines": [70], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.create_generation_params_defaults": {"executed_lines": [85, 87], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.validate_generation_params": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [116, 117], "excluded_lines": []}, "ConfigurationFactory.get_azure_config": {"executed_lines": [130, 132, 133, 136, 139, 140, 141, 142, 143, 147, 154, 155], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.get_app_config": {"executed_lines": [168, 170], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.get_veo3_settings": {"executed_lines": [191, 192], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.create_veo3_config": {"executed_lines": [233, 236, 239, 240, 241, 244, 245, 246, 249, 250, 251, 252, 253, 254, 257, 263, 265], "summary": {"covered_lines": 17, "num_statements": 18, "percent_covered": 94.44444444444444, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [258], "excluded_lines": []}, "ConfigurationFactory.get_provider_availability": {"executed_lines": [291, 292], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.get_default_provider": {"executed_lines": [302, 303], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.create_provider_config": {"executed_lines": [338, 339, 341, 343, 346, 347, 348, 350, 351, 353, 355, 356, 358, 359, 362], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.validate_provider_configuration": {"executed_lines": [375, 376], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationFactory.get_provider_performance_config": {"executed_lines": [389, 391, 392, 408, 409, 410, 413], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProviderConfigurationFactory.create_environment_aware_config": {"executed_lines": [445, 447, 448, 451, 453, 455, 460, 462, 468, 470, 477, 479], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProviderConfigurationFactory.get_optimal_provider_config": {"executed_lines": [492, 493, 496, 497, 498, 499, 500, 503, 505, 508, 509, 510, 512], "summary": {"covered_lines": 13, "num_statements": 14, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [501], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 7, 8, 9, 15, 18, 19, 31, 32, 46, 47, 62, 63, 72, 73, 94, 95, 119, 120, 157, 158, 180, 181, 194, 195, 278, 279, 294, 295, 305, 306, 364, 365, 378, 379, 416, 417, 425, 426, 481, 482], "summary": {"covered_lines": 41, "num_statements": 41, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ConfigurationFactory": {"executed_lines": [44, 59, 60, 70, 85, 87, 130, 132, 133, 136, 139, 140, 141, 142, 143, 147, 154, 155, 168, 170, 191, 192, 233, 236, 239, 240, 241, 244, 245, 246, 249, 250, 251, 252, 253, 254, 257, 263, 265, 291, 292, 302, 303, 338, 339, 341, 343, 346, 347, 348, 350, 351, 353, 355, 356, 358, 359, 362, 375, 376, 389, 391, 392, 408, 409, 410, 413], "summary": {"covered_lines": 67, "num_statements": 70, "percent_covered": 95.71428571428571, "percent_covered_display": "96", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [116, 117, 258], "excluded_lines": []}, "ProviderConfigurationFactory": {"executed_lines": [445, 447, 448, 451, 453, 455, 460, 462, 468, 470, 477, 479, 492, 493, 496, 497, 498, 499, 500, 503, 505, 508, 509, 510, 512], "summary": {"covered_lines": 25, "num_statements": 26, "percent_covered": 96.15384615384616, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [501], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 7, 8, 9, 15, 18, 19, 31, 32, 46, 47, 62, 63, 72, 73, 94, 95, 119, 120, 157, 158, 180, 181, 194, 195, 278, 279, 294, 295, 305, 306, 364, 365, 378, 379, 416, 417, 425, 426, 481, 482], "summary": {"covered_lines": 41, "num_statements": 41, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/config/security.py": {"executed_lines": [1, 3, 4, 5, 6, 7, 10, 11, 19, 20, 23, 26, 29, 38, 54, 61, 62, 72, 73, 76, 77, 80, 81, 82, 85, 86, 88, 90, 91, 101, 102, 104, 106, 107, 117, 118, 121, 122, 125, 126, 127, 130, 131, 132, 135, 136, 138, 140, 141, 151, 152, 155, 156, 157, 160, 163, 165, 167, 168, 181, 182, 184, 185, 187, 188, 191, 192, 195, 196, 197, 198, 201, 202, 204, 209, 210, 220, 222, 223, 230, 257, 258, 265, 268, 269, 276, 279, 280, 285, 288, 292, 294, 295, 302, 303, 305, 306, 308, 309, 313, 314, 316, 317, 319, 320, 322], "summary": {"covered_lines": 104, "num_statements": 106, "percent_covered": 98.11320754716981, "percent_covered_display": "98", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [206, 207], "excluded_lines": [], "functions": {"SecurityConfig.validate_prompt": {"executed_lines": [72, 73, 76, 77, 80, 81, 82, 85, 86, 88], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.validate_job_id": {"executed_lines": [101, 102, 104], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.validate_filename": {"executed_lines": [117, 118, 121, 122, 125, 126, 127, 130, 131, 132, 135, 136, 138], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.sanitize_prompt": {"executed_lines": [151, 152, 155, 156, 157, 160, 163, 165], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.validate_url": {"executed_lines": [181, 182, 184, 185, 187, 188, 191, 192, 195, 196, 197, 198, 201, 202, 204], "summary": {"covered_lines": 15, "num_statements": 17, "percent_covered": 88.23529411764706, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [206, 207], "excluded_lines": []}, "SecurityConfig.generate_secure_token": {"executed_lines": [220], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.get_security_headers": {"executed_lines": [230], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.check_environment_security": {"executed_lines": [265, 268, 269, 276, 279, 280, 285, 288, 292], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfig.get_security_recommendations": {"executed_lines": [302, 303, 305, 306, 308, 309, 313, 314, 316, 317, 319, 320, 322], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 7, 10, 11, 19, 20, 23, 26, 29, 38, 54, 61, 62, 90, 91, 106, 107, 140, 141, 167, 168, 209, 210, 222, 223, 257, 258, 294, 295], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SecurityConfig": {"executed_lines": [72, 73, 76, 77, 80, 81, 82, 85, 86, 88, 101, 102, 104, 117, 118, 121, 122, 125, 126, 127, 130, 131, 132, 135, 136, 138, 151, 152, 155, 156, 157, 160, 163, 165, 181, 182, 184, 185, 187, 188, 191, 192, 195, 196, 197, 198, 201, 202, 204, 220, 230, 265, 268, 269, 276, 279, 280, 285, 288, 292, 302, 303, 305, 306, 308, 309, 313, 314, 316, 317, 319, 320, 322], "summary": {"covered_lines": 73, "num_statements": 75, "percent_covered": 97.33333333333333, "percent_covered_display": "97", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [206, 207], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 7, 10, 11, 19, 20, 23, 26, 29, 38, 54, 61, 62, 90, 91, 106, 107, 140, 141, 167, 168, 209, 210, 222, 223, 257, 258, 294, 295], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/config/service.py": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 35, 36, 37, 38, 40, 41, 54, 55, 63, 66, 72, 73, 74, 77, 78, 79, 80, 81, 82, 84, 85, 87, 88, 89, 92, 93, 101, 102, 109, 110, 112, 114, 116, 117, 120, 121, 129, 130, 131, 134, 135, 136, 137, 139, 141, 142, 168, 169, 183, 184, 187, 188, 189, 190, 193, 194, 195, 198, 199, 200, 203, 204, 205, 206, 208, 209, 238, 241, 244, 245, 246, 249, 252, 253, 255, 256, 257, 258, 261, 262, 263, 264, 273, 274, 289, 290, 292, 293, 294, 298, 304, 306, 308, 309, 310, 311, 312, 313, 326, 327, 344, 346, 347, 350, 354, 356, 357, 373, 375, 376, 383, 384, 386, 387, 395, 396, 397, 398, 399, 400, 402, 403, 423, 438], "summary": {"covered_lines": 130, "num_statements": 204, "percent_covered": 63.72549019607843, "percent_covered_display": "64", "missing_lines": 74, "excluded_lines": 0}, "missing_lines": [48, 49, 50, 51, 52, 67, 68, 69, 70, 95, 96, 97, 98, 99, 113, 115, 145, 146, 147, 148, 149, 152, 153, 154, 155, 158, 159, 160, 161, 164, 165, 166, 172, 173, 176, 177, 178, 179, 181, 254, 265, 266, 267, 268, 269, 270, 271, 295, 296, 299, 300, 301, 302, 307, 315, 317, 321, 322, 323, 324, 348, 349, 351, 352, 410, 425, 426, 427, 430, 431, 432, 434, 435, 440], "excluded_lines": [], "functions": {"ConfigurationService.reload": {"executed_lines": [395, 396, 397, 398, 399, 400], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationService._ensure_initialized": {"executed_lines": [63, 66, 72, 73, 74, 77, 78, 79, 80, 81, 82, 84, 85, 87, 88, 89, 92, 93], "summary": {"covered_lines": 18, "num_statements": 27, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 70, 95, 96, 97, 98, 99], "excluded_lines": []}, "ConfigurationService._apply_environment_overrides": {"executed_lines": [109, 110, 112, 114, 116, 117], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [113, 115], "excluded_lines": []}, "ConfigurationService._detect_deployment_type": {"executed_lines": [129, 130, 131, 134, 135, 136, 137, 139], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationService._apply_docker_overrides": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [145, 146, 147, 148, 149, 152, 153, 154, 155, 158, 159, 160, 161, 164, 165, 166], "excluded_lines": []}, "ConfigurationService._apply_production_overrides": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [172, 173, 176, 177, 178, 179, 181], "excluded_lines": []}, "ConfigurationService._apply_local_overrides": {"executed_lines": [187, 188, 189, 190, 193, 194, 195, 198, 199, 200, 203, 204, 205, 206], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationService.get": {"executed_lines": [238, 241, 244, 245, 246, 249, 252, 253, 255, 256, 257, 258, 261, 262, 263, 264], "summary": {"covered_lines": 16, "num_statements": 24, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [254, 265, 266, 267, 268, 269, 270, 271], "excluded_lines": []}, "ConfigurationService._convert_type": {"executed_lines": [289, 290, 292, 293, 294, 298, 304, 306, 308, 309, 310, 311, 312, 313], "summary": {"covered_lines": 14, "num_statements": 27, "percent_covered": 51.851851851851855, "percent_covered_display": "52", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [295, 296, 299, 300, 301, 302, 307, 315, 317, 321, 322, 323, 324], "excluded_lines": []}, "ConfigurationService.get_int": {"executed_lines": [344, 346, 347, 350, 354], "summary": {"covered_lines": 5, "num_statements": 9, "percent_covered": 55.55555555555556, "percent_covered_display": "56", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [348, 349, 351, 352], "excluded_lines": []}, "ConfigurationService.get_bool": {"executed_lines": [373], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationService.clear_cache": {"executed_lines": [383, 384], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationService.get_debug_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [410], "excluded_lines": []}, "get_database_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [425, 426, 427, 430, 431, 432, 434, 435], "excluded_lines": []}, "get_azure_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [440], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 35, 36, 37, 38, 40, 41, 54, 55, 101, 102, 120, 121, 141, 142, 168, 169, 183, 184, 208, 209, 273, 274, 326, 327, 356, 357, 375, 376, 386, 387, 402, 403, 423, 438], "summary": {"covered_lines": 40, "num_statements": 40, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ConfigurationService": {"executed_lines": [63, 66, 72, 73, 74, 77, 78, 79, 80, 81, 82, 84, 85, 87, 88, 89, 92, 93, 109, 110, 112, 114, 116, 117, 129, 130, 131, 134, 135, 136, 137, 139, 187, 188, 189, 190, 193, 194, 195, 198, 199, 200, 203, 204, 205, 206, 238, 241, 244, 245, 246, 249, 252, 253, 255, 256, 257, 258, 261, 262, 263, 264, 289, 290, 292, 293, 294, 298, 304, 306, 308, 309, 310, 311, 312, 313, 344, 346, 347, 350, 354, 373, 383, 384, 395, 396, 397, 398, 399, 400], "summary": {"covered_lines": 90, "num_statements": 155, "percent_covered": 58.064516129032256, "percent_covered_display": "58", "missing_lines": 65, "excluded_lines": 0}, "missing_lines": [48, 49, 50, 51, 52, 67, 68, 69, 70, 95, 96, 97, 98, 99, 113, 115, 145, 146, 147, 148, 149, 152, 153, 154, 155, 158, 159, 160, 161, 164, 165, 166, 172, 173, 176, 177, 178, 179, 181, 254, 265, 266, 267, 268, 269, 270, 271, 295, 296, 299, 300, 301, 302, 307, 315, 317, 321, 322, 323, 324, 348, 349, 351, 352, 410], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 35, 36, 37, 38, 40, 41, 54, 55, 101, 102, 120, 121, 141, 142, 168, 169, 183, 184, 208, 209, 273, 274, 326, 327, 356, 357, 375, 376, 386, 387, 402, 403, 423, 438], "summary": {"covered_lines": 40, "num_statements": 49, "percent_covered": 81.63265306122449, "percent_covered_display": "82", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [425, 426, 427, 430, 431, 432, 434, 435, 440], "excluded_lines": []}}}, "src/config/veo3_settings.py": {"executed_lines": [1, 11, 12, 13, 14, 15, 17, 18, 20, 22, 25, 26, 51, 63, 67, 72, 77, 81, 86, 90, 97, 101, 109, 115, 119, 120, 121, 134, 138, 139, 143, 144, 146, 147, 149, 151, 152, 153, 166, 172, 173, 177, 179, 186, 192, 193, 196, 203, 205, 215, 217, 219, 220, 222, 241, 243, 245, 249, 250, 252, 253, 256, 260, 265, 285, 293, 300, 323, 324, 334, 335, 338, 339, 346, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 385, 386, 399, 404, 405, 406, 408, 421, 428, 442, 452, 453, 462, 464, 465, 468, 469, 472, 473, 474, 475, 478, 479, 482, 483, 486, 494, 498, 499, 500, 501, 503], "summary": {"covered_lines": 113, "num_statements": 118, "percent_covered": 95.76271186440678, "percent_covered_display": "96", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [135, 246, 261, 400, 490], "excluded_lines": [], "functions": {"Veo3Settings.validate_google_project_id": {"executed_lines": [134, 138, 139, 143, 144, 146, 147, 149], "summary": {"covered_lines": 8, "num_statements": 9, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [135], "excluded_lines": []}, "Veo3Settings.validate_model_version": {"executed_lines": [166, 172, 173, 177], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Veo3Settings.get_provider_availability": {"executed_lines": [186, 192, 193, 196, 203], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Veo3Settings.validate_provider_configuration": {"executed_lines": [215, 217, 219, 220, 222, 241, 243, 245, 249, 250, 252, 253, 256, 260, 265, 285], "summary": {"covered_lines": 16, "num_statements": 18, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [246, 261], "excluded_lines": []}, "Veo3Settings.get_performance_config": {"executed_lines": [300], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_cached_veo3_settings": {"executed_lines": [334, 335], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Veo3ProviderConfig.__init__": {"executed_lines": [374, 375, 376, 377, 378, 379, 380, 381, 382, 383], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Veo3ProviderConfig.from_settings": {"executed_lines": [399, 404, 405, 406, 408], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [400], "excluded_lines": []}, "Veo3ProviderConfig.to_dict": {"executed_lines": [428], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "validate_veo3_environment": {"executed_lines": [452, 453, 462, 464, 465, 468, 469, 472, 473, 474, 475, 478, 479, 482, 483, 486, 494, 498, 499, 500, 501, 503], "summary": {"covered_lines": 22, "num_statements": 23, "percent_covered": 95.65217391304348, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [490], "excluded_lines": []}, "": {"executed_lines": [1, 11, 12, 13, 14, 15, 17, 18, 20, 22, 25, 26, 51, 63, 67, 72, 77, 81, 86, 90, 97, 101, 109, 115, 119, 120, 121, 151, 152, 153, 179, 205, 293, 323, 324, 338, 339, 346, 385, 386, 421, 442], "summary": {"covered_lines": 39, "num_statements": 39, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Veo3Settings": {"executed_lines": [134, 138, 139, 143, 144, 146, 147, 149, 166, 172, 173, 177, 186, 192, 193, 196, 203, 215, 217, 219, 220, 222, 241, 243, 245, 249, 250, 252, 253, 256, 260, 265, 285, 300], "summary": {"covered_lines": 34, "num_statements": 37, "percent_covered": 91.89189189189189, "percent_covered_display": "92", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [135, 246, 261], "excluded_lines": []}, "Veo3ProviderConfig": {"executed_lines": [374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 399, 404, 405, 406, 408, 428], "summary": {"covered_lines": 16, "num_statements": 17, "percent_covered": 94.11764705882354, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [400], "excluded_lines": []}, "": {"executed_lines": [1, 11, 12, 13, 14, 15, 17, 18, 20, 22, 25, 26, 51, 63, 67, 72, 77, 81, 86, 90, 97, 101, 109, 115, 119, 120, 121, 151, 152, 153, 179, 205, 293, 323, 324, 334, 335, 338, 339, 346, 385, 386, 421, 442, 452, 453, 462, 464, 465, 468, 469, 472, 473, 474, 475, 478, 479, 482, 483, 486, 494, 498, 499, 500, 501, 503], "summary": {"covered_lines": 63, "num_statements": 64, "percent_covered": 98.4375, "percent_covered_display": "98", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [490], "excluded_lines": []}}}, "src/config/video_config.py": {"executed_lines": [1, 3, 4, 6, 9, 10, 11, 19, 20, 21, 22, 25, 26, 27, 30, 33, 34, 35, 37, 38, 48, 59, 98, 112], "summary": {"covered_lines": 22, "num_statements": 34, "percent_covered": 64.70588235294117, "percent_covered_display": "65", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [74, 76, 77, 81, 82, 86, 87, 91, 92, 96, 105, 119], "excluded_lines": [], "functions": {"VideoGenerationConfig.from_config": {"executed_lines": [48], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VideoGenerationConfig.validate_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [74, 76, 77, 81, 82, 86, 87, 91, 92, 96], "excluded_lines": []}, "VideoGenerationConfig.get_defaults_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [105], "excluded_lines": []}, "VideoGenerationConfig.get_constraints_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [119], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 6, 9, 10, 11, 19, 20, 21, 22, 25, 26, 27, 30, 33, 34, 35, 37, 38, 59, 98, 112], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"VideoGenerationConfig": {"executed_lines": [48], "summary": {"covered_lines": 1, "num_statements": 13, "percent_covered": 7.6923076923076925, "percent_covered_display": "8", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [74, 76, 77, 81, 82, 86, 87, 91, 92, 96, 105, 119], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 6, 9, 10, 11, 19, 20, 21, 22, 25, 26, 27, 30, 33, 34, 35, 37, 38, 59, 98, 112], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}}, "totals": {"covered_lines": 817, "num_statements": 928, "percent_covered": 88.03879310344827, "percent_covered_display": "88", "missing_lines": 111, "excluded_lines": 0}}