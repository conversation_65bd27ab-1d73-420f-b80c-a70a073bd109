"""Add multi-user support columns

Revision ID: 65a4af831447
Revises: 00d2bd36b444
Create Date: 2025-07-08 16:59:00.000000

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "65a4af831447"
down_revision = "00d2bd36b444"
branch_labels = None
depends_on = None


def upgrade():
    # Add multi-user support columns
    with op.batch_alter_table("video_jobs", schema=None) as batch_op:
        batch_op.add_column(sa.Column("session_id", sa.String(255), nullable=True))
        batch_op.add_column(
            sa.Column("priority", sa.Integer(), nullable=False, server_default="0")
        )
        batch_op.add_column(sa.Column("queue_position", sa.Integer(), nullable=True))
        batch_op.add_column(
            sa.Column("retry_count", sa.Integer(), nullable=False, server_default="0")
        )
        batch_op.add_column(sa.Column("updated_at", sa.DateTime(), nullable=True))

        # Add indexes for multi-user queries
        batch_op.create_index(
            "idx_session_status", ["session_id", "status"], unique=False
        )
        batch_op.create_index(
            "idx_priority_created", ["priority", "created_at"], unique=False
        )
        batch_op.create_index("idx_queue_position", ["queue_position"], unique=False)
        batch_op.create_index("ix_video_jobs_session_id", ["session_id"], unique=False)


def downgrade():
    # Remove multi-user support columns
    with op.batch_alter_table("video_jobs", schema=None) as batch_op:
        batch_op.drop_index("ix_video_jobs_session_id")
        batch_op.drop_index("idx_queue_position")
        batch_op.drop_index("idx_priority_created")
        batch_op.drop_index("idx_session_status")

        batch_op.drop_column("updated_at")
        batch_op.drop_column("retry_count")
        batch_op.drop_column("queue_position")
        batch_op.drop_column("priority")
        batch_op.drop_column("session_id")
