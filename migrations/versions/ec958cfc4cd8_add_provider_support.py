"""Add provider support columns

Revision ID: ec958cfc4cd8
Revises: 65a4af831447
Create Date: 2025-07-23 05:45:00.000000

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ec958cfc4cd8"
down_revision = "65a4af831447"
branch_labels = None
depends_on = None


def upgrade():
    # Add provider support columns
    with op.batch_alter_table("video_jobs", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "api_provider",
                sa.String(20),
                nullable=False,
                server_default="azure_sora",
            )
        )
        batch_op.add_column(sa.Column("input_image_path", sa.Text(), nullable=True))
        batch_op.add_column(
            sa.Column(
                "audio_generated", sa.Bo<PERSON>an(), nullable=False, server_default="false"
            )
        )

        # Add indexes for provider-aware queries
        batch_op.create_index(
            "idx_provider_status", ["api_provider", "status"], unique=False
        )
        batch_op.create_index(
            "idx_provider_created", ["api_provider", "created_at"], unique=False
        )


def downgrade():
    # Remove provider support columns
    with op.batch_alter_table("video_jobs", schema=None) as batch_op:
        batch_op.drop_index("idx_provider_created")
        batch_op.drop_index("idx_provider_status")

        batch_op.drop_column("audio_generated")
        batch_op.drop_column("input_image_path")
        batch_op.drop_column("api_provider")
