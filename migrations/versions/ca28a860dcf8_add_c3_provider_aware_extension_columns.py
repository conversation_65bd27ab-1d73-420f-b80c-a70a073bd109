"""Add C3 provider-aware extension columns

Revision ID: ca28a860dcf8
Revises: ec958cfc4cd8
Create Date: 2025-07-29 12:43:24.979450

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ca28a860dcf8"
down_revision = "ec958cfc4cd8"
branch_labels = None
depends_on = None


def upgrade():
    # Add C3 Provider-aware extension columns to video_jobs table
    with op.batch_alter_table("video_jobs", schema=None) as batch_op:
        # Add the missing C3 Provider-aware extension columns
        batch_op.add_column(sa.Column("provider_job_id", sa.String(255), nullable=True))
        batch_op.add_column(sa.Column("provider_metadata", sa.Text(), nullable=True))
        batch_op.add_column(
            sa.Column("provider_queue_position", sa.Integer(), nullable=True)
        )
        batch_op.add_column(
            sa.Column("provider_estimated_completion", sa.DateTime(), nullable=True)
        )
        batch_op.add_column(
            sa.Column(
                "provider_health_status",
                sa.String(20),
                nullable=True,
                server_default="healthy",
            )
        )

        # Add C3 Provider-aware performance indexes
        batch_op.create_index(
            "idx_provider_queue_position",
            ["api_provider", "provider_queue_position"],
            unique=False,
        )
        batch_op.create_index(
            "idx_provider_health_status",
            ["api_provider", "provider_health_status"],
            unique=False,
        )
        batch_op.create_index(
            "idx_session_provider_status",
            ["session_id", "api_provider", "status"],
            unique=False,
        )
        batch_op.create_index(
            "idx_provider_estimated_completion",
            ["api_provider", "provider_estimated_completion"],
            unique=False,
        )


def downgrade():
    # Remove C3 Provider-aware extension columns and indexes
    with op.batch_alter_table("video_jobs", schema=None) as batch_op:
        # Drop indexes first
        batch_op.drop_index("idx_provider_estimated_completion")
        batch_op.drop_index("idx_session_provider_status")
        batch_op.drop_index("idx_provider_health_status")
        batch_op.drop_index("idx_provider_queue_position")

        # Drop columns
        batch_op.drop_column("provider_health_status")
        batch_op.drop_column("provider_estimated_completion")
        batch_op.drop_column("provider_queue_position")
        batch_op.drop_column("provider_metadata")
        batch_op.drop_column("provider_job_id")
