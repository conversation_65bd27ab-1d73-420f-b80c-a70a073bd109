{"permissions": {"allow": ["WebFetch(domain:learn.microsoft.com)", "Bash(find:*)", "Bash(tree:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(uv sync:*)", "<PERSON><PERSON>(uv pip:*)", "WebFetch(domain:devblogs.microsoft.com)", "WebFetch(domain:testdriven.io)", "WebFetch(domain:realpython.com)", "Bash(rm:*)", "<PERSON><PERSON>(source:*)", "Bash(pytest:*)", "Bash(PYTHONPATH=. pytest src/tests/test_integration_e2e.py::TestVideoGenerationIntegration::test_complete_video_generation_workflow -v -s)", "Bash(ls:*)", "Bash(uv add:*)", "<PERSON><PERSON>(uv list:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "mcp__brave-search__brave_web_search", "mcp__fetch__fetch", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(diff:*)", "mcp__playwright__playwright_navigate", "<PERSON><PERSON>(npx playwright:*)", "mcp__playwright", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_click", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_select", "mcp__playwright__playwright_close", "mcp__playwright__playwright_get_visible_html", "mcp__playwright__playwright_get_visible_text", "mcp__filesystem__read_multiple_files", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(true)", "Bash(unset:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(printenv:*)", "<PERSON><PERSON>(timeout 5 uv run:*)", "Bash(ps:*)", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "Bash(/research-validate PRDs/multi-user-hardening-prd.md)", "Bash(git merge:*)", "mcp__perplexity-mcp__perplexity_search_web", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "Bash(redis-cli:*)", "Bash(rg:*)", "<PERSON><PERSON>(mv:*)", "<PERSON>sh(redis-server:*)", "Bash(# Start Celery worker in background\nuv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=2 &\nCELERY_PID=$!\n\n# Start Flask app in background\nuv run python src/main.py &\nFLASK_PID=$!\n\n# Wait for services to start\nsleep 8\n\necho \"\"=== Services Status ===\"\"\necho \"\"Celery PID: $CELERY_PID\"\"\necho \"\"Flask PID: $FLASK_PID\"\"\necho \"\"Redis status: $(redis-cli ping)\"\")", "Bash(# Test health endpoints\necho \"\"=== Testing Health Endpoints ===\"\"\ncurl -s http://localhost:5001/health | jq ''.overall_status''\n\necho \"\"=== Testing Multi-User Endpoints ===\"\"\ncurl -s http://localhost:5001/queue/status | jq ''.success''\n\necho \"\"=== Testing Session Info ===\"\"\ncurl -s http://localhost:5001/session/info | jq ''.success'')", "Bash(# Test video generation with form data\necho \"\"=== Testing Video Generation with Form Data ===\"\"\ncurl -X POST http://localhost:5001/generate \\\n  -H \"\"Content-Type: application/x-www-form-urlencoded\"\" \\\n  -d \"\"prompt=A happy cat playing with a ball\"\" | jq ''.'')", "Bash(# Test Azure connection directly\necho \"\"=== Testing Azure Connection ===\"\"\nuv run python test_azure_connection.py)", "Bash(# Clean up services\npkill -f \"\"python src/main.py\"\"\npkill -f \"\"celery.*worker\"\"\n\necho \"\"=== Services Stopped ===\"\"\necho \"\"Flask and Celery processes terminated\"\"\necho \"\"Redis still running: $(redis-cli ping)\"\")", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(uv:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__list_directory", "Bash(git stash:*)", "Bash(./scripts/start_celery_clean.sh:*)", "Bash(PYTHONPATH=/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/src python3 -c \"\nimport os\nimport sys\nfrom dotenv import load_dotenv\n\n# Load the environment file\nload_dotenv()\n\n# Check environment variable\nprint(''Environment variable AZURE_OPENAI_API_VERSION:'', os.getenv(''AZURE_OPENAI_API_VERSION''))\n\n# Import configuration service\nfrom config.service import ConfigurationService\n\n# Test what ConfigurationService returns\napi_version = ConfigurationService.get(''AZURE_OPENAI_API_VERSION'', ''2025-04-01-preview'')\nprint(''ConfigurationService returned:'', api_version)\n\n# Test the factory\nfrom config.factory import ConfigurationFactory\nazure_config = ConfigurationFactory.get_azure_config()\nprint(''ConfigurationFactory returned:'', azure_config.get(''api_version''))\n\")", "mcp__playwright__playwright_get", "mcp__playwright__playwright_post", "Bash(ffprobe:*)", "Bash(git rm:*)", "Bash(git branch:*)", "mcp__filesystem__read_file", "mcp__filesystem__search_files", "Bash(docker build:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker run:*)", "Bash(docker system prune:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(env -i:*)", "Bash(DATABASE_URL=\"\" CELERY_BROKER_URL=\"\" CELERY_RESULT_BACKEND=\"\" PORT=5003 uv run python src/main.py)", "Bash(DATABASE_URL=\"\" CELERY_BROKER_URL=\"\" CELERY_RESULT_BACKEND=\"\" PORT=5004 uv run python src/main.py)", "Bash(DEPLOYMENT_TYPE=docker python -c \"\nfrom src.config.service import ConfigurationService\nimport os\n\n# Test configuration loading\nprint(''🔧 Testing ConfigurationService with Docker override...'')\nConfigurationService.reload()\n\n# Test environment detection\ndeployment_type = ConfigurationService._detect_deployment_type()\nprint(f''✅ Detected deployment type: {deployment_type}'')\n\n# Test database URL\ndb_url = ConfigurationService.get(''DATABASE_URL'')\nprint(f''✅ DATABASE_URL: {db_url}'')\n\n# Test Redis URL\nredis_url = ConfigurationService.get(''CELERY_BROKER_URL'')\nprint(f''✅ CELERY_BROKER_URL: {redis_url}'')\n\n# Test Flask environment\nflask_env = ConfigurationService.get(''FLASK_ENV'')\nprint(f''✅ FLASK_ENV: {flask_env}'')\n\n# Test rate limiting\nrate_limit = ConfigurationService.get(''RATE_LIMIT_ENABLED'')\nprint(f''✅ RATE_LIMIT_ENABLED: {rate_limit}'')\n\nprint(''✅ Docker configuration test completed successfully!'')\n\")", "Bash(./scripts/start_local.sh:*)", "Bash(brew services start:*)", "Bash(./scripts/dev-local.sh:*)", "Bash(ffmpeg:*)", "Bash(cp:*)", "Bash(./scripts/start-app.sh:*)", "Bash(git log:*)", "WebFetch(domain:go.microsoft.com)", "Bash(./scripts/dev-docker.sh:*)", "Bash(DB_PASSWORD=sora_secure_password_2024 AZURE_OPENAI_API_KEY=FTVW2fTmJxtD6OAExnudU14hmeB2dOBb7xmBWILFYha4G2LWZMoAJQQJ99BFACHYHv6XJ3w3AAAAACOGc3rQ AZURE_OPENAI_ENDPOINT=https://eric-mc4zkcb5-eastus2.openai.azure.com/ AZURE_OPENAI_API_VERSION=preview AZURE_OPENAI_DEPLOYMENT_NAME=sora SECRET_KEY=dev-secret-key-for-docker-testing docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d)", "<PERSON><PERSON>(docker ps:*)", "<PERSON><PERSON>(docker restart:*)", "mcp__playwright__start_codegen_session", "mcp__playwright__end_codegen_session", "<PERSON><PERSON>(docker cp:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(git fetch:*)", "Bash(git cat-file:*)", "Bash(git fsck:*)"], "deny": []}}