{".class": "MypyFile", "_fullname": "attr.converters", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CallableConverterType": {".class": "SymbolTableNode", "cross_ref": "attrs._CallableConverterType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ConverterType": {".class": "SymbolTableNode", "cross_ref": "attrs._ConverterType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.converters.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.converters.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.converters.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.converters.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.converters.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.converters.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "default_if_none": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "attr.converters.default_if_none", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.converters.default_if_none", "name": "default_if_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["default"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_if_none", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.converters.default_if_none", "name": "default_if_none", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["default"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_if_none", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3], "arg_names": ["factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.converters.default_if_none", "name": "default_if_none", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["factory"], "arg_types": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_if_none", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.converters.default_if_none", "name": "default_if_none", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["factory"], "arg_types": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_if_none", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["default"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_if_none", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3], "arg_names": ["factory"], "arg_types": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_if_none", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "optional": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "attr.converters.optional", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.converters.optional", "name": "optional", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.converters.optional", "name": "optional", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.converters.optional", "name": "optional", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.converters.optional", "name": "optional", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "attr.converters.pipe", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.converters.pipe", "name": "pipe", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.converters.pipe", "name": "pipe", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.converters.pipe", "name": "pipe", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.converters.pipe", "name": "pipe", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._CallableConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "attrs._ConverterType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "to_bool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.converters.to_bool", "name": "to_bool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_bool", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/opt/homebrew/lib/python3.11/site-packages/attr/converters.pyi"}