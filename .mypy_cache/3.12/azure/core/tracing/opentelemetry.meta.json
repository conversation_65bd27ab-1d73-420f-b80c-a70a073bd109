{"data_mtime": 1750965151, "dep_lines": [26, 25, 32, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 17, 11, 18, 21, 10], "dep_prios": [5, 5, 25, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5], "dependencies": ["azure.core.tracing._models", "azure.core._version", "azure.core.tracing", "__future__", "contextlib", "<PERSON><PERSON><PERSON>", "typing", "builtins", "_contextvars", "_frozen_importlib", "abc", "azure.core._enum_meta", "enum"], "hash": "a283e4691823b57460ce2d748279e41d013dcbe6", "id": "azure.core.tracing.opentelemetry", "ignore_all": true, "interface_hash": "fb7c5535506101be0fe771912ee55f763b590b56", "mtime": 1750963073, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/tracing/opentelemetry.py", "plugin_data": null, "size": 9597, "suppressed": ["opentelemetry.trace.propagation", "opentelemetry.trace", "opentelemetry.propagate", "opentelemetry.context", "opentelemetry"], "version_id": "1.16.1"}