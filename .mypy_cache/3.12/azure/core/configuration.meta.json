{"data_mtime": 1750965151, "dep_lines": [33, 26, 27, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline", "azure.core.pipeline.policies._base", "azure.core.pipeline.policies._base_async"], "hash": "073a2e0f3811e50520857cffd8498c26762778a7", "id": "azure.core.configuration", "ignore_all": true, "interface_hash": "045a2f1bb36fe0f2181e0f38e5cb6e0fdf028485", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/configuration.py", "plugin_data": null, "size": 7191, "suppressed": [], "version_id": "1.16.1"}