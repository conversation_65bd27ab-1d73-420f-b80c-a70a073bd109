{"data_mtime": 1750965151, "dep_lines": [44, 45, 46, 39, 205, 26, 27, 28, 205, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies", "azure.core.pipeline._tools", "azure.core.pipeline.transport", "azure.core.pipeline", "concurrent.futures", "__future__", "logging", "typing", "concurrent", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline.policies._base", "azure.core.pipeline.transport._base", "contextlib", "types"], "hash": "b02cb1a64e768c537bf295984c8e21d71ee22a4f", "id": "azure.core.pipeline._base", "ignore_all": true, "interface_hash": "a1bc9148ecc11bca17c0c0591a179bad2c8c3a74", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/_base.py", "plugin_data": null, "size": 9835, "suppressed": [], "version_id": "1.16.1"}