{"data_mtime": 1750965151, "dep_lines": [34, 35, 42, 44, 33, 39, 40, 41, 43, 29, 27, 28, 29, 30, 31, 1, 1, 1, 1, 1, 1, 1, 47], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["azure.core.pipeline.policies", "azure.core.pipeline.transport", "azure.core.tracing.common", "azure.core.tracing._models", "azure.core.pipeline", "azure.core.rest", "azure.core.settings", "azure.core.tracing", "azure.core.instrumentation", "urllib.parse", "logging", "sys", "urllib", "typing", "types", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline.policies._base", "azure.core.pipeline.transport._base", "azure.core.rest._helpers", "azure.core.rest._rest_py3"], "hash": "da9b6b26f9fc3f8dc4f6952506f0c6e40c12eb20", "id": "azure.core.pipeline.policies._distributed_tracing", "ignore_all": true, "interface_hash": "fda1d6a8175b02f605576746db8be983db1d8fba", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/policies/_distributed_tracing.py", "plugin_data": null, "size": 12102, "suppressed": ["opentelemetry.trace"], "version_id": "1.16.1"}