{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies._redirect_async", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "kind": "Gdef"}, "AsyncHTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncHTTPResponseType", "name": "AsyncHTTPResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}}, "AsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.AsyncHttpResponse", "kind": "Gdef"}, "AsyncRedirectPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "name": "AsyncRedirectPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.policies._redirect_async", "mro": ["azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AsyncRedirectPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "AsyncHTTPResponseType"], "typeddict_type": null}}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect_async.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "LegacyAsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "kind": "Gdef"}, "LegacyHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "RedirectPolicyBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "kind": "Gdef"}, "TooManyRedirectsError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.TooManyRedirectsError", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect_async.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect_async.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect_async.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect_async.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect_async.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect_async.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "domain_changed": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._redirect.domain_changed", "kind": "Gdef"}, "get_domain": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._utils.get_domain", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/policies/_redirect_async.py"}