{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies._redirect", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllHttpResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.AllHttpResponseType", "name": "AllHttpResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.AsyncHttpResponse", "kind": "Gdef"}, "ClsRedirectPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "name": "ClsRedirectPolicy", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.HTTPPolicy", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "HttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpResponse", "kind": "Gdef"}, "LegacyAsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "kind": "Gdef"}, "LegacyHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "LegacyHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpResponse", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "RedirectPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.HTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicy", "name": "RedirectPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.policies._redirect", "mro": ["azure.core.pipeline.policies._redirect.RedirectPolicy", "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "azure.core.pipeline.policies._base.HTTPPolicy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicy.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._redirect.RedirectPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of RedirectPolicy", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._redirect.RedirectPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "RedirectPolicyBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "name": "RedirectPolicyBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._redirect", "mro": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", "builtins.object"], "names": {".class": "SymbolTable", "REDIRECT_HEADERS_BLACKLIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.REDIRECT_HEADERS_BLACKLIST", "name": "REDIRECT_HEADERS_BLACKLIST", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "REDIRECT_STATUSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.REDIRECT_STATUSES", "name": "REDIRECT_STATUSES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RedirectPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_redirect_on_status_codes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase._redirect_on_status_codes", "name": "_redirect_on_status_codes", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_remove_headers_on_redirect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase._remove_headers_on_redirect", "name": "_remove_headers_on_redirect", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "allow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.allow", "name": "allow", "setter_type": null, "type": "builtins.bool"}}, "configure_redirects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.configure_redirects", "name": "configure_redirects", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options"], "arg_types": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure_redirects of RedirectPolicyBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_redirect_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.get_redirect_location", "name": "get_redirect_location", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.AllHttpResponseType", "id": -1, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.get_redirect_location", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_redirect_location of RedirectPolicyBase", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.AllHttpResponseType", "id": -1, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.get_redirect_location", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}}}, "increment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "settings", "response", "redirect_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.increment", "name": "increment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "settings", "response", "redirect_location"], "arg_types": ["azure.core.pipeline.policies._redirect.RedirectPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.AllHttpResponseType", "id": -1, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "increment of RedirectPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.AllHttpResponseType", "id": -1, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}}}, "max_redirects": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.max_redirects", "name": "max_redirects", "setter_type": null, "type": "builtins.int"}}, "no_redirects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "name": "no_redirects", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "id": -1, "name": "ClsRedirectPolicy", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "no_redirects of RedirectPolicyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "id": -1, "name": "ClsRedirectPolicy", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "id": -1, "name": "ClsRedirectPolicy", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "name": "no_redirects", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "id": -1, "name": "ClsRedirectPolicy", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "no_redirects of RedirectPolicyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "id": -1, "name": "ClsRedirectPolicy", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.ClsRedirectPolicy", "id": -1, "name": "ClsRedirectPolicy", "namespace": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.no_redirects", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._redirect.RedirectPolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._redirect.RedirectPolicyBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestHistory": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.RequestHistory", "kind": "Gdef"}, "TooManyRedirectsError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.TooManyRedirectsError", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._redirect._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._redirect.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "domain_changed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["original_domain", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._redirect.domain_changed", "name": "domain_changed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["original_domain", "url"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "domain_changed", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_domain": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._utils.get_domain", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/policies/_redirect.py"}