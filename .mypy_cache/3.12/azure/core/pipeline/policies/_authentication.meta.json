{"data_mtime": 1750965151, "dep_lines": [23, 16, 21, 9, 15, 20, 22, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies._utils", "azure.core.pipeline.transport", "azure.core.pipeline.policies", "azure.core.credentials", "azure.core.pipeline", "azure.core.rest", "azure.core.exceptions", "time", "base64", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "azure.core.pipeline.policies._base", "azure.core.pipeline.transport._base", "azure.core.rest._helpers", "azure.core.rest._rest_py3", "contextlib", "types"], "hash": "4aac613d874fa3017611cfa3ccbdadf38f670071", "id": "azure.core.pipeline.policies._authentication", "ignore_all": true, "interface_hash": "8cb15cca834d90699b093dec76e10a9e1731cb95", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/policies/_authentication.py", "plugin_data": null, "size": 13283, "suppressed": [], "version_id": "1.16.1"}