{"data_mtime": 1750965151, "dep_lines": [41, 27, 28, 29, 31, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 30], "dependencies": ["azure.core.pipeline", "abc", "copy", "logging", "typing", "builtins", "_frozen_importlib"], "hash": "45e406e0672e71ae3215849d5b1b9e59d17a0ecc", "id": "azure.core.pipeline.policies._base", "ignore_all": true, "interface_hash": "b3a3a062621c22eebf515dbcd2a809980e00eacc", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/policies/_base.py", "plugin_data": null, "size": 5461, "suppressed": [], "version_id": "1.16.1"}