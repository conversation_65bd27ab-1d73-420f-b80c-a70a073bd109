{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies._universal", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef"}, "ContentDecodePolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy", "name": "ContentDecodePolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.ContentDecodePolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "CONTEXT_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.CONTEXT_NAME", "name": "CONTEXT_NAME", "setter_type": null, "type": "builtins.str"}}, "JSON_REGEXP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.JSON_REGEXP", "name": "JSON_REGEXP", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "response_encoding", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "response_encoding", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.ContentDecodePolicy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ContentDecodePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_response_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy._response_encoding", "name": "_response_encoding", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "deserialize_from_http_generics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "response", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_http_generics", "name": "deserialize_from_http_generics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "response", "encoding"], "arg_types": [{".class": "TypeType", "item": "azure.core.pipeline.policies._universal.ContentDecodePolicy"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "deserialize_from_http_generics of ContentDecodePolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_http_generics", "name": "deserialize_from_http_generics", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "response", "encoding"], "arg_types": [{".class": "TypeType", "item": "azure.core.pipeline.policies._universal.ContentDecodePolicy"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "deserialize_from_http_generics of ContentDecodePolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "deserialize_from_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "data", "mime_type", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "name": "deserialize_from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "data", "mime_type", "response"], "arg_types": [{".class": "TypeType", "item": "azure.core.pipeline.policies._universal.ContentDecodePolicy"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "deserialize_from_text of ContentDecodePolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "name": "deserialize_from_text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "data", "mime_type", "response"], "arg_types": [{".class": "TypeType", "item": "azure.core.pipeline.policies._universal.ContentDecodePolicy"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "deserialize_from_text of ContentDecodePolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "azure.core.pipeline.policies._universal.ContentDecodePolicy.deserialize_from_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.ContentDecodePolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of ContentDecodePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "arg_types": ["azure.core.pipeline.policies._universal.ContentDecodePolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_response of ContentDecodePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.ContentDecodePolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.ContentDecodePolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DecodeError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.DecodeError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ET": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.pipeline.policies._universal.HTTPRequestType", "line": 54, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.rest._rest_py3.HttpRequest"], "uses_pep604_syntax": false}}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.pipeline.policies._universal.HTTPResponseType", "line": 55, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["azure.core.pipeline.transport._base._HttpResponseBase", "azure.core.rest._rest_py3._HttpResponseBase"], "uses_pep604_syntax": false}}}, "HeadersPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy", "name": "HeadersPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.HeadersPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "base_headers", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "base_headers", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.HeadersPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HeadersPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy._headers", "name": "_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy.add_header", "name": "add_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["azure.core.pipeline.policies._universal.HeadersPolicy", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_header of HeadersPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.policies._universal.HeadersPolicy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HeadersPolicy", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy.headers", "name": "headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.policies._universal.HeadersPolicy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HeadersPolicy", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.HeadersPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of HeadersPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.HeadersPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.HeadersPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpLoggingPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": "azure.core.pipeline.policies._universal._HiddenClassProperties", "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy", "name": "HttpLoggingPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy", "has_param_spec_type": false, "metaclass_type": "azure.core.pipeline.policies._universal._HiddenClassProperties", "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.HttpLoggingPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_HEADERS_ALLOWLIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.DEFAULT_HEADERS_ALLOWLIST", "name": "DEFAULT_HEADERS_ALLOWLIST", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "MULTI_RECORD_LOG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.MULTI_RECORD_LOG", "name": "MULTI_RECORD_LOG", "setter_type": null, "type": "builtins.str"}}, "REDACTED_PLACEHOLDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.REDACTED_PLACEHOLDER", "name": "REDACTED_PLACEHOLDER", "setter_type": null, "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "logger", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "logger", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.HttpLoggingPolicy", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HttpLoggingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_redact_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy._redact_header", "name": "_redact_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["azure.core.pipeline.policies._universal.HttpLoggingPolicy", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_redact_header of HttpLoggingPolicy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_redact_query_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy._redact_query_param", "name": "_redact_query_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["azure.core.pipeline.policies._universal.HttpLoggingPolicy", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_redact_query_param of HttpLoggingPolicy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_header_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.allowed_header_names", "name": "allowed_header_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "allowed_query_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.allowed_query_params", "name": "allowed_query_params", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.HttpLoggingPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of HttpLoggingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "arg_types": ["azure.core.pipeline.policies._universal.HttpLoggingPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_response of HttpLoggingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.HttpLoggingPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.HttpLoggingPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "LegacyHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "LegacySansIOHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base._HttpResponseBase", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NetworkTraceLoggingPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "name": "NetworkTraceLoggingPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "logging_enable", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "logging_enable", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NetworkTraceLoggingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_http_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy.enable_http_logger", "name": "enable_http_logger", "setter_type": null, "type": "builtins.bool"}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of NetworkTraceLoggingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "arg_types": ["azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_response of NetworkTraceLoggingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "PipelineResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.pipeline.policies._universal.PipelineResponseType", "line": 56, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}}}, "ProxyPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.ProxyPolicy", "name": "ProxyPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.ProxyPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.ProxyPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "proxies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ProxyPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.ProxyPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ProxyPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.ProxyPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.ProxyPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of ProxyPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "proxies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.ProxyPolicy.proxies", "name": "proxies", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.ProxyPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.ProxyPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestIdPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy", "name": "RequestIdPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.RequestIdPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "request_id", "auto_request_id", "request_id_header_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "request_id", "auto_request_id", "request_id_header_name", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.RequestIdPolicy", {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RequestIdPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_request_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy._auto_request_id", "name": "_auto_request_id", "setter_type": null, "type": "builtins.bool"}}, "_request_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy._request_id", "name": "_request_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}}}, "_request_id_header_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy._request_id_header_name", "name": "_request_id_header_name", "setter_type": null, "type": "builtins.str"}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.RequestIdPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of RequestIdPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_request_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy.set_request_id", "name": "set_request_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["azure.core.pipeline.policies._universal.RequestIdPolicy", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_request_id of RequestIdPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.RequestIdPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.RequestIdPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "SansIOHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3._HttpResponseBase", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UserAgentPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPResponseType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy", "name": "UserAgentPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal.UserAgentPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "_ENV_ADDITIONAL_USER_AGENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy._ENV_ADDITIONAL_USER_AGENT", "name": "_ENV_ADDITIONAL_USER_AGENT", "setter_type": null, "type": "builtins.str"}}, "_USERAGENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy._USERAGENT", "name": "_USERAGENT", "setter_type": null, "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "base_user_agent", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "base_user_agent", "kwargs"], "arg_types": ["azure.core.pipeline.policies._universal.UserAgentPolicy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UserAgentPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_user_agent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy._user_agent", "name": "_user_agent", "setter_type": null, "type": "builtins.str"}}, "add_user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.add_user_agent", "name": "add_user_agent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["azure.core.pipeline.policies._universal.UserAgentPolicy", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_user_agent of UserAgentPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["azure.core.pipeline.policies._universal.UserAgentPolicy", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._universal.HTTPRequestType"}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_request of UserAgentPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overwrite": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.overwrite", "name": "overwrite", "setter_type": null, "type": "builtins.bool"}}, "use_env": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.use_env", "name": "use_env", "setter_type": null, "type": "builtins.bool"}}, "user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.user_agent", "name": "user_agent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.policies._universal.UserAgentPolicy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "user_agent of UserAgentPolicy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.user_agent", "name": "user_agent", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.policies._universal.UserAgentPolicy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "user_agent of UserAgentPolicy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal.UserAgentPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal.UserAgentPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HiddenClassProperties": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties", "name": "_HiddenClassProperties", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal._HiddenClassProperties", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_HEADERS_ALLOWLIST": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties.DEFAULT_HEADERS_ALLOWLIST", "name": "DEFAULT_HEADERS_ALLOWLIST", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "DEFAULT_HEADERS_WHITELIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties.DEFAULT_HEADERS_WHITELIST", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties.DEFAULT_HEADERS_WHITELIST", "name": "DEFAULT_HEADERS_WHITELIST", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["azure.core.pipeline.policies._universal._HiddenClassProperties"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DEFAULT_HEADERS_WHITELIST of _HiddenClassProperties", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties.DEFAULT_HEADERS_WHITELIST", "name": "DEFAULT_HEADERS_WHITELIST", "setter_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": ["azure.core.pipeline.policies._universal._HiddenClassProperties", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DEFAULT_HEADERS_WHITELIST of _HiddenClassProperties", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["azure.core.pipeline.policies._universal._HiddenClassProperties"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DEFAULT_HEADERS_WHITELIST of _HiddenClassProperties", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties.DEFAULT_HEADERS_WHITELIST", "name": "DEFAULT_HEADERS_WHITELIST", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": ["azure.core.pipeline.policies._universal._HiddenClassProperties", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DEFAULT_HEADERS_WHITELIST of _HiddenClassProperties", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "", "name": "DEFAULT_HEADERS_WHITELIST", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": ["azure.core.pipeline.policies._universal._HiddenClassProperties", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DEFAULT_HEADERS_WHITELIST of _HiddenClassProperties", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": 1, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["azure.core.pipeline.policies._universal._HiddenClassProperties"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DEFAULT_HEADERS_WHITELIST of _HiddenClassProperties", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal._HiddenClassProperties.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal._HiddenClassProperties", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._universal._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "_Unset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._universal._Unset", "name": "_Unset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._universal._Unset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._universal", "mro": ["azure.core.pipeline.policies._universal._Unset", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._universal._Unset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._universal._Unset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._universal.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._universal.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._universal.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._universal.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._universal.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._universal.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "azcore_version": {".class": "SymbolTableNode", "cross_ref": "azure.core.__version__", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/policies/_universal.py"}