{".class": "MypyFile", "_fullname": "azure.core.pipeline.transport._base_async", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncContextManager": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncContextManager", "kind": "Gdef"}, "AsyncHTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "name": "AsyncHTTPResponseType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "AsyncHttpClientTransportResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.transport._base._HttpClientTransportResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpClientTransportResponse", "name": "AsyncHttpClientTransportResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpClientTransportResponse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._base_async", "mro": ["azure.core.pipeline.transport._base_async.AsyncHttpClientTransportResponse", "azure.core.pipeline.transport._base._HttpClientTransportResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "azure.core.pipeline.transport._base._HttpResponseBase", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpClientTransportResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._base_async.AsyncHttpClientTransportResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncHttpResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.transport._base._HttpResponseBase", {".class": "Instance", "args": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.AsyncContextManager"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "name": "AsyncHttpResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._base_async", "mro": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse", "azure.core.pipeline.transport._base._HttpResponseBase", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpResponse.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncHttpResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpResponse.parts", "name": "parts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parts of AsyncHttpResponse", "ret_type": {".class": "Instance", "args": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream_download": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "pipeline", "decompress", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpResponse.stream_download", "name": "stream_download", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "pipeline", "decompress", "kwargs"], "arg_types": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse", {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stream_download of AsyncHttpResponse", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncHttpTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__aexit__", 1], ["close", 1], ["open", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "extra_attrs": null, "type_ref": "typing.AsyncContextManager"}, "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "name": "AsyncHttpTransport", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._base_async", "mro": ["azure.core.pipeline.transport._base_async.AsyncHttpTransport", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.close", "name": "close", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "open of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.open", "name": "open", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "open of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.send", "name": "send", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sleep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "duration"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}, "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sleep of AsyncHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHttpTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "AsyncHTTPResponseType"], "typeddict_type": null}}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncIteratorType": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncPipeline": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._base_async.AsyncPipeline", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_HttpClientTransportResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base._HttpClientTransportResponse", "kind": "Gdef"}, "_HttpResponseBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base._HttpResponseBase", "kind": "Gdef"}, "_PartGenerator": {".class": "SymbolTableNode", "cross_ref": "azure.core.utils._pipeline_transport_rest_shared_async._PartGenerator", "kind": "Gdef"}, "_ResponseStopIteration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._base_async._ResponseStopIteration", "name": "_ResponseStopIteration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._base_async._ResponseStopIteration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.transport._base_async", "mro": ["azure.core.pipeline.transport._base_async._ResponseStopIteration", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._base_async._ResponseStopIteration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._base_async._ResponseStopIteration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._base_async.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._base_async.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._base_async.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._base_async.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._base_async.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._base_async.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_iterate_response_content": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._base_async._iterate_response_content", "name": "_iterate_response_content", "type": null}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/transport/_base_async.py"}