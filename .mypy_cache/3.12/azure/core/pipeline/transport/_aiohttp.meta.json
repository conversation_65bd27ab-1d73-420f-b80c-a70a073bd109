{"data_mtime": 1750965151, "dep_lines": [57, 58, 59, 63, 64, 73, 49, 50, 55, 69, 40, 26, 27, 28, 39, 42, 43, 44, 173, 416, 520, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 46, 45, 47, 518, 515], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 5, 25, 5, 5, 10, 5, 5, 10, 10, 10, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 5, 20, 20], "dependencies": ["azure.core.pipeline.transport._base", "azure.core.pipeline.transport._base_async", "azure.core.utils._pipeline_transport_rest_shared", "azure.core.pipeline._tools", "azure.core.pipeline._tools_async", "azure.core.rest._aiohttp", "azure.core.configuration", "azure.core.exceptions", "azure.core.pipeline", "azure.core.rest", "collections.abc", "__future__", "sys", "typing", "types", "logging", "asyncio", "codecs", "ssl", "zlib", "charset_normalizer", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "azure.core.pipeline._base_async", "azure.core.rest._helpers", "azure.core.rest._http_response_impl", "azure.core.rest._http_response_impl_async", "azure.core.rest._rest_py3", "contextlib"], "hash": "aaefba2a85811763775194a5c27b2779b650cb75", "id": "azure.core.pipeline.transport._aiohttp", "ignore_all": true, "interface_hash": "c4ebc2996397b9854fdc2028e6d3b5bcb4819d51", "mtime": 1750963073, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/transport/_aiohttp.py", "plugin_data": null, "size": 22602, "suppressed": ["aiohttp.client_exceptions", "aiohttp", "multidict", "chardet", "cchardet"], "version_id": "1.16.1"}