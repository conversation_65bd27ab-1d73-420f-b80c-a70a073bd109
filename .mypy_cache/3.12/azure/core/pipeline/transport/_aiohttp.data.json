{".class": "MypyFile", "_fullname": "azure.core.pipeline.transport._aiohttp", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AioHttpStreamDownloadGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", "name": "AioHttpStreamDownloadGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._aiohttp", "mro": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", "typing.AsyncIterator", "typing.AsyncIterable", "builtins.object"], "names": {".class": "SymbolTable", "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__anext__", "name": "__anext__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__init__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, {".class": "UnionType", "items": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "azure.core.rest._aiohttp.RestAioHttpTransportResponse"], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.rest._rest_py3.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "azure.core.rest._aiohttp.RestAioHttpTransportResponse", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.rest._rest_py3.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "azure.core.rest._aiohttp.RestAioHttpTransportResponse", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "pipeline", "response", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", {".class": "Instance", "args": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.rest._rest_py3.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "azure.core.rest._aiohttp.RestAioHttpTransportResponse", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpStreamDownloadGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.__len__", "name": "__len__", "type": null}}, "_decompress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator._decompress", "name": "_decompress", "setter_type": null, "type": "builtins.bool"}}, "_decompressor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator._decompressor", "name": "_decompressor", "setter_type": null, "type": {".class": "NoneType"}}}, "block_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.block_size", "name": "block_size", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "uses_pep604_syntax": false}}}, "content_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.content_length", "name": "content_length", "setter_type": null, "type": "builtins.int"}}, "pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.pipeline", "name": "pipeline", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.request", "name": "request", "setter_type": null, "type": {".class": "UnionType", "items": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.rest._rest_py3.HttpRequest"], "uses_pep604_syntax": false}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.response", "name": "response", "setter_type": null, "type": {".class": "UnionType", "items": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "azure.core.rest._aiohttp.RestAioHttpTransportResponse"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._aiohttp.AioHttpStreamDownloadGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AioHttpTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport", "name": "AioHttpTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._aiohttp", "mro": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "session", "loop", "session_owner", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "session", "loop", "session_owner", "kwargs"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "azure.core.pipeline.transport._aiohttp.aiohttp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_ssl_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cert", "verify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport._build_ssl_config", "name": "_build_ssl_config", "type": null}}, "_get_request_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport._get_request_data", "name": "_get_request_data", "type": null}}, "_has_been_opened": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport._has_been_opened", "name": "_has_been_opened", "setter_type": null, "type": "builtins.bool"}}, "_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport._loop", "name": "_loop", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_session_owner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport._session_owner", "name": "_session_owner", "setter_type": null, "type": "builtins.bool"}}, "_use_env_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport._use_env_settings", "name": "_use_env_settings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.close", "name": "close", "type": null}}, "connection_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.connection_config", "name": "connection_config", "setter_type": null, "type": "azure.core.configuration.ConnectionConfiguration"}}, "open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.open", "name": "open", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.send", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", {".class": "UnionType", "items": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.rest._rest_py3.HttpRequest"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.pipeline.transport._base.HttpRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.send", "name": "send", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.pipeline.transport._base.HttpRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.rest._rest_py3.HttpRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "azure.core.rest._rest_py3.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.send", "name": "send", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.rest._rest_py3.HttpRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "azure.core.rest._rest_py3.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.pipeline.transport._base.HttpRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "request", "stream", "proxies", "config"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransport", "azure.core.rest._rest_py3.HttpRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AioHttpTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "azure.core.rest._rest_py3.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.session", "name": "session", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "azure.core.pipeline.transport._aiohttp.aiohttp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._aiohttp.AioHttpTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AioHttpTransportResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "name": "AioHttpTransportResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._aiohttp", "mro": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "azure.core.pipeline.transport._base._HttpResponseBase", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5], "arg_names": ["self", "request", "aiohttp_response", "block_size", "decompress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5], "arg_names": ["self", "request", "aiohttp_response", "block_size", "decompress"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "azure.core.pipeline.transport._base.HttpRequest", {".class": "AnyType", "missing_import_name": "azure.core.pipeline.transport._aiohttp.aiohttp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AioHttpTransportResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse._content", "name": "_content", "setter_type": null, "type": {".class": "NoneType"}}}, "_decompress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse._decompress", "name": "_decompress", "setter_type": null, "type": "builtins.bool"}}, "_decompressed_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse._decompressed_content", "name": "_decompressed_content", "setter_type": null, "type": "builtins.bool"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.body", "name": "body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "body of AioHttpTransportResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.load_body", "name": "load_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_body of AioHttpTransportResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream_download": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "pipeline", "decompress", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.stream_download", "name": "stream_download", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "pipeline", "decompress", "kwargs"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stream_download of AioHttpTransportResponse", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "encoding"], "arg_types": ["azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of AioHttpTransportResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._aiohttp.AioHttpTransportResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "kind": "Gdef"}, "AsyncHttpTransport": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncIteratorType": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncPipeline": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._base_async.AsyncPipeline", "kind": "Gdef"}, "CIMultiDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.CIMultiDict", "name": "CIMultiDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.core.pipeline.transport._aiohttp.CIMultiDict", "source_any": null, "type_of_any": 3}}}, "CONTENT_CHUNK_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.transport._aiohttp.CONTENT_CHUNK_SIZE", "name": "CONTENT_CHUNK_SIZE", "setter_type": null, "type": "builtins.int"}}, "ConnectionConfiguration": {".class": "SymbolTableNode", "cross_ref": "azure.core.configuration.ConnectionConfiguration", "kind": "Gdef"}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "IncompleteReadError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.IncompleteReadError", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RestAioHttpTransportResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._aiohttp.RestAioHttpTransportResponse", "kind": "Gdef"}, "RestAsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.AsyncHttpResponse", "kind": "Gdef"}, "RestHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "ServiceRequestError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceRequestError", "kind": "Gdef"}, "ServiceResponseError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceResponseError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.transport._aiohttp._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "_ResponseStopIteration": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async._ResponseStopIteration", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._aiohttp.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._aiohttp.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._aiohttp.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._aiohttp.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._aiohttp.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._aiohttp.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_aiohttp_body_helper": {".class": "SymbolTableNode", "cross_ref": "azure.core.utils._pipeline_transport_rest_shared._aiohttp_body_helper", "kind": "Gdef"}, "_handle_no_stream_rest_response": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._tools_async.handle_no_stream_rest_response", "kind": "Gdef"}, "_is_rest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._tools.is_rest", "kind": "Gdef"}, "aiohttp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._aiohttp.aiohttp", "name": "aiohttp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.core.pipeline.transport._aiohttp.aiohttp", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "codecs": {".class": "SymbolTableNode", "cross_ref": "codecs", "kind": "Gdef"}, "get_file_items": {".class": "SymbolTableNode", "cross_ref": "azure.core.utils._pipeline_transport_rest_shared.get_file_items", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/transport/_aiohttp.py"}