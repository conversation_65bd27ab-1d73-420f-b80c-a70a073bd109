{"data_mtime": 1750965151, "dep_lines": [56, 57, 54, 58, 411, 36, 46, 47, 64, 37, 26, 27, 44, 136, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 5, 5, 25, 5, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.transport._base", "azure.core.pipeline.transport._bigger_block_size_http_adapters", "azure.core.pipeline.transport", "azure.core.pipeline._tools", "azure.core.rest._requests_basic", "urllib3.util.retry", "azure.core.configuration", "azure.core.exceptions", "azure.core.rest", "urllib3.exceptions", "logging", "typing", "requests", "codecs", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "azure.core.rest._helpers", "azure.core.rest._http_response_impl", "azure.core.rest._rest_py3", "contextlib", "http", "http.client", "requests.adapters", "requests.exceptions", "requests.models", "requests.sessions", "types", "urllib3", "urllib3.connection", "urllib3.util"], "hash": "3f6c544a069708e411f4cd67f52b145537779d60", "id": "azure.core.pipeline.transport._requests_basic", "ignore_all": true, "interface_hash": "60976807efb7079028fcce0502b3550462c47c99", "mtime": 1750963073, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/transport/_requests_basic.py", "plugin_data": null, "size": 16432, "suppressed": [], "version_id": "1.16.1"}