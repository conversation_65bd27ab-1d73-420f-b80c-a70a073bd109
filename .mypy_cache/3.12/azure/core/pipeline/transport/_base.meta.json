{"data_mtime": 1750965151, "dep_lines": [58, 59, 60, 79, 34, 57, 78, 28, 33, 34, 55, 26, 27, 29, 30, 31, 32, 34, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 10, 5, 25, 5, 5, 20, 5, 5, 10, 10, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies", "azure.core.utils._utils", "azure.core.utils._pipeline_transport_rest_shared", "azure.core.rest._helpers", "xml.etree.ElementTree", "azure.core.exceptions", "azure.core.pipeline", "email.message", "urllib.parse", "xml.etree", "http.client", "__future__", "abc", "json", "logging", "time", "copy", "xml", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "azure.core.pipeline._base", "azure.core.pipeline.policies._base", "azure.core.utils", "contextlib", "email", "json.encoder", "types"], "hash": "945c0f1b8afee78d7b79dfdbf650bfb60a837216", "id": "azure.core.pipeline.transport._base", "ignore_all": true, "interface_hash": "70623430e3ab4d4f840f511689e16d89d2bcf83f", "mtime": 1750963073, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/pipeline/transport/_base.py", "plugin_data": null, "size": 32455, "suppressed": [], "version_id": "1.16.1"}