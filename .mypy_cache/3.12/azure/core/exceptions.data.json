{".class": "MypyFile", "_fullname": "azure.core.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AzureError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.AzureError", "name": "AzureError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.AzureError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "message", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.AzureError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "message", "args", "kwargs"], "arg_types": ["azure.core.exceptions.AzureError", {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AzureError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__traceback__": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.__traceback__", "name": "__traceback__", "setter_type": null, "type": {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "continuation_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.continuation_token", "name": "continuation_token", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "exc_msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.exc_msg", "name": "exc_msg", "setter_type": null, "type": "builtins.str"}}, "exc_traceback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.exc_traceback", "name": "exc_traceback", "setter_type": null, "type": {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "exc_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.exc_type", "name": "exc_type", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "exc_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.exc_value", "name": "exc_value", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "inner_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.inner_exception", "name": "inner_exception", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.AzureError.message", "name": "message", "setter_type": null, "type": "builtins.str"}}, "raise_with_traceback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.AzureError.raise_with_traceback", "name": "raise_with_traceback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions.AzureError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "raise_with_traceback of AzureError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.AzureError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.AzureError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClientAuthenticationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ClientAuthenticationError", "name": "ClientAuthenticationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ClientAuthenticationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ClientAuthenticationError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ClientAuthenticationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ClientAuthenticationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DecodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.DecodeError", "name": "DecodeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.DecodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.DecodeError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.DecodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.DecodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeserializationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.DeserializationError", "name": "DeserializationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.DeserializationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.DeserializationError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.DeserializationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.DeserializationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ErrorMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ErrorMap", "name": "ErrorMap", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ErrorMap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ErrorMap", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 4], "arg_names": ["self", "custom_error_map", "default_error", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ErrorMap.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 4], "arg_names": ["self", "custom_error_map", "default_error", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.exceptions.ErrorMap"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ErrorMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_custom_error_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.exceptions.ErrorMap._custom_error_map", "name": "_custom_error_map", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_default_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.exceptions.ErrorMap._default_error", "name": "_default_error", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ErrorMap.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.exceptions.ErrorMap"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of ErrorMap", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ErrorMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "id": 1, "name": "KeyType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "id": 2, "name": "ValueType", "namespace": "azure.core.exceptions.ErrorMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.exceptions.ErrorMap"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["KeyType", "ValueType"], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HttpResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.AzureError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.HttpResponseError", "name": "HttpResponseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.HttpResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "message", "response", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.HttpResponseError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "message", "response", "kwargs"], "arg_types": ["azure.core.exceptions.HttpResponseError", {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["azure.core.exceptions._HttpResponseCommonAPI", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HttpResponseError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.HttpResponseError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.core.exceptions.HttpResponseError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of HttpResponseError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_odata_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["error_format", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "azure.core.exceptions.HttpResponseError._parse_odata_body", "name": "_parse_odata_body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["error_format", "response"], "arg_types": [{".class": "TypeType", "item": "azure.core.exceptions.ODataV4Format"}, {".class": "UnionType", "items": ["azure.core.exceptions._HttpResponseCommonAPI", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_odata_body of HttpResponseError", "ret_type": {".class": "UnionType", "items": ["azure.core.exceptions.ODataV4Format", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.HttpResponseError._parse_odata_body", "name": "_parse_odata_body", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["error_format", "response"], "arg_types": [{".class": "TypeType", "item": "azure.core.exceptions.ODataV4Format"}, {".class": "UnionType", "items": ["azure.core.exceptions._HttpResponseCommonAPI", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_odata_body of HttpResponseError", "ret_type": {".class": "UnionType", "items": ["azure.core.exceptions.ODataV4Format", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.HttpResponseError.error", "name": "error", "setter_type": null, "type": {".class": "UnionType", "items": ["azure.core.exceptions.ODataV4Format", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.HttpResponseError.model", "name": "model", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.HttpResponseError.reason", "name": "reason", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.HttpResponseError.response", "name": "response", "setter_type": null, "type": {".class": "UnionType", "items": ["azure.core.exceptions._HttpResponseCommonAPI", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.HttpResponseError.status_code", "name": "status_code", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HttpResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.HttpResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncompleteReadError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.DecodeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.IncompleteReadError", "name": "IncompleteReadError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.IncompleteReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.IncompleteReadError", "azure.core.exceptions.DecodeError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.IncompleteReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.IncompleteReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.KeyType", "name": "KeyType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "ODataV4Error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ODataV4Error", "name": "ODataV4Error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ODataV4Error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ODataV4Error", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "_ERROR_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.ODataV4Error._ERROR_FORMAT", "name": "_ERROR_FORMAT", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["json_object"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "azure.core.exceptions.ODataV4Format", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "response", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ODataV4Error.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "response", "kwargs"], "arg_types": ["azure.core.exceptions.ODataV4Error", "azure.core.exceptions._HttpResponseCommonAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ODataV4Error", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ODataV4Error.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.core.exceptions.ODataV4Error"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of ODataV4Error", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_error_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Error._error_format", "name": "_error_format", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "azure.core.exceptions.ODataV4Format", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Error.code", "name": "code", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "details": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Error.details", "name": "details", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "innererror": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Error.innererror", "name": "innererror", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "odata_json": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Error.odata_json", "name": "odata_json", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Error.target", "name": "target", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ODataV4Error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ODataV4Error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ODataV4Format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ODataV4Format", "name": "ODataV4Format", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ODataV4Format", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ODataV4Format", "builtins.object"], "names": {".class": "SymbolTable", "CODE_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.ODataV4Format.CODE_LABEL", "name": "CODE_LABEL", "setter_type": null, "type": "builtins.str"}}, "DETAILS_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.ODataV4Format.DETAILS_LABEL", "name": "DETAILS_LABEL", "setter_type": null, "type": "builtins.str"}}, "INNERERROR_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.ODataV4Format.INNERERROR_LABEL", "name": "INNERERROR_LABEL", "setter_type": null, "type": "builtins.str"}}, "MESSAGE_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.ODataV4Format.MESSAGE_LABEL", "name": "MESSAGE_LABEL", "setter_type": null, "type": "builtins.str"}}, "TARGET_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.ODataV4Format.TARGET_LABEL", "name": "TARGET_LABEL", "setter_type": null, "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_object"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ODataV4Format.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_object"], "arg_types": ["azure.core.exceptions.ODataV4Format", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ODataV4Format", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ODataV4Format.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.core.exceptions.ODataV4Format"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of ODataV4Format", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Format.code", "name": "code", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "details": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Format.details", "name": "details", "setter_type": null, "type": {".class": "Instance", "args": ["azure.core.exceptions.ODataV4Format"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "azure.core.exceptions.ODataV4Format.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "id": -1, "name": "SelfODataV4Format", "namespace": "azure.core.exceptions.ODataV4Format.error", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "error of ODataV4Format", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "id": -1, "name": "SelfODataV4Format", "namespace": "azure.core.exceptions.ODataV4Format.error", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "id": -1, "name": "SelfODataV4Format", "namespace": "azure.core.exceptions.ODataV4Format.error", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Format.error", "name": "error", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "id": -1, "name": "SelfODataV4Format", "namespace": "azure.core.exceptions.ODataV4Format.error", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "error of ODataV4Format", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "id": -1, "name": "SelfODataV4Format", "namespace": "azure.core.exceptions.ODataV4Format.error", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "id": -1, "name": "SelfODataV4Format", "namespace": "azure.core.exceptions.ODataV4Format.error", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}]}}}}, "innererror": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Format.innererror", "name": "innererror", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Format.message", "name": "message", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "message_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ODataV4Format.message_details", "name": "message_details", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions.ODataV4Format"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "message_details of ODataV4Format", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions.ODataV4Format.target", "name": "target", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ODataV4Format.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef", "module_public": false}, "RequestHistory": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.RequestHistory", "kind": "Gdef", "module_public": false}, "ResourceExistsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ResourceExistsError", "name": "ResourceExistsError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ResourceExistsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ResourceExistsError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ResourceExistsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ResourceExistsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceModifiedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ResourceModifiedError", "name": "ResourceModifiedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ResourceModifiedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ResourceModifiedError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ResourceModifiedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ResourceModifiedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ResourceNotFoundError", "name": "ResourceNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ResourceNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ResourceNotFoundError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ResourceNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ResourceNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceNotModifiedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ResourceNotModifiedError", "name": "ResourceNotModifiedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ResourceNotModifiedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ResourceNotModifiedError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ResourceNotModifiedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ResourceNotModifiedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseNotReadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.AzureError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ResponseNotReadError", "name": "ResponseNotReadError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ResponseNotReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ResponseNotReadError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.ResponseNotReadError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["azure.core.exceptions.ResponseNotReadError", "azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseNotReadError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ResponseNotReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ResponseNotReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelfODataV4Format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SelfODataV4Format", "name": "SelfODataV4Format", "upper_bound": "azure.core.exceptions.ODataV4Format", "values": [], "variance": 0}}, "SerializationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.SerializationError", "name": "SerializationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.SerializationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.SerializationError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.SerializationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.SerializationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceRequestError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.AzureError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ServiceRequestError", "name": "ServiceRequestError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ServiceRequestError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ServiceRequestError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ServiceRequestError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ServiceRequestError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceRequestTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.ServiceRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ServiceRequestTimeoutError", "name": "ServiceRequestTimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ServiceRequestTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ServiceRequestTimeoutError", "azure.core.exceptions.ServiceRequestError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ServiceRequestTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ServiceRequestTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.AzureError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ServiceResponseError", "name": "ServiceResponseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ServiceResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ServiceResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ServiceResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ServiceResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceResponseTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.ServiceResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.ServiceResponseTimeoutError", "name": "ServiceResponseTimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.ServiceResponseTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.ServiceResponseTimeoutError", "azure.core.exceptions.ServiceResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ServiceResponseTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.ServiceResponseTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamClosedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.AzureError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.StreamClosedError", "name": "StreamClosedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.StreamClosedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.StreamClosedError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.StreamClosedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["azure.core.exceptions.StreamClosedError", "azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StreamClosedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.StreamClosedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.StreamClosedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamConsumedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.AzureError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.StreamConsumedError", "name": "StreamConsumedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.StreamConsumedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.StreamConsumedError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.StreamConsumedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["azure.core.exceptions.StreamConsumedError", "azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StreamConsumedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.StreamConsumedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions.StreamConsumedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TooManyRedirectsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.exceptions.HttpResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions.TooManyRedirectsError", "name": "TooManyRedirectsError", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.TooManyRedirectsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions.TooManyRedirectsError", "azure.core.exceptions.HttpResponseError", "azure.core.exceptions.AzureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "history", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core.exceptions.TooManyRedirectsError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "history", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.exceptions.TooManyRedirectsError"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.RequestHistory"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TooManyRedirectsError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.exceptions.TooManyRedirectsError.history", "name": "history", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.RequestHistory"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.TooManyRedirectsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.exceptions.TooManyRedirectsError", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.exceptions.TooManyRedirectsError"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "ValueType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions.ValueType", "name": "ValueType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_HttpResponseCommonAPI": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["reason", 2], ["request", 2], ["status_code", 2], ["text", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.exceptions._HttpResponseCommonAPI", "name": "_HttpResponseCommonAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.exceptions", "mro": ["azure.core.exceptions._HttpResponseCommonAPI", "builtins.object"], "names": {".class": "SymbolTable", "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.reason", "name": "reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reason of _HttpResponseCommonAPI", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.reason", "name": "reason", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reason of _HttpResponseCommonAPI", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request of _HttpResponseCommonAPI", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.request", "name": "request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request of _HttpResponseCommonAPI", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.status_code", "name": "status_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "status_code of _HttpResponseCommonAPI", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.status_code", "name": "status_code", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "status_code of _HttpResponseCommonAPI", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "azure.core.exceptions._HttpResponseCommonAPI.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.exceptions._HttpResponseCommonAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of _HttpResponseCommonAPI", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.exceptions._HttpResponseCommonAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.exceptions._HttpResponseCommonAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.exceptions.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "map_error": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["status_code", "response", "error_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.map_error", "name": "map_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["status_code", "response", "error_map"], "arg_types": ["builtins.int", "azure.core.exceptions._HttpResponseCommonAPI", {".class": "Instance", "args": ["builtins.int", {".class": "TypeType", "item": "azure.core.exceptions.HttpResponseError"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "map_error", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raise_with_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["exception", "args", "message", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.exceptions.raise_with_traceback", "name": "raise_with_traceback", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["exception", "args", "message", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "raise_with_traceback", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.runtime_checkable", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/exceptions.py"}