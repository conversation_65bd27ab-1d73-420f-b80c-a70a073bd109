{".class": "MypyFile", "_fullname": "azure.core._pipeline_client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Configuration": {".class": "SymbolTableNode", "cross_ref": "azure.core.configuration.Configuration", "kind": "Gdef"}, "ContentDecodePolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.ContentDecodePolicy", "kind": "Gdef"}, "DistributedTracingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HttpLoggingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.HttpLoggingPolicy", "kind": "Gdef"}, "HttpTransport": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpTransport", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Pipeline": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._base.Pipeline", "kind": "Gdef"}, "PipelineClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.transport._base.PipelineClientBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core._pipeline_client.PipelineClient", "name": "PipelineClient", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core._pipeline_client.PipelineClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core._pipeline_client", "mro": ["azure.core._pipeline_client.PipelineClient", "azure.core.pipeline.transport._base.PipelineClientBase", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core._pipeline_client.PipelineClient.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of PipelineClient", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core._pipeline_client.PipelineClient.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of PipelineClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "base_url", "pipeline", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core._pipeline_client.PipelineClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "base_url", "pipeline", "config", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base.Pipeline"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.configuration.Configuration"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PipelineClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 4], "arg_names": ["self", "config", "transport", "policies", "per_call_policies", "per_retry_policies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core._pipeline_client.PipelineClient._build_pipeline", "name": "_build_pipeline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 4], "arg_names": ["self", "config", "transport", "policies", "per_call_policies", "per_retry_policies", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.configuration.Configuration"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base.HttpTransport"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_build_pipeline of PipelineClient", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base.Pipeline"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core._pipeline_client.PipelineClient._config", "name": "_config", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.configuration.Configuration"}}}, "_pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core._pipeline_client.PipelineClient._pipeline", "name": "_pipeline", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base.Pipeline"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core._pipeline_client.PipelineClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of PipelineClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "stream", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.core._pipeline_client.PipelineClient.send_request", "name": "send_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "stream", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_request of PipelineClient", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.PipelineClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core._pipeline_client.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core._pipeline_client.PipelineClient", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core._pipeline_client.PipelineClient"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "PipelineClientBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.PipelineClientBase", "kind": "Gdef"}, "RequestIdPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.RequestIdPolicy", "kind": "Gdef"}, "RetryPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._retry.RetryPolicy", "kind": "Gdef"}, "SensitiveHeaderCleanupPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._sensitive_header_cleanup_policy.SensitiveHeaderCleanupPolicy", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core._pipeline_client._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core._pipeline_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core._pipeline_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core._pipeline_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core._pipeline_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core._pipeline_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core._pipeline_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/core/_pipeline_client.py"}