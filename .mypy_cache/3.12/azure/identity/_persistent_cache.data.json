{".class": "MypyFile", "_fullname": "azure.identity._persistent_cache", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CACHE_CAE_SUFFIX": {".class": "SymbolTableNode", "cross_ref": "azure.identity._constants.CACHE_CAE_SUFFIX", "kind": "Gdef"}, "CACHE_NON_CAE_SUFFIX": {".class": "SymbolTableNode", "cross_ref": "azure.identity._constants.CACHE_NON_CAE_SUFFIX", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TokenCachePersistenceOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.identity._persistent_cache.TokenCachePersistenceOptions", "name": "TokenCachePersistenceOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.identity._persistent_cache.TokenCachePersistenceOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.identity._persistent_cache", "mro": ["azure.identity._persistent_cache.TokenCachePersistenceOptions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "allow_unencrypted_storage", "name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._persistent_cache.TokenCachePersistenceOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "allow_unencrypted_storage", "name", "kwargs"], "arg_types": ["azure.identity._persistent_cache.TokenCachePersistenceOptions", "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TokenCachePersistenceOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_unencrypted_storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._persistent_cache.TokenCachePersistenceOptions.allow_unencrypted_storage", "name": "allow_unencrypted_storage", "setter_type": null, "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._persistent_cache.TokenCachePersistenceOptions.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.identity._persistent_cache.TokenCachePersistenceOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.identity._persistent_cache.TokenCachePersistenceOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.identity._persistent_cache._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._persistent_cache.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._persistent_cache.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._persistent_cache.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._persistent_cache.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._persistent_cache.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._persistent_cache.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_persistence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["allow_unencrypted", "account_name", "cache_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._persistent_cache._get_persistence", "name": "_get_persistence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["allow_unencrypted", "account_name", "cache_name"], "arg_types": ["builtins.bool", "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_persistence", "ret_type": {".class": "AnyType", "missing_import_name": "azure.identity._persistent_cache.msal_extensions", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_persistent_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["options", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._persistent_cache._load_persistent_cache", "name": "_load_persistent_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["options", "is_cae"], "arg_types": ["azure.identity._persistent_cache.TokenCachePersistenceOptions", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_persistent_cache", "ret_type": {".class": "AnyType", "missing_import_name": "azure.identity._persistent_cache.msal_extensions", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "msal_extensions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.identity._persistent_cache.msal_extensions", "name": "msal_extensions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._persistent_cache.msal_extensions", "source_any": null, "type_of_any": 3}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_persistent_cache.py"}