{"data_mtime": 1750965152, "dep_lines": [16, 17, 18, 11, 12, 15, 19, 14, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["azure.identity._internal.decorators", "azure.identity._internal.msal_client", "azure.identity._internal.shared_token_cache", "azure.core.credentials", "azure.core.exceptions", "azure.identity._internal", "azure.identity._persistent_cache", "azure.identity", "platform", "time", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "azure.core", "azure.identity._auth_record", "azure.identity._exceptions", "azure.identity._internal.utils"], "hash": "418f61380ff0b71b1ac92b3f181bd64951c8ae07", "id": "azure.identity._credentials.silent", "ignore_all": true, "interface_hash": "fd221f469286c88c01b98e36d4e35b2f00935214", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/silent.py", "plugin_data": null, "size": 9329, "suppressed": ["msal"], "version_id": "1.16.1"}