{".class": "MypyFile", "_fullname": "azure.identity._credentials.on_behalf_of", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessTokenInfo", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AuthenticationRecord": {".class": "SymbolTableNode", "cross_ref": "azure.identity._auth_record.AuthenticationRecord", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClientAuthenticationError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ClientAuthenticationError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GetTokenMixin": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.get_token_mixin.GetTokenMixin", "kind": "Gdef"}, "MsalCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.msal_credentials.MsalCredential", "kind": "Gdef"}, "OnBehalfOfCredential": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.identity._internal.msal_credentials.MsalCredential", "azure.identity._internal.get_token_mixin.GetTokenMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "name": "OnBehalfOfCredential", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.identity._credentials.on_behalf_of", "mro": ["azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "azure.identity._internal.msal_credentials.MsalCredential", "azure.identity._internal.get_token_mixin.GetTokenMixin", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 3, 5, 5, 4], "arg_names": ["self", "tenant_id", "client_id", "client_certificate", "client_secret", "client_assertion_func", "user_assertion", "password", "send_certificate_chain", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 3, 5, 5, 4], "arg_names": ["self", "tenant_id", "client_id", "client_certificate", "client_secret", "client_assertion_func", "user_assertion", "password", "send_certificate_chain", "kwargs"], "arg_types": ["azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OnBehalfOfCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_acquire_token_silently": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential._acquire_token_silently", "name": "_acquire_token_silently", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_acquire_token_silently of OnBehalfOfCredential", "ret_type": {".class": "UnionType", "items": ["azure.core.credentials.AccessTokenInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential._acquire_token_silently", "name": "_acquire_token_silently", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_assertion": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential._assertion", "name": "_assertion", "setter_type": null, "type": "builtins.str"}}, "_auth_record": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential._auth_record", "name": "_auth_record", "setter_type": null, "type": {".class": "UnionType", "items": ["azure.identity._auth_record.AuthenticationRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_request_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential._request_token", "name": "_request_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_request_token of OnBehalfOfCredential", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential._request_token", "name": "_request_token", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.on_behalf_of.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.on_behalf_of.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.on_behalf_of.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.on_behalf_of.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.on_behalf_of.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.on_behalf_of.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_build_auth_record": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.interactive._build_auth_record", "kind": "Gdef"}, "get_client_credential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.certificate.get_client_credential", "kind": "Gdef"}, "msal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.on_behalf_of.msal", "name": "msal", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._credentials.on_behalf_of.msal", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "wrap_exceptions": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.decorators.wrap_exceptions", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/on_behalf_of.py"}