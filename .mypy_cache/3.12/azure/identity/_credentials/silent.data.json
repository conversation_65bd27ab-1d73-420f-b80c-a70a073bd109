{".class": "MypyFile", "_fullname": "azure.identity._credentials.silent", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessToken": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessToken", "kind": "Gdef"}, "AccessTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessTokenInfo", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AuthenticationRecord": {".class": "SymbolTableNode", "cross_ref": "azure.identity._auth_record.AuthenticationRecord", "kind": "Gdef"}, "ClientAuthenticationError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ClientAuthenticationError", "kind": "Gdef"}, "CredentialUnavailableError": {".class": "SymbolTableNode", "cross_ref": "azure.identity._exceptions.CredentialUnavailableError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "MsalClient": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.msal_client.MsalClient", "kind": "Gdef"}, "NO_TOKEN": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.shared_token_cache.NO_TOKEN", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PublicClientApplication": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.silent.PublicClientApplication", "name": "PublicClientApplication", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._credentials.silent.PublicClientApplication", "source_any": null, "type_of_any": 3}}}, "SilentAuthenticationCredential": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential", "name": "SilentAuthenticationCredential", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.identity._credentials.silent", "mro": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of SilentAuthenticationCredential", "ret_type": "azure.identity._credentials.silent.SilentAuthenticationCredential", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.__exit__", "name": "__exit__", "type": null}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getstate__ of SilentAuthenticationCredential", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "authentication_record", "tenant_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "authentication_record", "tenant_id", "kwargs"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "azure.identity._auth_record.AuthenticationRecord", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SilentAuthenticationCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setstate__ of SilentAuthenticationCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_acquire_token_silent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._acquire_token_silent", "name": "_acquire_token_silent", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_acquire_token_silent of SilentAuthenticationCredential", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._acquire_token_silent", "name": "_acquire_token_silent", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_additionally_allowed_tenants": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._additionally_allowed_tenants", "name": "_additionally_allowed_tenants", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_auth_record": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._auth_record", "name": "_auth_record", "setter_type": null, "type": "azure.identity._auth_record.AuthenticationRecord"}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._cache", "name": "_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_cache_persistence_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._cache_persistence_options", "name": "_cache_persistence_options", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_cae_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._cae_cache", "name": "_cae_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_cae_client_applications": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._cae_client_applications", "name": "_cae_client_applications", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "azure.identity._credentials.silent.PublicClientApplication", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._client", "name": "_client", "setter_type": null, "type": "azure.identity._internal.msal_client.MsalClient"}}, "_client_applications": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._client_applications", "name": "_client_applications", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "azure.identity._credentials.silent.PublicClientApplication", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_custom_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._custom_cache", "name": "_custom_cache", "setter_type": null, "type": "builtins.bool"}}, "_get_client_application": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._get_client_application", "name": "_get_client_application", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_client_application of SilentAuthenticationCredential", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_token_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "scopes", "options", "base_method_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._get_token_base", "name": "_get_token_base", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "scopes", "options", "base_method_name", "kwargs"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenRequestOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_token_base of SilentAuthenticationCredential", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._initialize_cache", "name": "_initialize_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "is_cae"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_cache of SilentAuthenticationCredential", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "azure.identity._credentials.silent.TokenCache", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tenant_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential._tenant_id", "name": "_tenant_id", "setter_type": null, "type": "builtins.str"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of SilentAuthenticationCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "scopes", "claims", "tenant_id", "enable_cae", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.get_token", "name": "get_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "scopes", "claims", "tenant_id", "enable_cae", "kwargs"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_token of SilentAuthenticationCredential", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.AccessToken"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_token_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "scopes", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.get_token_info", "name": "get_token_info", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "scopes", "options"], "arg_types": ["azure.identity._credentials.silent.SilentAuthenticationCredential", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenRequestOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_token_info of SilentAuthenticationCredential", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.identity._credentials.silent.SilentAuthenticationCredential.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.identity._credentials.silent.SilentAuthenticationCredential", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.silent.TokenCache", "name": "TokenCache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._credentials.silent.TokenCache", "source_any": null, "type_of_any": 3}}}, "TokenCachePersistenceOptions": {".class": "SymbolTableNode", "cross_ref": "azure.identity._persistent_cache.TokenCachePersistenceOptions", "kind": "Gdef"}, "TokenRequestOptions": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.TokenRequestOptions", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.silent.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.silent.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.silent.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.silent.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.silent.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.silent.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_load_persistent_cache": {".class": "SymbolTableNode", "cross_ref": "azure.identity._persistent_cache._load_persistent_cache", "kind": "Gdef"}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "resolve_tenant": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.utils.resolve_tenant", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "validate_tenant_id": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.utils.validate_tenant_id", "kind": "Gdef"}, "within_dac": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.utils.within_dac", "kind": "Gdef"}, "wrap_exceptions": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.decorators.wrap_exceptions", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/silent.py"}