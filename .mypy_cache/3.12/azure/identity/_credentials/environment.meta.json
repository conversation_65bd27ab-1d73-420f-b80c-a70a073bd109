{"data_mtime": 1750965152, "dep_lines": [14, 15, 16, 17, 9, 12, 13, 11, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.identity._internal.decorators", "azure.identity._credentials.certificate", "azure.identity._credentials.client_secret", "azure.identity._credentials.user_password", "azure.core.credentials", "azure.identity._constants", "azure.identity._internal", "azure.identity", "logging", "os", "typing", "warnings", "builtins", "_contextvars", "_frozen_importlib", "_warnings", "abc", "azure.core", "azure.identity._internal.client_credential_base", "azure.identity._internal.get_token_mixin", "azure.identity._internal.interactive", "azure.identity._internal.msal_credentials", "azure.identity._internal.utils", "types"], "hash": "c36de952033b8cdde1774b9c1eccf7c56a6657cb", "id": "azure.identity._credentials.environment", "ignore_all": true, "interface_hash": "fb516ae71c30f1f7e74293c2c4a28c30e8117375", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/environment.py", "plugin_data": null, "size": 9369, "suppressed": [], "version_id": "1.16.1"}