{"data_mtime": 1750965152, "dep_lines": [13, 16, 9, 10, 11, 15, 14, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.identity._credentials.client_assertion", "azure.identity._internal.pipeline", "azure.core.exceptions", "azure.core.credentials", "azure.core.rest", "azure.identity._internal", "azure.identity", "os", "typing", "builtins", "_frozen_importlib", "abc", "azure.core", "azure.core.rest._helpers", "azure.core.rest._rest_py3", "azure.identity._internal.get_token_mixin", "azure.identity._internal.utils"], "hash": "7d9ef5d240fcc080f117dc7f23e760af4a134ef6", "id": "azure.identity._credentials.azure_pipelines", "ignore_all": true, "interface_hash": "99df23bd65a80de2c24edfb7c53a6ef61d5d0537", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/azure_pipelines.py", "plugin_data": null, "size": 8566, "suppressed": [], "version_id": "1.16.1"}