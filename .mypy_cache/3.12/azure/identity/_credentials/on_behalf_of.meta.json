{"data_mtime": 1750965152, "dep_lines": [13, 14, 15, 16, 17, 10, 11, 18, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["azure.identity._credentials.certificate", "azure.identity._internal.decorators", "azure.identity._internal.get_token_mixin", "azure.identity._internal.interactive", "azure.identity._internal.msal_credentials", "azure.core.credentials", "azure.core.exceptions", "azure.identity", "time", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "azure.core", "azure.core.pipeline", "azure.core.pipeline.transport", "azure.core.pipeline.transport._base", "azure.identity._auth_record", "azure.identity._internal", "azure.identity._internal.msal_client"], "hash": "b318a1c9e122acb3781c0a455e40088f0f60bd5b", "id": "azure.identity._credentials.on_behalf_of", "ignore_all": true, "interface_hash": "f57297730969592b21da2e25482db3d06ba3c49d", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/on_behalf_of.py", "plugin_data": null, "size": 9014, "suppressed": ["msal"], "version_id": "1.16.1"}