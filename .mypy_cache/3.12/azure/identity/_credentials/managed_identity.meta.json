{"data_mtime": 1750965152, "dep_lines": [12, 85, 92, 99, 105, 110, 117, 137, 9, 11, 10, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 20, 20, 20, 20, 20, 20, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.identity._internal.decorators", "azure.identity._credentials.service_fabric", "azure.identity._credentials.app_service", "azure.identity._credentials.azure_arc", "azure.identity._credentials.azure_ml", "azure.identity._credentials.cloud_shell", "azure.identity._credentials.workload_identity", "azure.identity._credentials.imds", "azure.core.credentials", "azure.identity._constants", "azure.identity", "logging", "os", "typing", "builtins", "_frozen_importlib", "abc", "azure.core", "azure.identity._credentials.client_assertion", "azure.identity._internal", "azure.identity._internal.get_token_mixin", "azure.identity._internal.managed_identity_base", "azure.identity._internal.msal_managed_identity_client", "contextlib", "types"], "hash": "01cbd1caac944485062a0c03b0ea3a10b9ba88da", "id": "azure.identity._credentials.managed_identity", "ignore_all": true, "interface_hash": "e376e64ec76485942df8d469c21e60196307a44a", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/managed_identity.py", "plugin_data": null, "size": 10332, "suppressed": [], "version_id": "1.16.1"}