{"data_mtime": 1750965152, "dep_lines": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 9, 10, 11, 5, 6, 7, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["azure.identity._credentials.azure_powershell", "azure.identity._credentials.browser", "azure.identity._credentials.chained", "azure.identity._credentials.environment", "azure.identity._credentials.managed_identity", "azure.identity._credentials.shared_cache", "azure.identity._credentials.azure_cli", "azure.identity._credentials.azd_cli", "azure.identity._credentials.vscode", "azure.identity._credentials.workload_identity", "azure.core.credentials", "azure.identity._constants", "azure.identity._internal", "logging", "os", "typing", "builtins", "_frozen_importlib", "abc", "azure.core"], "hash": "93b3c9a819ac22076d0f55b3c2ca2f66e47b107c", "id": "azure.identity._credentials.default", "ignore_all": true, "interface_hash": "bc299e59099e239f2423e750bf1f1bb118cb6f70", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/default.py", "plugin_data": null, "size": 16496, "suppressed": [], "version_id": "1.16.1"}