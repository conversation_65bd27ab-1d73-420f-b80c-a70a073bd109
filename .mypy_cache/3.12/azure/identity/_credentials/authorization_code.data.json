{".class": "MypyFile", "_fullname": "azure.identity._credentials.authorization_code", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AadClient": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.aad_client.AadClient", "kind": "Gdef"}, "AccessToken": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessToken", "kind": "Gdef"}, "AccessTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessTokenInfo", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AuthorizationCodeCredential": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.identity._internal.get_token_mixin.GetTokenMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "name": "AuthorizationCodeCredential", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.identity._credentials.authorization_code", "mro": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "azure.identity._internal.get_token_mixin.GetTokenMixin", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of AuthorizationCodeCredential", "ret_type": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of AuthorizationCodeCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "tenant_id", "client_id", "authorization_code", "redirect_uri", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "tenant_id", "client_id", "authorization_code", "redirect_uri", "kwargs"], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AuthorizationCodeCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_acquire_token_silently": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._acquire_token_silently", "name": "_acquire_token_silently", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_acquire_token_silently of AuthorizationCodeCredential", "ret_type": {".class": "UnionType", "items": ["azure.core.credentials.AccessTokenInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_authorization_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._authorization_code", "name": "_authorization_code", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._client", "name": "_client", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "azure.identity._internal.aad_client.AadClient"], "uses_pep604_syntax": false}}}, "_client_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._client_id", "name": "_client_id", "setter_type": null, "type": "builtins.str"}}, "_client_secret": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._client_secret", "name": "_client_secret", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_redirect_uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._redirect_uri", "name": "_redirect_uri", "setter_type": null, "type": "builtins.str"}}, "_request_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential._request_token", "name": "_request_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_request_token of AuthorizationCodeCredential", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AuthorizationCodeCredential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "scopes", "claims", "tenant_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.get_token", "name": "get_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "scopes", "claims", "tenant_id", "kwargs"], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_token of AuthorizationCodeCredential", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.AccessToken"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_token_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "scopes", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.get_token_info", "name": "get_token_info", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "scopes", "options"], "arg_types": ["azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenRequestOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_token_info of AuthorizationCodeCredential", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientAuthenticationError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ClientAuthenticationError", "kind": "Gdef"}, "GetTokenMixin": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.get_token_mixin.GetTokenMixin", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TokenRequestOptions": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.TokenRequestOptions", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.authorization_code.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.authorization_code.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.authorization_code.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.authorization_code.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.authorization_code.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._credentials.authorization_code.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_credentials/authorization_code.py"}