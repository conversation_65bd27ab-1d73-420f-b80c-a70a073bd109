{".class": "MypyFile", "_fullname": "azure.identity", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AuthenticationRecord": {".class": "SymbolTableNode", "cross_ref": "azure.identity._auth_record.AuthenticationRecord", "kind": "Gdef"}, "AuthenticationRequiredError": {".class": "SymbolTableNode", "cross_ref": "azure.identity._exceptions.AuthenticationRequiredError", "kind": "Gdef"}, "AuthorizationCodeCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.authorization_code.AuthorizationCodeCredential", "kind": "Gdef"}, "AzureAuthorityHosts": {".class": "SymbolTableNode", "cross_ref": "azure.identity._constants.AzureAuthorityHosts", "kind": "Gdef"}, "AzureCliCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.azure_cli.AzureCliCredential", "kind": "Gdef"}, "AzureDeveloperCliCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.azd_cli.AzureDeveloperCliCredential", "kind": "Gdef"}, "AzurePipelinesCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.azure_pipelines.AzurePipelinesCredential", "kind": "Gdef"}, "AzurePowerShellCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.azure_powershell.AzurePowerShellCredential", "kind": "Gdef"}, "CertificateCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.certificate.CertificateCredential", "kind": "Gdef"}, "ChainedTokenCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.chained.ChainedTokenCredential", "kind": "Gdef"}, "ClientAssertionCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.client_assertion.ClientAssertionCredential", "kind": "Gdef"}, "ClientSecretCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.client_secret.ClientSecretCredential", "kind": "Gdef"}, "CredentialUnavailableError": {".class": "SymbolTableNode", "cross_ref": "azure.identity._exceptions.CredentialUnavailableError", "kind": "Gdef"}, "DefaultAzureCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.default.DefaultAzureCredential", "kind": "Gdef"}, "DeviceCodeCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.device_code.DeviceCodeCredential", "kind": "Gdef"}, "EnvironmentCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.environment.EnvironmentCredential", "kind": "Gdef"}, "InteractiveBrowserCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.browser.InteractiveBrowserCredential", "kind": "Gdef"}, "KnownAuthorities": {".class": "SymbolTableNode", "cross_ref": "azure.identity._constants.KnownAuthorities", "kind": "Gdef"}, "ManagedIdentityCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.managed_identity.ManagedIdentityCredential", "kind": "Gdef"}, "OnBehalfOfCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.on_behalf_of.OnBehalfOfCredential", "kind": "Gdef"}, "SharedTokenCacheCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.shared_cache.SharedTokenCacheCredential", "kind": "Gdef"}, "TokenCachePersistenceOptions": {".class": "SymbolTableNode", "cross_ref": "azure.identity._persistent_cache.TokenCachePersistenceOptions", "kind": "Gdef"}, "UsernamePasswordCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.user_password.UsernamePasswordCredential", "kind": "Gdef"}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "azure.identity._version.VERSION", "kind": "Gdef", "module_public": false}, "VisualStudioCodeCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.vscode.VisualStudioCodeCredential", "kind": "Gdef"}, "WorkloadIdentityCredential": {".class": "SymbolTableNode", "cross_ref": "azure.identity._credentials.workload_identity.WorkloadIdentityCredential", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.identity.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.identity.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "get_bearer_token_provider": {".class": "SymbolTableNode", "cross_ref": "azure.identity._bearer_token_provider.get_bearer_token_provider", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/__init__.py"}