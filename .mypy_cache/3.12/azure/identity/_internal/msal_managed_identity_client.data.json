{".class": "MypyFile", "_fullname": "azure.identity._internal.msal_managed_identity_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessToken": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessToken", "kind": "Gdef"}, "AccessTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessTokenInfo", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClientAuthenticationError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ClientAuthenticationError", "kind": "Gdef"}, "CredentialUnavailableError": {".class": "SymbolTableNode", "cross_ref": "azure.identity._exceptions.CredentialUnavailableError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MsalClient": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.msal_client.MsalClient", "kind": "Gdef"}, "MsalManagedIdentityClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_unavailable_message", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "name": "MsalManagedIdentityClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.identity._internal.msal_managed_identity_client", "mro": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of MsalManagedIdentityClient", "ret_type": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of MsalManagedIdentityClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getstate__ of MsalManagedIdentityClient", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "client_id", "identity_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "client_id", "identity_config", "kwargs"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MsalManagedIdentityClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setstate__ of MsalManagedIdentityClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient._client", "name": "_client", "setter_type": null, "type": "azure.identity._internal.msal_client.MsalClient"}}, "_get_token_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "scopes", "options", "base_method_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient._get_token_base", "name": "_get_token_base", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "scopes", "options", "base_method_name", "kwargs"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenRequestOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_token_base of MsalManagedIdentityClient", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_msal_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient._msal_client", "name": "_msal_client", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._internal.msal_managed_identity_client.msal", "source_any": {".class": "AnyType", "missing_import_name": "azure.identity._internal.msal_managed_identity_client.msal", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "_request_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient._request_token", "name": "_request_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_request_token of MsalManagedIdentityClient", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient._settings", "name": "_settings", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of MsalManagedIdentityClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_managed_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.get_managed_identity", "name": "get_managed_identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_managed_identity of MsalManagedIdentityClient", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "azure.identity._internal.msal_managed_identity_client.msal", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "azure.identity._internal.msal_managed_identity_client.msal", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "scopes", "claims", "tenant_id", "enable_cae", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.get_token", "name": "get_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "scopes", "claims", "tenant_id", "enable_cae", "kwargs"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_token of MsalManagedIdentityClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.AccessToken"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_token_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "scopes", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.get_token_info", "name": "get_token_info", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "scopes", "options"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenRequestOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_token_info of MsalManagedIdentityClient", "ret_type": "azure.core.credentials.AccessTokenInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unavailable_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.get_unavailable_message", "name": "get_unavailable_message", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "desc"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_unavailable_message of MsalManagedIdentityClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.get_unavailable_message", "name": "get_unavailable_message", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "desc"], "arg_types": ["azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_unavailable_message of MsalManagedIdentityClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.identity._internal.msal_managed_identity_client.MsalManagedIdentityClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TokenRequestOptions": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.TokenRequestOptions", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.msal_managed_identity_client._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.msal_managed_identity_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.msal_managed_identity_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.msal_managed_identity_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.msal_managed_identity_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.msal_managed_identity_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.msal_managed_identity_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_scopes_to_resource": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal._scopes_to_resource", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "msal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.identity._internal.msal_managed_identity_client.msal", "name": "msal", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._internal.msal_managed_identity_client.msal", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "within_credential_chain": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.utils.within_credential_chain", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/msal_managed_identity_client.py"}