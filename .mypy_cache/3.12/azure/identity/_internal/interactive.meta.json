{"data_mtime": 1750965152, "dep_lines": [18, 15, 16, 19, 20, 21, 22, 13, 7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.identity._internal.msal_credentials", "azure.core.credentials", "azure.core.exceptions", "azure.identity._auth_record", "azure.identity._constants", "azure.identity._exceptions", "azure.identity._internal", "urllib.parse", "abc", "base64", "json", "logging", "time", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "azure.core", "azure.core.pipeline", "azure.core.pipeline.transport", "azure.core.pipeline.transport._base", "azure.identity._internal.decorators", "azure.identity._internal.msal_client", "types"], "hash": "5d4e8ca45a9c07fc1d80f71da2cfad13634a64ac", "id": "azure.identity._internal.interactive", "ignore_all": true, "interface_hash": "9172f537dcedcd2ce96d5efa10272d21bad4010c", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/interactive.py", "plugin_data": null, "size": 13848, "suppressed": [], "version_id": "1.16.1"}