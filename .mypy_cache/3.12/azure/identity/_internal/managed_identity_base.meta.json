{"data_mtime": 1750965152, "dep_lines": [10, 11, 8, 9, 5, 6, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30], "dependencies": ["azure.identity._internal.managed_identity_client", "azure.identity._internal.get_token_mixin", "azure.core.credentials", "azure.identity", "abc", "typing", "builtins", "_frozen_importlib", "azure.core"], "hash": "95e2fccad2c7eedfda9df1dfd7d88a7a0015b0d2", "id": "azure.identity._internal.managed_identity_base", "ignore_all": true, "interface_hash": "ab2162040fc7d465d5089704b8748f5e3d94045a", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/managed_identity_base.py", "plugin_data": null, "size": 2416, "suppressed": [], "version_id": "1.16.1"}