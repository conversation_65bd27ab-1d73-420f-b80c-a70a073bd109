{".class": "MypyFile", "_fullname": "azure.identity._internal.shared_token_cache", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.identity._internal.shared_token_cache.ABC", "line": 19, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "abc.ABC"}}, "AadClientBase": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.aad_client_base.AadClientBase", "kind": "Gdef"}, "AccessTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessTokenInfo", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CacheItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.identity._internal.shared_token_cache.CacheItem", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "CredentialUnavailableError": {".class": "SymbolTableNode", "cross_ref": "azure.identity._exceptions.CredentialUnavailableError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "KNOWN_ALIASES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.shared_token_cache.KNOWN_ALIASES", "name": "KNOWN_ALIASES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "KnownAuthorities": {".class": "SymbolTableNode", "cross_ref": "azure.identity._constants.KnownAuthorities", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MULTIPLE_ACCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.shared_token_cache.MULTIPLE_ACCOUNTS", "name": "MULTIPLE_ACCOUNTS", "setter_type": null, "type": "builtins.str"}}, "MULTIPLE_MATCHING_ACCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.shared_token_cache.MULTIPLE_MATCHING_ACCOUNTS", "name": "MULTIPLE_MATCHING_ACCOUNTS", "setter_type": null, "type": "builtins.str"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "NO_ACCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.shared_token_cache.NO_ACCOUNTS", "name": "NO_ACCOUNTS", "setter_type": null, "type": "builtins.str"}}, "NO_MATCHING_ACCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.shared_token_cache.NO_MATCHING_ACCOUNTS", "name": "NO_MATCHING_ACCOUNTS", "setter_type": null, "type": "builtins.str"}}, "NO_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.identity._internal.shared_token_cache.NO_TOKEN", "name": "NO_TOKEN", "setter_type": null, "type": "builtins.str"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SharedTokenCacheBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_get_auth_client", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase", "name": "SharedTokenCacheBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.identity._internal.shared_token_cache", "mro": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getstate__ of SharedTokenCacheBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 4], "arg_names": ["self", "username", "authority", "tenant_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 4], "arg_names": ["self", "username", "authority", "tenant_id", "kwargs"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SharedTokenCacheBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setstate__ of SharedTokenCacheBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_authority": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._authority", "name": "_authority", "setter_type": null, "type": "builtins.str"}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._cache", "name": "_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_cache_persistence_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._cache_persistence_options", "name": "_cache_persistence_options", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_cae_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._cae_cache", "name": "_cae_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._client", "name": "_client", "setter_type": null, "type": "azure.identity._internal.aad_client_base.AadClientBase"}}, "_client_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._client_initialized", "name": "_client_initialized", "setter_type": null, "type": "builtins.bool"}}, "_client_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._client_kwargs", "name": "_client_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_custom_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._custom_cache", "name": "_custom_cache", "setter_type": null, "type": "builtins.bool"}}, "_environment_aliases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._environment_aliases", "name": "_environment_aliases", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_get_account": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "username", "tenant_id", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_account", "name": "_get_account", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "username", "tenant_id", "is_cae"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_account of SharedTokenCacheBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "azure.identity._internal.shared_token_cache.CacheItem"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_account", "name": "_get_account", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_get_accounts_having_matching_refresh_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_accounts_having_matching_refresh_tokens", "name": "_get_accounts_having_matching_refresh_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "is_cae"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_accounts_having_matching_refresh_tokens of SharedTokenCacheBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.identity._internal.shared_token_cache.CacheItem"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_auth_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_auth_client", "name": "_get_auth_client", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_auth_client of SharedTokenCacheBase", "ret_type": "azure.identity._internal.aad_client_base.AadClientBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_auth_client", "name": "_get_auth_client", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_auth_client of SharedTokenCacheBase", "ret_type": "azure.identity._internal.aad_client_base.AadClientBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_cache_items_for_authority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "credential_type", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_cache_items_for_authority", "name": "_get_cache_items_for_authority", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "credential_type", "is_cae"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "AnyType", "missing_import_name": "azure.identity._internal.shared_token_cache.msal", "source_any": null, "type_of_any": 3}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cache_items_for_authority of SharedTokenCacheBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.identity._internal.shared_token_cache.CacheItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cached_access_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "scopes", "account", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_cached_access_token", "name": "_get_cached_access_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "scopes", "account", "is_cae"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.identity._internal.shared_token_cache.CacheItem"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cached_access_token of SharedTokenCacheBase", "ret_type": {".class": "UnionType", "items": ["azure.core.credentials.AccessTokenInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_refresh_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "account", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._get_refresh_tokens", "name": "_get_refresh_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "account", "is_cae"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_refresh_tokens of SharedTokenCacheBase", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "is_cae"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._initialize_cache", "name": "_initialize_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "is_cae"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_cache of SharedTokenCacheBase", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "azure.identity._internal.shared_token_cache.msal", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._initialize_client", "name": "_initialize_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.identity._internal.shared_token_cache.SharedTokenCacheBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_client of SharedTokenCacheBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tenant_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._tenant_id", "name": "_tenant_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase._username", "name": "_username", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "supported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase.supported", "name": "supported", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "supported of SharedTokenCacheBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase.supported", "name": "supported", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "supported of SharedTokenCacheBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.identity._internal.shared_token_cache.SharedTokenCacheBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenCachePersistenceOptions": {".class": "SymbolTableNode", "cross_ref": "azure.identity._persistent_cache.TokenCachePersistenceOptions", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.shared_token_cache.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.shared_token_cache.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.shared_token_cache.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.shared_token_cache.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.shared_token_cache.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.shared_token_cache.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_account_to_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["account"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._internal.shared_token_cache._account_to_string", "name": "_account_to_string", "type": null}}, "_filtered_accounts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["accounts", "username", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._internal.shared_token_cache._filtered_accounts", "name": "_filtered_accounts", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["accounts", "username", "tenant_id"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.identity._internal.shared_token_cache.CacheItem"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_filtered_accounts", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.identity._internal.shared_token_cache.CacheItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_persistent_cache": {".class": "SymbolTableNode", "cross_ref": "azure.identity._persistent_cache._load_persistent_cache", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_default_authority": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.utils.get_default_authority", "kind": "Gdef"}, "msal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "azure.identity._internal.shared_token_cache.msal", "name": "msal", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "azure.identity._internal.shared_token_cache.msal", "source_any": null, "type_of_any": 3}}}, "normalize_authority": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.utils.normalize_authority", "kind": "Gdef"}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "wrap_exceptions": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.decorators.wrap_exceptions", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/shared_token_cache.py"}