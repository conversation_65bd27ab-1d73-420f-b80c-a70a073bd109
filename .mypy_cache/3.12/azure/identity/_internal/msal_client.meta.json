{"data_mtime": 1750965151, "dep_lines": [9, 10, 15, 8, 14, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies", "azure.core.pipeline.transport", "azure.identity._internal.pipeline", "azure.core.exceptions", "azure.core.pipeline", "threading", "typing", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "azure.core", "azure.core.pipeline.policies._base", "azure.core.pipeline.policies._universal", "azure.core.pipeline.transport._base", "types"], "hash": "600c791a8940942902c5e051d8034057394cd3a4", "id": "azure.identity._internal.msal_client", "ignore_all": true, "interface_hash": "d869ea35bb06026495dda90a5f2c455f6aff156b", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/msal_client.py", "plugin_data": null, "size": 5871, "suppressed": [], "version_id": "1.16.1"}