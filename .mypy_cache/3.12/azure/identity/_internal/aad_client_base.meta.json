{"data_mtime": 1750965151, "dep_lines": [15, 16, 19, 20, 14, 17, 18, 21, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 5], "dependencies": ["azure.core.pipeline.policies", "azure.core.pipeline.transport", "azure.identity._internal.utils", "azure.identity._internal.aadclient_certificate", "azure.core.pipeline", "azure.core.credentials", "azure.core.exceptions", "azure.identity._persistent_cache", "abc", "base64", "json", "time", "uuid", "typing", "builtins", "_frozen_importlib", "_typeshed", "azure.core", "azure.core.pipeline.transport._base"], "hash": "e186dd46c4fa5686ff2a334657afaf2772492644", "id": "azure.identity._internal.aad_client_base", "ignore_all": true, "interface_hash": "618ff3d22c31171a79fa68c95737b45e476cb6a9", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/aad_client_base.py", "plugin_data": null, "size": 17087, "suppressed": ["msal"], "version_id": "1.16.1"}