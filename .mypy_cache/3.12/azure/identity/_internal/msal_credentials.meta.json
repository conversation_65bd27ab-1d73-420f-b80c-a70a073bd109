{"data_mtime": 1750965152, "dep_lines": [11, 12, 13, 14, 5, 6, 7, 1, 1, 1, 1, 9], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 10], "dependencies": ["azure.identity._internal.msal_client", "azure.identity._internal.utils", "azure.identity._constants", "azure.identity._persistent_cache", "os", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "73fb9de4b94bdb983792cee909e05c19b4214399", "id": "azure.identity._internal.msal_credentials", "ignore_all": true, "interface_hash": "3c62b8f9bd795dbae8c01fdd25c44051742e7ca6", "mtime": 1750963072, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/msal_credentials.py", "plugin_data": null, "size": 6041, "suppressed": ["msal"], "version_id": "1.16.1"}