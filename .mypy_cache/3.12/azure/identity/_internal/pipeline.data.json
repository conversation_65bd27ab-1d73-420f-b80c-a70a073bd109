{".class": "MypyFile", "_fullname": "azure.identity._internal.pipeline", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Configuration": {".class": "SymbolTableNode", "cross_ref": "azure.core.configuration.Configuration", "kind": "Gdef"}, "ContentDecodePolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.ContentDecodePolicy", "kind": "Gdef"}, "CustomHookPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._custom_hook.CustomHookPolicy", "kind": "Gdef"}, "DistributedTracingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "kind": "Gdef"}, "HeadersPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.HeadersPolicy", "kind": "Gdef"}, "HttpLoggingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.HttpLoggingPolicy", "kind": "Gdef"}, "NetworkTraceLoggingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "kind": "Gdef"}, "Pipeline": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._base.Pipeline", "kind": "Gdef"}, "ProxyPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.ProxyPolicy", "kind": "Gdef"}, "RetryPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._retry.RetryPolicy", "kind": "Gdef"}, "USER_AGENT": {".class": "SymbolTableNode", "cross_ref": "azure.identity._internal.user_agent.USER_AGENT", "kind": "Gdef"}, "UserAgentPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.UserAgentPolicy", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.pipeline.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.pipeline.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.pipeline.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.pipeline.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.pipeline.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.identity._internal.pipeline.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._internal.pipeline._get_config", "name": "_get_config", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_config", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.configuration.Configuration"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_policies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["config", "_per_retry_policies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._internal.pipeline._get_policies", "name": "_get_policies", "type": null}}, "build_async_pipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["transport", "policies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._internal.pipeline.build_async_pipeline", "name": "build_async_pipeline", "type": null}}, "build_pipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["transport", "policies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.identity._internal.pipeline.build_pipeline", "name": "build_pipeline", "type": null}}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/azure/identity/_internal/pipeline.py"}