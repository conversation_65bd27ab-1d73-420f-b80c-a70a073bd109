{".class": "MypyFile", "_fullname": "cryptography.x509.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.Attribute", "name": "Attribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.Attribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.Attribute", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attribute.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.x509.base.Attribute", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of Attribute", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attribute.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.Attribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__hash__ of Attribute", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "oid", "value", "_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attribute.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "oid", "value", "_type"], "arg_types": ["cryptography.x509.base.Attribute", "cryptography.hazmat.bindings._rust.ObjectIdentifier", "builtins.bytes", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Attribute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attribute.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["cryptography.x509.base.Attribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Attribute", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_oid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.Attribute._oid", "name": "_oid", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.Attribute._type", "name": "_type", "setter_type": null, "type": "builtins.int"}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.Attribute._value", "name": "_value", "setter_type": null, "type": "builtins.bytes"}}, "oid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cryptography.x509.base.Attribute.oid", "name": "oid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.Attribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "oid of Attribute", "ret_type": "cryptography.hazmat.bindings._rust.ObjectIdentifier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base.Attribute.oid", "name": "oid", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.Attribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "oid of Attribute", "ret_type": "cryptography.hazmat.bindings._rust.ObjectIdentifier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cryptography.x509.base.Attribute.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.Attribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "value of Attribute", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base.Attribute.value", "name": "value", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.Attribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "value of Attribute", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.Attribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.Attribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AttributeNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.AttributeNotFound", "name": "AttributeNotFound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.AttributeNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.AttributeNotFound", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "oid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.AttributeNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "oid"], "arg_types": ["cryptography.x509.base.AttributeNotFound", "builtins.str", "cryptography.hazmat.bindings._rust.ObjectIdentifier"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AttributeNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "oid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.AttributeNotFound.oid", "name": "oid", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.AttributeNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.AttributeNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Attributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.Attributes", "name": "Attributes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.Attributes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.Attributes", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.Attributes.__getitem__", "name": "__getitem__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attributes.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "arg_types": ["cryptography.x509.base.Attributes", {".class": "Instance", "args": ["cryptography.x509.base.Attribute"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Attributes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.Attributes.__iter__", "name": "__iter__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.Attributes.__len__", "name": "__len__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attributes.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["cryptography.x509.base.Attributes"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Attributes", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.Attributes._attributes", "name": "_attributes", "setter_type": null, "type": {".class": "Instance", "args": ["cryptography.x509.base.Attribute"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_attribute_for_oid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.Attributes.get_attribute_for_oid", "name": "get_attribute_for_oid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oid"], "arg_types": ["cryptography.x509.base.Attributes", "cryptography.hazmat.bindings._rust.ObjectIdentifier"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_attribute_for_oid of Attributes", "ret_type": "cryptography.x509.base.Attribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.Attributes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.Attributes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Certificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.base.Certificate", "line": 163, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.x509.Certificate"}}, "CertificateBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.CertificateBuilder", "name": "CertificateBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.CertificateBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.CertificateBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "issuer_name", "subject_name", "public_key", "serial_number", "not_valid_before", "not_valid_after", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "issuer_name", "subject_name", "public_key", "serial_number", "not_valid_before", "not_valid_after", "extensions"], "arg_types": ["cryptography.x509.base.CertificateBuilder", {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificatePublicKeyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CertificateBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.x509.base.CertificateBuilder._extensions", "name": "_extensions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_issuer_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._issuer_name", "name": "_issuer_name", "setter_type": null, "type": {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_not_valid_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._not_valid_after", "name": "_not_valid_after", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_not_valid_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._not_valid_before", "name": "_not_valid_before", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_public_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._public_key", "name": "_public_key", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificatePublicKeyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_serial_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._serial_number", "name": "_serial_number", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_subject_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._subject_name", "name": "_subject_name", "setter_type": null, "type": {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateBuilder._version", "name": "_version", "setter_type": null, "type": "cryptography.x509.base.Version"}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "arg_types": ["cryptography.x509.base.CertificateBuilder", "cryptography.x509.extensions.ExtensionType", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_extension of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "issuer_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.issuer_name", "name": "issuer_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["cryptography.x509.base.CertificateBuilder", "cryptography.x509.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "issuer_name of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_valid_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.not_valid_after", "name": "not_valid_after", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "time"], "arg_types": ["cryptography.x509.base.CertificateBuilder", "datetime.datetime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_valid_after of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_valid_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.not_valid_before", "name": "not_valid_before", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "time"], "arg_types": ["cryptography.x509.base.CertificateBuilder", "datetime.datetime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_valid_before of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["cryptography.x509.base.CertificateBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificatePublicKeyTypes"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "public_key of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serial_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "number"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.serial_number", "name": "serial_number", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "number"], "arg_types": ["cryptography.x509.base.CertificateBuilder", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serial_number of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "private_key", "algorithm", "backend", "rsa_padding", "ecdsa_deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "private_key", "algorithm", "backend", "rsa_padding", "ecdsa_deterministic"], "arg_types": ["cryptography.x509.base.CertificateBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificateIssuerPrivateKeyTypes"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.x509.base._AllowedHashTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.padding.PSS", "cryptography.hazmat.primitives.asymmetric.padding.PKCS1v15", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign of <PERSON><PERSON><PERSON>er", "ret_type": "cryptography.hazmat.bindings._rust.x509.Certificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subject_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateBuilder.subject_name", "name": "subject_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["cryptography.x509.base.CertificateBuilder", "cryptography.x509.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "subject_name of CertificateBuilder", "ret_type": "cryptography.x509.base.CertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.CertificateBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.CertificateBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CertificateIssuerPrivateKeyTypes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificateIssuerPrivateKeyTypes", "kind": "Gdef"}, "CertificatePublicKeyTypes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificatePublicKeyTypes", "kind": "Gdef"}, "CertificateRevocationList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.base.CertificateRevocationList", "line": 235, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.x509.CertificateRevocationList"}}, "CertificateRevocationListBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.CertificateRevocationListBuilder", "name": "CertificateRevocationListBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.CertificateRevocationListBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "issuer_name", "last_update", "next_update", "extensions", "revoked_certificates"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "issuer_name", "last_update", "next_update", "extensions", "revoked_certificates"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["cryptography.x509.base.RevokedCertificate"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CertificateRevocationListBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder._extensions", "name": "_extensions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_issuer_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder._issuer_name", "name": "_issuer_name", "setter_type": null, "type": {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder._last_update", "name": "_last_update", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_next_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder._next_update", "name": "_next_update", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_revoked_certificates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder._revoked_certificates", "name": "_revoked_certificates", "setter_type": null, "type": {".class": "Instance", "args": ["cryptography.x509.base.RevokedCertificate"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", "cryptography.x509.extensions.ExtensionType", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_extension of CertificateRevocationListBuilder", "ret_type": "cryptography.x509.base.CertificateRevocationListBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_revoked_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "revoked_certificate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.add_revoked_certificate", "name": "add_revoked_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "revoked_certificate"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", "cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_revoked_certificate of CertificateRevocationListBuilder", "ret_type": "cryptography.x509.base.CertificateRevocationListBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "issuer_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "issuer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.issuer_name", "name": "issuer_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "issuer_name"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", "cryptography.x509.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "issuer_name of CertificateRevocationListBuilder", "ret_type": "cryptography.x509.base.CertificateRevocationListBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "last_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.last_update", "name": "last_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "last_update"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", "datetime.datetime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "last_update of CertificateRevocationListBuilder", "ret_type": "cryptography.x509.base.CertificateRevocationListBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "next_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.next_update", "name": "next_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "next_update"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", "datetime.datetime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "next_update of CertificateRevocationListBuilder", "ret_type": "cryptography.x509.base.CertificateRevocationListBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "private_key", "algorithm", "backend", "rsa_padding", "ecdsa_deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "private_key", "algorithm", "backend", "rsa_padding", "ecdsa_deterministic"], "arg_types": ["cryptography.x509.base.CertificateRevocationListBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificateIssuerPrivateKeyTypes"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.x509.base._AllowedHashTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.padding.PSS", "cryptography.hazmat.primitives.asymmetric.padding.PKCS1v15", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign of CertificateRevocationListBuilder", "ret_type": "cryptography.hazmat.bindings._rust.x509.CertificateRevocationList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.CertificateRevocationListBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.CertificateRevocationListBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CertificateSigningRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.base.CertificateSigningRequest", "line": 236, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.x509.CertificateSigningRequest"}}, "CertificateSigningRequestBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder", "name": "CertificateSigningRequestBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.CertificateSigningRequestBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "subject_name", "extensions", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "subject_name", "extensions", "attributes"], "arg_types": ["cryptography.x509.base.CertificateSigningRequestBuilder", {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CertificateSigningRequestBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder._attributes", "name": "_attributes", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder._extensions", "name": "_extensions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_subject_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder._subject_name", "name": "_subject_name", "setter_type": null, "type": {".class": "UnionType", "items": ["cryptography.x509.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "add_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "oid", "value", "_tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder.add_attribute", "name": "add_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "oid", "value", "_tag"], "arg_types": ["cryptography.x509.base.CertificateSigningRequestBuilder", "cryptography.hazmat.bindings._rust.ObjectIdentifier", "builtins.bytes", {".class": "UnionType", "items": ["cryptography.x509.name._ASN1Type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_attribute of CertificateSigningRequestBuilder", "ret_type": "cryptography.x509.base.CertificateSigningRequestBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "arg_types": ["cryptography.x509.base.CertificateSigningRequestBuilder", "cryptography.x509.extensions.ExtensionType", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_extension of CertificateSigningRequestBuilder", "ret_type": "cryptography.x509.base.CertificateSigningRequestBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "private_key", "algorithm", "backend", "rsa_padding", "ecdsa_deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "private_key", "algorithm", "backend", "rsa_padding", "ecdsa_deterministic"], "arg_types": ["cryptography.x509.base.CertificateSigningRequestBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificateIssuerPrivateKeyTypes"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.x509.base._AllowedHashTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.padding.PSS", "cryptography.hazmat.primitives.asymmetric.padding.PKCS1v15", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign of CertificateSigningRequestBuilder", "ret_type": "cryptography.hazmat.bindings._rust.x509.CertificateSigningRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subject_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder.subject_name", "name": "subject_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["cryptography.x509.base.CertificateSigningRequestBuilder", "cryptography.x509.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "subject_name of CertificateSigningRequestBuilder", "ret_type": "cryptography.x509.base.CertificateSigningRequestBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.CertificateSigningRequestBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.CertificateSigningRequestBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Extension": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.extensions.Extension", "kind": "Gdef"}, "ExtensionType": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.extensions.ExtensionType", "kind": "Gdef"}, "Extensions": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.extensions.Extensions", "kind": "Gdef"}, "InvalidVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.InvalidVersion", "name": "InvalidVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.InvalidVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.InvalidVersion", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "parsed_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.InvalidVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "parsed_version"], "arg_types": ["cryptography.x509.base.InvalidVersion", "builtins.str", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of InvalidVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parsed_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.InvalidVersion.parsed_version", "name": "parsed_version", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.InvalidVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.InvalidVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Name": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.name.Name", "kind": "Gdef"}, "ObjectIdentifier": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.ObjectIdentifier", "kind": "Gdef"}, "RevokedCertificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["extensions", 1], ["revocation_date", 1], ["revocation_date_utc", 1], ["serial_number", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.RevokedCertificate", "name": "RevokedCertificate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.x509.base.RevokedCertificate", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.RevokedCertificate", "builtins.object"], "names": {".class": "SymbolTable", "extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificate.extensions", "name": "extensions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extensions of RevokedCertificate", "ret_type": "cryptography.x509.extensions.Extensions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificate.extensions", "name": "extensions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extensions of RevokedCertificate", "ret_type": "cryptography.x509.extensions.Extensions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "revocation_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificate.revocation_date", "name": "revocation_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date of RevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificate.revocation_date", "name": "revocation_date", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date of RevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "revocation_date_utc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificate.revocation_date_utc", "name": "revocation_date_utc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date_utc of RevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificate.revocation_date_utc", "name": "revocation_date_utc", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date_utc of RevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "serial_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificate.serial_number", "name": "serial_number", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serial_number of RevokedCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificate.serial_number", "name": "serial_number", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base.RevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serial_number of RevokedCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.RevokedCertificate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.RevokedCertificate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RevokedCertificateBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.RevokedCertificateBuilder", "name": "RevokedCertificateBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.RevokedCertificateBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.RevokedCertificateBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "serial_number", "revocation_date", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "serial_number", "revocation_date", "extensions"], "arg_types": ["cryptography.x509.base.RevokedCertificateBuilder", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RevokedCertificateBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder._extensions", "name": "_extensions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_revocation_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder._revocation_date", "name": "_revocation_date", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_serial_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder._serial_number", "name": "_serial_number", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "arg_types": ["cryptography.x509.base.RevokedCertificateBuilder", "cryptography.x509.extensions.ExtensionType", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_extension of RevokedCertificateBuilder", "ret_type": "cryptography.x509.base.RevokedCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "arg_types": ["cryptography.x509.base.RevokedCertificateBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build of RevokedCertificateBuilder", "ret_type": "cryptography.x509.base.RevokedCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "revocation_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder.revocation_date", "name": "revocation_date", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "time"], "arg_types": ["cryptography.x509.base.RevokedCertificateBuilder", "datetime.datetime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date of RevokedCertificateBuilder", "ret_type": "cryptography.x509.base.RevokedCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serial_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "number"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base.RevokedCertificateBuilder.serial_number", "name": "serial_number", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "number"], "arg_types": ["cryptography.x509.base.RevokedCertificateBuilder", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serial_number of RevokedCertificateBuilder", "ret_type": "cryptography.x509.base.RevokedCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.RevokedCertificateBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.RevokedCertificateBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base.Version", "name": "Version", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.x509.base.Version", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base.Version", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "v1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.Version.v1", "name": "v1", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "v3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.Version.v3", "name": "v3", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base.Version.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base.Version", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ASN1Type": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.name._ASN1Type", "kind": "Gdef"}, "_AllowedHashTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.base._AllowedHashTypes", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.SHA224", "cryptography.hazmat.primitives.hashes.SHA256", "cryptography.hazmat.primitives.hashes.SHA384", "cryptography.hazmat.primitives.hashes.SHA512", "cryptography.hazmat.primitives.hashes.SHA3_224", "cryptography.hazmat.primitives.hashes.SHA3_256", "cryptography.hazmat.primitives.hashes.SHA3_384", "cryptography.hazmat.primitives.hashes.SHA3_512"], "uses_pep604_syntax": false}}}, "_EARLIEST_UTC_TIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base._EARLIEST_UTC_TIME", "name": "_EARLIEST_UTC_TIME", "setter_type": null, "type": "datetime.datetime"}}, "_RawRevokedCertificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.x509.base.RevokedCertificate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.base._RawRevokedCertificate", "name": "_RawRevokedCertificate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.base._RawRevokedCertificate", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.x509.base", "mro": ["cryptography.x509.base._RawRevokedCertificate", "cryptography.x509.base.RevokedCertificate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "serial_number", "revocation_date", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cryptography.x509.base._RawRevokedCertificate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "serial_number", "revocation_date", "extensions"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate", "builtins.int", "datetime.datetime", "cryptography.x509.extensions.Extensions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _RawRevokedCertificate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate._extensions", "name": "_extensions", "setter_type": null, "type": "cryptography.x509.extensions.Extensions"}}, "_revocation_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate._revocation_date", "name": "_revocation_date", "setter_type": null, "type": "datetime.datetime"}}, "_serial_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate._serial_number", "name": "_serial_number", "setter_type": null, "type": "builtins.int"}}, "extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cryptography.x509.base._RawRevokedCertificate.extensions", "name": "extensions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extensions of _RawRevokedCertificate", "ret_type": "cryptography.x509.extensions.Extensions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate.extensions", "name": "extensions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extensions of _RawRevokedCertificate", "ret_type": "cryptography.x509.extensions.Extensions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "revocation_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cryptography.x509.base._RawRevokedCertificate.revocation_date", "name": "revocation_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date of _RawRevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate.revocation_date", "name": "revocation_date", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date of _RawRevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "revocation_date_utc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cryptography.x509.base._RawRevokedCertificate.revocation_date_utc", "name": "revocation_date_utc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date_utc of _RawRevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate.revocation_date_utc", "name": "revocation_date_utc", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revocation_date_utc of _RawRevokedCertificate", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "serial_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cryptography.x509.base._RawRevokedCertificate.serial_number", "name": "serial_number", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serial_number of _RawRevokedCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.x509.base._RawRevokedCertificate.serial_number", "name": "serial_number", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.base._RawRevokedCertificate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serial_number of _RawRevokedCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.base._RawRevokedCertificate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.base._RawRevokedCertificate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_convert_to_naive_utc_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.base._convert_to_naive_utc_time", "name": "_convert_to_naive_utc_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["time"], "arg_types": ["datetime.datetime"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_to_naive_utc_time", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_sequence_methods": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.extensions._make_sequence_methods", "kind": "Gdef"}, "_reject_duplicate_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["oid", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.base._reject_duplicate_attribute", "name": "_reject_duplicate_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["oid", "attributes"], "arg_types": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_reject_duplicate_attribute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reject_duplicate_extension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["extension", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.base._reject_duplicate_extension", "name": "_reject_duplicate_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["extension", "extensions"], "arg_types": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_reject_duplicate_extension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "dsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.dsa", "kind": "Gdef"}, "ec": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec", "kind": "Gdef"}, "ed25519": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed25519", "kind": "Gdef"}, "ed448": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed448", "kind": "Gdef"}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "load_der_x509_certificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_der_x509_certificate", "name": "load_der_x509_certificate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.x509.Certificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_der_x509_crl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_der_x509_crl", "name": "load_der_x509_crl", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.x509.CertificateRevocationList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_der_x509_csr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_der_x509_csr", "name": "load_der_x509_csr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.x509.CertificateSigningRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pem_x509_certificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_pem_x509_certificate", "name": "load_pem_x509_certificate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.x509.Certificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pem_x509_certificates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_pem_x509_certificates", "name": "load_pem_x509_certificates", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.x509.Certificate"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pem_x509_crl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_pem_x509_crl", "name": "load_pem_x509_crl", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.x509.CertificateRevocationList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pem_x509_csr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.base.load_pem_x509_csr", "name": "load_pem_x509_csr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.x509.CertificateSigningRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "padding": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.padding", "kind": "Gdef"}, "random_serial_number": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.base.random_serial_number", "name": "random_serial_number", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "random_serial_number", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa", "kind": "Gdef"}, "rust_x509": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.x509", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.utils", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "x25519": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.x25519", "kind": "Gdef"}, "x448": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.x448", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/x509/base.py"}