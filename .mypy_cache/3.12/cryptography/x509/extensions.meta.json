{"data_mtime": 1750965151, "dep_lines": [15, 16, 18, 19, 20, 15, 17, 17, 17, 24, 27, 38, 39, 12, 14, 5, 7, 8, 9, 10, 11, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 20, 10, 10, 20, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.asn1", "cryptography.hazmat.bindings._rust.x509", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.asymmetric.types", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.primitives.constant_time", "cryptography.hazmat.primitives.serialization", "cryptography.hazmat.primitives", "cryptography.x509.certificate_transparency", "cryptography.x509.general_name", "cryptography.x509.name", "cryptography.x509.oid", "collections.abc", "cryptography.utils", "__future__", "abc", "datetime", "<PERSON><PERSON><PERSON>", "ipaddress", "typing", "cryptography", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "cryptography.hazmat", "cryptography.hazmat._oid", "cryptography.hazmat.bindings", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives.asymmetric.dsa", "cryptography.hazmat.primitives.asymmetric.ed25519", "cryptography.hazmat.primitives.asymmetric.ed448", "cryptography.hazmat.primitives.asymmetric.x25519", "cryptography.hazmat.primitives.asymmetric.x448", "enum", "types"], "hash": "a657437e78d40c94b9a00cb00ea652075a854b16", "id": "cryptography.x509.extensions", "ignore_all": true, "interface_hash": "26a3152d8290cb63752752650f52480b40e05bf7", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/x509/extensions.py", "plugin_data": null, "size": 77724, "suppressed": [], "version_id": "1.16.1"}