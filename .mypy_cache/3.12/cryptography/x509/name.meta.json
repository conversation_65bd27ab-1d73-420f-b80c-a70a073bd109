{"data_mtime": 1750965151, "dep_lines": [15, 15, 16, 12, 14, 5, 7, 8, 9, 10, 11, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 10, 5, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.x509", "cryptography.hazmat.bindings._rust", "cryptography.x509.oid", "collections.abc", "cryptography.utils", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "re", "sys", "typing", "warnings", "cryptography", "builtins", "_collections_abc", "_frozen_importlib", "_warnings", "abc", "cryptography.hazmat", "cryptography.hazmat._oid", "cryptography.hazmat.bindings", "enum", "types"], "hash": "4a18151833877f8b8568b4bf4d924832068aa34c", "id": "cryptography.x509.name", "ignore_all": true, "interface_hash": "056485d981ed83972d3ca23c762d703212bea4ff", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/x509/name.py", "plugin_data": null, "size": 15121, "suppressed": [], "version_id": "1.16.1"}