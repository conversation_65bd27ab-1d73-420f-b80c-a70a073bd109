{"data_mtime": 1750965150, "dep_lines": [12, 5, 7, 8, 9, 10, 11, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "enum", "sys", "types", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "14930209807f0a167d8839016af8aeaba23d8029", "id": "cryptography.utils", "ignore_all": true, "interface_hash": "1d7c40ff710da294fefd368b380a854b48570b9a", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/utils.py", "plugin_data": null, "size": 4397, "suppressed": [], "version_id": "1.16.1"}