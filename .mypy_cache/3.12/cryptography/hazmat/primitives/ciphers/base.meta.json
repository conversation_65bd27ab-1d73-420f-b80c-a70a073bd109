{"data_mtime": 1750965150, "dep_lines": [10, 12, 10, 11, 12, 13, 5, 7, 8, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 20, 5, 5, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.primitives.ciphers.modes", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.primitives._cipheralgorithm", "cryptography.hazmat.primitives.ciphers", "cryptography.utils", "__future__", "abc", "typing", "builtins", "_frozen_importlib", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust.openssl.ciphers", "types"], "hash": "89162ee1efa181f2133e3edb3cae7effba983cc0", "id": "cryptography.hazmat.primitives.ciphers.base", "ignore_all": true, "interface_hash": "a810241e034b92e6940bc9737a2d738d87d7c0d6", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/ciphers/base.py", "plugin_data": null, "size": 4253, "suppressed": [], "version_id": "1.16.1"}