{"data_mtime": 1750965150, "dep_lines": [10, 10, 10, 10, 10, 10, 10, 10, 10, 9, 5, 7, 9, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 5, 10, 20, 5, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.dh", "cryptography.hazmat.primitives.asymmetric.dsa", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.ed448", "cryptography.hazmat.primitives.asymmetric.ed25519", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.asymmetric.x448", "cryptography.hazmat.primitives.asymmetric.x25519", "cryptography.hazmat.primitives.asymmetric", "cryptography.utils", "__future__", "typing", "cryptography", "builtins", "_frozen_importlib", "abc"], "hash": "2b0c2a0fc99939dc3351d3a35b79cd73b70127bd", "id": "cryptography.hazmat.primitives.asymmetric.types", "ignore_all": true, "interface_hash": "56cd23f1a2d778352a90f22e19639738cbc3bee3", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/asymmetric/types.py", "plugin_data": null, "size": 2996, "suppressed": [], "version_id": "1.16.1"}