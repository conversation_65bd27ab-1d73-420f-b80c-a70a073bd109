{"data_mtime": 1750965150, "dep_lines": [13, 15, 369, 13, 14, 14, 15, 12, 14, 10, 11, 5, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 20, 10, 10, 20, 5, 20, 10, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.primitives.asymmetric.utils", "cryptography.hazmat.backends.openssl.backend", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.primitives._serialization", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat._oid", "cryptography.hazmat.primitives", "cryptography.utils", "cryptography.exceptions", "__future__", "abc", "typing", "cryptography", "builtins", "_frozen_importlib", "cryptography.hazmat.backends", "cryptography.hazmat.backends.openssl", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust.exceptions", "cryptography.hazmat.bindings._rust.openssl.ec", "enum"], "hash": "e6e33db42ca33fe966919975d69de299f056d3c9", "id": "cryptography.hazmat.primitives.asymmetric.ec", "ignore_all": true, "interface_hash": "b78063d6ee48594910f1781612275dc77eca6feb", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py", "plugin_data": null, "size": 12999, "suppressed": [], "version_id": "1.16.1"}