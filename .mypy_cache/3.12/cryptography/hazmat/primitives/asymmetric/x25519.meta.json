{"data_mtime": 1750965150, "dep_lines": [10, 18, 10, 11, 11, 9, 12, 5, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 20, 10, 20, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.backends.openssl.backend", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.primitives._serialization", "cryptography.hazmat.primitives", "cryptography.exceptions", "cryptography.utils", "__future__", "abc", "builtins", "_frozen_importlib", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust.openssl.x25519", "enum", "typing"], "hash": "ad6a40d21e526a7fa59c0e55d88dfa986f34b815", "id": "cryptography.hazmat.primitives.asymmetric.x25519", "ignore_all": true, "interface_hash": "622245d384f40530ecfc698935fa6544dff1fffc", "mtime": 1750963583, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py", "plugin_data": null, "size": 3613, "suppressed": [], "version_id": "1.16.1"}