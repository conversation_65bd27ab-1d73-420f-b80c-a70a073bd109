{".class": "MypyFile", "_fullname": "ctypes.wintypes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ATOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.ATOM", "line": 66, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ushort"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Array": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Array", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BOOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.BOOL", "line": 37, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_long"}}, "BOOLEAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.BOOLEAN", "line": 36, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_byte"}}, "BYTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.BYTE", "line": 27, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_byte"}}, "CHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.CHAR", "line": 30, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_char"}}, "COLORREF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.COLORREF", "line": 68, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulong"}}, "DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.DOUBLE", "line": 34, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_double"}}, "DWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.DWORD", "line": 29, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulong"}}, "FILETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.FILETIME", "name": "FILETIME", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.FILETIME", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.FILETIME", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "dwHighDateTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.FILETIME.dwHighDateTime", "name": "dwHighDateTime", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "dwLowDateTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.FILETIME.dwLowDateTime", "name": "dwLowDateTime", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.FILETIME.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.FILETIME", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.FLOAT", "line": 35, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_float"}}, "HACCEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HACCEL", "line": 74, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HANDLE", "line": 73, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HBITMAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HBITMAP", "line": 75, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HBRUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HBRUSH", "line": 76, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HCOLORSPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HCOLORSPACE", "line": 77, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HDC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HDC", "line": 78, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HDESK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HDESK", "line": 79, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HDWP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HDWP", "line": 80, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HENHMETAFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HENHMETAFILE", "line": 81, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HFONT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HFONT", "line": 82, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HGDIOBJ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HGDIOBJ", "line": 83, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HGLOBAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HGLOBAL", "line": 84, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HHOOK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HHOOK", "line": 85, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HICON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HICON", "line": 86, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HINSTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HINSTANCE", "line": 87, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HKEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HKEY", "line": 88, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HKL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HKL", "line": 89, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HLOCAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HLOCAL", "line": 90, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HMENU": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HMENU", "line": 91, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HMETAFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HMETAFILE", "line": 92, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HMODULE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HMODULE", "line": 93, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HMONITOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HMONITOR", "line": 94, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HPALETTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HPALETTE", "line": 95, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HPEN", "line": 96, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HRGN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HRGN", "line": 97, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HRSRC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HRSRC", "line": 98, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HSTR", "line": 99, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HTASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HTASK", "line": 100, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HWINSTA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HWINSTA", "line": 101, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "HWND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.HWND", "line": 102, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "INT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.INT", "line": 33, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_int"}}, "LANGID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LANGID", "line": 67, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ushort"}}, "LARGE_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LARGE_INTEGER", "line": 45, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_longlong"}}, "LCID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LCID", "line": 71, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulong"}}, "LCTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LCTYPE", "line": 70, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulong"}}, "LGRPID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LGRPID", "line": 69, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulong"}}, "LONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LONG", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_long"}}, "LPARAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.LPARAM", "name": "LPARAM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.LPARAM", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.LPARAM", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.LPARAM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.LPARAM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LPBOOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPBOOL", "line": 208, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PLONG"}}, "LPBYTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPBYTE", "line": 240, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PBYTE"}}, "LPCOLESTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPCOLESTR", "line": 52, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_wchar_p"}}, "LPCOLORREF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPCOLORREF", "line": 215, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PULONG"}}, "LPCSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPCSTR", "line": 56, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_char_p"}}, "LPCVOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPCVOID", "line": 58, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "LPCWSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPCWSTR", "line": 54, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_wchar_p"}}, "LPDWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPDWORD", "line": 214, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PULONG"}}, "LPFILETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPFILETIME", "line": 266, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PFILETIME"}}, "LPHANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPHANDLE", "line": 258, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PHANDLE"}}, "LPHKL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPHKL", "line": 260, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PHANDLE"}}, "LPINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPINT", "line": 221, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PINT"}}, "LPLONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPLONG", "line": 206, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PLONG"}}, "LPMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPMSG", "line": 271, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PMSG"}}, "LPOLESTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPOLESTR", "line": 51, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_wchar_p"}}, "LPPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPPOINT", "line": 276, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PPOINT"}}, "LPRECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPRECT", "line": 282, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PRECT"}}, "LPRECTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPRECTL", "line": 284, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PRECT"}}, "LPSC_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPSC_HANDLE", "line": 261, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PHANDLE"}}, "LPSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPSIZE", "line": 289, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PSIZE"}}, "LPSIZEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPSIZEL", "line": 291, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PSIZE"}}, "LPSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPSTR", "line": 55, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_char_p"}}, "LPUINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPUINT", "line": 226, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PUINT"}}, "LPVOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPVOID", "line": 57, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "LPWIN32_FIND_DATAA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPWIN32_FIND_DATAA", "line": 299, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PWIN32_FIND_DATAA"}}, "LPWIN32_FIND_DATAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPWIN32_FIND_DATAW", "line": 304, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PWIN32_FIND_DATAW"}}, "LPWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPWORD", "line": 201, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PUSHORT"}}, "LPWSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.LPWSTR", "line": 53, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_wchar_p"}}, "MAX_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.MAX_PATH", "name": "MAX_PATH", "setter_type": null, "type": "builtins.int"}}, "MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.MSG", "name": "MSG", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.MSG", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.MSG", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "hWnd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.MSG.hWnd", "name": "hWnd", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.c_void_p", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ctypes.c_void_p", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "lParam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.MSG.lParam", "name": "lParam", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.wintypes.LPARAM"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.MSG.message", "name": "message", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_uint"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "pt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.MSG.pt", "name": "pt", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.POINT", "ctypes.wintypes.POINT", "ctypes.wintypes.POINT"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.MSG.time", "name": "time", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "wParam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.MSG.wParam", "name": "wParam", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.wintypes.WPARAM"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.MSG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.MSG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OLESTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.OLESTR", "line": 50, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_wchar_p"}}, "PBOOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PBOOL", "line": 207, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PLONG"}}, "PBOOLEAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PBOOLEAN", "line": 241, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PBYTE"}}, "PBYTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_byte"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PBYTE", "name": "PBYTE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PBYTE", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PBYTE", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PBYTE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PBYTE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PCHAR", "name": "PCHAR", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PCHAR", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PCHAR", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "from_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ctypes.wintypes.PCHAR.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of PCHAR", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ctypes.wintypes.PCHAR.from_param", "name": "from_param", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of PCHAR", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PCHAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PDWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PDWORD", "line": 213, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PULONG"}}, "PFILETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PFILETIME", "name": "PFILETIME", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PFILETIME", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PFILETIME", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PFILETIME.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PFILETIME", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PFLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_float"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PFLOAT", "name": "PFLOAT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PFLOAT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PFLOAT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PFLOAT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PFLOAT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PHANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_void_p"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PHANDLE", "name": "PHANDLE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PHANDLE", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PHANDLE", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PHANDLE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PHANDLE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PHKEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PHKEY", "line": 259, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PHANDLE"}}, "PINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_int"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PINT", "name": "PINT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PINT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PINT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PLARGE_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_longlong"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PLARGE_INTEGER", "name": "PLARGE_INTEGER", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PLARGE_INTEGER", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PLARGE_INTEGER", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PLARGE_INTEGER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PLARGE_INTEGER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PLCID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PLCID", "line": 216, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PULONG"}}, "PLONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_long"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PLONG", "name": "PLONG", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PLONG", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PLONG", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PLONG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PLONG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.MSG"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PMSG", "name": "PMSG", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PMSG", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PMSG", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PMSG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PMSG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "POINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.POINT", "name": "POINT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.POINT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.POINT", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.POINT.x", "name": "x", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.POINT.y", "name": "y", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.POINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.POINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "POINTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.POINTL", "line": 135, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.POINT"}}, "PPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.POINT"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PPOINT", "name": "PPOINT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PPOINT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PPOINT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PPOINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PPOINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PPOINTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PPOINTL", "line": 277, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PPOINT"}}, "PRECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.RECT"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PRECT", "name": "PRECT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PRECT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PRECT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PRECT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PRECT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PRECTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PRECTL", "line": 283, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PRECT"}}, "PSHORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_short"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PSHORT", "name": "PSHORT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PSHORT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PSHORT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PSHORT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PSHORT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.SIZE"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PSIZE", "name": "PSIZE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PSIZE", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PSIZE", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PSIZE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PSIZE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PSIZEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PSIZEL", "line": 290, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PSIZE"}}, "PSMALL_RECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes._SMALL_RECT"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PSMALL_RECT", "name": "PSMALL_RECT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PSMALL_RECT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PSMALL_RECT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PSMALL_RECT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PSMALL_RECT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PUINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_uint"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PUINT", "name": "PUINT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PUINT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PUINT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PUINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PUINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PULARGE_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_ulonglong"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PULARGE_INTEGER", "name": "PULARGE_INTEGER", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PULARGE_INTEGER", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PULARGE_INTEGER", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PULARGE_INTEGER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PULARGE_INTEGER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PULONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_ulong"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PULONG", "name": "PULONG", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PULONG", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PULONG", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PULONG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PULONG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PUSHORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_ushort"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PUSHORT", "name": "PUSHORT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PUSHORT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PUSHORT", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PUSHORT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PUSHORT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PWCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.c_wchar"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PWCHAR", "name": "PWCHAR", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PWCHAR", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PWCHAR", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "from_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ctypes.wintypes.PWCHAR.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of PWCHAR", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ctypes.wintypes.PWCHAR.from_param", "name": "from_param", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of PWCHAR", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWCHAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PWIN32_FIND_DATAA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.WIN32_FIND_DATAA"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PWIN32_FIND_DATAA", "name": "PWIN32_FIND_DATAA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PWIN32_FIND_DATAA", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PWIN32_FIND_DATAA", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWIN32_FIND_DATAA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWIN32_FIND_DATAA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PWIN32_FIND_DATAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["ctypes.wintypes.WIN32_FIND_DATAW"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.PWIN32_FIND_DATAW", "name": "PWIN32_FIND_DATAW", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.PWIN32_FIND_DATAW", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCPointerType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.PWIN32_FIND_DATAW", "_ctypes._Pointer", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.PWIN32_FIND_DATAW.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.PWIN32_FIND_DATAW", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.PWORD", "line": 200, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.PUSHORT"}}, "RECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.RECT", "name": "RECT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.RECT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.RECT", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "bottom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.RECT.bottom", "name": "bottom", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.RECT.left", "name": "left", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.RECT.right", "name": "right", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.RECT.top", "name": "top", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.RECT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.RECT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RECTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.RECTL", "line": 115, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.RECT"}}, "RGB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["red", "green", "blue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.RGB", "name": "RGB", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["red", "green", "blue"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SC_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.SC_HANDLE", "line": 103, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "SERVICE_STATUS_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.SERVICE_STATUS_HANDLE", "line": 104, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "SHORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.SHORT", "line": 44, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_short"}}, "SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.SIZE", "name": "SIZE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.SIZE", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.SIZE", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "cx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.SIZE.cx", "name": "cx", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "cy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.SIZE.cy", "name": "cy", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_long"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.SIZE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.SIZE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SIZEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.SIZEL", "line": 143, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.SIZE"}}, "SMALL_RECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.SMALL_RECT", "line": 125, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes._SMALL_RECT"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Structure": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Structure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.UINT", "line": 32, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_uint"}}, "ULARGE_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.ULARGE_INTEGER", "line": 47, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulonglong"}}, "ULONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.ULONG", "line": 41, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulong"}}, "USHORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.USHORT", "line": 43, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ushort"}}, "VARIANT_BOOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.VARIANT_BOOL", "name": "VARIANT_BOOL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.VARIANT_BOOL", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.VARIANT_BOOL", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.VARIANT_BOOL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.VARIANT_BOOL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.WCHAR", "line": 31, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_wchar"}}, "WIN32_FIND_DATAA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.WIN32_FIND_DATAA", "name": "WIN32_FIND_DATAA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.WIN32_FIND_DATAA", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "cAlternateFileName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.cAlternateFileName", "name": "cAlternateFileName", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "cFileName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.cFileName", "name": "cFileName", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "dwFileAttributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.dwFileAttributes", "name": "dwFileAttributes", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "dwReserved0": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.dwReserved0", "name": "dwReserved0", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "dwReserved1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.dwReserved1", "name": "dwReserved1", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "ftCreationTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.ftCreationTime", "name": "ftCreationTime", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "ftLastAccessTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.ftLastAccessTime", "name": "ftLastAccessTime", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "ftLastWriteTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.ftLastWriteTime", "name": "ftLastWriteTime", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "nFileSizeHigh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.nFileSizeHigh", "name": "nFileSizeHigh", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "nFileSizeLow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.nFileSizeLow", "name": "nFileSizeLow", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.WIN32_FIND_DATAA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.WIN32_FIND_DATAA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WIN32_FIND_DATAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.WIN32_FIND_DATAW", "name": "WIN32_FIND_DATAW", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.WIN32_FIND_DATAW", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "cAlternateFileName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.cAlternateFileName", "name": "cAlternateFileName", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["ctypes.c_wchar"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "cFileName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.cFileName", "name": "cFileName", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["ctypes.c_wchar"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "dwFileAttributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.dwFileAttributes", "name": "dwFileAttributes", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "dwReserved0": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.dwReserved0", "name": "dwReserved0", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "dwReserved1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.dwReserved1", "name": "dwReserved1", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "ftCreationTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.ftCreationTime", "name": "ftCreationTime", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "ftLastAccessTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.ftLastAccessTime", "name": "ftLastAccessTime", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "ftLastWriteTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.ftLastWriteTime", "name": "ftLastWriteTime", "setter_type": null, "type": {".class": "Instance", "args": ["ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME", "ctypes.wintypes.FILETIME"], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "nFileSizeHigh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.nFileSizeHigh", "name": "nFileSizeHigh", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "nFileSizeLow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.nFileSizeLow", "name": "nFileSizeLow", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_ulong"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.WIN32_FIND_DATAW.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.WIN32_FIND_DATAW", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.WORD", "line": 28, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ushort"}}, "WPARAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes.WPARAM", "name": "WPARAM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes.WPARAM", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes.WPARAM", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes.WPARAM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes.WPARAM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CArgObject": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CArgObject", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CField": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CField", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CIntLikeField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes._CIntLikeT", "id": 1, "name": "_CIntLikeT", "namespace": "ctypes.wintypes._CIntLikeField", "upper_bound": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, "values": [], "variance": 0}], "column": 0, "fullname": "ctypes.wintypes._CIntLikeField", "line": 107, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes._CIntLikeT", "id": 1, "name": "_CIntLikeT", "namespace": "ctypes.wintypes._CIntLikeField", "upper_bound": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, "values": [], "variance": 0}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes._CIntLikeT", "id": 1, "name": "_CIntLikeT", "namespace": "ctypes.wintypes._CIntLikeField", "upper_bound": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, "values": [], "variance": 0}, "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_ctypes._CField"}}}, "_CIntLikeT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes._CIntLikeT", "name": "_CIntLikeT", "upper_bound": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, "values": [], "variance": 0}}, "_COORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes._COORD", "name": "_COORD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes._COORD", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes._COORD", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "X": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes._COORD.X", "name": "X", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_short"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "Y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes._COORD.Y", "name": "Y", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_short"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes._COORD.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes._COORD", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FILETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes._FILETIME", "line": 152, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.FILETIME"}}, "_LARGE_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes._LARGE_INTEGER", "line": 46, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_longlong"}}, "_POINTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes._POINTL", "line": 136, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.POINT"}}, "_Pointer": {".class": "SymbolTableNode", "cross_ref": "_ctypes._Pointer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_RECTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes._RECTL", "line": 116, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.RECT"}}, "_SMALL_RECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.wintypes._SMALL_RECT", "name": "_SMALL_RECT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.wintypes._SMALL_RECT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes.wintypes", "mro": ["ctypes.wintypes._SMALL_RECT", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "Bottom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes._SMALL_RECT.Bottom", "name": "Bottom", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_short"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "Left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes._SMALL_RECT.Left", "name": "Left", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_short"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "Right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes._SMALL_RECT.Right", "name": "Right", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_short"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}, "Top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.wintypes._SMALL_RECT.Top", "name": "Top", "setter_type": null, "type": {".class": "TypeAliasType", "args": ["ctypes.c_short"], "type_ref": "ctypes.wintypes._CIntLikeField"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.wintypes._SMALL_RECT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.wintypes._SMALL_RECT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SimpleCData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._SimpleCData", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ULARGE_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes._ULARGE_INTEGER", "line": 48, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ulonglong"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.wintypes.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "c_byte": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_byte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_char": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_char", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_char_p": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_char_p", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_double": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_double", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_float": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_float", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_int": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_int", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_long": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_long", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_longlong": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_longlong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_short": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_short", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_uint": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_uint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_ulong": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_ulong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_ulonglong": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_ulonglong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_ushort": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_ushort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_void_p": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_void_p", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_wchar": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_wchar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_wchar_p": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_wchar_p", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tagMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.tagMSG", "line": 162, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.MSG"}}, "tagPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.tagPOINT", "line": 137, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.POINT"}}, "tagRECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.tagRECT", "line": 117, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.RECT"}}, "tagSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.wintypes.tagSIZE", "line": 144, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.wintypes.SIZE"}}}, "path": "/opt/homebrew/lib/python3.11/site-packages/mypy/typeshed/stdlib/ctypes/wintypes.pyi"}