{"data_mtime": 1750965150, "dep_lines": [7, 13, 14, 15, 16, 1, 3, 4, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["charset_normalizer.cd", "charset_normalizer.constant", "charset_normalizer.md", "charset_normalizer.models", "charset_normalizer.utils", "__future__", "logging", "os", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "08c7289de86e1bcbe4af23b5c310221cadf11609", "id": "charset_normalizer.api", "ignore_all": true, "interface_hash": "c887606ab8233dcdf731b51f3f0d36f9ddafc3b6", "mtime": 1748427506, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/charset_normalizer/api.py", "plugin_data": null, "size": 22617, "suppressed": [], "version_id": "1.16.1"}