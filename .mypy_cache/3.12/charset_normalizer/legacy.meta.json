{"data_mtime": 1750965151, "dep_lines": [6, 7, 1, 3, 4, 11, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 30, 30], "dependencies": ["charset_normalizer.api", "charset_normalizer.constant", "__future__", "typing", "warnings", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "5b00ee6a9284d445a8bf68621e923bcfe0322ed1", "id": "charset_normalizer.legacy", "ignore_all": true, "interface_hash": "1e248afe751b5df81bb8cde50d43819953664217", "mtime": 1748427506, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/charset_normalizer/legacy.py", "plugin_data": null, "size": 2287, "suppressed": [], "version_id": "1.16.1"}