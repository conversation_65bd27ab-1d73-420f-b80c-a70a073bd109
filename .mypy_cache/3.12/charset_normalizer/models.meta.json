{"data_mtime": 1750965150, "dep_lines": [3, 9, 10, 141, 1, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["encodings.aliases", "charset_normalizer.constant", "charset_normalizer.utils", "charset_normalizer.cd", "__future__", "<PERSON><PERSON><PERSON>", "json", "re", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "functools", "types"], "hash": "517f3709990f1636d57b6135f0f1c32d6b30994f", "id": "charset_normalizer.models", "ignore_all": true, "interface_hash": "705f203861a1fe6b63e07f76b333d747182d712f", "mtime": 1748427506, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/charset_normalizer/models.py", "plugin_data": null, "size": 12394, "suppressed": [], "version_id": "1.16.1"}