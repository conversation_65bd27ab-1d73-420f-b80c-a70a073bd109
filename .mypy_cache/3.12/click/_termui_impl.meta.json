{"data_mtime": 1751950750, "dep_lines": [9, 23, 31, 32, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 91, 582, 616, 801, 802, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "click._compat", "click.exceptions", "click.utils", "__future__", "collections", "contextlib", "math", "os", "shlex", "sys", "time", "typing", "gettext", "io", "pathlib", "shutil", "types", "operator", "subprocess", "tempfile", "termios", "tty", "builtins", "_frozen_importlib", "_io", "_operator", "_typeshed", "abc"], "hash": "3bb2ba513d81ccd7677c5909e5983fc090036dc5", "id": "click._termui_impl", "ignore_all": true, "interface_hash": "7a6da5c054d8cc48f271f117f46dfba9d2532506", "mtime": 1749897708, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/click/_termui_impl.py", "plugin_data": null, "size": 26712, "suppressed": [], "version_id": "1.16.0"}