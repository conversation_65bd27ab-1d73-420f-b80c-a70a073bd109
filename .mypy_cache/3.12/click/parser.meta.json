{"data_mtime": 1751950750, "dep_lines": [27, 33, 39, 25, 27, 28, 30, 365, 1, 1, 1], "dep_prios": [10, 5, 25, 5, 5, 10, 5, 20, 5, 30, 30], "dependencies": ["collections.abc", "click.exceptions", "click.core", "__future__", "collections", "typing", "gettext", "difflib", "builtins", "_frozen_importlib", "abc"], "hash": "bdff694cb0d82615f737f3241d3accbb28919944", "id": "click.parser", "ignore_all": true, "interface_hash": "76bd8b8bd4a8f3f9869de547f69195b9a10569e0", "mtime": 1749897708, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/click/parser.py", "plugin_data": null, "size": 18979, "suppressed": [], "version_id": "1.16.0"}