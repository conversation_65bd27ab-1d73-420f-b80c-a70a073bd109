{"data_mtime": 1751950750, "dep_lines": [3, 12, 14, 16, 17, 20, 24, 1, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 25, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "click._compat", "click.exceptions", "click.globals", "click.types", "click.utils", "click._termui_impl", "__future__", "collections", "inspect", "io", "itertools", "sys", "typing", "contextlib", "gettext", "builtins", "_frozen_importlib", "abc"], "hash": "d0ed013ca187d919b95fa54fcd722f7bbc8cae45", "id": "click.termui", "ignore_all": true, "interface_hash": "ff5247a3d3fa8cc768c76a08ab145a27d5d027e2", "mtime": 1749897708, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/click/termui.py", "plugin_data": null, "size": 30847, "suppressed": [], "version_id": "1.16.0"}