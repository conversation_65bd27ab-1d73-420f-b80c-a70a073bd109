{".class": "MypyFile", "_fullname": "argcomplete.packages._shlex", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.packages._shlex.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.packages._shlex.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.packages._shlex.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.packages._shlex.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.packages._shlex.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.packages._shlex.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shlex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "argcomplete.packages._shlex.shlex", "name": "shlex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "argcomplete.packages._shlex", "mro": ["argcomplete.packages._shlex.shlex", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "instream", "infile", "posix", "punctuation_chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.__iter__", "name": "__iter__", "type": null}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.__next__", "name": "__next__", "type": null}}, "_pushback_chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "argcomplete.packages._shlex.shlex._pushback_chars", "name": "_pushback_chars", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "commenters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.commenters", "name": "commenters", "setter_type": null, "type": "builtins.str"}}, "debug": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.debug", "name": "debug", "setter_type": null, "type": "builtins.int"}}, "eof": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.eof", "name": "eof", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "error_leader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "infile", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.error_leader", "name": "error_leader", "type": null}}, "escape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.escape", "name": "escape", "setter_type": null, "type": "builtins.str"}}, "escapedquotes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.escapedquotes", "name": "escapedquotes", "setter_type": null, "type": "builtins.str"}}, "filestack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.filestack", "name": "filestack", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "get_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.get_token", "name": "get_token", "type": null}}, "infile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.infile", "name": "infile", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "instream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.instream", "name": "instream", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "last_wordbreak_pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "argcomplete.packages._shlex.shlex.last_wordbreak_pos", "name": "last_wordbreak_pos", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "lineno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.lineno", "name": "lineno", "setter_type": null, "type": "builtins.int"}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "argcomplete.packages._shlex.shlex.next", "name": "next", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["argcomplete.packages._shlex.shlex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.pop_source", "name": "pop_source", "type": null}}, "posix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.posix", "name": "posix", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "punctuation_chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.punctuation_chars", "name": "punctuation_chars", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "push_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "newstream", "newfile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.push_source", "name": "push_source", "type": null}}, "push_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tok"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.push_token", "name": "push_token", "type": null}}, "pushback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.pushback", "name": "pushback", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "quotes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.quotes", "name": "quotes", "setter_type": null, "type": "builtins.str"}}, "read_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.read_token", "name": "read_token", "type": null}}, "source": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "argcomplete.packages._shlex.shlex.source", "name": "source", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sourcehook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "newfile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.packages._shlex.shlex.sourcehook", "name": "sourcehook", "type": null}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.state", "name": "state", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.token", "name": "token", "setter_type": null, "type": "builtins.str"}}, "whitespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.whitespace", "name": "whitespace", "setter_type": null, "type": "builtins.str"}}, "whitespace_split": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.whitespace_split", "name": "whitespace_split", "setter_type": null, "type": "builtins.bool"}}, "wordbreaks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.wordbreaks", "name": "wordbreaks", "setter_type": null, "type": "builtins.str"}}, "wordchars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "argcomplete.packages._shlex.shlex.wordchars", "name": "wordchars", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "argcomplete.packages._shlex.shlex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "argcomplete.packages._shlex.shlex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.11/site-packages/argcomplete/packages/_shlex.py"}