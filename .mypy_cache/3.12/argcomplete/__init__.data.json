{".class": "MypyFile", "_fullname": "argcomplete", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArgcompleteException": {".class": "SymbolTableNode", "cross_ref": "argcomplete.exceptions.ArgcompleteException", "kind": "Gdef"}, "ChoicesCompleter": {".class": "SymbolTableNode", "cross_ref": "argcomplete.completers.ChoicesCompleter", "kind": "Gdef"}, "CompletionFinder": {".class": "SymbolTableNode", "cross_ref": "argcomplete.finders.CompletionFinder", "kind": "Gdef"}, "DirectoriesCompleter": {".class": "SymbolTableNode", "cross_ref": "argcomplete.completers.DirectoriesCompleter", "kind": "Gdef"}, "EnvironCompleter": {".class": "SymbolTableNode", "cross_ref": "argcomplete.completers.EnvironCompleter", "kind": "Gdef"}, "ExclusiveCompletionFinder": {".class": "SymbolTableNode", "cross_ref": "argcomplete.finders.ExclusiveCompletionFinder", "kind": "Gdef"}, "FilesCompleter": {".class": "SymbolTableNode", "cross_ref": "argcomplete.completers.FilesCompleter", "kind": "Gdef"}, "SuppressCompleter": {".class": "SymbolTableNode", "cross_ref": "argcomplete.completers.SuppressCompleter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "autocomplete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "argcomplete.autocomplete", "name": "autocomplete", "setter_type": null, "type": "argcomplete.finders.CompletionFinder"}}, "completers": {".class": "SymbolTableNode", "cross_ref": "argcomplete.completers", "kind": "Gdef"}, "debug": {".class": "SymbolTableNode", "cross_ref": "argcomplete.io.debug", "kind": "Gdef"}, "mute_stderr": {".class": "SymbolTableNode", "cross_ref": "argcomplete.io.mute_stderr", "kind": "Gdef"}, "safe_actions": {".class": "SymbolTableNode", "cross_ref": "argcomplete.finders.safe_actions", "kind": "Gdef"}, "shellcode": {".class": "SymbolTableNode", "cross_ref": "argcomplete.shell_integration.shellcode", "kind": "Gdef"}, "split_line": {".class": "SymbolTableNode", "cross_ref": "argcomplete.lexers.split_line", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "argcomplete.io.warn", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.11/site-packages/argcomplete/__init__.py"}