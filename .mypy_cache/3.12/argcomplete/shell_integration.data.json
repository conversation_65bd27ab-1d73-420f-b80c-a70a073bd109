{".class": "MypyFile", "_fullname": "argcomplete.shell_integration", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.shell_integration.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.shell_integration.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.shell_integration.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.shell_integration.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.shell_integration.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "argcomplete.shell_integration.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "bashcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "argcomplete.shell_integration.bashcode", "name": "bashcode", "setter_type": null, "type": "builtins.str"}}, "fishcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "argcomplete.shell_integration.fishcode", "name": "fishcode", "setter_type": null, "type": "builtins.str"}}, "powershell_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "argcomplete.shell_integration.powershell_code", "name": "powershell_code", "setter_type": null, "type": "builtins.str"}}, "quote": {".class": "SymbolTableNode", "cross_ref": "shlex.quote", "kind": "Gdef"}, "shell_codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "argcomplete.shell_integration.shell_codes", "name": "shell_codes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "shellcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["executables", "use_defaults", "shell", "complete_arguments", "argcomplete_script"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "argcomplete.shell_integration.shellcode", "name": "shellcode", "type": null}}, "tcshcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "argcomplete.shell_integration.tcshcode", "name": "tcshcode", "setter_type": null, "type": "builtins.str"}}}, "path": "/opt/homebrew/lib/python3.11/site-packages/argcomplete/shell_integration.py"}