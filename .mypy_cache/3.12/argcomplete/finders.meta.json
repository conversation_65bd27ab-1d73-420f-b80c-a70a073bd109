{"data_mtime": 1751950750, "dep_lines": [16, 9, 12, 13, 15, 6, 7, 8, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["argcomplete.packages._argparse", "collections.abc", "argcomplete.io", "argcomplete.completers", "argcomplete.lexers", "<PERSON><PERSON><PERSON><PERSON>", "os", "sys", "typing", "argcomplete", "builtins", "_collections_abc", "_frozen_importlib", "_io", "abc", "argcomplete.packages", "contextlib", "io", "types"], "hash": "9417a8919a64493353de706fe1c3492251d3772c", "id": "argcomplete.finders", "ignore_all": true, "interface_hash": "cc0452df076fd964f3e23c710d4f692fa29d771d", "mtime": 1749897756, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/argcomplete/finders.py", "plugin_data": null, "size": 28265, "suppressed": [], "version_id": "1.16.0"}