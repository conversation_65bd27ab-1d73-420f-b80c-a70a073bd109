{"data_mtime": 1751398980, "dep_lines": [19, 4, 30, 31, 32, 34, 41, 2, 8, 9, 10, 11, 12, 39, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 10, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "collections.abc", "_pytest._io", "_pytest.config", "_pytest.nodes", "_pytest.outcomes", "_pytest.runner", "__future__", "dataclasses", "io", "os", "pprint", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_io", "_pytest._code", "_pytest._io.terminalwriter", "_typeshed", "abc", "types"], "hash": "d7a5eca76454a92d20a4f87fec5993924ea47109", "id": "_pytest.reports", "ignore_all": true, "interface_hash": "9441db70777c7cd08a38f23e9c7402dc1f5ec174", "mtime": 1749897756, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/_pytest/reports.py", "plugin_data": null, "size": 21406, "suppressed": [], "version_id": "1.16.0"}