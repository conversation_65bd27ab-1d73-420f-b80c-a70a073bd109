{"data_mtime": 1751950750, "dep_lines": [20, 213, 5, 19, 3, 6, 7, 8, 9, 1, 1, 1, 14, 16, 17, 15, 13, 80], "dep_prios": [5, 20, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 5, 5, 5, 5, 10, 20], "dependencies": ["_pytest._io.wcwidth", "_pytest.config.exceptions", "collections.abc", "_pytest.compat", "__future__", "os", "shutil", "sys", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "c15d4aa710c364f968cf8762e3865f103584362b", "id": "_pytest._io.terminalwriter", "ignore_all": true, "interface_hash": "9a3ae4d8902135743067119f346d428ce049ad4f", "mtime": 1749897756, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/_pytest/_io/terminalwriter.py", "plugin_data": null, "size": 8849, "suppressed": ["pygments.formatters.terminal", "pygments.lexers.diff", "pygments.lexers.python", "pygments.lexer", "pygments", "colorama"], "version_id": "1.16.0"}