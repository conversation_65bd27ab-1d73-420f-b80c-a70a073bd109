{".class": "MypyFile", "_fullname": "_pytest.warning_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "PytestAssertRewriteWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestAssertRewriteWarning", "name": "PytestAssertRewriteWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestAssertRewriteWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestAssertRewriteWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestAssertRewriteWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestAssertRewriteWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestAssertRewriteWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestCacheWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestCacheWarning", "name": "PytestCacheWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestCacheWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestCacheWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestCacheWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestCacheWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestCacheWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestCollectionWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestCollectionWarning", "name": "PytestCollectionWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestCollectionWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestCollectionWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestCollectionWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestCollectionWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestCollectionWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestConfigWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestConfigWarning", "name": "PytestConfigWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestConfigWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestConfigWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestConfigWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestConfigWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestConfigWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestDeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning", "builtins.DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestDeprecationWarning", "name": "PytestDeprecationWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.warning_types.PytestDeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestDeprecationWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestDeprecationWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestDeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestDeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestExperimentalApiWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning", "builtins.FutureWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestExperimentalApiWarning", "name": "PytestExperimentalApiWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestExperimentalApiWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestExperimentalApiWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.FutureWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestExperimentalApiWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}, "simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "apiname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_pytest.warning_types.PytestExperimentalApiWarning.simple", "name": "simple", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "apiname"], "arg_types": [{".class": "TypeType", "item": "_pytest.warning_types.PytestExperimentalApiWarning"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simple of PytestExperimentalApiWarning", "ret_type": "_pytest.warning_types.PytestExperimentalApiWarning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.warning_types.PytestExperimentalApiWarning.simple", "name": "simple", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "apiname"], "arg_types": [{".class": "TypeType", "item": "_pytest.warning_types.PytestExperimentalApiWarning"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simple of PytestExperimentalApiWarning", "ret_type": "_pytest.warning_types.PytestExperimentalApiWarning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestExperimentalApiWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestExperimentalApiWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestFDWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestFDWarning", "name": "PytestFDWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestFDWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestFDWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestFDWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestFDWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestFDWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestRemovedIn9Warning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestRemovedIn9Warning", "name": "PytestRemovedIn9Warning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.warning_types.PytestRemovedIn9Warning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestRemovedIn9Warning", "_pytest.warning_types.PytestDeprecationWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestRemovedIn9Warning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestRemovedIn9Warning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestRemovedIn9Warning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestUnhandledThreadExceptionWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestUnhandledThreadExceptionWarning", "name": "PytestUnhandledThreadExceptionWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestUnhandledThreadExceptionWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestUnhandledThreadExceptionWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestUnhandledThreadExceptionWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestUnhandledThreadExceptionWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestUnhandledThreadExceptionWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestUnknownMarkWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestUnknownMarkWarning", "name": "PytestUnknownMarkWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestUnknownMarkWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestUnknownMarkWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestUnknownMarkWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestUnknownMarkWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestUnknownMarkWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestUnraisableExceptionWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.warning_types.PytestWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestUnraisableExceptionWarning", "name": "PytestUnraisableExceptionWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.PytestUnraisableExceptionWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestUnraisableExceptionWarning", "_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestUnraisableExceptionWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestUnraisableExceptionWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestUnraisableExceptionWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.UserWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.PytestWarning", "name": "PytestWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.warning_types.PytestWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.PytestWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.warning_types.PytestWarning.__module__", "name": "__module__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.PytestWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UnformattedWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.warning_types.UnformattedWarning", "name": "UnformattedWarning", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.warning_types.UnformattedWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 118, "name": "category", "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 119, "name": "template", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "_pytest.warning_types", "mro": ["_pytest.warning_types.UnformattedWarning", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.warning_types.UnformattedWarning.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "category", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.warning_types.UnformattedWarning.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "category", "template"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.warning_types.UnformattedWarning"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnformattedWarning", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "_pytest.warning_types.UnformattedWarning.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "category"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "template"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["category", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.warning_types.UnformattedWarning.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["category", "template"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of UnformattedWarning", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.warning_types.UnformattedWarning.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["category", "template"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of UnformattedWarning", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "category": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.warning_types.UnformattedWarning.category", "name": "category", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_pytest.warning_types.UnformattedWarning.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.warning_types.UnformattedWarning"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of UnformattedWarning", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.warning_types.UnformattedWarning.template", "name": "template", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types.UnformattedWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "id": 1, "name": "_W", "namespace": "_pytest.warning_types.UnformattedWarning", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.warning_types.UnformattedWarning"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_W"], "typeddict_type": null}}, "_W": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.warning_types._W", "name": "_W", "upper_bound": "_pytest.warning_types.PytestWarning", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.warning_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.warning_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.warning_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.warning_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.warning_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.warning_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "warn_explicit_for": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["method", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.warning_types.warn_explicit_for", "name": "warn_explicit_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["method", "message"], "arg_types": ["types.FunctionType", "_pytest.warning_types.PytestWarning"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn_explicit_for", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.11/site-packages/_pytest/warning_types.py"}