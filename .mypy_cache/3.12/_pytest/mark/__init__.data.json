{".class": "MypyFile", "_fullname": "_pytest.mark", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef", "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef", "module_public": false}, "EMPTY_PARAMETERSET_OPTION": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.EMPTY_PARAMETERSET_OPTION", "kind": "Gdef", "module_public": false}, "ExitCode": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ExitCode", "kind": "Gdef", "module_public": false}, "Expression": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.expression.Expression", "kind": "Gdef", "module_public": false}, "HIDDEN_PARAM": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.HIDDEN_PARAM", "kind": "Gdef"}, "Item": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Item", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "KeywordMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.mark.KeywordMatcher", "name": "KeywordMatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.mark.KeywordMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 169, "name": "_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "_pytest.mark", "mro": ["_pytest.mark.KeywordMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": [null, null, "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_pytest.mark.KeywordMatcher.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": [null, null, "kwargs"], "arg_types": ["_pytest.mark.KeywordMatcher", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of KeywordMatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.mark.KeywordMatcher.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.KeywordMatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_names"], "arg_types": ["_pytest.mark.KeywordMatcher", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeywordMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "_pytest.mark.KeywordMatcher.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_names"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.mark.KeywordMatcher.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["_names"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of KeywordMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.mark.KeywordMatcher.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["_names"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of KeywordMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_pytest.mark.KeywordMatcher.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.mark.KeywordMatcher._names", "name": "_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}}}, "from_item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_pytest.mark.KeywordMatcher.from_item", "name": "from_item", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "item"], "arg_types": [{".class": "TypeType", "item": "_pytest.mark.KeywordMatcher"}, "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_item of KeywordMatcher", "ret_type": "_pytest.mark.KeywordMatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.mark.KeywordMatcher.from_item", "name": "from_item", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "item"], "arg_types": [{".class": "TypeType", "item": "_pytest.mark.KeywordMatcher"}, "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_item of KeywordMatcher", "ret_type": "_pytest.mark.KeywordMatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.mark.KeywordMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.mark.KeywordMatcher", "values": [], "variance": 0}, "slots": ["_names"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MARK_GEN": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MARK_GEN", "kind": "Gdef"}, "Mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.Mark", "kind": "Gdef"}, "MarkDecorator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkDecorator", "kind": "Gdef"}, "MarkGenerator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkGenerator", "kind": "Gdef"}, "MarkMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.mark.MarkMatcher", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.mark.MarkMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 238, "name": "own_mark_name_mapping", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "_pytest.mark", "mro": ["_pytest.mark.MarkMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": [null, null, "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_pytest.mark.MarkMatcher.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": [null, null, "kwargs"], "arg_types": ["_pytest.mark.MarkMatcher", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Mark<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.mark.MarkMatcher.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "own_mark_name_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.MarkMatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "own_mark_name_mapping"], "arg_types": ["_pytest.mark.MarkMatcher", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MarkM<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "_pytest.mark.MarkMatcher.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "own_mark_name_mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["own_mark_name_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.mark.MarkMatcher.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["own_mark_name_mapping"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.mark.MarkMatcher.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["own_mark_name_mapping"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_pytest.mark.MarkMatcher.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "from_markers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "markers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_pytest.mark.MarkMatcher.from_markers", "name": "from_markers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markers"], "arg_types": [{".class": "TypeType", "item": "_pytest.mark.MarkMatcher"}, {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_markers of MarkMatcher", "ret_type": "_pytest.mark.MarkMatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.mark.MarkMatcher.from_markers", "name": "from_markers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markers"], "arg_types": [{".class": "TypeType", "item": "_pytest.mark.MarkMatcher"}, {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_markers of MarkMatcher", "ret_type": "_pytest.mark.MarkMatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "own_mark_name_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.mark.MarkMatcher.own_mark_name_mapping", "name": "own_mark_name_mapping", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.mark.MarkMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.mark.MarkMatcher", "values": [], "variance": 0}, "slots": ["own_mark_name_mapping"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NOT_SET": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.NOT_SET", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParameterSet": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.ParameterSet", "kind": "Gdef"}, "ParseError": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.expression.ParseError", "kind": "Gdef", "module_public": false}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef", "module_public": false}, "StashKey": {".class": "SymbolTableNode", "cross_ref": "_pytest.stash.StashKey", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.exceptions.UsageError", "kind": "Gdef", "module_public": false}, "_HiddenParam": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures._HiddenParam", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.mark.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.mark.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_parse_expression": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expr", "exc_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark._parse_expression", "name": "_parse_expression", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expr", "exc_message"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_expression", "ret_type": "_pytest.mark.expression.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "deselect_by_keyword": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["items", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.deselect_by_keyword", "name": "deselect_by_keyword", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["items", "config"], "arg_types": [{".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "builtins.list"}, "_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deselect_by_keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deselect_by_mark": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["items", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.deselect_by_mark", "name": "deselect_by_mark", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["items", "config"], "arg_types": [{".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "builtins.list"}, "_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deselect_by_mark", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_empty_parameterset_mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.get_empty_parameterset_mark", "kind": "Gdef"}, "hookimpl": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookimpl", "kind": "Gdef", "module_public": false}, "old_mark_config_key": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.mark.old_mark_config_key", "name": "old_mark_config_key", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "_pytest.stash.StashKey"}}}, "param": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 5], "arg_names": ["values", "marks", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.param", "name": "param", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5], "arg_names": ["values", "marks", "id"], "arg_types": ["builtins.object", {".class": "UnionType", "items": ["_pytest.mark.structures.MarkDecorator", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.mark.structures.MarkDecorator", "_pytest.mark.structures.Mark"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_pytest.mark.structures._HiddenParam", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "param", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser"], "arg_types": ["_pytest.config.argparsing.Parser"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_cmdline_main": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.mark.pytest_cmdline_main", "name": "pytest_cmdline_main", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_cmdline_main", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.mark.pytest_cmdline_main", "name": "pytest_cmdline_main", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_cmdline_main", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_collection_modifyitems": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["items", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.pytest_collection_modifyitems", "name": "pytest_collection_modifyitems", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["items", "config"], "arg_types": [{".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "builtins.list"}, "_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_collection_modifyitems", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_unconfigure": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.mark.pytest_unconfigure", "name": "pytest_unconfigure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_unconfigure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/opt/homebrew/lib/python3.11/site-packages/_pytest/mark/__init__.py"}