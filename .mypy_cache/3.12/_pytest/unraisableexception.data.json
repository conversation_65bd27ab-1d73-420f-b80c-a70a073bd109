{".class": "MypyFile", "_fullname": "_pytest.unraisableexception", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "Item": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Item", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "StashKey": {".class": "SymbolTableNode", "cross_ref": "_pytest.stash.StashKey", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UnraisableMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.unraisableexception.UnraisableMeta", "name": "UnraisableMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "_pytest.unraisableexception.UnraisableMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["msg", "cause_msg", "exc_value"]}}, "module_name": "_pytest.unraisableexception", "mro": ["_pytest.unraisableexception.UnraisableMeta", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "msg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cause_msg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exc_value"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "msg", "cause_msg", "exc_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "_pytest.unraisableexception.UnraisableMeta.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "msg", "cause_msg", "exc_value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__new__ of UnraisableMeta", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.UnraisableMeta._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_asdict of UnraisableMeta", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta._field_defaults", "name": "_field_defaults", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta._field_types", "name": "_field_types", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta._fields", "name": "_fields", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.unraisableexception.UnraisableMeta._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of UnraisableMeta", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta._make", "name": "_make", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of UnraisableMeta", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "msg", "cause_msg", "exc_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.UnraisableMeta._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "msg", "cause_msg", "exc_value"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_replace of UnraisableMeta", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta._NT", "id": -1, "name": "_NT", "namespace": "_pytest.unraisableexception.UnraisableMeta._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta._source", "name": "_source", "setter_type": null, "type": "builtins.str"}}, "cause_msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta.cause_msg", "name": "cause_msg", "setter_type": null, "type": "builtins.str"}}, "cause_msg-redefinition": {".class": "SymbolTableNode", "cross_ref": "_pytest.unraisableexception.UnraisableMeta.cause_msg", "kind": "<PERSON><PERSON><PERSON>"}, "exc_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta.exc_value", "name": "exc_value", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "exc_value-redefinition": {".class": "SymbolTableNode", "cross_ref": "_pytest.unraisableexception.UnraisableMeta.exc_value", "kind": "<PERSON><PERSON><PERSON>"}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "_pytest.unraisableexception.UnraisableMeta.msg", "name": "msg", "setter_type": null, "type": "builtins.str"}}, "msg-redefinition": {".class": "SymbolTableNode", "cross_ref": "_pytest.unraisableexception.UnraisableMeta.msg", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.unraisableexception.UnraisableMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": "_pytest.unraisableexception.UnraisableMeta"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.unraisableexception.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.unraisableexception.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.unraisableexception.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.unraisableexception.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.unraisableexception.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.unraisableexception.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cleanup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["config", "prev_hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.cleanup", "name": "cleanup", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["config", "prev_hook"], "arg_types": ["_pytest.config.Config", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sys.UnraisableHookArgs"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cleanup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect_unraisable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.collect_unraisable", "name": "collect_unraisable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_unraisable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef"}, "gc_collect_harder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.gc_collect_harder", "name": "gc_collect_harder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iterations"], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "gc_collect_harder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gc_collect_iterations_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.unraisableexception.gc_collect_iterations_key", "name": "gc_collect_iterations_key", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_pytest.stash.StashKey"}}}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.unraisableexception.pytest_runtest_call", "name": "pytest_runtest_call", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_call", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.unraisableexception.pytest_runtest_call", "name": "pytest_runtest_call", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_call", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_setup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.unraisableexception.pytest_runtest_setup", "name": "pytest_runtest_setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_setup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.unraisableexception.pytest_runtest_setup", "name": "pytest_runtest_setup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_setup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_teardown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.unraisableexception.pytest_runtest_teardown", "name": "pytest_runtest_teardown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_teardown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.unraisableexception.pytest_runtest_teardown", "name": "pytest_runtest_teardown", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_teardown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "tracemalloc_message": {".class": "SymbolTableNode", "cross_ref": "_pytest.tracemalloc.tracemalloc_message", "kind": "Gdef"}, "unraisable_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "_pytest.unraisableexception.unraisable_exceptions", "name": "unraisable_exceptions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.unraisableexception.UnraisableMeta"}, "builtins.BaseException"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "collections.deque"}], "extra_attrs": null, "type_ref": "_pytest.stash.StashKey"}}}, "unraisable_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": [null, "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.unraisableexception.unraisable_hook", "name": "unraisable_hook", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "append"], "arg_types": ["sys.UnraisableHookArgs", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.unraisableexception.UnraisableMeta"}, "builtins.BaseException"], "uses_pep604_syntax": true}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unraisable_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/_pytest/unraisableexception.py"}