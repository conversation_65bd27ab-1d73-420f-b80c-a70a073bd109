{"data_mtime": 1751398979, "dep_lines": [21, 22, 6, 19, 20, 24, 4, 6, 12, 13, 14, 17, 19, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 20, 10, 10, 5, 5, 20, 5, 30, 30], "dependencies": ["_pytest._io.pprint", "_pytest._io.saferepr", "collections.abc", "_pytest.outcomes", "_pytest._code", "_pytest.config", "__future__", "collections", "os", "pprint", "typing", "unicodedata", "_pytest", "builtins", "_frozen_importlib", "abc"], "hash": "dde1f912e04c8477c51a7b12a3cf09b3887a9963", "id": "_pytest.assertion.util", "ignore_all": true, "interface_hash": "718a0475a71263772fd3c4f4872ecebb8bc784fd", "mtime": 1749897756, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/_pytest/assertion/util.py", "plugin_data": null, "size": 20713, "suppressed": [], "version_id": "1.16.0"}