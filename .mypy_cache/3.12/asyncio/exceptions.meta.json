{"data_mtime": 1751950749, "dep_lines": [1, 27, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 30, 30, 30, 30, 30], "dependencies": ["sys", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing"], "hash": "42fc15660d7655d36773f053dc5131e53e720ada", "id": "asyncio.exceptions", "ignore_all": true, "interface_hash": "8f825b84fada6e8881559f234cc1281ab597ab45", "mtime": 1749897762, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/exceptions.pyi", "plugin_data": null, "size": 1163, "suppressed": [], "version_id": "1.16.0"}