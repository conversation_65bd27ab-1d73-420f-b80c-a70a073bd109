{"data_mtime": 1751950751, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 1, 2, 3, 4, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio.events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "collections.abc", "concurrent.futures", "ssl", "sys", "_typeshed", "asyncio", "<PERSON><PERSON><PERSON>", "socket", "typing", "typing_extensions", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "_socket", "_ssl", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "enum", "types"], "hash": "f36372a22effd98d6e21008d56ee5aa510be6328", "id": "asyncio.base_events", "ignore_all": true, "interface_hash": "9e36c8adfd1c11c67a16c478ccf84443de188d24", "mtime": 1749897762, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/opt/homebrew/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/base_events.pyi", "plugin_data": null, "size": 19671, "suppressed": [], "version_id": "1.16.0"}