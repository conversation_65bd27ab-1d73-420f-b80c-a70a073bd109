{".class": "MypyFile", "_fullname": "anyio._core._sockets", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddressFamily": {".class": "SymbolTableNode", "cross_ref": "socket.AddressFamily", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyIPAddressFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio._core._sockets.AnyIPAddressFamily", "line": 49, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "socket.AddressFamily", "value": "AF_UNSPEC"}, {".class": "LiteralType", "fallback": "socket.AddressFamily", "value": "AF_INET"}, {".class": "LiteralType", "fallback": "socket.AddressFamily", "value": "AF_INET6"}], "uses_pep604_syntax": false}}}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "ConnectedUDPSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.ConnectedUDPSocket", "kind": "Gdef"}, "ConnectedUNIXDatagramSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.ConnectedUNIXDatagramSocket", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Event", "kind": "Gdef"}, "FileDescriptorLike": {".class": "SymbolTableNode", "cross_ref": "_typeshed.FileDescriptorLike", "kind": "Gdef"}, "IPAddressFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio._core._sockets.IPAddressFamily", "line": 52, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "socket.AddressFamily", "value": "AF_INET"}, {".class": "LiteralType", "fallback": "socket.AddressFamily", "value": "AF_INET6"}], "uses_pep604_syntax": false}}}, "IPAddressType": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.IPAddressType", "kind": "Gdef"}, "IPPROTO_IPV6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "anyio._core._sockets.IPPROTO_IPV6", "name": "IPPROTO_IPV6", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "IPSockAddrType": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.IPSockAddrType", "kind": "Gdef"}, "IPv6Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Address", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MultiListener": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.stapled.MultiListener", "kind": "Gdef"}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "os.PathLike", "kind": "Gdef"}, "SocketKind": {".class": "SymbolTableNode", "cross_ref": "socket.SocketKind", "kind": "Gdef"}, "SocketListener": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.SocketListener", "kind": "Gdef"}, "SocketStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.SocketStream", "kind": "Gdef"}, "TLSStream": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.tls.TLSStream", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UDPSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.UDPSocket", "kind": "Gdef"}, "UNIXDatagramSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.UNIXDatagramSocket", "kind": "Gdef"}, "UNIXSocketStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.UNIXSocketStream", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._sockets.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._sockets.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._sockets.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._sockets.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._sockets.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._sockets.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aclose_forcefully": {".class": "SymbolTableNode", "cross_ref": "anyio._core._resources.aclose_forcefully", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chmod": {".class": "SymbolTableNode", "cross_ref": "os.chmod", "kind": "Gdef"}, "connect_tcp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "anyio._core._sockets.connect_tcp", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["anyio.abc._sockets.SocketStream", "anyio.streams.tls.TLSStream"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 3, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 3, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 3, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 3, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "ssl.SSLContext", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "ssl.SSLContext", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "happy_eyeballs_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.connect_tcp", "name": "connect_tcp", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 3, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "ssl.SSLContext", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 3, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "tls", "ssl_context", "tls_standard_compatible", "tls_hostname", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["remote_host", "remote_port", "local_host", "happy_eyeballs_delay"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "connect_unix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.connect_unix", "name": "connect_unix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_unix", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXSocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_ipv6_sockaddr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sockaddr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "anyio._core._sockets.convert_ipv6_sockaddr", "name": "convert_ipv6_sockaddr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sockaddr"], "arg_types": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert_ipv6_sockaddr", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_connected_udp_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "family", "local_host", "local_port", "reuse_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.create_connected_udp_socket", "name": "create_connected_udp_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["remote_host", "remote_port", "family", "local_host", "local_port", "reuse_port"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._sockets.AnyIPAddressFamily"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connected_udp_socket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.ConnectedUDPSocket"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_connected_unix_datagram_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["remote_path", "local_path", "local_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.create_connected_unix_datagram_socket", "name": "create_connected_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["remote_path", "local_path", "local_mode"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connected_unix_datagram_socket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.ConnectedUNIXDatagramSocket"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_task_group": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.create_task_group", "kind": "Gdef"}, "create_tcp_listener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["local_host", "local_port", "family", "backlog", "reuse_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.create_tcp_listener", "name": "create_tcp_listener", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["local_host", "local_port", "family", "backlog", "reuse_port"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._sockets.AnyIPAddressFamily"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tcp_listener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "anyio.streams.stapled.MultiListener"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_udp_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5], "arg_names": ["family", "local_host", "local_port", "reuse_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.create_udp_socket", "name": "create_udp_socket", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": ["family", "local_host", "local_port", "reuse_port"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._sockets.AnyIPAddressFamily"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPAddressType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_udp_socket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UDPSocket"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_unix_datagram_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["local_path", "local_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["local_path", "local_mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXDatagramSocket"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_unix_listener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["path", "mode", "backlog"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.create_unix_listener", "name": "create_unix_listener", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["path", "mode", "backlog"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_listener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketListener"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "get_async_backend": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.get_async_backend", "kind": "Gdef"}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "socket.AddressFamily"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "socket.SocketKind"], "uses_pep604_syntax": true}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getaddrinfo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["socket.AddressFamily", "socket.SocketKind", "builtins.int", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getnameinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["sockaddr", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "anyio._core._sockets.getnameinfo", "name": "getnameinfo", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["sockaddr", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnameinfo", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ip_address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.ip_address", "kind": "Gdef"}, "move_on_after": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.move_on_after", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "setup_unix_local_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["path", "mode", "socktype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "anyio._core._sockets.setup_unix_local_socket", "name": "setup_unix_local_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["path", "mode", "socktype"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_unix_local_socket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "socket.socket"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "to_thread": {".class": "SymbolTableNode", "cross_ref": "anyio.to_thread", "kind": "Gdef"}, "wait_readable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "anyio._core._sockets.wait_readable", "name": "wait_readable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_readable", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wait_socket_readable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sock"], "dataclass_transform_spec": null, "deprecated": "function anyio._core._sockets.wait_socket_readable is deprecated: This function is deprecated; use `wait_readable` instead", "flags": ["is_decorated"], "fullname": "anyio._core._sockets.wait_socket_readable", "name": "wait_socket_readable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sock"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_socket_readable", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.wait_socket_readable", "name": "wait_socket_readable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sock"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_socket_readable", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_socket_writable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sock"], "dataclass_transform_spec": null, "deprecated": "function anyio._core._sockets.wait_socket_writable is deprecated: This function is deprecated; use `wait_writable` instead", "flags": ["is_decorated"], "fullname": "anyio._core._sockets.wait_socket_writable", "name": "wait_socket_writable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sock"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_socket_writable", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._sockets.wait_socket_writable", "name": "wait_socket_writable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sock"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_socket_writable", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_writable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "anyio._core._sockets.wait_writable", "name": "wait_writable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_writable", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/opt/homebrew/lib/python3.11/site-packages/anyio/_core/_sockets.py"}