{"data_mtime": 1750965150, "dep_lines": [3, 1, 3, 4, 5, 6, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 5, 5, 30, 30], "dependencies": ["collections.abc", "__future__", "collections", "inspect", "typing", "weakref", "builtins", "_frozen_importlib", "abc"], "hash": "d36a695ff3a329fb42c20fcf2254064367dfc1f6", "id": "blinker._utilities", "ignore_all": true, "interface_hash": "cbc2e7abfde15dabc9a549c8e66225242a2eace9", "mtime": 1745616054, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/Desktop/Codebase/Astral_Code/sora-poc/.venv/lib/python3.12/site-packages/blinker/_utilities.py", "plugin_data": null, "size": 1675, "suppressed": [], "version_id": "1.16.1"}