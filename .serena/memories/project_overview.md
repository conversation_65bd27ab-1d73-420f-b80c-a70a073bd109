# Sora Video Generation System - Project Overview

## Project Purpose
Production-ready multi-user video generation system built around Azure OpenAI's Sora API with dual-provider architecture supporting both Azure Sora and Google Veo3. Features unified provider interface, 15+ concurrent users with real-time updates, intelligent queue management, and complete browser video playbook.

## Status: PRODUCTION VALIDATED
- Foundation phase 85% complete
- Dual-provider architecture operational  
- UI integration validated
- Browser video playback SOLVED (WebM/VP9 conversion)
- 15+ concurrent users validated
- Zero-configuration Docker deployment

## Technology Stack
- **Backend**: Python 3.11+ with Flask 3.0+
- **Data Validation**: Pydantic v2 for all models
- **AI Agents**: Pydantic AI with module-global reuse pattern
- **Package Management**: UV (always use `uv add/remove`, never edit pyproject.toml directly)
- **Code Quality**: Ruff (formatting/linting), MyPy (type checking)
- **Testing**: Pytest (300+ tests, 93% pass rate)
- **Database**: SQLAlchemy with SQLite (local) / PostgreSQL (Docker/production)
- **Queue System**: Celery + Redis for background processing
- **Real-time**: Flask-SocketIO WebSocket integration
- **Deployment**: Docker Compose with 5 containers

## Key Architecture Patterns

### 1. Dual-Provider Architecture
- **Azure Sora**: Production video generation (real API)
- **Google Veo3**: Mock provider for development/testing
- **Unified Interface**: Single API for all provider interactions
- **Provider Factory**: Dynamic provider creation and feature discovery

### 2. Configuration Factory Pattern
- Environment-aware configuration (local/docker/production auto-detection)
- Single `.env` file with automatic environment detection
- Factory pattern for all configuration creation

### 3. AI Agent Reuse Pattern (Performance Critical)
- Module-global constants to avoid repeated instantiation
- Never instantiate AI agents per-call
- Cached configuration and provider instances

### 4. Vertical Slice Architecture
- Each feature contains complete functionality stacks
- Co-located testing in `tests/` subdirectories
- Clear module boundaries with single responsibilities

## Project Structure
```
src/
├── main.py                 # Flask app entry point
├── core/                   # Pydantic models & domain logic
├── api/                    # Flask routes & API endpoints
├── database/               # SQLAlchemy models & connection management
├── config/                 # Environment configuration & factory patterns
├── features/               # Business logic modules
│   ├── sora_integration/   # Azure Sora video generation
│   ├── veo3_integration/   # Google Veo3 mock provider
│   └── video_generation/   # Unified provider interface
├── job_queue/              # Celery background processing
├── realtime/               # WebSocket real-time updates
├── session/                # Multi-user session management
├── rate_limiting/          # Distributed rate limiting
├── monitoring/             # Health checks & metrics
└── deployment/             # Docker & production config
```

## Development Commands

### Quick Start
```bash
# Local Development (Recommended)
./scripts/dev-local.sh              # Starts Redis, Celery worker, Flask app
# Access: http://localhost:5001

# Docker Deployment (Production-ready)
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d
# Access: http://localhost:8090
```

### Code Quality Pipeline
```bash
# Complete quality check (run before commits)
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest

# Individual commands
uv run ruff format .                # Code formatting
uv run ruff check .                 # Linting  
uv run mypy src/                    # Type checking
uv run pytest                      # All tests
```

## Key Features Validated
- ✅ Complete Video Display: Videos play directly in browser
- ✅ Dual-Provider Support: Azure Sora + Google Veo3 unified interface
- ✅ Multi-User Support: 15+ concurrent users with session isolation
- ✅ Real-time Updates: WebSocket notifications for job progress
- ✅ WebM/VP9 Conversion: Automatic browser-compatible video format
- ✅ Zero-Configuration Deployment: Docker compose up and running
- ✅ Production Security: Rate limiting, input validation, secure sessions

## Critical Development Patterns
- **MANDATORY**: Always use `uv` for package management
- **MANDATORY**: Restart Flask server after provider routing changes
- **File Length Limits**: 500 lines max, refactor if approaching
- **Function Length**: 50 lines max for single responsibility
- **AI Agent Pattern**: Module-global constants, never per-call instantiation
- **Testing**: Co-located tests, 300+ tests with comprehensive coverage

## Environment Configuration
- **Single .env file**: Auto-detects local/docker/production
- **Local Development**: Uses localhost services (Redis, SQLite)
- **Docker Deployment**: Uses container hostnames (postgres, redis)
- **Production**: Full security settings, production database

## Known Critical Patterns
- **Provider Routing**: Flask dev server caches routing logic - always restart after changes
- **Video Format**: WebM/VP9 conversion solves browser compatibility
- **Session Management**: Per-IP isolation with WebSocket real-time updates
- **Performance**: 15+ concurrent users validated, <50ms provider creation