# Docker Deployment End-to-End Test Plan

## 🎯 **Test Objective**
Perform a complete end-to-end test of the Docker deployment by:
1. Building all Docker containers from scratch
2. Starting the complete application stack
3. Using Playwright to test the UI end-to-end
4. Successfully generating a video and verifying playback

## 📋 **Test Steps**

### **Phase 0: Create Test Plan Documentation**
1. **Create TEST_PLAN.md**
   - ✅ Create `TEST_PLAN.md` in project root
   - ✅ Document complete test procedure for future iterations
   - ✅ Include all commands, selectors, and validation steps
   - ✅ Add troubleshooting section with common issues

### **Phase 1: Environment Setup**
1. **Use Existing Configuration**
   - ✅ **SKIP .env copy** - use existing `.env` file with real Azure credentials
   - ✅ Verify current `.env` has required Azure OpenAI variables
   - ✅ No environment file changes needed

2. **Clean Docker Environment**
   - Stop any existing containers
   - Remove existing containers and images
   - Clean up Docker volumes and networks

### **Phase 2: Build Docker Containers**
1. **Build from Scratch**
   - Build all containers using `docker-compose build --no-cache`
   - Verify 5 containers are built successfully:
     - `sora-postgres-simple` (PostgreSQL database)
     - `sora-redis-simple` (Redis cache/broker)
     - `sora-app-simple` (Flask application)
     - `sora-worker-simple` (Celery worker)
     - `sora-nginx-simple` (Nginx reverse proxy)

2. **Container Verification**
   - Check that all containers start successfully
   - Verify health checks pass for all services
   - Confirm port mappings (port 80 for application)

### **Phase 3: Application Health Checks**
1. **Service Health Verification**
   - Check application health: `curl http://localhost/health`
   - Check database health: `curl http://localhost/health/database`
   - Check Azure API health: `curl http://localhost/health/azure`
   - Check queue status: `curl http://localhost/queue/status`

2. **Database Initialization**
   - Verify PostgreSQL database is created
   - Confirm database tables are initialized
   - Check database connectivity

### **Phase 4: Playwright End-to-End Testing**
1. **Start Playwright Browser Session**
   - Navigate to `http://localhost`
   - Take screenshot of initial page
   - Verify page loads correctly

2. **UI Interaction Test**
   - Fill the prompt textarea (`#prompt`) with test text: "A cat playing with a ball"
   - Click the "Generate Video" button (`#generateBtn`)
   - Wait for video generation to start
   - Monitor real-time progress updates via WebSocket

3. **Video Generation Verification**
   - Wait for video generation to complete (may take 1-3 minutes)
   - Verify video container appears (`#videoContainer`)
   - Check that video element is populated (`#generatedVideo`)
   - Verify video has valid `src` attribute

4. **Video Playback Test - CRITICAL VALIDATION**
   - **MUST VERIFY**: Video element is visible (not `display: none`)
   - **MUST CHECK**: Video src attribute is populated and accessible
   - **MUST VALIDATE**: No browser video errors (check `video.error` property)
   - **MUST TEST**: Video actually loads and displays frames (not just download option)
   - **MUST CONFIRM**: Video plays when play button is clicked
   - **Browser Console Check**: Verify no DEMUXER_ERROR or codec errors
   - **Fallback Detection**: Ensure download fallback is NOT the primary experience
   - Check video controls are functional (play, pause, scrub)
   - Confirm video dimensions and duration are displayed

### **Phase 5: Test Validation**
1. **Success Criteria**
   - ✅ All 5 Docker containers running and healthy
   - ✅ Application accessible at `http://localhost`
   - ✅ Video generation form functional
   - ✅ Video generation completes successfully
   - ✅ Generated video displays in browser
   - ✅ Video playback works correctly

2. **Failure Conditions - BLOCKING ISSUES**
   - ❌ Any container fails to start or health check fails
   - ❌ Application not accessible
   - ❌ Video generation fails or times out
   - ❌ **CRITICAL**: Video element has `display: none` style
   - ❌ **CRITICAL**: Video shows error message "Video preview is not available"
   - ❌ **CRITICAL**: Browser console shows DEMUXER_ERROR or codec errors
   - ❌ **CRITICAL**: Video file downloads but doesn't play in browser
   - ❌ **CRITICAL**: Video element shows error state (`video.error` property set)
   - ❌ Video does not display or play correctly

### **Phase 6: Cleanup and Reporting**
1. **Collect Test Evidence**
   - Screenshots of UI at each step
   - Container logs for debugging
   - Video generation time metrics
   - Final video file verification

2. **Environment Cleanup**
   - Stop all containers
   - Optional: Remove test containers and volumes
   - Generate test report

3. **Update Test Plan Documentation**
   - Add results to TEST_PLAN.md
   - Document any issues encountered
   - Update troubleshooting section

## 🔧 **Technical Implementation Details**

### **Docker Commands**
```bash
# Clean existing containers
docker-compose -f src/deployment/docker/docker-compose.simple.yml down -v
docker system prune -f

# Build containers from scratch
docker-compose -f src/deployment/docker/docker-compose.simple.yml build --no-cache

# Start all services
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# Check container status
docker-compose -f src/deployment/docker/docker-compose.simple.yml ps

# View logs
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs -f
```

### **Playwright Test Selectors**
- **Prompt Input**: `#prompt`
- **Generate Button**: `#generateBtn` (submit button)
- **Video Container**: `#videoContainer`
- **Video Element**: `#generatedVideo`
- **Download Button**: `#downloadBtn`

### **Critical Playwright Validation Commands**
```javascript
// MUST RUN: Check video element visibility
const video = document.getElementById('generatedVideo');
const isVisible = video.style.display !== 'none';
console.log('Video visible:', isVisible); // MUST be true

// MUST RUN: Check for video errors
const hasError = video.error !== null;
console.log('Video error:', video.error?.message || 'No error'); // MUST be 'No error'

// MUST RUN: Verify video can play
const canPlay = video.canPlayType('video/mp4');
console.log('Can play MP4:', canPlay); // MUST be 'probably' or 'maybe'

// MUST RUN: Check for error messages in UI
const errorElement = document.querySelector('.alert-danger, .alert-warning');
const hasErrorMessage = errorElement?.textContent.includes('Video preview is not available');
console.log('Has error message:', hasErrorMessage); // MUST be false
```

### **Expected Test Flow**
1. Page loads with form visible
2. User fills prompt textarea
3. User clicks "Generate Video" button
4. Progress indicators show generation in progress
5. Video container appears when generation completes
6. **CRITICAL VALIDATION**: Video element is visible (not hidden)
7. **CRITICAL VALIDATION**: Video element has no error state
8. **CRITICAL VALIDATION**: No error messages in UI about video preview
9. **CRITICAL VALIDATION**: Video actually loads and plays in browser
10. Video element has valid source and plays successfully

### **Timeout Considerations**
- **Container startup**: 2-3 minutes
- **Health checks**: 30 seconds each
- **Video generation**: 1-3 minutes
- **Total test time**: 5-7 minutes

## 🚨 **Prerequisites**
- Docker and Docker Compose installed
- ✅ **Existing .env file with valid Azure OpenAI credentials** (no changes needed)
- Playwright browser automation available
- Sufficient system resources (8GB RAM recommended)

## 📊 **Success Metrics**
- **Container Health**: All 5 services healthy
- **Response Times**: Application responds within 2 seconds
- **Video Quality**: Generated video plays without errors
- **End-to-End Time**: Complete test completes within 7 minutes

## 🔍 **Troubleshooting Guide**

### **Common Issues**

**1. Container fails to start**
```bash
# Check logs
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs [service_name]

# Restart specific service
docker-compose -f src/deployment/docker/docker-compose.simple.yml restart [service_name]
```

**2. Health check failures**
```bash
# Check container health
docker-compose -f src/deployment/docker/docker-compose.simple.yml ps

# Check application health directly
curl -v http://localhost/health
```

**3. Video generation fails**
```bash
# Check worker logs
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs worker

# Check Redis connectivity
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec redis redis-cli ping
```

**4. CRITICAL: Video playback issues (PRODUCTION BLOCKER)**
```bash
# Check video file accessibility
curl -I http://localhost/video/[VIDEO_ID]

# Check worker FFmpeg processing logs
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs worker | grep -i ffmpeg

# Test video file format
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app ffprobe uploads/[VIDEO_ID]_video.mp4

# Browser console check (in Playwright/browser)
const video = document.getElementById('generatedVideo');
console.log('Video error:', video.error);
console.log('Video display:', video.style.display);
console.log('Can play type:', video.canPlayType('video/mp4'));
```

**Common Video Playback Symptoms (BLOCKING)**:
- Video element has `style="display: none"`
- Error message: "Video preview is not available"  
- Browser console: "DEMUXER_ERROR_NO_SUPPORTED_STREAMS"
- Download button shown instead of video player
- `video.error` property is not null

**5. Database connection issues**
```bash
# Check database logs
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs postgres

# Test database connection
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec postgres psql -U sora_user -d sora_production -c "SELECT 1"
```

### **Performance Issues**
- **High memory usage**: Reduce worker concurrency
- **Slow startup**: Increase health check intervals
- **Network timeouts**: Check Docker network configuration

### **Playwright Issues**
- **Element not found**: Check selector accuracy
- **Timeout errors**: Increase wait times for video generation
- **Browser crashes**: Ensure sufficient memory allocation

## 📝 **Test Results Reporting**

### **IMPORTANT: Do NOT modify this file with test results**
- This file is a **reusable template/manual**
- **Always report results in terminal/console only**
- Keep this file clean for repeated use by different team members

### **Test Results Template (for terminal reporting)**
```
=== DOCKER DEPLOYMENT TEST RESULTS ===
Date: [YYYY-MM-DD HH:MM]
Environment: [Development/Staging/Production]
Docker Version: [Version]
Test Duration: [X minutes]
Result: [PASS/FAIL]

=== CRITICAL VIDEO PLAYBACK VALIDATION ===
Video Element Visible: [✅ YES / ❌ NO - display: none]
Video Plays in Browser: [✅ YES / ❌ NO - download only]
Browser Video Errors: [✅ NONE / ❌ DEMUXER_ERROR/CODEC_ERROR]
Error Messages in UI: [✅ NONE / ❌ "Video preview is not available"]
Video Controls Functional: [✅ YES / ❌ NO]

=== INFRASTRUCTURE HEALTH ===
All 5 Containers Healthy: [✅ YES / ❌ NO]
Application Accessible: [✅ YES / ❌ NO]
Video Generation Completes: [✅ YES / ❌ NO]
Database Initialized: [✅ YES / ❌ NO]

=== ISSUES ===
Blocking Issues: [Any production-blocking problems]
Other Issues: [Any non-blocking issues]
Notes: [Additional observations]
===========================================
```

---

## 📋 **Quick Reference Commands**

### **Full Test Execution**
```bash
# 1. Clean environment
docker-compose -f src/deployment/docker/docker-compose.simple.yml down -v
docker system prune -f

# 2. Build and start
docker-compose -f src/deployment/docker/docker-compose.simple.yml build --no-cache
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# 3. Health checks
curl http://localhost/health
curl http://localhost/health/database
curl http://localhost/health/azure

# 4. Open browser and test UI
open http://localhost
```

### **Monitoring Commands**
```bash
# Container status
docker-compose -f src/deployment/docker/docker-compose.simple.yml ps

# Real-time logs
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs -f

# Resource usage
docker stats
```

This test plan can be executed repeatedly to validate deployment reliability and catch regressions.