# SORA Video Generation Prototype - Product Requirements Document

## 1. Executive Summary

This PRD outlines the development of a locally runnable prototype that validates real Azure OpenAI Sora API integration. The prototype transforms the current mock implementation into a working system that demonstrates the complete video generation workflow.

**Core Objectives:**
- **Real Azure Integration**: Replace mock implementation with actual Azure Sora API calls
- **End-to-End Workflow**: Prompt input → video generation → playback → download → cleanup
- **Local Development**: Runs entirely on local machine with SQLite database
- **Comprehensive Testing**: >90% test coverage with real API integration testing
- **Production Validation**: Proves the concept works before scaling infrastructure

## 2. Problem & Solution

### Problem Statement
**Current State (POC)**: Mock implementation that demonstrates UI/UX but doesn't prove Azure Sora actually works. Jobs are stored in memory and lost on restart. No real video generation occurs.

**Required State (Prototype)**: Working implementation that calls real Azure Sora API, persists job state in SQLite, handles actual video files, and proves the end-to-end workflow functions correctly.

### Solution Overview
A locally runnable Flask prototype that:
- **Real Azure Integration**: Actual Azure Sora API calls with authentication and error handling
- **SQLite Persistence**: Job state survives server restarts
- **File Management**: Downloads and serves real video files with automatic cleanup
- **Complete UI Workflow**: Prompt input → status polling → video playback → download
- **Comprehensive Testing**: Validates every component with real and mocked API calls

## 3. User Stories

### Epic: Video Generation Demo Interface

#### Story 1: Generate Video from Text Prompt
**As a** customer evaluating SORA capabilities  
**I want** to enter a text description and generate a video  
**So that** I can see the AI's video creation capabilities  

**Acceptance Criteria:**
- [ ] Web form accepts text prompts up to 500 characters
- [ ] Submit button triggers video generation process
- [ ] System validates prompt before API call
- [ ] Error handling for invalid prompts or API failures
- [ ] Generated video appears in 5-second duration

**Technical Notes:**
- Flask route handles form submission
- Azure SORA API integration with job polling
- Video stored temporarily on server

#### Story 2: Preview Generated Video
**As a** user who has generated a video  
**I want** to play the video directly in the browser  
**So that** I can immediately view the results  

**Acceptance Criteria:**
- [ ] Video player embedded in web interface
- [ ] Support for MP4 format playback
- [ ] Basic video controls (play, pause, volume)
- [ ] Responsive design for different screen sizes
- [ ] Video loads automatically when generation completes

#### Story 3: Download Generated Video
**As a** user satisfied with the generated video  
**I want** to download the video file  
**So that** I can use it for my own purposes  

**Acceptance Criteria:**
- [ ] Download button appears below video player
- [ ] File downloads with descriptive filename
- [ ] MP4 format maintained
- [ ] Download works across different browsers
- [ ] File cleanup after download or timeout

#### Story 4: Monitor Generation Progress
**As a** user waiting for video generation  
**I want** to see progress indicators  
**So that** I understand the system is working  

**Acceptance Criteria:**
- [ ] Progress spinner during API calls
- [ ] Status messages for different generation phases
- [ ] Estimated time remaining (if available)
- [ ] Error messages for failed generations
- [ ] Ability to cancel long-running requests

## 4. Technical Architecture

```mermaid
graph TB
    subgraph "Client Browser"
        A[Web Interface] --> B[HTML Form]
        A --> C[Video Player]
        A --> D[Download Button]
    end
    
    subgraph "Flask Application"
        E[Flask Server] --> F[Route Handlers]
        F --> G[Azure SORA Client]
        F --> H[File Management]
        F --> I[Progress Tracking]
    end
    
    subgraph "Azure Services"
        J[Azure OpenAI Service]
        K[SORA API Endpoint]
        J --> K
    end
    
    subgraph "Local Storage"
        L[Temporary Video Files]
        M[Static Assets]
    end
    
    B --> F
    C --> L
    D --> L
    G --> K
    H --> L
    I --> A
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style J fill:#e8f5e8
```

### System Components

#### Frontend Layer
- **HTML Templates**: Jinja2 templates for form and video display
- **CSS Styling**: Basic responsive design
- **JavaScript**: Progress updates and video player controls
- **Video Player**: HTML5 video element with controls

#### Backend Layer
- **Flask Application**: Web server and routing
- **Azure SORA Client**: API integration and authentication
- **Job Manager**: Polling and status tracking
- **File Handler**: Temporary storage and cleanup

#### Integration Layer
- **Azure OpenAI SDK**: Python client for SORA API
- **Authentication**: Azure credentials management
- **Error Handling**: Comprehensive error management

## 5. Azure OpenAI Sora API Specifications

### API Endpoints and Authentication
```python
# Endpoint Configuration
ENDPOINT = "{azure_resource_endpoint}/openai/v1/video/generations/jobs"
API_VERSION = "preview"  # Current Sora API version

# Authentication Methods (Ranked by Production Readiness)
# 1. Microsoft Entra ID (Recommended for Production)
from azure.identity import DefaultAzureCredential
credential = DefaultAzureCredential()
token = credential.get_token("https://cognitiveservices.azure.com/.default")
headers = {
    "Authorization": f"Bearer {token.token}",
    "Content-Type": "application/json"
}

# 2. API Key Authentication (Development/Testing)
headers = {
    "api-key": os.getenv("AZURE_OPENAI_API_KEY"),
    "Content-Type": "application/json"
}
```

### Video Generation Request Structure
```python
# Complete Request Payload
{
    "prompt": "A cat playing piano in a jazz bar",  # Required: 1-500 chars
    "model": "sora",                               # Required: Model deployment name
    "width": 480,                                  # Optional: 480-1920px
    "height": 480,                                 # Optional: 480-1920px  
    "n_seconds": 5                                 # Optional: 1-20 seconds
}

# HTTP Request Pattern with Error Handling
async def create_video_job(payload):
    async with aiohttp.ClientSession() as session:
        async with session.post(
            url=f"{ENDPOINT}?api-version={API_VERSION}",
            headers=headers,
            json=payload,
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            if response.status == 429:  # Rate limited
                retry_after = int(response.headers.get('retry-after', 60))
                raise RateLimitError(f"Rate limited. Retry after {retry_after}s")
            
            response.raise_for_status()
            return await response.json()
```

### Job Status Lifecycle and Polling
```python
# Job Status Values (Official Azure Documentation)
JOB_STATUSES = {
    "queued": "Job submitted and waiting in queue",
    "preprocessing": "Initial validation and preparation",
    "running": "Video generation in progress", 
    "processing": "Post-generation processing",
    "succeeded": "Video generated successfully",
    "failed": "Generation failed with error"
}

# Optimal Polling Pattern (Exponential Backoff)
async def poll_job_status(job_id):
    base_delay = 2  # Start with 2 second delay
    max_delay = 60  # Maximum 60 second delay
    max_attempts = 150  # 5 minutes total polling time
    
    for attempt in range(max_attempts):
        try:
            status_response = await get_job_status(job_id)
            
            if status_response["status"] in ["succeeded", "failed"]:
                return status_response
                
            # Exponential backoff with jitter
            delay = min(base_delay * (2 ** (attempt // 10)), max_delay)
            await asyncio.sleep(delay + random.uniform(0, 1))
            
        except RateLimitError as e:
            await asyncio.sleep(e.retry_after)
        except Exception as e:
            logger.error(f"Polling error: {e}")
            await asyncio.sleep(base_delay)
    
    raise TimeoutError("Job polling exceeded maximum time")
```

### Rate Limits and Quota Management
```python
# Azure OpenAI Sora Rate Limits (as of 2024)
RATE_LIMITS = {
    "sora_requests_per_minute": 60,      # Default Sora quota
    "concurrent_jobs": 10,               # Estimated concurrent limit
    "monthly_quota": 1000,               # Typical monthly allocation
    "max_video_duration": 20,            # Seconds
    "max_video_resolution": "1920x1920"  # Pixels
}

# Rate Limiting Implementation
import asyncio
from asyncio import Semaphore

class SoraRateLimiter:
    def __init__(self, requests_per_minute=60):
        self.semaphore = Semaphore(requests_per_minute)
        self.request_times = []
    
    async def acquire(self):
        async with self.semaphore:
            now = time.time()
            # Remove requests older than 1 minute
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            if len(self.request_times) >= 60:
                wait_time = 60 - (now - self.request_times[0])
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
            
            self.request_times.append(now)
```

### Complete Error Code Matrix
```python
# Azure OpenAI Sora Error Handling Matrix
ERROR_CODES = {
    # Authentication Errors
    401: {
        "name": "Unauthorized",
        "message": "Invalid API key or expired token",
        "retry": False,
        "action": "Check AZURE_OPENAI_API_KEY or refresh Entra ID token"
    },
    403: {
        "name": "Forbidden", 
        "message": "Quota exceeded or insufficient permissions",
        "retry": False,
        "action": "Check quota usage or assign 'Cognitive Services User' role"
    },
    
    # Rate Limiting
    429: {
        "name": "Too Many Requests",
        "message": "Rate limit exceeded",
        "retry": True,
        "action": "Implement exponential backoff, check retry-after header"
    },
    431: {
        "name": "Request Header Fields Too Large",
        "message": "Too many custom headers",
        "retry": False,
        "action": "Reduce custom header count"
    },
    
    # Server Errors
    500: {
        "name": "Internal Server Error",
        "message": "Azure service error",
        "retry": True,
        "action": "Retry with exponential backoff"
    },
    502: {
        "name": "Bad Gateway",
        "message": "Service temporarily unavailable",
        "retry": True,
        "action": "Retry after delay"
    },
    503: {
        "name": "Service Unavailable", 
        "message": "Azure OpenAI service overloaded",
        "retry": True,
        "action": "Retry with longer delays"
    },
    
    # Job-Specific Errors  
    "job_failed": {
        "name": "Video Generation Failed",
        "message": "Prompt processing or generation error",
        "retry": False,
        "action": "Validate prompt content and parameters"
    }
}

# Error Handler Implementation
async def handle_api_error(response, error_context="API call"):
    error_info = ERROR_CODES.get(response.status, {
        "name": "Unknown Error",
        "message": f"HTTP {response.status}",
        "retry": False,
        "action": "Contact support"
    })
    
    logger.error(f"{error_context} failed: {error_info['message']}")
    
    if error_info["retry"]:
        retry_after = int(response.headers.get('retry-after', 30))
        raise RetryableError(error_info["message"], retry_after)
    else:
        raise PermanentError(error_info["message"], error_info["action"])
```

### Enhanced Flask Routes with Production Patterns
```python
# Production-Ready Route Definitions
from flask import Blueprint, request, jsonify, send_file
from marshmallow import ValidationError
import asyncio

api_bp = Blueprint("sora_api", __name__)

@api_bp.route("/")
def index():
    """Main interface with health check capability."""
    return render_template("index.html")

@api_bp.route("/generate", methods=["POST"])
@rate_limit("60/minute")  # Client-side rate limiting
async def generate_video():
    """Create video generation job with comprehensive validation."""
    try:
        # Validate request data
        schema = VideoGenerationSchema()
        data = schema.load(request.form.to_dict())
        
        # Create job with database persistence
        async with get_db_session() as session:
            job = VideoJob(
                id=str(uuid.uuid4()),
                prompt=data["prompt"],
                status="queued",
                created_at=datetime.utcnow(),
                parameters=data
            )
            session.add(job)
            await session.commit()
            
            # Submit to Azure API with rate limiting
            sora_client = SoraClient()
            azure_response = await sora_client.create_video_job(data)
            
            # Update job with Azure job ID
            job.azure_job_id = azure_response["id"]
            await session.commit()
            
            # Start background polling task
            asyncio.create_task(poll_job_status_background(job.id))
            
            return jsonify({
                "success": True,
                "data": {
                    "job_id": job.id,
                    "status": job.status,
                    "estimated_completion": estimate_completion_time(data)
                }
            })
            
    except ValidationError as e:
        return jsonify({
            "success": False,
            "message": "Invalid request data",
            "errors": e.messages
        }), 400
        
    except RateLimitError as e:
        return jsonify({
            "success": False,
            "message": "Rate limit exceeded",
            "retry_after": e.retry_after
        }), 429
        
    except Exception as e:
        logger.exception("Video generation failed")
        return jsonify({
            "success": False,
            "message": "Internal server error",
            "error_id": str(uuid.uuid4())
        }), 500

@api_bp.route("/status/<job_id>")
async def get_job_status(job_id: str):
    """Get job status with real-time updates."""
    try:
        async with get_db_session() as session:
            job = await session.get(VideoJob, job_id)
            if not job:
                return jsonify({
                    "success": False,
                    "message": "Job not found"
                }), 404
            
            # Return cached status if recently updated
            if job.last_updated and (datetime.utcnow() - job.last_updated).seconds < 5:
                return jsonify({
                    "success": True,
                    "data": job.to_dict()
                })
            
            # Poll Azure API for latest status
            if job.status in ["queued", "running", "processing"]:
                sora_client = SoraClient()
                azure_status = await sora_client.get_job_status(job.azure_job_id)
                
                # Update job status
                job.status = azure_status["status"]
                job.last_updated = datetime.utcnow()
                
                if azure_status["status"] == "succeeded":
                    job.video_url = azure_status.get("video_url")
                    job.completed_at = datetime.utcnow()
                elif azure_status["status"] == "failed":
                    job.error_message = azure_status.get("error", "Unknown error")
                    job.completed_at = datetime.utcnow()
                
                await session.commit()
            
            return jsonify({
                "success": True,
                "data": job.to_dict()
            })
            
    except Exception as e:
        logger.exception(f"Status check failed for job {job_id}")
        return jsonify({
            "success": False,
            "message": "Status check failed"
        }), 500

@api_bp.route("/video/<job_id>")
async def serve_video(job_id: str):
    """Serve generated video with streaming support."""
    try:
        async with get_db_session() as session:
            job = await session.get(VideoJob, job_id)
            if not job or job.status != "succeeded":
                return jsonify({"error": "Video not available"}), 404
            
            # Download video if not cached locally
            if not job.local_file_path or not os.path.exists(job.local_file_path):
                file_handler = VideoFileHandler()
                local_path = await file_handler.download_video(
                    job.video_url, 
                    job_id
                )
                job.local_file_path = local_path
                await session.commit()
            
            return send_file(
                job.local_file_path,
                mimetype="video/mp4",
                as_attachment=False,
                conditional=True  # Enable range requests for streaming
            )
            
    except Exception as e:
        logger.exception(f"Video serving failed for job {job_id}")
        return jsonify({"error": "Video serving failed"}), 500

@api_bp.route("/health")
async def health_check():
    """Comprehensive health check endpoint."""
    health_status = {
        "service": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {}
    }
    
    try:
        # Database connectivity
        async with get_db_session() as session:
            await session.execute("SELECT 1")
        health_status["components"]["database"] = "healthy"
    except Exception as e:
        health_status["components"]["database"] = f"unhealthy: {str(e)}"
        health_status["service"] = "degraded"
    
    try:
        # Azure API connectivity
        sora_client = SoraClient()
        await sora_client.health_check()
        health_status["components"]["azure_api"] = "healthy"
    except Exception as e:
        health_status["components"]["azure_api"] = f"unhealthy: {str(e)}"
        health_status["service"] = "degraded"
    
    # Disk space check
    disk_usage = shutil.disk_usage(UPLOAD_FOLDER)
    free_gb = disk_usage.free / (1024**3)
    if free_gb < 1:  # Less than 1GB free
        health_status["components"]["disk_space"] = f"low: {free_gb:.1f}GB free"
        health_status["service"] = "degraded"
    else:
        health_status["components"]["disk_space"] = f"healthy: {free_gb:.1f}GB free"
    
    status_code = 200 if health_status["service"] == "healthy" else 503
    return jsonify(health_status), status_code
```

## 6. Enhanced Data Models

### Database Schema (SQLAlchemy ORM)
```python
from sqlalchemy import Column, String, Text, DateTime, Integer, Enum, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum

Base = declarative_base()

class JobStatus(enum.Enum):
    """Job status enumeration matching Azure API."""
    QUEUED = "queued"
    PREPROCESSING = "preprocessing" 
    RUNNING = "running"
    PROCESSING = "processing"
    SUCCEEDED = "succeeded"
    FAILED = "failed"

class VideoJobDB(Base):
    """SQLAlchemy model for video generation jobs."""
    __tablename__ = 'video_jobs'
    
    # Primary identifiers
    id = Column(String(36), primary_key=True)  # UUID4
    azure_job_id = Column(String(100), unique=True, nullable=True)
    
    # Job parameters
    prompt = Column(Text, nullable=False)
    width = Column(Integer, default=720)
    height = Column(Integer, default=720) 
    duration = Column(Integer, default=5)
    model = Column(String(50), default="sora")
    
    # Status tracking
    status = Column(Enum(JobStatus), nullable=False, default=JobStatus.QUEUED)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # Results and errors
    video_url = Column(String(500), nullable=True)  # Azure storage URL
    local_file_path = Column(String(255), nullable=True)  # Local cache path
    file_size = Column(Integer, nullable=True)  # Bytes
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    
    # Performance tracking
    estimated_completion = Column(DateTime, nullable=True)
    actual_duration = Column(Integer, nullable=True)  # Processing time in seconds
    
    # Cleanup tracking
    cleanup_scheduled = Column(Boolean, default=False)
    cleanup_at = Column(DateTime, nullable=True)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_status_created', 'status', 'created_at'),
        Index('idx_azure_job_id', 'azure_job_id'),
        Index('idx_cleanup_scheduled', 'cleanup_scheduled', 'cleanup_at'),
    )
```

### Pydantic Request/Response Models
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class VideoGenerationRequest(BaseModel):
    """Request model for video generation with comprehensive validation."""
    prompt: str = Field(..., min_length=1, max_length=500, description="Video description")
    width: int = Field(720, ge=480, le=1920, description="Video width in pixels")
    height: int = Field(720, ge=480, le=1920, description="Video height in pixels") 
    duration: int = Field(5, ge=1, le=20, description="Video duration in seconds")
    model: str = Field("sora", description="AI model name")
    
    @validator('prompt')
    def validate_prompt_content(cls, v):
        """Validate prompt for content policy compliance."""
        forbidden_terms = ['violence', 'explicit', 'nsfw']  # Expand as needed
        if any(term in v.lower() for term in forbidden_terms):
            raise ValueError("Prompt contains prohibited content")
        return v.strip()
    
    @validator('width', 'height')  
    def validate_dimensions(cls, v):
        """Ensure dimensions are multiples of 16 for video encoding."""
        if v % 16 != 0:
            raise ValueError("Dimensions must be multiples of 16")
        return v
    
    def to_azure_payload(self) -> Dict[str, Any]:
        """Convert to Azure API format."""
        return {
            "prompt": self.prompt,
            "width": self.width,
            "height": self.height,
            "n_seconds": self.duration,
            "model": self.model
        }

class VideoJobResponse(BaseModel):
    """Comprehensive response model for job operations."""
    job_id: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Request parameters (for reference)
    prompt: str
    width: int
    height: int
    duration: int
    
    # Progress and completion
    progress_percentage: Optional[int] = Field(None, ge=0, le=100)
    estimated_completion: Optional[datetime] = None
    actual_duration: Optional[int] = None
    
    # Results
    download_url: Optional[str] = None
    video_url: Optional[str] = None
    file_size: Optional[int] = None
    
    # Error handling
    error_message: Optional[str] = None
    retry_count: int = 0
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class HealthCheckResponse(BaseModel):
    """Health check response model."""
    service: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime
    components: Dict[str, str]
    version: Optional[str] = None
    uptime_seconds: Optional[int] = None

class ErrorResponse(BaseModel):
    """Standardized error response."""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    error_id: Optional[str] = None  # For tracking/debugging
    details: Optional[Dict[str, Any]] = None
    retry_after: Optional[int] = None  # For rate limiting
```

### SQLite Database Schema
```sql
-- Simple SQLite schema for local prototype
CREATE TABLE video_jobs (
    id TEXT PRIMARY KEY,
    azure_job_id TEXT UNIQUE,
    prompt TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'queued',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    video_url TEXT NULL,
    local_file_path TEXT NULL,
    file_size INTEGER NULL,
    error_message TEXT NULL,
    cleanup_at TIMESTAMP NULL
);

-- Basic indexes for performance
CREATE INDEX idx_status_created ON video_jobs(status, created_at);
CREATE INDEX idx_azure_job_id ON video_jobs(azure_job_id);
```

## 7. Implementation Phases

### Phase 1: Core Functionality (MVP)
- Flask application setup
- Basic HTML interface
- Azure SORA API integration
- Video generation and display
- File download capability

### Phase 2: Enhanced User Experience
- Progress indicators
- Better error handling
- Responsive design improvements
- Video thumbnail generation

### Phase 3: Production Readiness
- Security hardening
- Performance optimization
- Logging and monitoring
- Configuration management

## 8. Risks & Mitigations

### Technical Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Azure API Rate Limits | High | Medium | Implement request queuing, show estimated wait times |
| Video Generation Failures | Medium | Low | Comprehensive error handling, retry logic |
| Large File Handling | Medium | Medium | Implement streaming, file cleanup |
| Browser Compatibility | Low | Low | Test across major browsers, provide fallbacks |

### Business Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| API Cost Overruns | Medium | Low | Set generation limits, monitor usage |
| Poor Demo Experience | High | Low | Thorough testing, error graceful handling |
| Security Vulnerabilities | High | Low | Input validation, secure credential storage |

## 9. Success Metrics

### Functional Metrics
- **Video Generation Success Rate**: >95% of valid prompts generate videos
- **Average Response Time**: <60 seconds for video generation
- **Download Success Rate**: >99% of generated videos download successfully
- **Error Recovery**: All errors display helpful messages

### User Experience Metrics
- **Interface Usability**: Non-technical users can generate videos without assistance
- **Video Quality**: Generated videos meet demonstration standards
- **System Reliability**: Application runs continuously without crashes

### Technical Metrics
- **API Integration**: Successful authentication and request handling
- **File Management**: Proper cleanup of temporary files
- **Performance**: Application responds within 2 seconds for non-generation requests

## 10. Local Prototype Requirements

### Simple Dependencies (pyproject.toml)
```toml
[project]
dependencies = [
    # Core Framework
    "flask>=3.0.0",
    "pydantic>=2.0.0", 
    "python-dotenv>=1.0.0",
    
    # Azure Integration
    "azure-identity>=1.15.0",
    "openai>=1.3.0",
    "requests>=2.31.0",
    
    # Database (SQLite)
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    
    # File Handling
    "werkzeug>=3.0.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    
    # Linting & Type Checking
    "mypy>=1.7.0",
    "ruff>=0.1.6",
]
```

### Local Environment Configuration
```bash
# .env.example - Copy to .env and add your Azure credentials

# ===== AZURE OPENAI (Required) =====
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_DEPLOYMENT_NAME=sora

# ===== APPLICATION =====
SECRET_KEY=dev-secret-key-change-me
FLASK_ENV=development
HOST=127.0.0.1
PORT=5000

# ===== LOCAL STORAGE =====
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=104857600  # 100MB
CLEANUP_AFTER_HOURS=24

# ===== DATABASE (SQLite) =====
DATABASE_URL=sqlite:///sora_prototype.db
TEST_DATABASE_URL=sqlite:///:memory:
```

### System Requirements
- **Python**: 3.8+ (3.12 recommended)
- **Disk Space**: 5GB free (for video storage)
- **Azure OpenAI**: Resource with Sora model deployed
- **Internet**: For Azure API calls and video downloads

## 11. Simplified File Structure
```
sora-video-poc/
├── src/
│   ├── main.py           # Flask app entry point (existing)
│   ├── database/
│   │   ├── __init__.py
│   │   ├── models.py     # SQLAlchemy models
│   │   └── connection.py # SQLite connection
│   ├── features/sora_integration/
│   │   ├── client.py     # REAL Azure Sora client (replace mock)
│   │   ├── job_manager.py # Job lifecycle management
│   │   └── file_handler.py # Video download/cleanup
│   └── api/
│       └── routes.py     # Enhanced with database persistence
├── templates/            # Existing HTML templates
├── static/              # Existing CSS/JS
├── uploads/             # Local video storage
├── alembic/             # Database migrations
├── .env.example         # Environment template
└── sora_prototype.db    # SQLite database file
```

## 12. Prototype Development Workflow

### Setup Steps
```bash
# 1. Environment Setup
cp .env.example .env  # Add your Azure credentials
uv sync               # Install dependencies
uv run alembic upgrade head  # Create SQLite database

# 2. Start Development
uv run python src/main.py    # Start Flask app
# Open http://127.0.0.1:5000   # Test UI

# 3. Testing
uv run pytest --cov=src     # Run all tests (target >90% coverage)
uv run ruff check .          # Lint code
uv run mypy src/             # Type check
```

### Validation Workflow
1. **Real API Integration**: Submit test prompt → verify Azure API call → confirm job creation
2. **Status Polling**: Check job progresses through queued → running → succeeded states  
3. **Video Playback**: Download completes → video displays in browser → playback works
4. **File Download**: Download button works → MP4 file saved correctly
5. **Cleanup**: Old videos automatically deleted after 24 hours
6. **Error Handling**: Invalid prompts show proper error messages
7. **Persistence**: Restart server → jobs survive and continue from database

### Success Criteria ✅
- [ ] Real Azure Sora API integration working
- [ ] Complete end-to-end workflow functional  
- [ ] SQLite database persisting job state
- [ ] Video files downloadable and playable
- [ ] Automatic cleanup working
- [ ] Test coverage >90%
- [ ] All linting and type checks pass

---

**Simplified Focus**: This PRD now focuses on creating a working local prototype that validates the core Azure Sora integration before any production scaling.