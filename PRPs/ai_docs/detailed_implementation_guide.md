# Detailed Implementation Guide

## Essential Development Patterns

### Video Generation Workflow (3-Step Process)
```python
# Step 1: Submit job to Azure API
initial_job = client.create_video_job(prompt, ui_parameters)
generation_id = initial_job.generation_id

# Step 2: Update job status to running
_update_job_status(job_id, "running", {"generation_id": generation_id})

# Step 3: Poll Azure API until completion with exponential backoff
while True:
    current_job = client.poll_job_status(job_id, generation_id)
    if current_job.status == "succeeded":
        completion_data = {
            "file_path": current_job.file_path,
            "download_url": current_job.download_url,
            "completed_at": datetime.utcnow(),
        }
        _update_job_status(job_id, "succeeded", completion_data)
        break
    elif current_job.status == "failed":
        raise Exception(f"Azure API job failed: {current_job.error_message}")
    
    # Exponential backoff: 5s -> 7.5s -> 11.25s -> ... max 30s
    time.sleep(poll_interval)
    poll_interval = min(poll_interval * 1.5, max_poll_interval)
```

### Database Session Pattern
```python
# SQLAlchemy 2.x compatible
from sqlalchemy import text
with get_db_session() as session:
    job = session.query(VideoJobDB).filter_by(job_id=job_id).first()
    if job:
        job.status = VideoJobStatus.COMPLETED
        session.commit()
```

### Test Data Generation Pattern
```python
# Always use unique IDs in tests for stability
import uuid
job_id = str(uuid.uuid4())
generation_id = "gen-" + str(uuid.uuid4())
```

### Azure API Resolution Presets
```python
# Azure Sora API requires exact resolution specifications
resolution_presets = {
    'SD': {'width': 854, 'height': 480},      # Azure API requires exactly 854x480
    'HD': {'width': 1280, 'height': 720},     # 1280x720 supported
    'Full HD': {'width': 1920, 'height': 1080} # 1920x1080 supported
}
# Supported resolutions: (480,480), (854,480), (720,720), (1280,720), (1080,1080), (1920,1080)
```

## Factory Patterns

### Parameter Creation with Defaults
```python
# Parameter creation with defaults
params = GenerationParamsFactory.create_from_ui_request(
    prompt="user prompt",
    ui_parameters={'duration': 8, 'width': 1920, 'height': 1080}
)

# Configuration management
video_config = ConfigurationFactory.get_video_config()
```

### Multi-User Patterns
```python
# Session management (cryptographically secure)
session_id, session_data = get_or_create_session(client_ip)

# Queue-based job processing
task = process_video_generation.delay(session_id, job_id, job_data)

# WebSocket real-time updates
broadcast_job_status(job_id, {"status": "completed", "file_path": "/path/to/video.mp4"})
```

## Azure Integration Notes

- **Real Azure OpenAI integration** with HTTP calls and rate limiting (10 req/sec)
- Authentication supports both API keys and DefaultAzureCredential
- File uploads stored in `uploads/` directory with automatic cleanup
- Frontend uses Bootstrap with JavaScript polling for real-time updates
- Database persistence replaces in-memory job storage

## Database Integration Notes

- **SQLAlchemy ORM** with Flask-Migrate for schema management
- **PostgreSQL for production**, SQLite for development/testing
- **Connection pooling** with configurable pool sizes
- **Automatic migrations** with Alembic version control
- **Transaction management** with proper session handling