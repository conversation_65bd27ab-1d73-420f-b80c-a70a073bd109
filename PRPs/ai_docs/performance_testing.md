# Performance Testing Framework - Comprehensive Performance Validation

## Overview

Complete performance testing framework for the production-ready multi-user video generation system. This guide covers performance testing methodology, benchmarking infrastructure, load testing patterns, and performance optimization strategies across all system components.

## Performance Testing Infrastructure

### Performance Test Suite Structure

**36 comprehensive performance test methods** across 8 test categories:

```
src/tests/test_performance.py
├── TestMemoryUsagePerformance          # Memory profiling and leak detection
├── TestResponseTimeBenchmarks          # API response time validation
├── TestDatabasePerformance             # Database query optimization
├── TestConcurrentRequestHandling       # Multi-user load testing
├── TestResourceUtilizationMonitoring   # System resource monitoring
├── TestCachePerformance               # Cache efficiency validation
├── TestQueueProcessingPerformance     # Job queue throughput testing
└── TestPerformanceRegression          # Baseline regression detection
```

### Performance Configuration

```python
# Performance testing thresholds and limits
PERFORMANCE_CONFIG = {
    "memory_threshold_mb": 50,          # Maximum memory increase during tests
    "response_time_threshold_ms": 100,   # Maximum API response time
    "throughput_threshold_rps": 50,      # Minimum requests per second
    "concurrent_users": 20,              # Concurrent users for load testing
    "cache_hit_ratio_threshold": 0.8,   # Minimum cache hit ratio
    "queue_processing_threshold": 100,   # Minimum queue processing rate (jobs/min)
    "database_query_threshold_ms": 50,   # Maximum database query time
    "memory_leak_threshold_mb": 10,      # Memory leak detection threshold
    "sustained_load_duration_s": 30,     # Duration for sustained load tests
    "max_queue_depth": 5000,             # Maximum queue depth for testing
}
```

## Memory Performance Testing

### Memory Usage Monitoring
```python
# src/tests/test_performance.py
import tracemalloc
import psutil
import os
from typing import Dict, Any, List

class TestMemoryUsagePerformance:
    """Memory usage testing and leak detection."""
    
    def test_video_generation_memory_usage(self):
        """Test memory usage during video generation workflow."""
        # Start memory tracking
        tracemalloc.start()
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate video generation workflow
        for i in range(10):
            job_data = self._create_test_job_data(f"job_{i}")
            self._simulate_video_processing(job_data)
        
        # Measure memory increase
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Get detailed memory trace
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Performance assertions
        assert memory_increase < PERFORMANCE_CONFIG["memory_threshold_mb"]
        assert peak / 1024 / 1024 < 100  # Peak memory under 100MB
        
        print(f"Memory increase: {memory_increase:.2f}MB")
        print(f"Peak traced memory: {peak / 1024 / 1024:.2f}MB")
    
    def test_memory_leak_detection(self):
        """Detect memory leaks in repetitive operations."""
        tracemalloc.start()
        
        # Baseline measurement
        baseline_snapshots = []
        for _ in range(5):
            self._perform_memory_intensive_operation()
            snapshot = tracemalloc.take_snapshot()
            baseline_snapshots.append(snapshot)
        
        # Test phase - repeat operations
        test_snapshots = []
        for _ in range(20):
            self._perform_memory_intensive_operation()
            snapshot = tracemalloc.take_snapshot()
            test_snapshots.append(snapshot)
        
        # Compare memory usage patterns
        baseline_memory = sum(s.traced_memory[0] for s in baseline_snapshots[-5:]) / 5
        test_memory = sum(s.traced_memory[0] for s in test_snapshots[-5:]) / 5
        
        memory_growth = (test_memory - baseline_memory) / 1024 / 1024
        
        tracemalloc.stop()
        
        # Memory leak assertion
        assert memory_growth < PERFORMANCE_CONFIG["memory_leak_threshold_mb"]
        print(f"Memory growth over iterations: {memory_growth:.2f}MB")
    
    def test_large_dataset_memory_efficiency(self):
        """Test memory efficiency with large datasets."""
        import gc
        
        # Force garbage collection before test
        gc.collect()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Process large dataset
        large_dataset = self._generate_large_test_dataset(size_mb=50)
        processed_results = self._process_large_dataset(large_dataset)
        
        # Measure memory after processing
        peak_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_usage = peak_memory - initial_memory
        
        # Clean up and measure final memory
        del large_dataset, processed_results
        gc.collect()
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_cleanup = peak_memory - final_memory
        
        # Efficiency assertions
        assert memory_usage < 100  # Under 100MB for 50MB dataset
        assert memory_cleanup > memory_usage * 0.8  # 80% cleanup efficiency
        
        print(f"Memory usage: {memory_usage:.2f}MB")
        print(f"Memory cleanup: {memory_cleanup:.2f}MB")
```

### Memory Profiling Tools
```python
class PerformanceProfiler:
    """Advanced memory and performance profiling."""
    
    def __init__(self):
        self.memory_snapshots = []
        self.timing_data = {}
        self.metrics = {}
    
    def start_profiling(self):
        """Start comprehensive profiling."""
        tracemalloc.start(10)  # Track 10 stack frames
        self.start_time = time.time()
        self._take_memory_snapshot("start")
    
    def stop_profiling(self) -> Dict[str, Any]:
        """Stop profiling and return comprehensive metrics."""
        self._take_memory_snapshot("end")
        
        # Memory analysis
        if len(self.memory_snapshots) >= 2:
            start_snapshot = self.memory_snapshots[0][1]
            end_snapshot = self.memory_snapshots[-1][1]
            
            memory_growth = end_snapshot.traced_memory[0] - start_snapshot.traced_memory[0]
            peak_memory = max(s[1].traced_memory[1] for s in self.memory_snapshots)
        else:
            memory_growth = 0
            peak_memory = 0
        
        # Timing analysis
        total_duration = time.time() - self.start_time
        
        tracemalloc.stop()
        
        return {
            'memory_growth_mb': memory_growth / 1024 / 1024,
            'peak_memory_mb': peak_memory / 1024 / 1024,
            'total_duration_s': total_duration,
            'timing_data': self.timing_data,
            'custom_metrics': self.metrics
        }
    
    def _take_memory_snapshot(self, label: str):
        """Take labeled memory snapshot."""
        snapshot = tracemalloc.take_snapshot()
        self.memory_snapshots.append((label, snapshot))
```

## Response Time Benchmarks

### API Response Time Testing
```python
class TestResponseTimeBenchmarks:
    """API response time performance testing."""
    
    def test_api_response_times_under_load(self):
        """Test API response times under various load conditions."""
        endpoint_benchmarks = {}
        
        # Test different endpoints with increasing load
        endpoints = [
            ('GET', '/health'),
            ('POST', '/generate'),
            ('GET', '/status/test-job-123'),
            ('GET', '/queue/status'),
            ('GET', '/metrics')
        ]
        
        for method, endpoint in endpoints:
            response_times = []
            
            # Measure response times under load
            for concurrent_requests in [1, 5, 10, 20]:
                times = self._measure_concurrent_response_times(
                    method, endpoint, concurrent_requests, iterations=10
                )
                response_times.extend(times)
            
            # Calculate statistics
            avg_response_time = sum(response_times) / len(response_times)
            p95_response_time = sorted(response_times)[int(0.95 * len(response_times))]
            max_response_time = max(response_times)
            
            endpoint_benchmarks[f"{method} {endpoint}"] = {
                'avg_ms': avg_response_time,
                'p95_ms': p95_response_time,
                'max_ms': max_response_time
            }
            
            # Performance assertions
            assert avg_response_time < PERFORMANCE_CONFIG["response_time_threshold_ms"]
            assert p95_response_time < PERFORMANCE_CONFIG["response_time_threshold_ms"] * 2
        
        print("API Response Time Benchmarks:")
        for endpoint, stats in endpoint_benchmarks.items():
            print(f"  {endpoint}: avg={stats['avg_ms']:.1f}ms, p95={stats['p95_ms']:.1f}ms")
    
    def test_response_time_degradation_detection(self):
        """Detect response time degradation under increasing load."""
        baseline_times = []
        stress_times = []
        
        # Baseline measurements (low load)
        for _ in range(20):
            start_time = time.time()
            response = self._make_test_request('GET', '/health')
            response_time = (time.time() - start_time) * 1000
            baseline_times.append(response_time)
            time.sleep(0.1)  # Small delay between requests
        
        # Stress measurements (high load)
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for _ in range(50):
                future = executor.submit(self._measure_request_time, 'GET', '/health')
                futures.append(future)
            
            stress_times = [future.result() for future in futures]
        
        # Calculate performance degradation
        baseline_avg = sum(baseline_times) / len(baseline_times)
        stress_avg = sum(stress_times) / len(stress_times)
        degradation_factor = stress_avg / baseline_avg
        
        # Performance assertions
        assert degradation_factor < 3.0  # No more than 3x degradation
        assert stress_avg < PERFORMANCE_CONFIG["response_time_threshold_ms"] * 2
        
        print(f"Response time under load: {baseline_avg:.1f}ms → {stress_avg:.1f}ms")
        print(f"Degradation factor: {degradation_factor:.2f}x")
```

## Database Performance Testing

### Query Performance Optimization
```python
class TestDatabasePerformance:
    """Database performance and optimization testing."""
    
    def test_database_query_performance(self):
        """Test database query performance under various conditions."""
        from src.api.job_repository import JobRepository
        
        job_repository = JobRepository()
        query_benchmarks = {}
        
        # Prepare test data
        self._create_test_jobs(count=1000)
        
        # Test different query patterns
        query_tests = [
            ('get_job_by_id', lambda: job_repository.get_job_by_id('test-job-500')),
            ('get_jobs_by_session', lambda: job_repository.get_jobs_by_session('test-session', limit=50)),
            ('get_jobs_by_status', lambda: job_repository.get_jobs_by_status('completed', limit=100)),
            ('get_active_jobs', lambda: job_repository.get_active_jobs()),
            ('get_job_statistics', lambda: job_repository.get_job_statistics())
        ]
        
        for query_name, query_func in query_tests:
            response_times = []
            
            # Measure query performance
            for _ in range(50):
                start_time = time.time()
                result = query_func()
                query_time = (time.time() - start_time) * 1000
                response_times.append(query_time)
                
                # Verify query returns results
                assert result is not None
            
            # Calculate statistics
            avg_time = sum(response_times) / len(response_times)
            p95_time = sorted(response_times)[int(0.95 * len(response_times))]
            
            query_benchmarks[query_name] = {
                'avg_ms': avg_time,
                'p95_ms': p95_time
            }
            
            # Performance assertions
            assert avg_time < PERFORMANCE_CONFIG["database_query_threshold_ms"]
            assert p95_time < PERFORMANCE_CONFIG["database_query_threshold_ms"] * 2
        
        print("Database Query Performance:")
        for query, stats in query_benchmarks.items():
            print(f"  {query}: avg={stats['avg_ms']:.1f}ms, p95={stats['p95_ms']:.1f}ms")
    
    def test_connection_pool_performance(self):
        """Test database connection pool efficiency."""
        from src.database.connection import get_database_manager
        
        db_manager = get_database_manager()
        
        # Measure connection acquisition times
        acquisition_times = []
        
        def acquire_connection():
            start_time = time.time()
            with db_manager.get_session() as session:
                # Perform simple query
                session.execute(text("SELECT 1")).scalar()
            acquisition_time = (time.time() - start_time) * 1000
            return acquisition_time
        
        # Test concurrent connection acquisition
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(acquire_connection) for _ in range(100)]
            acquisition_times = [future.result() for future in futures]
        
        # Performance analysis
        avg_acquisition_time = sum(acquisition_times) / len(acquisition_times)
        max_acquisition_time = max(acquisition_times)
        
        # Pool efficiency assertions
        assert avg_acquisition_time < 50  # Under 50ms average
        assert max_acquisition_time < 200  # Under 200ms maximum
        
        print(f"Connection pool performance:")
        print(f"  Average acquisition: {avg_acquisition_time:.1f}ms")
        print(f"  Maximum acquisition: {max_acquisition_time:.1f}ms")
```

## Concurrent Load Testing

### Multi-User Simulation
```python
class TestConcurrentRequestHandling:
    """Multi-user concurrent request handling performance."""
    
    def test_concurrent_user_simulation(self):
        """Simulate multiple concurrent users."""
        concurrent_users = PERFORMANCE_CONFIG["concurrent_users"]
        requests_per_user = 10
        
        def simulate_user_workflow(user_id: int) -> Dict[str, Any]:
            """Simulate complete user workflow."""
            user_stats = {
                'user_id': user_id,
                'requests_completed': 0,
                'total_response_time': 0,
                'errors': 0
            }
            
            for request_num in range(requests_per_user):
                try:
                    start_time = time.time()
                    
                    # Simulate user actions
                    self._simulate_user_request(user_id, request_num)
                    
                    response_time = (time.time() - start_time) * 1000
                    user_stats['requests_completed'] += 1
                    user_stats['total_response_time'] += response_time
                    
                    # Small delay between requests
                    time.sleep(random.uniform(0.1, 0.5))
                    
                except Exception as e:
                    user_stats['errors'] += 1
                    print(f"User {user_id} error: {e}")
            
            return user_stats
        
        # Execute concurrent user simulation
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [
                executor.submit(simulate_user_workflow, user_id)
                for user_id in range(concurrent_users)
            ]
            
            user_results = [future.result() for future in futures]
        
        total_duration = time.time() - start_time
        
        # Analyze results
        total_requests = sum(stats['requests_completed'] for stats in user_results)
        total_errors = sum(stats['errors'] for stats in user_results)
        avg_response_time = sum(stats['total_response_time'] for stats in user_results) / total_requests
        
        requests_per_second = total_requests / total_duration
        error_rate = (total_errors / (total_requests + total_errors)) * 100
        
        # Performance assertions
        assert requests_per_second > PERFORMANCE_CONFIG["throughput_threshold_rps"]
        assert error_rate < 5.0  # Less than 5% error rate
        assert avg_response_time < PERFORMANCE_CONFIG["response_time_threshold_ms"] * 2
        
        print(f"Concurrent load test results:")
        print(f"  Users: {concurrent_users}")
        print(f"  Requests/second: {requests_per_second:.1f}")
        print(f"  Average response time: {avg_response_time:.1f}ms")
        print(f"  Error rate: {error_rate:.2f}%")
    
    def test_sustained_load_performance(self):
        """Test performance under sustained load."""
        duration = PERFORMANCE_CONFIG["sustained_load_duration_s"]
        target_rps = 30
        
        request_times = []
        error_count = 0
        start_time = time.time()
        
        def make_sustained_requests():
            nonlocal error_count
            while time.time() - start_time < duration:
                try:
                    request_start = time.time()
                    self._make_test_request('GET', '/health')
                    request_time = (time.time() - request_start) * 1000
                    request_times.append(request_time)
                    
                    # Control request rate
                    time.sleep(1.0 / target_rps)
                    
                except Exception:
                    error_count += 1
        
        # Run sustained load test
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_sustained_requests) for _ in range(5)]
            for future in futures:
                future.result()
        
        # Analyze sustained performance
        actual_rps = len(request_times) / duration
        avg_response_time = sum(request_times) / len(request_times) if request_times else 0
        error_rate = (error_count / (len(request_times) + error_count)) * 100 if request_times else 100
        
        # Sustained performance assertions
        assert actual_rps > target_rps * 0.8  # Within 80% of target
        assert avg_response_time < PERFORMANCE_CONFIG["response_time_threshold_ms"] * 1.5
        assert error_rate < 2.0  # Less than 2% error rate under sustained load
        
        print(f"Sustained load performance:")
        print(f"  Duration: {duration}s")
        print(f"  Actual RPS: {actual_rps:.1f}")
        print(f"  Average response time: {avg_response_time:.1f}ms")
        print(f"  Error rate: {error_rate:.2f}%")
```

## Queue Processing Performance

### Job Queue Throughput Testing
```python
class TestQueueProcessingPerformance:
    """Job queue processing performance and scalability."""
    
    def test_queue_throughput_measurement(self):
        """Measure job queue processing throughput."""
        from src.job_queue.tasks import process_video_generation
        
        job_count = 100
        jobs_submitted = []
        
        # Submit jobs to queue
        start_submission = time.time()
        
        for i in range(job_count):
            job_data = {
                'session_id': f'perf_test_session_{i % 10}',
                'job_id': f'perf_test_job_{i}',
                'prompt': f'Performance test video {i}',
                'params': {'duration': 5}
            }
            
            # Submit to Celery queue (mocked for testing)
            task = process_video_generation.delay(**job_data)
            jobs_submitted.append((task, time.time()))
        
        submission_duration = time.time() - start_submission
        submission_rate = job_count / submission_duration
        
        # Monitor job completion (mock implementation)
        completed_jobs = 0
        start_processing = time.time()
        
        # Simulate job processing monitoring
        while completed_jobs < job_count and (time.time() - start_processing) < 300:
            # Check job status (mocked)
            newly_completed = self._check_completed_jobs(jobs_submitted, completed_jobs)
            completed_jobs += newly_completed
            time.sleep(1)
        
        processing_duration = time.time() - start_processing
        processing_rate = completed_jobs / processing_duration
        
        # Queue performance assertions
        assert submission_rate > 50  # Submit 50+ jobs per second
        assert processing_rate > PERFORMANCE_CONFIG["queue_processing_threshold"] / 60  # Jobs per second
        assert completed_jobs >= job_count * 0.95  # 95% completion rate
        
        print(f"Queue throughput performance:")
        print(f"  Submission rate: {submission_rate:.1f} jobs/s")
        print(f"  Processing rate: {processing_rate:.1f} jobs/s")
        print(f"  Completion rate: {(completed_jobs/job_count)*100:.1f}%")
    
    def test_queue_scalability_under_load(self):
        """Test queue performance under high load."""
        max_queue_depth = PERFORMANCE_CONFIG["max_queue_depth"]
        
        # Fill queue to test capacity
        start_time = time.time()
        jobs_submitted = 0
        
        for batch_size in [100, 500, 1000, 2500, 5000]:
            if jobs_submitted >= max_queue_depth:
                break
                
            batch_start = time.time()
            
            # Submit batch of jobs
            for i in range(min(batch_size, max_queue_depth - jobs_submitted)):
                job_data = {
                    'session_id': f'scale_test_{i % 50}',
                    'job_id': f'scale_job_{jobs_submitted + i}',
                    'prompt': 'Scale test video',
                    'params': {'duration': 3}
                }
                
                # Mock job submission
                self._submit_mock_job(job_data)
                jobs_submitted += 1
            
            batch_duration = time.time() - batch_start
            batch_rate = batch_size / batch_duration
            
            # Monitor queue health during load
            queue_stats = self._get_queue_statistics()
            
            print(f"Batch {batch_size}: {batch_rate:.1f} jobs/s, queue depth: {queue_stats['depth']}")
            
            # Ensure queue remains responsive
            assert batch_rate > 20  # Maintain minimum submission rate
            assert queue_stats['depth'] <= max_queue_depth
            assert queue_stats['status'] == 'healthy'
        
        total_duration = time.time() - start_time
        overall_rate = jobs_submitted / total_duration
        
        print(f"Queue scalability results:")
        print(f"  Total jobs submitted: {jobs_submitted}")
        print(f"  Overall submission rate: {overall_rate:.1f} jobs/s")
        print(f"  Maximum queue depth reached: {max_queue_depth}")
```

## Resource Utilization Monitoring

### System Resource Testing
```python
class TestResourceUtilizationMonitoring:
    """System resource utilization under load."""
    
    def test_cpu_utilization_under_load(self):
        """Monitor CPU utilization during intensive operations."""
        import psutil
        
        cpu_measurements = []
        
        def monitor_cpu():
            """Background CPU monitoring."""
            while self.monitoring_active:
                cpu_percent = psutil.cpu_percent(interval=1)
                cpu_measurements.append(cpu_percent)
                time.sleep(1)
        
        # Start CPU monitoring
        self.monitoring_active = True
        monitor_thread = threading.Thread(target=monitor_cpu, daemon=True)
        monitor_thread.start()
        
        # Perform CPU-intensive operations
        try:
            self._perform_cpu_intensive_workload(duration=30)
        finally:
            self.monitoring_active = False
            monitor_thread.join(timeout=5)
        
        # Analyze CPU utilization
        avg_cpu = sum(cpu_measurements) / len(cpu_measurements)
        max_cpu = max(cpu_measurements)
        
        # CPU performance assertions
        assert avg_cpu < 80  # Average CPU under 80%
        assert max_cpu < 95  # Peak CPU under 95%
        
        print(f"CPU utilization: avg={avg_cpu:.1f}%, max={max_cpu:.1f}%")
    
    def test_memory_utilization_optimization(self):
        """Test memory utilization patterns and optimization."""
        import psutil
        
        # Baseline memory measurement
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024
        
        # Perform memory-intensive operations
        memory_measurements = []
        
        for iteration in range(20):
            # Simulate memory usage
            self._perform_memory_intensive_operation()
            
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_measurements.append(current_memory)
            
            # Check for memory leaks
            memory_growth = current_memory - baseline_memory
            if iteration > 10:  # After warmup
                assert memory_growth < 200  # Under 200MB growth
        
        # Memory utilization analysis
        avg_memory = sum(memory_measurements) / len(memory_measurements)
        peak_memory = max(memory_measurements)
        memory_variance = max(memory_measurements) - min(memory_measurements)
        
        print(f"Memory utilization: avg={avg_memory:.1f}MB, peak={peak_memory:.1f}MB")
        print(f"Memory variance: {memory_variance:.1f}MB")
        
        # Memory efficiency assertions
        assert memory_variance < 100  # Stable memory usage
        assert peak_memory < baseline_memory + 300  # Reasonable peak
    
    def test_disk_io_performance(self):
        """Test disk I/O performance and optimization."""
        import tempfile
        
        # Create test files for I/O testing
        test_data_sizes = [1, 10, 50, 100]  # MB
        io_performance = {}
        
        for size_mb in test_data_sizes:
            # Write performance test
            write_times = []
            read_times = []
            
            for _ in range(5):
                test_data = b'0' * (size_mb * 1024 * 1024)
                
                # Measure write performance
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    start_time = time.time()
                    temp_file.write(test_data)
                    temp_file.flush()
                    os.fsync(temp_file.fileno())
                    write_time = (time.time() - start_time) * 1000
                    write_times.append(write_time)
                    
                    temp_filename = temp_file.name
                
                # Measure read performance
                start_time = time.time()
                with open(temp_filename, 'rb') as f:
                    read_data = f.read()
                read_time = (time.time() - start_time) * 1000
                read_times.append(read_time)
                
                # Cleanup
                os.unlink(temp_filename)
            
            # Calculate I/O statistics
            avg_write_time = sum(write_times) / len(write_times)
            avg_read_time = sum(read_times) / len(read_times)
            
            write_speed_mbps = size_mb / (avg_write_time / 1000)
            read_speed_mbps = size_mb / (avg_read_time / 1000)
            
            io_performance[f"{size_mb}MB"] = {
                'write_speed_mbps': write_speed_mbps,
                'read_speed_mbps': read_speed_mbps
            }
            
            # I/O performance assertions
            assert write_speed_mbps > 10  # Minimum 10 MB/s write
            assert read_speed_mbps > 50   # Minimum 50 MB/s read
        
        print("Disk I/O Performance:")
        for size, stats in io_performance.items():
            print(f"  {size}: write={stats['write_speed_mbps']:.1f}MB/s, read={stats['read_speed_mbps']:.1f}MB/s")
```

## Performance Regression Detection

### Baseline Performance Validation
```python
class TestPerformanceRegression:
    """Performance regression detection and baseline validation."""
    
    def test_performance_baseline_validation(self):
        """Validate performance against established baselines."""
        # Load performance baselines (from config or previous runs)
        baselines = self._load_performance_baselines()
        
        current_metrics = {}
        
        # Memory performance baseline
        memory_result = self._measure_memory_performance()
        current_metrics['memory_usage_mb'] = memory_result['peak_memory_mb']
        
        # Response time baseline
        response_result = self._measure_response_time_performance()
        current_metrics['avg_response_time_ms'] = response_result['avg_response_time']
        
        # Database performance baseline
        db_result = self._measure_database_performance()
        current_metrics['db_query_time_ms'] = db_result['avg_query_time']
        
        # Queue processing baseline
        queue_result = self._measure_queue_performance()
        current_metrics['queue_throughput_jps'] = queue_result['jobs_per_second']
        
        # Compare against baselines
        regressions = []
        improvements = []
        
        for metric, current_value in current_metrics.items():
            if metric in baselines:
                baseline_value = baselines[metric]
                change_percent = ((current_value - baseline_value) / baseline_value) * 100
                
                if change_percent > 10:  # 10% regression threshold
                    regressions.append({
                        'metric': metric,
                        'baseline': baseline_value,
                        'current': current_value,
                        'regression_percent': change_percent
                    })
                elif change_percent < -5:  # 5% improvement threshold
                    improvements.append({
                        'metric': metric,
                        'baseline': baseline_value,
                        'current': current_value,
                        'improvement_percent': -change_percent
                    })
        
        # Save current metrics as new baseline candidate
        self._save_performance_metrics(current_metrics)
        
        # Report results
        if improvements:
            print("Performance Improvements:")
            for improvement in improvements:
                print(f"  {improvement['metric']}: {improvement['improvement_percent']:.1f}% improvement")
        
        if regressions:
            print("Performance Regressions:")
            for regression in regressions:
                print(f"  {regression['metric']}: {regression['regression_percent']:.1f}% regression")
        
        # Fail test if significant regressions detected
        critical_regressions = [r for r in regressions if r['regression_percent'] > 25]
        assert not critical_regressions, f"Critical performance regressions detected: {critical_regressions}"
    
    def test_automated_regression_detection(self):
        """Automated detection of performance regressions."""
        regression_tests = [
            ('memory_leak_detection', self._detect_memory_leaks),
            ('response_time_degradation', self._detect_response_degradation),
            ('database_slowdown', self._detect_database_slowdown),
            ('queue_bottleneck', self._detect_queue_bottlenecks)
        ]
        
        detected_issues = []
        
        for test_name, test_function in regression_tests:
            try:
                result = test_function()
                if result['has_regression']:
                    detected_issues.append({
                        'test': test_name,
                        'severity': result['severity'],
                        'details': result['details']
                    })
            except Exception as e:
                detected_issues.append({
                    'test': test_name,
                    'severity': 'error',
                    'details': f"Test failed: {str(e)}"
                })
        
        # Report detected issues
        if detected_issues:
            print("Automated Regression Detection Results:")
            for issue in detected_issues:
                print(f"  {issue['test']}: {issue['severity']} - {issue['details']}")
        
        # Fail if critical regressions found
        critical_issues = [i for i in detected_issues if i['severity'] in ['critical', 'error']]
        assert not critical_issues, f"Critical performance issues detected: {len(critical_issues)} issues"
```

## Performance Testing Utilities

### Context Managers and Helpers
```python
@contextmanager
def performance_monitor(profiler: PerformanceProfiler, test_name: str):
    """Context manager for performance monitoring."""
    profiler.start_profiling()
    
    try:
        yield profiler
    finally:
        metrics = profiler.stop_profiling()
        print(f"Performance metrics for {test_name}:")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")

def measure_execution_time(func):
    """Decorator to measure function execution time."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = (time.time() - start_time) * 1000
        print(f"{func.__name__} execution time: {execution_time:.2f}ms")
        return result
    return wrapper

class PerformanceAssertion:
    """Custom assertion class for performance testing."""
    
    @staticmethod
    def assert_response_time(actual_ms: float, max_ms: float, context: str = ""):
        """Assert response time is within acceptable limits."""
        assert actual_ms <= max_ms, f"Response time {actual_ms:.1f}ms exceeds limit {max_ms}ms {context}"
    
    @staticmethod
    def assert_throughput(actual_rps: float, min_rps: float, context: str = ""):
        """Assert throughput meets minimum requirements."""
        assert actual_rps >= min_rps, f"Throughput {actual_rps:.1f} RPS below minimum {min_rps} RPS {context}"
    
    @staticmethod
    def assert_memory_usage(actual_mb: float, max_mb: float, context: str = ""):
        """Assert memory usage is within limits."""
        assert actual_mb <= max_mb, f"Memory usage {actual_mb:.1f}MB exceeds limit {max_mb}MB {context}"
```

## Running Performance Tests

### Test Execution Commands
```bash
# Run all performance tests
uv run pytest src/tests/test_performance.py -v

# Run specific test categories
uv run pytest::TestMemoryUsagePerformance -v
uv run pytest::TestResponseTimeBenchmarks -v
uv run pytest::TestDatabasePerformance -v
uv run pytest::TestConcurrentRequestHandling -v

# Run performance tests with extended timeout
uv run pytest src/tests/test_performance.py --timeout=600

# Run performance tests with markers
uv run pytest -m "performance" -v
uv run pytest -m "performance and not slow" -v

# Generate performance report
uv run pytest src/tests/test_performance.py --html=performance_report.html

# Run memory profiling tests
uv run pytest::TestMemoryUsagePerformance::test_memory_leak_detection -v -s

# Run load testing
uv run pytest::TestConcurrentRequestHandling::test_concurrent_user_simulation -v -s
```

### CI/CD Integration
```yaml
# .github/workflows/performance.yml
name: Performance Testing

on: [push, pull_request]

jobs:
  performance:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install uv
        uv sync
    
    - name: Run performance tests
      run: |
        uv run pytest src/tests/test_performance.py \
          --html=performance_report.html \
          --timeout=600 \
          -v
    
    - name: Upload performance report
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance_report.html
    
    - name: Performance regression check
      run: |
        uv run pytest src/tests/test_performance.py::TestPerformanceRegression \
          --tb=short
```

## Performance Optimization Guidelines

### Memory Optimization
1. **Profile Memory Usage**: Use tracemalloc and psutil for detailed memory analysis
2. **Detect Memory Leaks**: Implement continuous monitoring for memory growth
3. **Optimize Data Structures**: Use efficient data structures for large datasets
4. **Garbage Collection**: Monitor and optimize garbage collection patterns
5. **Memory Pooling**: Implement object pooling for frequently created objects

### Response Time Optimization
1. **Identify Bottlenecks**: Profile all API endpoints under various loads
2. **Database Optimization**: Optimize queries and connection pooling
3. **Caching Strategies**: Implement appropriate caching layers
4. **Async Processing**: Use background tasks for heavy operations
5. **Load Balancing**: Distribute load across multiple instances

### Scalability Testing
1. **Concurrent Users**: Test with realistic concurrent user loads
2. **Queue Scalability**: Validate queue processing under high loads
3. **Resource Limits**: Test system behavior at resource limits
4. **Graceful Degradation**: Ensure system degrades gracefully under stress
5. **Auto-scaling**: Test auto-scaling mechanisms if implemented

### Monitoring and Alerting
1. **Baseline Metrics**: Establish and maintain performance baselines
2. **Regression Detection**: Implement automated regression detection
3. **Real-time Monitoring**: Monitor performance in production
4. **Alert Thresholds**: Set appropriate alerting thresholds
5. **Performance Dashboards**: Create comprehensive performance dashboards

## Best Practices Summary

1. **Comprehensive Coverage**: Test all system components under load
2. **Realistic Scenarios**: Use realistic user workflows and data
3. **Automated Testing**: Integrate performance tests into CI/CD pipeline
4. **Baseline Management**: Maintain and update performance baselines
5. **Early Detection**: Catch performance regressions early in development
6. **Resource Monitoring**: Monitor all system resources (CPU, memory, disk, network)
7. **Load Patterns**: Test various load patterns (burst, sustained, gradual)
8. **Error Handling**: Ensure error handling doesn't impact performance
9. **Documentation**: Document all performance requirements and test results
10. **Continuous Improvement**: Regularly review and improve performance tests

This comprehensive performance testing framework ensures the multi-user video generation system maintains optimal performance under all conditions and scales effectively with increasing load.