# API Endpoints Reference Guide

## Core Video Generation
- `GET /` - Main web interface with advanced parameter controls
- `POST /generate` - Create video generation job (supports optional parameters: duration, width, height, model)
- `GET /status/<job_id>` - Poll job status and progress
- `GET /video/<job_id>` - Stream video file
- `GET /download/<job_id>` - Download video file
- `GET /config` - Get UI configuration constraints and defaults for parameter validation

## Health Monitoring & Metrics
- `GET /health` - Overall system health status (database, Azure API, disk space, job queue)
- `GET /health/database` - Database connectivity and performance
- `GET /health/azure` - Azure OpenAI API connectivity
- `GET /metrics` - Comprehensive system metrics (job completion rates, API performance, errors)
- `GET /metrics/jobs` - Job-specific metrics and queue health

## Multi-User Management (NEW)
- `GET /queue/status` - Current queue status for user's session
- `GET /queue/stats` - Overall queue statistics and performance
- `GET /session/info` - Current session information and limits

## Debug Endpoints (Development Only)
- `GET /debug/azure-config` - **⚠️ SECURITY CRITICAL**: Exposes Azure OpenAI configuration including API keys

**🚨 PRODUCTION SECURITY WARNING 🚨**
- **Risk**: Debug endpoint exposes Azure OpenAI API keys and sensitive configuration
- **Development**: Safe to use locally for debugging API configuration issues
- **Production**: **MUST BE REMOVED** before any production deployment
- **Detection**: Check `src/api/routes.py` for `/debug/` routes before deployment
- **Impact**: Complete compromise of Azure OpenAI resources if exposed in production

## Development Commands

### Multi-Service Startup (Production-Ready)
```bash
# Terminal 1: Start Redis server (required for queue and rate limiting)
redis-server                                        # Or: docker run -p 6379:6379 redis:alpine

# Terminal 2: Start Celery worker (required for background processing)
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4

# Terminal 3: Start Flask application (with WebSocket support)
uv run python src/main.py

# Optional: Start Celery monitoring
uv run celery -A src.job_queue.celery_app flower        # Web UI at http://localhost:5555
```

### Database Management
```bash
uv run flask --app src.main:create_app db init       # Initialize migrations (one-time)
uv run flask --app src.main:create_app db migrate -m "Description"  # Create new migration
uv run flask --app src.main:create_app db upgrade    # Apply migrations
uv run flask --app src.main:create_app db downgrade  # Rollback migrations
uv run flask --app src.main:create_app db current    # Show current migration
```

### Code Quality
```bash
uv run ruff format .                                # Format code
uv run ruff check .                                 # Run linter
uv run mypy src/                                    # Type checking

# Development workflow (run before committing)
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest
```

### Health and Monitoring
```bash
curl http://localhost:5001/health                   # Check overall health
curl http://localhost:5001/health/database          # Check database health
curl http://localhost:5001/health/azure            # Check Azure API health
curl http://localhost:5001/metrics                 # Get system metrics

# Multi-user endpoints
curl http://localhost:5001/queue/status             # Get user queue status
curl http://localhost:5001/queue/stats              # Get overall queue statistics
curl http://localhost:5001/session/info             # Get session information

# Security validation (run before production deployment)
grep -r "/debug/" src/api/routes.py                     # Check for debug endpoints
```