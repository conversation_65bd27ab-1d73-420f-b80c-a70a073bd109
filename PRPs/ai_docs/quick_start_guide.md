# Quick Start Guide

## ✅ **ZERO-CONFIGURATION DOCKER DEPLOYMENT** (Recommended)

**Get started in 2 commands - no manual setup required!**

```bash
# 1. Clone repository
git clone <repository-url>
cd sora-poc

# 2. Start simple deployment (5 containers)
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# 3. That's it! 🎉
# - Access application at http://localhost
# - Database tables created automatically
# - Video generation working immediately
```

**Add your Azure OpenAI credentials:**
```bash
# Set environment variables
export AZURE_OPENAI_API_KEY="your_api_key_here"
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
export DB_PASSWORD="sora_secure_password_2024"
```

## 🔧 Manual Setup (Advanced)

### First-Time Setup
1. **Environment**: `uv venv && source .venv/bin/activate && uv sync`
2. **Services**: Start Redis → Celery worker → Flask app (3 terminals)
3. **Database**: ✅ **AUTOMATIC** - Database tables created automatically via Docker entrypoint
4. **Config**: Create `.env` with Azure OpenAI keys

## 🚀 Development Workflow
1. **Code**: Use Pydantic v2 models, type hints, Google docstrings
2. **Test**: Co-locate tests, use `uuid.uuid4()` for unique IDs
3. **Quality**: `uv run ruff format . && uv run ruff check . && uv run mypy src/`
4. **Deploy**: Remove debug endpoints, validate security

## 🛠️ Essential Commands
```bash
# Multi-Service Startup (RECOMMENDED - with environment conflict resolution)
redis-server                                        # Terminal 1
./scripts/start_celery_clean.sh                     # Terminal 2 - Automatic environment cleanup
uv run python src/main.py                          # Terminal 3

# Manual startup (if needed)
redis-server                                        # Terminal 1
export -n AZURE_OPENAI_API_VERSION && uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4  # Terminal 2
uv run python src/main.py                          # Terminal 3

# Environment validation (run anytime to check for conflicts)
uv run python scripts/check_environment.py

# Development Quality
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest

# Production Testing
python3 run_all_production_tests.py                # Complete validation
```