# Troubleshooting & Common Issues

## ✅ System Status (As of 2025-07-16)
- **Video Generation**: ✅ Working - Azure OpenAI Sora API integration functional
- **Background Processing**: ✅ Working - Celery workers processing jobs successfully
- **File Download**: ✅ Working - Videos downloaded and stored locally
- **UI Video Display**: ✅ **FIXED** - Videos now appear correctly in web interface
- **Database Initialization**: ✅ **AUTOMATIC** - Tables created automatically on container startup
- **Complete Workflow**: ✅ Working - End-to-end generation and display functional
- **Team Collaboration**: ✅ **ZERO-CONFIG** - New developers can start with single command

## Common Issues & Solutions

### 1. Database Tables Not Found (500 Errors)
**Status**: ✅ **PERMANENTLY FIXED** (2025-07-16)
```bash
# Symptoms: 500 errors when submitting video generation requests
# Error: "relation 'video_jobs' does not exist"
# Root Cause: Database tables weren't created on fresh deployments
# Solution: Automatic database initialization via entrypoint.sh

# ✅ PERMANENT FIX APPLIED:
# - Added src/deployment/docker/entrypoint.sh script
# - Runs 'flask db upgrade' automatically on container startup
# - Works for all deployment types (simple, development, production)
# - No manual database setup required for new developers

# Verification:
docker logs sora-app-simple  # Should show "Database migrations completed successfully!"
```

### 2. Frontend Real-Time Architecture Gap
**Status**: 📋 **DOCUMENTED** (2025-07-12)
```bash
# Current Implementation: HTTP Polling (3-second intervals)
# Architecture Gap: Complete WebSocket infrastructure exists but unused by frontend
# Location: src/realtime/ (backend) vs static/js/app.js (polling frontend)
# Impact: Higher latency, increased server load, missed real-time benefits
# Future Enhancement: Integrate Socket.IO client for true real-time updates
```

### 3. Videos Generated But Not Displayed in UI
**Status**: ✅ **FIXED** (2025-07-11)
```bash
# Symptoms: Video generation succeeds, but UI shows black player
# Root Cause: Status mismatch and URL routing issues
# Solution: Applied in src/api/job_routes.py, src/api/models.py, static/js/app.js
# Verification: curl http://localhost:5001/status/job-id should return video_url field
```

### 4. Environment Variable Conflicts
**Status**: ✅ **RESOLVED** with automated scripts
```bash
# Symptoms: Celery workers using wrong API version
# Solution: Use clean startup script
./scripts/start_celery_clean.sh

# Manual fix:
export -n AZURE_OPENAI_API_VERSION
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4
```

### 5. Configuration Issues
```bash
# Check environment setup
uv run python scripts/check_environment.py

# Verify Azure API connectivity
curl -H "api-key: $AZURE_OPENAI_API_KEY" "$AZURE_OPENAI_ENDPOINT/health"

# Test complete system health
curl http://localhost:5001/health
```

### 5. Video File Access Issues
```bash
# Check video files exist
ls -la uploads/*.mp4

# Test video streaming endpoint
curl -I http://localhost:5001/video/job-id

# Verify HTTP range support (needed for video streaming)
curl -H "Range: bytes=0-1023" http://localhost:5001/video/job-id -I
```

## Debug Commands
```bash
# Development startup (3 terminals)
redis-server                                        # Terminal 1
./scripts/start_celery_clean.sh                     # Terminal 2
uv run python src/main.py                          # Terminal 3

# Check system status
curl http://localhost:5001/health                   # Overall health
curl http://localhost:5001/metrics                  # System metrics
curl http://localhost:5001/queue/stats              # Queue status

# Test video generation flow
curl -X POST http://localhost:5001/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt":"test video","duration":3}'

# Monitor job status
curl http://localhost:5001/status/JOB_ID

# Verify video accessibility
curl -I http://localhost:5001/video/JOB_ID
```

## Performance Monitoring
```bash
# Complete system validation
python3 run_all_production_tests.py

# Code quality checks
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest

# Monitor Celery workers
celery -A src.job_queue.celery_app inspect active    # Active tasks
celery -A src.job_queue.celery_app inspect stats     # Worker statistics
```

## Critical Security Notes
- **🚨 PRODUCTION BLOCKER**: Remove `/debug/azure-config` endpoint before deployment
- **Rate Limiting**: 10 req/sec for Azure API, Redis-based coordination
- **File Security**: Use `werkzeug.secure_filename` and path validation
- **Environment**: Never commit secrets, use `.env` file only
- **⚠️ Environment Conflicts**: Use `./scripts/start_celery_clean.sh` to avoid environment variable conflicts that can override .env values

## Azure Sora API Critical Issues (Updated 2025-07-17)

### 🚨 **Azure Sora API 404 Errors**
If video generation fails with 404 errors, the issue is likely:

1. **Wrong API Version**: Must be `AZURE_OPENAI_API_VERSION=preview`
2. **Missing /jobs Suffix**: Endpoint must be `/openai/v1/video/generations/jobs`
3. **Incorrect Payload**: Use `width`, `height`, `n_seconds` (not `size`, `duration`)

**Quick Fix:**
```bash
# Update .env file
AZURE_OPENAI_API_VERSION=preview

# Test endpoint
curl -X POST "https://your-resource.openai.azure.com/openai/v1/video/generations/jobs?api-version=preview" \
  -H "api-key: your-key" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "test", "width": 1280, "height": 720, "n_seconds": 5, "model": "sora"}'
```

### 🎥 **Frontend Video Display Issues** (Fixed 2025-07-17)
If the frontend shows "Video file not found" even though videos are generated:

**Root Cause**: Relative vs absolute path handling in video serving endpoint

**Symptoms**:
- Video generation completes successfully  
- Database shows `succeeded` status with file path
- Frontend shows "Video preview is not available" error
- JSON response: `{"error": "The video file could not be located"}`

**Solution Applied (2025-07-17)**:
Fixed `src/api/file_routes.py` to handle both absolute and relative file paths:

```python
# Handle both absolute and relative paths
if os.path.isabs(video_path):
    # Absolute path - use as is
    resolved_path = video_path
else:
    # Relative path - resolve relative to current working directory
    resolved_path = os.path.abspath(video_path)

# Validate file path security
if not _validate_file_path_security(resolved_path, upload_folder):
    # Security validation...
```

**Database Initialization Issue**:
If video generation fails with "no such table: video_jobs" error:

**Root Cause**: Database tables not created when Flask app starts

**Solution Applied (2025-07-17)**:
Added database initialization to `src/main.py`:

```python
# Initialize database manager and create tables
db_manager = get_db_manager()
db_manager.init_db()
```

**Testing Video Serving**:
```bash
# Test if video endpoint works
curl -s "http://localhost:5001/video/YOUR_JOB_ID" | head -c 100

# Should return MP4 binary data starting with: ftypisom
```

**Status**: ✅ **FULLY RESOLVED** - Frontend video display now works correctly with proper video streaming