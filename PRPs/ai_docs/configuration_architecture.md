# Configuration Architecture

## Fork-Safe Configuration Pattern
The system uses a fork-safe configuration architecture to handle Celery worker processes:

1. **ConfigurationService**: Runtime configuration resolution with fork detection
2. **Post-Fork Hooks**: Automatic cache clearing when workers are forked
3. **Process ID Tracking**: Detects process forks and reloads configuration
4. **Environment Validation**: Scripts to detect and resolve configuration conflicts

## Configuration Resolution Flow
```python
# 1. ConfigurationService detects fork and clears cache
# 2. Environment variables reloaded from .env file
# 3. Property-based configuration ensures runtime resolution
# 4. Cached values prevent repeated expensive operations
```

## Troubleshooting Configuration Issues
```bash
# Check for environment variable conflicts
uv run python scripts/check_environment.py

# Use clean startup script (recommended)
./scripts/start_celery_clean.sh

# Manual environment cleanup
export -n AZURE_OPENAI_API_VERSION
uv run celery -A src.job_queue.celery_app worker --loglevel=info
```

## Dependencies Overview

### Core Stack
- **Flask 3.0+**: Web framework with Blueprint organization
- **SQLAlchemy 2.0+**: Database ORM with modern async support
- **Pydantic v2**: Data validation and serialization
- **Celery 5.3+**: Background task processing and queue management
- **Redis 5.0+**: Message broker, result backend, and rate limiting

### Tool Configuration
- **Ruff**: Line length 88, extends pycodestyle, flake8-bugbear, pyupgrade
- **MyPy**: Strict mode with complete type checking
- **Pytest**: Auto-discovery with coverage reporting and test markers