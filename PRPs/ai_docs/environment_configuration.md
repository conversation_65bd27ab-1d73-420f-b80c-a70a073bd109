# Environment Configuration Guide

## Complete Environment Variables

Environment variables in `.env`:

```bash
# Azure OpenAI (Required)
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_SORA_DEPLOYMENT=sora

# Flask Configuration
FLASK_ENV=development  # development, testing, production
SECRET_KEY=your-secure-secret-key
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=104857600

# Database Configuration
DATABASE_URL=sqlite:///sora_poc.db  # SQLite for development
# DATABASE_URL=postgresql://user:pass@localhost/sora_prod  # PostgreSQL for production

# Video Generation Settings
MAX_PROMPT_LENGTH=500
DEFAULT_VIDEO_DURATION=5
MAX_VIDEO_DURATION=20

# Multi-User Queue Management (NEW)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
MAX_CONCURRENT_JOBS_PER_SESSION=3
QUEUE_PRIORITY_ENABLED=true

# Session Management (NEW)
SESSION_LIFETIME_HOURS=24
SESSION_CLEANUP_INTERVAL_HOURS=6
MAX_SESSIONS_PER_IP=10

# Redis Configuration (NEW)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Rate Limiting (NEW)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=10

# Monitoring & Health
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# File Management
FILE_CLEANUP_ENABLED=true
FILE_MAX_AGE_HOURS=24

# Logging
LOG_LEVEL=INFO
SQL_DEBUG=false
```

## 🛠️ UV Package Management

This project uses UV for Python package management. Key commands include:

```bash
# Create virtual environment
uv venv

# Install dependencies from pyproject.toml
uv sync

# Install a specific package
uv add requests

# Remove a package
uv remove requests

# Run a Python script or command
uv run python script.py
uv run pytest

# Install editable packages
uv pip install -e .
```

When running scripts or tools, always use `uv run` to ensure proper virtual environment activation:

```bash
# Preferred way to run commands
uv run pytest
uv run black .

# Running tools without installing
uvx black .
uvx ruff check .
```

## 🛠️ BRANCHING STRATEGY

This repository follows a develop → main branching strategy, where:

- `main` is the production branch containing stable releases
- `develop` is the integration branch where features are merged
- Feature branches are created from `develop` for work in progress

When creating branches, follow these naming conventions:

- Feature branches: `feature/descriptive-name`
- Bug fix branches: `fix/issue-description`
- Documentation branches: `docs/what-is-changing`
- Refactoring branches: `refactor/what-is-changing`