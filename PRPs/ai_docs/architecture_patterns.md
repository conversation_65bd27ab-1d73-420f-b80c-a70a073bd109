# Architecture Patterns - Development Patterns and Best Practices

## Overview

Comprehensive guide to architectural patterns, design principles, and development best practices used throughout the production-ready multi-user video generation system. This document provides reusable patterns for building scalable, maintainable, and type-safe applications.

## Core Architectural Principles

### Universal Software Principles
- **KISS** (Keep It Simple, Stupid): Choose straightforward solutions over complex ones
- **YAGNI** (You Aren't Gonna Need It): Build only what's needed now
- **DRY** (Don't Repeat Yourself): Single source of truth for all logic and knowledge
- **Dependency Inversion**: High-level modules depend on abstractions, not low-level modules
- **Open/Closed Principle**: Open for extension, closed for modification

### Vertical Slice Architecture
```
src/
├── api/                    # API layer with routes, models, repository
│   ├── routes.py
│   ├── models.py
│   ├── job_repository.py
│   └── tests/
├── features/               # Business logic organized by feature
│   └── sora_integration/
│       ├── client.py
│       ├── http_client.py
│       ├── job_manager.py
│       └── tests/
├── core/                   # Domain models and interfaces
│   ├── models.py
│   ├── interfaces.py
│   └── tests/
└── [other modules follow same pattern]
```

**Benefits:**
- **High Cohesion**: Related functionality grouped together
- **Testability**: Co-located tests with business logic
- **Maintainability**: Changes isolated to specific slices
- **Scalability**: Independent module development and deployment

## Configuration Management Patterns

### Factory Pattern for Configuration
```python
# Universal configuration management
class ConfigurationFactory:
    """Factory for environment-specific configurations."""
    
    @staticmethod
    def get_environment_config() -> EnvironmentConfig:
        """Get configuration for current environment."""
        environment = os.getenv('FLASK_ENV', 'development')
        
        if environment == 'production':
            return ProductionConfig()
        elif environment == 'testing':
            return TestingConfig()
        else:
            return DevelopmentConfig()
    
    @staticmethod
    def get_video_config() -> VideoConfig:
        """Get video generation configuration."""
        return VideoConfig(
            max_duration=safe_int_from_env('MAX_VIDEO_DURATION', 60),
            default_duration=safe_int_from_env('DEFAULT_VIDEO_DURATION', 5),
            max_prompt_length=safe_int_from_env('MAX_PROMPT_LENGTH', 500)
        )

# Configuration hierarchy: .env → Config → Factory → Models
class EnvironmentConfig(BaseModel):
    """Environment-specific configuration with validation."""
    
    environment: str = "development"
    database_url: str
    redis_host: str = "localhost"
    redis_port: int = 6379
    
    # Security configuration
    secret_key: str
    azure_openai_api_key: str
    azure_openai_endpoint: str
    
    # Performance configuration
    max_concurrent_jobs: int = 3
    session_lifetime_hours: int = 24
    
    @validator('azure_openai_endpoint')
    def validate_endpoint_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Azure endpoint must be a valid URL')
        return v

# Usage pattern
def create_app() -> Flask:
    config = ConfigurationFactory.get_environment_config()
    app = Flask(__name__)
    app.config.from_object(config)
    return app
```

### Safe Environment Variable Parsing
```python
def safe_int_from_env(key: str, default: int, min_val: int = None, max_val: int = None) -> int:
    """Safely parse integer from environment with validation."""
    try:
        value = int(os.getenv(key, str(default)))
        
        if min_val is not None and value < min_val:
            logger.warning(f"{key}={value} below minimum {min_val}, using {min_val}")
            return min_val
            
        if max_val is not None and value > max_val:
            logger.warning(f"{key}={value} above maximum {max_val}, using {max_val}")
            return max_val
            
        return value
    except ValueError as e:
        logger.error(f"Invalid {key} environment variable: {e}")
        return default

# Environment variable precedence pattern
def get_config_value(key: str, default: Any = None, type_converter: Callable = str) -> Any:
    """Get configuration value with precedence: env var → config file → default."""
    # 1. Environment variable (highest priority)
    env_value = os.getenv(key)
    if env_value is not None:
        try:
            return type_converter(env_value)
        except (ValueError, TypeError):
            logger.warning(f"Invalid environment variable {key}={env_value}")
    
    # 2. Configuration file
    config_value = get_from_config_file(key)
    if config_value is not None:
        return config_value
    
    # 3. Default value (lowest priority)
    return default
```

## Data Model Patterns

### Pydantic V2 Domain Models
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, Literal
from datetime import datetime

# Core domain model with validation
class VideoJob(BaseModel):
    """Core domain model for video generation jobs."""
    
    job_id: str = Field(..., min_length=1, description="Unique job identifier")
    prompt: str = Field(..., min_length=1, max_length=500, description="Video generation prompt")
    duration: Optional[int] = Field(5, ge=1, le=60, description="Video duration in seconds")
    width: Optional[int] = Field(1280, ge=480, le=1920, description="Video width in pixels")
    height: Optional[int] = Field(720, ge=480, le=1080, description="Video height in pixels")
    model: Optional[str] = Field("sora-1.0", description="AI model to use")
    
    # Status management with literals for type safety
    status: Literal["pending", "running", "succeeded", "failed"] = "pending"
    progress: float = Field(0.0, ge=0.0, le=100.0, description="Completion percentage")
    message: Optional[str] = None
    
    # API integration fields
    generation_id: Optional[str] = None
    file_path: Optional[str] = None
    download_url: Optional[str] = None
    error_message: Optional[str] = None
    
    # Timestamps
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @validator('prompt')
    def validate_prompt_content(cls, v):
        """Validate prompt content for security and quality."""
        if not v.strip():
            raise ValueError("Prompt cannot be empty or whitespace")
        
        # Security: Check for potentially harmful content
        harmful_patterns = ['<script>', 'javascript:', 'data:', 'vbscript:']
        v_lower = v.lower()
        if any(pattern in v_lower for pattern in harmful_patterns):
            raise ValueError("Prompt contains potentially harmful content")
        
        return v.strip()
    
    @validator('duration')
    def validate_duration_constraints(cls, v, values):
        """Validate duration based on other parameters."""
        # Higher resolution videos have duration limits
        width = values.get('width', 1280)
        height = values.get('height', 720)
        
        if width * height > 1920 * 1080 and v > 30:
            raise ValueError("High resolution videos limited to 30 seconds")
        
        return v

# Model composition patterns
class GenerationParams(BaseModel):
    """Video generation parameters with validation."""
    
    prompt: str
    duration: int = 5
    width: int = 1280
    height: int = 720
    model: str = "sora-1.0"
    
    def to_api_payload(self) -> Dict[str, Any]:
        """Convert to Azure API payload format."""
        return {
            "prompt": self.prompt,
            "video_length": self.duration,
            "aspect_ratio": f"{self.width}:{self.height}",
            "model": self.model
        }
    
    @classmethod
    def from_ui_request(cls, prompt: str, ui_parameters: Dict[str, Any]) -> 'GenerationParams':
        """Create from UI request with defaults."""
        return cls(
            prompt=prompt,
            duration=ui_parameters.get('duration', 5),
            width=ui_parameters.get('width', 1280),
            height=ui_parameters.get('height', 720),
            model=ui_parameters.get('model', 'sora-1.0')
        )

# Factory pattern for model creation
class GenerationParamsFactory:
    """Factory for creating generation parameters with validation."""
    
    @staticmethod
    def create_from_ui_request(prompt: str, ui_parameters: Dict[str, Any]) -> GenerationParams:
        """Create parameters from UI request with validation and defaults."""
        video_config = get_cached_video_config()
        
        # Apply constraints from configuration
        duration = min(
            ui_parameters.get('duration', video_config.default_duration),
            video_config.max_duration
        )
        
        # Validate resolution presets
        resolution_presets = {
            'SD': {'width': 854, 'height': 480},
            'HD': {'width': 1280, 'height': 720},
            'Full HD': {'width': 1920, 'height': 1080}
        }
        
        preset = ui_parameters.get('resolution_preset')
        if preset and preset in resolution_presets:
            width = resolution_presets[preset]['width']
            height = resolution_presets[preset]['height']
        else:
            width = ui_parameters.get('width', 1280)
            height = ui_parameters.get('height', 720)
        
        return GenerationParams(
            prompt=prompt,
            duration=duration,
            width=width,
            height=height,
            model=ui_parameters.get('model', 'sora-1.0')
        )
```

## Repository Pattern

### Database Abstraction Layer
```python
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

# Interface definition for dependency inversion
class JobRepositoryInterface(ABC):
    """Abstract interface for job repository operations."""
    
    @abstractmethod
    def create_job(self, job: VideoJob, session_id: str) -> VideoJobDB:
        """Create new job in storage."""
        pass
    
    @abstractmethod
    def get_job_by_id(self, job_id: str) -> Optional[VideoJobDB]:
        """Get job by unique identifier."""
        pass
    
    @abstractmethod
    def get_jobs_by_session(self, session_id: str, limit: int = 100) -> List[VideoJobDB]:
        """Get all jobs for a session."""
        pass
    
    @abstractmethod
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status with automatic timestamp handling."""
        pass

# Concrete implementation
class JobRepository(JobRepositoryInterface):
    """SQLAlchemy-based job repository implementation."""
    
    def create_job(self, job: VideoJob, session_id: str, client_ip: str = None, user_agent: str = None) -> VideoJobDB:
        """Create new job with session context."""
        with get_db_session() as session:
            db_job = VideoJobDB.from_core_model(job, session_id, client_ip, user_agent)
            session.add(db_job)
            session.flush()  # Get the ID without committing
            session.refresh(db_job)
            
            logger.info(f"Created job {db_job.job_id} for session {session_id}")
            return db_job
    
    def get_job_by_id(self, job_id: str) -> Optional[VideoJobDB]:
        """Get job with error handling and logging."""
        try:
            with get_db_session() as session:
                job = session.query(VideoJobDB).filter_by(job_id=job_id).first()
                if job:
                    logger.debug(f"Retrieved job {job_id}")
                return job
        except Exception as e:
            logger.error(f"Error retrieving job {job_id}: {e}")
            return None
    
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status with automatic timestamp management."""
        try:
            with get_db_session() as session:
                job = session.query(VideoJobDB).filter_by(job_id=job_id).first()
                
                if not job:
                    logger.warning(f"Job {job_id} not found for status update")
                    return False
                
                # Use model's status update method for timestamp handling
                job.update_status(status, **kwargs)
                
                logger.info(f"Updated job {job_id} status to {status}")
                return True
        except Exception as e:
            logger.error(f"Error updating job {job_id}: {e}")
            return False

# Repository factory pattern
class RepositoryFactory:
    """Factory for creating repository instances."""
    
    @staticmethod
    def create_job_repository() -> JobRepositoryInterface:
        """Create job repository with dependency injection."""
        return JobRepository()
    
    @staticmethod
    def create_session_repository() -> SessionRepositoryInterface:
        """Create session repository."""
        redis_client = Redis(
            host=get_config_value('REDIS_HOST', 'localhost'),
            port=get_config_value('REDIS_PORT', 6379, int),
            db=get_config_value('REDIS_DB', 0, int)
        )
        return SessionRepository(redis_client)
```

## Session Management Patterns

### Cryptographically Secure Sessions
```python
import secrets
import hashlib
from typing import Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class SessionData:
    """Session data with security context."""
    session_id: str
    client_ip: str
    created_at: datetime
    last_activity: datetime
    user_agent: Optional[str] = None
    security_hash: Optional[str] = None

class SessionManager:
    """Secure session management with IP validation."""
    
    def __init__(self, redis_client, session_lifetime_hours: int = 24):
        self.redis_client = redis_client
        self.session_lifetime = session_lifetime_hours * 3600
        self.session_prefix = "session:"
        self.ip_tracking_prefix = "ip_sessions:"
    
    def create_session(self, client_ip: str, user_agent: str = None) -> Tuple[str, SessionData]:
        """Create cryptographically secure session."""
        # Generate secure session ID (256 bits of entropy)
        session_id = secrets.token_urlsafe(32)
        
        # Create security hash for validation
        security_hash = self._generate_security_hash(session_id, client_ip, user_agent)
        
        # Create session data
        now = datetime.utcnow()
        session_data = SessionData(
            session_id=session_id,
            client_ip=client_ip,
            created_at=now,
            last_activity=now,
            user_agent=user_agent,
            security_hash=security_hash
        )
        
        # Store in Redis with expiration
        self._store_session(session_data)
        self._track_ip_session(client_ip, session_id)
        
        return session_id, session_data
    
    def validate_session(self, session_id: str, client_ip: str, user_agent: str = None) -> bool:
        """Validate session with comprehensive security checks."""
        session_data = self.get_session(session_id)
        
        if not session_data:
            return False
        
        # IP consistency check
        if session_data.client_ip != client_ip:
            logger.warning(f"IP mismatch for session {session_id}: {client_ip} != {session_data.client_ip}")
            return False
        
        # Security hash validation
        expected_hash = self._generate_security_hash(session_id, client_ip, user_agent)
        if session_data.security_hash != expected_hash:
            logger.warning(f"Security hash mismatch for session {session_id}")
            return False
        
        # Session expiration check
        if self._is_session_expired(session_data):
            self.delete_session(session_id)
            return False
        
        # Update last activity
        self._update_last_activity(session_id)
        return True
    
    def _generate_security_hash(self, session_id: str, client_ip: str, user_agent: str = None) -> str:
        """Generate security hash for session validation."""
        hash_input = f"{session_id}:{client_ip}:{user_agent or ''}".encode('utf-8')
        return hashlib.sha256(hash_input).hexdigest()

# Session isolation pattern
class SessionIsolation:
    """Provides data isolation between user sessions."""
    
    def __init__(self, job_repository: JobRepositoryInterface):
        self.job_repository = job_repository
    
    def get_session_jobs(self, session_id: str) -> List[Dict[str, Any]]:
        """Get jobs isolated to specific session."""
        jobs = self.job_repository.get_jobs_by_session(session_id)
        return [job.to_dict() for job in jobs]
    
    def can_submit_job(self, session_id: str, max_concurrent: int = 3) -> bool:
        """Check if session can submit new job (rate limiting)."""
        active_jobs = self.get_session_active_jobs(session_id)
        return len(active_jobs) < max_concurrent
    
    def get_session_statistics(self, session_id: str) -> Dict[str, Any]:
        """Get comprehensive session statistics."""
        jobs = self.get_session_jobs(session_id)
        active_jobs = [job for job in jobs if job['status'] in ['pending', 'running']]
        
        return {
            'session_id': session_id,
            'total_jobs': len(jobs),
            'active_jobs': len(active_jobs),
            'succeeded_jobs': len([job for job in jobs if job['status'] == 'succeeded']),
            'failed_jobs': len([job for job in jobs if job['status'] == 'failed']),
            'can_submit_more': self.can_submit_job(session_id)
        }
```

## Multi-User Workflow Patterns

### Queue-Based Job Processing
```python
from celery import Celery
from src.realtime.broadcaster import StatusBroadcaster

class MultiUserJobProcessor:
    """Handles multi-user job processing with isolation."""
    
    def __init__(self, celery_app: Celery, status_broadcaster: StatusBroadcaster):
        self.celery_app = celery_app
        self.status_broadcaster = status_broadcaster
    
    def submit_video_job(self, session_id: str, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit video generation job with session context."""
        try:
            # Validate session can submit job
            if not self._can_submit_job(session_id):
                return {
                    'success': False,
                    'error': 'Too many active jobs for session',
                    'session_id': session_id
                }
            
            # Create job with unique ID
            job_id = self._generate_job_id(session_id)
            
            # Submit to Celery queue with session context
            task = process_video_generation.delay(
                session_id=session_id,
                job_id=job_id,
                job_data=job_data
            )
            
            # Store job metadata
            self._store_job_metadata(job_id, session_id, task.id, job_data)
            
            # Broadcast job submission
            self.status_broadcaster.broadcast_job_status(
                job_id=job_id,
                status_data={'status': 'submitted', 'message': 'Job submitted to queue'},
                session_id=session_id
            )
            
            return {
                'success': True,
                'job_id': job_id,
                'task_id': task.id,
                'session_id': session_id
            }
            
        except Exception as e:
            logger.error(f"Error submitting job for session {session_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }

# Background task with real-time updates
@celery_app.task(bind=True)
def process_video_generation(self, session_id: str, job_id: str, job_data: Dict[str, Any]):
    """Process video generation with real-time status updates."""
    try:
        # Update job status to running
        update_job_status(job_id, 'running', message='Starting video generation')
        broadcast_status_update(job_id, session_id, 'running', 'Video generation started')
        
        # Initialize Azure client
        sora_client = SoraClient.from_env()
        
        # Submit job to Azure API
        azure_job = sora_client.create_video_job(
            prompt=job_data['prompt'],
            ui_parameters=job_data.get('params', {})
        )
        
        # Update with generation ID
        update_job_status(job_id, 'running', 
                         generation_id=azure_job.generation_id,
                         message='Job submitted to Azure API')
        
        # Poll for completion with exponential backoff
        poll_interval = 5  # Start with 5 seconds
        max_poll_interval = 30
        max_polls = 120  # 10 minutes max
        
        for poll_count in range(max_polls):
            current_status = sora_client.poll_job_status(job_id, azure_job.generation_id)
            
            if current_status.status == 'succeeded':
                # Download video file
                file_path = sora_client.download_video(
                    current_status.download_url,
                    job_id
                )
                
                # Update job as completed
                update_job_status(job_id, 'succeeded',
                                file_path=file_path,
                                download_url=current_status.download_url,
                                message='Video generation completed')
                
                broadcast_status_update(job_id, session_id, 'succeeded', 
                                      'Video generation completed successfully')
                return {'status': 'completed', 'file_path': file_path}
                
            elif current_status.status == 'failed':
                error_msg = current_status.error_message or 'Unknown error'
                update_job_status(job_id, 'failed', error_message=error_msg)
                broadcast_status_update(job_id, session_id, 'failed', f'Generation failed: {error_msg}')
                raise Exception(f"Azure job failed: {error_msg}")
            
            # Broadcast progress updates
            if hasattr(current_status, 'progress'):
                broadcast_status_update(job_id, session_id, 'running', 
                                      f'Generation {current_status.progress}% complete')
            
            # Exponential backoff
            time.sleep(poll_interval)
            poll_interval = min(poll_interval * 1.5, max_poll_interval)
        
        # Timeout
        raise Exception("Video generation timed out")
        
    except Exception as exc:
        # Handle job failure
        error_message = str(exc)
        update_job_status(job_id, 'failed', error_message=error_message)
        broadcast_status_update(job_id, session_id, 'failed', f'Generation failed: {error_message}')
        raise
```

## Error Handling Patterns

### Comprehensive Error Management
```python
from enum import Enum
from typing import Optional, Dict, Any
import traceback

class ErrorType(Enum):
    """Categorized error types for proper handling."""
    VALIDATION_ERROR = "validation"
    AUTHENTICATION_ERROR = "authentication"
    AUTHORIZATION_ERROR = "authorization"
    RESOURCE_NOT_FOUND = "not_found"
    RATE_LIMIT_ERROR = "rate_limit"
    EXTERNAL_API_ERROR = "external_api"
    DATABASE_ERROR = "database"
    INTERNAL_ERROR = "internal"

class ApplicationError(Exception):
    """Base application error with context."""
    
    def __init__(self, 
                 message: str,
                 error_type: ErrorType,
                 error_code: str = None,
                 context: Dict[str, Any] = None,
                 original_exception: Exception = None):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.error_code = error_code
        self.context = context or {}
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for API responses."""
        return {
            'error': True,
            'error_type': self.error_type.value,
            'error_code': self.error_code,
            'message': self.message,
            'context': self.context,
            'timestamp': self.timestamp.isoformat()
        }

# Specific error classes
class ValidationError(ApplicationError):
    def __init__(self, message: str, field: str = None, **kwargs):
        super().__init__(message, ErrorType.VALIDATION_ERROR, **kwargs)
        if field:
            self.context['field'] = field

class ExternalAPIError(ApplicationError):
    def __init__(self, message: str, api_name: str, status_code: int = None, **kwargs):
        super().__init__(message, ErrorType.EXTERNAL_API_ERROR, **kwargs)
        self.context.update({
            'api_name': api_name,
            'status_code': status_code
        })

# Error handling decorator
def handle_errors(error_context: str = None):
    """Decorator for comprehensive error handling."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ApplicationError:
                # Re-raise application errors as-is
                raise
            except ValidationError as e:
                logger.warning(f"Validation error in {func.__name__}: {e}")
                raise ValidationError(str(e), context={'function': func.__name__})
            except ConnectionError as e:
                logger.error(f"Connection error in {func.__name__}: {e}")
                raise ExternalAPIError(
                    "External service unavailable",
                    api_name=error_context or func.__name__,
                    original_exception=e
                )
            except Exception as e:
                logger.error(f"Unexpected error in {func.__name__}: {e}")
                logger.error(traceback.format_exc())
                raise ApplicationError(
                    "An unexpected error occurred",
                    ErrorType.INTERNAL_ERROR,
                    context={'function': func.__name__},
                    original_exception=e
                )
        return wrapper
    return decorator

# Flask error handlers
def register_error_handlers(app: Flask):
    """Register application error handlers."""
    
    @app.errorhandler(ApplicationError)
    def handle_application_error(error: ApplicationError):
        """Handle custom application errors."""
        status_code = {
            ErrorType.VALIDATION_ERROR: 400,
            ErrorType.AUTHENTICATION_ERROR: 401,
            ErrorType.AUTHORIZATION_ERROR: 403,
            ErrorType.RESOURCE_NOT_FOUND: 404,
            ErrorType.RATE_LIMIT_ERROR: 429,
            ErrorType.EXTERNAL_API_ERROR: 502,
            ErrorType.DATABASE_ERROR: 503,
            ErrorType.INTERNAL_ERROR: 500
        }.get(error.error_type, 500)
        
        return jsonify(error.to_dict()), status_code
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error):
        """Handle Pydantic validation errors."""
        return jsonify({
            'error': True,
            'error_type': 'validation',
            'message': 'Validation failed',
            'details': str(error)
        }), 400
    
    @app.errorhandler(404)
    def handle_not_found(error):
        """Handle 404 errors."""
        return jsonify({
            'error': True,
            'error_type': 'not_found',
            'message': 'Resource not found'
        }), 404
```

## Security Patterns

### Input Validation and Sanitization
```python
import re
from werkzeug.security import safe_str_cmp
from werkzeug.utils import secure_filename

class SecurityValidator:
    """Comprehensive security validation utilities."""
    
    @staticmethod
    def validate_prompt(prompt: str) -> bool:
        """Validate prompt for security and content guidelines."""
        if not prompt or not prompt.strip():
            return False
        
        # Length validation
        if len(prompt) > 500:
            return False
        
        # Security patterns to reject
        dangerous_patterns = [
            r'<script[^>]*>',
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'on\w+\s*=',  # Event handlers
            r'eval\s*\(',
            r'document\.',
            r'window\.',
        ]
        
        prompt_lower = prompt.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, prompt_lower, re.IGNORECASE):
                return False
        
        return True
    
    @staticmethod
    def sanitize_prompt(prompt: str) -> str:
        """Sanitize prompt while preserving content."""
        if not prompt:
            return ""
        
        # Remove control characters
        prompt = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', prompt)
        
        # Normalize whitespace
        prompt = re.sub(r'\s+', ' ', prompt).strip()
        
        # Remove potentially harmful HTML/JS
        prompt = re.sub(r'<[^>]+>', '', prompt)
        
        return prompt
    
    @staticmethod
    def validate_file_path_security(file_path: str, allowed_directory: str) -> bool:
        """Validate file path to prevent directory traversal."""
        try:
            # Normalize paths
            file_path = os.path.normpath(file_path)
            allowed_directory = os.path.normpath(allowed_directory)
            
            # Check for directory traversal attempts
            if '..' in file_path:
                return False
            
            # Ensure file is within allowed directory
            abs_file_path = os.path.abspath(file_path)
            abs_allowed_dir = os.path.abspath(allowed_directory)
            
            return abs_file_path.startswith(abs_allowed_dir)
        except Exception:
            return False
    
    @staticmethod
    def sanitize_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """Sanitize HTTP headers to prevent sensitive data exposure."""
        sanitized = {}
        sensitive_headers = {'authorization', 'api-key', 'x-api-key', 'cookie', 'set-cookie'}
        
        for key, value in headers.items():
            if key.lower() in sensitive_headers:
                sanitized[key] = '[REDACTED]'
            else:
                sanitized[key] = value
        
        return sanitized

# Rate limiting security
class SecurityRateLimiter:
    """Security-focused rate limiting for authentication attempts."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.login_attempts_prefix = "login_attempts:"
        self.suspicious_ips_prefix = "suspicious_ips:"
    
    def check_login_attempts(self, ip_address: str) -> bool:
        """Check if IP has exceeded login attempt limits."""
        key = f"{self.login_attempts_prefix}{ip_address}"
        attempts = self.redis_client.get(key)
        
        if attempts and int(attempts) >= 5:  # 5 attempts per hour
            return False
        
        return True
    
    def record_failed_login(self, ip_address: str):
        """Record failed login attempt with exponential backoff."""
        key = f"{self.login_attempts_prefix}{ip_address}"
        
        with self.redis_client.pipeline() as pipe:
            pipe.incr(key)
            pipe.expire(key, 3600)  # 1 hour
            attempts = pipe.execute()[0]
        
        # Mark as suspicious after multiple failures
        if attempts >= 3:
            suspicious_key = f"{self.suspicious_ips_prefix}{ip_address}"
            self.redis_client.setex(suspicious_key, 86400, attempts)  # 24 hours
```

## Testing Patterns

### Comprehensive Testing Strategy
```python
import pytest
import uuid
from unittest.mock import Mock, patch
from contextlib import contextmanager

# Test data factories
class TestDataFactory:
    """Factory for creating consistent test data."""
    
    @staticmethod
    def create_video_job(job_id: str = None, **kwargs) -> VideoJob:
        """Create test VideoJob with unique ID."""
        return VideoJob(
            job_id=job_id or str(uuid.uuid4()),
            prompt=kwargs.get('prompt', 'Test video prompt'),
            duration=kwargs.get('duration', 5),
            width=kwargs.get('width', 1280),
            height=kwargs.get('height', 720),
            status=kwargs.get('status', 'pending'),
            **kwargs
        )
    
    @staticmethod
    def create_session_data(session_id: str = None, **kwargs) -> SessionData:
        """Create test session data."""
        return SessionData(
            session_id=session_id or str(uuid.uuid4()),
            client_ip=kwargs.get('client_ip', '127.0.0.1'),
            created_at=kwargs.get('created_at', datetime.utcnow()),
            last_activity=kwargs.get('last_activity', datetime.utcnow()),
            user_agent=kwargs.get('user_agent', 'Test Browser')
        )

# Test fixtures
@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    redis_mock = Mock()
    redis_mock.ping.return_value = True
    redis_mock.get.return_value = b'test_value'
    redis_mock.set.return_value = True
    redis_mock.hgetall.return_value = {}
    return redis_mock

@pytest.fixture
def mock_sora_client():
    """Mock SoraClient for testing."""
    client_mock = Mock()
    client_mock.create_video_job.return_value = Mock(
        generation_id='gen-123',
        job_id='job-456',
        status='pending'
    )
    client_mock.poll_job_status.return_value = Mock(
        status='succeeded',
        download_url='http://example.com/video.mp4'
    )
    return client_mock

# Integration testing patterns
class TestVideoGenerationWorkflow:
    """Integration tests for complete video generation workflow."""
    
    def test_complete_workflow_success(self, app, mock_sora_client):
        """Test complete successful video generation workflow."""
        with app.test_client() as client:
            # Step 1: Submit video generation request
            response = client.post('/generate', json={
                'prompt': 'Test video prompt',
                'duration': 5
            })
            
            assert response.status_code == 200
            data = response.get_json()
            job_id = data['job_id']
            
            # Step 2: Check job status progression
            status_checks = ['pending', 'running', 'succeeded']
            for expected_status in status_checks:
                response = client.get(f'/status/{job_id}')
                assert response.status_code == 200
                status_data = response.get_json()
                
                if status_data['status'] == expected_status:
                    break
            
            # Step 3: Download completed video
            if status_data['status'] == 'succeeded':
                response = client.get(f'/download/{job_id}')
                assert response.status_code == 200

# Performance testing patterns
@contextmanager
def performance_timer():
    """Context manager for measuring execution time."""
    start_time = time.time()
    yield
    execution_time = (time.time() - start_time) * 1000
    print(f"Execution time: {execution_time:.2f}ms")

def test_api_response_time():
    """Test API response time under load."""
    with performance_timer():
        # Make multiple concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(make_test_request, 'GET', '/health')
                for _ in range(50)
            ]
            results = [future.result() for future in futures]
    
    # Assert all requests succeeded
    assert all(r.status_code == 200 for r in results)
```

## Async Processing Patterns

### Background Task Management
```python
from celery import Celery, Task
from typing import Any, Dict

class CallbackTask(Task):
    """Custom Celery task with callback support."""
    
    def on_success(self, retval: Any, task_id: str, args: tuple, kwargs: dict):
        """Handle successful task completion."""
        logger.info(f"Task {task_id} completed successfully")
        
        # Broadcast success status if job_id provided
        if 'job_id' in kwargs and 'session_id' in kwargs:
            broadcast_job_status(
                kwargs['job_id'],
                'succeeded',
                kwargs['session_id'],
                result=retval
            )
    
    def on_failure(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo):
        """Handle task failure."""
        logger.error(f"Task {task_id} failed: {exc}")
        
        # Broadcast failure status if job_id provided
        if 'job_id' in kwargs and 'session_id' in kwargs:
            broadcast_job_status(
                kwargs['job_id'],
                'failed',
                kwargs['session_id'],
                error=str(exc)
            )

# Celery app configuration
def create_celery_app(flask_app: Flask) -> Celery:
    """Create and configure Celery app."""
    celery = Celery(
        flask_app.import_name,
        broker=flask_app.config['CELERY_BROKER_URL'],
        backend=flask_app.config['CELERY_RESULT_BACKEND']
    )
    
    # Update configuration
    celery.conf.update(
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        task_track_started=True,
        task_routes={
            'src.job_queue.tasks.process_video_generation': {'queue': 'video_processing'},
            'src.job_queue.tasks.cleanup_old_files': {'queue': 'maintenance'}
        }
    )
    
    # Task context integration
    class ContextTask(Task):
        def __call__(self, *args, **kwargs):
            with flask_app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery

# Queue monitoring patterns
class QueueMonitor:
    """Monitor Celery queue health and performance."""
    
    def __init__(self, celery_app: Celery):
        self.celery_app = celery_app
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get comprehensive queue statistics."""
        inspect = self.celery_app.control.inspect()
        
        # Get active tasks
        active_tasks = inspect.active()
        reserved_tasks = inspect.reserved()
        stats = inspect.stats()
        
        if not stats:
            return {'status': 'unhealthy', 'message': 'No workers responding'}
        
        total_workers = len(stats)
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_reserved = sum(len(tasks) for tasks in (reserved_tasks or {}).values())
        
        return {
            'status': 'healthy',
            'workers_online': total_workers,
            'active_tasks': total_active,
            'reserved_tasks': total_reserved,
            'queue_depth': total_active + total_reserved
        }
```

## Performance Optimization Patterns

### Caching Strategies
```python
from functools import lru_cache, wraps
import time
from typing import Any, Callable

# Simple memory cache with TTL
class TTLCache:
    """Time-to-live cache for temporary data."""
    
    def __init__(self, ttl_seconds: int = 300):
        self.ttl = ttl_seconds
        self.cache = {}
    
    def get(self, key: str) -> Any:
        """Get cached value if not expired."""
        if key in self.cache:
            value, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl:
                return value
            else:
                del self.cache[key]
        return None
    
    def set(self, key: str, value: Any):
        """Set cache value with timestamp."""
        self.cache[key] = (value, time.time())
    
    def clear(self):
        """Clear all cached values."""
        self.cache.clear()

# Configuration caching
@lru_cache(maxsize=1)
def get_cached_video_config() -> VideoConfig:
    """Get video configuration with caching."""
    return ConfigurationFactory.get_video_config()

# Decorator for method caching
def cache_result(ttl_seconds: int = 300):
    """Decorator for caching method results."""
    def decorator(func: Callable):
        cache = TTLCache(ttl_seconds)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from arguments
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Check cache first
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result)
            return result
        
        # Add cache control methods
        wrapper.clear_cache = cache.clear
        return wrapper
    
    return decorator

# Database query optimization
class OptimizedJobRepository(JobRepository):
    """Job repository with query optimization."""
    
    @cache_result(ttl_seconds=60)
    def get_jobs_by_session_cached(self, session_id: str, limit: int = 100) -> List[VideoJobDB]:
        """Get session jobs with caching for frequently accessed data."""
        return super().get_jobs_by_session(session_id, limit)
    
    def get_jobs_with_pagination(self, session_id: str, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """Get jobs with efficient pagination."""
        with get_db_session() as session:
            query = session.query(VideoJobDB).filter_by(session_id=session_id)
            
            # Get total count
            total_jobs = query.count()
            
            # Apply pagination
            jobs = query.order_by(desc(VideoJobDB.created_at))\
                        .offset((page - 1) * per_page)\
                        .limit(per_page)\
                        .all()
            
            return {
                'jobs': [job.to_dict() for job in jobs],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_jobs,
                    'pages': (total_jobs + per_page - 1) // per_page
                }
            }
```

## Best Practices Summary

### Code Organization
1. **Vertical Slice Architecture**: Group related functionality together
2. **Interface Segregation**: Define clear interfaces for dependency inversion
3. **Factory Patterns**: Use factories for complex object creation
4. **Repository Pattern**: Abstract data access behind interfaces
5. **Single Responsibility**: Each class/function has one clear purpose

### Security Best Practices
1. **Input Validation**: Validate all inputs at system boundaries
2. **Secure Sessions**: Use cryptographically secure session management
3. **Path Validation**: Prevent directory traversal attacks
4. **Rate Limiting**: Implement comprehensive rate limiting
5. **Error Handling**: Don't expose sensitive information in errors

### Performance Optimization
1. **Caching Strategies**: Implement appropriate caching layers
2. **Query Optimization**: Optimize database queries and indexes
3. **Async Processing**: Use background tasks for heavy operations
4. **Connection Pooling**: Pool database and external API connections
5. **Resource Monitoring**: Monitor and optimize resource usage

### Testing Excellence
1. **Test Organization**: Co-locate tests with code in vertical slices
2. **Test Data Factories**: Use factories for consistent test data
3. **Mock Dependencies**: Mock external dependencies appropriately
4. **Integration Testing**: Test complete workflows end-to-end
5. **Performance Testing**: Include performance tests in test suite

### Maintainability
1. **Type Safety**: Use comprehensive type hints and validation
2. **Documentation**: Document all public APIs and complex logic
3. **Error Handling**: Implement comprehensive error handling
4. **Logging**: Add structured logging throughout the application
5. **Configuration**: Use environment-based configuration management

This comprehensive architecture guide provides the foundation for building scalable, maintainable, and secure applications following established patterns and best practices.