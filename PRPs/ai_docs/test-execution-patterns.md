# Test Execution Patterns and Templates

**Reference Document for PRP Framework Test Execution**

## Overview

This document provides standardized test execution patterns and templates for the PRP framework to ensure consistent, reliable test execution across all implementation commands.

**Reference**: This document is referenced by implementation commands to ensure consistent test execution patterns.

## Core Test Execution Principles

### 1. **Mandatory Test Execution**
- **RULE**: All tests must be executed using `uv run pytest` before any completion claims
- **REQUIREMENT**: 100% test pass rate is required - no exceptions
- **BLOCKING**: Cannot proceed to completion without passing tests

### 2. **Environment Validation**
- **REQUIREMENT**: Tests must work in the actual project environment
- **VALIDATION**: Import paths, dependencies, and configuration must be verified
- **SETUP**: Test databases, external services, and mock environments must be configured

### 3. **Comprehensive Coverage**
- **REQUIREMENT**: All test categories must be executed and pass
- **CATEGORIES**: Unit, Integration, Security, Performance, E2E, Documentation
- **COVERAGE**: Must meet project requirements via `uv run pytest --cov=src --cov-report=html`

## Standardized Test Execution Templates

### Basic Test Execution Script

```bash
#!/bin/bash
# Standard test execution script for PRP framework implementations
# Usage: Save as test-execution.sh and run during implementation

echo "🧪 Starting comprehensive test execution..."

# 1. Environment validation
echo "🔍 Validating test environment..."
if ! uv run python -c "import sys; print('Python environment ready')"; then
    echo "❌ ERROR: Python environment not properly configured"
    exit 1
fi

# 2. Install/verify dependencies
echo "📦 Verifying test dependencies..."
uv run pip list | grep -E "(pytest|coverage)" || {
    echo "❌ ERROR: Required test dependencies missing"
    exit 1
}

# 3. Execute unit tests
echo "🎯 Running unit tests..."
uv run pytest tests/unit/ -v --tb=short
UNIT_EXIT_CODE=$?

# 4. Execute integration tests
echo "🔗 Running integration tests..."
uv run pytest tests/integration/ -v --tb=short
INTEGRATION_EXIT_CODE=$?

# 5. Execute security tests
echo "🔒 Running security tests..."
uv run pytest tests/security/ -v --tb=short
SECURITY_EXIT_CODE=$?

# 6. Execute performance tests
echo "⚡ Running performance tests..."
uv run pytest tests/performance/ -v --tb=short
PERFORMANCE_EXIT_CODE=$?

# 7. Execute e2e tests
echo "🌐 Running end-to-end tests..."
uv run pytest tests/e2e/ -v --tb=short
E2E_EXIT_CODE=$?

# 8. Generate coverage report
echo "📊 Generating coverage report..."
uv run pytest --cov=src --cov-report=html --cov-report=term
COVERAGE_EXIT_CODE=$?

# 9. Validate all tests passed
TOTAL_FAILURES=$((UNIT_EXIT_CODE + INTEGRATION_EXIT_CODE + SECURITY_EXIT_CODE + PERFORMANCE_EXIT_CODE + E2E_EXIT_CODE + COVERAGE_EXIT_CODE))

if [ $TOTAL_FAILURES -eq 0 ]; then
    echo "✅ All tests passed successfully!"
    echo "🎉 Test execution complete with 100% pass rate"
else
    echo "❌ Test execution failed with $TOTAL_FAILURES failure(s)"
    echo "🔧 Debug and fix failing tests before proceeding"
    exit 1
fi
```

### Python Test Execution Framework

```python
"""
Standardized test execution framework for PRP implementations.
Reference: PRPs/ai_docs/test-execution-patterns.md
"""

import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json

class TestExecutionFramework:
    """
    Standardized test execution framework for PRP implementations.
    
    Usage:
        executor = TestExecutionFramework(Path("."))
        success = executor.execute_all_tests()
    """
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_results = {}
        self.coverage_results = {}
        self.failures = []
        
    def execute_all_tests(self) -> bool:
        """
        Execute all test categories and validate 100% pass rate.
        
        Returns:
            bool: True if all tests pass, False otherwise
        """
        print("🧪 Starting comprehensive test execution...")
        
        # 1. Validate environment
        if not self._validate_environment():
            return False
            
        # 2. Execute test categories
        test_categories = [
            ("unit", "tests/unit/"),
            ("integration", "tests/integration/"),
            ("security", "tests/security/"),
            ("performance", "tests/performance/"),
            ("e2e", "tests/e2e/")
        ]
        
        all_passed = True
        for category, path in test_categories:
            print(f"🎯 Running {category} tests...")
            result = self._execute_test_category(category, path)
            self.test_results[category] = result
            if not result["passed"]:
                all_passed = False
                self.failures.extend(result["failures"])
        
        # 3. Generate coverage report
        print("📊 Generating coverage report...")
        coverage_result = self._generate_coverage_report()
        self.coverage_results = coverage_result
        
        # 4. Validate results
        if all_passed and coverage_result["meets_requirements"]:
            print("✅ All tests passed successfully!")
            print("🎉 Test execution complete with 100% pass rate")
            self._generate_success_report()
            return True
        else:
            print("❌ Test execution failed")
            self._generate_failure_report()
            return False
    
    def _validate_environment(self) -> bool:
        """Validate test environment is properly configured."""
        checks = [
            self._check_python_environment(),
            self._check_dependencies(),
            self._check_import_paths(),
            self._check_test_discovery()
        ]
        
        return all(checks)
    
    def _check_python_environment(self) -> bool:
        """Check Python environment is working."""
        try:
            result = subprocess.run(
                ["uv", "run", "python", "-c", "import sys; print('Python ready')"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Python environment check failed: {e}")
            return False
    
    def _check_dependencies(self) -> bool:
        """Check required test dependencies are installed."""
        required_deps = ["pytest", "coverage", "pytest-cov"]
        
        try:
            result = subprocess.run(
                ["uv", "run", "pip", "list"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode != 0:
                return False
                
            installed_packages = result.stdout.lower()
            for dep in required_deps:
                if dep not in installed_packages:
                    print(f"❌ Missing required dependency: {dep}")
                    return False
            
            return True
        except Exception as e:
            print(f"❌ Dependency check failed: {e}")
            return False
    
    def _check_import_paths(self) -> bool:
        """Check import paths are properly configured."""
        try:
            result = subprocess.run(
                ["uv", "run", "python", "-c", "import src; print('Import paths ready')"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Import path check failed: {e}")
            return False
    
    def _check_test_discovery(self) -> bool:
        """Check pytest can discover tests."""
        try:
            result = subprocess.run(
                ["uv", "run", "pytest", "--collect-only", "-q"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Test discovery check failed: {e}")
            return False
    
    def _execute_test_category(self, category: str, path: str) -> Dict:
        """Execute a specific test category."""
        try:
            result = subprocess.run(
                ["uv", "run", "pytest", path, "-v", "--tb=short"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Parse results
            passed = result.returncode == 0
            failures = []
            
            if not passed:
                failures.append({
                    "category": category,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode
                })
            
            return {
                "passed": passed,
                "failures": failures,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
        except Exception as e:
            return {
                "passed": False,
                "failures": [{
                    "category": category,
                    "error": str(e),
                    "type": "execution_error"
                }],
                "stdout": "",
                "stderr": str(e)
            }
    
    def _generate_coverage_report(self) -> Dict:
        """Generate coverage report and validate requirements."""
        try:
            result = subprocess.run(
                ["uv", "run", "pytest", "--cov=src", "--cov-report=html", "--cov-report=term", "--cov-report=json"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Try to parse coverage JSON if available
            coverage_file = self.project_root / "coverage.json"
            coverage_percentage = 0
            
            if coverage_file.exists():
                try:
                    with open(coverage_file) as f:
                        coverage_data = json.load(f)
                        coverage_percentage = coverage_data.get("totals", {}).get("percent_covered", 0)
                except Exception:
                    pass
            
            # Default requirement is 80%
            meets_requirements = coverage_percentage >= 80
            
            return {
                "meets_requirements": meets_requirements,
                "percentage": coverage_percentage,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
        except Exception as e:
            return {
                "meets_requirements": False,
                "percentage": 0,
                "error": str(e)
            }
    
    def _generate_success_report(self):
        """Generate success report."""
        report = f"""
# TEST EXECUTION SUCCESS REPORT

## Summary
✅ **All tests passed successfully**
📊 **Coverage meets requirements**
🎉 **100% test pass rate achieved**

## Test Results
"""
        
        for category, result in self.test_results.items():
            report += f"- {category.upper()} Tests: ✅ PASSED\n"
        
        report += f"""
## Coverage Results
- Coverage: {self.coverage_results['percentage']:.1f}%
- Meets Requirements: {'✅ YES' if self.coverage_results['meets_requirements'] else '❌ NO'}

## Validation Status
✅ Environment validated
✅ Dependencies verified
✅ Import paths configured
✅ Test discovery working
✅ All test categories executed
✅ Coverage requirements met

**RESULT: READY FOR COMPLETION**
"""
        
        with open(self.project_root / "TEST_EXECUTION_SUCCESS.md", "w") as f:
            f.write(report)
        
        print("📄 Success report generated: TEST_EXECUTION_SUCCESS.md")
    
    def _generate_failure_report(self):
        """Generate failure report with debugging information."""
        report = f"""
# TEST EXECUTION FAILURE REPORT

## Summary
❌ **Test execution failed**
🔧 **Debugging required before completion**

## Failed Tests
"""
        
        for category, result in self.test_results.items():
            status = "✅ PASSED" if result["passed"] else "❌ FAILED"
            report += f"- {category.upper()} Tests: {status}\n"
        
        report += f"""
## Coverage Results
- Coverage: {self.coverage_results.get('percentage', 0):.1f}%
- Meets Requirements: {'✅ YES' if self.coverage_results.get('meets_requirements', False) else '❌ NO'}

## Failure Details
"""
        
        for failure in self.failures:
            report += f"""
### {failure.get('category', 'Unknown').upper()} Test Failure
```
{failure.get('stderr', failure.get('error', 'Unknown error'))}
```
"""
        
        report += """
## Next Steps
1. Debug and fix failing tests
2. Resolve import/configuration issues
3. Re-run test execution
4. Ensure 100% pass rate before completion

**RESULT: CANNOT COMPLETE UNTIL ALL TESTS PASS**
"""
        
        with open(self.project_root / "TEST_EXECUTION_FAILURE.md", "w") as f:
            f.write(report)
        
        print("📄 Failure report generated: TEST_EXECUTION_FAILURE.md")
```

## Test Execution Validation Checklist

### Pre-Execution Validation
- [ ] Python environment is properly configured
- [ ] All required dependencies are installed (`pytest`, `coverage`, `pytest-cov`)
- [ ] Import paths are correctly configured
- [ ] Test discovery is working (`pytest --collect-only`)
- [ ] Test databases and external services are configured
- [ ] Mock environments are properly set up

### Execution Validation
- [ ] Unit tests execute and pass (`uv run pytest tests/unit/`)
- [ ] Integration tests execute and pass (`uv run pytest tests/integration/`)
- [ ] Security tests execute and pass (`uv run pytest tests/security/`)
- [ ] Performance tests execute and pass (`uv run pytest tests/performance/`)
- [ ] E2E tests execute and pass (`uv run pytest tests/e2e/`)
- [ ] Coverage report generates successfully
- [ ] Coverage meets project requirements

### Post-Execution Validation
- [ ] All test categories report 100% pass rate
- [ ] No import errors or configuration issues
- [ ] Test execution reports are generated
- [ ] Coverage reports are available
- [ ] All environment compatibility issues resolved

## Common Failure Patterns and Solutions

### Import Errors
**Problem**: `ImportError: No module named 'src'`
**Solution**: 
```python
# Add to conftest.py or test setup
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
```

### Configuration Issues
**Problem**: `KeyError: 'DATABASE_URL'`
**Solution**: 
```bash
# Create .env.test file
DATABASE_URL=sqlite:///:memory:
API_KEY=test-key
```

### Test Discovery Problems
**Problem**: `pytest: error: file not found`
**Solution**: 
```bash
# Ensure proper test file naming
# Files must start with test_
mv module_test.py test_module.py
```

### Coverage Issues
**Problem**: Coverage below requirements
**Solution**: 
```bash
# Run with coverage details
uv run pytest --cov=src --cov-report=html --cov-report=term-missing
# Review htmlcov/index.html for uncovered lines
```

## Integration with PRP Commands

### Standard Test Execution Reference
Implementation commands reference this document for consistent test execution patterns:

```markdown
**MANDATORY TEST EXECUTION CHECKPOINT**:
Before claiming completion, implement test execution following:
**Reference**: PRPs/ai_docs/test-execution-patterns.md

**BLOCKING REQUIREMENT**: Cannot proceed without 100% test pass rate.
```

### For Implementation Commands
- **Complex Module Implementation**: References test execution patterns for 6th specialist
- **Simple Feature Implementation**: References test execution patterns for validation
- **Test Suite Management**: References test execution patterns for agent implementation

### For Quality Assurance
Implementation commands must ensure:
- All tests execute in the actual project environment
- 100% pass rate is achieved
- Coverage requirements are met
- Environment compatibility is verified

## Success Criteria

### Test Execution Success
- ✅ All test categories execute without errors
- ✅ 100% test pass rate achieved
- ✅ Coverage requirements met
- ✅ Environment compatibility verified
- ✅ Test execution report generated

### Test Execution Failure
- ❌ Any test category fails
- ❌ Import or configuration errors
- ❌ Coverage below requirements
- ❌ Environment compatibility issues
- ❌ Test execution cannot complete

**CRITICAL**: Test execution failure blocks completion until all issues are resolved and 100% pass rate is achieved.

## Framework Benefits

1. **Consistency**: Standardized test execution across all PRP commands
2. **Reliability**: Comprehensive validation ensures tests actually work
3. **Quality**: 100% pass rate requirement maintains high standards
4. **Debugging**: Detailed failure reports help resolve issues quickly
5. **Automation**: Standardized patterns reduce manual effort
6. **Verification**: Multiple validation checkpoints ensure completeness

## Usage in Implementation Commands

### Reference Pattern
```markdown
**Test Execution Reference**: PRPs/ai_docs/test-execution-patterns.md
**Implementation**: Follow standardized test execution patterns
**Validation**: Use TestExecutionFramework for comprehensive validation
```

### Command Integration
- Commands reference this document for test execution patterns
- Specialists use the provided templates and checklists
- Quality gates are enforced using the standardized success criteria

This reference document ensures that the PRP promise of reliable, high-quality autonomous implementation is maintained through rigorous test execution validation patterns that are consistently applied across all commands.