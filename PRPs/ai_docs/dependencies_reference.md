# Dependencies Reference Guide

## Core Dependencies
- **Flask 3.0+**: Web framework with Blueprint organization
- **SQLAlchemy 2.0+**: Database ORM with modern async support
- **Flask-Migrate**: Database migration management
- **Alembic**: Database schema version control
- **Pydantic v2**: Data validation and serialization
- **requests**: HTTP client for Azure API integration
- **python-dotenv**: Environment variable management
- **azure-identity**: Azure authentication support
- **openai**: OpenAI Python SDK for Azure integration
- **werkzeug**: WSGI utilities and security functions

## Multi-User Dependencies (NEW)
- **celery>=5.3.0**: Background task processing and queue management
- **redis>=5.0.0**: Message broker, result backend, and rate limiting
- **flask-socketio>=5.3.0**: WebSocket support for real-time updates

## Development Dependencies
- **pytest**: Testing framework with fixtures and plugins
- **pytest-cov**: Code coverage reporting with HTML/XML output
- **pytest-mock**: Mocking utilities for tests
- **ruff**: Fast Python linter and formatter (line length: 88)
- **mypy**: Static type checking with strict settings
- **types-requests**: Type stubs for requests library

## Tool Configuration
- **Ruff**: Line length 88, extends pycodestyle, flake8-bugbear, pyupgrade
- **MyPy**: Strict mode with complete type checking
- **Pytest**: Auto-discovery with coverage reporting and test markers
- **Coverage**: Source-based with exclusions for test files

## Flask Application Architecture

**Production-ready vertical slice architecture** with comprehensive infrastructure:

### Core Infrastructure Layers
- **Database Layer** (`src/database/`): SQLAlchemy ORM with migration support
- **Configuration Layer** (`src/config/`): Environment-specific configs and security
- **Monitoring Layer** (`src/monitoring/`): Health checks and metrics collection
- **Core Models** (`src/core/`): Pydantic v2 data models with validation
- **API Layer** (`src/api/`): Flask routes with health endpoints and database operations
- **Features** (`src/features/`): Business logic slices (e.g., `sora_integration/`)
- **Tests**: Co-located with code in `tests/` subdirectories

### Key Components

#### Core Domain Models
- **VideoJob**: Core domain model tracking generation workflow state
- **GenerationParams**: Validated API parameters with serialization
- **APIResponse**: Standardized response format

#### Database Models
- **VideoJobDB**: SQLAlchemy ORM model for job persistence
- **DatabaseManager**: Connection pooling and session management
- **JobRepository**: CRUD operations and database queries

#### Monitoring & Health
- **HealthCheck**: System component health monitoring
- **MetricsCollector**: Performance metrics and statistics
- **SecurityConfig**: Security validation and hardening

#### Integration & Processing
- **SoraClient**: Real Azure OpenAI API integration with rate limiting
- **FileHandler**: Secure video download, validation, and cleanup