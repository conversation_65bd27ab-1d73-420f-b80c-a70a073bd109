# Deployment Guide - Production Deployment & Infrastructure

## Overview

Comprehensive production deployment guide for the multi-user video generation system. This guide covers infrastructure setup, containerization, CI/CD pipelines, monitoring, scaling strategies, and production operations for a production-ready multi-user platform.

## Production Architecture Overview

### System Components
```
┌─────────────────────────────────────────────────────────────┐
│                    Production Architecture                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   Load      │    │   Web App   │    │   Worker    │     │
│  │  Balancer   │──▶ │  Instances  │◀──▶│  Instances  │     │
│  │  (nginx)    │    │  (Flask)    │    │  (Celery)   │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                               │
│                             ▼                               │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   Redis     │    │ PostgreSQL  │    │   File      │     │
│  │  (Queue/    │    │ (Primary    │    │  Storage    │     │
│  │  Session)   │    │  Database)  │    │ (S3/Azure)  │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│              External Services & Monitoring                 │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   Azure     │    │  Monitoring │    │    CDN      │     │
│  │  OpenAI     │    │ (Prometheus │    │ (CloudFlare │     │
│  │   Sora      │    │  / Grafana) │    │ / CloudFront│     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

### Infrastructure Requirements

#### Minimum Production Setup
- **Web Servers**: 2+ instances (2 CPU, 4GB RAM each)
- **Worker Servers**: 3+ instances (4 CPU, 8GB RAM each) 
- **Database**: PostgreSQL cluster (4 CPU, 16GB RAM, 100GB SSD)
- **Cache/Queue**: Redis cluster (2 CPU, 8GB RAM)
- **Load Balancer**: nginx or cloud load balancer
- **File Storage**: Cloud storage (S3, Azure Blob, GCS)
- **CDN**: Content delivery network for static assets

#### Recommended Production Setup
- **Web Servers**: 4+ instances (4 CPU, 8GB RAM each)
- **Worker Servers**: 6+ instances (8 CPU, 16GB RAM each)
- **Database**: PostgreSQL HA cluster (8 CPU, 32GB RAM, 500GB SSD)
- **Cache/Queue**: Redis HA cluster (4 CPU, 16GB RAM)
- **Monitoring**: Dedicated monitoring stack
- **Backup**: Automated backup infrastructure

## Containerization

### Docker Configuration

#### Application Dockerfile
```dockerfile
# Production Dockerfile
FROM python:3.11-slim-bullseye AS base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/opt/venv/bin:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv

# Install uv for fast package management
RUN pip install uv

# Set work directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies
RUN uv sync --frozen --no-dev

# Copy application code
COPY src/ ./src/
COPY templates/ ./templates/
COPY static/ ./static/

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Expose port
EXPOSE 5001

# Default command
CMD ["python", "-m", "gunicorn", "--bind", "0.0.0.0:5001", "--workers", "4", "--timeout", "120", "src.main:create_app()"]
```

#### Worker Dockerfile
```dockerfile
# Worker Dockerfile
FROM python:3.11-slim-bullseye AS worker

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/opt/venv/bin:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv

# Install uv
RUN pip install uv

# Set work directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies
RUN uv sync --frozen --no-dev

# Copy application code
COPY src/ ./src/

# Create non-root user
RUN groupadd -r worker && useradd -r -g worker worker
RUN chown -R worker:worker /app
USER worker

# Health check for worker
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD celery -A src.job_queue.celery_app inspect ping || exit 1

# Default command
CMD ["celery", "-A", "src.job_queue.celery_app", "worker", "--loglevel=info", "--concurrency=4"]
```

### Docker Compose Configuration

#### Development Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=**************************************/sora_dev
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=**************************************/sora_dev
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=sora_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### Production Compose
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  web:
    image: sora-poc:latest
    deploy:
      replicas: 4
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - SECRET_KEY=${SECRET_KEY}
    ports:
      - "5001-5004:5001"
    volumes:
      - shared_uploads:/app/uploads
      - logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  worker:
    image: sora-poc-worker:latest
    deploy:
      replicas: 6
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
    volumes:
      - shared_uploads:/app/uploads
      - logs:/app/logs
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/production.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - shared_uploads:/var/www/uploads:ro
    depends_on:
      - web
    restart: unless-stopped

volumes:
  shared_uploads:
    driver: local
    driver_opts:
      type: nfs
      o: addr=${NFS_SERVER},rw
      device: ":${NFS_PATH}/uploads"
  logs:
    driver: local
```

## Infrastructure as Code

### Terraform Configuration

#### AWS Infrastructure
```hcl
# terraform/aws/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Configuration
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "sora-poc-vpc"
    Environment = var.environment
  }
}

# Subnets
resource "aws_subnet" "public" {
  count = 2

  vpc_id                  = aws_vpc.main.id
  cidr_block              = "10.0.${count.index + 1}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name        = "sora-poc-public-${count.index + 1}"
    Environment = var.environment
  }
}

resource "aws_subnet" "private" {
  count = 2

  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "sora-poc-private-${count.index + 1}"
    Environment = var.environment
  }
}

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "sora-poc-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = aws_subnet.public[*].id

  enable_deletion_protection = var.environment == "production"

  tags = {
    Environment = var.environment
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "sora-poc-cluster"

  capacity_providers = ["FARGATE"]
  default_capacity_provider_strategy {
    capacity_provider = "FARGATE"
    weight            = 100
  }

  tags = {
    Environment = var.environment
  }
}

# ECS Services
resource "aws_ecs_service" "web" {
  name            = "sora-poc-web"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.web.arn
  desired_count   = var.web_instance_count

  deployment_configuration {
    maximum_percent         = 200
    minimum_healthy_percent = 100
  }

  network_configuration {
    security_groups  = [aws_security_group.web.id]
    subnets          = aws_subnet.private[*].id
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.web.arn
    container_name   = "web"
    container_port   = 5001
  }

  depends_on = [aws_lb_listener.web]

  tags = {
    Environment = var.environment
  }
}

# RDS Database
resource "aws_db_instance" "main" {
  identifier = "sora-poc-db"

  engine         = "postgres"
  engine_version = "15.4"
  instance_class = var.db_instance_class

  allocated_storage     = var.db_allocated_storage
  max_allocated_storage = var.db_max_allocated_storage
  storage_type          = "gp3"
  storage_encrypted     = true

  db_name  = "sora_prod"
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.database.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = var.environment != "production"

  tags = {
    Environment = var.environment
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "sora-poc-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_replication_group" "main" {
  description          = "Redis cluster for Sora POC"
  replication_group_id = "sora-poc-redis"

  node_type                  = var.redis_node_type
  port                       = 6379
  parameter_group_name       = "default.redis7"
  num_cache_clusters         = 2
  automatic_failover_enabled = true
  multi_az_enabled          = true

  subnet_group_name  = aws_elasticache_subnet_group.main.name
  security_group_ids = [aws_security_group.redis.id]

  at_rest_encryption_enabled = true
  transit_encryption_enabled = true

  tags = {
    Environment = var.environment
  }
}
```

#### Kubernetes Configuration
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: sora-poc
  labels:
    name: sora-poc

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: sora-poc-config
  namespace: sora-poc
data:
  FLASK_ENV: "production"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  MAX_CONCURRENT_JOBS_PER_SESSION: "3"
  SESSION_LIFETIME_HOURS: "24"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: sora-poc-secrets
  namespace: sora-poc
type: Opaque
data:
  DATABASE_URL: <base64-encoded-database-url>
  AZURE_OPENAI_API_KEY: <base64-encoded-api-key>
  AZURE_OPENAI_ENDPOINT: <base64-encoded-endpoint>
  SECRET_KEY: <base64-encoded-secret-key>

---
# k8s/deployment-web.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sora-poc-web
  namespace: sora-poc
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sora-poc-web
  template:
    metadata:
      labels:
        app: sora-poc-web
    spec:
      containers:
      - name: web
        image: sora-poc:latest
        ports:
        - containerPort: 5001
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sora-poc-secrets
              key: DATABASE_URL
        - name: AZURE_OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: sora-poc-secrets
              key: AZURE_OPENAI_API_KEY
        envFrom:
        - configMapRef:
            name: sora-poc-config
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 5001
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 5001
          initialDelaySeconds: 5
          periodSeconds: 10

---
# k8s/deployment-worker.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sora-poc-worker
  namespace: sora-poc
spec:
  replicas: 6
  selector:
    matchLabels:
      app: sora-poc-worker
  template:
    metadata:
      labels:
        app: sora-poc-worker
    spec:
      containers:
      - name: worker
        image: sora-poc-worker:latest
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sora-poc-secrets
              key: DATABASE_URL
        - name: AZURE_OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: sora-poc-secrets
              key: AZURE_OPENAI_API_KEY
        envFrom:
        - configMapRef:
            name: sora-poc-config
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 4000m
            memory: 8Gi

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: sora-poc-web-service
  namespace: sora-poc
spec:
  selector:
    app: sora-poc-web
  ports:
    - protocol: TCP
      port: 80
      targetPort: 5001
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sora-poc-ingress
  namespace: sora-poc
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - sora.yourdomain.com
    secretName: sora-poc-tls
  rules:
  - host: sora.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sora-poc-web-service
            port:
              number: 80
```

## CI/CD Pipeline

### GitHub Actions Workflow

#### Build and Test Pipeline
```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install uv
      run: curl -LsSf https://astral.sh/uv/install.sh | sh

    - name: Install dependencies
      run: uv sync

    - name: Run security tests
      run: |
        uv run pytest src/tests/test_security.py -v
        uv run bandit -r src/ -f json -o security_report.json || true

    - name: Run unit tests
      run: uv run pytest src/ -v --cov=src --cov-report=xml

    - name: Run integration tests
      run: uv run pytest src/tests/test_integration_*.py -v
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Run performance tests
      run: uv run pytest src/tests/test_performance.py -v --timeout=600

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install uv
      run: curl -LsSf https://astral.sh/uv/install.sh | sh

    - name: Install dependencies
      run: uv sync

    - name: Run ruff
      run: |
        uv run ruff check .
        uv run ruff format --check .

    - name: Run mypy
      run: uv run mypy src/

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Run security checks
      run: |
        # Check for debug endpoints
        if grep -r "/debug/" src/api/routes.py; then
          echo "❌ Debug endpoints found!"
          exit 1
        fi
        
        # Check for hardcoded secrets
        if grep -r -E "(sk-|password.*=|secret.*=)" src/ --exclude-dir=tests; then
          echo "⚠️ Potential hardcoded secrets found"
          exit 1
        fi

    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: auto
```

#### Production Deployment Pipeline
```yaml
# .github/workflows/deploy.yml
name: Production Deployment

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

    - name: Build and push Worker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: Dockerfile.worker
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-worker:${{ steps.meta.outputs.version }}

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment"
        # Add staging deployment commands

  deploy-production:
    needs: [build, deploy-staging]
    runs-on: ubuntu-latest
    environment: production
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment"
        # Add production deployment commands
```

## Environment Configuration

### Production Environment Variables
```bash
# Production .env configuration
# Application Configuration
FLASK_ENV=production
SECRET_KEY=${PRODUCTION_SECRET_KEY}
DEBUG=false

# Database Configuration
DATABASE_URL=${PRODUCTION_DATABASE_URL}
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600

# Redis Configuration
REDIS_HOST=${REDIS_CLUSTER_ENDPOINT}
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_SSL=true

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_SORA_DEPLOYMENT=sora

# Multi-User Configuration
MAX_CONCURRENT_JOBS_PER_SESSION=3
SESSION_LIFETIME_HOURS=24
MAX_SESSIONS_PER_IP=10

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=10

# File Storage
UPLOAD_FOLDER=/app/uploads
MAX_CONTENT_LENGTH=104857600
FILE_CLEANUP_ENABLED=true
FILE_MAX_AGE_HOURS=24

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
S3_BUCKET=${S3_BUCKET_NAME}

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
LOG_LEVEL=INFO
SENTRY_DSN=${SENTRY_DSN}

# Celery Configuration
CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:6379/0
CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:6379/0
CELERY_WORKER_CONCURRENCY=4
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json

# Security Configuration
CORS_ORIGINS=${ALLOWED_ORIGINS}
TRUSTED_PROXIES=${TRUSTED_PROXY_IPS}
```

### Configuration Management Script
```python
# scripts/configure_production.py
#!/usr/bin/env python3
"""Production configuration validation and setup."""

import os
import sys
from typing import Dict, List, Optional

class ProductionConfigValidator:
    """Validate production configuration requirements."""
    
    REQUIRED_VARS = [
        'SECRET_KEY',
        'DATABASE_URL', 
        'REDIS_HOST',
        'AZURE_OPENAI_API_KEY',
        'AZURE_OPENAI_ENDPOINT'
    ]
    
    SECURITY_VARS = [
        'SECRET_KEY',
        'DATABASE_URL',
        'AZURE_OPENAI_API_KEY',
        'REDIS_PASSWORD'
    ]
    
    def validate_environment(self) -> Dict[str, List[str]]:
        """Validate production environment configuration."""
        results = {
            'missing': [],
            'weak': [],
            'insecure': [],
            'warnings': []
        }
        
        # Check required variables
        for var in self.REQUIRED_VARS:
            if not os.getenv(var):
                results['missing'].append(var)
        
        # Check secret strength
        secret_key = os.getenv('SECRET_KEY', '')
        if len(secret_key) < 32:
            results['weak'].append('SECRET_KEY too short (minimum 32 characters)')
        
        # Check for debug mode
        if os.getenv('DEBUG', '').lower() == 'true':
            results['insecure'].append('DEBUG=true in production')
        
        # Check for development settings
        if os.getenv('FLASK_ENV') == 'development':
            results['insecure'].append('FLASK_ENV=development in production')
        
        return results
    
    def generate_secrets(self) -> Dict[str, str]:
        """Generate secure secrets for production."""
        import secrets
        
        return {
            'SECRET_KEY': secrets.token_urlsafe(64),
            'REDIS_PASSWORD': secrets.token_urlsafe(32),
            'DB_PASSWORD': secrets.token_urlsafe(24)
        }
    
    def check_external_services(self) -> Dict[str, bool]:
        """Check connectivity to external services."""
        results = {}
        
        # Test database connection
        try:
            import psycopg2
            db_url = os.getenv('DATABASE_URL')
            if db_url:
                conn = psycopg2.connect(db_url)
                conn.close()
                results['database'] = True
            else:
                results['database'] = False
        except Exception:
            results['database'] = False
        
        # Test Redis connection
        try:
            import redis
            redis_client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD')
            )
            redis_client.ping()
            results['redis'] = True
        except Exception:
            results['redis'] = False
        
        # Test Azure OpenAI
        try:
            import requests
            endpoint = os.getenv('AZURE_OPENAI_ENDPOINT')
            api_key = os.getenv('AZURE_OPENAI_API_KEY')
            
            if endpoint and api_key:
                # Simple connectivity test
                response = requests.get(
                    f"{endpoint}/openai/deployments",
                    headers={'api-key': api_key},
                    timeout=10
                )
                results['azure_openai'] = response.status_code < 500
            else:
                results['azure_openai'] = False
        except Exception:
            results['azure_openai'] = False
        
        return results

def main():
    """Main configuration validation."""
    validator = ProductionConfigValidator()
    
    print("🔧 Production Configuration Validation")
    print("=" * 50)
    
    # Validate environment
    validation = validator.validate_environment()
    
    if validation['missing']:
        print(f"❌ Missing required variables: {', '.join(validation['missing'])}")
        return 1
    
    if validation['insecure']:
        print(f"🚨 Security issues: {', '.join(validation['insecure'])}")
        return 1
    
    if validation['weak']:
        print(f"⚠️  Weak configuration: {', '.join(validation['weak'])}")
    
    # Check external services
    print("\n🔗 External Service Connectivity")
    services = validator.check_external_services()
    
    for service, status in services.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {service.title()}: {'Connected' if status else 'Failed'}")
    
    if not all(services.values()):
        print("\n⚠️  Some services are not accessible. Check configuration.")
        return 1
    
    print("\n✅ Production configuration validated successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
```

## Monitoring and Observability

### Prometheus Configuration
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'sora-poc-web'
    static_configs:
      - targets: ['web:5001']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'sora-poc-workers'
    static_configs:
      - targets: ['worker1:8080', 'worker2:8080', 'worker3:8080']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

### Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "Sora POC Production Dashboard",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph", 
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Jobs",
        "type": "stat",
        "targets": [
          {
            "expr": "celery_active_tasks",
            "legendFormat": "Active Tasks"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"4..|5..\"}[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

### Application Metrics
```python
# src/monitoring/production_metrics.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import time

# Request metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

# Business metrics
VIDEO_GENERATION_COUNT = Counter(
    'video_generation_total',
    'Total video generation requests',
    ['status']
)

VIDEO_GENERATION_DURATION = Histogram(
    'video_generation_duration_seconds',
    'Video generation duration in seconds'
)

ACTIVE_SESSIONS = Gauge(
    'active_sessions_total',
    'Number of active user sessions'
)

QUEUE_DEPTH = Gauge(
    'queue_depth_total',
    'Number of jobs in queue'
)

class ProductionMetrics:
    """Production metrics collection."""
    
    def __init__(self):
        self.start_metrics_server()
    
    def start_metrics_server(self, port: int = 8080):
        """Start Prometheus metrics server."""
        start_http_server(port)
    
    def record_request(self, method: str, endpoint: str, status: int, duration: float):
        """Record HTTP request metrics."""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_video_generation(self, status: str, duration: float = None):
        """Record video generation metrics."""
        VIDEO_GENERATION_COUNT.labels(status=status).inc()
        if duration:
            VIDEO_GENERATION_DURATION.observe(duration)
    
    def update_session_count(self, count: int):
        """Update active session count."""
        ACTIVE_SESSIONS.set(count)
    
    def update_queue_depth(self, depth: int):
        """Update queue depth."""
        QUEUE_DEPTH.set(depth)
```

## Scaling Strategies

### Horizontal Scaling Configuration

#### Auto-scaling with Kubernetes
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sora-poc-web-hpa
  namespace: sora-poc
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sora-poc-web
  minReplicas: 4
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 4
        periodSeconds: 60

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sora-poc-worker-hpa
  namespace: sora-poc
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sora-poc-worker
  minReplicas: 6
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: celery_queue_depth
      target:
        type: AverageValue
        averageValue: "10"
```

#### Load Balancer Configuration
```nginx
# nginx/production.conf
upstream sora_web {
    least_conn;
    server web1:5001 max_fails=3 fail_timeout=30s;
    server web2:5001 max_fails=3 fail_timeout=30s;
    server web3:5001 max_fails=3 fail_timeout=30s;
    server web4:5001 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name sora.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name sora.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Main application
    location / {
        proxy_pass http://sora_web;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Static files with caching
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Health check
    location /health {
        access_log off;
        proxy_pass http://sora_web;
    }

    # Metrics (internal only)
    location /metrics {
        allow 10.0.0.0/8;
        deny all;
        proxy_pass http://sora_web;
    }
}
```

## Backup and Disaster Recovery

### Database Backup Strategy
```bash
#!/bin/bash
# scripts/backup_database.sh

set -e

# Configuration
BACKUP_DIR="/backups/database"
RETENTION_DAYS=30
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="sora_backup_${TIMESTAMP}.sql.gz"

# Create backup directory
mkdir -p ${BACKUP_DIR}

# Create database backup
echo "Creating database backup: ${BACKUP_FILE}"
pg_dump ${DATABASE_URL} | gzip > ${BACKUP_DIR}/${BACKUP_FILE}

# Verify backup
if [ $? -eq 0 ]; then
    echo "✅ Database backup completed successfully"
    
    # Upload to cloud storage
    aws s3 cp ${BACKUP_DIR}/${BACKUP_FILE} s3://${BACKUP_BUCKET}/database/ --storage-class STANDARD_IA
    
    echo "✅ Backup uploaded to S3"
else
    echo "❌ Database backup failed"
    exit 1
fi

# Clean up old backups
find ${BACKUP_DIR} -name "sora_backup_*.sql.gz" -mtime +${RETENTION_DAYS} -delete

echo "🧹 Old backups cleaned up (older than ${RETENTION_DAYS} days)"
```

### Disaster Recovery Plan
```yaml
# disaster_recovery.yml
disaster_recovery:
  rto: 4 hours  # Recovery Time Objective
  rpo: 1 hour   # Recovery Point Objective
  
  procedures:
    database_recovery:
      - Check backup integrity
      - Provision new database instance
      - Restore from latest backup
      - Update connection strings
      - Verify data integrity
      
    application_recovery:
      - Deploy to backup region
      - Update DNS records
      - Scale services to handle load
      - Monitor performance
      
    redis_recovery:
      - Provision new Redis cluster
      - Update connection configuration
      - Clear any corrupted data
      - Restart worker processes
      
  monitoring:
    - Database replication lag
    - Backup completion status
    - Cross-region connectivity
    - Service health checks
```

## Production Operations

### Health Monitoring
```python
# scripts/production_health_check.py
#!/usr/bin/env python3
"""Production health monitoring script."""

import requests
import time
import json
from typing import Dict, Any

class ProductionHealthMonitor:
    """Monitor production system health."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.endpoints = [
            '/health',
            '/health/database',
            '/health/redis',
            '/metrics'
        ]
    
    def check_health(self) -> Dict[str, Any]:
        """Comprehensive health check."""
        results = {
            'timestamp': time.time(),
            'overall_status': 'healthy',
            'endpoints': {},
            'metrics': {}
        }
        
        # Check each endpoint
        for endpoint in self.endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                results['endpoints'][endpoint] = {
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'healthy': response.status_code == 200
                }
                
                if endpoint == '/metrics':
                    results['metrics'] = self._parse_metrics(response.text)
                    
            except Exception as e:
                results['endpoints'][endpoint] = {
                    'status_code': 0,
                    'error': str(e),
                    'healthy': False
                }
                results['overall_status'] = 'unhealthy'
        
        return results
    
    def _parse_metrics(self, metrics_text: str) -> Dict[str, float]:
        """Parse Prometheus metrics."""
        metrics = {}
        for line in metrics_text.split('\n'):
            if line.startswith('http_requests_total'):
                # Parse key metrics
                pass
        return metrics

def main():
    """Main health monitoring."""
    monitor = ProductionHealthMonitor('https://sora.yourdomain.com')
    
    while True:
        health = monitor.check_health()
        
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] "
              f"Overall Status: {health['overall_status']}")
        
        # Check for issues
        unhealthy_endpoints = [
            endpoint for endpoint, data in health['endpoints'].items()
            if not data.get('healthy', False)
        ]
        
        if unhealthy_endpoints:
            print(f"⚠️  Unhealthy endpoints: {', '.join(unhealthy_endpoints)}")
        
        time.sleep(60)  # Check every minute

if __name__ == "__main__":
    main()
```

### Log Management
```yaml
# logging/fluent-bit.conf
[SERVICE]
    Flush         1
    Log_Level     info
    Daemon        off
    Parsers_File  parsers.conf

[INPUT]
    Name              tail
    Path              /app/logs/*.log
    Parser            json
    Tag               sora-poc.*
    Refresh_Interval  5

[FILTER]
    Name                kubernetes
    Match               sora-poc.*
    Kube_URL            https://kubernetes.default.svc:443
    Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token

[OUTPUT]
    Name  es
    Match *
    Host  elasticsearch.logging.svc.cluster.local
    Port  9200
    Index sora-poc-logs
    Type  _doc
```

## Security in Production

### SSL/TLS Configuration
```bash
# scripts/setup_ssl.sh
#!/bin/bash
# SSL certificate setup with Let's Encrypt

# Install certbot
apt-get update
apt-get install -y certbot python3-certbot-nginx

# Generate certificates
certbot --nginx -d sora.yourdomain.com --email <EMAIL> --agree-tos --non-interactive

# Set up auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -

echo "✅ SSL certificates configured and auto-renewal set up"
```

### Firewall Configuration
```bash
# scripts/configure_firewall.sh
#!/bin/bash
# Production firewall configuration

# Reset iptables
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow SSH (change port as needed)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# Allow HTTP/HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Allow health checks from load balancer
iptables -A INPUT -s 10.0.0.0/8 -p tcp --dport 5001 -j ACCEPT

# Rate limiting
iptables -A INPUT -p tcp --dport 443 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "✅ Firewall configured for production"
```

## Performance Optimization

### Application Performance
```python
# src/performance/optimization.py
"""Production performance optimizations."""

import functools
import time
from typing import Any, Callable

def cache_with_ttl(ttl_seconds: int = 300):
    """Decorator for caching function results with TTL."""
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key = str(args) + str(sorted(kwargs.items()))
            current_time = time.time()
            
            # Check cache
            if key in cache:
                result, timestamp = cache[key]
                if current_time - timestamp < ttl_seconds:
                    return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache[key] = (result, current_time)
            
            # Clean old entries
            cache = {
                k: v for k, v in cache.items()
                if current_time - v[1] < ttl_seconds
            }
            
            return result
        return wrapper
    return decorator

class ConnectionPool:
    """Production-optimized connection pooling."""
    
    def __init__(self, max_connections: int = 20):
        self.max_connections = max_connections
        self.pool = []
        self.in_use = set()
    
    def get_connection(self):
        """Get connection from pool."""
        if self.pool:
            conn = self.pool.pop()
            self.in_use.add(conn)
            return conn
        
        if len(self.in_use) < self.max_connections:
            conn = self._create_connection()
            self.in_use.add(conn)
            return conn
        
        raise Exception("Connection pool exhausted")
    
    def return_connection(self, conn):
        """Return connection to pool."""
        if conn in self.in_use:
            self.in_use.remove(conn)
            if self._is_connection_healthy(conn):
                self.pool.append(conn)
            else:
                self._close_connection(conn)
```

## Troubleshooting Guide

### Common Production Issues

#### High Memory Usage
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head -20

# Check for memory leaks
python3 -m memory_profiler src/main.py

# Docker container memory
docker stats --no-stream
```

#### Database Performance Issues
```sql
-- Check slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Check connection count
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE state = 'active';

-- Check table sizes
SELECT schemaname, tablename, 
       pg_size_pretty(size) as size,
       pg_size_pretty(total_size) as total_size
FROM (
    SELECT schemaname, tablename,
           pg_relation_size(schemaname||'.'||tablename) as size,
           pg_total_relation_size(schemaname||'.'||tablename) as total_size
    FROM pg_tables
) t
ORDER BY total_size DESC;
```

#### Redis Issues
```bash
# Check Redis memory usage
redis-cli info memory

# Check connected clients
redis-cli info clients

# Monitor Redis commands
redis-cli monitor

# Check slow log
redis-cli slowlog get 10
```

This comprehensive deployment guide provides everything needed to deploy the multi-user video generation system to production with proper infrastructure, monitoring, security, and operational procedures.