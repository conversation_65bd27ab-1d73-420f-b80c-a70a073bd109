# Feature Request: Google Veo3 API Testing and Integration

## SCOPE:

production - Complete Google Veo3 API testing suite with authentication, video generation workflows, and dual-provider integration for production-ready multi-user video generation platform

## FEATURE:

Comprehensive Google Veo3 API testing implementation enabling immediate API validation and integration with existing Azure Sora production system. This feature provides complete API testing workflow including authentication setup, connection verification, text-to-video generation, image-to-video generation, and video retrieval functionality.

### Core Testing Requirements Enhanced with Technical Specifications:

- **Authentication with Google Cloud**: Service account and API key authentication using google-genai Python SDK
  - _Exact functionality_: Implement OAuth 2.0 and service account authentication with credential validation
  - _Performance requirements_: Authentication setup <200ms, credential validation <100ms
  - _Error handling_: Handle 401 Unauthorized, 403 Forbidden, token expiration with exponential backoff
  - _Integration points_: Use existing ConfigurationFactory pattern with SecretStr for Google client secrets

- **API Connection Testing**: Verify connectivity to Google GenAI Video API with proper credentials validation
  - _Exact functionality_: Health check endpoint validation, API availability testing, region verification (US-only)
  - _Performance requirements_: Connection validation <500ms, health check response <200ms
  - _Error handling_: Network timeouts, DNS resolution failures, regional access restrictions
  - _Integration points_: Integrate with existing health check infrastructure in `src/monitoring/health_check.py`

- **Text-to-Video Generation**: Create videos from text prompts using Veo3 model with configurable parameters
  - _Exact functionality_: Support `veo-2.0-generate-001` model with duration (2-60s), aspect ratio (16:9, 9:16, 1:1), FPS (24-30)
  - _Performance requirements_: Generation initiation <1s, polling interval 20s, total timeout 1800s
  - _Error handling_: Content policy violations, rate limiting (10 req/min), generation failures at different stages
  - _Integration points_: Follow existing async pattern from `src/features/sora_integration/client.py`

- **Image-to-Video Generation**: Generate videos from image inputs with text prompts using Veo3 capabilities
  - _Exact functionality_: Support PNG/JPEG image inputs, text prompt modification, same video parameters as text-to-video
  - _Performance requirements_: Image validation <100ms, generation same as text-to-video requirements
  - _Error handling_: Image format validation, size limits (10MB max), content policy on images
  - _Integration points_: Implement missing image security pipeline with PIL validation following project patterns

- **Video Retrieval and Processing**: Poll generation status, retrieve completed videos, and handle WebM/VP9 conversion for browser compatibility
  - _Exact functionality_: Operation polling with status updates, video download from GCS URIs, FFmpeg conversion pipeline
  - _Performance requirements_: Status polling <100ms response, video download streaming, conversion <30s for 60s video
  - _Error handling_: Download failures, conversion errors, temporary file cleanup, storage limits
  - _Integration points_: Use existing WebM/VP9 conversion from `src/features/sora_integration/video_downloader.py`

- **Error Handling**: Comprehensive error scenarios including rate limiting, content policy violations, and generation failures
  - _Exact functionality_: Structured error categorization, retry strategies, user-friendly error messages
  - _Performance requirements_: Error classification <50ms, retry logic with exponential backoff (5s → 30s max)
  - _Error handling_: Map Google API errors to unified error responses, sanitize sensitive data from logs
  - _Integration points_: Follow existing error handling patterns from `src/job_queue/tasks.py`

- **Performance Validation**: Support for concurrent operations and integration with existing 15+ user concurrent testing framework
  - _Exact functionality_: Concurrent generation tracking, queue fairness, resource management
  - _Performance requirements_: 15+ concurrent users, <2% error rate, <50MB memory increase per generation
  - _Error handling_: Resource exhaustion, queue overflow, performance degradation detection
  - _Integration points_: Extend existing load testing framework in `src/tests/test_load_testing.py`

### Integration with Existing System Enhanced:

- **Provider Factory Integration**: Seamless integration with existing VideoProviderFactory pattern for unified provider switching
  - _Implementation details_: Use existing provider registration pattern, support dynamic provider availability checking
  - _Performance requirements_: Provider instantiation <50ms (cached), factory creation <10ms
  - _Configuration_: Environment-based provider switching (`USE_MOCK_VEO=true/false`)

- **Database Schema Compatibility**: Utilize existing F1 database extensions for dual-provider support with api_provider field
  - _Implementation details_: Leverage `api_provider VARCHAR(20) DEFAULT 'azure_sora'`, `input_image_path TEXT`, `audio_generated BOOLEAN`
  - _Performance requirements_: Provider-specific queries <100ms using existing indexes
  - _Migration safety_: Zero-downtime deployment with backward-compatible schema changes

- **Job Queue Integration**: Compatible with existing Celery task system for background video generation processing
  - _Implementation details_: Use existing `process_video_generation` task pattern, provider-aware job routing
  - _Performance requirements_: Task dispatch <100ms, queue processing fairness across providers
  - _Error handling_: Task failure recovery, retry coordination across providers

- **Zero Regression Requirement**: Maintain 100% Azure Sora functionality while adding Google Veo3 capabilities
  - _Implementation details_: Comprehensive regression testing, provider isolation validation
  - _Performance requirements_: No performance degradation for existing Azure Sora workflows
  - _Quality gates_: All existing 275+ tests must continue passing at 89%+ rate

- **Configuration Management**: Environment-based provider switching using existing Veo3Settings configuration
  - _Implementation details_: Follow existing configuration factory pattern, SecretStr for sensitive data
  - _Performance requirements_: Settings load <50ms, configuration validation <100ms
  - _Security_: Credential sanitization, environment variable priority system

### Technical Implementation Enhanced:

- **Google GenAI SDK**: Use googleapis/python-genai library (NOT deprecated google-generativeai) with proper authentication and video generation APIs
  - _Version requirement_: `google-genai>=0.1.0` (latest unified SDK)
  - _Authentication patterns_: Service account preferred, API key for development
  - _Integration approach_: Follow Azure Sora client architecture with component orchestration

- **Async Operations**: Long-running video generation with proper polling and status management
  - _Implementation details_: Async/await pattern with background task management, concurrent state tracking
  - _Performance requirements_: Non-blocking generation initiation, efficient polling with exponential backoff
  - _Pattern compliance_: Follow existing async patterns from mock client implementation

- **Video Format Handling**: WebM/VP9 conversion pipeline for browser compatibility (following existing Azure Sora patterns)
  - _Implementation details_: FFmpeg integration with VP9 codec, HTTP Range request support
  - _Performance requirements_: Conversion <30s for 60s video, streaming delivery support
  - _Resource management_: Temporary file cleanup, memory management during conversion

- **Performance Monitoring**: Integration with existing monitoring and health check systems
  - _Implementation details_: Provider-specific health endpoints, performance baseline tracking
  - _Metrics collection_: Response times, error rates, concurrent user metrics
  - _Alerting integration_: Provider availability, performance degradation detection

- **Testing Framework**: Comprehensive pytest test suite with mock and real API testing capabilities
  - _Implementation details_: Co-located tests, six test markers (unit, integration, performance, security, slow, asyncio)
  - _Coverage requirements_: 90%+ test coverage, provider isolation testing
  - _Test patterns_: Follow existing e2e testing patterns from `src/tests/test_integration_e2e.py`

## EXAMPLES:

### Verified Codebase Integration Points:

- **`src/features/sora_integration/client.py`** - Azure Sora client pattern for Google Veo3 implementation
  - _Key functions_: `SoraClient.__init__()`, `create_video_job()`, `get_job_status()`
  - _Pattern_: Component orchestration with HTTP client + Job manager + Video downloader
  - _Usage_: Follow same architecture with GoogleVeo3Client using google-genai SDK instead of Azure API

- **`src/features/video_generation/provider_factory.py`** - Factory pattern for dual-provider support
  - _Key functions_: `create_provider()`, `get_available_providers()`, `register_provider()`
  - _Pattern_: Provider registration with dynamic availability checking
  - _Usage_: Register Veo3 provider with availability based on GOOGLE_PROJECT_ID and credentials

- **`src/config/veo3_settings.py`** - Complete Google Veo3 configuration with environment variable support
  - _Key classes_: `Veo3Settings`, `Veo3ProviderConfig`, `validate_veo3_environment()`
  - _Pattern_: Pydantic v2 BaseSettings with SecretStr for sensitive data
  - _Usage_: Use existing configuration factory pattern for credential management

- **`src/features/veo3_integration/mock_client.py`** - Mock implementation pattern for development workflow
  - _Key classes_: `MockVeo3Client`, `ConcurrentStateManager`, `MockGenerationState`
  - _Pattern_: VideoProviderInterface compliance with realistic timing simulation
  - _Usage_: Enable mock-first development with `USE_MOCK_VEO=true` environment variable

- **`src/database/models.py`** - F1 database schema extensions with api_provider field for dual-provider support
  - _Key fields_: `api_provider`, `input_image_path`, `audio_generated`, `request_metadata`
  - _Pattern_: Backward-compatible schema extensions with proper indexing
  - _Usage_: Provider-specific job tracking with performance-optimized queries

- **`src/job_queue/tasks.py`** - Background processing pattern for long-running video generation
  - _Key functions_: `process_video_generation()`, task retry configuration, error handling
  - _Pattern_: Celery task with exponential backoff, structured error handling
  - _Usage_: Extend existing task for provider-aware processing with same retry patterns

- **`src/tests/test_integration_e2e.py`** - End-to-end testing pattern for complete workflow validation
  - _Key classes_: `TestVideoGenerationIntegration`, provider-specific test fixtures
  - _Pattern_: Full workflow testing from job creation to video playback validation
  - _Usage_: Extend existing e2e patterns for dual-provider testing scenarios

### Additional Patterns Discovered:

- **`src/features/sora_integration/http_client.py`** - HTTP authentication and rate limiting patterns
  - _Key functions_: Header authentication, retry logic, rate limiting integration
  - _Pattern_: Session management with connection pooling and error recovery
  - _Usage_: Adapt for Google OAuth 2.0 with Bearer token authentication

- **`src/database/integrations/provider_integrations.py`** - Provider integration validation patterns
  - _Key functions_: Zero-regression validation, backward compatibility checks
  - _Pattern_: Comprehensive integration testing with performance validation
  - _Usage_: Validate Veo3 integration doesn't impact Azure Sora performance

- **`src/monitoring/performance_baselines.py`** - Performance baseline management patterns
  - _Key functions_: Baseline measurement, regression detection, metric collection
  - _Pattern_: JSON-stored performance baselines with threshold monitoring
  - _Usage_: Establish Veo3-specific performance baselines and regression detection

### Existing Architecture Patterns Enhanced:

- **Vertical Slice Architecture**: Co-located tests and feature modules with comprehensive CLAUDE.md documentation
  - _Implementation guidance_: Each Veo3 module includes tests/ subdirectory and module-specific CLAUDE.md
  - _Pattern compliance_: Follow existing structure with complete functionality stacks per module

- **Provider Interface Pattern**: Unified VideoProviderInterface for seamless provider switching
  - _Implementation guidance_: Veo3 client must implement all interface methods with consistent error handling
  - _Performance requirements_: Interface overhead <5ms, consistent response format across providers

- **Configuration Factory Pattern**: Environment-based configuration with validation and performance optimization
  - _Implementation guidance_: Use existing factory pattern with caching, never hardcode defaults in models
  - _Security compliance_: Follow SecretStr patterns for sensitive data, environment variable priority

- **Mock-First Development**: Complete mock provider implementation enabling development without API credentials
  - _Implementation guidance_: Mock client provides 100% interface compliance with realistic timing simulation
  - _Development workflow_: All downstream development possible without Google Cloud credentials

## DOCUMENTATION:

### Verified Google Veo3 API Documentation:

- **[Google GenAI Python SDK](https://googleapis.github.io/python-genai/)** - Official Python SDK with video generation examples
  - _Verified sections_: Video generation methods, authentication patterns, configuration options
  - _Key implementation guidance_: Use unified SDK (not deprecated libraries), service account auth preferred
  - _Version considerations_: Latest unified SDK required, includes video generation support

- **[Google Veo3 API Reference](https://ai.google.dev/gemini-api/docs/video)** - Complete API documentation for video generation
  - _Verified sections_: Video generation endpoints, model specifications, parameter documentation
  - _Key implementation guidance_: `veo-2.0-generate-001` model, operation polling patterns, GCS integration
  - _Critical requirements_: US-only access, allowlist requirement for production use

- **[Google Cloud Authentication](https://cloud.google.com/docs/authentication/getting-started)** - Service account setup and credential management
  - _Verified sections_: Service account creation, Application Default Credentials, OAuth 2.0 flows
  - _Key implementation guidance_: ADC preferred for production, OAuth for development
  - _Security considerations_: JSON key file protection, environment variable security

- **[Vertex AI Veo Documentation](https://cloud.google.com/vertex-ai/generative-ai/docs/video/generate-videos)** - Comprehensive video generation guide
  - _Verified sections_: Model capabilities, parameter optimization, rate limiting guidelines
  - _Key implementation guidance_: 10 requests/minute limit, exponential backoff required
  - _Pricing considerations_: $0.75/second pricing model, budget planning required

- **[Google GenAI Video Examples](https://github.com/googleapis/python-genai/blob/main/README.md)** - Code examples for text-to-video and image-to-video generation
  - _Verified sections_: Text-to-video, image-to-video, operation polling examples
  - _Key implementation guidance_: Async operation patterns, status polling with 20s intervals
  - _Integration patterns_: Direct adaptation possible for Flask/Celery integration

### Additional Documentation Resources Discovered:

- **[Google GenAI SDK Installation Guide](https://googleapis.github.io/python-genai/installation.html)** - Setup and dependency management
  - _Installation requirements_: Python 3.8+, specific dependency versions
  - _Virtual environment guidance_: UV package management compatibility confirmed

- **[Google Cloud Project Setup](https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com)** - API enablement and project configuration
  - _Required APIs_: Generative Language API, Identity and Access Management API
  - _Allowlist process_: Production access requires Google approval, estimated 2-4 weeks

- **[Rate Limiting Best Practices](https://cloud.google.com/docs/quota)** - Quota and rate limit management
  - _Implementation patterns_: Exponential backoff, request queuing, graceful degradation
  - _Monitoring guidance_: Quota usage tracking, rate limit alerting

### Project-Specific Integration Documentation:

- **[Project CLAUDE.md](./CLAUDE.md)** - Complete project architecture and development standards
  - _Development standards_: Ruff formatting, MyPy type checking, Pytest framework requirements
  - _Architecture guidance_: Vertical slice architecture, provider interface patterns, AI agent reuse

- **[Veo3 Integration Project Plan](./PRPs/project-planning/google-veo3-integration/5-project-plan-google-veo3-integration.md)** - Comprehensive dual-provider integration strategy
  - _Module specifications_: 9 modules (F1-F4, C1-C3, I1-I2) with dependency mapping
  - _Implementation phases_: Foundation → Core → Integration with quality gates

- **[Development Standards](./PRPs/ai_docs/development_standards.md)** - Quality gates and testing patterns
  - _Quality requirements_: 90%+ test coverage, performance baselines, security validation
  - _Code standards_: 500-line file limits, 50-line function limits, comprehensive type hints

- **[Testing Framework](./src/tests/CLAUDE.md)** - 275+ test comprehensive testing infrastructure
  - _Test categories_: Unit, integration, performance, security testing with co-located structure
  - _Test execution_: Pytest markers, load testing patterns, OWASP security validation

## OTHER CONSIDERATIONS:

### Authentication and Security Enhanced:

- **Google Cloud Project Setup**: Requires Google Cloud project with GenAI API enabled and US-region access
  - _Critical gotcha_: Veo3 is **allowlist-only** - production access requires Google approval (2-4 weeks)
  - _Regional restrictions_: US-only access requirement, ensure proper project configuration
  - _API enablement_: Generative Language API must be enabled, billing account required

- **Service Account Credentials**: JSON key file or Application Default Credentials for production authentication
  - _Security pattern_: Follow existing SecretStr pattern from `veo3_settings.py` for credential protection
  - _Environment variables_: `GOOGLE_APPLICATION_CREDENTIALS` for service account, `GOOGLE_PROJECT_ID` required
  - _Credential validation_: Implement existing validation pattern with comprehensive error messages

- **API Key Authentication**: Alternative authentication method for development and testing
  - _Development workflow_: API key suitable for development, service account for production
  - _Rate limiting_: API key has lower rate limits than service account authentication
  - _Security considerations_: API key rotation, environment variable protection

- **Environment Variable Security**: Secure credential management following existing project patterns
  - _Pattern compliance_: Use existing ConfigurationService priority system
  - _Credential sanitization_: Follow existing header sanitization pattern for sensitive data logging
  - _Validation_: Comprehensive credential validation without exposing secrets in logs

- **Rate Limiting**: Google Veo3 API has rate limits (10 requests/minute for video generation) requiring proper throttling implementation
  - _Implementation pattern_: Extend existing Redis-based rate limiter with provider-specific limits
  - _Error handling_: 429 Too Many Requests with exponential backoff (5s → 30s max intervals)
  - _Monitoring_: Rate limit usage tracking, quota monitoring, degraded service detection

### Performance and Scalability Enhanced:

- **Async Generation Pattern**: Video generation is long-running (10-60 seconds) requiring proper async handling and status polling
  - _Critical pattern_: SoraClient is synchronous - Veo3 must provide both sync/async interfaces for compatibility
  - _Performance requirements_: Generation initiation <1s, polling with 20s intervals, 1800s total timeout
  - _Resource management_: Background task coordination, memory management during generation

- **Concurrent Operation Support**: Must maintain existing 15+ concurrent user capability across both providers
  - _Performance baseline_: 15+ concurrent users with <2% error rate across providers
  - _Memory constraints_: <50MB increase per concurrent generation, total system limit monitoring
  - _Queue fairness_: Prevent provider monopolization, maintain fair processing across Azure/Veo3

- **WebM/VP9 Conversion**: Browser compatibility requires video format conversion (following existing Azure Sora patterns)
  - _Critical gotcha_: Browser environment lacks H.264 codec support, FFmpeg conversion mandatory
  - _Implementation pattern_: Use existing conversion pipeline from `video_downloader.py:150-162`
  - _Performance requirements_: Conversion <30s for 60s video, streaming delivery with HTTP Range support

- **Resource Management**: Proper cleanup of temporary files and memory management for video processing
  - _File management_: Videos can be 50-200MB, require streaming support and cleanup automation
  - _Memory monitoring_: Connection pooling (20-pool size, 30 max overflow), Redis connection management
  - _Process management_: FFmpeg subprocess cleanup, error handling for conversion failures

- **Provider Health Monitoring**: Integration with existing health check and monitoring infrastructure
  - _Health endpoints_: Provider-specific health checks, dependency validation
  - _Performance metrics_: Response times, error rates, queue depth monitoring
  - _Alerting integration_: Provider degradation detection, automatic failover capabilities

### Technical Integration Challenges Enhanced:

- **Dual-Provider State Management**: Coordinate between Azure Sora and Google Veo3 generation states
  - _State isolation_: Provider-specific state management, session isolation per IP
  - _Database coordination_: Provider-tagged jobs, unified status tracking across providers
  - _WebSocket routing_: Real-time updates must route to correct sessions based on provider

- **Error Handling Consistency**: Unified error responses across different provider API patterns
  - _Error mapping_: Map Google API errors to existing error response format
  - _Structured handling_: Three-tier retry strategy (transient, rate limit, permanent errors)
  - _Logging sanitization_: Remove sensitive authentication data from error logs consistently

- **Configuration Management**: Environment-based provider switching without breaking existing workflows
  - _Dynamic switching_: Runtime provider availability checking based on credentials
  - _Configuration isolation_: Provider-specific configurations without cross-contamination
  - _Rollback capability_: Environment variable-based rollback for provider availability

- **Database Schema Migration**: Safe deployment of F1 database extensions with zero downtime
  - _Migration safety_: Backward-compatible schema changes, existing queries unaffected
  - _Performance validation_: Provider-specific indexes maintain <100ms query performance
  - _Rollback procedures_: Schema rollback capability, data integrity validation

- **Testing Strategy**: Comprehensive testing of both mock and real API integration scenarios
  - _Mock isolation_: Complete development workflow using mock provider without credentials
  - _Provider isolation_: Separate test configurations, database isolation per provider
  - _Integration testing_: Cross-provider compatibility, performance baseline maintenance

### Development Workflow Enhanced:

- **Mock-First Development**: Complete development possible using existing F3 mock provider without API credentials
  - _Implementation approach_: 100% interface compliance with realistic timing simulation
  - _Development efficiency_: All downstream modules (C1, C2, C3, I1) can be developed without Google Cloud access
  - _Testing workflow_: Comprehensive mock testing before real API integration

- **Gradual Integration**: Incremental testing from mock → real API → production deployment
  - _Migration path_: Environment variable switching (`USE_MOCK_VEO=true/false`)
  - _Validation stages_: Mock validation → real API testing → production deployment
  - _Risk mitigation_: Rollback capability at each integration stage

- **Zero Regression Testing**: Comprehensive validation that Azure Sora functionality remains unchanged
  - _Test requirements_: All existing 275+ tests must maintain 89%+ pass rate
  - _Performance validation_: No degradation in Azure Sora response times or error rates
  - _Integration isolation_: Veo3 integration cannot impact existing Azure Sora workflows

- **Performance Benchmarking**: Maintain <2% error rate and 15+ concurrent user performance across both providers
  - _Baseline maintenance_: Provider-specific performance baselines with regression detection
  - _Load testing extension_: Extend existing load testing for dual-provider scenarios
  - _Resource monitoring_: Memory, CPU, connection pool monitoring across both providers

### Quality Gates Enhanced:

- **90%+ Test Coverage**: Comprehensive pytest coverage for all Google Veo3 integration components
  - _Coverage requirements_: Module-level coverage validation, integration test coverage
  - _Test categories_: Unit, integration, performance, security tests following existing markers
  - _Co-located testing_: Tests in module-specific `tests/` subdirectories

- **End-to-End Validation**: Complete video generation workflow testing from API call to browser video playback
  - _Workflow coverage_: Authentication → generation → polling → download → conversion → browser playback
  - _Provider isolation_: E2e testing for both Azure Sora and Google Veo3 independently
  - _Cross-provider validation_: Provider switching scenarios and state management

- **Security Validation**: OWASP compliance and secure credential management
  - _OWASP Top 10_: SQL injection, XSS, authentication testing following existing security tests
  - _Credential security_: SecretStr usage, environment variable protection, credential sanitization
  - _Input validation_: Pydantic v2 validation, parameter sanitization, path traversal protection

- **Performance Validation**: <30s provider switching time and maintained concurrent user capacity
  - _Switching performance_: Provider availability detection and switching <30s total
  - _Concurrent capacity_: 15+ users across both providers with fair queue processing
  - _Resource constraints_: Memory, connection pool, file system resource management

- **Documentation Completeness**: Full CLAUDE.md documentation for all new modules and integration points
  - _Documentation standards_: Google-style docstrings, comprehensive type annotations
  - _Module documentation_: CLAUDE.md files for each new module with usage examples
  - _Integration guides_: Comprehensive setup, configuration, and troubleshooting documentation

### Additional Critical Considerations:

- **AI Agent Performance Pattern**: Module-global constants for optimal performance (NEVER instantiate per-call)
- **File Length Limits**: 500-line maximum file length, refactor if approaching limit
- **Function Complexity**: 50-line maximum function length, single responsibility principle
- **Database Connection Management**: SQLAlchemy context managers, avoid session leaks in Celery tasks
- **FFmpeg Process Management**: Subprocess cleanup and error handling for video conversion
- **Temporary File Cleanup**: Critical for disk space management in production environments
- **Provider Pricing Considerations**: Veo3 at $0.75/second requires usage monitoring and budget controls
