# Provider Orchestration Code Restructuring PRP

## Goal

Restructure misplaced provider orchestration code from `/src/deployment/` to proper architectural location in `/src/features/video_generation/orchestration/` while preserving all working functionality. Move Azure Sora + Google Veo3 provider management system (c3_*.py files) to correct architectural location with zero functionality changes.

## Why

- **Architectural Integrity**: Provider orchestration logic is currently misplaced in deployment infrastructure directory, violating separation of concerns
- **Code Maintainability**: Having provider-specific orchestration mixed with deployment infrastructure creates confusion and maintenance overhead  
- **Feature Cohesion**: Provider orchestration belongs with video generation features, not deployment infrastructure
- **Team Productivity**: Developers expect provider logic in features directory, not deployment directory
- **Future Scalability**: Proper organization enables easier provider addition and orchestration enhancements

## What

**Pure code movement and renaming** - no refactoring, no functionality changes, no splitting of large files. Maintain working code integrity while fixing architectural organization.

### Success Criteria
- [ ] All 4 provider orchestration files moved to correct location with preserved functionality
- [ ] All import statements updated to reference new file locations  
- [ ] Provider-specific configuration files relocated with orchestration code
- [ ] All existing tests continue to pass after restructuring
- [ ] Application starts and provider functionality works identically
- [ ] Docker deployment functions with moved code
- [ ] Zero regression in existing functionality

## All Needed Context

### Files to be moved - VERIFIED EXISTING
```yaml
# Current location → New location (with new names)
- file: src/deployment/c3_deployment_manager.py (1043 lines)
  new_location: src/features/video_generation/orchestration/provider_orchestration.py
  why: Contains ProviderOrchestrationManager and deployment workflows for providers

- file: src/deployment/c3_operations_automation.py (1118 lines)  
  new_location: src/features/video_generation/orchestration/provider_operations.py
  why: Contains C3OperationsAutomation with provider-specific automation logic

- file: src/deployment/c3_production_readiness.py (1125 lines)
  new_location: src/features/video_generation/orchestration/provider_readiness.py
  why: Contains C3ProductionReadinessValidator with provider validation workflows

- file: src/monitoring/c3_provider_monitor.py (1046 lines)
  new_location: src/features/video_generation/orchestration/provider_monitoring.py
  why: Contains C3ProviderMonitor for provider-specific monitoring and metrics
```

### Configuration Files to Relocate
```yaml
# Provider-specific configs MUST move with orchestration code
- file: src/deployment/config/mock-veo3-dev.env
  new_location: src/features/video_generation/orchestration/config/mock-veo3-dev.env
  why: Provider-specific configuration for Google Veo3 mock implementation

- file: src/deployment/config/mock-veo3-staging.env  
  new_location: src/features/video_generation/orchestration/config/mock-veo3-staging.env
  why: Provider-specific staging configuration

- file: src/deployment/config/mock-veo3-production.env
  new_location: src/features/video_generation/orchestration/config/mock-veo3-production.env
  why: Provider-specific production configuration

# Infrastructure configs STAY in deployment (verified correct location)
- file: src/deployment/config/f4-environment-local.env - STAYS
- file: src/deployment/config/f4-environment-staging.env - STAYS  
- file: src/deployment/config/f4-environment-production.env - STAYS
  why: These are deployment infrastructure configurations, not provider-specific
```

### Target Directory Structure - VERIFIED EXISTS
```bash
src/features/video_generation/orchestration/
├── __init__.py                              # Already exists
├── core/                                   # Already exists - modular components
│   ├── deployment_manager.py              # ProviderOrchestrationManager 
│   ├── operations_manager.py              # ProviderOperationsManager
│   ├── readiness_validator.py            # ProviderReadinessValidator
│   └── provider_monitor.py               # ProviderMonitoringService
├── config/                                # NEW - for provider-specific configs
│   ├── mock-veo3-dev.env                 # MOVED from deployment/config/
│   ├── mock-veo3-staging.env             # MOVED from deployment/config/
│   └── mock-veo3-production.env          # MOVED from deployment/config/
├── provider_orchestration.py             # MOVED - c3_deployment_manager.py
├── provider_operations.py                # MOVED - c3_operations_automation.py  
├── provider_readiness.py                 # MOVED - c3_production_readiness.py
└── provider_monitoring.py                # MOVED - c3_provider_monitor.py
```

### Import Dependencies Analysis - VERIFIED
```python
# Current imports found in codebase (need updating)
# Very minimal direct imports found - low risk for import updates

# Primary imports to update:
# 1. References in orchestration integration tests
# 2. Any imports in deployment runbooks or scripts
# 3. Factory function imports in core application

# CRITICAL: The orchestration/ subdirectory already has modular structure
# This means the functionality has been partially refactored already
# The c3_*.py files may be legacy files that need to be carefully moved
```

### Known Gotchas of our codebase & Library Quirks
```python
# CRITICAL: Flask development server caches provider routing logic
# After ANY provider routing changes, MUST restart Flask server:
# pkill -f "flask run" && ./scripts/dev-local.sh

# CRITICAL: All 4 files are 1000+ lines (2x architectural limit)  
# These are MONOLITHIC files that should be decomposed but NOT in this task
# Risk: Large files with complex interdependencies

# CRITICAL: Target orchestration/ directory already exists with modular components
# This suggests partial refactoring already occurred
# Must verify new location doesn't conflict with existing modular structure

# Configuration path dependencies - check for hardcoded paths:
# - Docker compose file references  
# - Environment variable loading
# - Script references to config files

# Provider factory pattern dependencies:
# - Factory imports may need updating
# - Provider registration may reference old locations
# - Health check imports in job queue integration
```

## Implementation Blueprint

### Task 1: Verify Current State and Prepare Move
```yaml
ANALYZE current orchestration/ directory structure:
  - LIST all existing files in src/features/video_generation/orchestration/
  - VERIFY there are no naming conflicts with planned moves
  - CHECK if modular components already implement c3_* functionality
  - DOCUMENT any existing imports that may conflict

CREATE config directory for provider-specific configurations:
  - MKDIR src/features/video_generation/orchestration/config/
  - ENSURE directory is included in git tracking

BACKUP current state before any changes:
  - CREATE git branch for restructuring work
  - COMMIT current state as baseline
```

### Task 2: Move Configuration Files  
```yaml
MOVE provider-specific configuration files:
  - MOVE src/deployment/config/mock-veo3-dev.env → src/features/video_generation/orchestration/config/
  - MOVE src/deployment/config/mock-veo3-staging.env → src/features/video_generation/orchestration/config/
  - MOVE src/deployment/config/mock-veo3-production.env → src/features/video_generation/orchestration/config/

VERIFY no hardcoded paths broken:
  - GREP codebase for references to old config paths
  - UPDATE any hardcoded references to new locations
```

### Task 3: Move Provider Orchestration Files
```yaml  
MOVE core orchestration files with new names:
  - MOVE src/deployment/c3_deployment_manager.py → src/features/video_generation/orchestration/provider_orchestration.py
  - MOVE src/deployment/c3_operations_automation.py → src/features/video_generation/orchestration/provider_operations.py  
  - MOVE src/deployment/c3_production_readiness.py → src/features/video_generation/orchestration/provider_readiness.py
  - MOVE src/monitoring/c3_provider_monitor.py → src/features/video_generation/orchestration/provider_monitoring.py

PRESERVE exact file content:
  - NO changes to class names or method signatures
  - NO changes to functionality or logic
  - ONLY update import paths if absolutely necessary for functionality
```

### Task 4: Update Import Statements
```yaml
FIND and update import references:
  - GREP codebase for "from src.deployment.c3_deployment_manager" 
  - REPLACE with "from src.features.video_generation.orchestration.provider_orchestration"
  - REPEAT for all 4 moved files

UPDATE test imports specifically:
  - UPDATE test_orchestration_basic.py imports
  - UPDATE test_provider_orchestration_integration.py imports  
  - VERIFY no test files break due to import changes

UPDATE any factory function imports:
  - CHECK provider factory for imports of moved modules
  - UPDATE health check imports in job queue integration
  - VERIFY no circular dependencies created
```

### Task 5: Update Configuration References
```yaml
UPDATE configuration file references:
  - FIND any Docker compose references to moved config files
  - UPDATE deployment scripts that reference old config paths
  - VERIFY environment loading still works with new paths

TEST configuration loading:
  - START application and verify provider configs load correctly
  - TEST that mock-veo3 configurations work from new location
  - VALIDATE no configuration-related errors in startup
```

## Validation Loop

### Level 1: Syntax & File Structure  
```bash
# Verify files moved successfully
ls -la src/features/video_generation/orchestration/provider_*.py
ls -la src/features/video_generation/orchestration/config/mock-veo3-*.env

# Check no syntax errors in moved files
python -m py_compile src/features/video_generation/orchestration/provider_orchestration.py
python -m py_compile src/features/video_generation/orchestration/provider_operations.py
python -m py_compile src/features/video_generation/orchestration/provider_readiness.py
python -m py_compile src/features/video_generation/orchestration/provider_monitoring.py

# Expected: All files compile without syntax errors
```

### Level 2: Import Validation
```bash
# Test that imports work from new locations  
python -c "from src.features.video_generation.orchestration.provider_orchestration import C3DeploymentManager; print('✅ C3DeploymentManager import works')"
python -c "from src.features.video_generation.orchestration.provider_operations import C3OperationsAutomation; print('✅ C3OperationsAutomation import works')"  
python -c "from src.features.video_generation.orchestration.provider_readiness import C3ProductionReadinessValidator; print('✅ C3ProductionReadinessValidator import works')"
python -c "from src.features.video_generation.orchestration.provider_monitoring import C3ProviderMonitor; print('✅ C3ProviderMonitor import works')"

# Expected: All import tests pass without errors
```

### Level 3: Application Integration Test
```bash
# Start the application and test core functionality
./scripts/dev-local.sh

# Test basic application endpoints
curl -X GET http://localhost:5001/health
curl -X GET http://localhost:5001/providers

# Test provider functionality still works
curl -X POST http://localhost:5001/generate \
  -H "Content-Type: application/json" \
  -d '{"text": "test prompt", "provider": "azure_sora"}'

# Expected: All endpoints respond correctly, provider functionality works
```

### Level 4: Docker Deployment Test
```bash
# Test Docker deployment still works with moved files
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# Verify services start successfully
docker-compose -f src/deployment/docker/docker-compose.simple.yml ps

# Test application functionality in Docker
curl -X GET http://localhost:8090/health
curl -X GET http://localhost:8090/providers

# Expected: Docker deployment successful, all services healthy
```

## Final validation Checklist
- [ ] All 4 provider orchestration files moved to new locations
- [ ] All 3 provider-specific config files moved with code
- [ ] All import statements updated and working
- [ ] No syntax errors in moved files: `python -m py_compile` on all files
- [ ] Application starts successfully: `./scripts/dev-local.sh`
- [ ] Provider functionality works: `/health` and `/providers` endpoints respond
- [ ] Docker deployment works: `docker-compose up -d` succeeds
- [ ] All existing tests pass: Verify tests that might be affected still pass
- [ ] Configuration loading works: Provider configs load from new location
- [ ] No regression: Video generation functionality works identically

---

## Anti-Patterns to Avoid
- ❌ Don't refactor or optimize code during move - pure movement only
- ❌ Don't split large files - keep monolithic structure intact  
- ❌ Don't change class names or method signatures
- ❌ Don't modify functionality or business logic
- ❌ Don't forget to restart Flask server after provider routing changes
- ❌ Don't hardcode new paths - ensure configuration loading is dynamic
- ❌ Don't break Docker compose references to moved files

**Implementation Confidence**: 8/10 - Well-researched with clear file mapping, existing target structure, and comprehensive validation strategy. Risk mitigated by pure movement approach and extensive testing protocol.