# Docker Deployment for Sora Video Generation System - Implementation PRP

## Goal
Transform the fully operational multi-user Sora video generation system into a production-ready containerized deployment supporting 15+ concurrent users with horizontal scaling, load balancing, and comprehensive monitoring.

## Why
- **Production Readiness**: Enable scalable deployment of the video generation system in production environments
- **Multi-User Support**: Support 15+ concurrent users with proper resource allocation and load distribution
- **Operational Excellence**: Provide monitoring, logging, and automated scaling capabilities
- **Infrastructure Standardization**: Create consistent deployment patterns across development, staging, and production

## What
Deploy the existing Flask-based multi-user video generation system as a containerized architecture with:
- 2-3 Flask application instances behind nginx load balancer
- 1-2 Celery worker containers for video generation
- PostgreSQL database with persistent storage
- Redis for session management and job queues
- Comprehensive monitoring and health checks

### Success Criteria
- [ ] All containers deploy successfully and maintain healthy status
- [ ] Load balancer distributes requests across Flask instances
- [ ] Database migrations complete successfully
- [ ] Celery workers process video generation tasks
- [ ] WebSocket real-time updates function correctly
- [ ] System supports 15+ concurrent users
- [ ] Health checks and monitoring operational
- [ ] Non-root container execution for security

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://docs.docker.com/compose/production/
  why: Production Docker Compose configurations, restart policies, resource limits
  
- url: https://docs.docker.com/develop/dev-best-practices/
  why: Multi-stage builds, security hardening, container lifecycle management
  
- url: https://nginx.org/en/docs/http/load_balancing.html
  why: Upstream configuration, health checks, session persistence for WebSockets
  
- url: https://flask.palletsprojects.com/en/3.0.x/deploying/
  why: Gunicorn configuration, worker processes, reverse proxy setup
  
- url: https://docs.celeryq.dev/en/stable/userguide/daemonizing.html
  why: Production daemonization, worker management, monitoring
  
- url: https://flask-socketio.readthedocs.io/en/latest/deployment.html
  why: WebSocket proxying, load balancer configuration for real-time updates
  
- file: src/main.py
  why: Flask application factory pattern ready for containerization
  
- file: src/config/factory.py
  why: Environment-based configuration management for container environments
  
- file: src/job_queue/celery_app.py
  why: Celery worker configuration with fork handling and Redis integration
  
- file: src/monitoring/health_check.py
  why: Comprehensive health checks for container orchestration
  
- file: src/session/manager.py
  why: Multi-user session management supporting load-balanced deployments
  
- file: src/rate_limiting/limiter.py
  why: Redis-based distributed rate limiting for multiple container instances
  
- docfile: PRPs/ai_docs/deployment_guide.md
  why: Complete production deployment procedures and monitoring setup
```

### Current Codebase Structure
```bash
src/
├── main.py                     # Flask application factory (containerizable)
├── config/
│   ├── factory.py             # Environment configuration management
│   └── environments.py        # Environment-specific configurations
├── core/
│   └── models.py              # Pydantic v2 models for data validation
├── database/
│   └── connection.py          # Database connection pooling
├── features/
│   └── sora_integration/
│       └── client.py          # Azure OpenAI integration
├── job_queue/
│   └── celery_app.py          # Celery worker configuration
├── monitoring/
│   └── health_check.py        # Health checks for orchestration
├── session/
│   └── manager.py             # Multi-user session management
├── rate_limiting/
│   └── limiter.py             # Distributed rate limiting
├── realtime/
│   └── websocket.py           # WebSocket handling
└── api/
    └── routes.py              # API endpoints
```

### Target Docker Architecture
```bash
deployment/
├── docker/
│   ├── Dockerfile             # Multi-stage Flask application container
│   ├── Dockerfile.worker      # Celery worker container
│   ├── docker-compose.yml     # Multi-service orchestration
│   └── nginx/
│       └── nginx.conf         # Load balancer configuration
├── config/
│   ├── redis.conf             # Redis configuration
│   └── postgres.conf          # PostgreSQL configuration
└── volumes/
    ├── postgres/              # Database persistent storage
    ├── redis/                 # Redis persistent storage
    └── uploads/               # Video file storage
```

### Known Gotchas & Critical Patterns
```python
# CRITICAL: Flask application factory pattern is container-ready
# From src/main.py - use create_app() as container entrypoint
def create_app() -> Flask:
    app = Flask(__name__)
    _initialize_multiuser_components(app)
    return app

# CRITICAL: Celery worker requires proper Redis configuration
# From src/job_queue/celery_app.py - use create_celery_app() pattern
def create_celery_app(config_name: Optional[str] = None) -> Celery:
    celery = Celery("sora_multiuser")
    celery.conf.update(
        broker_url=redis_url,
        result_backend=redis_url,
        worker_prefetch_multiplier=1,  # Fair distribution
        task_acks_late=True  # Reliability
    )

# CRITICAL: Session management supports distributed deployment
# From src/session/manager.py - Redis-based sessions work with load balancing
session_manager = SessionManager(redis_client, session_lifetime)

# CRITICAL: Rate limiting coordinates across multiple instances
# From src/rate_limiting/limiter.py - uses Redis for distributed limiting
rate_limiter = RateLimiter(redis_client, requests_per_minute=60)

# CRITICAL: WebSocket support requires nginx configuration
# From src/realtime/websocket.py - needs sticky sessions for WebSocket
socketio.init_app(app, cors_allowed_origins="*")

# CRITICAL: Health checks designed for container orchestration
# From src/monitoring/health_check.py - provides comprehensive health validation
health_checker = HealthCheck(database_manager, azure_client)
```

## Implementation Blueprint

### Data Models and Structure
All core models are already implemented with Pydantic v2 validation:
```python
# From src/core/models.py - production-ready data models
class VideoGenerationRequest(BaseModel):
    prompt: str
    duration: int
    user_id: str
    session_id: str

class VideoGenerationResponse(BaseModel):
    job_id: str
    status: str
    estimated_completion: Optional[datetime]
    video_url: Optional[str]
```

### Implementation Tasks

```yaml
Task 1: Create Docker Application Container
CREATE src/deployment/docker/Dockerfile:
  - BASE image: python:3.11-slim
  - SECURITY: Create non-root user "sora" 
  - PATTERN: Multi-stage build with builder and production stages
  - INSTALL: System dependencies (gcc, libpq-dev) in builder stage only
  - COPY: Use uv.lock for dependency management
  - EXPOSE: Port 5001 for Flask application
  - HEALTHCHECK: Use existing /health endpoint from health_check.py
  - CMD: gunicorn with gevent workers for async support

Task 2: Create Celery Worker Container
CREATE src/deployment/docker/Dockerfile.worker:
  - BASE image: python:3.11-slim
  - SECURITY: Create non-root user "celery"
  - PATTERN: Same multi-stage build as Flask container
  - OPTIMIZE: Concurrency=2, max-tasks-per-child=100 for video processing
  - HEALTHCHECK: Use celery inspect ping command
  - CMD: celery worker with logging and monitoring

Task 3: Create nginx Load Balancer Configuration

**File: `src/deployment/docker/nginx/nginx.conf`**
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging configuration
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic optimization
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Upstream Flask applications
    upstream flask_app {
        least_conn;
        server sora-app-1:5001 max_fails=3 fail_timeout=30s;
        server sora-app-2:5001 max_fails=3 fail_timeout=30s;
        server sora-app-3:5001 max_fails=3 fail_timeout=30s;
    }

    # Main server block
    server {
        listen 80;
        server_name _;

        # Client max body size for video uploads
        client_max_body_size 100M;

        # Proxy settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;

        # Main application proxy
        location / {
            proxy_pass http://flask_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Health check handling
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        }

        # WebSocket support for real-time updates
        location /socket.io/ {
            proxy_pass http://flask_app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific settings
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        # Static files (served directly by nginx)
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check endpoint
        location /health {
            proxy_pass http://flask_app/health;
            access_log off;
        }

        # API endpoints with timeout optimization
        location /api/ {
            proxy_pass http://flask_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeout for video generation
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
    }
}
```

Task 4: Create Docker Compose Orchestration
CREATE src/deployment/docker/docker-compose.yml:
  - DATABASE: PostgreSQL with persistent volume and health checks
  - CACHE: Redis with persistence and memory optimization
  - APPS: 2-3 Flask application instances with resource limits
  - WORKERS: 1-2 Celery worker instances with video processing optimization
  - BALANCER: nginx load balancer with upstream configuration
  - MONITORING: Include health check endpoints and restart policies

Task 5: Create Environment Configuration
CREATE .env.example:
  - FLASK_ENV: Production configuration
  - DATABASE_URL: PostgreSQL connection string for containers
  - REDIS_URL: Redis connection for session/queue management
  - AZURE_OPENAI: API configuration for video generation
  - SECURITY: Secret key generation and secure defaults

Task 6: Create Database Initialization
CREATE src/deployment/docker/init-db.sh:
  - PATTERN: Database initialization script
  - MIGRATION: Run Flask db upgrade on container startup
  - VALIDATION: Verify database schema creation
  - SECURITY: Use environment variables for credentials

Task 7: Create Health Check Integration
MODIFY src/monitoring/health_check.py:
  - ADD: Container-specific health checks
  - EXTEND: Database connection validation for Docker
  - INCLUDE: Redis connectivity and queue health
  - OPTIMIZE: Response time thresholds for container orchestration
```

### Task Implementation Details

#### Task 1: Flask Application Container
```dockerfile
# Multi-stage build for Flask application
FROM python:3.11-slim as builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc g++ libpq-dev && \
    rm -rf /var/lib/apt/lists/*

# Copy dependency files
COPY pyproject.toml uv.lock ./
RUN pip install uv && uv sync --frozen

# Production stage
FROM python:3.11-slim as production

# Create non-root user
RUN groupadd -r sora && useradd -r -g sora sora

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 curl && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy virtual environment and application
COPY --from=builder /app/.venv /app/.venv
ENV PATH="/app/.venv/bin:$PATH"
COPY . .

# Set permissions
RUN mkdir -p /app/uploads /app/logs && \
    chown -R sora:sora /app

USER sora
EXPOSE 5001

# Health check using existing endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Use gunicorn for production
CMD ["gunicorn", "--bind", "0.0.0.0:5001", "--workers", "3", \
     "--worker-class", "gevent", "--worker-connections", "1000", \
     "--timeout", "120", "src.main:create_app()"]
```

#### Task 4: Docker Compose Orchestration
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: sora_production
      POSTGRES_USER: sora_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sora_user"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '0.5'

  sora-app-1:
    build:
      context: ../..
      dockerfile: src/deployment/docker/Dockerfile
    environment:
      DATABASE_URL: postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'

  sora-app-2:
    build:
      context: ../..
      dockerfile: src/deployment/docker/Dockerfile
    environment:
      DATABASE_URL: postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'

  worker-1:
    build:
      context: ../..
      dockerfile: src/deployment/docker/Dockerfile.worker
    environment:
      DATABASE_URL: postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.5'

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - sora-app-1
      - sora-app-2
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  postgres_data:
  redis_data:
```

### Integration Points
```yaml
DATABASE:
  - migration: Use existing Flask-Migrate commands in container startup
  - pooling: Leverage existing DatabaseManager connection pooling
  
CONFIG:
  - factory: Use existing ConfigurationFactory for environment-specific settings
  - validation: Leverage existing Pydantic models for configuration validation
  
ROUTES:
  - existing: All API routes from src/api/routes.py work without modification
  - health: Use existing health check endpoints from src/monitoring/health_check.py
  
WORKERS:
  - existing: Use existing Celery configuration from src/job_queue/celery_app.py
  - tasks: All existing video generation tasks work without modification
```

## Validation Loop

### Level 1: Container Build Validation
```bash
# Build Flask application container
docker build -f src/deployment/docker/Dockerfile -t sora-app:latest .

# Build Celery worker container
docker build -f src/deployment/docker/Dockerfile.worker -t sora-worker:latest .

# Verify images exist
docker images | grep sora

# Expected: Both sora-app and sora-worker images built successfully
```

### Level 2: Service Deployment Validation
```bash
# Start all services
docker-compose -f src/deployment/docker/docker-compose.yml up -d

# Check container health
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Expected: All containers show "healthy" status
```

### Level 3: Application Integration Testing
```bash
# Test load balancer
curl -f http://localhost/health

# Test database connectivity
curl -f http://localhost/health/database

# Test video generation API
curl -X POST http://localhost/api/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A test video", "duration": 5}'

# Expected: 200 OK responses and job ID returned
```

### Level 4: Multi-User Load Testing
```bash
# Test concurrent requests
for i in {1..10}; do
  curl -s http://localhost/health > /dev/null &
done
wait

# Test load balancing
for i in {1..6}; do 
  curl -s http://localhost/health | grep -o "app-[0-9]"
done

# Expected: Requests distributed across multiple app instances
```

### Level 5: Worker and Queue Validation
```bash
# Check Celery worker status
docker-compose -f src/deployment/docker/docker-compose.yml exec worker-1 \
  celery -A src.job_queue.celery_app inspect active

# Monitor queue processing
docker-compose -f src/deployment/docker/docker-compose.yml logs -f worker-1

# Expected: Workers processing tasks successfully
```

## Final Validation Checklist
- [ ] All containers build successfully: `docker images | grep sora`
- [ ] All services deploy and show healthy status: `docker ps`
- [ ] Load balancer responds: `curl -f http://localhost/health`
- [ ] Database connectivity verified: `curl -f http://localhost/health/database`
- [ ] Video generation API functional: POST to `/api/generate`
- [ ] Load balancing distributes requests across instances
- [ ] Celery workers process tasks: `docker-compose exec worker celery inspect active`
- [ ] Non-root execution: `docker exec sora-app-simple whoami` returns "sora"
- [ ] WebSocket connections work with load balancing
- [ ] Resource limits enforced: `docker stats`
- [ ] Health checks respond within 1 second
- [ ] Path corrections verified: All Celery import paths use `src.job_queue.celery_app`

## Quick Start Commands
```bash
# Copy environment template
cp .env.example .env

# Generate secure secrets
openssl rand -base64 32  # Use for SECRET_KEY
openssl rand -base64 32  # Use for DB_PASSWORD

# Edit .env with your Azure OpenAI credentials
nano .env

# Build and start simplified deployment
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# Monitor deployment
docker-compose -f src/deployment/docker/docker-compose.simple.yml logs -f

# Test deployment
curl -f http://localhost:5001/health
```

## Emergency Recovery Procedures
```bash
# Complete system reset
docker-compose -f src/deployment/docker/docker-compose.yml down -v
docker system prune -a
docker-compose -f src/deployment/docker/docker-compose.yml build --no-cache
docker-compose -f src/deployment/docker/docker-compose.yml up -d

# Database backup
docker-compose -f src/deployment/docker/docker-compose.yml exec postgres \
  pg_dump -U sora_user sora_production > backup.sql

# Service health check
#!/bin/bash
echo "=== Container Health Status ==="
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo -e "\n=== Service Health Checks ==="
curl -f http://localhost/health && echo " ✓ Application healthy"
curl -f http://localhost/health/database && echo " ✓ Database healthy"
```

---

## Anti-Patterns to Avoid
- ❌ Don't run containers as root - use dedicated users (sora, celery)
- ❌ Don't hardcode environment variables - use .env files
- ❌ Don't skip health checks - they're critical for orchestration
- ❌ Don't ignore resource limits - they prevent system overload
- ❌ Don't use :latest tags in production - pin specific versions
- ❌ Don't ignore container logs - they provide critical debugging info
- ❌ Don't skip database migrations - they're required for schema updates
- ❌ Don't use development settings in production containers