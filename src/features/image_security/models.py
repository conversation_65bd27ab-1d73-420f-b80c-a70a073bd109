"""
C1 Image Security Pipeline Models.

Pydantic v2 models for secure image processing with comprehensive validation
and integration with F2 provider interface patterns.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class ImageValidationError(Exception):
    """Raised when image validation fails."""

    def __init__(self, message: str, error_code: str = "VALIDATION_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(message)


class ImageMetadata(BaseModel):
    """
    Image metadata for secure processing and database storage.

    Follows F1 database schema integration patterns for provider compatibility.
    """

    model_config = ConfigDict(frozen=True, use_enum_values=True, populate_by_name=True)

    # Basic image properties
    filename: str = Field(..., min_length=1, max_length=255)
    file_size: int = Field(..., gt=0, le=10 * 1024 * 1024)  # 10MB limit
    mime_type: str = Field(..., pattern=r"^image/(jpeg|png|webp|gif)$")
    width: int = Field(..., gt=0, le=4096)
    height: int = Field(..., gt=0, le=4096)

    # Security validation fields
    magic_number_valid: bool = Field(..., description="Magic number validation result")
    pil_validation_passed: bool = Field(..., description="PIL format validation result")
    security_scan_passed: bool = Field(..., description="Security scan result")

    # Processing metadata
    processing_time_ms: float = Field(..., ge=0.0)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Provider integration fields (F2 compatibility)
    provider_compatible: bool = Field(
        True, description="Compatible with provider interface"
    )
    provider_metadata: Dict[str, Any] = Field(default_factory=dict)

    @field_validator("filename")
    @classmethod
    def validate_filename_security(cls, v: str) -> str:
        """Validate filename for security concerns."""
        # Remove directory traversal attempts
        safe_filename = Path(v).name

        # Check for suspicious patterns
        dangerous_patterns = ["..", "/", "\\", "<", ">", ":", '"', "|", "?", "*"]
        for pattern in dangerous_patterns:
            if pattern in safe_filename:
                raise ValueError(f"Unsafe filename pattern detected: {pattern}")

        return safe_filename

    @field_validator("mime_type")
    @classmethod
    def validate_mime_type_security(cls, v: str) -> str:
        """Validate MIME type for security."""
        allowed_types = {"image/jpeg", "image/png", "image/webp", "image/gif"}
        if v not in allowed_types:
            raise ValueError(f"Unsupported MIME type: {v}")
        return v


class ImageSecurityResult(BaseModel):
    """
    Result of image security processing with comprehensive validation status.

    Used for Flask API responses and provider interface integration.
    """

    model_config = ConfigDict(use_enum_values=True, populate_by_name=True)

    # Security validation status
    is_safe: bool = Field(..., description="Overall security validation result")
    validation_passed: bool = Field(..., description="All validations passed")

    # Image metadata
    metadata: Optional[ImageMetadata] = Field(None)

    # Processing results
    base64_data: Optional[str] = Field(
        None, description="Base64 encoded image data for API"
    )
    secure_path: Optional[str] = Field(
        None, description="Secure file path (metadata only)"
    )

    # Error handling
    error_message: Optional[str] = Field(None)
    error_code: Optional[str] = Field(None)

    # Validation details
    validation_results: Dict[str, bool] = Field(default_factory=dict)
    security_warnings: List[str] = Field(default_factory=list)

    # Performance metrics
    total_processing_time_ms: float = Field(..., ge=0.0)

    # Provider integration (F2 compatibility)
    provider_ready: bool = Field(True, description="Ready for provider interface")
    provider_format: str = Field("base64", description="Format for provider API")

    @field_validator("base64_data")
    @classmethod
    def validate_base64_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate base64 data format."""
        if v is None:
            return v

        try:
            # Verify base64 format without fully decoding (performance)
            if not v.startswith("data:image/"):
                raise ValueError("Base64 data must include data URI prefix")

            # Basic format validation
            base64_part = v.split(",", 1)[1] if "," in v else v
            if len(base64_part) % 4 != 0:
                raise ValueError("Invalid base64 padding")

            return v
        except Exception as e:
            raise ValueError(f"Invalid base64 format: {e}")

    def to_api_response(self) -> Dict[str, Any]:
        """Convert to API response format for Flask endpoints."""
        return {
            "success": self.is_safe and self.validation_passed,
            "is_safe": self.is_safe,
            "validation_passed": self.validation_passed,
            "metadata": self.metadata.model_dump() if self.metadata else None,
            "base64_data": self.base64_data,
            "error_message": self.error_message,
            "error_code": self.error_code,
            "validation_results": self.validation_results,
            "security_warnings": self.security_warnings,
            "processing_time_ms": self.total_processing_time_ms,
            "provider_ready": self.provider_ready,
            "provider_format": self.provider_format,
        }

    def to_database_record(self, session_id: str) -> Dict[str, Any]:
        """Convert to F1 database record format."""
        return {
            "session_id": session_id,
            "filename": self.metadata.filename if self.metadata else None,
            "file_size": self.metadata.file_size if self.metadata else None,
            "mime_type": self.metadata.mime_type if self.metadata else None,
            "width": self.metadata.width if self.metadata else None,
            "height": self.metadata.height if self.metadata else None,
            "validation_passed": self.validation_passed,
            "is_safe": self.is_safe,
            "processing_time_ms": self.total_processing_time_ms,
            "error_message": self.error_message,
            "created_at": self.metadata.created_at
            if self.metadata
            else datetime.utcnow(),
            # F1 provider integration fields
            "provider_ready": self.provider_ready,
            "provider_metadata": self.metadata.provider_metadata
            if self.metadata
            else {},
        }


class ImageUploadRequest(BaseModel):
    """
    Request model for image upload API with security validation.

    Used by Flask API endpoints for request validation.
    """

    model_config = ConfigDict(use_enum_values=True, populate_by_name=True)

    # File data (from multipart form)
    filename: str = Field(..., min_length=1, max_length=255)
    content_type: str = Field(..., pattern=r"^image/(jpeg|png|webp|gif)$")

    # Processing options
    max_width: int = Field(1920, gt=0, le=4096)
    max_height: int = Field(1080, gt=0, le=4096)
    quality: int = Field(85, ge=1, le=100)

    # Security options
    strict_validation: bool = Field(
        True, description="Enable strict security validation"
    )
    scan_for_malware: bool = Field(True, description="Enable malware scanning")

    # Provider integration options (F2 compatibility)
    target_provider: Literal["azure_sora", "google_veo3"] = Field("google_veo3")
    provider_format: Literal["base64", "url"] = Field("base64")

    @field_validator("filename")
    @classmethod
    def validate_upload_filename(cls, v: str) -> str:
        """Validate uploaded filename for security."""
        # Use same validation as ImageMetadata
        safe_filename = Path(v).name

        # Additional upload-specific checks
        if not safe_filename or safe_filename in [".", ".."]:
            raise ValueError("Invalid filename")

        # Check file extension
        allowed_extensions = {".jpg", ".jpeg", ".png", ".webp", ".gif"}
        if Path(safe_filename).suffix.lower() not in allowed_extensions:
            raise ValueError(
                f"Unsupported file extension: {Path(safe_filename).suffix}"
            )

        return safe_filename


class ImageProcessingConfig(BaseModel):
    """
    Configuration for image processing pipeline.

    Uses configuration factory pattern for environment-aware setup.
    """

    model_config = ConfigDict(frozen=True, use_enum_values=True)

    # File size limits
    max_file_size_mb: int = Field(10, ge=1, le=50)
    max_width: int = Field(4096, ge=100, le=8192)
    max_height: int = Field(4096, ge=100, le=8192)

    # Security settings
    enable_magic_number_check: bool = Field(True)
    enable_pil_validation: bool = Field(True)
    enable_malware_scan: bool = Field(False)
    strict_mime_type_check: bool = Field(True)

    # Processing settings
    auto_resize: bool = Field(True)
    jpeg_quality: int = Field(85, ge=1, le=100)
    png_optimize: bool = Field(True)

    # Performance settings
    processing_timeout_seconds: int = Field(30, ge=5, le=120)
    max_concurrent_uploads: int = Field(15, ge=1, le=50)

    # Provider integration settings (F2 compatibility)
    default_provider: Literal["azure_sora", "google_veo3"] = Field("google_veo3")
    provider_timeout_seconds: int = Field(60, ge=10, le=300)

    @classmethod
    def from_environment(cls) -> "ImageProcessingConfig":
        """Create configuration from environment variables using enhanced security settings."""
        try:
            # Use the new configuration factory for enhanced security settings
            from src.config.factory import ConfigurationFactory

            # Get environment-aware image security configuration
            security_config = ConfigurationFactory.create_environment_image_config()

            # Convert to ImageProcessingConfig format
            return cls(
                max_file_size_mb=security_config.max_file_size_mb,
                max_width=security_config.max_width,
                max_height=security_config.max_height,
                enable_magic_number_check=security_config.enable_magic_number_check,
                enable_pil_validation=security_config.enable_pil_validation,
                enable_malware_scan=security_config.enable_malware_scan,
                strict_mime_type_check=security_config.strict_mime_type_check,
                auto_resize=security_config.auto_resize,
                jpeg_quality=security_config.jpeg_quality,
                png_optimize=security_config.png_optimize,
                processing_timeout_seconds=security_config.processing_timeout_seconds,
                max_concurrent_uploads=security_config.max_concurrent_uploads,
                default_provider=security_config.default_provider,
                provider_timeout_seconds=security_config.provider_timeout_seconds,
            )

        except Exception as e:
            # Fallback to basic environment variable configuration
            import logging
            import os

            logger = logging.getLogger(__name__)
            logger.warning(
                f"Failed to load enhanced security config, using basic config: {e}"
            )

            return cls(
                max_file_size_mb=int(os.getenv("IMAGE_MAX_FILE_SIZE_MB", "10")),
                max_width=int(os.getenv("IMAGE_MAX_WIDTH", "4096")),
                max_height=int(os.getenv("IMAGE_MAX_HEIGHT", "4096")),
                enable_magic_number_check=os.getenv(
                    "IMAGE_ENABLE_MAGIC_CHECK", "true"
                ).lower()
                == "true",
                enable_pil_validation=os.getenv(
                    "IMAGE_ENABLE_PIL_VALIDATION", "true"
                ).lower()
                == "true",
                strict_mime_type_check=os.getenv(
                    "IMAGE_STRICT_MIME_CHECK", "true"
                ).lower()
                == "true",
                auto_resize=os.getenv("IMAGE_AUTO_RESIZE", "true").lower() == "true",
                jpeg_quality=int(os.getenv("IMAGE_JPEG_QUALITY", "85")),
                processing_timeout_seconds=int(
                    os.getenv("IMAGE_PROCESSING_TIMEOUT", "30")
                ),
                max_concurrent_uploads=int(
                    os.getenv("IMAGE_MAX_CONCURRENT_UPLOADS", "15")
                ),
                default_provider=os.getenv("IMAGE_DEFAULT_PROVIDER", "google_veo3"),
                provider_timeout_seconds=int(os.getenv("IMAGE_PROVIDER_TIMEOUT", "60")),
            )

    @classmethod
    def from_security_config(cls, security_config) -> "ImageProcessingConfig":
        """Create ImageProcessingConfig from ImageSecurityEnvironmentSettings."""
        return cls(
            max_file_size_mb=security_config.max_file_size_mb,
            max_width=security_config.max_width,
            max_height=security_config.max_height,
            enable_magic_number_check=security_config.enable_magic_number_check,
            enable_pil_validation=security_config.enable_pil_validation,
            enable_malware_scan=security_config.enable_malware_scan,
            strict_mime_type_check=security_config.strict_mime_type_check,
            auto_resize=security_config.auto_resize,
            jpeg_quality=security_config.jpeg_quality,
            png_optimize=security_config.png_optimize,
            processing_timeout_seconds=security_config.processing_timeout_seconds,
            max_concurrent_uploads=security_config.max_concurrent_uploads,
            default_provider=security_config.default_provider,
            provider_timeout_seconds=security_config.provider_timeout_seconds,
        )
