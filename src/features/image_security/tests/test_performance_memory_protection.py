"""
C1 Image Security - Performance and Memory Protection Tests.

Comprehensive testing of performance benchmarks and memory protection in image security.
Tests cover processing time limits, memory usage monitoring, and resource protection.

Test Categories:
- Processing time performance (4 tests)
- Memory usage and protection (4 tests)
- Concurrent processing performance (2 tests)
- Resource exhaustion protection (2 tests)
"""

import asyncio
import time
import tracemalloc
from typing import Any, Dict, List

from src.features.image_security.pipeline import ImageSecurityPipeline
from src.features.image_security.processors import ImageProcessor
from src.features.image_security.validators import ImageValidator


class TestProcessingTimePerformance:
    """Test suite for processing time performance validation."""

    def test_processing_time_under_2_seconds(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_test_images: Dict[str, bytes],
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test processing time meets <2s requirement for all image sizes."""
        # Test: All image processing should complete under 2 seconds
        max_time_ms = performance_benchmark_config["max_processing_time_ms"]  # 2000ms

        async def run_performance_test():
            for image_name, image_data in performance_test_images.items():
                start_time = time.time()

                result = await security_pipeline.process_uploaded_image(
                    image_data,
                    f"performance_test_{image_name}.jpg",
                    f"test_session_{image_name}",
                )

                end_time = time.time()
                processing_time_ms = (end_time - start_time) * 1000

                assert processing_time_ms < max_time_ms, (
                    f"Processing {image_name} took {processing_time_ms:.2f}ms, "
                    f"should be < {max_time_ms}ms"
                )

                assert result.total_processing_time_ms < max_time_ms, (
                    f"Reported processing time {result.total_processing_time_ms:.2f}ms "
                    f"should be < {max_time_ms}ms"
                )

        asyncio.run(run_performance_test())

    def test_validation_performance_breakdown(
        self,
        security_validator: ImageValidator,
        performance_test_images: Dict[str, bytes],
    ):
        """Test performance breakdown of individual validation steps."""
        # Test: Individual validation steps should be fast
        time_limits = {
            "magic_number": 50,  # 50ms max
            "mime_detection": 100,  # 100ms max
            "pil_validation": 500,  # 500ms max
            "security_scan": 200,  # 200ms max
        }

        for image_name, image_data in performance_test_images.items():
            # Magic number validation
            start_time = time.time()
            magic_result = security_validator._validate_magic_number(image_data)
            magic_time_ms = (time.time() - start_time) * 1000

            assert magic_time_ms < time_limits["magic_number"], (
                f"Magic number validation for {image_name} took {magic_time_ms:.2f}ms"
            )

            # MIME detection
            start_time = time.time()
            mime_type = security_validator._detect_mime_type(image_data)
            mime_time_ms = (time.time() - start_time) * 1000

            assert mime_time_ms < time_limits["mime_detection"], (
                f"MIME detection for {image_name} took {mime_time_ms:.2f}ms"
            )

            # PIL validation
            start_time = time.time()
            pil_result = security_validator._validate_with_pil(image_data)
            pil_time_ms = (time.time() - start_time) * 1000

            assert pil_time_ms < time_limits["pil_validation"], (
                f"PIL validation for {image_name} took {pil_time_ms:.2f}ms"
            )

            # Security scan
            start_time = time.time()
            security_result = security_validator._perform_security_scan(image_data)
            security_time_ms = (time.time() - start_time) * 1000

            assert security_time_ms < time_limits["security_scan"], (
                f"Security scan for {image_name} took {security_time_ms:.2f}ms"
            )

    def test_processing_performance_consistency(
        self,
        security_pipeline: ImageSecurityPipeline,
        valid_jpeg_data: bytes,
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test processing performance consistency across multiple runs."""
        # Test: Processing time should be consistent across multiple runs
        iterations = performance_benchmark_config["performance_test_iterations"]
        processing_times = []

        async def run_consistency_test():
            for i in range(iterations):
                start_time = time.time()

                result = await security_pipeline.process_uploaded_image(
                    valid_jpeg_data,
                    f"consistency_test_{i}.jpg",
                    f"test_session_consistency_{i}",
                )

                end_time = time.time()
                processing_time_ms = (end_time - start_time) * 1000
                processing_times.append(processing_time_ms)

                assert result.is_safe is True, f"Iteration {i} should succeed"

            # Calculate statistics
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)

            # Performance should be consistent (max time shouldn't be > 3x min time)
            consistency_ratio = max_time / min_time if min_time > 0 else float("inf")
            assert consistency_ratio < 3.0, (
                f"Performance inconsistency: max {max_time:.2f}ms vs min {min_time:.2f}ms "
                f"(ratio: {consistency_ratio:.2f})"
            )

            # Average should be well under limit
            max_avg_time = performance_benchmark_config["max_processing_time_ms"] * 0.7
            assert avg_time < max_avg_time, (
                f"Average processing time {avg_time:.2f}ms should be < {max_avg_time}ms"
            )

        asyncio.run(run_consistency_test())

    def test_large_image_processing_performance(
        self,
        security_pipeline: ImageSecurityPipeline,
        memory_intensive_image: bytes,
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test processing performance with large/complex images."""
        # Test: Large images should still meet performance requirements
        max_time_ms = (
            performance_benchmark_config["max_processing_time_ms"] * 1.5
        )  # Allow 50% more time

        async def run_large_image_test():
            start_time = time.time()

            result = await security_pipeline.process_uploaded_image(
                memory_intensive_image,
                "large_performance_test.png",
                "test_session_large",
            )

            end_time = time.time()
            processing_time_ms = (end_time - start_time) * 1000

            assert processing_time_ms < max_time_ms, (
                f"Large image processing took {processing_time_ms:.2f}ms, "
                f"should be < {max_time_ms}ms"
            )

            # Should still be successful despite size
            assert result.validation_passed is True, (
                "Large image should be processed successfully"
            )

        asyncio.run(run_large_image_test())


class TestMemoryUsageProtection:
    """Test suite for memory usage and protection validation."""

    def test_memory_usage_under_100mb_growth(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_test_images: Dict[str, bytes],
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test memory usage stays under 100MB growth limit."""
        # Test: Memory growth should be under 100MB
        max_memory_growth_mb = performance_benchmark_config["max_memory_growth_mb"]

        tracemalloc.start()
        baseline_current, baseline_peak = tracemalloc.get_traced_memory()

        async def run_memory_test():
            # Process multiple images to test cumulative memory usage
            for image_name, image_data in performance_test_images.items():
                result = await security_pipeline.process_uploaded_image(
                    image_data,
                    f"memory_test_{image_name}.jpg",
                    f"test_session_memory_{image_name}",
                )

                assert result.validation_passed is True, (
                    f"Memory test {image_name} should succeed"
                )

            # Check final memory usage
            current, peak = tracemalloc.get_traced_memory()
            memory_growth_mb = (peak - baseline_peak) / 1024 / 1024

            assert memory_growth_mb < max_memory_growth_mb, (
                f"Memory growth {memory_growth_mb:.2f}MB exceeds limit of {max_memory_growth_mb}MB"
            )

        asyncio.run(run_memory_test())
        tracemalloc.stop()

    def test_memory_leak_prevention(
        self, security_pipeline: ImageSecurityPipeline, valid_png_data: bytes
    ):
        """Test prevention of memory leaks during repeated processing."""
        # Test: Repeated processing should not cause memory leaks
        tracemalloc.start()

        async def run_leak_test():
            baseline_memory = None

            # Process same image multiple times
            for i in range(50):  # 50 iterations to detect leaks
                result = await security_pipeline.process_uploaded_image(
                    valid_png_data,
                    f"memory_leak_test_{i}.png",
                    f"test_session_leak_{i}",
                )

                assert result.is_safe is True, f"Iteration {i} should succeed"

                # Check memory every 10 iterations
                if i % 10 == 0:
                    current, peak = tracemalloc.get_traced_memory()
                    current_mb = current / 1024 / 1024

                    if baseline_memory is None:
                        baseline_memory = current_mb
                    else:
                        # Memory should not grow significantly with repeated processing
                        memory_growth = current_mb - baseline_memory
                        assert memory_growth < 50, (
                            f"Memory leak detected: {memory_growth:.2f}MB growth after {i} iterations"
                        )

        asyncio.run(run_leak_test())
        tracemalloc.stop()

    def test_memory_efficiency_image_processing(
        self, image_processor: ImageProcessor, performance_test_images: Dict[str, bytes]
    ):
        """Test memory efficiency of image processing operations."""
        # Test: Image processing should be memory efficient
        tracemalloc.start()

        for image_name, image_data in performance_test_images.items():
            # Measure memory for individual processing
            baseline_current, baseline_peak = tracemalloc.get_traced_memory()

            # Process image
            processed_bytes, metadata = image_processor.optimize_for_provider(
                image_data, "google_veo3"
            )

            current, peak = tracemalloc.get_traced_memory()

            # Memory usage should be reasonable relative to image size
            processing_memory_mb = (peak - baseline_peak) / 1024 / 1024
            image_size_mb = len(image_data) / 1024 / 1024

            # Processing memory should not exceed 5x image size
            max_processing_memory = max(
                image_size_mb * 5, 10
            )  # At least 10MB allowance
            assert processing_memory_mb < max_processing_memory, (
                f"Processing {image_name} used {processing_memory_mb:.2f}MB, "
                f"should be < {max_processing_memory:.2f}MB"
            )

        tracemalloc.stop()

    def test_memory_protection_large_images(
        self, security_validator: ImageValidator, memory_intensive_image: bytes
    ):
        """Test memory protection with memory-intensive images."""
        # Test: Memory-intensive images should be handled safely
        tracemalloc.start()

        try:
            # Process memory-intensive image
            is_valid, validation_results = security_validator.validate_image_data(
                memory_intensive_image, "memory_intensive_test.png"
            )

            current, peak = tracemalloc.get_traced_memory()
            peak_mb = peak / 1024 / 1024

            # Should complete without excessive memory usage
            assert peak_mb < 500, (
                f"Memory-intensive validation used {peak_mb:.2f}MB, should be < 500MB"
            )

            # Should handle the image (pass or fail gracefully)
            assert isinstance(is_valid, bool), "Should return boolean validation result"

        except Exception as e:
            # Memory protection exceptions are acceptable
            assert (
                "memory" in str(e).lower()
                or "size" in str(e).lower()
                or "resource" in str(e).lower()
            )
        finally:
            tracemalloc.stop()


class TestConcurrentProcessingPerformance:
    """Test suite for concurrent processing performance."""

    def test_concurrent_user_processing_performance(
        self,
        security_pipeline: ImageSecurityPipeline,
        valid_jpeg_data: bytes,
        concurrent_test_session_ids: List[str],
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test processing performance with 15 concurrent users."""
        # Test: Should handle 15 concurrent users efficiently
        concurrent_users = performance_benchmark_config["concurrent_user_count"]
        max_total_time_ms = (
            performance_benchmark_config["max_processing_time_ms"] * 2
        )  # Allow 2x time for concurrency

        async def process_user_image(session_id: str) -> tuple:
            start_time = time.time()
            try:
                result = await security_pipeline.process_uploaded_image(
                    valid_jpeg_data, f"concurrent_test_{session_id}.jpg", session_id
                )
                end_time = time.time()
                processing_time_ms = (end_time - start_time) * 1000
                return (session_id, processing_time_ms, result.is_safe, True)
            except Exception:
                end_time = time.time()
                processing_time_ms = (end_time - start_time) * 1000
                return (session_id, processing_time_ms, False, False)

        async def run_concurrent_test():
            start_time = time.time()

            # Create tasks for concurrent processing
            tasks = []
            for session_id in concurrent_test_session_ids[:concurrent_users]:
                task = process_user_image(session_id)
                tasks.append(task)

            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks)

            end_time = time.time()
            total_time_ms = (end_time - start_time) * 1000

            # All should complete within reasonable time
            assert total_time_ms < max_total_time_ms, (
                f"Concurrent processing took {total_time_ms:.2f}ms, "
                f"should be < {max_total_time_ms}ms"
            )

            # Most should succeed
            successful_results = [r for r in results if r[3] and r[2]]
            success_rate = len(successful_results) / len(results)
            assert success_rate >= 0.8, (
                f"Concurrent success rate {success_rate:.2%} should be >= 80%"
            )

            # Individual processing times should still be reasonable
            for session_id, proc_time, is_safe, success in results:
                if success:
                    assert (
                        proc_time
                        < performance_benchmark_config["max_processing_time_ms"] * 1.5
                    ), (
                        f"Individual processing time {proc_time:.2f}ms too high for {session_id}"
                    )

        asyncio.run(run_concurrent_test())

    def test_concurrent_memory_usage_efficiency(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_test_images: Dict[str, bytes],
        concurrent_test_session_ids: List[str],
    ):
        """Test memory efficiency during concurrent processing."""
        # Test: Concurrent processing should be memory efficient
        tracemalloc.start()

        async def process_concurrent_images():
            tasks = []

            # Create concurrent tasks with different images
            for i, session_id in enumerate(
                concurrent_test_session_ids[:10]
            ):  # Limit to 10
                image_name = list(performance_test_images.keys())[
                    i % len(performance_test_images)
                ]
                image_data = performance_test_images[image_name]

                task = security_pipeline.process_uploaded_image(
                    image_data,
                    f"concurrent_memory_{session_id}_{image_name}.jpg",
                    session_id,
                )
                tasks.append(task)

            # Process all concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check memory usage
            current, peak = tracemalloc.get_traced_memory()
            peak_mb = peak / 1024 / 1024

            # Memory usage should be reasonable even with concurrent processing
            assert peak_mb < 1000, (
                f"Concurrent processing used {peak_mb:.2f}MB, should be < 1000MB"
            )

            # Most should succeed
            successful_results = [
                r for r in results if not isinstance(r, Exception) and r.is_safe
            ]
            success_rate = len(successful_results) / len(results)
            assert success_rate >= 0.7, (
                f"Concurrent memory test success rate {success_rate:.2%} should be >= 70%"
            )

        asyncio.run(process_concurrent_images())
        tracemalloc.stop()


class TestResourceExhaustionProtection:
    """Test suite for resource exhaustion protection."""

    def test_cpu_exhaustion_protection(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_stress_test_data: Dict[str, bytes],
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test protection against CPU exhaustion attacks."""
        # Test: CPU-intensive processing should be handled safely
        cpu_intensive_data = performance_stress_test_data["cpu_intensive"]
        max_time_ms = (
            performance_benchmark_config["max_processing_time_ms"] * 3
        )  # Allow more time for stress test

        async def run_cpu_stress_test():
            start_time = time.time()

            try:
                result = await security_pipeline.process_uploaded_image(
                    cpu_intensive_data, "cpu_stress_test.png", "test_session_cpu_stress"
                )

                end_time = time.time()
                processing_time_ms = (end_time - start_time) * 1000

                # Should complete within reasonable time or timeout gracefully
                assert processing_time_ms < max_time_ms, (
                    f"CPU stress test took {processing_time_ms:.2f}ms, "
                    f"should be < {max_time_ms}ms"
                )

                # Should handle CPU-intensive content (pass or fail gracefully)
                assert isinstance(result.is_safe, bool), "Should return boolean result"

            except Exception as e:
                # Resource protection exceptions are acceptable
                assert any(
                    keyword in str(e).lower()
                    for keyword in [
                        "timeout",
                        "resource",
                        "limit",
                        "memory",
                        "processing",
                    ]
                ), f"CPU exhaustion should be handled with appropriate exception: {e}"

        asyncio.run(run_cpu_stress_test())

    def test_memory_exhaustion_protection(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_stress_test_data: Dict[str, bytes],
    ):
        """Test protection against memory exhaustion attacks."""
        # Test: Memory-intensive processing should be protected
        memory_intensive_data = performance_stress_test_data["memory_intensive"]

        tracemalloc.start()

        async def run_memory_stress_test():
            try:
                result = await security_pipeline.process_uploaded_image(
                    memory_intensive_data,
                    "memory_stress_test.png",
                    "test_session_memory_stress",
                )

                # Check memory usage during stress test
                current, peak = tracemalloc.get_traced_memory()
                peak_mb = peak / 1024 / 1024

                # Should not use excessive memory
                assert peak_mb < 1000, (
                    f"Memory stress test used {peak_mb:.2f}MB, should be < 1000MB"
                )

                # Should handle memory-intensive content
                assert isinstance(result.is_safe, bool), "Should return boolean result"

            except Exception as e:
                # Memory protection exceptions are acceptable
                assert any(
                    keyword in str(e).lower()
                    for keyword in ["memory", "resource", "limit", "size", "allocation"]
                ), (
                    f"Memory exhaustion should be handled with appropriate exception: {e}"
                )

        asyncio.run(run_memory_stress_test())
        tracemalloc.stop()


class TestPerformanceMonitoring:
    """Test suite for performance monitoring and metrics."""

    def test_processing_statistics_accuracy(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_test_images: Dict[str, bytes],
    ):
        """Test accuracy of processing statistics and metrics."""
        # Test: Processing statistics should be accurate
        initial_stats = security_pipeline.get_processing_statistics()
        initial_total = initial_stats["total_processed"]

        async def run_stats_test():
            # Process several images
            processed_count = 0
            for image_name, image_data in performance_test_images.items():
                result = await security_pipeline.process_uploaded_image(
                    image_data,
                    f"stats_test_{image_name}.jpg",
                    f"test_session_stats_{image_name}",
                )
                if result.validation_passed:
                    processed_count += 1

            # Check updated statistics
            final_stats = security_pipeline.get_processing_statistics()

            # Statistics should reflect actual processing
            expected_total = initial_total + len(performance_test_images)
            assert final_stats["total_processed"] == expected_total, (
                f"Total processed should be {expected_total}, got {final_stats['total_processed']}"
            )

            # Success rate should be reasonable
            assert final_stats["success_rate"] >= 0.8, (
                f"Success rate {final_stats['success_rate']:.2%} should be >= 80%"
            )

            # Average processing time should be reasonable
            assert final_stats["avg_processing_time_ms"] < 2000, (
                f"Average processing time {final_stats['avg_processing_time_ms']:.2f}ms should be < 2000ms"
            )

        asyncio.run(run_stats_test())

    def test_health_check_performance(self, security_pipeline: ImageSecurityPipeline):
        """Test performance of health check operations."""
        # Test: Health checks should be fast and accurate
        start_time = time.time()

        health_status = security_pipeline.health_check()

        end_time = time.time()
        health_check_time_ms = (end_time - start_time) * 1000

        # Health check should be very fast
        assert health_check_time_ms < 100, (
            f"Health check took {health_check_time_ms:.2f}ms, should be < 100ms"
        )

        # Should return valid health status
        assert isinstance(health_status["pipeline_healthy"], bool), (
            "Should return boolean pipeline health status"
        )
        assert isinstance(health_status["validator_healthy"], bool), (
            "Should return boolean validator health status"
        )
        assert isinstance(health_status["processor_healthy"], bool), (
            "Should return boolean processor health status"
        )
