"""
C1 Image Security - Integration Security Tests.

Comprehensive testing of integration security between F1/F2 components and cross-module security.
Tests cover provider interface security, database integration security, and API endpoint security.

Test Categories:
- Provider interface security integration (3 tests)
- Database integration security (3 tests)
- API endpoint security integration (2 tests)
- Cross-module security validation (2 tests)
"""

import asyncio
from typing import Dict, List

from src.features.image_security.models import (
    ImageUploadRequest,
)
from src.features.image_security.pipeline import ImageSecurityPipeline


class TestProviderInterfaceSecurityIntegration:
    """Test suite for provider interface security integration."""

    def test_google_veo3_security_integration(
        self, security_pipeline: ImageSecurityPipeline, valid_jpeg_data: bytes
    ):
        """Test security integration with Google Veo3 provider interface."""

        # Test: Google Veo3 integration should maintain security standards
        async def run_veo3_integration_test():
            # Create upload request for Veo3
            upload_request = ImageUploadRequest(
                filename="veo3_security_test.jpg",
                content_type="image/jpeg",
                target_provider="google_veo3",
                provider_format="base64",
                strict_validation=True,
                scan_for_malware=True,
            )

            result = await security_pipeline.process_uploaded_image(
                valid_jpeg_data,
                "veo3_security_test.jpg",
                "test_session_veo3_security",
                upload_request,
            )

            # Should pass security validation
            assert result.is_safe is True, "Veo3 integration should maintain security"
            assert result.validation_passed is True, "Should pass validation for Veo3"
            assert result.provider_ready is True, "Should be ready for Veo3 provider"

            # Should have appropriate format for Veo3
            assert result.provider_format == "base64", (
                "Should use base64 format for Veo3"
            )
            assert result.base64_data is not None, "Should provide base64 data for Veo3"
            assert result.base64_data.startswith("data:image/"), (
                "Should have proper data URI format"
            )

            # Database record should include security validation
            db_record = result.to_database_record("test_session_veo3_security")
            assert db_record["validation_passed"] is True, (
                "DB record should show validation passed"
            )
            assert db_record["is_safe"] is True, "DB record should show image is safe"
            assert db_record["provider_ready"] is True, (
                "DB record should show provider readiness"
            )

        asyncio.run(run_veo3_integration_test())

    def test_azure_sora_security_integration(
        self, security_pipeline: ImageSecurityPipeline, valid_png_data: bytes
    ):
        """Test security integration with Azure Sora provider interface."""

        # Test: Azure Sora integration should maintain security standards
        async def run_sora_integration_test():
            # Create upload request for Sora
            upload_request = ImageUploadRequest(
                filename="sora_security_test.png",
                content_type="image/png",
                target_provider="azure_sora",
                provider_format="base64",
                strict_validation=True,
                scan_for_malware=True,
            )

            result = await security_pipeline.process_uploaded_image(
                valid_png_data,
                "sora_security_test.png",
                "test_session_sora_security",
                upload_request,
            )

            # Should pass security validation
            assert result.is_safe is True, "Sora integration should maintain security"
            assert result.validation_passed is True, "Should pass validation for Sora"
            assert result.provider_ready is True, "Should be ready for Sora provider"

            # Should have appropriate optimization for Sora
            assert result.metadata.provider_compatible is True, (
                "Should be compatible with provider interface"
            )
            assert (
                "provider_metadata" in result.metadata.provider_metadata
                or result.metadata.provider_metadata == {}
            ), "Should have provider metadata structure"

        asyncio.run(run_sora_integration_test())

    def test_provider_security_isolation(
        self,
        security_pipeline: ImageSecurityPipeline,
        valid_webp_data: bytes,
        security_helpers,
    ):
        """Test security isolation between provider interfaces."""

        # Test: Provider security should be isolated per session
        async def run_isolation_test():
            # Process same image for different providers
            veo3_request = ImageUploadRequest(
                filename="isolation_test_veo3.webp",
                content_type="image/webp",
                target_provider="google_veo3",
                provider_format="base64",
            )

            sora_request = ImageUploadRequest(
                filename="isolation_test_sora.webp",
                content_type="image/webp",
                target_provider="azure_sora",
                provider_format="base64",
            )

            # Process for both providers
            veo3_result = await security_pipeline.process_uploaded_image(
                valid_webp_data,
                "isolation_test_veo3.webp",
                "test_session_veo3_isolation",
                veo3_request,
            )

            sora_result = await security_pipeline.process_uploaded_image(
                valid_webp_data,
                "isolation_test_sora.webp",
                "test_session_sora_isolation",
                sora_request,
            )

            # Both should succeed but be isolated
            assert veo3_result.is_safe is True, "Veo3 processing should succeed"
            assert sora_result.is_safe is True, "Sora processing should succeed"

            # Should have different secure paths (session isolation)
            assert veo3_result.secure_path != sora_result.secure_path, (
                "Should have different secure paths for isolation"
            )

            # Verify security isolation
            assert security_helpers.verify_security_isolation(
                veo3_result, sora_result
            ), "Should maintain security isolation between providers"

        asyncio.run(run_isolation_test())


class TestDatabaseIntegrationSecurity:
    """Test suite for database integration security."""

    def test_database_record_security_validation(
        self, security_pipeline: ImageSecurityPipeline, valid_gif_data: bytes
    ):
        """Test security validation in database record creation."""

        # Test: Database records should include comprehensive security validation
        async def run_db_security_test():
            result = await security_pipeline.process_uploaded_image(
                valid_gif_data, "db_security_test.gif", "test_session_db_security"
            )

            # Convert to database record
            db_record = result.to_database_record("test_session_db_security")

            # Should include all security validation fields
            required_security_fields = [
                "session_id",
                "validation_passed",
                "is_safe",
                "processing_time_ms",
                "provider_ready",
                "created_at",
            ]

            for field in required_security_fields:
                assert field in db_record, f"Database record should include {field}"

            # Security fields should have correct values
            assert db_record["validation_passed"] is True, (
                "Should record validation status"
            )
            assert db_record["is_safe"] is True, "Should record safety status"
            assert db_record["session_id"] == "test_session_db_security", (
                "Should record session ID"
            )
            assert db_record["provider_ready"] is True, (
                "Should record provider readiness"
            )

            # Should not include sensitive data in metadata
            provider_metadata = db_record.get("provider_metadata", {})
            sensitive_keys = ["base64_data", "secure_path", "file_content"]
            for sensitive_key in sensitive_keys:
                assert sensitive_key not in provider_metadata, (
                    f"Database record should not include sensitive {sensitive_key}"
                )

        asyncio.run(run_db_security_test())

    def test_sql_injection_protection_database_integration(
        self, security_pipeline: ImageSecurityPipeline, valid_jpeg_data: bytes
    ):
        """Test SQL injection protection in database integration."""

        # Test: Database integration should protect against SQL injection
        async def run_sql_injection_test():
            # Try malicious session ID and filename
            malicious_session_id = "test'; DROP TABLE images; --"
            malicious_filename = 'test"; DELETE FROM users; --.jpg'

            try:
                result = await security_pipeline.process_uploaded_image(
                    valid_jpeg_data, malicious_filename, malicious_session_id
                )

                # Should handle malicious input safely
                db_record = result.to_database_record(malicious_session_id)

                # Session ID should be sanitized or safely handled
                assert isinstance(db_record["session_id"], str), (
                    "Session ID should be string"
                )

                # Filename should be sanitized
                safe_filename = db_record["filename"]
                dangerous_patterns = [";", "--", "DROP", "DELETE", "INSERT", "UPDATE"]
                for pattern in dangerous_patterns:
                    assert pattern not in safe_filename.upper(), (
                        f"Filename should not contain dangerous pattern: {pattern}"
                    )

            except Exception as e:
                # Validation rejection is acceptable for malicious input
                assert any(
                    keyword in str(e).lower()
                    for keyword in ["validation", "invalid", "unsafe", "error"]
                ), f"Should handle SQL injection safely: {e}"

        asyncio.run(run_sql_injection_test())

    def test_database_session_isolation_security(
        self,
        security_pipeline: ImageSecurityPipeline,
        valid_png_data: bytes,
        concurrent_test_session_ids: List[str],
    ):
        """Test database session isolation security."""

        # Test: Database records should maintain session isolation
        async def run_session_isolation_test():
            # Process images for multiple sessions
            results = []

            for session_id in concurrent_test_session_ids[:5]:  # Test with 5 sessions
                result = await security_pipeline.process_uploaded_image(
                    valid_png_data, f"session_isolation_{session_id}.png", session_id
                )

                db_record = result.to_database_record(session_id)
                results.append((session_id, db_record))

            # Verify session isolation in database records
            session_ids = [record[1]["session_id"] for record in results]
            unique_sessions = set(session_ids)

            assert len(unique_sessions) == len(results), (
                "Each database record should have unique session ID"
            )

            # Verify no cross-session data contamination
            for i, (session_id, db_record) in enumerate(results):
                for j, (other_session_id, other_db_record) in enumerate(results):
                    if i != j:
                        assert (
                            db_record["session_id"] != other_db_record["session_id"]
                            or db_record["session_id"] == session_id
                        ), "Database records should maintain session isolation"

        asyncio.run(run_session_isolation_test())


class TestAPIEndpointSecurityIntegration:
    """Test suite for API endpoint security integration."""

    def test_api_response_security_validation(
        self, security_pipeline: ImageSecurityPipeline, valid_webp_data: bytes
    ):
        """Test security validation in API response generation."""

        # Test: API responses should include comprehensive security information
        async def run_api_security_test():
            result = await security_pipeline.process_uploaded_image(
                valid_webp_data, "api_security_test.webp", "test_session_api_security"
            )

            # Convert to API response format
            api_response = result.to_api_response()

            # Should include all security fields
            required_api_fields = [
                "success",
                "is_safe",
                "validation_passed",
                "validation_results",
                "security_warnings",
                "provider_ready",
            ]

            for field in required_api_fields:
                assert field in api_response, f"API response should include {field}"

            # Security status should be clearly indicated
            assert api_response["success"] is True, "API should indicate success"
            assert api_response["is_safe"] is True, "API should indicate safety"
            assert api_response["validation_passed"] is True, (
                "API should indicate validation status"
            )

            # Should not expose sensitive internal data
            sensitive_internal_fields = ["secure_path", "internal_id", "session_data"]
            for field in sensitive_internal_fields:
                assert field not in api_response, (
                    f"API response should not expose sensitive {field}"
                )

            # Base64 data should be properly formatted if present
            if api_response.get("base64_data"):
                assert api_response["base64_data"].startswith("data:image/"), (
                    "Base64 data should have proper data URI format"
                )

        asyncio.run(run_api_security_test())

    def test_error_handling_security_api_integration(
        self, security_pipeline: ImageSecurityPipeline, corrupted_jpeg_data: bytes
    ):
        """Test secure error handling in API integration."""

        # Test: API error responses should not expose sensitive information
        async def run_error_security_test():
            result = await security_pipeline.process_uploaded_image(
                corrupted_jpeg_data,
                "error_security_test.jpg",
                "test_session_error_security",
            )

            # Should handle error securely
            assert result.is_safe is False, "Should detect corrupted image as unsafe"
            assert result.validation_passed is False, (
                "Should fail validation for corrupted image"
            )
            assert result.error_message is not None, "Should provide error message"

            # Convert to API response
            api_response = result.to_api_response()

            # Error response should be secure
            assert api_response["success"] is False, "API should indicate failure"
            assert api_response["error_message"] is not None, (
                "Should provide error message"
            )

            # Error message should not expose sensitive information
            error_message = api_response["error_message"].lower()
            sensitive_patterns = [
                "stack trace",
                "internal error",
                "file path",
                "database",
                "memory address",
                "exception",
            ]

            for pattern in sensitive_patterns:
                assert pattern not in error_message, (
                    f"Error message should not expose {pattern}"
                )

            # Should still provide helpful information for debugging
            assert len(api_response["error_message"]) > 10, (
                "Error message should be informative"
            )

        asyncio.run(run_error_security_test())


class TestCrossModuleSecurityValidation:
    """Test suite for cross-module security validation."""

    def test_f1_f2_security_compliance(
        self, security_pipeline: ImageSecurityPipeline, valid_gif_data: bytes
    ):
        """Test F1/F2 security compliance across modules."""

        # Test: Should maintain security compliance across F1/F2 integration
        async def run_f1_f2_compliance_test():
            result = await security_pipeline.process_uploaded_image(
                valid_gif_data,
                "f1_f2_compliance_test.gif",
                "test_session_f1_f2_compliance",
            )

            # F1 Database integration compliance
            db_record = result.to_database_record("test_session_f1_f2_compliance")

            # Should comply with F1 database schema expectations
            f1_required_fields = [
                "session_id",
                "filename",
                "validation_passed",
                "created_at",
            ]
            for field in f1_required_fields:
                assert field in db_record, f"Should comply with F1 requirement: {field}"

            # F2 Provider interface compliance
            assert result.provider_ready is True, (
                "Should be ready for F2 provider interface"
            )
            assert result.provider_format in ["base64", "url"], (
                "Should use F2-compatible provider format"
            )

            # Metadata should include F2 provider compatibility fields
            if result.metadata:
                assert result.metadata.provider_compatible is True, (
                    "Should indicate F2 provider compatibility"
                )
                assert isinstance(result.metadata.provider_metadata, dict), (
                    "Should include F2 provider metadata structure"
                )

        asyncio.run(run_f1_f2_compliance_test())

    def test_security_pipeline_end_to_end_validation(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_test_images: Dict[str, bytes],
    ):
        """Test end-to-end security validation across all pipeline components."""

        # Test: Complete pipeline should maintain security through all components
        async def run_end_to_end_security_test():
            security_results = []

            for image_name, image_data in performance_test_images.items():
                # Test each image type through complete pipeline
                result = await security_pipeline.process_uploaded_image(
                    image_data,
                    f"e2e_security_{image_name}.jpg",
                    f"test_session_e2e_{image_name}",
                )

                security_results.append((image_name, result))

                # Each result should pass comprehensive security validation
                assert result.is_safe is True, f"E2E: {image_name} should be safe"
                assert result.validation_passed is True, (
                    f"E2E: {image_name} should pass validation"
                )

                # Should have comprehensive validation results
                validation_results = result.validation_results
                critical_validations = [
                    "magic_number_valid",
                    "mime_type_valid",
                    "pil_validation_passed",
                    "security_scan_passed",
                ]

                for validation in critical_validations:
                    assert validation in validation_results, (
                        f"E2E: Should include {validation} for {image_name}"
                    )
                    assert validation_results[validation] is True, (
                        f"E2E: {validation} should pass for {image_name}"
                    )

                # Should be ready for provider interface
                assert result.provider_ready is True, (
                    f"E2E: {image_name} should be provider ready"
                )

                # Should have secure processing time
                assert result.total_processing_time_ms < 5000, (
                    f"E2E: {image_name} processing should be under 5s"
                )

            # Overall pipeline statistics should indicate healthy operation
            pipeline_stats = security_pipeline.get_processing_statistics()
            assert pipeline_stats["success_rate"] == 1.0, (
                "E2E: All valid images should process successfully"
            )
            assert pipeline_stats["total_processed"] >= len(performance_test_images), (
                "E2E: Should process all test images"
            )

        asyncio.run(run_end_to_end_security_test())
