#!/usr/bin/env python3
"""
C1 Image Upload Security Pipeline Integration Test Runner.

Comprehensive test runner for all integration tests with detailed reporting,
dependency validation, and performance metrics.
"""

import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

# Add src to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent.parent.parent
sys.path.insert(0, str(src_dir))


def setup_logging():
    """Set up logging for test execution."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("integration_test_results.log"),
        ],
    )
    return logging.getLogger(__name__)


def validate_dependencies() -> Dict[str, Any]:
    """Validate all dependencies before running tests."""
    logger = logging.getLogger(__name__)
    logger.info("🔍 Validating dependencies for integration tests...")

    validation_results = {
        "python_version": True,
        "required_packages": {},
        "environment_setup": True,
        "overall_valid": True,
    }

    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (
        python_version.major == 3 and python_version.minor < 8
    ):
        logger.error(
            f"❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}"
        )
        validation_results["python_version"] = False
        validation_results["overall_valid"] = False
    else:
        logger.info(
            f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}"
        )

    # Check required packages
    required_packages = [
        "pytest",
        "flask",
        "pydantic",
        "pillow",
        "python-magic",
        "sqlalchemy",
        "werkzeug",
        "requests",
    ]

    for package in required_packages:
        try:
            __import__(package)
            validation_results["required_packages"][package] = True
            logger.info(f"✅ {package} available")
        except ImportError:
            validation_results["required_packages"][package] = False
            validation_results["overall_valid"] = False
            logger.error(f"❌ {package} not available - install with: uv add {package}")

    # Check environment setup
    try:
        # Test configuration factory
        from src.config.factory import ConfigurationFactory

        config = ConfigurationFactory.create_environment_image_config()
        logger.info(
            f"✅ Configuration factory working - environment: {config.environment}"
        )

        # Test dependency validation
        dep_validation = ConfigurationFactory.validate_image_dependencies()
        dep_status = dep_validation.get("overall_status", "error")
        if dep_status in ["ok", "warning"]:
            logger.info(f"✅ Image dependencies: {dep_status}")
        else:
            logger.warning(f"⚠️ Image dependencies: {dep_status}")
            validation_results["environment_setup"] = False

    except Exception as e:
        logger.error(f"❌ Environment setup error: {e}")
        validation_results["environment_setup"] = False
        validation_results["overall_valid"] = False

    return validation_results


def run_test_suite(test_category: str = "all") -> Dict[str, Any]:
    """
    Run specific test suite with detailed reporting.

    Args:
        test_category: Category of tests to run (all, api, pipeline, security, etc.)

    Returns:
        Dict containing test execution results
    """
    logger = logging.getLogger(__name__)

    # Import pytest
    try:
        import pytest
    except ImportError:
        logger.error("❌ pytest not available - install with: uv add pytest")
        return {"success": False, "error": "pytest not available"}

    # Set up test environment
    os.environ["TESTING"] = "true"
    os.environ["FLASK_ENV"] = "testing"
    os.environ["SORA_IMAGE_ENVIRONMENT"] = "development"

    # Determine test files and markers
    test_dir = Path(__file__).parent

    test_configs = {
        "all": {
            "files": [
                str(test_dir / "test_integration_api.py"),
                str(test_dir / "test_integration_pipeline.py"),
            ],
            "markers": [],
        },
        "api": {
            "files": [str(test_dir / "test_integration_api.py")],
            "markers": ["api_integration"],
        },
        "pipeline": {
            "files": [str(test_dir / "test_integration_pipeline.py")],
            "markers": ["pipeline_integration"],
        },
        "security": {
            "files": [
                str(test_dir / "test_integration_api.py"),
                str(test_dir / "test_integration_pipeline.py"),
            ],
            "markers": ["security_validation"],
        },
        "cross_module": {
            "files": [
                str(test_dir / "test_integration_api.py"),
                str(test_dir / "test_integration_pipeline.py"),
            ],
            "markers": ["cross_module"],
        },
    }

    config = test_configs.get(test_category, test_configs["all"])

    # Build pytest arguments
    pytest_args = [
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker enforcement
        "--color=yes",  # Color output
        "--durations=10",  # Show 10 slowest tests
    ]

    # Add test files
    pytest_args.extend(config["files"])

    # Add markers if specified
    if config["markers"]:
        for marker in config["markers"]:
            pytest_args.extend(["-m", marker])

    # Add coverage if requested
    if os.getenv("WITH_COVERAGE", "false").lower() == "true":
        pytest_args.extend(
            [
                "--cov=src.features.image_security",
                "--cov=src.api.image_upload_routes",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
            ]
        )

    logger.info(f"🧪 Running {test_category} integration tests...")
    logger.info(f"📋 Test files: {len(config['files'])}")
    logger.info(f"🏷️ Markers: {config['markers'] or ['none']}")

    # Run tests
    start_time = time.time()

    try:
        exit_code = pytest.main(pytest_args)
        execution_time = time.time() - start_time

        results = {
            "success": exit_code == 0,
            "exit_code": exit_code,
            "execution_time_seconds": execution_time,
            "test_category": test_category,
            "files_tested": config["files"],
            "markers_used": config["markers"],
        }

        if exit_code == 0:
            logger.info(f"✅ All {test_category} tests passed in {execution_time:.2f}s")
        else:
            logger.error(
                f"❌ Some {test_category} tests failed (exit code: {exit_code})"
            )

        return results

    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"💥 Test execution error: {e}")

        return {
            "success": False,
            "error": str(e),
            "execution_time_seconds": execution_time,
            "test_category": test_category,
        }


def generate_test_report(results: List[Dict[str, Any]]) -> str:
    """Generate comprehensive test report."""
    logger = logging.getLogger(__name__)

    report_lines = [
        "=" * 80,
        "C1 IMAGE UPLOAD SECURITY PIPELINE - INTEGRATION TEST REPORT",
        "=" * 80,
        f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}",
        "",
    ]

    # Summary
    total_suites = len(results)
    successful_suites = sum(1 for r in results if r.get("success", False))
    total_time = sum(r.get("execution_time_seconds", 0) for r in results)

    report_lines.extend(
        [
            "SUMMARY:",
            f"  Test Suites: {successful_suites}/{total_suites} passed",
            f"  Total Time: {total_time:.2f} seconds",
            f"  Overall Status: {'✅ PASSED' if successful_suites == total_suites else '❌ FAILED'}",
            "",
        ]
    )

    # Detailed results
    report_lines.append("DETAILED RESULTS:")

    for result in results:
        category = result.get("test_category", "unknown")
        success = result.get("success", False)
        exec_time = result.get("execution_time_seconds", 0)

        status_emoji = "✅" if success else "❌"
        report_lines.extend(
            [
                f"  {status_emoji} {category.upper()} Tests:",
                f"    Status: {'PASSED' if success else 'FAILED'}",
                f"    Duration: {exec_time:.2f}s",
            ]
        )

        if "error" in result:
            report_lines.append(f"    Error: {result['error']}")

        if "files_tested" in result:
            report_lines.append(f"    Files: {len(result['files_tested'])} test files")

        report_lines.append("")

    # Recommendations
    report_lines.extend(
        [
            "RECOMMENDATIONS:",
            "1. Review any failed tests and fix underlying issues",
            "2. Run tests with coverage: WITH_COVERAGE=true python run_integration_tests.py",
            '3. Check dependency status: python -c "from src.config.factory import ConfigurationFactory; print(ConfigurationFactory.validate_image_dependencies())"',
            "4. Monitor performance: tests taking >5s may need optimization",
            "",
            "For detailed logs, check: integration_test_results.log",
            "=" * 80,
        ]
    )

    report_content = "\n".join(report_lines)

    # Write report to file
    report_file = Path("integration_test_report.txt")
    with open(report_file, "w") as f:
        f.write(report_content)

    logger.info(f"📋 Test report saved to: {report_file.absolute()}")

    return report_content


def main():
    """Main test runner function."""
    logger = setup_logging()

    print("🚀 C1 Image Upload Security Pipeline - Integration Test Runner")
    print("=" * 60)

    # Validate dependencies first
    print("\n1. DEPENDENCY VALIDATION")
    dep_validation = validate_dependencies()

    if not dep_validation["overall_valid"]:
        print(
            "❌ Dependency validation failed. Please fix issues before running tests."
        )
        sys.exit(1)

    print("✅ All dependencies validated successfully")

    # Run test suites
    print("\n2. INTEGRATION TEST EXECUTION")

    test_categories = ["api", "pipeline", "security", "cross_module"]
    all_results = []

    for category in test_categories:
        print(f"\n📋 Running {category} tests...")

        result = run_test_suite(category)
        all_results.append(result)

        if result["success"]:
            print(
                f"✅ {category} tests: PASSED ({result['execution_time_seconds']:.2f}s)"
            )
        else:
            print(f"❌ {category} tests: FAILED")
            if "error" in result:
                print(f"   Error: {result['error']}")

    # Generate and display report
    print("\n3. FINAL REPORT")
    report_content = generate_test_report(all_results)

    # Display summary
    successful_suites = sum(1 for r in all_results if r.get("success", False))
    total_suites = len(all_results)

    if successful_suites == total_suites:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ C1 Image Upload Security Pipeline is ready for deployment")
        sys.exit(0)
    else:
        print(
            f"\n💥 {total_suites - successful_suites}/{total_suites} TEST SUITES FAILED"
        )
        print("❌ Please review failures and fix issues before deployment")
        sys.exit(1)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Run C1 Image Upload Security Pipeline integration tests"
    )
    parser.add_argument(
        "--category",
        choices=["all", "api", "pipeline", "security", "cross_module"],
        default="all",
        help="Category of tests to run",
    )
    parser.add_argument(
        "--coverage", action="store_true", help="Run tests with coverage reporting"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    # Set environment variables based on arguments
    if args.coverage:
        os.environ["WITH_COVERAGE"] = "true"

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Run specific category if requested
    if args.category != "all":
        logger = setup_logging()
        print(f"🚀 Running {args.category} integration tests only")

        dep_validation = validate_dependencies()
        if not dep_validation["overall_valid"]:
            print("❌ Dependency validation failed")
            sys.exit(1)

        result = run_test_suite(args.category)

        if result["success"]:
            print(f"✅ {args.category} tests: PASSED")
            sys.exit(0)
        else:
            print(f"❌ {args.category} tests: FAILED")
            sys.exit(1)
    else:
        main()
