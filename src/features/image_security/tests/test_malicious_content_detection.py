"""
C1 Image Security - Malicious Content Detection Tests.

Comprehensive testing of malicious content detection in image security validation.
Tests cover steganography detection, script injection, format exploits, and payload detection.

Test Categories:
- Steganography and hidden data detection (6 tests)
- Script injection and XSS prevention (4 tests)
- Format-based exploit detection (4 tests)
- OWASP security compliance testing (4 tests)
"""

from typing import Any, Dict, List

import pytest

from src.features.image_security.models import ImageValidationError
from src.features.image_security.pipeline import ImageSecurityPipeline
from src.features.image_security.validators import ImageValidator


class TestSteganographyDetection:
    """Test suite for steganography and hidden data detection."""

    def test_steganography_tool_signature_detection(
        self, security_validator: ImageValidator, malicious_steganography_data: bytes
    ):
        """Test detection of steganography tool signatures."""
        # Test: Steganography tool signatures should be detected
        result = security_validator._perform_security_scan(malicious_steganography_data)

        assert result["steganography_check_passed"] is False, (
            "Steganography tool signature should be detected"
        )
        assert result["security_scan_passed"] is False, (
            "Overall security scan should fail when steganography detected"
        )

    def test_clean_image_steganography_check(
        self, security_validator: ImageValidator, valid_jpeg_data: bytes
    ):
        """Test steganography check with clean image."""
        # Test: Clean image should pass steganography check
        result = security_validator._perform_security_scan(valid_jpeg_data)

        assert result["steganography_check_passed"] is True, (
            "Clean image should pass steganography check"
        )

    def test_multiple_steganography_signatures(
        self,
        security_validator: ImageValidator,
        valid_png_data: bytes,
        security_helpers,
    ):
        """Test detection of multiple steganography tool signatures."""
        # Test: Multiple steganography signatures should be detected
        signatures = [b"steghide", b"jphide", b"outguess", b"F5"]

        for signature in signatures:
            malicious_data = security_helpers.inject_malicious_data(
                valid_png_data, signature, "end"
            )

            result = security_validator._perform_security_scan(malicious_data)
            assert result["steganography_check_passed"] is False, (
                f"Should detect {signature} steganography signature"
            )

    def test_entropy_analysis_detection(
        self, security_validator: ImageValidator, valid_jpeg_data: bytes
    ):
        """Test entropy analysis for steganography detection."""
        # Test: Low entropy patterns should be detected
        # Create low entropy data by repeating same bytes
        low_entropy_data = valid_jpeg_data[:-1000] + b"\x00" * 1000

        result = security_validator._perform_security_scan(low_entropy_data)

        # Entropy check should detect unusual patterns
        assert result["entropy_check_passed"] is False, (
            "Low entropy pattern should be detected"
        )

    def test_steganography_false_positive_prevention(
        self,
        security_validator: ImageValidator,
        performance_test_images: Dict[str, bytes],
    ):
        """Test prevention of steganography false positives."""
        # Test: Normal images should not trigger false positives
        for image_name, image_data in performance_test_images.items():
            result = security_validator._perform_security_scan(image_data)

            assert result["steganography_check_passed"] is True, (
                f"Normal image {image_name} should not trigger steganography false positive"
            )

    def test_steganography_payload_size_detection(
        self,
        security_validator: ImageValidator,
        valid_gif_data: bytes,
        security_helpers,
    ):
        """Test detection of large hidden payloads."""
        # Test: Large hidden payloads should be detected
        large_payload = b"hidden_payload_" * 1000  # Large hidden data

        malicious_data = security_helpers.inject_malicious_data(
            valid_gif_data, large_payload, "end"
        )

        result = security_validator._perform_security_scan(malicious_data)

        # Should detect unusually large data at end of image
        assert result["security_scan_passed"] is False, (
            "Large hidden payload should be detected"
        )


class TestScriptInjectionPrevention:
    """Test suite for script injection and XSS prevention."""

    def test_script_injection_detection(
        self, security_validator: ImageValidator, malicious_script_injection_data: bytes
    ):
        """Test detection of script injection in image data."""
        # Test: Script injection should be detected
        result = security_validator._perform_security_scan(
            malicious_script_injection_data
        )

        assert result["security_scan_passed"] is False, (
            "Script injection should be detected"
        )

    def test_html_injection_prevention(
        self,
        security_validator: ImageValidator,
        valid_png_data: bytes,
        security_helpers,
    ):
        """Test prevention of HTML injection in image data."""
        # Test: HTML injection attempts should be detected
        html_payloads = [
            b'<script>alert("xss")</script>',
            b'<iframe src="javascript:alert(1)"></iframe>',
            b"<img src=x onerror=alert(1)>",
            b"<svg onload=alert(1)></svg>",
        ]

        for payload in html_payloads:
            malicious_data = security_helpers.inject_malicious_data(
                valid_png_data, payload, "end"
            )

            # Should detect HTML/script content
            # Note: Basic implementation might not catch all, but should catch obvious ones
            try:
                result = security_validator._perform_security_scan(malicious_data)
                # Either detect as security issue or handle gracefully
                assert isinstance(result["security_scan_passed"], bool), (
                    f"Should handle HTML injection safely: {payload}"
                )
            except Exception:
                # Graceful failure is acceptable for security
                pass

    def test_javascript_url_injection_prevention(
        self,
        security_validator: ImageValidator,
        valid_jpeg_data: bytes,
        security_helpers,
    ):
        """Test prevention of JavaScript URL injection."""
        # Test: JavaScript URL injection should be prevented
        js_payloads = [
            b'javascript:alert("xss")',
            b"data:text/html;base64,PHNjcmlwdD5hbGVydCgieHNzIik8L3NjcmlwdD4=",
            b'vbscript:msgbox("xss")',
            b"javascript:void(0)",
        ]

        for payload in js_payloads:
            malicious_data = security_helpers.inject_malicious_data(
                valid_jpeg_data, payload, "middle"
            )

            result = security_validator._perform_security_scan(malicious_data)

            # Should handle JavaScript injection safely
            assert isinstance(result["security_scan_passed"], bool), (
                f"Should handle JavaScript injection safely: {payload}"
            )

    def test_sql_injection_pattern_detection(
        self,
        security_validator: ImageValidator,
        valid_webp_data: bytes,
        security_helpers,
    ):
        """Test detection of SQL injection patterns in image data."""
        # Test: SQL injection patterns should be detected
        sql_payloads = [
            b"'; DROP TABLE users; --",
            b"' OR '1'='1",
            b"UNION SELECT * FROM sensitive_data",
            b"'; DELETE FROM images; --",
        ]

        for payload in sql_payloads:
            malicious_data = security_helpers.inject_malicious_data(
                valid_webp_data, payload, "end"
            )

            # Basic security scan should handle SQL patterns safely
            result = security_validator._perform_security_scan(malicious_data)
            assert isinstance(result["security_scan_passed"], bool), (
                f"Should handle SQL injection patterns safely: {payload}"
            )


class TestFormatExploitDetection:
    """Test suite for format-based exploit detection."""

    def test_polyglot_file_detection(
        self,
        security_validator: ImageValidator,
        attack_simulation_data: Dict[str, bytes],
    ):
        """Test detection of polyglot files."""
        # Test: Polyglot files should be detected or handled safely
        polyglot_data = attack_simulation_data["polyglot"]

        # Full validation should detect format confusion
        is_valid, validation_results = security_validator.validate_image_data(
            polyglot_data, "polyglot_test.jpg"
        )

        assert is_valid is False, "Polyglot file should fail validation"
        assert validation_results["security_scan_passed"] is False, (
            "Security scan should detect polyglot file"
        )

    def test_format_confusion_attack_prevention(
        self,
        security_validator: ImageValidator,
        attack_simulation_data: Dict[str, bytes],
    ):
        """Test prevention of format confusion attacks."""
        # Test: Format confusion should be prevented
        confusion_data = attack_simulation_data["format_confusion"]

        is_valid, validation_results = security_validator.validate_image_data(
            confusion_data, "confusion_test.png"
        )

        assert is_valid is False, "Format confusion attack should be prevented"

    def test_compression_bomb_detection(
        self, security_validator: ImageValidator, security_test_vectors: Dict[str, Any]
    ):
        """Test detection of compression bomb attempts."""
        # Test: Compression bombs should be detected
        for bomb_data in security_test_vectors.get("compression_bombs", []):
            try:
                result = security_validator._perform_security_scan(bomb_data)
                # Should handle compression bombs safely
                assert isinstance(result["security_scan_passed"], bool), (
                    "Should handle compression bomb safely"
                )
            except Exception as e:
                # Resource protection is acceptable
                assert "memory" in str(e).lower() or "size" in str(e).lower()

    def test_buffer_overflow_exploit_prevention(
        self, security_validator: ImageValidator, security_test_vectors: Dict[str, Any]
    ):
        """Test prevention of buffer overflow exploits."""
        # Test: Buffer overflow attempts should be prevented
        for overflow_data in security_test_vectors["buffer_overflow"]:
            try:
                result = security_validator._perform_security_scan(overflow_data)

                # Should handle large buffers safely
                assert isinstance(result["security_scan_passed"], bool), (
                    "Should handle buffer overflow attempts safely"
                )

                # File size validation should catch oversized data
                assert result["file_size_valid"] is False, (
                    "Oversized data should fail file size validation"
                )

            except Exception as e:
                # Memory protection is acceptable
                assert "memory" in str(e).lower() or "size" in str(e).lower()


class TestOWASPSecurityCompliance:
    """Test suite for OWASP security compliance."""

    def test_owasp_injection_prevention(
        self,
        security_validator: ImageValidator,
        owasp_security_test_cases: Dict[str, List[str]],
        valid_jpeg_data: bytes,
        security_helpers,
    ):
        """Test OWASP injection prevention."""
        # Test: OWASP injection patterns should be prevented
        injection_cases = owasp_security_test_cases["injection"]

        for injection_pattern in injection_cases:
            injection_bytes = injection_pattern.encode("utf-8", errors="ignore")
            malicious_data = security_helpers.inject_malicious_data(
                valid_jpeg_data, injection_bytes, "end"
            )

            result = security_validator._perform_security_scan(malicious_data)
            # Should handle injection patterns safely
            assert isinstance(result["security_scan_passed"], bool), (
                f"Should handle injection pattern safely: {injection_pattern}"
            )

    def test_owasp_broken_access_control_prevention(
        self,
        security_validator: ImageValidator,
        owasp_security_test_cases: Dict[str, List[str]],
        directory_traversal_filename: str,
    ):
        """Test OWASP broken access control prevention."""
        # Test: Directory traversal should be prevented in filename validation
        access_control_cases = owasp_security_test_cases["broken_access_control"]

        for path_traversal in access_control_cases:
            try:
                # Test filename validation
                from ..models import ImageMetadata

                # Should fail validation or sanitize dangerous paths
                with pytest.raises((ValueError, ImageValidationError)):
                    ImageMetadata(
                        filename=path_traversal,
                        file_size=1000,
                        mime_type="image/jpeg",
                        width=640,
                        height=480,
                        magic_number_valid=True,
                        pil_validation_passed=True,
                        security_scan_passed=True,
                        processing_time_ms=100.0,
                    )
            except Exception:
                # Security validation rejection is expected
                pass

    def test_owasp_xss_prevention(
        self,
        security_validator: ImageValidator,
        owasp_security_test_cases: Dict[str, List[str]],
        valid_png_data: bytes,
        security_helpers,
    ):
        """Test OWASP XSS prevention."""
        # Test: XSS patterns should be handled safely
        xss_cases = owasp_security_test_cases["cross_site_scripting"]

        for xss_pattern in xss_cases:
            xss_bytes = xss_pattern.encode("utf-8", errors="ignore")
            malicious_data = security_helpers.inject_malicious_data(
                valid_png_data, xss_bytes, "end"
            )

            result = security_validator._perform_security_scan(malicious_data)
            # Should handle XSS patterns safely
            assert isinstance(result["security_scan_passed"], bool), (
                f"Should handle XSS pattern safely: {xss_pattern}"
            )

    def test_owasp_security_misconfiguration_detection(
        self, security_validator: ImageValidator, security_test_config
    ):
        """Test OWASP security misconfiguration detection."""
        # Test: Security configuration should be properly set
        config = security_test_config

        # Verify security features are enabled
        assert config.enable_magic_number_check is True, (
            "Magic number check should be enabled for security"
        )
        assert config.enable_pil_validation is True, (
            "PIL validation should be enabled for security"
        )
        assert config.strict_mime_type_check is True, (
            "Strict MIME type check should be enabled for security"
        )

        # Verify reasonable limits are set
        assert config.max_file_size_mb <= 50, (
            "File size limit should be reasonable for security"
        )
        assert config.max_width <= 8192, "Width limit should be reasonable for security"
        assert config.max_height <= 8192, (
            "Height limit should be reasonable for security"
        )


class TestMaliciousContentIntegration:
    """Integration tests for malicious content detection."""

    def test_end_to_end_malicious_content_detection(
        self,
        security_pipeline: ImageSecurityPipeline,
        malicious_steganography_data: bytes,
    ):
        """Test end-to-end malicious content detection through pipeline."""
        # Test: Malicious content should be detected through full pipeline
        import asyncio

        async def run_test():
            result = await security_pipeline.process_uploaded_image(
                malicious_steganography_data, "malicious_test.jpg", "test_session_123"
            )

            assert result.is_safe is False, (
                "Malicious content should be detected as unsafe"
            )
            assert result.validation_passed is False, (
                "Validation should fail for malicious content"
            )
            assert result.error_code == "SECURITY_VALIDATION_FAILED", (
                "Should return security validation error"
            )
            assert len(result.security_warnings) > 0, "Should provide security warnings"

        asyncio.run(run_test())

    def test_malicious_content_performance_impact(
        self,
        security_pipeline: ImageSecurityPipeline,
        malicious_script_injection_data: bytes,
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test performance impact of malicious content detection."""
        # Test: Malicious content detection should not significantly impact performance
        import asyncio
        import time

        async def run_test():
            start_time = time.time()

            result = await security_pipeline.process_uploaded_image(
                malicious_script_injection_data,
                "malicious_performance_test.png",
                "test_session_456",
            )

            end_time = time.time()
            processing_time_ms = (end_time - start_time) * 1000

            # Should complete within reasonable time even for malicious content
            max_time_ms = performance_benchmark_config["max_processing_time_ms"]
            assert processing_time_ms < max_time_ms, (
                f"Malicious content detection took {processing_time_ms:.2f}ms, "
                f"should be < {max_time_ms}ms"
            )

            assert result.is_safe is False, "Should detect malicious content"

        asyncio.run(run_test())

    def test_concurrent_malicious_content_detection(
        self,
        security_pipeline: ImageSecurityPipeline,
        attack_simulation_data: Dict[str, bytes],
        concurrent_test_session_ids: List[str],
    ):
        """Test concurrent malicious content detection."""
        # Test: Should handle concurrent malicious content detection safely
        import asyncio

        async def process_malicious_image(session_id: str, attack_data: bytes) -> tuple:
            try:
                result = await security_pipeline.process_uploaded_image(
                    attack_data, f"concurrent_malicious_{session_id}.jpg", session_id
                )
                return (session_id, result, True)
            except Exception:
                return (session_id, None, False)

        async def run_concurrent_test():
            tasks = []

            for i, session_id in enumerate(
                concurrent_test_session_ids[:5]
            ):  # Limit to 5 for test
                attack_type = list(attack_simulation_data.keys())[
                    i % len(attack_simulation_data)
                ]
                attack_data = attack_simulation_data[attack_type]

                task = process_malicious_image(session_id, attack_data)
                tasks.append(task)

            results = await asyncio.gather(*tasks)

            # All should complete (either successfully detecting malicious content or failing safely)
            completed_results = [
                r for r in results if r[2] or (r[1] and not r[1].is_safe)
            ]
            assert len(completed_results) >= len(tasks) * 0.8, (
                "At least 80% of concurrent malicious content tests should complete"
            )

        asyncio.run(run_concurrent_test())
