"""
C1 Image Security - Error Handling and Edge Case Tests.

Comprehensive testing of error handling, edge cases, and boundary conditions in image security.
Tests cover graceful failure, boundary validation, and exceptional condition handling.

Test Categories:
- Graceful error handling (4 tests)
- Boundary condition validation (2 tests)
- Exceptional condition handling (2 tests)
"""

import asyncio
from typing import Any, Dict
from unittest.mock import patch

from src.features.image_security.models import (
    ImageProcessingConfig,
    ImageValidationError,
)
from src.features.image_security.pipeline import ImageSecurityPipeline
from src.features.image_security.validators import ImageValidator


class TestGracefulErrorHandling:
    """Test suite for graceful error handling."""

    def test_corrupted_image_graceful_handling(
        self, security_pipeline: ImageSecurityPipeline, corrupted_jpeg_data: bytes
    ):
        """Test graceful handling of corrupted image data."""

        # Test: Corrupted images should fail gracefully with informative errors
        async def run_corrupted_handling_test():
            result = await security_pipeline.process_uploaded_image(
                corrupted_jpeg_data,
                "corrupted_graceful_test.jpg",
                "test_session_corrupted_graceful",
            )

            # Should fail gracefully
            assert result.is_safe is False, "Corrupted image should be marked as unsafe"
            assert result.validation_passed is False, (
                "Corrupted image should fail validation"
            )
            assert result.error_message is not None, "Should provide error message"
            assert result.error_code is not None, "Should provide error code"

            # Error should be informative but not expose sensitive details
            error_message = result.error_message.lower()
            assert "validation" in error_message or "invalid" in error_message, (
                "Error message should indicate validation issue"
            )

            # Should not crash or expose stack traces
            assert "traceback" not in error_message, "Should not expose stack traces"
            assert "exception" not in error_message, "Should not expose raw exceptions"

            # Processing time should still be recorded
            assert result.total_processing_time_ms > 0, (
                "Should record processing time even for errors"
            )

            # Should provide validation details for debugging
            assert isinstance(result.validation_results, dict), (
                "Should provide validation results"
            )

        asyncio.run(run_corrupted_handling_test())

    def test_invalid_filename_graceful_handling(
        self,
        security_pipeline: ImageSecurityPipeline,
        valid_png_data: bytes,
        security_test_vectors: Dict[str, Any],
    ):
        """Test graceful handling of invalid filenames."""

        # Test: Invalid filenames should be handled gracefully
        async def run_invalid_filename_test():
            malicious_filenames = security_test_vectors["metadata_exploits"]

            for malicious_filename in malicious_filenames:
                try:
                    result = await security_pipeline.process_uploaded_image(
                        valid_png_data,
                        malicious_filename,
                        "test_session_invalid_filename",
                    )

                    # If processing succeeds, filename should be sanitized
                    if result.validation_passed:
                        safe_filename = result.metadata.filename
                        dangerous_chars = ["..", "/", "\\", "\x00", "<", ">"]
                        for char in dangerous_chars:
                            assert char not in safe_filename, (
                                f"Filename should not contain dangerous character: {char}"
                            )
                    else:
                        # If processing fails, should fail gracefully
                        assert result.error_message is not None, (
                            "Should provide error for invalid filename"
                        )
                        assert result.error_code is not None, (
                            "Should provide error code"
                        )

                except Exception as e:
                    # Should handle invalid filenames gracefully, not crash
                    assert isinstance(e, ImageValidationError), (
                        f"Should raise ImageValidationError for invalid filename: {malicious_filename}"
                    )

        asyncio.run(run_invalid_filename_test())

    def test_network_timeout_graceful_handling(
        self, security_pipeline: ImageSecurityPipeline, valid_jpeg_data: bytes
    ):
        """Test graceful handling of network timeouts and external service failures."""

        # Test: External service failures should be handled gracefully
        async def run_timeout_handling_test():
            # Mock external service timeout
            with patch.object(
                security_pipeline.processor, "optimize_for_provider"
            ) as mock_optimize:
                mock_optimize.side_effect = TimeoutError("Network timeout")

                result = await security_pipeline.process_uploaded_image(
                    valid_jpeg_data,
                    "timeout_graceful_test.jpg",
                    "test_session_timeout_graceful",
                )

                # Should handle timeout gracefully
                assert result.is_safe is False, (
                    "Should mark as unsafe due to processing failure"
                )
                assert result.validation_passed is False, (
                    "Should fail validation due to timeout"
                )
                assert result.error_message is not None, (
                    "Should provide timeout error message"
                )
                assert (
                    "timeout" in result.error_message.lower()
                    or "processing error" in result.error_message.lower()
                ), "Error message should indicate timeout or processing issue"

                # Should not expose internal details
                assert "TimeoutError" not in result.error_message, (
                    "Should not expose internal exception names"
                )

        asyncio.run(run_timeout_handling_test())

    def test_memory_limit_graceful_handling(
        self, security_pipeline: ImageSecurityPipeline, memory_intensive_image: bytes
    ):
        """Test graceful handling of memory limit violations."""

        # Test: Memory limit violations should be handled gracefully
        async def run_memory_limit_test():
            # Mock memory error
            with patch.object(
                security_pipeline.validator, "_validate_with_pil"
            ) as mock_pil:
                mock_pil.side_effect = MemoryError("Out of memory")

                result = await security_pipeline.process_uploaded_image(
                    memory_intensive_image,
                    "memory_limit_graceful_test.png",
                    "test_session_memory_graceful",
                )

                # Should handle memory error gracefully
                assert result.is_safe is False, (
                    "Should mark as unsafe due to memory error"
                )
                assert result.validation_passed is False, (
                    "Should fail validation due to memory error"
                )
                assert result.error_message is not None, (
                    "Should provide memory error message"
                )

                # Error message should be user-friendly
                error_message = result.error_message.lower()
                assert (
                    "memory" in error_message
                    or "resource" in error_message
                    or "processing" in error_message
                ), "Should indicate resource issue"

                # Should not expose internal memory details
                assert "memoryerror" not in error_message, (
                    "Should not expose raw exception type"
                )


class TestBoundaryConditionValidation:
    """Test suite for boundary condition validation."""

    def test_file_size_boundary_validation(
        self, security_pipeline: ImageSecurityPipeline
    ):
        """Test file size boundary validation."""

        # Test: File size boundaries should be strictly enforced
        async def run_boundary_size_test():
            config = security_pipeline.config
            max_size_bytes = config.max_file_size_mb * 1024 * 1024

            # Test file exactly at limit
            at_limit_data = b"\xff\xd8\xff\xe0" + b"A" * (max_size_bytes - 4)

            try:
                result = await security_pipeline.process_uploaded_image(
                    at_limit_data, "at_limit_test.jpg", "test_session_at_limit"
                )

                # Should either succeed or fail gracefully
                if not result.validation_passed:
                    assert "size" in result.error_message.lower(), (
                        "Should indicate size issue if failing"
                    )
            except ImageValidationError as e:
                assert "size" in str(e).lower(), "Should indicate size limit violation"

            # Test file over limit
            over_limit_data = b"\xff\xd8\xff\xe0" + b"B" * (max_size_bytes + 1000)

            try:
                result = await security_pipeline.process_uploaded_image(
                    over_limit_data, "over_limit_test.jpg", "test_session_over_limit"
                )

                # Should fail validation
                assert result.validation_passed is False, (
                    "Oversized file should fail validation"
                )
                assert "size" in result.error_message.lower(), (
                    "Should indicate size limit exceeded"
                )

            except ImageValidationError as e:
                assert "size" in str(e).lower() or "large" in str(e).lower(), (
                    "Should indicate size limit violation"
                )

        asyncio.run(run_boundary_size_test())

    def test_dimension_boundary_validation(
        self, security_validator: ImageValidator, security_test_config
    ):
        """Test image dimension boundary validation."""
        # Test: Dimension boundaries should be strictly enforced
        max_width = security_test_config.max_width
        max_height = security_test_config.max_height

        # Test dimensions exactly at limit
        at_limit_result = security_validator._validate_image_dimensions(
            max_width, max_height
        )
        assert at_limit_result is True, "Dimensions at limit should be valid"

        # Test dimensions over limit
        over_width_result = security_validator._validate_image_dimensions(
            max_width + 1, max_height
        )
        assert over_width_result is False, "Width over limit should be invalid"

        over_height_result = security_validator._validate_image_dimensions(
            max_width, max_height + 1
        )
        assert over_height_result is False, "Height over limit should be invalid"

        # Test minimum dimensions
        min_dim_result = security_validator._validate_image_dimensions(10, 10)
        assert min_dim_result is True, "Minimum valid dimensions should pass"

        too_small_result = security_validator._validate_image_dimensions(5, 5)
        assert too_small_result is False, "Too small dimensions should fail"

        # Test zero and negative dimensions
        zero_result = security_validator._validate_image_dimensions(0, 100)
        assert zero_result is False, "Zero dimension should fail"

        negative_result = security_validator._validate_image_dimensions(-100, 100)
        assert negative_result is False, "Negative dimension should fail"


class TestExceptionalConditionHandling:
    """Test suite for exceptional condition handling."""

    def test_concurrent_session_conflict_handling(
        self, security_pipeline: ImageSecurityPipeline, valid_webp_data: bytes
    ):
        """Test handling of concurrent session conflicts."""

        # Test: Concurrent sessions should be handled without conflicts
        async def run_concurrent_conflict_test():
            same_session_id = "test_session_concurrent_conflict"

            # Process multiple images concurrently with same session ID
            async def process_image(image_num: int):
                try:
                    result = await security_pipeline.process_uploaded_image(
                        valid_webp_data,
                        f"concurrent_conflict_{image_num}.webp",
                        same_session_id,
                    )
                    return (image_num, result, True)
                except Exception:
                    return (image_num, None, False)

            # Create concurrent tasks
            tasks = [process_image(i) for i in range(5)]
            results = await asyncio.gather(*tasks)

            # All should complete successfully or fail gracefully
            successful_results = [r for r in results if r[2]]
            assert len(successful_results) >= 4, (
                "At least 4 out of 5 concurrent operations should succeed"
            )

            # No results should interfere with each other
            for i, (image_num, result, success) in enumerate(results):
                if success and result:
                    # Each should have unique secure path despite same session
                    assert (
                        f"concurrent_conflict_{image_num}" in result.secure_path
                        or result.secure_path is not None
                    ), "Should maintain unique processing even with session conflicts"

        asyncio.run(run_concurrent_conflict_test())

    def test_resource_exhaustion_recovery(
        self,
        security_pipeline: ImageSecurityPipeline,
        performance_stress_test_data: Dict[str, bytes],
    ):
        """Test recovery from resource exhaustion conditions."""

        # Test: System should recover from resource exhaustion
        async def run_resource_exhaustion_test():
            stress_data = performance_stress_test_data["memory_intensive"]

            # Process resource-intensive data multiple times
            results = []
            for i in range(3):  # Multiple iterations to test recovery
                try:
                    result = await security_pipeline.process_uploaded_image(
                        stress_data,
                        f"resource_exhaustion_{i}.png",
                        f"test_session_exhaustion_{i}",
                    )
                    results.append((i, result, True))

                except Exception as e:
                    # Resource exhaustion exceptions are acceptable
                    if any(
                        keyword in str(e).lower()
                        for keyword in ["memory", "resource", "timeout", "limit"]
                    ):
                        results.append((i, None, False))
                    else:
                        # Unexpected exceptions should still be handled
                        results.append((i, None, False))

            # Should handle resource exhaustion gracefully
            assert len(results) == 3, "Should attempt all iterations"

            # System should remain functional after resource stress
            # Test with normal image after stress
            normal_result = await security_pipeline.process_uploaded_image(
                b"\xff\xd8\xff\xe0" + b"normal_data" * 100,  # Small normal image
                "recovery_test.jpg",
                "test_session_recovery",
            )

            # System should recover and process normal images
            assert isinstance(normal_result.is_safe, bool), (
                "System should recover and process normal images after stress"
            )

        asyncio.run(run_resource_exhaustion_test())


class TestEdgeCaseValidation:
    """Additional edge case validation tests."""

    def test_empty_session_id_handling(
        self, security_pipeline: ImageSecurityPipeline, valid_gif_data: bytes
    ):
        """Test handling of empty or invalid session IDs."""

        # Test: Empty session IDs should be handled appropriately
        async def run_empty_session_test():
            invalid_session_ids = ["", "   ", "\t\n", "x" * 300, "test\x00session"]

            for invalid_session in invalid_session_ids:
                try:
                    result = await security_pipeline.process_uploaded_image(
                        valid_gif_data, "empty_session_test.gif", invalid_session
                    )

                    # If processing succeeds, session should be handled safely
                    if result.validation_passed:
                        db_record = result.to_database_record(invalid_session)
                        assert isinstance(db_record["session_id"], str), (
                            "Session ID should be handled as string"
                        )
                    else:
                        assert (
                            "session" in result.error_message.lower()
                            or "invalid" in result.error_message.lower()
                        ), "Should indicate session issue"

                except ImageValidationError as e:
                    # Validation rejection is acceptable for invalid sessions
                    assert "session" in str(e).lower() or "invalid" in str(e).lower(), (
                        f"Should indicate session validation error: {invalid_session}"
                    )

        asyncio.run(run_empty_session_test())

    def test_unicode_filename_handling(
        self, security_pipeline: ImageSecurityPipeline, valid_png_data: bytes
    ):
        """Test handling of Unicode filenames and edge cases."""

        # Test: Unicode filenames should be handled properly
        async def run_unicode_filename_test():
            unicode_filenames = [
                "测试图片.png",  # Chinese characters
                "тест_изображение.png",  # Cyrillic characters
                "🖼️_test_image.png",  # Emoji
                "café_münü.png",  # Accented characters
                "file_with_\u202e_override.png",  # Unicode override
                "normal_file.png",  # Normal for comparison
            ]

            for unicode_filename in unicode_filenames:
                try:
                    result = await security_pipeline.process_uploaded_image(
                        valid_png_data, unicode_filename, "test_session_unicode"
                    )

                    # Should handle Unicode filenames appropriately
                    if result.validation_passed:
                        safe_filename = result.metadata.filename
                        # Should maintain some form of the filename
                        assert len(safe_filename) > 0, "Filename should not be empty"
                        assert (
                            safe_filename.endswith(".png") or ".png" in safe_filename
                        ), "Should preserve file extension"
                    else:
                        # If validation fails, should provide appropriate error
                        assert result.error_message is not None, (
                            f"Should provide error for problematic filename: {unicode_filename}"
                        )

                except Exception as e:
                    # Unicode handling exceptions should be graceful
                    assert isinstance(
                        e, (ImageValidationError, UnicodeError, ValueError)
                    ), f"Should handle Unicode filename gracefully: {unicode_filename}"

        asyncio.run(run_unicode_filename_test())

    def test_configuration_edge_cases(self):
        """Test edge cases in configuration handling."""
        # Test: Configuration edge cases should be handled properly

        # Test with zero limits
        try:
            zero_config = ImageProcessingConfig(
                max_file_size_mb=0,
                max_width=0,
                max_height=0,
                processing_timeout_seconds=0,
            )
            # Should either create with minimum values or raise validation error
            assert (
                zero_config.max_file_size_mb > 0 or True
            )  # Pydantic should handle this
        except Exception:
            # Configuration validation rejection is acceptable
            pass

        # Test with extremely large limits
        try:
            large_config = ImageProcessingConfig(
                max_file_size_mb=10000,  # 10GB
                max_width=100000,
                max_height=100000,
                processing_timeout_seconds=86400,  # 24 hours
            )
            # Should either accept or limit to reasonable values
            assert isinstance(large_config, ImageProcessingConfig)
        except Exception:
            # Configuration validation rejection is acceptable
            pass

        # Test with negative values
        try:
            negative_config = ImageProcessingConfig(
                max_file_size_mb=-1, max_width=-100, max_height=-100
            )
            # Should reject negative values
            assert False, "Should not accept negative configuration values"
        except Exception:
            # Rejection of negative values is expected
            pass
