"""
Integration tests for C1 Image Security Pipeline components.

Tests the integration between validators, processors, and pipeline orchestration
with comprehensive security validation scenarios.
"""

from unittest.mock import Mock, patch

import pytest

from src.config.factory import ConfigurationFactory
from src.features.image_security import (
    ImageProcessingConfig,
    ImageProcessor,
    ImageSecurityPipeline,
    ImageUploadRequest,
    ImageValidator,
)


@pytest.fixture
def test_config():
    """Create test configuration."""
    return ImageProcessingConfig(
        max_file_size_mb=5,
        max_width=1024,
        max_height=1024,
        enable_magic_number_check=True,
        enable_pil_validation=True,
        enable_malware_scan=False,
        strict_mime_type_check=True,
        processing_timeout_seconds=10,
    )


@pytest.fixture
def security_pipeline(test_config):
    """Create image security pipeline for testing."""
    return ImageSecurityPipeline(test_config)


@pytest.fixture
def image_validator(test_config):
    """Create image validator for testing."""
    return ImageValidator(test_config)


@pytest.fixture
def image_processor(test_config):
    """Create image processor for testing."""
    return ImageProcessor(test_config)


@pytest.fixture
def sample_jpeg_bytes():
    """Create sample JPEG image bytes."""
    # Minimal valid JPEG structure
    jpeg_header = (
        b"\\xFF\\xD8\\xFF\\xE0\\x00\\x10JFIF\\x00\\x01\\x01\\x01\\x00H\\x00H\\x00\\x00"
    )
    jpeg_content = b"\\xFF\\xC0\\x00\\x11\\x08\\x00\\x64\\x00\\x64\\x01\\x01\\x22\\x00\\x02\\x11\\x01\\x03\\x11\\x01"
    jpeg_footer = b"\\xFF\\xD9"

    return jpeg_header + jpeg_content + jpeg_footer


@pytest.fixture
def sample_png_bytes():
    """Create sample PNG image bytes."""
    # Minimal valid PNG structure
    png_signature = b"\\x89PNG\\r\\n\\x1a\\n"
    ihdr_chunk = b"\\x00\\x00\\x00\\rIHDR\\x00\\x00\\x00\\x64\\x00\\x00\\x00\\x64\\x08\\x02\\x00\\x00\\x00\\xff\\x80\\x02\\x03"
    iend_chunk = b"\\x00\\x00\\x00\\x00IEND\\xaeB`\\x82"

    return png_signature + ihdr_chunk + iend_chunk


@pytest.fixture
def upload_request():
    """Create sample upload request."""
    return ImageUploadRequest(
        filename="test_image.jpg",
        content_type="image/jpeg",
        target_provider="google_veo3",
    )


class TestImageValidatorIntegration:
    """Integration tests for image validator component."""

    def test_validate_jpeg_image_success(self, image_validator, sample_jpeg_bytes):
        """Test successful JPEG validation integration."""
        with patch.object(image_validator, "_magic_mime") as mock_magic:
            mock_magic.from_buffer.return_value = "image/jpeg"

            # Test validation
            is_valid, results = image_validator.validate_image_data(
                sample_jpeg_bytes, "test.jpg"
            )

            # Assertions
            assert is_valid is True
            assert results["magic_number_valid"] is True
            assert results["mime_type_valid"] is True
            assert results["extension_match"] is True

    def test_validate_png_image_success(self, image_validator, sample_png_bytes):
        """Test successful PNG validation integration."""
        with patch.object(image_validator, "_magic_mime") as mock_magic:
            mock_magic.from_buffer.return_value = "image/png"

            # Test validation
            is_valid, results = image_validator.validate_image_data(
                sample_png_bytes, "test.png"
            )

            # Assertions
            assert is_valid is True
            assert results["magic_number_valid"] is True
            assert results["mime_type_valid"] is True
            assert results["extension_match"] is True

    def test_validate_malicious_file_detection(self, image_validator):
        """Test detection of malicious files."""
        # Create fake image with malicious content
        malicious_data = b"\\xFF\\xD8\\xFF\\xE0" + b"<script>alert(1)</script>" * 50

        with patch.object(image_validator, "_magic_mime") as mock_magic:
            mock_magic.from_buffer.return_value = "text/html"  # Wrong MIME type

            # Test validation
            is_valid, results = image_validator.validate_image_data(
                malicious_data, "fake.jpg"
            )

            # Assertions
            assert is_valid is False
            assert results["mime_type_valid"] is False

    def test_metadata_extraction_integration(self, image_validator, sample_jpeg_bytes):
        """Test metadata extraction integration."""
        validation_results = {
            "magic_number_valid": True,
            "pil_validation_passed": True,
            "security_scan_passed": True,
            "pil_width": 100,
            "pil_height": 100,
            "pil_format": "JPEG",
        }

        with patch.object(image_validator, "_detect_mime_type") as mock_detect:
            mock_detect.return_value = "image/jpeg"

            # Test metadata extraction
            metadata = image_validator.extract_metadata(
                sample_jpeg_bytes, "test.jpg", validation_results
            )

            # Assertions
            assert metadata.filename == "test.jpg"
            assert metadata.mime_type == "image/jpeg"
            assert metadata.width == 100
            assert metadata.height == 100
            assert metadata.magic_number_valid is True
            assert metadata.pil_validation_passed is True


class TestImageProcessorIntegration:
    """Integration tests for image processor component."""

    @patch("PIL.Image.open")
    def test_process_image_for_provider_integration(
        self, mock_open, image_processor, sample_jpeg_bytes
    ):
        """Test complete image processing for provider integration."""
        # Mock PIL Image
        mock_img = Mock()
        mock_img.width = 800
        mock_img.height = 600
        mock_img.mode = "RGB"
        mock_img.size = (800, 600)

        # Mock image operations
        mock_img.resize.return_value = mock_img
        mock_img.save = Mock()

        mock_context = Mock()
        mock_context.__enter__.return_value = mock_img
        mock_context.__exit__.return_value = None
        mock_open.return_value = mock_context

        # Test processing
        processed_bytes, metadata = image_processor.process_image_for_provider(
            sample_jpeg_bytes,
            target_format="image/jpeg",
            max_width=1024,
            max_height=1024,
        )

        # Assertions
        assert isinstance(processed_bytes, bytes)
        assert isinstance(metadata, dict)
        assert "original_dimensions" in metadata
        assert "final_dimensions" in metadata
        assert "processing_steps" in metadata
        assert "format_converted" in metadata["processing_steps"]

    def test_create_base64_data_uri_integration(self, image_processor):
        """Test base64 data URI creation integration."""
        test_bytes = b"test_image_data"
        mime_type = "image/jpeg"

        # Test base64 creation
        data_uri = image_processor.create_base64_data_uri(test_bytes, mime_type)

        # Assertions
        assert data_uri.startswith("data:image/jpeg;base64,")
        assert len(data_uri) > len("data:image/jpeg;base64,")

        # Verify base64 decoding works
        import base64

        base64_part = data_uri.split(",", 1)[1]
        decoded = base64.b64decode(base64_part)
        assert decoded == test_bytes

    def test_optimize_for_provider_integration(
        self, image_processor, sample_jpeg_bytes
    ):
        """Test provider-specific optimization integration."""
        with patch.object(
            image_processor, "process_image_for_provider"
        ) as mock_process:
            mock_process.return_value = (b"optimized_data", {"test": "metadata"})

            # Test Google Veo3 optimization
            optimized_bytes, metadata = image_processor.optimize_for_provider(
                sample_jpeg_bytes, "google_veo3"
            )

            # Assertions
            assert optimized_bytes == b"optimized_data"
            assert "provider" in metadata
            assert metadata["provider"] == "google_veo3"
            assert "veo3_format_optimization" in metadata["optimizations_applied"]

            # Verify correct parameters were passed
            mock_process.assert_called_with(
                sample_jpeg_bytes,
                target_format="image/jpeg",
                max_width=1920,
                max_height=1080,
            )

    def test_secure_filename_creation_integration(self, image_processor):
        """Test secure filename creation integration."""
        original_filename = "test image file.jpg"
        session_id = "session_12345678"

        # Test secure filename creation
        secure_name = image_processor.create_secure_filename(
            original_filename, session_id
        )

        # Assertions
        assert session_id[:8] in secure_name
        assert secure_name.endswith(".jpg")
        assert len(secure_name) <= 100  # Length limit
        assert " " not in secure_name  # No spaces
        assert ".." not in secure_name  # No directory traversal


class TestImageSecurityPipelineIntegration:
    """Integration tests for complete image security pipeline."""

    def test_process_uploaded_image_success_integration(
        self, security_pipeline, sample_jpeg_bytes, upload_request
    ):
        """Test complete successful image processing integration."""
        session_id = "test_session_12345678"

        with patch.object(
            security_pipeline.validator, "validate_image_data"
        ) as mock_validate:
            with patch.object(
                security_pipeline.validator, "extract_metadata"
            ) as mock_extract:
                with patch.object(
                    security_pipeline.processor, "optimize_for_provider"
                ) as mock_optimize:
                    with patch.object(
                        security_pipeline.processor, "create_base64_data_uri"
                    ) as mock_base64:
                        # Mock successful validation
                        mock_validate.return_value = (
                            True,
                            {
                                "magic_number_valid": True,
                                "pil_validation_passed": True,
                                "security_scan_passed": True,
                            },
                        )

                        # Mock metadata extraction
                        from src.features.image_security.models import ImageMetadata

                        mock_metadata = ImageMetadata(
                            filename="test.jpg",
                            file_size=len(sample_jpeg_bytes),
                            mime_type="image/jpeg",
                            width=800,
                            height=600,
                            magic_number_valid=True,
                            pil_validation_passed=True,
                            security_scan_passed=True,
                            processing_time_ms=0.0,
                        )
                        mock_extract.return_value = mock_metadata

                        # Mock optimization and base64 creation
                        mock_optimize.return_value = (
                            b"optimized_data",
                            {"test": "metadata"},
                        )
                        mock_base64.return_value = "data:image/jpeg;base64,dGVzdA=="

                        # Test processing
                        result = security_pipeline.process_uploaded_image(
                            image_data=sample_jpeg_bytes,
                            filename="test.jpg",
                            session_id=session_id,
                            upload_request=upload_request,
                        )

                        # Assertions
                        assert result.is_safe is True
                        assert result.validation_passed is True
                        assert result.metadata is not None
                        assert result.base64_data == "data:image/jpeg;base64,dGVzdA=="
                        assert result.provider_ready is True
                        assert result.total_processing_time_ms > 0

    def test_process_uploaded_image_validation_failure(
        self, security_pipeline, sample_jpeg_bytes, upload_request
    ):
        """Test image processing with validation failure."""
        session_id = "test_session_12345678"

        with patch.object(
            security_pipeline.validator, "validate_image_data"
        ) as mock_validate:
            # Mock validation failure
            mock_validate.return_value = (
                False,
                {
                    "magic_number_valid": False,
                    "pil_validation_passed": False,
                    "security_scan_passed": False,
                },
            )

            # Test processing
            result = security_pipeline.process_uploaded_image(
                image_data=sample_jpeg_bytes,
                filename="malicious.jpg",
                session_id=session_id,
                upload_request=upload_request,
            )

            # Assertions
            assert result.is_safe is False
            assert result.validation_passed is False
            assert result.error_message is not None
            assert result.error_code == "SECURITY_VALIDATION_FAILED"
            assert result.provider_ready is False

    def test_pipeline_statistics_integration(self, security_pipeline):
        """Test pipeline statistics tracking integration."""
        # Get initial statistics
        initial_stats = security_pipeline.get_processing_statistics()

        assert "total_processed" in initial_stats
        assert "successful_processed" in initial_stats
        assert "validation_failures" in initial_stats
        assert "success_rate" in initial_stats
        assert "configuration" in initial_stats

        # Verify configuration is included
        config = initial_stats["configuration"]
        assert "max_file_size_mb" in config
        assert "default_provider" in config

    def test_pipeline_health_check_integration(self, security_pipeline):
        """Test pipeline health check integration."""
        # Test health check
        health = security_pipeline.health_check()

        # Assertions
        assert "pipeline_healthy" in health
        assert "validator_healthy" in health
        assert "processor_healthy" in health
        assert "configuration_valid" in health

        # Should be healthy with valid configuration
        assert health["pipeline_healthy"] is True
        assert health["configuration_valid"] is True

    def test_pipeline_with_different_providers(
        self, security_pipeline, sample_jpeg_bytes
    ):
        """Test pipeline integration with different provider configurations."""
        session_id = "test_session_12345678"

        # Test with Google Veo3
        veo3_request = ImageUploadRequest(
            filename="test.jpg",
            content_type="image/jpeg",
            target_provider="google_veo3",
        )

        with patch.object(
            security_pipeline.validator, "validate_image_data"
        ) as mock_validate:
            with patch.object(
                security_pipeline.validator, "extract_metadata"
            ) as mock_extract:
                with patch.object(
                    security_pipeline.processor, "optimize_for_provider"
                ) as mock_optimize:
                    # Mock successful validation
                    mock_validate.return_value = (True, {"test": True})

                    from src.features.image_security.models import ImageMetadata

                    mock_metadata = ImageMetadata(
                        filename="test.jpg",
                        file_size=len(sample_jpeg_bytes),
                        mime_type="image/jpeg",
                        width=800,
                        height=600,
                        magic_number_valid=True,
                        pil_validation_passed=True,
                        security_scan_passed=True,
                        processing_time_ms=0.0,
                    )
                    mock_extract.return_value = mock_metadata
                    mock_optimize.return_value = (
                        b"optimized",
                        {"provider": "google_veo3"},
                    )

                    # Test processing with Veo3
                    result = security_pipeline.process_uploaded_image(
                        image_data=sample_jpeg_bytes,
                        filename="test.jpg",
                        session_id=session_id,
                        upload_request=veo3_request,
                    )

                    # Verify provider was passed correctly
                    mock_optimize.assert_called_with(sample_jpeg_bytes, "google_veo3")


class TestConfigurationFactoryIntegration:
    """Integration tests for configuration factory with image security."""

    def test_create_image_security_config_integration(self):
        """Test image security configuration creation integration."""
        # Test basic configuration creation
        config = ConfigurationFactory.create_image_security_config()

        assert hasattr(config, "max_file_size_mb")
        assert hasattr(config, "enable_magic_number_check")
        assert hasattr(config, "environment")

        # Test with overrides
        config_with_overrides = ConfigurationFactory.create_image_security_config(
            max_file_size_mb=20, enable_malware_scan=True
        )

        assert config_with_overrides.max_file_size_mb == 20
        assert config_with_overrides.enable_malware_scan is True

    def test_environment_specific_config_integration(self):
        """Test environment-specific configuration integration."""
        # Test development configuration
        dev_config = ConfigurationFactory.create_environment_image_config("development")
        assert dev_config.development_mode is True
        assert dev_config.environment == "development"

        # Test production configuration
        prod_config = ConfigurationFactory.create_environment_image_config("production")
        assert prod_config.development_mode is False
        assert prod_config.environment == "production"
        assert prod_config.enable_malware_scan is True
        assert prod_config.max_file_size_mb <= 10

    def test_dependency_validation_integration(self):
        """Test dependency validation integration."""
        # Test dependency validation
        validation_result = ConfigurationFactory.validate_image_dependencies()

        assert "overall_status" in validation_result
        assert "dependencies" in validation_result
        assert "timestamp" in validation_result

        # Check specific dependencies
        dependencies = validation_result["dependencies"]
        assert "pillow" in dependencies
        assert "python_magic" in dependencies

        # Test health check
        health_result = ConfigurationFactory.get_image_dependency_health()

        assert "healthy" in health_result
        assert "components" in health_result


class TestImageProcessingConfigIntegration:
    """Integration tests for image processing configuration."""

    def test_from_environment_integration(self):
        """Test configuration creation from environment integration."""
        with patch.object(
            ConfigurationFactory, "create_environment_image_config"
        ) as mock_factory:
            # Mock security configuration
            mock_security_config = Mock()
            mock_security_config.max_file_size_mb = 12
            mock_security_config.max_width = 2048
            mock_security_config.enable_magic_number_check = True
            mock_security_config.jpeg_quality = 90
            mock_security_config.default_provider = "google_veo3"

            mock_factory.return_value = mock_security_config

            # Test configuration creation
            config = ImageProcessingConfig.from_environment()

            # Assertions
            assert config.max_file_size_mb == 12
            assert config.max_width == 2048
            assert config.enable_magic_number_check is True
            assert config.jpeg_quality == 90
            assert config.default_provider == "google_veo3"

    def test_from_security_config_integration(self):
        """Test configuration creation from security config integration."""
        # Create security configuration
        security_config = ConfigurationFactory.create_image_security_config(
            max_file_size_mb=15, jpeg_quality=95, enable_malware_scan=True
        )

        # Create processing configuration
        processing_config = ImageProcessingConfig.from_security_config(security_config)

        # Assertions
        assert processing_config.max_file_size_mb == 15
        assert processing_config.jpeg_quality == 95
        assert processing_config.enable_malware_scan is True
