"""
Integration tests for C1 Image Upload Security Pipeline Flask API.

Tests the complete integration between Flask API endpoints, security pipeline,
database storage, and real-time WebSocket communication.
"""

import io
import json
from unittest.mock import Mock, patch

import pytest
from flask import Flask
from werkzeug.datastructures import FileStorage

from src.api.image_upload_routes import image_upload_bp
from src.config.factory import ConfigurationFactory
from src.database.models import ImageUploadRecord
from src.features.image_security import ImageProcessingConfig


@pytest.fixture
def app():
    """Create Flask test application with image upload blueprint."""
    app = Flask(__name__)
    app.config["TESTING"] = True
    app.config["SECRET_KEY"] = "test-secret-key"
    app.config["WTF_CSRF_ENABLED"] = False

    # Register blueprint
    app.register_blueprint(image_upload_bp)

    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def mock_database():
    """Mock database operations."""
    with patch("src.api.image_upload_routes.get_db") as mock_get_db:
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        yield mock_db


@pytest.fixture
def mock_session_manager():
    """Mock session manager."""
    with patch("src.api.image_upload_routes.session_manager") as mock_manager:
        mock_manager.get_or_create_session_id.return_value = "test_session_12345678"
        yield mock_manager


@pytest.fixture
def mock_rate_limiter():
    """Mock rate limiter."""
    with patch("src.api.image_upload_routes.rate_limiter") as mock_limiter:
        mock_limiter.check_rate_limit.return_value = True
        yield mock_limiter


@pytest.fixture
def mock_websocket_manager():
    """Mock WebSocket manager."""
    with patch("src.api.image_upload_routes.websocket_manager") as mock_ws:
        mock_ws.is_healthy.return_value = True
        yield mock_ws


@pytest.fixture
def sample_jpeg_data():
    """Create sample JPEG image data for testing."""
    # Create minimal valid JPEG header
    jpeg_header = (
        b"\\xFF\\xD8\\xFF\\xE0\\x00\\x10JFIF\\x00\\x01\\x01\\x01\\x00H\\x00H\\x00\\x00"
    )
    jpeg_footer = b"\\xFF\\xD9"

    # Simple JPEG content (minimal but valid)
    jpeg_content = jpeg_header + b"\\x00" * 100 + jpeg_footer

    return jpeg_content


@pytest.fixture
def sample_image_file(sample_jpeg_data):
    """Create sample image file for upload testing."""
    return FileStorage(
        stream=io.BytesIO(sample_jpeg_data),
        filename="test_image.jpg",
        content_type="image/jpeg",
    )


class TestImageUploadAPIIntegration:
    """Integration tests for image upload API endpoints."""

    def test_upload_image_success_integration(
        self,
        client,
        sample_image_file,
        mock_database,
        mock_session_manager,
        mock_rate_limiter,
        mock_websocket_manager,
    ):
        """Test complete successful image upload integration."""
        # Mock successful pipeline processing
        with patch("src.api.image_upload_routes.get_image_pipeline") as mock_pipeline:
            mock_result = Mock()
            mock_result.is_safe = True
            mock_result.validation_passed = True
            mock_result.total_processing_time_ms = 150.0
            mock_result.to_api_response.return_value = {
                "success": True,
                "validation_passed": True,
                "processing_time_ms": 150.0,
            }

            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_uploaded_image.return_value = mock_result
            mock_pipeline.return_value = mock_pipeline_instance

            # Mock database record creation
            mock_record = Mock()
            mock_record.id = 123

            with patch(
                "src.api.image_upload_routes.ImageUploadRecord"
            ) as mock_record_class:
                mock_record_class.from_security_result.return_value = mock_record

                # Make request
                response = client.post(
                    "/api/upload/image",
                    data={"image": sample_image_file},
                    content_type="multipart/form-data",
                )

                # Assertions
                assert response.status_code == 200
                response_data = json.loads(response.data)

                assert response_data["success"] is True
                assert (
                    "Image uploaded and validated successfully"
                    in response_data["message"]
                )
                assert "data" in response_data
                assert response_data["data"]["database_stored"] is True

                # Verify pipeline was called
                mock_pipeline_instance.process_uploaded_image.assert_called_once()

                # Verify database operations
                mock_database.add.assert_called_once()
                mock_database.commit.assert_called_once()

                # Verify session management
                mock_session_manager.get_or_create_session_id.assert_called()

                # Verify rate limiting
                mock_rate_limiter.check_rate_limit.assert_called_with(
                    "test_session_12345678", "image_upload"
                )

    def test_upload_image_validation_failure(
        self,
        client,
        sample_image_file,
        mock_session_manager,
        mock_rate_limiter,
        mock_websocket_manager,
    ):
        """Test image upload with validation failure."""
        with patch("src.api.image_upload_routes.get_image_pipeline") as mock_pipeline:
            # Mock validation failure
            mock_result = Mock()
            mock_result.is_safe = False
            mock_result.validation_passed = False
            mock_result.error_message = "Magic number validation failed"
            mock_result.error_code = "MAGIC_NUMBER_INVALID"
            mock_result.to_api_response.return_value = {
                "success": False,
                "validation_passed": False,
                "error_message": "Magic number validation failed",
            }

            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_uploaded_image.return_value = mock_result
            mock_pipeline.return_value = mock_pipeline_instance

            # Make request
            response = client.post(
                "/api/upload/image",
                data={"image": sample_image_file},
                content_type="multipart/form-data",
            )

            # Assertions
            assert response.status_code == 422
            response_data = json.loads(response.data)

            assert response_data["success"] is False
            assert "Image validation failed" in response_data["message"]
            assert response_data["error"] == "Magic number validation failed"

    def test_upload_image_rate_limit_exceeded(
        self, client, sample_image_file, mock_session_manager, mock_websocket_manager
    ):
        """Test image upload with rate limit exceeded."""
        with patch("src.api.image_upload_routes.rate_limiter") as mock_limiter:
            mock_limiter.check_rate_limit.return_value = False

            # Make request
            response = client.post(
                "/api/upload/image",
                data={"image": sample_image_file},
                content_type="multipart/form-data",
            )

            # Assertions
            assert response.status_code == 429
            response_data = json.loads(response.data)

            assert response_data["success"] is False
            assert "Rate limit exceeded" in response_data["message"]

    def test_upload_status_endpoint(self, client, mock_session_manager, mock_database):
        """Test upload status endpoint integration."""
        with patch("src.api.image_upload_routes.get_image_pipeline") as mock_pipeline:
            # Mock pipeline statistics
            mock_stats = {
                "total_processed": 10,
                "successful_processed": 8,
                "avg_processing_time_ms": 145.5,
            }
            mock_health = {
                "pipeline_healthy": True,
                "validator_healthy": True,
                "processor_healthy": True,
            }

            mock_pipeline_instance = Mock()
            mock_pipeline_instance.get_processing_statistics.return_value = mock_stats
            mock_pipeline_instance.health_check.return_value = mock_health
            mock_pipeline.return_value = mock_pipeline_instance

            # Mock database session statistics
            mock_database.query.return_value.filter.return_value.count.return_value = 5

            # Make request
            response = client.get("/api/upload/image/status")

            # Assertions
            assert response.status_code == 200
            response_data = json.loads(response.data)

            assert response_data["success"] is True
            assert "data" in response_data
            assert response_data["data"]["pipeline_statistics"] == mock_stats
            assert response_data["data"]["pipeline_health"] == mock_health

    def test_upload_config_endpoint(self, client):
        """Test upload configuration endpoint."""
        with patch("src.api.image_upload_routes.get_image_pipeline") as mock_pipeline:
            # Mock configuration
            mock_config = Mock()
            mock_config.max_file_size_mb = 10
            mock_config.max_width = 4096
            mock_config.max_height = 4096
            mock_config.auto_resize = True
            mock_config.jpeg_quality = 85

            mock_pipeline_instance = Mock()
            mock_pipeline_instance.config = mock_config
            mock_pipeline.return_value = mock_pipeline_instance

            # Make request
            response = client.get("/api/upload/image/config")

            # Assertions
            assert response.status_code == 200
            response_data = json.loads(response.data)

            assert response_data["success"] is True
            assert "data" in response_data

            config_data = response_data["data"]
            assert config_data["upload_limits"]["max_file_size_mb"] == 10
            assert config_data["upload_limits"]["max_width"] == 4096
            assert "supported_formats" in config_data
            assert "security_features" in config_data

    def test_health_check_endpoint_integration(
        self, client, mock_database, mock_websocket_manager
    ):
        """Test health check endpoint with full integration."""
        with patch("src.api.image_upload_routes.get_image_pipeline") as mock_pipeline:
            # Mock pipeline health
            mock_health = {
                "pipeline_healthy": True,
                "validator_healthy": True,
                "processor_healthy": True,
            }

            mock_pipeline_instance = Mock()
            mock_pipeline_instance.health_check.return_value = mock_health
            mock_pipeline.return_value = mock_pipeline_instance

            # Mock dependency health
            with patch.object(
                ConfigurationFactory, "get_image_dependency_health"
            ) as mock_deps:
                mock_deps.return_value = {"healthy": True, "components": {}}

                # Make request
                response = client.get("/api/upload/health")

                # Assertions
                assert response.status_code == 200
                response_data = json.loads(response.data)

                assert response_data["healthy"] is True
                assert "components" in response_data
                assert response_data["components"]["image_pipeline"] == mock_health
                assert response_data["components"]["database"]["healthy"] is True
                assert response_data["components"]["websocket"]["healthy"] is True
                assert response_data["components"]["dependencies"]["healthy"] is True

    def test_dependency_status_endpoint(self, client):
        """Test dependency status endpoint integration."""
        # Mock dependency validation
        mock_validation = {
            "overall_status": "ok",
            "dependencies": {
                "pillow": {"status": "ok", "version": "10.4.0"},
                "python_magic": {"status": "ok"},
            },
            "security_checks": {"pil_truncated_images": {"status": "ok"}},
        }

        # Mock security configuration
        mock_config = Mock()
        mock_config.environment = "development"
        mock_config.get_security_policy.return_value = {"test": "policy"}
        mock_config.get_pil_security_config.return_value = {"test": "pil_config"}

        with patch.object(
            ConfigurationFactory, "validate_image_dependencies"
        ) as mock_validate:
            with patch.object(
                ConfigurationFactory, "create_environment_image_config"
            ) as mock_config_method:
                mock_validate.return_value = mock_validation
                mock_config_method.return_value = mock_config

                # Make request
                response = client.get("/api/upload/dependencies")

                # Assertions
                assert response.status_code == 200
                response_data = json.loads(response.data)

                assert response_data["success"] is True
                assert "data" in response_data

                data = response_data["data"]
                assert data["dependency_validation"] == mock_validation
                assert data["security_configuration"]["environment"] == "development"
                assert "environment_examples" in data


class TestImageUploadCrossModuleIntegration:
    """Integration tests for cross-module communication."""

    def test_database_integration(self, mock_database):
        """Test F1 database integration with image upload records."""
        from src.features.image_security.models import (
            ImageMetadata,
            ImageSecurityResult,
        )

        # Create test security result
        metadata = ImageMetadata(
            filename="test.jpg",
            file_size=1024,
            mime_type="image/jpeg",
            width=800,
            height=600,
            magic_number_valid=True,
            pil_validation_passed=True,
            security_scan_passed=True,
            processing_time_ms=100.0,
        )

        result = ImageSecurityResult(
            is_safe=True,
            validation_passed=True,
            metadata=metadata,
            validation_results={"test": True},
            security_warnings=[],
            total_processing_time_ms=120.0,
        )

        # Test database record creation
        record = ImageUploadRecord.from_security_result(
            session_id="test_session", result=result, target_provider="google_veo3"
        )

        # Assertions
        assert record.session_id == "test_session"
        assert record.filename == "test.jpg"
        assert record.file_size == 1024
        assert record.mime_type == "image/jpeg"
        assert record.validation_passed is True
        assert record.is_safe is True
        assert record.target_provider == "google_veo3"

    def test_configuration_factory_integration(self):
        """Test configuration factory integration with image security."""
        # Test environment configuration creation
        config = ConfigurationFactory.create_environment_image_config("development")

        assert config.environment == "development"
        assert config.development_mode is True
        assert config.enable_magic_number_check is True
        assert config.enable_pil_validation is True

        # Test production configuration
        prod_config = ConfigurationFactory.create_environment_image_config("production")

        assert prod_config.environment == "production"
        assert prod_config.development_mode is False
        assert prod_config.enable_malware_scan is True
        assert prod_config.max_file_size_mb <= 10

    def test_pipeline_configuration_integration(self):
        """Test image processing pipeline configuration integration."""
        # Test configuration factory integration
        with patch.object(
            ConfigurationFactory, "create_environment_image_config"
        ) as mock_factory:
            mock_security_config = Mock()
            mock_security_config.max_file_size_mb = 15
            mock_security_config.max_width = 2048
            mock_security_config.enable_magic_number_check = True
            mock_security_config.enable_pil_validation = True
            mock_security_config.jpeg_quality = 90
            mock_security_config.default_provider = "google_veo3"

            mock_factory.return_value = mock_security_config

            # Create config using enhanced factory
            config = ImageProcessingConfig.from_environment()

            # Verify integration
            assert config.max_file_size_mb == 15
            assert config.max_width == 2048
            assert config.enable_magic_number_check is True
            assert config.jpeg_quality == 90
            assert config.default_provider == "google_veo3"

    @patch("src.realtime.websocket_manager.websocket_manager")
    def test_websocket_integration(self, mock_ws_manager):
        """Test WebSocket integration for real-time notifications."""
        from src.api.image_upload_routes import _send_websocket_notification

        # Test WebSocket notification
        test_data = {
            "type": "image_upload_success",
            "message": "Upload completed successfully",
        }

        # Call the function (it runs in a separate thread)
        _send_websocket_notification("test_session", test_data)

        # Give time for the thread to execute
        import time

        time.sleep(0.1)

        # Note: Due to threading, this test verifies the function doesn't crash
        # In a real integration test, you'd verify the WebSocket message was sent

    def test_provider_interface_integration(self):
        """Test F2 provider interface integration."""
        from src.features.image_security.models import ImageSecurityResult

        # Create result with provider compatibility
        result = ImageSecurityResult(
            is_safe=True,
            validation_passed=True,
            provider_ready=True,
            provider_format="base64",
            total_processing_time_ms=100.0,
        )

        # Test provider interface compatibility
        api_response = result.to_api_response()

        assert api_response["provider_ready"] is True
        assert api_response["provider_format"] == "base64"
        assert api_response["success"] is True

        # Test database record for provider integration
        db_record = result.to_database_record("test_session")

        assert db_record["provider_ready"] is True
        assert db_record["session_id"] == "test_session"


class TestImageUploadEndToEndSecurity:
    """End-to-end security validation tests."""

    def test_malicious_file_upload_protection(
        self, client, mock_session_manager, mock_rate_limiter
    ):
        """Test protection against malicious file uploads."""
        # Create malicious file (fake JPEG header)
        malicious_data = (
            b"\\xFF\\xD8\\xFF\\xE0" + b'<script>alert(\\"xss\\")</script>' * 100
        )

        malicious_file = FileStorage(
            stream=io.BytesIO(malicious_data),
            filename="malicious.jpg",
            content_type="image/jpeg",
        )

        with patch("src.api.image_upload_routes.get_image_pipeline") as mock_pipeline:
            # Mock validation failure for malicious content
            mock_result = Mock()
            mock_result.is_safe = False
            mock_result.validation_passed = False
            mock_result.error_message = "Security validation failed"
            mock_result.error_code = "SECURITY_VIOLATION"

            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_uploaded_image.return_value = mock_result
            mock_pipeline.return_value = mock_pipeline_instance

            # Make request
            response = client.post(
                "/api/upload/image",
                data={"image": malicious_file},
                content_type="multipart/form-data",
            )

            # Assertions
            assert response.status_code == 422
            response_data = json.loads(response.data)
            assert response_data["success"] is False
            assert "Security validation failed" in response_data["error"]

    def test_session_isolation_security(self, client, mock_rate_limiter):
        """Test session isolation prevents cross-session data exposure."""
        session_1 = "session_1_12345678"
        session_2 = "session_2_87654321"

        with patch("src.api.image_upload_routes.session_manager") as mock_manager:
            # Test session 1
            mock_manager.get_or_create_session_id.return_value = session_1

            response1 = client.get("/api/upload/image/status")
            assert response1.status_code == 200

            data1 = json.loads(response1.data)

            # Test session 2
            mock_manager.get_or_create_session_id.return_value = session_2

            response2 = client.get("/api/upload/image/status")
            assert response2.status_code == 200

            data2 = json.loads(response2.data)

            # Verify sessions are isolated
            assert data1["data"]["session_id"] != data2["data"]["session_id"]

    def test_input_validation_security(
        self, client, mock_session_manager, mock_rate_limiter
    ):
        """Test comprehensive input validation security."""
        # Test missing image file
        response = client.post("/api/upload/image", data={})
        assert response.status_code == 400

        # Test empty filename
        empty_file = FileStorage(
            stream=io.BytesIO(b"test"), filename="", content_type="image/jpeg"
        )

        response = client.post(
            "/api/upload/image",
            data={"image": empty_file},
            content_type="multipart/form-data",
        )
        assert response.status_code == 400

        # Test invalid content type
        invalid_file = FileStorage(
            stream=io.BytesIO(b"test"),
            filename="test.exe",
            content_type="application/octet-stream",
        )

        response = client.post(
            "/api/upload/image",
            data={"image": invalid_file},
            content_type="multipart/form-data",
        )
        assert response.status_code == 400

    def test_dependency_security_validation(self):
        """Test dependency security validation."""
        # Test dependency health check
        health = ConfigurationFactory.get_image_dependency_health()

        assert "healthy" in health
        assert "components" in health

        # Test dependency validation
        validation = ConfigurationFactory.validate_image_dependencies()

        assert "overall_status" in validation
        assert "dependencies" in validation
        assert "security_checks" in validation

        # Verify security checks include PIL settings
        security_checks = validation.get("security_checks", {})
        assert "pil_truncated_images" in security_checks or "error" in validation
