"""
C1 Image Security - Magic Number Validation Tests.

Comprehensive testing of magic number verification for image security validation.
Tests cover valid formats, format spoofing, buffer attacks, and edge cases.

Test Categories:
- Valid magic number verification (5 tests)
- Invalid/spoofed magic numbers (5 tests)
- Buffer overflow and edge cases (5 tests)
"""

from typing import Any, Dict

import pytest

from src.features.image_security.validators import ImageValidator


class TestMagicNumberValidation:
    """Test suite for magic number validation security."""

    def test_valid_jpeg_magic_number(
        self, security_validator: ImageValidator, valid_jpeg_data: bytes
    ):
        """Test valid JPEG magic number detection."""
        # Test: Valid JPEG header should pass magic number validation
        result = security_validator._validate_magic_number(valid_jpeg_data)

        assert result is True, "Valid JPEG magic number should be detected"

        # Verify JPEG signature detection
        jpeg_header = valid_jpeg_data[:16]
        assert jpeg_header.startswith(b"\xff\xd8\xff"), (
            "JPEG header signature should be present"
        )

    def test_valid_png_magic_number(
        self, security_validator: ImageValidator, valid_png_data: bytes
    ):
        """Test valid PNG magic number detection."""
        # Test: Valid PNG header should pass magic number validation
        result = security_validator._validate_magic_number(valid_png_data)

        assert result is True, "Valid PNG magic number should be detected"

        # Verify PNG signature detection
        png_header = valid_png_data[:16]
        assert png_header.startswith(b"\x89PNG\r\n\x1a\n"), (
            "PNG header signature should be present"
        )

    def test_valid_webp_magic_number(
        self, security_validator: ImageValidator, valid_webp_data: bytes
    ):
        """Test valid WEBP magic number detection."""
        # Test: Valid WEBP header should pass magic number validation
        result = security_validator._validate_magic_number(valid_webp_data)

        assert result is True, "Valid WEBP magic number should be detected"

        # Verify WEBP signature detection (WEBP is at offset 8 in RIFF container)
        assert b"WEBP" in valid_webp_data[:16], (
            "WEBP signature should be present in header"
        )

    def test_valid_gif_magic_number(
        self, security_validator: ImageValidator, valid_gif_data: bytes
    ):
        """Test valid GIF magic number detection."""
        # Test: Valid GIF header should pass magic number validation
        result = security_validator._validate_magic_number(valid_gif_data)

        assert result is True, "Valid GIF magic number should be detected"

        # Verify GIF signature detection
        gif_header = valid_gif_data[:16]
        assert gif_header.startswith(b"GIF87a") or gif_header.startswith(b"GIF89a"), (
            "GIF header signature should be present"
        )

    def test_multiple_format_magic_number_detection(
        self,
        security_validator: ImageValidator,
        valid_jpeg_data: bytes,
        valid_png_data: bytes,
        valid_gif_data: bytes,
    ):
        """Test magic number detection across multiple formats."""
        # Test: All valid formats should be properly detected
        formats_data = {
            "JPEG": valid_jpeg_data,
            "PNG": valid_png_data,
            "GIF": valid_gif_data,
        }

        for format_name, data in formats_data.items():
            result = security_validator._validate_magic_number(data)
            assert result is True, (
                f"Valid {format_name} magic number should be detected"
            )

    def test_invalid_magic_number_random_bytes(
        self, security_validator: ImageValidator
    ):
        """Test invalid magic number with random bytes."""
        # Test: Random bytes should fail magic number validation
        random_data = b"\x12\x34\x56\x78\x9a\xbc\xde\xf0" * 10

        result = security_validator._validate_magic_number(random_data)
        assert result is False, "Random bytes should fail magic number validation"

    def test_spoofed_jpeg_magic_number(self, security_validator: ImageValidator):
        """Test spoofed JPEG magic number with invalid content."""
        # Test: JPEG header with non-JPEG content should fail validation
        spoofed_data = b"\xff\xd8\xff\xe0" + b"This is not JPEG data" * 100

        result = security_validator._validate_magic_number(spoofed_data)
        # Magic number validation only checks header, so this should pass
        # (Further validation steps will catch the invalid content)
        assert result is True, "Magic number validation only checks header bytes"

    def test_partial_magic_number(self, security_validator: ImageValidator):
        """Test partial/truncated magic number."""
        # Test: Partial magic number should fail validation
        partial_jpeg = b"\xff\xd8"  # Incomplete JPEG header

        result = security_validator._validate_magic_number(partial_jpeg)
        assert result is False, "Partial magic number should fail validation"

    def test_corrupted_magic_number(self, security_validator: ImageValidator):
        """Test corrupted magic number."""
        # Test: Corrupted magic number should fail validation
        corrupted_jpeg = b"\xff\xd7\xff\xe0"  # Corrupted JPEG header

        result = security_validator._validate_magic_number(corrupted_jpeg)
        assert result is False, "Corrupted magic number should fail validation"

    def test_format_confusion_attack(
        self,
        security_validator: ImageValidator,
        attack_simulation_data: Dict[str, bytes],
    ):
        """Test format confusion attack with polyglot file."""
        # Test: Polyglot file should be handled securely
        polyglot_data = attack_simulation_data["polyglot"]

        result = security_validator._validate_magic_number(polyglot_data)
        # Should detect first valid magic number (JPEG in this case)
        assert result is True, "Should detect first valid magic number in polyglot file"

    def test_empty_data_magic_number(self, security_validator: ImageValidator):
        """Test magic number validation with empty data."""
        # Test: Empty data should fail validation gracefully
        result = security_validator._validate_magic_number(b"")
        assert result is False, "Empty data should fail magic number validation"

    def test_single_byte_data(self, security_validator: ImageValidator):
        """Test magic number validation with single byte."""
        # Test: Single byte should fail validation
        result = security_validator._validate_magic_number(b"\xff")
        assert result is False, "Single byte should fail magic number validation"

    def test_null_byte_header(self, security_validator: ImageValidator):
        """Test magic number validation with null bytes."""
        # Test: Null byte header should fail validation
        null_header = b"\x00\x00\x00\x00\x00\x00\x00\x00"

        result = security_validator._validate_magic_number(null_header)
        assert result is False, "Null byte header should fail magic number validation"

    def test_buffer_overflow_protection(
        self, security_validator: ImageValidator, security_test_vectors: Dict[str, Any]
    ):
        """Test buffer overflow protection in magic number validation."""
        # Test: Large buffers should be handled safely
        for overflow_data in security_test_vectors["buffer_overflow"]:
            try:
                result = security_validator._validate_magic_number(overflow_data)
                # Should handle large buffers without crashing
                assert isinstance(result, bool), (
                    "Should return boolean result for large buffers"
                )
            except Exception as e:
                pytest.fail(
                    f"Magic number validation should handle large buffers safely: {e}"
                )

    def test_format_exploit_protection(
        self, security_validator: ImageValidator, security_test_vectors: Dict[str, Any]
    ):
        """Test protection against format-based exploits."""
        # Test: Format exploit attempts should be handled safely
        for exploit_data in security_test_vectors["format_exploits"]:
            try:
                result = security_validator._validate_magic_number(exploit_data)
                assert isinstance(result, bool), (
                    "Should return boolean result for exploit attempts"
                )
            except Exception as e:
                pytest.fail(
                    f"Magic number validation should handle exploits safely: {e}"
                )


class TestMagicNumberPerformance:
    """Performance tests for magic number validation."""

    def test_magic_number_validation_performance(
        self,
        security_validator: ImageValidator,
        performance_test_images: Dict[str, bytes],
        performance_benchmark_config: Dict[str, Any],
    ):
        """Test magic number validation performance."""
        import time

        max_time_ms = 10  # Magic number validation should be very fast

        for image_name, image_data in performance_test_images.items():
            start_time = time.time()
            result = security_validator._validate_magic_number(image_data)
            end_time = time.time()

            processing_time_ms = (end_time - start_time) * 1000

            assert processing_time_ms < max_time_ms, (
                f"Magic number validation for {image_name} took {processing_time_ms:.2f}ms, "
                f"should be < {max_time_ms}ms"
            )

            assert isinstance(result, bool), (
                f"Should return boolean result for {image_name}"
            )

    def test_magic_number_validation_memory_efficiency(
        self, security_validator: ImageValidator, memory_intensive_image: bytes
    ):
        """Test memory efficiency of magic number validation."""
        import tracemalloc

        # Start memory tracking
        tracemalloc.start()

        # Run magic number validation multiple times
        for _ in range(100):
            security_validator._validate_magic_number(memory_intensive_image)

        # Get memory usage
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        # Memory usage should be minimal (under 1MB)
        peak_mb = peak / 1024 / 1024
        assert peak_mb < 1.0, (
            f"Magic number validation used {peak_mb:.2f}MB, should be < 1MB"
        )


class TestMagicNumberEdgeCases:
    """Edge case tests for magic number validation."""

    def test_case_sensitivity_magic_number(self, security_validator: ImageValidator):
        """Test case sensitivity in magic number validation."""
        # Test: Magic numbers are binary, case shouldn't matter for binary data
        jpeg_header_upper = b"\xff\xd8\xff\xe0"
        jpeg_header_modified = b"\xff\xd8\xff\xe0"  # Lowercase hex (same binary)

        result1 = security_validator._validate_magic_number(
            jpeg_header_upper + b"test_data"
        )
        result2 = security_validator._validate_magic_number(
            jpeg_header_modified + b"test_data"
        )

        assert result1 == result2, "Binary magic numbers should not be case sensitive"

    def test_padding_after_magic_number(self, security_validator: ImageValidator):
        """Test various padding after magic number."""
        # Test: Different padding should not affect magic number detection
        jpeg_base = b"\xff\xd8\xff\xe0"

        padding_variants = [
            b"\x00" * 1000,  # Null padding
            b"\xff" * 1000,  # 0xFF padding
            b"A" * 1000,  # ASCII padding
            b"\x01\x02\x03\x04" * 250,  # Pattern padding
        ]

        for padding in padding_variants:
            data_with_padding = jpeg_base + padding
            result = security_validator._validate_magic_number(data_with_padding)
            assert result is True, (
                "Magic number should be detected regardless of padding"
            )

    def test_unicode_in_magic_number_context(self, security_validator: ImageValidator):
        """Test handling of unicode characters in magic number context."""
        # Test: Unicode should not interfere with binary magic number detection
        unicode_data = "🖼️📷🎨".encode()  # Image-related emojis

        # Valid JPEG header followed by unicode
        jpeg_with_unicode = b"\xff\xd8\xff\xe0" + unicode_data

        result = security_validator._validate_magic_number(jpeg_with_unicode)
        assert result is True, (
            "Unicode data should not interfere with magic number detection"
        )
