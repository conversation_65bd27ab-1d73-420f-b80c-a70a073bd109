"""
Pytest configuration for C1 Image Upload Security Pipeline integration tests.

Provides shared fixtures and test configuration for comprehensive
integration testing across all pipeline components.
"""

import io
import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from PIL import Image, ImageDraw

# Add src to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from src.features.image_security.models import (
    ImageProcessingConfig,
)
from src.features.image_security.pipeline import ImageSecurityPipeline
from src.features.image_security.processors import ImageProcessor
from src.features.image_security.validators import ImageValidator


@pytest.fixture(scope="session")
def test_environment():
    """Set up test environment variables."""
    test_env = {
        "ENVIRONMENT": "testing",
        "FLASK_ENV": "testing",
        "TESTING": "true",
        "SORA_IMAGE_ENVIRONMENT": "development",
        "SORA_IMAGE_MAX_FILE_SIZE_MB": "5",
        "SORA_IMAGE_ENABLE_MALWARE_SCAN": "false",
        "SORA_IMAGE_PROCESSING_TIMEOUT_SECONDS": "10",
        "SORA_IMAGE_MAX_CONCURRENT_UPLOADS": "5",
    }

    # Set environment variables
    for key, value in test_env.items():
        os.environ[key] = value

    yield test_env

    # Cleanup
    for key in test_env.keys():
        os.environ.pop(key, None)


@pytest.fixture
def mock_pil_imports():
    """Mock PIL imports for testing without requiring actual image processing."""
    with patch("PIL.Image") as mock_image:
        with patch("PIL.ImageOps") as mock_ops:
            with patch("PIL.ImageFile") as mock_file:
                # Configure mock Image
                mock_img = Mock()
                mock_img.width = 800
                mock_img.height = 600
                mock_img.mode = "RGB"
                mock_img.size = (800, 600)
                mock_img.format = "JPEG"

                # Mock image operations
                mock_img.resize.return_value = mock_img
                mock_img.convert.return_value = mock_img
                mock_img.save = Mock()
                mock_img.verify = Mock()
                mock_img.getexif.return_value = {}

                # Configure context manager
                mock_context = Mock()
                mock_context.__enter__.return_value = mock_img
                mock_context.__exit__.return_value = None

                mock_image.open.return_value = mock_context
                mock_image.new.return_value = mock_img
                mock_image.MAX_IMAGE_PIXELS = 178956970

                # Configure ImageOps
                mock_ops.exif_transpose.return_value = mock_img

                # Configure ImageFile
                mock_file.LOAD_TRUNCATED_IMAGES = False

                yield {
                    "Image": mock_image,
                    "ImageOps": mock_ops,
                    "ImageFile": mock_file,
                    "mock_img": mock_img,
                }


@pytest.fixture
def mock_magic_library():
    """Mock python-magic library for testing."""
    with patch("magic.Magic") as mock_magic_class:
        mock_magic = Mock()
        mock_magic.from_buffer.return_value = "image/jpeg"
        mock_magic_class.return_value = mock_magic

        yield mock_magic


@pytest.fixture
def sample_test_images():
    """Provide sample test image data for different formats."""
    return {
        "jpeg": {
            "data": b"\\xFF\\xD8\\xFF\\xE0\\x00\\x10JFIF\\x00\\x01\\x01\\x01\\x00H\\x00H\\x00\\x00\\xFF\\xD9",
            "filename": "test.jpg",
            "mime_type": "image/jpeg",
        },
        "png": {
            "data": b"\\x89PNG\\r\\n\\x1a\\n\\x00\\x00\\x00\\rIHDR\\x00\\x00\\x00\\x64\\x00\\x00\\x00\\x64\\x08\\x02\\x00\\x00\\x00\\xff\\x80\\x02\\x03\\x00\\x00\\x00\\x00IEND\\xaeB`\\x82",
            "filename": "test.png",
            "mime_type": "image/png",
        },
        "malicious": {
            "data": b'\\xFF\\xD8\\xFF\\xE0<script>alert("xss")</script>'
            + b"\\x00" * 1000
            + b"\\xFF\\xD9",
            "filename": "malicious.jpg",
            "mime_type": "image/jpeg",
        },
    }


@pytest.fixture
def test_session_id():
    """Provide consistent test session ID."""
    return "test_session_12345678"


@pytest.fixture
def mock_database_session():
    """Mock database session for testing."""
    with patch("src.database.connection.get_db") as mock_get_db:
        mock_db = Mock()
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.query.return_value.filter.return_value.count.return_value = 0
        mock_get_db.return_value = mock_db
        yield mock_db


@pytest.fixture
def mock_session_rate_websocket():
    """Mock session manager, rate limiter, and WebSocket manager."""
    mocks = {}

    with patch("src.session.session_manager.session_manager") as mock_session:
        mock_session.get_or_create_session_id.return_value = "test_session_12345678"
        mocks["session"] = mock_session

        with patch("src.rate_limiting.rate_limiter.rate_limiter") as mock_rate:
            mock_rate.check_rate_limit.return_value = True
            mocks["rate_limiter"] = mock_rate

            with patch("src.realtime.websocket_manager.websocket_manager") as mock_ws:
                mock_ws.is_healthy.return_value = True
                mocks["websocket"] = mock_ws

                yield mocks


@pytest.fixture
def clean_image_processing_config():
    """Provide clean image processing configuration for testing."""
    from src.features.image_security.models import ImageProcessingConfig

    return ImageProcessingConfig(
        max_file_size_mb=5,
        max_width=1024,
        max_height=1024,
        enable_magic_number_check=True,
        enable_pil_validation=True,
        enable_malware_scan=False,
        strict_mime_type_check=True,
        auto_resize=True,
        jpeg_quality=85,
        png_optimize=True,
        processing_timeout_seconds=10,
        max_concurrent_uploads=5,
        default_provider="google_veo3",
        provider_timeout_seconds=30,
    )


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state before each test."""
    # Reset any module-level variables
    yield
    # Cleanup after test


@pytest.fixture
def security_test_config():
    """Create security-focused test configuration."""
    return ImageProcessingConfig(
        max_file_size_mb=10,
        max_width=4096,
        max_height=4096,
        enable_magic_number_check=True,
        enable_pil_validation=True,
        enable_malware_scan=True,
        strict_mime_type_check=True,
        auto_resize=True,
        jpeg_quality=85,
        processing_timeout_seconds=10,
        max_concurrent_uploads=5,
        default_provider="google_veo3",
        provider_timeout_seconds=30,
    )


@pytest.fixture
def security_pipeline(security_test_config):
    """Create security pipeline with strict configuration."""
    return ImageSecurityPipeline(security_test_config)


@pytest.fixture
def security_validator(security_test_config):
    """Create security validator with strict configuration."""
    return ImageValidator(security_test_config)


@pytest.fixture
def image_processor(security_test_config):
    """Create image processor with security configuration."""
    return ImageProcessor(security_test_config)


@pytest.fixture
def temp_upload_dir():
    """Create temporary directory for upload testing."""
    temp_dir = tempfile.mkdtemp(prefix="image_security_test_")
    yield temp_dir

    # Cleanup
    import shutil

    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def valid_jpeg_data():
    """Create valid JPEG image data for testing."""
    img = Image.new("RGB", (640, 480), color="red")
    buffer = io.BytesIO()
    img.save(buffer, format="JPEG", quality=85)
    return buffer.getvalue()


@pytest.fixture
def valid_png_data():
    """Create valid PNG image data for testing."""
    img = Image.new("RGBA", (640, 480), color=(0, 255, 0, 128))
    buffer = io.BytesIO()
    img.save(buffer, format="PNG", optimize=True)
    return buffer.getvalue()


@pytest.fixture
def valid_webp_data():
    """Create valid WEBP image data for testing."""
    img = Image.new("RGB", (320, 240), color="blue")
    buffer = io.BytesIO()
    img.save(buffer, format="WEBP", quality=80)
    return buffer.getvalue()


@pytest.fixture
def valid_gif_data():
    """Create valid GIF image data for testing."""
    img = Image.new("P", (200, 200), color=1)
    buffer = io.BytesIO()
    img.save(buffer, format="GIF")
    return buffer.getvalue()


@pytest.fixture
def oversized_image_data():
    """Create oversized image data that should fail validation."""
    img = Image.new("RGB", (5000, 5000), color="white")
    buffer = io.BytesIO()
    img.save(buffer, format="JPEG", quality=50)
    return buffer.getvalue()


@pytest.fixture
def corrupted_jpeg_data():
    """Create corrupted JPEG data for negative testing."""
    # Start with valid JPEG header, then corrupt the data
    valid_header = b"\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00"
    corrupted_body = b"\x00\x01\x02\x03" * 100  # Invalid JPEG data
    return valid_header + corrupted_body


@pytest.fixture
def fake_image_data():
    """Create data that looks like an image but isn't."""
    # PNG header with invalid data
    png_header = b"\x89PNG\r\n\x1a\n"
    fake_data = b"This is not actually image data" * 50
    return png_header + fake_data


@pytest.fixture
def malicious_steganography_data(valid_jpeg_data):
    """Create image data with steganography signatures."""
    # Append steganography signature to end
    return valid_jpeg_data + b"steghide" + b"\x00" * 100


@pytest.fixture
def malicious_script_injection_data(valid_png_data):
    """Create image data with script injection attempt."""
    # Inject script content
    script_injection = b'<script>alert("xss")</script>'
    return valid_png_data + script_injection


@pytest.fixture
def directory_traversal_filename():
    """Create filename with directory traversal attempt."""
    return "../../../etc/passwd.jpg"


@pytest.fixture
def performance_test_images():
    """Create various images for performance testing."""
    images = {}

    # Small image for baseline
    small_img = Image.new("RGB", (100, 100), color="red")
    small_buffer = io.BytesIO()
    small_img.save(small_buffer, format="JPEG", quality=85)
    images["small"] = small_buffer.getvalue()

    # Medium image
    medium_img = Image.new("RGB", (1280, 720), color="green")
    medium_buffer = io.BytesIO()
    medium_img.save(medium_buffer, format="JPEG", quality=85)
    images["medium"] = medium_buffer.getvalue()

    # Large image
    large_img = Image.new("RGB", (1920, 1080), color="blue")
    large_buffer = io.BytesIO()
    large_img.save(large_buffer, format="JPEG", quality=85)
    images["large"] = large_buffer.getvalue()

    # High quality image
    hq_img = Image.new("RGB", (1920, 1080), color="purple")
    hq_buffer = io.BytesIO()
    hq_img.save(hq_buffer, format="PNG", optimize=True)
    images["high_quality"] = hq_buffer.getvalue()

    return images


@pytest.fixture
def memory_intensive_image():
    """Create memory-intensive image for resource testing."""
    # Create complex image with many details
    img = Image.new("RGB", (2048, 2048), color="white")
    draw = ImageDraw.Draw(img)

    # Add many complex shapes to increase processing complexity
    for i in range(0, 2048, 50):
        for j in range(0, 2048, 50):
            draw.rectangle(
                [i, j, i + 25, j + 25], fill=(i % 255, j % 255, (i + j) % 255)
            )

    buffer = io.BytesIO()
    img.save(buffer, format="PNG", optimize=False)  # No optimization for max size
    return buffer.getvalue()


@pytest.fixture
def concurrent_test_session_ids():
    """Generate multiple session IDs for concurrent testing."""
    return [f"test_session_{i:03d}" for i in range(15)]


@pytest.fixture
def performance_benchmark_config():
    """Configuration for performance benchmarking."""
    return {
        "max_processing_time_ms": 2000,  # 2 second limit
        "max_memory_growth_mb": 100,  # 100MB memory growth limit
        "concurrent_user_count": 15,  # Test with 15 concurrent users
        "performance_test_iterations": 10,  # Run each test 10 times
        "memory_sampling_interval_ms": 100,  # Sample memory every 100ms
    }


@pytest.fixture
def security_test_vectors():
    """Comprehensive security test vectors for validation."""
    return {
        "buffer_overflow": [
            b"A" * 10000,  # Large buffer
            b"\xff" * 10000,  # Large binary buffer
            b"\x00" * 10000,  # Null byte buffer
        ],
        "format_exploits": [
            b"\xff\xd8\xff\xe0" + b"A" * 1000000,  # Massive fake JPEG
            b"\x89PNG\r\n\x1a\n" + b"B" * 1000000,  # Massive fake PNG
            b"GIF89a" + b"C" * 1000000,  # Massive fake GIF
        ],
        "compression_bombs": [
            # ZIP bomb patterns (adapted for images)
            b"\x50\x4b\x03\x04" + b"\x00" * 100,  # ZIP header in image
        ],
        "metadata_exploits": [
            "filename_with_\x00_null_byte.jpg",
            "filename_with_unicode_\u202e_override.jpg",
            "filename_with_very_long_name_" + "A" * 500 + ".jpg",
        ],
    }


@pytest.fixture
def attack_simulation_data():
    """Simulate various attack vectors for security testing."""
    attacks = {}

    # 1. Polyglot file (appears as multiple formats)
    polyglot_data = (
        b"\xff\xd8\xff\xe0"  # JPEG header
        b"\x89PNG\r\n\x1a\n"  # PNG header
        b"GIF89a"  # GIF header
        b'<html><script>alert("xss")</script></html>'  # HTML content
        b"\x00" * 1000  # Padding
    )
    attacks["polyglot"] = polyglot_data

    # 2. Format confusion attack
    confusion_data = b"\x89PNG\r\n\x1a\n" + b"\xff\xd8\xff\xe0" * 100
    attacks["format_confusion"] = confusion_data

    # 3. Pixel flood attack (extreme dimensions)
    pixel_flood = b"\xff\xd8\xff\xe0\x00\x10JFIF" + b"\xff\xff" * 1000
    attacks["pixel_flood"] = pixel_flood

    return attacks


@pytest.fixture
def owasp_security_test_cases():
    """OWASP Top 10 security test cases adapted for image processing."""
    return {
        "injection": [
            'test"; DROP TABLE users; --.jpg',
            "test'; SELECT * FROM sensitive_data; --.png",
            "test`; rm -rf /; --.gif",
        ],
        "broken_auth": [
            "admin_password_file.jpg",
            "jwt_token_secret.png",
            "session_hijack_attempt.gif",
        ],
        "sensitive_data_exposure": [
            "social_security_numbers.jpg",
            "credit_card_data.png",
            "private_keys.gif",
        ],
        "xxe": [
            '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>',
        ],
        "broken_access_control": [
            "../../../admin/config.jpg",
            "..\\..\\..\\system32\\config.png",
            "/etc/shadow.gif",
        ],
        "security_misconfiguration": [
            "debug_info_exposed.jpg",
            "stack_trace_visible.png",
            "error_details_leaked.gif",
        ],
        "cross_site_scripting": [
            '<script>alert("xss")</script>.jpg',
            'javascript:alert("xss").png',
            "data:text/html;base64,PHNjcmlwdD5hbGVydCgieHNzIik8L3NjcmlwdD4=.gif",
        ],
        "deserialization": [
            "serialized_object_exploit.jpg",
            "pickle_injection_attempt.png",
            "yaml_deserialization_attack.gif",
        ],
        "vulnerable_components": [
            "known_cve_exploit.jpg",
            "outdated_library_attack.png",
            "dependency_confusion.gif",
        ],
        "logging_monitoring": [
            "log_injection_attempt.jpg",
            "monitoring_bypass.png",
            "audit_trail_corruption.gif",
        ],
    }


@pytest.fixture
def performance_stress_test_data():
    """Data for performance stress testing."""
    stress_data = {}

    # CPU intensive processing
    cpu_stress_img = Image.new("RGB", (1920, 1080), color="black")
    draw = ImageDraw.Draw(cpu_stress_img)

    # Create complex pattern that requires intensive processing
    for x in range(0, 1920, 10):
        for y in range(0, 1080, 10):
            draw.point((x, y), fill=(x % 256, y % 256, (x + y) % 256))

    buffer = io.BytesIO()
    cpu_stress_img.save(buffer, format="PNG")
    stress_data["cpu_intensive"] = buffer.getvalue()

    # Memory intensive data
    memory_stress_img = Image.new("RGBA", (2048, 2048), color=(255, 0, 0, 128))
    buffer = io.BytesIO()
    memory_stress_img.save(buffer, format="PNG", optimize=False)
    stress_data["memory_intensive"] = buffer.getvalue()

    return stress_data


class SecurityTestHelpers:
    """Helper methods for security testing."""

    @staticmethod
    def create_malicious_filename(attack_type: str) -> str:
        """Create malicious filename for testing."""
        patterns = {
            "directory_traversal": "../../../etc/passwd.jpg",
            "null_byte": "innocent\x00malicious.jpg",
            "unicode_override": "innocent\u202emalicious.jpg",
            "long_filename": "A" * 300 + ".jpg",
            "script_injection": '<script>alert("xss")</script>.jpg',
            "sql_injection": 'test"; DROP TABLE images; --.jpg',
        }
        return patterns.get(attack_type, "test.jpg")

    @staticmethod
    def inject_malicious_data(
        image_data: bytes, payload: bytes, position: str = "end"
    ) -> bytes:
        """Inject malicious data into image."""
        if position == "start":
            return payload + image_data
        elif position == "middle":
            mid = len(image_data) // 2
            return image_data[:mid] + payload + image_data[mid:]
        else:  # end
            return image_data + payload

    @staticmethod
    def verify_security_isolation(result1, result2) -> bool:
        """Verify that security processing maintains isolation."""
        # Check that different sessions don't interfere
        return (
            result1.metadata.provider_metadata != result2.metadata.provider_metadata
            and result1.secure_path != result2.secure_path
        )


@pytest.fixture
def security_helpers():
    """Provide security test helper methods."""
    return SecurityTestHelpers()


@pytest.fixture
def malicious_exif_data():
    """Create JPEG with malicious EXIF data."""

    img = Image.new("RGB", (640, 480), color="yellow")

    # Create malicious EXIF data with very long fields that should trigger validation
    # We'll use a simple approach since PIL is restrictive about EXIF format

    # Create image with GPS-like data in filename to trigger GPS detection
    # Since PIL EXIF handling is complex, we'll create an image that should fail validation
    buffer = io.BytesIO()

    # Save with minimal EXIF data that might pass, but we'll rely on our validator
    # to catch issues through other means
    img.save(buffer, format="JPEG", quality=85)

    # For our test, we'll modify the raw bytes to include suspicious EXIF-like data
    image_bytes = buffer.getvalue()

    # Insert suspicious GPS-like EXIF data into the image
    # This is a simplified approach for testing
    malicious_exif_marker = b"\xff\xe1\x00\x20GPS" + b"A" * 2000  # Large GPS-like data

    # Insert after JPEG header
    jpeg_header = image_bytes[:4]
    rest_of_image = image_bytes[4:]

    return jpeg_header + malicious_exif_marker + rest_of_image


@pytest.fixture
def integration_test_markers():
    """Provide test markers for different integration test types."""
    return {
        "api_integration": pytest.mark.api_integration,
        "pipeline_integration": pytest.mark.pipeline_integration,
        "security_validation": pytest.mark.security_validation,
        "cross_module": pytest.mark.cross_module,
        "end_to_end": pytest.mark.end_to_end,
    }


# Configure pytest markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line(
        "markers", "api_integration: mark test as API integration test"
    )
    config.addinivalue_line(
        "markers", "pipeline_integration: mark test as pipeline integration test"
    )
    config.addinivalue_line(
        "markers", "security_validation: mark test as security validation test"
    )
    config.addinivalue_line(
        "markers", "cross_module: mark test as cross-module integration test"
    )
    config.addinivalue_line(
        "markers", "end_to_end: mark test as end-to-end integration test"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Add markers based on test file names
        if "test_integration_api" in item.nodeid:
            item.add_marker(pytest.mark.api_integration)

        if "test_integration_pipeline" in item.nodeid:
            item.add_marker(pytest.mark.pipeline_integration)

        if "security" in item.name.lower():
            item.add_marker(pytest.mark.security_validation)

        if "cross_module" in item.name.lower():
            item.add_marker(pytest.mark.cross_module)

        if "end_to_end" in item.name.lower():
            item.add_marker(pytest.mark.end_to_end)
