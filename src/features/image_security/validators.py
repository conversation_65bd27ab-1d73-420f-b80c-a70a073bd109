"""
C1 Image Security Validators.

Comprehensive image validation with magic number verification, PIL validation,
and security scanning following development standards patterns.
"""

import io
import logging
from pathlib import Path
from typing import Any, Dict, Tuple

import magic
from PIL import Image, ImageFile
from PIL.ExifTags import TAGS

from .models import ImageMetadata, ImageProcessingConfig, ImageValidationError

# Configure PIL for security
ImageFile.LOAD_TRUNCATED_IMAGES = False  # Strict loading for security

logger = logging.getLogger(__name__)


class ImageValidator:
    """
    Comprehensive image validation with security focus.

    Implements magic number verification, PIL validation, and security scanning
    using development standards patterns for performance and reliability.
    """

    def __init__(self, config: ImageProcessingConfig):
        """
        Initialize validator with configuration.

        Args:
            config: Image processing configuration
        """
        self.config = config
        self._magic_mime = magic.Magic(mime=True)

        # Allowed MIME types for security
        self._allowed_mime_types = {
            "image/jpeg": [".jpg", ".jpeg"],
            "image/png": [".png"],
            "image/webp": [".webp"],
            "image/gif": [".gif"],
        }

        # Magic number signatures for validation
        self._magic_signatures = {
            "image/jpeg": [b"\xff\xd8\xff"],
            "image/png": [b"\x89PNG\r\n\x1a\n"],
            "image/webp": [b"RIFF"],  # WEBP files start with RIFF header
            "image/gif": [b"GIF87a", b"GIF89a"],
        }

    def validate_image_data(
        self, image_data: bytes, filename: str
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Comprehensive image validation with security checks.

        Args:
            image_data: Raw image data bytes
            filename: Original filename for extension validation

        Returns:
            Tuple of (is_valid, validation_results)

        Raises:
            ImageValidationError: If critical validation fails
        """
        validation_results = {
            "magic_number_valid": False,
            "mime_type_valid": False,
            "pil_validation_passed": False,
            "extension_match": False,
            "size_valid": False,
            "security_scan_passed": True,  # Default to true, set false if issues found
            "exif_safe": True,
        }

        try:
            # 1. Magic number validation
            if self.config.enable_magic_number_check:
                validation_results["magic_number_valid"] = self._validate_magic_number(
                    image_data
                )
                if not validation_results["magic_number_valid"]:
                    logger.warning(f"Magic number validation failed for {filename}")
            else:
                validation_results["magic_number_valid"] = True

            # 2. MIME type validation
            detected_mime = self._detect_mime_type(image_data)
            validation_results["mime_type_valid"] = self._validate_mime_type(
                detected_mime, filename
            )

            # 3. Extension matching validation
            validation_results["extension_match"] = self._validate_extension_match(
                detected_mime, filename
            )

            # 4. PIL format validation
            if self.config.enable_pil_validation:
                pil_result = self._validate_with_pil(image_data)
                validation_results.update(pil_result)
            else:
                validation_results["pil_validation_passed"] = True
                validation_results["size_valid"] = True

            # 5. Security-specific validations
            security_result = self._perform_security_scan(image_data)
            validation_results.update(security_result)

            # Overall validation result
            critical_checks = [
                "magic_number_valid",
                "mime_type_valid",
                "pil_validation_passed",
                "size_valid",
                "security_scan_passed",
            ]

            is_valid = all(
                validation_results.get(check, False) for check in critical_checks
            )

            logger.info(
                f"Image validation for {filename}: {'PASSED' if is_valid else 'FAILED'}"
            )
            return is_valid, validation_results

        except Exception as e:
            logger.error(f"Image validation error for {filename}: {e}")
            raise ImageValidationError(
                f"Validation failed: {str(e)}", error_code="VALIDATION_EXCEPTION"
            )

    def _validate_magic_number(self, image_data: bytes) -> bool:
        """
        Validate image magic number signatures.

        Args:
            image_data: Raw image data

        Returns:
            bool: True if magic number is valid
        """
        try:
            # Check first 16 bytes for magic signatures
            header = image_data[:16]

            for mime_type, signatures in self._magic_signatures.items():
                for signature in signatures:
                    if header.startswith(signature):
                        return True

            return False

        except Exception as e:
            logger.error(f"Magic number validation error: {e}")
            return False

    def _detect_mime_type(self, image_data: bytes) -> str:
        """
        Detect MIME type using python-magic.

        Args:
            image_data: Raw image data

        Returns:
            str: Detected MIME type
        """
        try:
            mime_type = self._magic_mime.from_buffer(image_data)
            return mime_type

        except Exception as e:
            logger.error(f"MIME type detection error: {e}")
            return "unknown/unknown"

    def _validate_mime_type(self, detected_mime: str, filename: str) -> bool:
        """
        Validate detected MIME type against allowed types.

        Args:
            detected_mime: MIME type detected by magic
            filename: Original filename

        Returns:
            bool: True if MIME type is allowed
        """
        if not self.config.strict_mime_type_check:
            return True

        allowed_mimes = set(self._allowed_mime_types.keys())
        is_valid = detected_mime in allowed_mimes

        if not is_valid:
            logger.warning(f"Disallowed MIME type {detected_mime} for {filename}")

        return is_valid

    def _validate_extension_match(self, detected_mime: str, filename: str) -> bool:
        """
        Validate that file extension matches detected MIME type.

        Args:
            detected_mime: Detected MIME type
            filename: Original filename

        Returns:
            bool: True if extension matches MIME type
        """
        try:
            file_extension = Path(filename).suffix.lower()

            if detected_mime in self._allowed_mime_types:
                allowed_extensions = self._allowed_mime_types[detected_mime]
                return file_extension in allowed_extensions

            return False

        except Exception as e:
            logger.error(f"Extension validation error for {filename}: {e}")
            return False

    def _validate_with_pil(self, image_data: bytes) -> Dict[str, Any]:
        """
        Validate image using PIL for format verification and metadata extraction.

        Args:
            image_data: Raw image data

        Returns:
            Dict containing PIL validation results
        """
        pil_results = {
            "pil_validation_passed": False,
            "size_valid": False,
            "format_valid": False,
            "pil_width": 0,
            "pil_height": 0,
            "pil_format": None,
            "exif_safe": True,
        }

        try:
            # Create PIL Image from bytes
            image_buffer = io.BytesIO(image_data)
            with Image.open(image_buffer) as img:
                # Verify image can be loaded
                img.verify()

                # Reset buffer for actual processing
                image_buffer.seek(0)
                with Image.open(image_buffer) as img:
                    # Extract basic information
                    pil_results["pil_width"] = img.width
                    pil_results["pil_height"] = img.height
                    pil_results["pil_format"] = img.format

                    # Validate image dimensions
                    pil_results["size_valid"] = self._validate_image_dimensions(
                        img.width, img.height
                    )

                    # Validate format
                    pil_results["format_valid"] = img.format in [
                        "JPEG",
                        "PNG",
                        "WEBP",
                        "GIF",
                    ]

                    # EXIF security check for JPEG images
                    if img.format == "JPEG":
                        pil_results["exif_safe"] = self._validate_exif_safety(img)

                    # Overall PIL validation
                    pil_results["pil_validation_passed"] = (
                        pil_results["size_valid"]
                        and pil_results["format_valid"]
                        and pil_results["exif_safe"]
                    )

            return pil_results

        except Exception as e:
            logger.error(f"PIL validation error: {e}")
            pil_results["pil_validation_passed"] = False
            return pil_results

    def _validate_image_dimensions(self, width: int, height: int) -> bool:
        """
        Validate image dimensions against configuration limits.

        Args:
            width: Image width in pixels
            height: Image height in pixels

        Returns:
            bool: True if dimensions are valid
        """
        if width <= 0 or height <= 0:
            return False

        if width > self.config.max_width or height > self.config.max_height:
            logger.warning(f"Image dimensions {width}x{height} exceed limits")
            return False

        # Reasonable minimum dimensions
        if width < 10 or height < 10:
            logger.warning(f"Image dimensions {width}x{height} too small")
            return False

        return True

    def _validate_exif_safety(self, img: Image.Image) -> bool:
        """
        Validate EXIF data for security concerns.

        Args:
            img: PIL Image object

        Returns:
            bool: True if EXIF data is safe
        """
        try:
            # Check if EXIF data exists
            exif_data = img.getexif()

            if not exif_data:
                return True  # No EXIF data is safe

            # Check for suspicious EXIF tags
            suspicious_tags = {"GPS", "UserComment", "ImageDescription"}

            for tag_id, value in exif_data.items():
                tag_name = TAGS.get(tag_id, f"Unknown({tag_id})")

                # Check for GPS data
                if "GPS" in tag_name:
                    logger.warning("GPS EXIF data found in image")
                    return False

                # Check for overly long text fields (potential exploit)
                if isinstance(value, str) and len(value) > 1000:
                    logger.warning(f"Suspiciously long EXIF field: {tag_name}")
                    return False

            return True

        except Exception as e:
            logger.error(f"EXIF validation error: {e}")
            return False  # Fail safe

    def _perform_security_scan(self, image_data: bytes) -> Dict[str, Any]:
        """
        Perform security-specific image scanning.

        Args:
            image_data: Raw image data

        Returns:
            Dict containing security scan results
        """
        security_results = {
            "security_scan_passed": True,
            "file_size_valid": False,
            "entropy_check_passed": True,
            "steganography_check_passed": True,
        }

        try:
            # 1. File size validation
            file_size = len(image_data)
            max_size = self.config.max_file_size_mb * 1024 * 1024

            security_results["file_size_valid"] = (
                file_size > 100  # Minimum 100 bytes
                and file_size <= max_size
            )

            if not security_results["file_size_valid"]:
                logger.warning(f"Invalid file size: {file_size} bytes")

            # 2. Basic entropy check (steganography detection)
            if self.config.enable_malware_scan:
                security_results["entropy_check_passed"] = self._check_entropy(
                    image_data
                )
                security_results["steganography_check_passed"] = (
                    self._check_steganography(image_data)
                )

            # Overall security result
            security_results["security_scan_passed"] = all(
                [
                    security_results["file_size_valid"],
                    security_results["entropy_check_passed"],
                    security_results["steganography_check_passed"],
                ]
            )

            return security_results

        except Exception as e:
            logger.error(f"Security scan error: {e}")
            security_results["security_scan_passed"] = False
            return security_results

    def _check_entropy(self, image_data: bytes) -> bool:
        """
        Basic entropy check for steganography detection.

        Args:
            image_data: Raw image data

        Returns:
            bool: True if entropy is normal
        """
        try:
            # Simple entropy check - look for unusual patterns
            # This is a basic implementation - full steganography detection would be more complex

            # Check for repeated byte patterns
            if len(set(image_data[-1000:])) < 10:  # Last 1KB has very low entropy
                logger.warning("Low entropy detected in image data")
                return False

            return True

        except Exception as e:
            logger.error(f"Entropy check error: {e}")
            return True  # Default to pass if check fails

    def _check_steganography(self, image_data: bytes) -> bool:
        """
        Basic steganography detection.

        Args:
            image_data: Raw image data

        Returns:
            bool: True if no steganography detected
        """
        try:
            # Basic checks for common steganography signatures
            suspicious_patterns = [b"steghide", b"jphide", b"outguess", b"F5"]

            # Check last 2KB for suspicious patterns
            data_tail = image_data[-2048:]

            for pattern in suspicious_patterns:
                if pattern in data_tail:
                    logger.warning(
                        f"Potential steganography tool signature found: {pattern}"
                    )
                    return False

            return True

        except Exception as e:
            logger.error(f"Steganography check error: {e}")
            return True  # Default to pass if check fails

    def extract_metadata(
        self, image_data: bytes, filename: str, validation_results: Dict[str, Any]
    ) -> ImageMetadata:
        """
        Extract comprehensive metadata from validated image.

        Args:
            image_data: Raw image data
            filename: Original filename
            validation_results: Results from validation process

        Returns:
            ImageMetadata: Extracted metadata

        Raises:
            ImageValidationError: If metadata extraction fails
        """
        try:
            # Get basic file information
            file_size = len(image_data)
            detected_mime = self._detect_mime_type(image_data)

            # Extract PIL metadata
            width = validation_results.get("pil_width", 0)
            height = validation_results.get("pil_height", 0)

            # Calculate processing time (placeholder - actual timing done in pipeline)
            processing_time_ms = 0.0

            # Create metadata object
            metadata = ImageMetadata(
                filename=filename,
                file_size=file_size,
                mime_type=detected_mime,
                width=width,
                height=height,
                magic_number_valid=validation_results.get("magic_number_valid", False),
                pil_validation_passed=validation_results.get(
                    "pil_validation_passed", False
                ),
                security_scan_passed=validation_results.get(
                    "security_scan_passed", False
                ),
                processing_time_ms=processing_time_ms,
                provider_compatible=True,  # Compatible with F2 provider interface
                provider_metadata={
                    "format": validation_results.get("pil_format"),
                    "validation_details": validation_results,
                },
            )

            return metadata

        except Exception as e:
            logger.error(f"Metadata extraction error for {filename}: {e}")
            raise ImageValidationError(
                f"Failed to extract metadata: {str(e)}",
                error_code="METADATA_EXTRACTION_ERROR",
            )
