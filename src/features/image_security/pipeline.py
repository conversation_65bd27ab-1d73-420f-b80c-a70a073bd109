"""
C1 Image Upload Security Pipeline.

Main orchestration pipeline for secure image processing with F2 provider interface
integration and F1 database compatibility.
"""

import logging
import time
from typing import Any, Dict, Optional

from .models import (
    ImageProcessingConfig,
    ImageSecurityResult,
    ImageUploadRequest,
    ImageValidationError,
)
from .processors import ImageProcessor
from .validators import ImageValidator

logger = logging.getLogger(__name__)


class ImageSecurityPipeline:
    """
    Main image security pipeline for comprehensive validation and processing.

    Integrates validation, processing, and provider interface compatibility
    following development standards patterns for performance and security.
    """

    def __init__(self, config: Optional[ImageProcessingConfig] = None):
        """
        Initialize pipeline with configuration.

        Args:
            config: Image processing configuration, uses environment defaults if None
        """
        self.config = config or ImageProcessingConfig.from_environment()
        self.validator = ImageValidator(self.config)
        self.processor = ImageProcessor(self.config)

        # Performance tracking
        self._processing_stats = {
            "total_processed": 0,
            "validation_failures": 0,
            "processing_failures": 0,
            "avg_processing_time_ms": 0.0,
        }

    def process_uploaded_image(
        self,
        image_data: bytes,
        filename: str,
        session_id: str,
        upload_request: Optional[ImageUploadRequest] = None,
    ) -> ImageSecurityResult:
        """
        Process uploaded image with comprehensive security validation.

        Args:
            image_data: Raw image data from upload
            filename: Original filename
            session_id: User session ID for isolation
            upload_request: Optional upload request parameters

        Returns:
            ImageSecurityResult: Complete processing result
        """
        start_time = time.time()
        processing_metadata = {
            "session_id": session_id,
            "original_filename": filename,
            "original_size": len(image_data),
            "processing_steps": [],
        }

        try:
            logger.info(
                f"Starting image security pipeline for {filename} (session: {session_id[:8]})"
            )

            # 1. Input validation
            self._validate_input_parameters(image_data, filename, session_id)
            processing_metadata["processing_steps"].append("input_validated")

            # 2. Security validation
            validation_start = time.time()
            is_valid, validation_results = self.validator.validate_image_data(
                image_data, filename
            )
            validation_time = (time.time() - validation_start) * 1000
            processing_metadata["validation_time_ms"] = validation_time
            processing_metadata["processing_steps"].append("security_validated")

            if not is_valid:
                self._processing_stats["validation_failures"] += 1
                return self._create_failure_result(
                    filename,
                    "Security validation failed",
                    "SECURITY_VALIDATION_FAILED",
                    validation_results,
                    time.time() - start_time,
                )

            # 3. Extract metadata
            metadata = self.validator.extract_metadata(
                image_data, filename, validation_results
            )
            processing_metadata["processing_steps"].append("metadata_extracted")

            # 4. Process image for provider compatibility
            processing_start = time.time()
            target_provider = (
                upload_request.target_provider
                if upload_request
                else self.config.default_provider
            )

            processed_bytes, proc_metadata = self.processor.optimize_for_provider(
                image_data, target_provider
            )
            processing_time = (time.time() - processing_start) * 1000
            processing_metadata["image_processing_time_ms"] = processing_time
            processing_metadata["processing_steps"].append("image_processed")

            # 5. Create base64 data URI for API
            base64_start = time.time()
            target_format = (
                "image/jpeg" if target_provider == "google_veo3" else "image/png"
            )
            base64_data = self.processor.create_base64_data_uri(
                processed_bytes, target_format
            )
            base64_time = (time.time() - base64_start) * 1000
            processing_metadata["base64_time_ms"] = base64_time
            processing_metadata["processing_steps"].append("base64_created")

            # 6. Create secure filename for reference
            secure_filename = self.processor.create_secure_filename(
                filename, session_id
            )
            processing_metadata["secure_filename"] = secure_filename
            processing_metadata["processing_steps"].append("filename_secured")

            # 7. Calculate total processing time
            total_time = (time.time() - start_time) * 1000
            metadata.processing_time_ms = total_time

            # 8. Update statistics
            self._update_processing_stats(total_time)

            # 9. Create success result
            result = ImageSecurityResult(
                is_safe=True,
                validation_passed=True,
                metadata=metadata,
                base64_data=base64_data,
                secure_path=secure_filename,  # Reference only, no actual path storage
                validation_results=validation_results,
                security_warnings=[],  # No warnings for successful processing
                total_processing_time_ms=total_time,
                provider_ready=True,
                provider_format="base64",
            )

            logger.info(
                f"Image processing completed successfully for {filename}: "
                f"{total_time:.2f}ms, provider: {target_provider}"
            )

            return result

        except ImageValidationError as e:
            self._processing_stats["validation_failures"] += 1
            logger.warning(f"Image validation error for {filename}: {e.message}")

            return self._create_failure_result(
                filename, e.message, e.error_code, {}, time.time() - start_time
            )

        except Exception as e:
            self._processing_stats["processing_failures"] += 1
            logger.error(f"Unexpected error processing {filename}: {e}")

            return self._create_failure_result(
                filename,
                f"Internal processing error: {str(e)}",
                "INTERNAL_ERROR",
                {},
                time.time() - start_time,
            )

    def _validate_input_parameters(
        self, image_data: bytes, filename: str, session_id: str
    ) -> None:
        """
        Validate input parameters before processing.

        Args:
            image_data: Raw image data
            filename: Original filename
            session_id: User session ID

        Raises:
            ImageValidationError: If input validation fails
        """
        # Check image data
        if not image_data:
            raise ImageValidationError("No image data provided", "EMPTY_IMAGE_DATA")

        # Check file size limits
        max_size = self.config.max_file_size_mb * 1024 * 1024
        if len(image_data) > max_size:
            raise ImageValidationError(
                f"File size {len(image_data)} bytes exceeds limit of {max_size} bytes",
                "FILE_TOO_LARGE",
            )

        if len(image_data) < 100:
            raise ImageValidationError(
                "File too small to be valid image", "FILE_TOO_SMALL"
            )

        # Check filename
        if not filename or not filename.strip():
            raise ImageValidationError("Invalid filename", "INVALID_FILENAME")

        # Check session ID
        if not session_id or len(session_id) < 8:
            raise ImageValidationError("Invalid session ID", "INVALID_SESSION")

    def _create_failure_result(
        self,
        filename: str,
        error_message: str,
        error_code: str,
        validation_results: Dict[str, Any],
        processing_time: float,
    ) -> ImageSecurityResult:
        """
        Create failure result for error cases.

        Args:
            filename: Original filename
            error_message: Error description
            error_code: Error code
            validation_results: Validation results if available
            processing_time: Processing time in seconds

        Returns:
            ImageSecurityResult: Failure result
        """
        return ImageSecurityResult(
            is_safe=False,
            validation_passed=False,
            metadata=None,
            base64_data=None,
            secure_path=None,
            error_message=error_message,
            error_code=error_code,
            validation_results=validation_results,
            security_warnings=[error_message],
            total_processing_time_ms=processing_time * 1000,
            provider_ready=False,
            provider_format="none",
        )

    def _update_processing_stats(self, processing_time_ms: float) -> None:
        """
        Update internal processing statistics.

        Args:
            processing_time_ms: Processing time in milliseconds
        """
        self._processing_stats["total_processed"] += 1

        # Update running average
        total = self._processing_stats["total_processed"]
        current_avg = self._processing_stats["avg_processing_time_ms"]
        self._processing_stats["avg_processing_time_ms"] = (
            current_avg * (total - 1) + processing_time_ms
        ) / total

    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        Get pipeline processing statistics.

        Returns:
            Dict containing processing statistics
        """
        total = self._processing_stats["total_processed"]
        failures = (
            self._processing_stats["validation_failures"]
            + self._processing_stats["processing_failures"]
        )

        return {
            "total_processed": total,
            "successful_processed": total - failures,
            "validation_failures": self._processing_stats["validation_failures"],
            "processing_failures": self._processing_stats["processing_failures"],
            "success_rate": (total - failures) / total if total > 0 else 0.0,
            "avg_processing_time_ms": self._processing_stats["avg_processing_time_ms"],
            "configuration": {
                "max_file_size_mb": self.config.max_file_size_mb,
                "max_width": self.config.max_width,
                "max_height": self.config.max_height,
                "default_provider": self.config.default_provider,
            },
        }

    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on pipeline components.

        Returns:
            Dict containing health status
        """
        health_status = {
            "pipeline_healthy": True,
            "validator_healthy": True,
            "processor_healthy": True,
            "configuration_valid": True,
            "error_messages": [],
        }

        try:
            # Test validator with minimal data
            test_data = b"\xff\xd8\xff\xe0\x00\x10JFIF"  # JPEG header
            try:
                self.validator._validate_magic_number(test_data)
            except Exception as e:
                health_status["validator_healthy"] = False
                health_status["error_messages"].append(f"Validator error: {e}")

            # Test processor
            try:
                self.processor.create_secure_filename("test.jpg", "test_session")
            except Exception as e:
                health_status["processor_healthy"] = False
                health_status["error_messages"].append(f"Processor error: {e}")

            # Validate configuration
            if self.config.max_file_size_mb <= 0 or self.config.max_width <= 0:
                health_status["configuration_valid"] = False
                health_status["error_messages"].append("Invalid configuration values")

            # Overall health
            health_status["pipeline_healthy"] = (
                health_status["validator_healthy"]
                and health_status["processor_healthy"]
                and health_status["configuration_valid"]
            )

        except Exception as e:
            health_status["pipeline_healthy"] = False
            health_status["error_messages"].append(f"Health check error: {e}")

        return health_status

    @classmethod
    def create_default_pipeline(cls) -> "ImageSecurityPipeline":
        """
        Create pipeline with default configuration.

        Returns:
            ImageSecurityPipeline: Pipeline with default configuration
        """
        config = ImageProcessingConfig.from_environment()
        return cls(config)

    @classmethod
    def create_testing_pipeline(cls) -> "ImageSecurityPipeline":
        """
        Create pipeline optimized for testing.

        Returns:
            ImageSecurityPipeline: Pipeline with testing configuration
        """
        config = ImageProcessingConfig(
            max_file_size_mb=5,  # Smaller for testing
            max_width=1024,
            max_height=1024,
            enable_magic_number_check=True,
            enable_pil_validation=True,
            enable_malware_scan=False,  # Disable for faster testing
            processing_timeout_seconds=10,
            max_concurrent_uploads=5,
        )
        return cls(config)
