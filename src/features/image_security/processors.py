"""
C1 Image Security Processors.

Secure image processing with PIL operations, format conversion, and base64 encoding
for F2 provider interface integration.
"""

import base64
import io
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Tuple

from PIL import Image, ImageOps
from werkzeug.utils import secure_filename

from .models import ImageProcessingConfig, ImageValidationError

logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    Secure image processing with PIL operations and provider interface compatibility.

    Handles resizing, format conversion, optimization, and base64 encoding
    following development standards for performance and security.
    """

    def __init__(self, config: ImageProcessingConfig):
        """
        Initialize processor with configuration.

        Args:
            config: Image processing configuration
        """
        self.config = config

        # Apply PIL security settings
        self._apply_pil_security_settings()

        # Supported output formats for provider interface
        self._output_formats = {
            "image/jpeg": "JPEG",
            "image/png": "PNG",
            "image/webp": "WEBP",
        }

        # Quality settings by format
        self._quality_settings = {
            "JPEG": {"quality": self.config.jpeg_quality, "optimize": True},
            "PNG": {"optimize": self.config.png_optimize},
            "WEBP": {"quality": self.config.jpeg_quality, "optimize": True},
        }

    def _apply_pil_security_settings(self) -> None:
        """
        Apply PIL security settings based on configuration.

        Sets global PIL security settings to prevent security vulnerabilities.
        """
        try:
            from PIL import Image, ImageFile

            # Set LOAD_TRUNCATED_IMAGES to False for security
            ImageFile.LOAD_TRUNCATED_IMAGES = False
            logger.info("PIL security: LOAD_TRUNCATED_IMAGES set to False")

            # Set maximum image pixels to prevent decompression bombs
            if hasattr(Image, "MAX_IMAGE_PIXELS"):
                # Use configuration-based limit or PIL default
                max_pixels = getattr(self.config, "pil_max_image_pixels", 178956970)
                if max_pixels and max_pixels > 0:
                    Image.MAX_IMAGE_PIXELS = max_pixels
                    logger.info(f"PIL security: MAX_IMAGE_PIXELS set to {max_pixels:,}")

            # Set additional security warnings
            import warnings

            warnings.filterwarnings("error", category=Image.DecompressionBombWarning)
            logger.info("PIL security: DecompressionBombWarning set to error")

        except Exception as e:
            logger.error(f"Error applying PIL security settings: {e}")

    def process_image_for_provider(
        self,
        image_data: bytes,
        target_format: str = "image/jpeg",
        max_width: Optional[int] = None,
        max_height: Optional[int] = None,
    ) -> Tuple[bytes, Dict[str, Any]]:
        """
        Process image for provider interface compatibility.

        Args:
            image_data: Raw image data
            target_format: Target MIME type for output
            max_width: Maximum width for resizing
            max_height: Maximum height for resizing

        Returns:
            Tuple of (processed_bytes, processing_metadata)

        Raises:
            ImageValidationError: If processing fails
        """
        try:
            processing_metadata = {
                "original_size": len(image_data),
                "target_format": target_format,
                "processing_steps": [],
                "final_dimensions": None,
                "compression_ratio": 0.0,
            }

            # Open image with PIL
            image_buffer = io.BytesIO(image_data)
            with Image.open(image_buffer) as img:
                original_size = (img.width, img.height)
                processing_metadata["original_dimensions"] = original_size
                processing_metadata["processing_steps"].append("image_loaded")

                # Convert to RGB if necessary (for JPEG output)
                if target_format == "image/jpeg" and img.mode in ("RGBA", "LA", "P"):
                    img = self._convert_to_rgb(img)
                    processing_metadata["processing_steps"].append("converted_to_rgb")

                # Resize if needed
                if self.config.auto_resize:
                    resize_width = max_width or self.config.max_width
                    resize_height = max_height or self.config.max_height

                    img = self._resize_image(img, resize_width, resize_height)
                    processing_metadata["processing_steps"].append("resized")

                # Strip EXIF data for security
                img = self._strip_exif_data(img)
                processing_metadata["processing_steps"].append("exif_stripped")

                # Apply orientation correction
                img = self._correct_orientation(img)
                processing_metadata["processing_steps"].append("orientation_corrected")

                # Convert to target format and optimize
                processed_bytes = self._convert_and_optimize(img, target_format)
                processing_metadata["processing_steps"].append("format_converted")

                # Calculate final metadata
                processing_metadata["final_dimensions"] = (img.width, img.height)
                processing_metadata["final_size"] = len(processed_bytes)
                processing_metadata["compression_ratio"] = (
                    len(processed_bytes) / len(image_data)
                    if len(image_data) > 0
                    else 1.0
                )

                logger.info(
                    f"Image processed successfully: {original_size} -> {img.size}, "
                    f"compression: {processing_metadata['compression_ratio']:.2f}"
                )

                return processed_bytes, processing_metadata

        except Exception as e:
            logger.error(f"Image processing error: {e}")
            raise ImageValidationError(
                f"Image processing failed: {str(e)}", error_code="PROCESSING_ERROR"
            )

    def _convert_to_rgb(self, img: Image.Image) -> Image.Image:
        """
        Convert image to RGB mode for JPEG compatibility.

        Args:
            img: PIL Image object

        Returns:
            PIL Image in RGB mode
        """
        try:
            if img.mode == "RGBA":
                # Create white background for transparency
                background = Image.new("RGB", img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])  # Use alpha channel as mask
                return background
            elif img.mode == "LA":
                # Convert grayscale with alpha to RGB
                background = Image.new("RGB", img.size, (255, 255, 255))
                grayscale = img.convert("L")
                background.paste(grayscale, mask=img.split()[-1])
                return background
            elif img.mode == "P":
                # Convert palette mode to RGB
                return img.convert("RGB")
            else:
                # Already in RGB or compatible mode
                return img.convert("RGB")

        except Exception as e:
            logger.error(f"RGB conversion error: {e}")
            # Fallback to basic conversion
            return img.convert("RGB")

    def _resize_image(
        self, img: Image.Image, max_width: int, max_height: int
    ) -> Image.Image:
        """
        Resize image while maintaining aspect ratio.

        Args:
            img: PIL Image object
            max_width: Maximum width
            max_height: Maximum height

        Returns:
            Resized PIL Image
        """
        try:
            # Check if resizing is needed
            if img.width <= max_width and img.height <= max_height:
                return img

            # Calculate resize dimensions maintaining aspect ratio
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)

            # Use high-quality resampling
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            logger.debug(f"Image resized from {img.size} to {resized_img.size}")
            return resized_img

        except Exception as e:
            logger.error(f"Image resize error: {e}")
            # Return original image if resize fails
            return img

    def _strip_exif_data(self, img: Image.Image) -> Image.Image:
        """
        Strip EXIF and other metadata for security.

        Args:
            img: PIL Image object

        Returns:
            PIL Image without EXIF data
        """
        try:
            # Create new image without metadata
            data = list(img.getdata())
            clean_img = Image.new(img.mode, img.size)
            clean_img.putdata(data)

            return clean_img

        except Exception as e:
            logger.error(f"EXIF stripping error: {e}")
            # Return original image if stripping fails
            return img

    def _correct_orientation(self, img: Image.Image) -> Image.Image:
        """
        Correct image orientation based on EXIF data.

        Args:
            img: PIL Image object

        Returns:
            Orientation-corrected PIL Image
        """
        try:
            # Use ImageOps to handle orientation correction
            return ImageOps.exif_transpose(img)

        except Exception as e:
            logger.error(f"Orientation correction error: {e}")
            # Return original image if correction fails
            return img

    def _convert_and_optimize(self, img: Image.Image, target_format: str) -> bytes:
        """
        Convert image to target format with optimization.

        Args:
            img: PIL Image object
            target_format: Target MIME type

        Returns:
            Optimized image bytes
        """
        try:
            output_buffer = io.BytesIO()
            pil_format = self._output_formats.get(target_format, "JPEG")

            # Get quality settings for format
            save_kwargs = self._quality_settings.get(pil_format, {})

            # Save with optimization
            img.save(output_buffer, format=pil_format, **save_kwargs)

            return output_buffer.getvalue()

        except Exception as e:
            logger.error(f"Format conversion error: {e}")
            raise ImageValidationError(
                f"Format conversion failed: {str(e)}",
                error_code="FORMAT_CONVERSION_ERROR",
            )

    def create_base64_data_uri(self, image_bytes: bytes, mime_type: str) -> str:
        """
        Create base64 data URI for provider interface.

        Args:
            image_bytes: Processed image bytes
            mime_type: MIME type for data URI

        Returns:
            Base64 data URI string

        Raises:
            ImageValidationError: If encoding fails
        """
        try:
            # Encode to base64
            base64_data = base64.b64encode(image_bytes).decode("ascii")

            # Create data URI
            data_uri = f"data:{mime_type};base64,{base64_data}"

            # Validate data URI size (reasonable limit for API)
            max_size = 10 * 1024 * 1024  # 10MB encoded limit
            if len(data_uri) > max_size:
                raise ImageValidationError(
                    f"Base64 data URI too large: {len(data_uri)} bytes",
                    error_code="DATA_URI_TOO_LARGE",
                )

            return data_uri

        except Exception as e:
            logger.error(f"Base64 encoding error: {e}")
            raise ImageValidationError(
                f"Base64 encoding failed: {str(e)}", error_code="BASE64_ENCODING_ERROR"
            )

    def create_secure_filename(self, original_filename: str, session_id: str) -> str:
        """
        Create secure filename for temporary storage.

        Args:
            original_filename: Original uploaded filename
            session_id: User session ID for isolation

        Returns:
            Secure filename for storage
        """
        try:
            # Use werkzeug secure_filename for basic security
            safe_name = secure_filename(original_filename)

            # Extract extension
            path_obj = Path(safe_name)
            extension = path_obj.suffix.lower()

            # Create unique filename with session isolation
            import uuid

            unique_id = str(uuid.uuid4())[:8]

            # Format: sessionid_uniqueid_original.ext
            secure_name = f"{session_id[:8]}_{unique_id}_{path_obj.stem}{extension}"

            # Additional security: limit length
            if len(secure_name) > 100:
                secure_name = f"{session_id[:8]}_{unique_id}{extension}"

            return secure_name

        except Exception as e:
            logger.error(f"Secure filename creation error: {e}")
            # Fallback to minimal secure name
            import uuid

            return f"{session_id[:8]}_{str(uuid.uuid4())[:8]}.jpg"

    def optimize_for_provider(
        self, image_data: bytes, provider: str = "google_veo3"
    ) -> Tuple[bytes, Dict[str, Any]]:
        """
        Optimize image for specific provider requirements.

        Args:
            image_data: Raw image data
            provider: Target provider name

        Returns:
            Tuple of (optimized_bytes, optimization_metadata)
        """
        try:
            optimization_metadata = {
                "provider": provider,
                "optimizations_applied": [],
                "original_size": len(image_data),
            }

            if provider == "google_veo3":
                # Google Veo3 optimizations
                processed_bytes, proc_meta = self.process_image_for_provider(
                    image_data,
                    target_format="image/jpeg",  # Veo3 prefers JPEG
                    max_width=1920,  # Veo3 recommended max width
                    max_height=1080,  # Veo3 recommended max height
                )
                optimization_metadata["optimizations_applied"].extend(
                    ["veo3_format_optimization", "veo3_dimension_optimization"]
                )

            elif provider == "azure_sora":
                # Azure Sora optimizations (future use)
                processed_bytes, proc_meta = self.process_image_for_provider(
                    image_data,
                    target_format="image/png",  # Sora might prefer PNG
                    max_width=1280,
                    max_height=720,
                )
                optimization_metadata["optimizations_applied"].extend(
                    ["sora_format_optimization", "sora_dimension_optimization"]
                )

            else:
                # Default optimization
                processed_bytes, proc_meta = self.process_image_for_provider(
                    image_data, target_format="image/jpeg"
                )
                optimization_metadata["optimizations_applied"].append(
                    "default_optimization"
                )

            # Update metadata
            optimization_metadata.update(proc_meta)
            optimization_metadata["final_size"] = len(processed_bytes)
            optimization_metadata["size_reduction"] = (
                (len(image_data) - len(processed_bytes)) / len(image_data)
                if len(image_data) > 0
                else 0.0
            )

            logger.info(
                f"Image optimized for {provider}: "
                f"size reduction {optimization_metadata['size_reduction']:.2%}"
            )

            return processed_bytes, optimization_metadata

        except Exception as e:
            logger.error(f"Provider optimization error for {provider}: {e}")
            raise ImageValidationError(
                f"Provider optimization failed: {str(e)}",
                error_code="PROVIDER_OPTIMIZATION_ERROR",
            )
