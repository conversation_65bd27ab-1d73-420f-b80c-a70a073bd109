"""
HTTP client for Azure OpenAI Sora API communication.

Handles low-level HTTP communication with proper error handling,
rate limiting, and response processing.
"""

import json
import logging
import time
import uuid
from typing import Any, Dict, Optional, Union

import requests

from src.rate_limiting.limiter import (
    GlobalRateLimiter,
    RateLimitExceeded,
    create_rate_limiter,
)

logger = logging.getLogger(__name__)


class SoraHttpClient:
    """
    HTTP client for Azure OpenAI Sora API communication.

    Handles authentication, rate limiting, and HTTP request/response processing.
    """

    def __init__(self, endpoint: str, api_key: str, api_version: str, deployment: str):
        """
        Initialize HTTP client with Azure configuration.

        Args:
            endpoint: Azure OpenAI endpoint URL
            api_key: Azure OpenAI API key
            api_version: Azure OpenAI API version
            deployment: Azure OpenAI deployment name
        """
        self.endpoint = endpoint
        self.api_key = api_key
        self.api_version = api_version
        self.deployment = deployment

        # Setup rate limiting
        self.rate_limiter = self._setup_rate_limiter()

        # Setup session for connection reuse
        self.session = requests.Session()
        self.session.headers.update(
            {"Content-Type": "application/json", "api-key": self.api_key}
        )

    def _setup_rate_limiter(self) -> Optional[GlobalRateLimiter]:
        """
        Setup rate limiter for API calls.

        Returns:
            Optional[GlobalRateLimiter]: Rate limiter instance or None if disabled
        """
        try:
            return create_rate_limiter()
        except Exception as e:
            logger.warning(f"Failed to create rate limiter: {e}")
            return None

    def _sanitize_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """
        Sanitize headers for logging by redacting sensitive information.

        Args:
            headers: Original headers dictionary

        Returns:
            Dict[str, str]: Sanitized headers for safe logging
        """
        sanitized = {}
        sensitive_keys = ["api-key", "authorization", "x-api-key", "bearer"]

        for key, value in headers.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = "***REDACTED***"
            else:
                sanitized[key] = value

        return sanitized

    def _apply_rate_limiting(self) -> None:
        """
        Apply rate limiting before making API calls.

        Raises:
            RateLimitExceeded: If rate limit is exceeded
        """
        if not self.rate_limiter:
            return

        try:
            # Use unique key for this endpoint/deployment
            rate_key = f"sora_api:{self.deployment}"

            # Check if request is allowed (10 requests per second)
            if not self.rate_limiter.is_allowed(rate_key, 10, 1):
                raise RateLimitExceeded("API rate limit exceeded")

            logger.debug(f"Rate limit passed for key: {rate_key}")

        except Exception as e:
            logger.warning(f"Rate limiting error: {e}")
            # Continue without rate limiting rather than failing

    def make_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Union[str, Dict[str, Any]]] = None,
        timeout: int = 30,
        max_retries: int = 3,
    ) -> requests.Response:
        """
        Make HTTP request with retry logic and error handling.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            headers: Optional additional headers
            data: Request body data
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts

        Returns:
            requests.Response: HTTP response object

        Raises:
            requests.RequestException: On request failure
            RateLimitExceeded: If rate limit is exceeded
        """
        # Apply rate limiting
        self._apply_rate_limiting()

        # Add required api-version query parameter for Azure OpenAI API
        if "?" in url:
            url += f"&api-version={self.api_version}"
        else:
            url += f"?api-version={self.api_version}"

        # Generate unique request ID for tracing
        request_id = str(uuid.uuid4())[:8]

        # Merge headers
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)

        # Sanitize headers for logging
        sanitized_headers = self._sanitize_headers(request_headers)

        # Log request details
        logger.info(f"🌐 HTTP REQUEST [{request_id}] - Method: {method}, URL: {url}")
        logger.info(f"🌐 HTTP REQUEST [{request_id}] - Headers: {sanitized_headers}")

        if data:
            if isinstance(data, str):
                logger.info(f"🌐 HTTP REQUEST [{request_id}] - Body: {data}")
            else:
                logger.info(
                    f"🌐 HTTP REQUEST [{request_id}] - Body: {json.dumps(data, indent=2)}"
                )

        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # Exponential backoff for retries
                    delay = 2**attempt
                    logger.info(f"🔄 Retry attempt {attempt} after {delay}s delay")
                    time.sleep(delay)

                # Make the request
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,  # Use original headers, not merged ones
                    data=data
                    if isinstance(data, str)
                    else json.dumps(data)
                    if data
                    else None,
                    timeout=timeout,
                )

                # Log response details
                logger.info(
                    f"🌐 HTTP RESPONSE [{request_id}] - Status: {response.status_code}"
                )
                logger.info(
                    f"🌐 HTTP RESPONSE [{request_id}] - Headers: {dict(response.headers)}"
                )

                # Log response body (truncated if too long)
                response_text = response.text
                if len(response_text) > 1000:
                    response_text = response_text[:1000] + "... (truncated)"
                logger.info(f"🌐 HTTP RESPONSE [{request_id}] - Body: {response_text}")

                # Check for HTTP errors
                response.raise_for_status()

                return response

            except requests.exceptions.RequestException as e:
                last_exception = e
                logger.warning(
                    f"🌐 HTTP REQUEST [{request_id}] failed (attempt {attempt + 1}): {e}"
                )

                # Don't retry on 4xx errors (client errors)
                if (
                    hasattr(e, "response")
                    and e.response
                    and 400 <= e.response.status_code < 500
                ):
                    logger.error(
                        f"🌐 HTTP REQUEST [{request_id}] - Client error, not retrying"
                    )
                    break

        # All retries exhausted
        logger.error(f"🌐 HTTP REQUEST [{request_id}] - All retries exhausted")
        raise last_exception

    def close(self) -> None:
        """Close the HTTP session."""
        self.session.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
