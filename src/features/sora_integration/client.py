"""
Refactored Azure OpenAI Sora API client with focused responsibilities.

This is a replacement for the original 494-line SoraClient, broken down
into focused components with clear separation of concerns.
"""

import logging
from typing import Any, Dict, Optional

from src.core.models import VideoJob

from .http_client import SoraHttpClient
from .job_manager import VideoJobManager
from .video_downloader import VideoDownloader

logger = logging.getLogger(__name__)


class SoraClient:
    """
    Focused Azure OpenAI Sora API client.

    Orchestrates HTTP communication, job management, and video downloads
    using focused component classes.
    """

    def __init__(self):
        """
        Initialize client with Azure configuration.

        Raises:
            ValueError: If required environment variables are missing
        """
        from src.config.factory import ConfigurationFactory

        # Get Azure configuration
        azure_config = ConfigurationFactory.get_azure_config()

        logger.info(
            f"🔧 Initializing SoraClient with deployment: {azure_config['deployment_name']}"
        )

        # Initialize HTTP client
        self.http_client = SoraHttpClient(
            endpoint=azure_config["endpoint"],
            api_key=azure_config["api_key"],
            api_version=azure_config["api_version"],
            deployment=azure_config["deployment_name"],
        )

        # Initialize video downloader
        self.video_downloader = VideoDownloader()

        # Initialize job manager with video downloader
        self.job_manager = VideoJobManager(self.http_client, self.video_downloader)

        logger.info("✅ SoraClient initialized successfully")

    def create_video_job(self, prompt: str, ui_parameters: Dict[str, Any]) -> VideoJob:
        """
        Create a new video generation job.

        Args:
            prompt: Text prompt for video generation
            ui_parameters: UI parameters (width, height, duration, model)

        Returns:
            VideoJob: Created job with generation ID

        Example:
            >>> client = SoraClient()
            >>> job = client.create_video_job(
            ...     prompt="A cat playing piano",
            ...     ui_parameters={"width": 1920, "height": 1080, "duration": 10}
            ... )
            >>> print(job.generation_id)
        """
        logger.info(f"🎬 Creating video job with prompt: '{prompt[:50]}...'")

        try:
            return self.job_manager.create_job(prompt, ui_parameters)
        except Exception as e:
            logger.error(f"❌ Failed to create video job: {e}")
            raise

    def poll_job_status(self, job_id: str, generation_id: str) -> VideoJob:
        """
        Poll job status from Azure API.

        Args:
            job_id: Local job identifier
            generation_id: Azure generation identifier

        Returns:
            VideoJob: Updated job with current status

        Example:
            >>> client = SoraClient()
            >>> job = client.poll_job_status("job-123", "gen-456")
            >>> print(job.status)  # "running", "succeeded", or "failed"
        """
        logger.info(
            f"📊 Polling job status - Job: {job_id}, Generation: {generation_id}"
        )

        try:
            # Get job status (VideoJobManager handles download internally)
            job = self.job_manager.poll_job_status(job_id, generation_id)

            # Job manager already handles download when status becomes "succeeded"
            return job

        except Exception as e:
            logger.error(f"❌ Failed to poll job status: {e}")
            raise

    def download_video(self, video_url: str, job_id: str) -> Optional[str]:
        """
        Download video from URL.

        Args:
            video_url: URL of the video to download
            job_id: Job identifier for file naming

        Returns:
            Optional[str]: Local file path if successful, None if failed

        Example:
            >>> client = SoraClient()
            >>> path = client.download_video("https://example.com/video.mp4", "job-123")
            >>> print(path)  # "/path/to/uploads/job-123_video.mp4"
        """
        logger.info(f"📥 Downloading video for job: {job_id}")

        try:
            return self.video_downloader.download_video(video_url, job_id)
        except Exception as e:
            logger.error(f"❌ Failed to download video: {e}")
            return None

    def get_storage_info(self) -> Dict[str, Any]:
        """
        Get video storage statistics.

        Returns:
            Dict[str, Any]: Storage information

        Example:
            >>> client = SoraClient()
            >>> info = client.get_storage_info()
            >>> print(f"Total files: {info['total_files']}")
        """
        return self.video_downloader.get_storage_info()

    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old video files.

        Args:
            max_age_hours: Maximum age in hours before files are deleted

        Returns:
            int: Number of files cleaned up

        Example:
            >>> client = SoraClient()
            >>> cleaned = client.cleanup_old_files(24)
            >>> print(f"Cleaned {cleaned} files")
        """
        return self.video_downloader.cleanup_old_files(max_age_hours)

    def close(self) -> None:
        """Close client resources."""
        self.http_client.close()
        self.video_downloader.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
