"""
Video file download and storage management.

Handles secure video file downloads with validation,
error handling, and local storage management.
"""

import logging
import subprocess
import time
from pathlib import Path
from typing import Optional

import requests

logger = logging.getLogger(__name__)


class VideoDownloader:
    """
    Handles video file downloads from Azure OpenAI Sora API.

    Provides secure download functionality with validation,
    error handling, and local storage management.
    """

    def __init__(self, upload_directory: str = "uploads"):
        """
        Initialize video downloader.

        Args:
            upload_directory: Directory for storing downloaded videos
        """
        self.upload_directory = Path(upload_directory)
        self.upload_directory.mkdir(exist_ok=True)

        # Setup session for downloads
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": "SoraClient/1.0"})

    def download_video(
        self, video_url: str, job_id: str, api_key: Optional[str] = None
    ) -> Optional[str]:
        """
        Download video from URL and save to local storage.

        Args:
            video_url: URL of the video to download
            job_id: Job identifier for file naming

        Returns:
            Optional[str]: Local file path if successful, None if failed
        """
        if not video_url:
            logger.error("No video URL provided for download")
            return None

        # Generate local file path
        filename = f"{job_id}_video.mp4"
        local_path = self.upload_directory / filename

        logger.info(f"📥 Downloading video from: {video_url}")
        logger.info(f"📁 Saving to: {local_path}")

        try:
            # Set authentication headers if API key provided
            headers = {}
            if api_key:
                headers["api-key"] = api_key
                logger.info("🔐 Using API key authentication for video download")

            # Make download request with streaming
            response = self.session.get(
                video_url, headers=headers, stream=True, timeout=60
            )
            response.raise_for_status()

            # Validate content type
            content_type = response.headers.get("content-type", "")
            if not self._is_valid_video_content_type(content_type):
                logger.warning(f"Unexpected content type: {content_type}")

            # Get file size for progress logging
            content_length = response.headers.get("content-length")
            if content_length:
                file_size = int(content_length)
                logger.info(f"📊 File size: {file_size / 1024 / 1024:.2f} MB")

            # Download file in chunks
            downloaded_size = 0
            chunk_size = 8192  # 8KB chunks

            with open(local_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # Log progress every 1MB
                        if downloaded_size % (1024 * 1024) == 0:
                            mb_downloaded = downloaded_size / 1024 / 1024
                            logger.info(f"📥 Downloaded: {mb_downloaded:.1f} MB")

            # Verify file was created and has content
            if not local_path.exists():
                raise Exception("Downloaded file does not exist")

            file_size = local_path.stat().st_size
            if file_size == 0:
                raise Exception("Downloaded file is empty")

            logger.info(
                f"✅ Video downloaded successfully - Size: {file_size / 1024 / 1024:.2f} MB"
            )
            logger.info(f"📁 Saved to: {local_path.absolute()}")

            # Post-process video for browser compatibility
            processed_path = self._process_video_for_browser(local_path)
            if processed_path:
                return str(processed_path)
            else:
                # Fall back to original file if processing fails
                logger.warning("Video processing failed, using original file")
                return str(local_path.absolute())

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Download request failed: {e}")
            self._cleanup_failed_download(local_path)
            return None

        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            self._cleanup_failed_download(local_path)
            return None

    def _process_video_for_browser(self, input_path: Path) -> Optional[Path]:
        """
        Process video file for browser compatibility using FFmpeg.

        Moves moov atom to beginning of file and ensures proper codec/container format.

        Args:
            input_path: Path to original video file

        Returns:
            Optional[Path]: Path to processed video file, or None if processing failed
        """
        try:
            # Create processed filename - use WebM for browser compatibility
            output_path = input_path.with_suffix(".processed.webm")

            logger.info(
                f"🔧 Processing video for browser compatibility: {input_path.name}"
            )

            # FFmpeg command for WebM output (VP9 codec) - browser environment has no H.264 support
            # Browser codec test shows: VP8/VP9 "probably" supported, H.264 codecs return ""
            ffmpeg_cmd = [
                "ffmpeg",
                "-i",
                str(input_path),  # Input video file from Azure
                "-c:v",
                "libvpx-vp9",  # Use VP9 codec (browser supported)
                "-crf",
                "30",  # Good quality for VP9 (higher = smaller)
                "-b:v",
                "0",  # Use CRF mode (constant quality)
                "-deadline",
                "good",  # Good quality/speed balance
                "-cpu-used",
                "2",  # Encoding speed vs compression
                "-y",  # Overwrite output file
                str(output_path),  # Output WebM file
            ]

            logger.info(
                f"🔧 Running FFmpeg command: {' '.join(ffmpeg_cmd[0:4])} ... {ffmpeg_cmd[-1]}"
            )

            # Run FFmpeg with timeout
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                check=True,
            )

            # Verify processed file exists and has content
            if not output_path.exists():
                raise Exception("Processed file was not created")

            processed_size = output_path.stat().st_size
            if processed_size == 0:
                raise Exception("Processed file is empty")

            # Remove original file and rename processed file
            input_path.unlink()
            final_path = input_path  # Use original filename
            output_path.rename(final_path)

            logger.info(
                f"✅ Video processed successfully - Size: {processed_size / 1024 / 1024:.2f} MB"
            )
            logger.info(
                f"📁 Browser-compatible video saved to: {final_path.absolute()}"
            )

            return final_path

        except subprocess.TimeoutExpired:
            logger.error("❌ Video processing timed out")
            self._cleanup_failed_processing(output_path)
            return None

        except subprocess.CalledProcessError as e:
            logger.error(f"❌ FFmpeg processing failed: {e}")
            logger.error(f"FFmpeg stderr: {e.stderr}")
            self._cleanup_failed_processing(output_path)
            return None

        except FileNotFoundError:
            logger.error("❌ FFmpeg not found - video processing disabled")
            logger.warning(
                "Install FFmpeg to enable browser-compatible video processing"
            )
            return None

        except Exception as e:
            logger.error(f"❌ Video processing failed: {e}")
            self._cleanup_failed_processing(output_path)
            return None

    def _cleanup_failed_processing(self, file_path: Path) -> None:
        """
        Clean up failed video processing files.

        Args:
            file_path: Path to processed file that failed
        """
        try:
            if file_path and file_path.exists():
                file_path.unlink()
                logger.info(f"🧹 Cleaned up failed processing file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup processing file {file_path}: {e}")

    def _is_valid_video_content_type(self, content_type: str) -> bool:
        """
        Validate if content type is a valid video format.

        Args:
            content_type: HTTP content type header

        Returns:
            bool: True if valid video content type
        """
        valid_types = [
            "video/mp4",
            "video/mpeg",
            "video/quicktime",
            "video/x-msvideo",
            "application/octet-stream",  # Sometimes used for video files
        ]

        return any(valid_type in content_type.lower() for valid_type in valid_types)

    def _cleanup_failed_download(self, file_path: Path) -> None:
        """
        Clean up failed download files.

        Args:
            file_path: Path to file that failed to download
        """
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"🧹 Cleaned up failed download: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup file {file_path}: {e}")

    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old video files to manage disk space.

        Args:
            max_age_hours: Maximum age in hours before files are deleted

        Returns:
            int: Number of files cleaned up
        """
        if not self.upload_directory.exists():
            return 0

        cutoff_time = time.time() - (max_age_hours * 3600)
        cleaned_count = 0

        logger.info(f"🧹 Cleaning up files older than {max_age_hours} hours")

        for file_path in self.upload_directory.glob("*_video.mp4"):
            try:
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    cleaned_count += 1
                    logger.info(f"🧹 Cleaned up old file: {file_path.name}")
            except Exception as e:
                logger.warning(f"Failed to clean up file {file_path}: {e}")

        logger.info(f"🧹 Cleanup complete - Removed {cleaned_count} files")
        return cleaned_count

    def get_storage_info(self) -> dict:
        """
        Get information about video storage.

        Returns:
            dict: Storage statistics
        """
        if not self.upload_directory.exists():
            return {"total_files": 0, "total_size_mb": 0}

        total_files = 0
        total_size = 0

        for file_path in self.upload_directory.glob("*_video.mp4"):
            try:
                total_files += 1
                total_size += file_path.stat().st_size
            except Exception as e:
                logger.warning(f"Failed to get file info for {file_path}: {e}")

        return {
            "total_files": total_files,
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "directory": str(self.upload_directory.absolute()),
        }

    def close(self) -> None:
        """Close the download session."""
        self.session.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
