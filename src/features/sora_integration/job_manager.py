"""
Video generation job management for Azure OpenAI Sora API.

Handles job creation, status polling, and completion tracking
with proper error handling and retry logic.
"""

import json
import logging
import time
from datetime import datetime
from typing import Any, Dict

from src.core.models import GenerationParamsFactory, VideoJob
from src.features.sora_integration.video_downloader import VideoDownloader

from .http_client import SoraHttpClient

logger = logging.getLogger(__name__)


class VideoJobManager:
    """
    Manages video generation jobs for Azure OpenAI Sora API.

    Handles job creation, status polling, and result processing.
    """

    def __init__(
        self, http_client: SoraHttpClient, video_downloader: VideoDownloader = None
    ):
        """
        Initialize job manager with HTTP client and video downloader.

        Args:
            http_client: HTTP client for API communication
            video_downloader: Video downloader instance (creates default if None)
        """
        self.http_client = http_client
        self.video_downloader = video_downloader or VideoDownloader()

    def create_job(self, prompt: str, ui_parameters: Dict[str, Any]) -> VideoJob:
        """
        Create a new video generation job.

        Args:
            prompt: Text prompt for video generation
            ui_parameters: UI parameters (width, height, duration, model)

        Returns:
            VideoJob: Created job with generation ID

        Raises:
            Exception: If job creation fails
        """
        logger.info(f"🎬 Creating video job with prompt: '{prompt[:50]}...'")

        # Generate job ID
        job_id = f"sora-job-{int(time.time())}"

        # Create generation parameters
        params = GenerationParamsFactory.create_from_ui_request(
            prompt=prompt, ui_parameters=ui_parameters
        )

        # Prepare API request (Azure Sora uses v1/video/generations/jobs endpoint)
        endpoint = self.http_client.endpoint.rstrip("/")
        url = f"{endpoint}/openai/v1/video/generations/jobs"

        # Build request payload (Azure OpenAI format)
        payload = {
            "prompt": params.prompt,
            "width": params.width,
            "height": params.height,
        }

        # Add duration if specified (Azure uses n_seconds)
        if params.duration:
            payload["n_seconds"] = params.duration

        # Add model if specified
        if params.model:
            payload["model"] = params.model

        logger.info(f"🚀 Submitting job to Azure API: {json.dumps(payload, indent=2)}")

        try:
            # Make API request
            response = self.http_client.make_request(
                method="POST", url=url, data=payload
            )

            # Parse response
            response_data = response.json()

            # Handle Azure API response format
            # Azure Sora API returns direct job object, not wrapped in "data" array
            if "id" in response_data and "object" in response_data:
                generation_id = response_data.get("id", job_id)
                status = response_data.get("status", "pending")

                logger.info(
                    f"✅ Job created successfully - ID: {generation_id}, Status: {status}"
                )

                # Map Azure status to internal status
                internal_status = "pending" if status == "queued" else status

                return VideoJob(
                    id=job_id,
                    generation_id=generation_id,
                    prompt=params.prompt,
                    status=internal_status,
                    created_at=datetime.utcnow(),
                )
            else:
                raise Exception(f"Invalid Azure API response format: {response_data}")

        except Exception as e:
            logger.error(f"❌ Job creation failed: {e}")
            return VideoJob(
                id=job_id,
                generation_id=job_id,
                prompt=params.prompt,
                status="failed",
                error_message=str(e),
                created_at=datetime.utcnow(),
            )

    def poll_job_status(self, job_id: str, generation_id: str) -> VideoJob:
        """
        Poll job status from Azure API.

        Args:
            job_id: Local job identifier
            generation_id: Azure generation identifier

        Returns:
            VideoJob: Updated job with current status

        Raises:
            Exception: If status polling fails
        """
        logger.info(
            f"📊 Polling job status - Job: {job_id}, Generation: {generation_id}"
        )

        # Prepare API request (Azure Sora uses v1/video/generations/jobs endpoint)
        endpoint = self.http_client.endpoint.rstrip("/")
        url = f"{endpoint}/openai/v1/video/generations/jobs/{generation_id}"

        try:
            # Make API request
            response = self.http_client.make_request(method="GET", url=url)

            # Parse response
            response_data = response.json()

            # Process response based on Azure API format
            status = response_data.get("status", "unknown")

            if status == "succeeded":
                # Extract video URL from response
                video_url = self._extract_video_url(response_data)

                logger.info(f"✅ Job completed successfully - URL: {video_url}")

                # Download the video file to local storage
                logger.info(f"📥 Downloading video file for job {job_id}")
                file_path = self.video_downloader.download_video(
                    video_url, job_id, self.http_client.api_key
                )

                if not file_path:
                    logger.error(f"❌ Failed to download video for job {job_id}")
                    return VideoJob(
                        id=job_id,
                        generation_id=generation_id,
                        prompt="polling_status",
                        status="failed",
                        error_message="Failed to download video file",
                        created_at=datetime.utcnow(),
                    )

                logger.info(f"✅ Video downloaded successfully - Path: {file_path}")

                return VideoJob(
                    id=job_id,
                    generation_id=generation_id,
                    prompt="polling_status",  # Default prompt for polling
                    status="succeeded",
                    download_url=video_url,
                    file_path=file_path,
                    completed_at=datetime.utcnow(),
                    created_at=datetime.utcnow(),  # Add required field
                )

            elif status == "failed":
                error_message = response_data.get("error", {}).get(
                    "message", "Unknown error"
                )

                logger.error(f"❌ Job failed - Error: {error_message}")

                return VideoJob(
                    id=job_id,
                    generation_id=generation_id,
                    prompt="polling_status",  # Default prompt for polling
                    status="failed",
                    error_message=error_message,
                    created_at=datetime.utcnow(),  # Add required field
                )

            elif status == "unknown":
                # Invalid response format - treat as error
                logger.error(
                    f"❌ Invalid response format - no status field: {response_data}"
                )

                return VideoJob(
                    id=job_id,
                    generation_id=generation_id,
                    prompt="polling_status",  # Default prompt for polling
                    status="failed",
                    error_message="Status polling failed: Invalid response format",
                    created_at=datetime.utcnow(),  # Add required field
                )

            else:
                # Job is still running
                logger.info(f"⏳ Job still running - Status: {status}")

                return VideoJob(
                    id=job_id,
                    generation_id=generation_id,
                    prompt="polling_status",  # Default prompt for polling
                    status="running",
                    created_at=datetime.utcnow(),  # Add required field
                )

        except Exception as e:
            logger.error(f"❌ Status polling failed: {e}")
            return VideoJob(
                id=job_id,
                generation_id=generation_id,
                prompt="polling_status",  # Default prompt for polling
                status="failed",
                error_message=f"Status polling failed: {str(e)}",
                created_at=datetime.utcnow(),  # Add required field
            )

    def _extract_video_url(self, response_data: Dict[str, Any]) -> str:
        """
        Extract video URL from Azure API response.

        Args:
            response_data: Azure API response data

        Returns:
            str: Video download URL

        Raises:
            Exception: If URL extraction fails
        """
        try:
            # Azure Sora API response format with generations array
            if "generations" in response_data and len(response_data["generations"]) > 0:
                generation = response_data["generations"][0]
                generation_id = generation.get("id")

                if generation_id:
                    # Build video download URL according to Azure Sora API documentation
                    # Must include api-version query parameter for downloads
                    endpoint = self.http_client.endpoint.rstrip("/")
                    api_version = self.http_client.api_version
                    video_url = f"{endpoint}/openai/v1/video/generations/{generation_id}/content/video?api-version={api_version}"

                    logger.info(f"🔗 Generated video download URL: {video_url}")

                    return video_url

            # Fallback to legacy response formats
            elif "data" in response_data and len(response_data["data"]) > 0:
                return response_data["data"][0].get("url", "")
            elif "url" in response_data:
                return response_data["url"]
            elif "result" in response_data and "url" in response_data["result"]:
                return response_data["result"]["url"]
            else:
                raise Exception(f"No video URL found in response: {response_data}")

        except Exception as e:
            logger.error(f"Failed to extract video URL: {e}")
            raise Exception(f"Invalid response format: {response_data}")
