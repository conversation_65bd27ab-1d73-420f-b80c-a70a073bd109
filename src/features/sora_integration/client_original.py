"""Azure OpenAI Sora API client."""

import json
import logging
import time
import uuid
from datetime import datetime
from typing import Optional

import requests
from azure.identity import DefaultAzureCredential

from src.core.models import GenerationParamsFactory, VideoJob
from src.rate_limiting.limiter import (
    GlobalRateLimiter,
    RateLimitExceeded,
    create_rate_limiter,
)


class SoraClient:
    """
    Azure OpenAI Sora API client with authentication handling.

    Handles video generation job creation and status polling with
    proper error handling and exponential backoff.
    """

    def __init__(self) -> None:
        """
        Initialize client with Azure credentials and rate limiting.

        Raises:
            ValueError: If required environment variables are missing
        """
        from src.config.factory import ConfigurationFactory

        # Get Azure configuration from factory
        azure_config = ConfigurationFactory.get_azure_config()
        self.endpoint = azure_config["endpoint"]
        self.api_version = azure_config["api_version"]
        self.deployment = azure_config["deployment_name"]
        self.api_key = azure_config["api_key"]

        # Setup logging first so we can use it for debugging
        self.logger = logging.getLogger(__name__)

        # DEBUG: Log complete Azure configuration for debugging
        self.logger.info(
            f"🔧 SoraClient.__init__ - Complete Azure config: {azure_config}"
        )
        self.logger.info(
            f"🔧 SoraClient.__init__ - API Version set to: '{self.api_version}'"
        )
        self.logger.info(f"🔧 SoraClient.__init__ - Endpoint: '{self.endpoint}'")

        if not self.endpoint:
            raise ValueError("AZURE_OPENAI_ENDPOINT environment variable required")

        # Remove trailing slash if present
        self.endpoint = self.endpoint.rstrip("/")

        # Construct base URL for video generation
        # Note: Azure OpenAI Sora is in preview and may have different endpoint structure
        self.base_url = f"{self.endpoint}/openai/v1/video/generations"
        self.logger.info(
            f"🔧 SoraClient.__init__ - Base URL constructed: '{self.base_url}'"
        )

        # Rate limiting: max 10 requests per second as per Azure docs
        self._last_request_time = 0
        self._min_request_interval = 0.1  # 100ms between requests (fallback)

        # Initialize distributed rate limiter
        try:
            redis_url = azure_config.get("redis_url", "redis://localhost:6379/0")
            self._distributed_limiter: Optional[GlobalRateLimiter] = (
                create_rate_limiter(redis_url)
            )
            self.logger.info("Distributed rate limiter initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize distributed rate limiter: {e}")
            self.logger.info("Falling back to local rate limiting")
            self._distributed_limiter = None

        # Use API key if available, otherwise DefaultAzureCredential
        if self.api_key:
            self.headers = {"api-key": self.api_key, "Content-Type": "application/json"}
        else:
            credential = DefaultAzureCredential()
            token = credential.get_token("https://cognitiveservices.azure.com/.default")
            self.headers = {
                "Authorization": f"Bearer {token.token}",
                "Content-Type": "application/json",
            }

    def _sanitize_headers(self, headers: dict[str, str]) -> dict[str, str]:
        """
        Sanitize headers for secure logging by redacting sensitive information.

        Args:
            headers: Dictionary of HTTP headers

        Returns:
            dict: Headers with sensitive values redacted
        """
        sensitive_keys = {"api-key", "authorization", "x-api-key", "bearer"}
        return {
            k: "***REDACTED***" if k.lower() in sensitive_keys else v
            for k, v in headers.items()
        }

    def _rate_limit(self) -> None:
        """
        Enforce rate limiting to comply with Azure API limits.

        Uses distributed rate limiting via Redis if available, otherwise falls back
        to local rate limiting. Ensures compliance with Azure's 10 req/sec limit.
        """
        if self._distributed_limiter:
            # Use distributed rate limiting
            try:
                if not self._distributed_limiter.acquire(timeout=30):
                    self.logger.warning(
                        "Distributed rate limit timeout - proceeding with caution"
                    )
                    # Fall back to local rate limiting as safety measure
                    self._local_rate_limit()
                else:
                    self.logger.debug("Distributed rate limit token acquired")
            except RateLimitExceeded as e:
                self.logger.warning(f"Distributed rate limit exceeded: {e}")
                # Sleep for the recommended retry time
                time.sleep(min(e.retry_after, 5.0))  # Cap at 5 seconds
            except Exception as e:
                self.logger.error(f"Distributed rate limiter error: {e}")
                # Fall back to local rate limiting
                self._local_rate_limit()
        else:
            # Use local rate limiting
            self._local_rate_limit()

    def _local_rate_limit(self) -> None:
        """
        Local rate limiting fallback implementation.

        Ensures minimum interval between requests for this client instance.
        """
        current_time = time.time()
        time_since_last = current_time - self._last_request_time

        if time_since_last < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last
            self.logger.debug(f"Local rate limit: sleeping for {sleep_time:.3f}s")
            time.sleep(sleep_time)

        self._last_request_time = time.time()

    def _make_request(
        self, method: str, url: str, request_id: str = "unknown", **kwargs
    ) -> requests.Response:
        """
        Make HTTP request with rate limiting and error handling.

        Args:
            method (str): HTTP method (GET, POST, etc.)
            url (str): Request URL
            request_id (str): Unique request identifier for tracking
            **kwargs: Additional arguments for requests

        Returns:
            requests.Response: HTTP response

        Raises:
            ConnectionError: If request fails after retries
        """
        self._rate_limit()

        # Add API version parameter for Azure OpenAI preview APIs
        params = kwargs.get("params", {})
        params["api-version"] = self.api_version
        kwargs["params"] = params

        # DEBUG: Log complete HTTP request details with request ID
        full_url = f"{url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
        payload = kwargs.get("json", {})

        self.logger.info(f"🌐 HTTP REQUEST [{request_id}] - Method: {method}")
        self.logger.info(f"🌐 HTTP REQUEST [{request_id}] - URL: {url}")
        self.logger.info(
            f"🌐 HTTP REQUEST [{request_id}] - API Version: '{self.api_version}'"
        )
        self.logger.info(f"🌐 HTTP REQUEST [{request_id}] - Full URL: {full_url}")
        self.logger.info(
            f"🌐 HTTP REQUEST [{request_id}] - Headers: {self._sanitize_headers(dict(self.headers))}"
        )
        self.logger.info(f"🌐 HTTP REQUEST [{request_id}] - Payload: {payload}")

        try:
            response = requests.request(method, url, headers=self.headers, **kwargs)
            self.logger.info(
                f"🌐 HTTP RESPONSE [{request_id}] - Status: {response.status_code}"
            )
            self.logger.info(
                f"🌐 HTTP RESPONSE [{request_id}] - Headers: {dict(response.headers)}"
            )

            # Always log response body for detailed analysis
            try:
                response_json = response.json()
                self.logger.info(
                    f"🌐 HTTP RESPONSE [{request_id}] - JSON: {response_json}"
                )
            except (
                ValueError,
                json.JSONDecodeError,
                UnicodeDecodeError,
            ) as parse_error:
                self.logger.info(
                    f"🌐 HTTP RESPONSE [{request_id}] - Failed to parse JSON ({parse_error})"
                )
                self.logger.info(
                    f"🌐 HTTP RESPONSE [{request_id}] - Body: {response.text[:1000]}..."
                )

            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            # Enhanced error logging with full response details
            self.logger.error(f"🌐 HTTP REQUEST FAILED [{request_id}] - Error: {e}")
            self.logger.error(
                f"🌐 HTTP REQUEST FAILED [{request_id}] - API Version: '{self.api_version}'"
            )

            # Log full error response for Azure API debugging
            if hasattr(e, "response") and e.response is not None:
                self.logger.error(
                    f"🌐 HTTP REQUEST FAILED [{request_id}] - Response Status: {e.response.status_code}"
                )
                self.logger.error(
                    f"🌐 HTTP REQUEST FAILED [{request_id}] - Response Headers: {self._sanitize_headers(dict(e.response.headers))}"
                )
                try:
                    error_json = e.response.json()
                    self.logger.error(
                        f"🌐 HTTP REQUEST FAILED [{request_id}] - Azure Error JSON: {error_json}"
                    )
                except (
                    ValueError,
                    json.JSONDecodeError,
                    UnicodeDecodeError,
                ) as parse_error:
                    self.logger.error(
                        f"🌐 HTTP REQUEST FAILED [{request_id}] - Failed to parse Azure error JSON ({parse_error})"
                    )
                    self.logger.error(
                        f"🌐 HTTP REQUEST FAILED [{request_id}] - Azure Error Body: {e.response.text[:1000]}"
                    )

            raise ConnectionError(f"Azure API request failed: {e}") from e

    def create_video_job(
        self, prompt: str, ui_parameters: Optional[dict] = None
    ) -> VideoJob:
        """
        Create video generation job with Azure Sora API.

        Args:
            prompt (str): Text prompt for video generation
            ui_parameters (Optional[dict]): Optional UI parameter overrides for Phase 2
                                          Format: {'width': int, 'height': int, 'duration': int, 'model': str}

        Returns:
            VideoJob: Created video job with initial status

        Raises:
            ValueError: If prompt is invalid
            ConnectionError: If API request fails

        Example:
            >>> client = SoraClient()
            >>> job = client.create_video_job("A cat playing piano")
            >>> print(job.status)  # "pending"

            >>> # With custom parameters
            >>> ui_params = {'width': 1920, 'height': 1080, 'duration': 10}
            >>> job = client.create_video_job("Ocean waves", ui_params)
        """
        # Generate unique request ID for tracking
        import uuid as uuid_module

        request_id = str(uuid_module.uuid4())[:8]

        # DEBUG: Log request type and parameters
        request_type = (
            "STANDARD (no UI params)"
            if not ui_parameters
            else "DROPDOWN (with UI params)"
        )
        self.logger.info(f"🎯 REQUEST [{request_id}] - Type: {request_type}")
        self.logger.info(f"🎯 REQUEST [{request_id}] - Prompt: '{prompt[:50]}...'")
        self.logger.info(f"🎯 REQUEST [{request_id}] - UI Parameters: {ui_parameters}")

        # Use factory pattern for parameter creation
        params = GenerationParamsFactory.create_from_ui_request(prompt, ui_parameters)
        self.logger.info(
            f"🎯 REQUEST [{request_id}] - Final Params: {params.model_dump()}"
        )

        job_id = str(uuid.uuid4())

        # Prepare request payload for Azure Sora API
        payload = {
            "prompt": params.prompt,
            "width": params.width,
            "height": params.height,
            "n_seconds": params.duration,
            "model": params.model,
        }

        try:
            # Make request to Azure Sora API
            jobs_url = f"{self.base_url}/jobs"
            self.logger.info(
                f"🎯 REQUEST [{request_id}] - Creating video job at: {jobs_url}"
            )
            self.logger.info(f"🎯 REQUEST [{request_id}] - Payload: {payload}")

            response = self._make_request(
                "POST", jobs_url, request_id=request_id, json=payload
            )
            response_data = response.json()

            # Extract generation ID from Azure response
            generation_id = response_data.get("id")
            if not generation_id:
                raise ValueError("No generation ID returned from Azure API")

            self.logger.info(
                f"🎯 REQUEST [{request_id}] - SUCCESS: Video job created: {generation_id}"
            )

            return VideoJob(
                id=job_id,
                prompt=params.prompt,
                status="pending",
                created_at=datetime.now(),
                generation_id=generation_id,
            )

        except Exception as e:
            self.logger.error(f"🎯 REQUEST [{request_id}] - FAILED: {e}")
            # Return failed job for graceful error handling
            return VideoJob(
                id=job_id,
                prompt=params.prompt,
                status="failed",
                created_at=datetime.now(),
                error_message=str(e),
            )

    def poll_job_status(self, job_id: str, generation_id: str) -> VideoJob:
        """
        Poll job status with exponential backoff.

        Args:
            job_id (str): Unique job identifier
            generation_id (str): Azure API generation identifier

        Returns:
            VideoJob: Updated job with current status

        Raises:
            ConnectionError: If API request fails
        """
        try:
            # Poll Azure API for job status
            status_url = f"{self.base_url}/jobs/{generation_id}"
            self.logger.debug(
                f"Polling status for job {job_id} (generation {generation_id})"
            )

            response = self._make_request("GET", status_url)
            response_data = response.json()

            # Map Azure status to our status
            azure_status = response_data.get("status", "unknown")
            status_mapping = {
                "queued": "pending",
                "preprocessing": "running",
                "running": "running",
                "processing": "running",
                "succeeded": "succeeded",
                "failed": "failed",
            }

            our_status = status_mapping.get(azure_status, "failed")

            # Create updated job
            job_data = {
                "id": job_id,
                "prompt": response_data.get("prompt", ""),
                "status": our_status,
                "created_at": datetime.now(),  # Would be preserved from original in real implementation
                "generation_id": generation_id,
            }

            # Add completion data if job is done
            if our_status == "succeeded":
                job_data["completed_at"] = datetime.now()

                # Get video content if available (updated to match Azure API response format)
                generations = response_data.get("generations", [])
                if generations:
                    generation_id = generations[0].get("id")
                    if generation_id:
                        # Download the video file using correct Azure endpoint structure
                        import os

                        # Use absolute path to ensure file can be found from any working directory
                        project_root = os.path.dirname(
                            os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                        )
                        uploads_dir = os.path.join(project_root, "uploads")
                        output_path = os.path.join(
                            uploads_dir, f"video_{job_id[:8]}.mp4"
                        )
                        os.makedirs(uploads_dir, exist_ok=True)

                        # Construct video download URL as per Azure docs
                        video_url = f"{self.endpoint}/openai/v1/video/generations/{generation_id}/content/video"

                        if self.download_video(video_url, output_path):
                            job_data["file_path"] = output_path
                            job_data["download_url"] = f"/video/{job_id}"
                        else:
                            self.logger.error(
                                f"Failed to download video for job {job_id}"
                            )
                            job_data["error_message"] = (
                                "Failed to download generated video"
                            )
                    else:
                        self.logger.error(
                            f"No generation ID found for completed job {job_id}"
                        )
                else:
                    self.logger.error(
                        f"No generations found for completed job {job_id}"
                    )

            elif our_status == "failed":
                job_data["completed_at"] = datetime.now()
                error_detail = response_data.get("error", {})
                if isinstance(error_detail, dict):
                    job_data["error_message"] = error_detail.get(
                        "message", "Unknown error"
                    )
                else:
                    job_data["error_message"] = str(error_detail)

            self.logger.debug(f"Job {job_id} status: {our_status}")
            return VideoJob(**job_data)

        except Exception as e:
            self.logger.error(f"Failed to poll job status for {job_id}: {e}")
            # Return failed job for graceful error handling
            return VideoJob(
                id=job_id,
                prompt="",
                status="failed",
                created_at=datetime.now(),
                completed_at=datetime.now(),
                generation_id=generation_id,
                error_message=str(e),
            )

    def download_video(self, video_url: str, output_path: str) -> bool:
        """
        Download video from Azure Sora API.

        Args:
            video_url (str): URL of the generated video
            output_path (str): Local path to save the video

        Returns:
            bool: True if download successful, False otherwise
        """
        try:
            self.logger.info(f"Downloading video from {video_url}")

            response = self._make_request("GET", video_url)

            with open(output_path, "wb") as f:
                f.write(response.content)

            self.logger.info(f"Video downloaded successfully to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to download video: {e}")
            return False
