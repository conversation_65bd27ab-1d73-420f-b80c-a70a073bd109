"""Tests for SoraHttpClient - Azure OpenAI HTTP communication."""

import json
import uuid
from unittest.mock import Mock, patch
from urllib.parse import urljoin

import pytest
import requests

from src.features.sora_integration.http_client import SoraHttpClient
from src.rate_limiting.limiter import RateLimitExceeded


@pytest.mark.unit
class TestSoraHttpClientInitialization:
    """Test SoraHttpClient initialization and configuration."""

    def test_initialization_with_valid_config(self):
        """Test HTTP client initialization with valid configuration."""
        endpoint = "https://test.openai.azure.com/"
        api_key = "test-key"
        api_version = "2024-02-15-preview"
        deployment = "sora"

        client = SoraHttpClient(endpoint, api_key, api_version, deployment)

        assert client.endpoint == endpoint
        assert client.api_key == api_key
        assert client.api_version == api_version
        assert client.deployment == deployment
        assert client.session is not None
        assert client.session.headers["Content-Type"] == "application/json"
        assert client.session.headers["api-key"] == api_key

    def test_initialization_sets_up_rate_limiter(self):
        """Test that initialization sets up rate limiter."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )

        # Rate limiter should be set up (may be None if Redis not available)
        assert hasattr(client, 'rate_limiter')

    def test_initialization_strips_trailing_slash(self):
        """Test that endpoint trailing slashes are handled consistently."""
        endpoint_with_slash = "https://test.openai.azure.com/"
        endpoint_without_slash = "https://test.openai.azure.com"

        client1 = SoraHttpClient(endpoint_with_slash, "key", "version", "deploy")
        client2 = SoraHttpClient(endpoint_without_slash, "key", "version", "deploy")

        # Both should work consistently
        assert client1.endpoint == endpoint_with_slash
        assert client2.endpoint == endpoint_without_slash


@pytest.mark.unit
class TestSoraHttpClientHeaders:
    """Test HTTP client header management."""

    def test_session_headers_setup(self):
        """Test that session headers are properly set up."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )

        expected_headers = {
            "Content-Type": "application/json",
            "api-key": "test-key"
        }

        for key, value in expected_headers.items():
            assert client.session.headers[key] == value

    def test_session_headers_with_different_key(self):
        """Test session headers with different API key."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "different-key",
            "2024-02-15-preview",
            "sora"
        )

        assert client.session.headers["Content-Type"] == "application/json"
        assert client.session.headers["api-key"] == "different-key"

    def test_sanitize_headers_redacts_sensitive_data(self):
        """Test that sensitive headers are redacted in sanitization."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )

        headers = {
            "Content-Type": "application/json",
            "api-key": "secret-key",
            "Authorization": "Bearer token",
            "X-API-Key": "another-secret",
            "Custom-Header": "safe-value"
        }

        sanitized = client._sanitize_headers(headers)

        assert sanitized["Content-Type"] == "application/json"
        assert sanitized["api-key"] == "***REDACTED***"
        assert sanitized["Authorization"] == "***REDACTED***"
        assert sanitized["X-API-Key"] == "***REDACTED***"
        assert sanitized["Custom-Header"] == "safe-value"

    def test_sanitize_headers_case_insensitive(self):
        """Test that header sanitization is case insensitive."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )

        headers = {
            "API-KEY": "secret",
            "authorization": "bearer token",
            "X-Api-Key": "another-secret"
        }

        sanitized = client._sanitize_headers(headers)

        assert sanitized["API-KEY"] == "***REDACTED***"
        assert sanitized["authorization"] == "***REDACTED***"
        assert sanitized["X-Api-Key"] == "***REDACTED***"


@pytest.mark.integration
class TestSoraHttpClientRateLimiting:
    """Test HTTP client rate limiting functionality."""

    @patch("src.features.sora_integration.http_client.create_rate_limiter")
    def test_rate_limiter_setup(self, mock_create_limiter):
        """Test rate limiter setup during initialization."""
        mock_limiter = Mock()
        mock_create_limiter.return_value = mock_limiter

        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )

        mock_create_limiter.assert_called_once()
        assert client.rate_limiter == mock_limiter

    def test_apply_rate_limiting_no_limiter(self):
        """Test rate limiting when no limiter is available."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Should not raise exception
        client._apply_rate_limiting()

    def test_apply_rate_limiting_allowed(self):
        """Test rate limiting when request is allowed."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        
        mock_limiter = Mock()
        mock_limiter.is_allowed.return_value = True
        client.rate_limiter = mock_limiter

        # Should not raise exception
        client._apply_rate_limiting()

        mock_limiter.is_allowed.assert_called_once_with("sora_api:sora", 10, 1)

    def test_apply_rate_limiting_exceeded(self):
        """Test rate limiting when limit is exceeded."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        
        mock_limiter = Mock()
        mock_limiter.is_allowed.return_value = False
        client.rate_limiter = mock_limiter

        # The implementation catches RateLimitExceeded and logs it
        # So we need to test that the exception is raised internally
        with patch.object(client.rate_limiter, 'is_allowed', return_value=False):
            # Should not raise exception due to error handling
            client._apply_rate_limiting()
            
            # Verify is_allowed was called
            mock_limiter.is_allowed.assert_called_once_with("sora_api:sora", 10, 1)

    def test_apply_rate_limiting_error_handling(self):
        """Test rate limiting error handling."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        
        mock_limiter = Mock()
        mock_limiter.is_allowed.side_effect = Exception("Rate limiter error")
        client.rate_limiter = mock_limiter

        # Should not raise exception - should continue without rate limiting
        client._apply_rate_limiting()


@pytest.mark.integration
class TestSoraHttpClientRequests:
    """Test HTTP client request functionality."""

    @patch('requests.Session.request')
    def test_make_request_success(self, mock_request):
        """Test successful HTTP request."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None  # Disable rate limiting for test

        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_response.headers = {"content-type": "application/json"}
        mock_response.text = '{"success": true}'  # Add string response for logging
        mock_request.return_value = mock_response

        url = "https://test.openai.azure.com/openai/deployments/sora/generations"
        response = client.make_request("POST", url, data={"prompt": "test"})

        assert response == mock_response
        mock_request.assert_called_once()

    @patch('requests.Session.request')
    def test_make_request_with_retry_on_failure(self, mock_request):
        """Test request retry logic on failure."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Mock first two requests to fail, third to succeed
        mock_success_response = Mock()
        mock_success_response.status_code = 200
        mock_success_response.headers = {"content-type": "application/json"}
        mock_success_response.text = '{"success": true}'
        
        mock_request.side_effect = [
            requests.exceptions.ConnectionError("Connection failed"),
            requests.exceptions.Timeout("Request timeout"),
            mock_success_response
        ]

        url = "https://test.openai.azure.com/test"
        response = client.make_request("GET", url, max_retries=3)

        assert mock_request.call_count == 3
        assert response.status_code == 200

    @patch('requests.Session.request')
    def test_make_request_max_retries_exceeded(self, mock_request):
        """Test request failure after max retries."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Mock all requests to fail
        mock_request.side_effect = requests.exceptions.ConnectionError("Connection failed")

        url = "https://test.openai.azure.com/test"
        
        with pytest.raises(requests.exceptions.ConnectionError):
            client.make_request("GET", url, max_retries=2)

        assert mock_request.call_count == 3  # Initial + 2 retries

    @patch('requests.Session.request')
    def test_make_request_with_rate_limiting(self, mock_request):
        """Test request with rate limiting applied."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        
        # Mock rate limiter
        mock_limiter = Mock()
        mock_limiter.is_allowed.return_value = True
        client.rate_limiter = mock_limiter

        # Mock successful response
        mock_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_response.text = '{"success": true}'
        mock_request.return_value = mock_response

        url = "https://test.openai.azure.com/test"
        response = client.make_request("GET", url)

        # Rate limiting should be applied
        mock_limiter.is_allowed.assert_called_once()
        assert response == mock_response

    @patch('requests.Session.request')
    def test_make_request_rate_limit_exceeded(self, mock_request):
        """Test request when rate limit is exceeded."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        
        # Mock rate limiter to reject request
        mock_limiter = Mock()
        mock_limiter.is_allowed.return_value = False
        client.rate_limiter = mock_limiter

        # Mock successful response (rate limiting logs warning but continues)
        mock_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_response.text = '{"success": true}'
        mock_request.return_value = mock_response

        url = "https://test.openai.azure.com/test"
        
        # Should not raise exception due to error handling in _apply_rate_limiting
        response = client.make_request("GET", url)
        
        # Request should still be made because rate limiting failure is handled
        mock_request.assert_called_once()
        assert response == mock_response

    @patch('requests.Session.request')
    @patch('time.sleep')
    def test_make_request_retry_with_backoff(self, mock_sleep, mock_request):
        """Test request retry with exponential backoff."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Mock first request to fail, second to succeed
        mock_success_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_success_response.text = '{"success": true}'
        mock_request.side_effect = [
            requests.exceptions.ConnectionError("Connection failed"),
            mock_success_response
        ]

        url = "https://test.openai.azure.com/test"
        client.make_request("GET", url, max_retries=2)

        # Should sleep before retry (exponential backoff: 2^attempt)
        mock_sleep.assert_called_with(2)  # 2^1 = 2 seconds


@pytest.mark.unit
class TestSoraHttpClientDataHandling:
    """Test HTTP client data handling and serialization."""

    @patch('requests.Session.request')
    def test_make_request_with_json_data(self, mock_request):
        """Test request with JSON data serialization."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        mock_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_response.text = '{"success": true}'
        mock_request.return_value = mock_response

        data = {"prompt": "test video", "n_seconds": 5}
        url = "https://test.openai.azure.com/test"
        
        client.make_request("POST", url, data=data)

        # Verify request was called with JSON data
        call_args = mock_request.call_args
        assert call_args[1]['data'] == json.dumps(data)
        # Note: headers is passed as None since session headers are used instead

    @patch('requests.Session.request')
    def test_make_request_with_string_data(self, mock_request):
        """Test request with string data."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        mock_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_response.text = '{"success": true}'
        mock_request.return_value = mock_response

        data = '{"prompt": "test"}'
        url = "https://test.openai.azure.com/test"
        
        client.make_request("POST", url, data=data)

        # Verify request was called with string data as-is
        call_args = mock_request.call_args
        assert call_args[1]['data'] == data

    @patch('requests.Session.request')
    def test_make_request_timeout_configuration(self, mock_request):
        """Test request timeout configuration."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        mock_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_response.text = '{"success": true}'
        mock_request.return_value = mock_response

        url = "https://test.openai.azure.com/test"
        client.make_request("GET", url, timeout=60)

        # Verify timeout was passed to request
        call_args = mock_request.call_args
        assert call_args[1]['timeout'] == 60


@pytest.mark.unit
class TestSoraHttpClientErrorHandling:
    """Test HTTP client error handling scenarios."""

    @patch('requests.Session.request')
    def test_make_request_http_error_handling(self, mock_request):
        """Test handling of HTTP errors."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Mock HTTP error response
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.headers = {"content-type": "application/json"}
        mock_response.text = "Not Found"
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
        mock_request.return_value = mock_response

        url = "https://test.openai.azure.com/test"
        
        with pytest.raises(requests.exceptions.HTTPError):
            client.make_request("GET", url, max_retries=0)

    @patch('requests.Session.request')
    def test_make_request_connection_error_retry(self, mock_request):
        """Test retry on connection errors."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Mock connection error then success
        mock_success_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_success_response.text = '{"success": true}'
        mock_request.side_effect = [
            requests.exceptions.ConnectionError("Connection failed"),
            mock_success_response
        ]

        url = "https://test.openai.azure.com/test"
        response = client.make_request("GET", url, max_retries=1)

        assert mock_request.call_count == 2
        assert response.status_code == 200

    @patch('requests.Session.request')
    def test_make_request_timeout_error_retry(self, mock_request):
        """Test retry on timeout errors."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )
        client.rate_limiter = None

        # Mock timeout error then success
        mock_success_response = Mock(status_code=200, headers={"content-type": "application/json"})
        mock_success_response.text = '{"success": true}'
        mock_request.side_effect = [
            requests.exceptions.Timeout("Request timeout"),
            mock_success_response
        ]

        url = "https://test.openai.azure.com/test"
        response = client.make_request("GET", url, max_retries=1)

        assert mock_request.call_count == 2
        assert response.status_code == 200


@pytest.mark.unit
@pytest.mark.integration
class TestSoraHttpClientIntegration:
    """Integration tests for HTTP client functionality."""

    def test_client_configuration_consistency(self):
        """Test that client configuration is consistently applied."""
        endpoint = "https://test.openai.azure.com/"
        api_key = "test-key-123"
        api_version = "2024-02-15-preview"
        deployment = "sora-deployment"

        client = SoraHttpClient(endpoint, api_key, api_version, deployment)

        # Test session headers use configuration
        assert client.session.headers["api-key"] == api_key

        # Test rate limiting key uses deployment
        expected_rate_key = f"sora_api:{deployment}"
        # We can't easily test this without mocking, but it's covered in other tests

    def test_error_handling_flow(self):
        """Test complete error handling flow."""
        client = SoraHttpClient(
            "https://test.openai.azure.com/",
            "test-key",
            "2024-02-15-preview",
            "sora"
        )

        # Test various error scenarios are properly defined
        assert hasattr(client, '_apply_rate_limiting')
        assert hasattr(client, 'make_request')
        assert hasattr(client, '_sanitize_headers')