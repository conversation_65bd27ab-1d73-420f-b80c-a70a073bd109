"""Tests for utility functions."""

import os
import tempfile
from unittest.mock import MagicMock, patch

import pytest

from src.features.sora_integration.utils import (
    cleanup_file,
    ensure_upload_directory,
    get_file_size,
    get_secure_filename,
    validate_video_file,
)


@pytest.mark.unit
class TestUtilityFunctions:
    """Test utility function functionality."""

    def test_get_secure_filename_valid(self):
        """Test secure filename generation with valid input."""
        filename = get_secure_filename("test_video.mp4")
        assert filename == "test_video.mp4"

    def test_get_secure_filename_dangerous(self):
        """Test secure filename generation with dangerous input."""
        dangerous_filename = "../../../etc/passwd"
        filename = get_secure_filename(dangerous_filename)

        assert filename != dangerous_filename
        assert ".." not in filename
        assert "/" not in filename

    def test_validate_video_file_valid(self):
        """Test video file validation with valid file."""
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as temp_file:
            temp_file.write(b"test content")
            temp_path = temp_file.name

        try:
            assert validate_video_file(temp_path) is True
        finally:
            os.unlink(temp_path)

    def test_validate_video_file_invalid_extension(self):
        """Test video file validation with invalid extension."""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as temp_file:
            temp_file.write(b"test content")
            temp_path = temp_file.name

        try:
            assert validate_video_file(temp_path) is False
        finally:
            os.unlink(temp_path)

    def test_validate_video_file_nonexistent(self):
        """Test video file validation with non-existent file."""
        assert validate_video_file("/nonexistent/path.mp4") is False

    def test_validate_video_file_empty_path(self):
        """Test video file validation with empty path."""
        assert validate_video_file("") is False
        assert validate_video_file(None) is False

    def test_get_file_size_valid(self):
        """Test getting file size for valid file."""
        test_content = b"test content for size measurement"
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_path = temp_file.name

        try:
            size = get_file_size(temp_path)
            assert size == len(test_content)
        finally:
            os.unlink(temp_path)

    def test_get_file_size_nonexistent(self):
        """Test getting file size for non-existent file."""
        size = get_file_size("/nonexistent/path.txt")
        assert size is None

    def test_ensure_upload_directory_new(self):
        """Test creating new upload directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            upload_path = os.path.join(temp_dir, "uploads", "videos")

            ensure_upload_directory(upload_path)

            assert os.path.exists(upload_path)
            assert os.path.isdir(upload_path)

    def test_ensure_upload_directory_existing(self):
        """Test ensuring existing upload directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            upload_path = os.path.join(temp_dir, "uploads")
            os.makedirs(upload_path)

            # Should not raise error
            ensure_upload_directory(upload_path)

            assert os.path.exists(upload_path)

    @patch("threading.Timer")
    def test_cleanup_file(self, mock_timer):
        """Test file cleanup scheduling."""
        mock_timer_instance = MagicMock()
        mock_timer.return_value = mock_timer_instance

        cleanup_file("/test/path.mp4", delay=60)

        mock_timer.assert_called_once_with(60, mock_timer.call_args[0][1])
        mock_timer_instance.start.assert_called_once()
        assert mock_timer_instance.daemon is True

    def test_cleanup_file_default_delay(self):
        """Test file cleanup with default delay."""
        with patch("threading.Timer") as mock_timer:
            mock_timer_instance = MagicMock()
            mock_timer.return_value = mock_timer_instance

            cleanup_file("/test/path.mp4")

            # Should use default delay of 3600 seconds
            mock_timer.assert_called_once_with(3600, mock_timer.call_args[0][1])
