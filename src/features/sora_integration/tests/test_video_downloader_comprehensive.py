"""Comprehensive unit tests for video downloader functionality.

Tests video download, validation, error handling, format conversion,
and file system operations for the Sora integration module.
"""

import os
import tempfile
import uuid
from pathlib import Path
from unittest.mock import Mock, mock_open, patch

import requests

from src.features.sora_integration.video_downloader import VideoDownloader


class TestVideoDownloaderInitialization:
    """Test VideoDownloader initialization and setup."""

    def test_video_downloader_initialization_default_directory(self):
        """Test VideoDownloader initialization with default upload directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            os.chdir(temp_dir)  # Change to temp directory

            downloader = VideoDownloader()

            assert downloader.upload_directory == Path("uploads")
            assert downloader.upload_directory.exists()
            assert isinstance(downloader.session, requests.Session)
            assert downloader.session.headers["User-Agent"] == "SoraClient/1.0"

    def test_video_downloader_initialization_custom_directory(self):
        """Test VideoDownloader initialization with custom upload directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            custom_dir = os.path.join(temp_dir, "custom_uploads")

            downloader = VideoDownloader(custom_dir)

            assert downloader.upload_directory == Path(custom_dir)
            assert downloader.upload_directory.exists()

    def test_video_downloader_initialization_existing_directory(self):
        """Test VideoDownloader initialization with existing directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Directory already exists
            downloader = VideoDownloader(temp_dir)

            assert downloader.upload_directory == Path(temp_dir)
            assert downloader.upload_directory.exists()

    def test_video_downloader_session_configuration(self):
        """Test that requests session is properly configured."""
        downloader = VideoDownloader()

        assert downloader.session.headers["User-Agent"] == "SoraClient/1.0"
        assert isinstance(downloader.session, requests.Session)


class TestVideoDownload:
    """Test video download functionality."""

    def test_download_video_success(self):
        """Test successful video download."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/test_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Mock successful response
            mock_response = Mock()
            mock_response.headers = {
                "content-type": "video/mp4",
                "content-length": "1048576",  # 1MB
            }
            mock_response.iter_content.return_value = [b"test video content chunk"]
            mock_response.raise_for_status.return_value = None

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()) as mock_file:
                result_path = downloader.download_video(video_url, job_id)

                # Verify return path
                expected_filename = f"{job_id}_video.mp4"
                expected_path = str(Path(temp_dir) / expected_filename)
                assert result_path == expected_path

                # Verify request was made correctly
                downloader.session.get.assert_called_once_with(
                    video_url, headers={}, stream=True, timeout=60
                )

                # Verify file was written
                mock_file.assert_called_once()

    def test_download_video_with_api_key(self):
        """Test video download with API key authentication."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/test_video.mp4"
        api_key = "test-api-key-123"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            mock_response = Mock()
            mock_response.headers = {"content-type": "video/mp4"}
            mock_response.iter_content.return_value = [b"video data"]
            mock_response.raise_for_status.return_value = None

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()):
                downloader.download_video(video_url, job_id, api_key)

                # Verify API key was included in headers
                call_args = downloader.session.get.call_args
                assert call_args[1]["headers"]["api-key"] == api_key

    def test_download_video_empty_url(self):
        """Test video download with empty URL."""
        job_id = str(uuid.uuid4())

        downloader = VideoDownloader()

        result = downloader.download_video("", job_id)

        assert result is None

    def test_download_video_none_url(self):
        """Test video download with None URL."""
        job_id = str(uuid.uuid4())

        downloader = VideoDownloader()

        result = downloader.download_video(None, job_id)

        assert result is None

    def test_download_video_http_error(self):
        """Test video download with HTTP error."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/not_found.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Mock HTTP error response
            mock_response = Mock()
            mock_response.raise_for_status.side_effect = requests.HTTPError(
                "404 Not Found"
            )

            with patch.object(downloader.session, "get", return_value=mock_response):
                result = downloader.download_video(video_url, job_id)

                assert result is None

    def test_download_video_timeout_error(self):
        """Test video download with timeout error."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/slow_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            with patch.object(
                downloader.session,
                "get",
                side_effect=requests.Timeout("Request timeout"),
            ):
                result = downloader.download_video(video_url, job_id)

                assert result is None

    def test_download_video_connection_error(self):
        """Test video download with connection error."""
        job_id = str(uuid.uuid4())
        video_url = "https://unreachable.example.com/video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            with patch.object(
                downloader.session,
                "get",
                side_effect=requests.ConnectionError("Connection failed"),
            ):
                result = downloader.download_video(video_url, job_id)

                assert result is None

    def test_download_video_file_write_error(self):
        """Test video download with file write error."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/test_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            mock_response = Mock()
            mock_response.headers = {"content-type": "video/mp4"}
            mock_response.iter_content.return_value = [b"video data"]
            mock_response.raise_for_status.return_value = None

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", side_effect=OSError("Disk full")):
                result = downloader.download_video(video_url, job_id)

                assert result is None

    def test_download_video_progress_logging(self):
        """Test that video download logs progress correctly."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/large_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Create large chunks to trigger progress logging
            large_chunk = b"x" * (1024 * 1024)  # 1MB chunk
            mock_response = Mock()
            mock_response.headers = {
                "content-type": "video/mp4",
                "content-length": str(len(large_chunk)),
            }
            mock_response.iter_content.return_value = [large_chunk]
            mock_response.raise_for_status.return_value = None

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()) as mock_file, patch(
                "src.features.sora_integration.video_downloader.logger"
            ) as mock_logger:
                downloader.download_video(video_url, job_id)

                # Verify file size logging
                info_calls = [call.args[0] for call in mock_logger.info.call_args_list]
                assert any("File size:" in call for call in info_calls)

    def test_download_video_content_type_validation(self):
        """Test video download content type validation."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/not_a_video.txt"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            mock_response = Mock()
            mock_response.headers = {"content-type": "text/plain"}  # Not a video
            mock_response.iter_content.return_value = [b"not video data"]
            mock_response.raise_for_status.return_value = None

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()), patch(
                "src.features.sora_integration.video_downloader.logger"
            ) as mock_logger:
                downloader.download_video(video_url, job_id)

                # Should log warning about unexpected content type
                warning_calls = [
                    call.args[0] for call in mock_logger.warning.call_args_list
                ]
                assert any("Unexpected content type" in call for call in warning_calls)


class TestVideoContentTypeValidation:
    """Test video content type validation logic."""

    def test_is_valid_video_content_type_valid_types(self):
        """Test valid video content types."""
        downloader = VideoDownloader()

        valid_types = [
            "video/mp4",
            "video/mpeg",
            "video/quicktime",
            "video/webm",
            "video/x-msvideo",  # AVI
            "application/octet-stream",  # Generic binary
        ]

        for content_type in valid_types:
            assert downloader._is_valid_video_content_type(content_type) is True

    def test_is_valid_video_content_type_invalid_types(self):
        """Test invalid video content types."""
        downloader = VideoDownloader()

        invalid_types = [
            "text/plain",
            "text/html",
            "application/json",
            "image/jpeg",
            "audio/mp3",
            "",  # Empty string
        ]

        for content_type in invalid_types:
            assert downloader._is_valid_video_content_type(content_type) is False

    def test_is_valid_video_content_type_case_insensitive(self):
        """Test that content type validation is case insensitive."""
        downloader = VideoDownloader()

        assert downloader._is_valid_video_content_type("VIDEO/MP4") is True
        assert downloader._is_valid_video_content_type("Video/Mp4") is True
        assert downloader._is_valid_video_content_type("video/MP4") is True


class TestVideoFormatConversion:
    """Test video format conversion functionality."""

    @patch("subprocess.run")
    def test_convert_to_webm_success(self, mock_subprocess):
        """Test successful video conversion to WebM format."""
        job_id = str(uuid.uuid4())
        input_path = f"/tmp/{job_id}_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Mock successful subprocess call
            mock_result = Mock()
            mock_result.returncode = 0
            mock_result.stdout = "Conversion completed successfully"
            mock_result.stderr = ""
            mock_subprocess.return_value = mock_result

            # Mock file existence
            with patch("pathlib.Path.exists", return_value=True):
                result_path = downloader.convert_to_webm(input_path)

                expected_webm_path = input_path.replace(".mp4", "_webm.mp4")
                assert result_path == expected_webm_path

                # Verify ffmpeg command was called correctly
                mock_subprocess.assert_called_once()
                call_args = mock_subprocess.call_args[0][
                    0
                ]  # First positional argument (command list)
                assert call_args[0] == "ffmpeg"
                assert "-i" in call_args
                assert input_path in call_args

    @patch("subprocess.run")
    def test_convert_to_webm_ffmpeg_error(self, mock_subprocess):
        """Test video conversion with ffmpeg error."""
        job_id = str(uuid.uuid4())
        input_path = f"/tmp/{job_id}_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Mock failed subprocess call
            mock_result = Mock()
            mock_result.returncode = 1
            mock_result.stdout = ""
            mock_result.stderr = "ffmpeg: error: invalid codec"
            mock_subprocess.return_value = mock_result

            with patch("pathlib.Path.exists", return_value=True):
                result_path = downloader.convert_to_webm(input_path)

                assert result_path is None

    @patch("subprocess.run")
    def test_convert_to_webm_input_file_not_found(self, mock_subprocess):
        """Test video conversion when input file doesn't exist."""
        nonexistent_path = "/tmp/nonexistent_video.mp4"

        downloader = VideoDownloader()

        # Mock file doesn't exist
        with patch("pathlib.Path.exists", return_value=False):
            result_path = downloader.convert_to_webm(nonexistent_path)

            assert result_path is None
            # ffmpeg should not be called
            mock_subprocess.assert_not_called()

    @patch("subprocess.run")
    def test_convert_to_webm_subprocess_exception(self, mock_subprocess):
        """Test video conversion with subprocess exception."""
        input_path = "/tmp/test_video.mp4"

        downloader = VideoDownloader()

        # Mock subprocess exception
        mock_subprocess.side_effect = Exception("Subprocess failed")

        with patch("pathlib.Path.exists", return_value=True):
            result_path = downloader.convert_to_webm(input_path)

            assert result_path is None

    def test_convert_to_webm_invalid_input_path(self):
        """Test video conversion with invalid input path."""
        downloader = VideoDownloader()

        # Test with None
        result = downloader.convert_to_webm(None)
        assert result is None

        # Test with empty string
        result = downloader.convert_to_webm("")
        assert result is None


class TestVideoValidation:
    """Test video file validation functionality."""

    def test_validate_video_file_success(self):
        """Test successful video file validation."""
        with tempfile.NamedTemporaryFile(suffix=".mp4") as temp_file:
            # Write some dummy video data
            temp_file.write(b"fake video data that's long enough")
            temp_file.flush()

            downloader = VideoDownloader()

            is_valid = downloader.validate_video_file(temp_file.name)

            assert is_valid is True

    def test_validate_video_file_not_exists(self):
        """Test video file validation when file doesn't exist."""
        downloader = VideoDownloader()

        is_valid = downloader.validate_video_file("/tmp/nonexistent_video.mp4")

        assert is_valid is False

    def test_validate_video_file_too_small(self):
        """Test video file validation when file is too small."""
        with tempfile.NamedTemporaryFile(suffix=".mp4") as temp_file:
            # Write very small amount of data
            temp_file.write(b"tiny")
            temp_file.flush()

            downloader = VideoDownloader()

            is_valid = downloader.validate_video_file(temp_file.name)

            assert is_valid is False

    def test_validate_video_file_wrong_extension(self):
        """Test video file validation with wrong file extension."""
        with tempfile.NamedTemporaryFile(suffix=".txt") as temp_file:
            temp_file.write(b"this is a text file not a video" * 100)
            temp_file.flush()

            downloader = VideoDownloader()

            is_valid = downloader.validate_video_file(temp_file.name)

            assert is_valid is False

    def test_validate_video_file_invalid_path(self):
        """Test video file validation with invalid path."""
        downloader = VideoDownloader()

        # Test with None
        assert downloader.validate_video_file(None) is False

        # Test with empty string
        assert downloader.validate_video_file("") is False


class TestVideoCleanup:
    """Test video file cleanup functionality."""

    def test_cleanup_old_videos_success(self):
        """Test successful cleanup of old video files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Create some test video files
            old_file1 = Path(temp_dir) / "old_video1.mp4"
            old_file2 = Path(temp_dir) / "old_video2_webm.mp4"
            recent_file = Path(temp_dir) / "recent_video.mp4"

            old_file1.touch()
            old_file2.touch()
            recent_file.touch()

            # Mock file modification times
            old_time = time.time() - (25 * 3600)  # 25 hours ago
            recent_time = time.time() - (1 * 3600)  # 1 hour ago

            with patch("os.path.getmtime") as mock_getmtime:
                mock_getmtime.side_effect = lambda path: {
                    str(old_file1): old_time,
                    str(old_file2): old_time,
                    str(recent_file): recent_time,
                }.get(path, recent_time)

                cleaned_count = downloader.cleanup_old_videos(max_age_hours=24)

                assert cleaned_count == 2  # Should clean up 2 old files

    def test_cleanup_old_videos_no_old_files(self):
        """Test cleanup when no old files exist."""
        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Create only recent files
            recent_file = Path(temp_dir) / "recent_video.mp4"
            recent_file.touch()

            cleaned_count = downloader.cleanup_old_videos(max_age_hours=24)

            assert cleaned_count == 0

    def test_cleanup_old_videos_empty_directory(self):
        """Test cleanup with empty directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            cleaned_count = downloader.cleanup_old_videos(max_age_hours=24)

            assert cleaned_count == 0

    def test_cleanup_old_videos_permission_error(self):
        """Test cleanup with permission error."""
        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Create a test file
            test_file = Path(temp_dir) / "test_video.mp4"
            test_file.touch()

            # Mock file removal to raise permission error
            with patch(
                "pathlib.Path.unlink", side_effect=PermissionError("Permission denied")
            ), patch(
                "os.path.getmtime", return_value=time.time() - (25 * 3600)
            ):  # Old file
                cleaned_count = downloader.cleanup_old_videos(max_age_hours=24)

                # Should handle error gracefully
                assert cleaned_count == 0


class TestVideoDownloaderIntegration:
    """Test VideoDownloader integration scenarios."""

    def test_download_and_convert_workflow(self):
        """Test complete download and conversion workflow."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/test_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Mock successful download
            mock_response = Mock()
            mock_response.headers = {"content-type": "video/mp4"}
            mock_response.iter_content.return_value = [b"test video data"]
            mock_response.raise_for_status.return_value = None

            # Mock successful conversion
            mock_subprocess_result = Mock()
            mock_subprocess_result.returncode = 0
            mock_subprocess_result.stdout = "Conversion successful"
            mock_subprocess_result.stderr = ""

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()), patch(
                "subprocess.run", return_value=mock_subprocess_result
            ), patch("pathlib.Path.exists", return_value=True):
                # Download video
                downloaded_path = downloader.download_video(video_url, job_id)
                assert downloaded_path is not None

                # Convert to WebM
                converted_path = downloader.convert_to_webm(downloaded_path)
                assert converted_path is not None
                assert "_webm.mp4" in converted_path

    def test_error_handling_throughout_workflow(self):
        """Test error handling throughout the download workflow."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/failing_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            # Test download failure
            with patch.object(
                downloader.session,
                "get",
                side_effect=requests.HTTPError("Download failed"),
            ):
                result = downloader.download_video(video_url, job_id)
                assert result is None

            # Test validation failure
            assert downloader.validate_video_file("/nonexistent/path") is False

            # Test conversion failure
            with patch("pathlib.Path.exists", return_value=False):
                result = downloader.convert_to_webm("/nonexistent/video.mp4")
                assert result is None

    def test_concurrent_downloads(self):
        """Test handling of multiple concurrent downloads."""
        job_ids = [str(uuid.uuid4()) for _ in range(3)]
        video_urls = [f"https://example.com/video_{i}.mp4" for i in range(3)]

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            mock_response = Mock()
            mock_response.headers = {"content-type": "video/mp4"}
            mock_response.iter_content.return_value = [b"video data"]
            mock_response.raise_for_status.return_value = None

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()):
                # Simulate concurrent downloads
                results = []
                for job_id, video_url in zip(job_ids, video_urls):
                    result = downloader.download_video(video_url, job_id)
                    results.append(result)

                # All downloads should succeed
                assert all(result is not None for result in results)
                assert len(set(results)) == 3  # All paths should be unique

    def test_session_reuse(self):
        """Test that requests session is reused across downloads."""
        job_id = str(uuid.uuid4())
        video_url = "https://example.com/test_video.mp4"

        with tempfile.TemporaryDirectory() as temp_dir:
            downloader = VideoDownloader(temp_dir)

            mock_response = Mock()
            mock_response.headers = {"content-type": "video/mp4"}
            mock_response.iter_content.return_value = [b"video data"]
            mock_response.raise_for_status.return_value = None

            original_session = downloader.session

            with patch.object(
                downloader.session, "get", return_value=mock_response
            ), patch("builtins.open", mock_open()):
                # Make multiple downloads
                downloader.download_video(video_url, job_id + "_1")
                downloader.download_video(video_url, job_id + "_2")

                # Session should be the same instance
                assert downloader.session is original_session

                # Session should be called multiple times
                assert downloader.session.get.call_count == 2
