"""Integration tests for Azure OpenAI Sora API client.

Tests component-based architecture with HTTP client, job manager,
and video downloader components. Covers job creation, polling,
and video download functionality.
"""

import os
import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from src.core.models import VideoJob
from src.features.sora_integration.client import SoraClient


@pytest.mark.integration
class TestSoraClient:
    """Test SoraClient component integration.

    Tests client initialization, video job creation, status polling,
    video downloading, and error handling with mocked components.
    """

    def setup_method(self):
        """Set up test environment variables."""
        # Clear any existing environment variables
        for key in list(os.environ.keys()):
            if key.startswith("AZURE_OPENAI_"):
                del os.environ[key]

        # Set test environment variables
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"
        os.environ["AZURE_OPENAI_SORA_DEPLOYMENT"] = "sora"
        os.environ["AZURE_OPENAI_API_VERSION"] = "2024-02-15-preview"

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    @patch("src.config.factory.ConfigurationFactory")
    def test_client_initialization_with_components(
        self,
        mock_config_factory,
        mock_http_client,
        mock_job_manager,
        mock_video_downloader,
    ):
        """Test client initialization with component-based architecture."""
        # Mock the Azure configuration
        mock_config_factory.get_azure_config.return_value = {
            "endpoint": "https://test.openai.azure.com/",
            "api_key": "test-key",
            "api_version": "2024-02-15-preview",
            "deployment_name": "sora",
        }

        # Mock the components
        mock_http_instance = MagicMock()
        mock_job_instance = MagicMock()
        mock_downloader_instance = MagicMock()

        mock_http_client.return_value = mock_http_instance
        mock_job_manager.return_value = mock_job_instance
        mock_video_downloader.return_value = mock_downloader_instance

        client = SoraClient()

        # Verify components were initialized
        assert client.http_client == mock_http_instance
        assert client.job_manager == mock_job_instance
        assert client.video_downloader == mock_downloader_instance

        # Verify HTTP client was called with correct parameters
        mock_http_client.assert_called_once_with(
            endpoint="https://test.openai.azure.com/",
            api_key="test-key",
            api_version="2024-02-15-preview",
            deployment="sora",
        )

    @patch("src.config.factory.ConfigurationFactory")
    def test_client_initialization_missing_endpoint(self, mock_config_factory):
        """Test client initialization with missing endpoint."""
        # Mock the configuration factory to raise ValueError
        mock_config_factory.get_azure_config.side_effect = ValueError(
            "AZURE_OPENAI_ENDPOINT is required"
        )

        with pytest.raises(ValueError, match="AZURE_OPENAI_ENDPOINT"):
            SoraClient()

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_create_video_job_valid_prompt(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test creating video job with valid prompt."""
        # Mock the job manager
        mock_job_instance = MagicMock()
        mock_job_manager.return_value = mock_job_instance

        # Create mock VideoJob
        job_id = str(uuid.uuid4())
        mock_job = VideoJob(
            id=job_id,
            prompt="A cat playing piano",
            status="pending",
            generation_id="gen-123",
            created_at=datetime.now(),
        )
        mock_job_instance.create_job.return_value = mock_job

        client = SoraClient()
        ui_parameters = {"width": 1280, "height": 720, "duration": 5}

        job = client.create_video_job("A cat playing piano", ui_parameters)

        assert isinstance(job, VideoJob)
        assert job.prompt == "A cat playing piano"
        assert job.status == "pending"
        assert job.id == job_id
        assert job.generation_id == "gen-123"

        # Verify job manager was called correctly
        mock_job_instance.create_job.assert_called_once_with(
            "A cat playing piano", ui_parameters
        )

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_create_video_job_invalid_prompt(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test creating video job with invalid prompt."""
        # Mock the job manager to raise validation error
        mock_job_instance = MagicMock()
        mock_job_manager.return_value = mock_job_instance
        mock_job_instance.create_job.side_effect = ValueError("Invalid prompt")

        client = SoraClient()
        ui_parameters = {"width": 1280, "height": 720, "duration": 5}

        with pytest.raises(ValueError, match="Invalid prompt"):
            client.create_video_job("", ui_parameters)

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_poll_job_status(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test polling job status."""
        # Mock the job manager and video downloader
        mock_job_instance = MagicMock()
        mock_job_manager.return_value = mock_job_instance
        mock_downloader_instance = MagicMock()
        mock_video_downloader.return_value = mock_downloader_instance

        # Create mock VideoJob with succeeded status
        job_id = "test-job-123"
        generation_id = "gen-456"
        mock_job = VideoJob(
            id=job_id,
            prompt="A cat playing piano",
            status="succeeded",
            generation_id=generation_id,
            download_url="https://example.com/video.mp4",
            created_at=datetime.now(),
            completed_at=datetime.now(),
        )
        mock_job_instance.poll_job_status.return_value = mock_job
        mock_downloader_instance.download_video.return_value = "/tmp/video.mp4"

        client = SoraClient()

        job = client.poll_job_status(job_id, generation_id)

        assert isinstance(job, VideoJob)
        assert job.id == job_id
        assert job.status == "succeeded"
        assert job.generation_id == generation_id
        assert job.file_path == "/tmp/video.mp4"

        # Verify job manager was called correctly
        mock_job_instance.poll_job_status.assert_called_once_with(job_id, generation_id)
        # Verify video downloader was called for succeeded job
        mock_downloader_instance.download_video.assert_called_once_with(
            "https://example.com/video.mp4", job_id
        )

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_poll_job_status_running(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test polling job status for running job."""
        # Mock the job manager
        mock_job_instance = MagicMock()
        mock_job_manager.return_value = mock_job_instance
        mock_downloader_instance = MagicMock()
        mock_video_downloader.return_value = mock_downloader_instance

        # Create mock VideoJob with running status
        job_id = "test-job-123"
        generation_id = "gen-456"
        mock_job = VideoJob(
            id=job_id,
            prompt="A cat playing piano",
            status="running",
            generation_id=generation_id,
            created_at=datetime.now(),
        )
        mock_job_instance.poll_job_status.return_value = mock_job

        client = SoraClient()

        job = client.poll_job_status(job_id, generation_id)

        assert isinstance(job, VideoJob)
        assert job.id == job_id
        assert job.status == "running"
        assert job.generation_id == generation_id

        # Verify job manager was called correctly
        mock_job_instance.poll_job_status.assert_called_once_with(job_id, generation_id)
        # Verify video downloader was NOT called for running job
        mock_downloader_instance.download_video.assert_not_called()

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_download_video(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test video download functionality."""
        # Mock the video downloader
        mock_downloader_instance = MagicMock()
        mock_video_downloader.return_value = mock_downloader_instance
        mock_downloader_instance.download_video.return_value = "/tmp/test-video.mp4"

        client = SoraClient()

        result = client.download_video("https://example.com/video.mp4", "test-job-123")

        assert result == "/tmp/test-video.mp4"
        mock_downloader_instance.download_video.assert_called_once_with(
            "https://example.com/video.mp4", "test-job-123"
        )

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_download_video_failure(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test video download failure handling."""
        # Mock the video downloader to raise an exception
        mock_downloader_instance = MagicMock()
        mock_video_downloader.return_value = mock_downloader_instance
        mock_downloader_instance.download_video.side_effect = Exception(
            "Download failed"
        )

        client = SoraClient()

        result = client.download_video("https://example.com/video.mp4", "test-job-123")

        assert result is None  # Should return None on failure

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_get_storage_info(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test getting storage information."""
        # Mock the video downloader
        mock_downloader_instance = MagicMock()
        mock_video_downloader.return_value = mock_downloader_instance
        mock_downloader_instance.get_storage_info.return_value = {
            "total_files": 5,
            "total_size_bytes": 1024000,
        }

        client = SoraClient()

        info = client.get_storage_info()

        assert info["total_files"] == 5
        assert info["total_size_bytes"] == 1024000
        mock_downloader_instance.get_storage_info.assert_called_once()

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_cleanup_old_files(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test cleanup of old files."""
        # Mock the video downloader
        mock_downloader_instance = MagicMock()
        mock_video_downloader.return_value = mock_downloader_instance
        mock_downloader_instance.cleanup_old_files.return_value = 3

        client = SoraClient()

        cleaned_count = client.cleanup_old_files(24)

        assert cleaned_count == 3
        mock_downloader_instance.cleanup_old_files.assert_called_once_with(24)

    @patch("src.features.sora_integration.client.VideoDownloader")
    @patch("src.features.sora_integration.client.VideoJobManager")
    @patch("src.features.sora_integration.client.SoraHttpClient")
    def test_client_context_manager(
        self, mock_http_client, mock_job_manager, mock_video_downloader
    ):
        """Test client as context manager."""
        # Mock the components
        mock_http_instance = MagicMock()
        mock_downloader_instance = MagicMock()
        mock_http_client.return_value = mock_http_instance
        mock_video_downloader.return_value = mock_downloader_instance

        with SoraClient() as client:
            assert isinstance(client, SoraClient)

        # Verify close was called on components
        mock_http_instance.close.assert_called_once()
        mock_downloader_instance.close.assert_called_once()

    def teardown_method(self):
        """Clean up environment variables."""
        env_vars = [
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_SORA_DEPLOYMENT",
        ]
        for var in env_vars:
            if var in os.environ:
                del os.environ[var]
