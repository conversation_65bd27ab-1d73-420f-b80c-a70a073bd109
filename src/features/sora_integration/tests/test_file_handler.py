"""Tests for file handler functionality."""

import os
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

import pytest

from src.features.sora_integration.file_handler import FileHandler


@pytest.mark.integration
class TestFileHandler:
    """Test file handler functionality."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield tmp_dir

    @pytest.fixture
    def file_handler(self, temp_dir):
        """Create file handler with temporary directory."""
        return FileHandler(upload_folder=temp_dir)

    @pytest.fixture
    def sample_video_file(self, temp_dir):
        """Create sample video file for testing."""
        video_path = Path(temp_dir) / "test_video.mp4"
        video_path.write_bytes(b"fake video content" * 100)  # Make it larger than 1KB
        return video_path

    def test_file_handler_initialization(self, temp_dir):
        """Test file handler initialization."""
        handler = <PERSON><PERSON>andler(upload_folder=temp_dir)
        assert handler.upload_folder == Path(temp_dir)
        assert handler.upload_folder.exists()

    def test_generate_filename(self, file_handler):
        """Test filename generation."""
        job_id = "test-job-123-456"
        filename = file_handler._generate_filename(job_id)

        # Should contain job ID prefix
        assert "test-job" in filename
        # Should be MP4
        assert filename.endswith(".mp4")
        # Should contain timestamp
        assert "sora_video_" in filename

    def test_validate_video_file_valid(self, file_handler, sample_video_file):
        """Test video file validation with valid file."""
        file_size = sample_video_file.stat().st_size
        assert file_handler._validate_video_file(sample_video_file, file_size)

    def test_validate_video_file_empty(self, file_handler, temp_dir):
        """Test video file validation with empty file."""
        empty_file = Path(temp_dir) / "empty.mp4"
        empty_file.touch()

        assert not file_handler._validate_video_file(empty_file, 0)

    def test_validate_video_file_too_small(self, file_handler, temp_dir):
        """Test video file validation with file too small."""
        small_file = Path(temp_dir) / "small.mp4"
        small_file.write_bytes(b"tiny")

        assert not file_handler._validate_video_file(small_file, 4)

    def test_validate_video_file_wrong_extension(self, file_handler, temp_dir):
        """Test video file validation with wrong extension."""
        wrong_ext = Path(temp_dir) / "video.txt"
        wrong_ext.write_bytes(b"fake video content" * 100)
        file_size = wrong_ext.stat().st_size

        assert not file_handler._validate_video_file(wrong_ext, file_size)

    @patch("src.features.sora_integration.file_handler.SoraClient")
    def test_download_video_success(self, mock_sora_client, file_handler, temp_dir):
        """Test successful video download."""
        # Mock SoraClient
        mock_client = Mock()
        mock_client.download_video.return_value = True
        mock_sora_client.return_value = mock_client

        # Create a fake downloaded file
        job_id = "test-job-123"
        video_url = "https://example.com/video.mp4"

        def create_file(*args):
            file_path = args[1]  # Second argument is file path
            Path(file_path).write_bytes(b"fake video content" * 100)
            return True

        mock_client.download_video.side_effect = create_file

        success, file_path, file_size = file_handler.download_video(video_url, job_id)

        assert success
        assert file_path is not None
        assert file_size > 0
        assert Path(file_path).exists()

    @patch("src.features.sora_integration.file_handler.SoraClient")
    def test_download_video_failure(self, mock_sora_client, file_handler):
        """Test failed video download."""
        # Mock SoraClient to fail
        mock_client = Mock()
        mock_client.download_video.return_value = False
        mock_sora_client.return_value = mock_client

        job_id = "test-job-123"
        video_url = "https://example.com/video.mp4"

        success, file_path, file_size = file_handler.download_video(video_url, job_id)

        assert not success
        assert file_path is None
        assert file_size is None

    def test_safe_delete_file(self, file_handler, sample_video_file):
        """Test safe file deletion."""
        assert sample_video_file.exists()

        success = file_handler._safe_delete_file(sample_video_file)

        assert success
        assert not sample_video_file.exists()

    def test_safe_delete_nonexistent_file(self, file_handler, temp_dir):
        """Test safe deletion of nonexistent file."""
        nonexistent = Path(temp_dir) / "nonexistent.mp4"

        success = file_handler._safe_delete_file(nonexistent)

        assert not success

    def test_cleanup_old_files(self, file_handler, temp_dir):
        """Test cleanup of old files."""
        # Create test files with different ages
        old_file = Path(temp_dir) / "old_video.mp4"
        new_file = Path(temp_dir) / "new_video.mp4"

        old_file.write_bytes(b"old content")
        new_file.write_bytes(b"new content")

        # Make old file appear old
        old_time = (datetime.now() - timedelta(hours=25)).timestamp()
        os.utime(old_file, (old_time, old_time))

        # Cleanup files older than 24 hours
        deleted_count = file_handler.cleanup_old_files(max_age_hours=24)

        assert deleted_count == 1
        assert not old_file.exists()
        assert new_file.exists()

    def test_get_disk_usage(self, file_handler, sample_video_file):
        """Test disk usage calculation."""
        usage = file_handler.get_disk_usage()

        assert isinstance(usage, dict)
        assert "total_files" in usage
        assert "total_size_bytes" in usage
        assert "disk_free_gb" in usage
        assert usage["total_files"] >= 1  # At least our sample file

    def test_check_available_space(self, file_handler):
        """Test available space check."""
        # Should have space for small requirement
        assert file_handler.check_available_space(required_mb=1)

        # Should not have space for huge requirement
        assert not file_handler.check_available_space(required_mb=999999999)
