"""Tests for VideoJobManager - Azure OpenAI job management."""

import json
import uuid
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
import requests
from pydantic import ValidationError

from src.core.models import VideoJob
from src.features.sora_integration.job_manager import VideoJobManager


@pytest.mark.unit
class TestVideoJobManagerInitialization:
    """Test VideoJobManager initialization."""

    def test_initialization_with_http_client(self):
        """Test job manager initialization with HTTP client."""
        mock_http_client = Mock()
        manager = VideoJobManager(mock_http_client)

        assert manager.http_client == mock_http_client

    def test_initialization_stores_client_reference(self):
        """Test that initialization properly stores client reference."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        
        manager = VideoJobManager(mock_http_client)
        
        assert manager.http_client.endpoint == "https://test.openai.azure.com/"


@pytest.mark.integration
class TestVideoJobManagerJobCreation:
    """Test VideoJobManager job creation functionality."""

    @patch("src.features.sora_integration.job_manager.logger")
    @patch("src.features.sora_integration.job_manager.GenerationParamsFactory")
    def test_create_job_success(self, mock_factory, mock_logger):
        """Test successful job creation."""
        # Setup mocks
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        
        # Mock successful API response - Azure format with data array
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [{
                "id": "gen-123456",
                "status": "pending",
                "created_at": "2024-01-01T12:00:00Z"
            }]
        }
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        # Mock parameter factory
        mock_params = Mock()
        mock_params.prompt = "test prompt"
        mock_params.width = 1280
        mock_params.height = 720
        mock_params.duration = 5
        mock_params.model = "sora"
        mock_params.to_api_dict.return_value = {
            "prompt": "test prompt",
            "width": 1280,
            "height": 720,
            "duration": 5,
            "model": "sora"
        }
        mock_factory.create_from_ui_request.return_value = mock_params

        manager = VideoJobManager(mock_http_client)
        
        # Create job
        prompt = "test prompt"
        ui_parameters = {"width": 1280, "height": 720, "duration": 5}
        
        result = manager.create_job(prompt, ui_parameters)

        # Verify result
        assert isinstance(result, VideoJob)
        assert result.generation_id == "gen-123456"
        assert result.status == "pending"
        assert result.prompt == "test prompt"
        assert result.id is not None  # job_id should be set

        # Verify API call was made correctly
        mock_http_client.make_request.assert_called_once()
        call_args = mock_http_client.make_request.call_args
        assert call_args[1]["method"] == "POST"
        assert "/openai/deployments/sora/generations" in call_args[1]["url"]
        
        # Verify request data structure (Azure format)
        request_data = call_args[1]['data']
        assert request_data["prompt"] == "test prompt"
        assert request_data["size"] == "1280x720"  # Azure format
        assert request_data["duration"] == 5

    @patch("src.features.sora_integration.job_manager.GenerationParamsFactory")
    def test_create_job_with_custom_parameters(self, mock_factory):
        """Test job creation with custom UI parameters."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        mock_http_client.api_version = "2024-02-15-preview"
        
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [{
                "id": "gen-789012",
                "status": "pending"
            }]
        }
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        # Mock parameter factory
        mock_params = Mock()
        mock_params.prompt = "custom prompt"
        mock_params.width = 1920
        mock_params.height = 1080
        mock_params.duration = 10
        mock_params.model = "sora-turbo"
        mock_params.to_api_dict.return_value = {
            "prompt": "custom prompt",
            "width": 1920,
            "height": 1080,
            "n_seconds": 10,
            "model": "sora-turbo"
        }
        mock_factory.create_from_ui_request.return_value = mock_params

        manager = VideoJobManager(mock_http_client)
        
        # Create job with custom parameters
        prompt = "custom prompt"
        ui_parameters = {
            "width": 1920,
            "height": 1080,
            "duration": 10,
            "model": "sora-turbo"
        }
        
        result = manager.create_job(prompt, ui_parameters)

        # Verify parameter factory was called correctly
        mock_factory.create_from_ui_request.assert_called_once_with(
            prompt="custom prompt",
            ui_parameters=ui_parameters
        )

        # Verify result
        assert result.generation_id == "gen-789012"
        assert result.status == "pending"

    @patch("src.features.sora_integration.job_manager.GenerationParamsFactory")
    def test_create_job_http_error(self, mock_factory):
        """Test job creation HTTP error handling."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        
        # Mock HTTP error
        mock_http_client.make_request.side_effect = requests.exceptions.HTTPError("400 Bad Request")

        # Mock parameter factory
        mock_params = Mock()
        mock_params.prompt = "test prompt"
        mock_params.width = 1280
        mock_params.height = 720
        mock_params.duration = 5
        mock_params.model = "sora"
        mock_params.to_api_dict.return_value = {"prompt": "test prompt"}
        mock_factory.create_from_ui_request.return_value = mock_params

        manager = VideoJobManager(mock_http_client)
        
        # The implementation returns a failed VideoJob instead of raising exception
        result = manager.create_job("test prompt", {})
        
        assert isinstance(result, VideoJob)
        assert result.status == "failed"
        assert "400 Bad Request" in result.error_message

    @patch("src.features.sora_integration.job_manager.GenerationParamsFactory")
    def test_create_job_connection_error(self, mock_factory):
        """Test job creation connection error handling."""
        mock_http_client = Mock()
        mock_http_client.make_request.side_effect = requests.exceptions.ConnectionError("Connection failed")

        # Mock parameter factory
        mock_params = Mock()
        mock_params.prompt = "test prompt"
        mock_params.width = 1280
        mock_params.height = 720
        mock_params.duration = 5
        mock_params.model = "sora"
        mock_params.to_api_dict.return_value = {"prompt": "test prompt"}
        mock_factory.create_from_ui_request.return_value = mock_params

        manager = VideoJobManager(mock_http_client)
        
        # The implementation returns a failed VideoJob instead of raising exception
        result = manager.create_job("test prompt", {})
        
        assert isinstance(result, VideoJob)
        assert result.status == "failed"
        assert "Connection failed" in result.error_message

    @patch("src.features.sora_integration.job_manager.GenerationParamsFactory")
    def test_create_job_invalid_response_format(self, mock_factory):
        """Test job creation with invalid response format."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        mock_http_client.api_version = "2024-02-15-preview"
        
        # Mock response with invalid format
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {}  # Missing required fields
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        # Mock parameter factory
        mock_params = Mock()
        mock_params.prompt = "test"
        mock_params.width = 1280
        mock_params.height = 720
        mock_params.duration = 5
        mock_params.model = "sora"
        mock_params.to_api_dict.return_value = {"prompt": "test"}
        mock_factory.create_from_ui_request.return_value = mock_params

        manager = VideoJobManager(mock_http_client)
        
        result = manager.create_job("test prompt", {})
        assert result.status == "failed"
        assert "Invalid Azure API response format" in result.error_message


@pytest.mark.integration
class TestVideoJobManagerJobPolling:
    """Test VideoJobManager job polling functionality."""

    def test_poll_job_status_success(self):
        """Test successful job status polling."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        
        # Mock successful API response with data array (Azure format)
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "status": "succeeded",
            "data": [{
                "url": "https://example.com/video.mp4"
            }]
        }
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        manager = VideoJobManager(mock_http_client)
        
        # Poll job status
        job_id = "test-job-123"
        generation_id = "gen-123456"
        
        result = manager.poll_job_status(job_id, generation_id)

        # Verify result
        assert isinstance(result, VideoJob)
        assert result.generation_id == "gen-123456"
        assert result.status == "succeeded"
        assert result.download_url == "https://example.com/video.mp4"

        # Verify API call was made correctly
        mock_http_client.make_request.assert_called_once()
        call_args = mock_http_client.make_request.call_args
        assert call_args[1]["method"] == "GET"
        assert f"/openai/deployments/sora/generations/{generation_id}" in call_args[1]["url"]

    def test_poll_job_status_pending(self):
        """Test polling job with pending status."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        mock_http_client.api_version = "2024-02-15-preview"
        
        # Mock pending response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "gen-123456",
            "status": "running"
        }
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        manager = VideoJobManager(mock_http_client)
        
        result = manager.poll_job_status("test-job-123", "gen-123456")

        # Verify result
        assert result.status == "running"
        assert result.generation_id == "gen-123456"

    def test_poll_job_status_failed(self):
        """Test polling job with failed status."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        mock_http_client.api_version = "2024-02-15-preview"
        
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "gen-123456",
            "status": "failed",
            "error": {
                "message": "Invalid prompt format"
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        manager = VideoJobManager(mock_http_client)
        
        result = manager.poll_job_status("test-job-123", "gen-123456")

        # Verify result
        assert result.status == "failed"
        assert result.error_message == "Invalid prompt format"

    def test_poll_job_status_http_error(self):
        """Test polling job with HTTP error."""
        mock_http_client = Mock()
        mock_http_client.make_request.side_effect = requests.exceptions.HTTPError("404 Not Found")

        manager = VideoJobManager(mock_http_client)
        
        # The implementation returns a failed VideoJob instead of raising exception
        result = manager.poll_job_status("test-job-123", "gen-123456")
        
        assert isinstance(result, VideoJob)
        assert result.status == "failed"
        assert "404 Not Found" in result.error_message

    def test_poll_job_status_connection_error(self):
        """Test polling job with connection error."""
        mock_http_client = Mock()
        mock_http_client.make_request.side_effect = requests.exceptions.ConnectionError("Connection failed")

        manager = VideoJobManager(mock_http_client)
        
        # The implementation returns a failed VideoJob instead of raising exception
        result = manager.poll_job_status("test-job-123", "gen-123456")
        
        assert isinstance(result, VideoJob)
        assert result.status == "failed"
        assert "Connection failed" in result.error_message

    def test_poll_job_status_invalid_response(self):
        """Test polling job with invalid response format."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        mock_http_client.api_version = "2024-02-15-preview"
        
        # Mock invalid response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {}  # Missing required fields
        mock_response.raise_for_status.return_value = None
        mock_http_client.make_request.return_value = mock_response

        manager = VideoJobManager(mock_http_client)
        
        result = manager.poll_job_status("test-job-123", "gen-123456")
        assert result.status == "failed"
        assert "Status polling failed" in result.error_message


# URL generation is handled internally in the implementation, no public methods to test


# Response processing is handled internally in the implementation, no public methods to test


@pytest.mark.unit
@pytest.mark.integration
class TestVideoJobManagerIntegration:
    """Integration tests for VideoJobManager functionality."""

    def test_complete_job_workflow(self):
        """Test complete job creation and polling workflow."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        mock_http_client.api_version = "2024-02-15-preview"
        
        # Mock job creation response
        create_response = Mock()
        create_response.status_code = 200
        create_response.json.return_value = {
            "data": [{
                "id": "gen-123456",
                "status": "pending"
            }]
        }
        create_response.raise_for_status.return_value = None
        
        # Mock job polling response
        poll_response = Mock()
        poll_response.status_code = 200
        poll_response.json.return_value = {
            "id": "gen-123456",
            "status": "succeeded",
            "result": {
                "url": "https://example.com/video.mp4"
            }
        }
        poll_response.raise_for_status.return_value = None
        
        # Setup mock to return different responses for different calls
        mock_http_client.make_request.side_effect = [create_response, poll_response]

        manager = VideoJobManager(mock_http_client)
        
        # Create job
        with patch("src.features.sora_integration.job_manager.GenerationParamsFactory") as mock_factory:
            mock_params = Mock()
            mock_params.prompt = "test"
            mock_params.width = 1280
            mock_params.height = 720
            mock_params.duration = 5
            mock_params.model = "sora"
            mock_params.to_api_dict.return_value = {"prompt": "test"}
            mock_factory.create_from_ui_request.return_value = mock_params
            
            created_job = manager.create_job("test prompt", {})
            
        # Poll job status
        polled_job = manager.poll_job_status("test-job-123", "gen-123456")
        
        # Verify workflow
        assert created_job.generation_id == "gen-123456"
        assert created_job.status == "pending"
        assert polled_job.generation_id == "gen-123456"
        assert polled_job.status == "succeeded"
        assert polled_job.download_url == "https://example.com/video.mp4"

    @patch("src.features.sora_integration.job_manager.GenerationParamsFactory")
    def test_error_handling_consistency(self, mock_factory):
        """Test consistent error handling across methods."""
        mock_http_client = Mock()
        manager = VideoJobManager(mock_http_client)
        
        # Mock parameter factory
        mock_params = Mock()
        mock_params.prompt = "test"
        mock_params.width = 1280
        mock_params.height = 720
        mock_params.duration = 5
        mock_params.model = "sora"
        mock_params.to_api_dict.return_value = {"prompt": "test"}
        mock_factory.create_from_ui_request.return_value = mock_params
        
        # Both methods should handle errors consistently
        mock_http_client.make_request.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        # Both methods return failed VideoJob instead of raising exceptions
        create_result = manager.create_job("test", {})
        assert isinstance(create_result, VideoJob)
        assert create_result.status == "failed"
        assert "Connection failed" in create_result.error_message
            
        poll_result = manager.poll_job_status("job-123", "gen-123")
        assert isinstance(poll_result, VideoJob)
        assert poll_result.status == "failed"
        assert "Connection failed" in poll_result.error_message

    def test_url_generation_consistency(self):
        """Test URL generation consistency across methods."""
        mock_http_client = Mock()
        mock_http_client.endpoint = "https://test.openai.azure.com/"
        mock_http_client.deployment = "sora"
        
        manager = VideoJobManager(mock_http_client)
        
        # URL generation is tested indirectly through method calls
        # Both create_job and poll_job_status should use consistent URL patterns
        assert manager.http_client.endpoint == "https://test.openai.azure.com/"
        assert manager.http_client.deployment == "sora"