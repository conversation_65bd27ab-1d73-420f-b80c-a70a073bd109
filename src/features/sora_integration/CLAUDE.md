# Sora Integration Module Documentation

## Overview

Production-ready Azure OpenAI Sora API integration with component-based architecture. Handles video generation workflow with rate limiting, authentication, file management, and comprehensive error handling.

## 🎯 **CRITICAL SUCCESS: WebM/VP9 Browser Compatibility Fix** (2025-07-20)

**✅ PROBLEM SOLVED**: Browser video playback now works perfectly with automatic WebM/VP9 conversion.

**Root Issue**: Browser environment lacked H.264 codec support → `DEMUXER_ERROR_NO_SUPPORTED_STREAMS`
**Solution**: FFmpeg conversion to WebM/VP9 format in `video_downloader.py:150-162`
**Result**: Videos now play directly in browser with full controls and streaming support

**Key Implementation**: 
```python
# FFmpeg command for WebM output (VP9 codec) - browser environment has no H.264 support
ffmpeg_cmd = [
    'ffmpeg',
    '-i', str(input_path),                        # Input video file from Azure
    '-c:v', 'libvpx-vp9',                         # Use VP9 codec (browser supported)
    '-crf', '30',                                 # Good quality for VP9
    '-b:v', '0',                                  # Use CRF mode (constant quality)
    '-deadline', 'good',                          # Good quality/speed balance
    '-cpu-used', '2',                             # Encoding speed vs compression
    '-y',                                         # Overwrite output file
    str(output_path)                              # Output WebM file
]
```

**Test Status**: ✅ Confirmed working - Videos display and play in browser interface

## Module Structure

```
src/features/sora_integration/
├── client.py              # Main orchestration client
├── http_client.py          # HTTP communication with retry logic
├── job_manager.py          # Job creation and status polling
├── video_downloader.py     # Video file downloading and validation
├── file_handler.py         # File management and cleanup
├── utils.py               # Shared utilities
├── client_original.py     # Legacy monolithic client (for reference)
└── tests/                 # Co-located integration tests
    ├── test_client.py
    ├── test_http_client.py
    ├── test_job_manager.py
    ├── test_video_downloader.py
    ├── test_file_handler.py
    └── test_utils.py
```

## Component Architecture

### SoraClient (Orchestrator)
Main client that coordinates all components for complete video generation workflow.

```python
from src.features.sora_integration.client import SoraClient
from src.features.sora_integration.http_client import SoraHttpClient
from src.features.sora_integration.job_manager import VideoJobManager
from src.features.sora_integration.video_downloader import VideoDownloader

# Component-based initialization
http_client = SoraHttpClient(endpoint, api_key, rate_limiter)
job_manager = VideoJobManager(http_client)
video_downloader = VideoDownloader()

client = SoraClient(
    http_client=http_client,
    job_manager=job_manager,
    video_downloader=video_downloader
)
```

### SoraHttpClient
Handles all HTTP communication with Azure OpenAI API including authentication, rate limiting, and retry logic.

**Key Features:**
- Bearer token authentication
- Request/response logging with header sanitization
- Rate limiting integration (10 req/sec)
- Exponential backoff retry logic
- Request timeout handling

### VideoJobManager
Manages video job lifecycle including creation, status polling, and completion handling.

**Key Features:**
- Job creation with parameter validation
- Status polling with exponential backoff
- Completion detection and result parsing
- Error handling and failure recovery

### VideoDownloader
Handles video file downloading, validation, and storage management.

**Key Features:**
- Progressive download with progress tracking
- Content validation and security checks
- File storage and cleanup operations
- Download retry logic and error handling

## Video Generation Workflow

### 3-Step Process
```python
# Step 1: Submit job to Azure API
initial_job = client.create_video_job(prompt, ui_parameters)
generation_id = initial_job.generation_id

# Step 2: Update job status to running
_update_job_status(job_id, "running", {"generation_id": generation_id})

# Step 3: Poll Azure API until completion with exponential backoff
while True:
    current_job = client.poll_job_status(job_id, generation_id)
    if current_job.status == "succeeded":
        completion_data = {
            "file_path": current_job.file_path,
            "download_url": current_job.download_url,
            "completed_at": datetime.utcnow(),
        }
        _update_job_status(job_id, "succeeded", completion_data)
        break
    elif current_job.status == "failed":
        raise Exception(f"Azure API job failed: {current_job.error_message}")
    
    # Exponential backoff: 5s -> 7.5s -> 11.25s -> ... max 30s
    time.sleep(poll_interval)
    poll_interval = min(poll_interval * 1.5, max_poll_interval)
```

### Status Workflow
```
"pending" → "running" → "succeeded" | "failed"
```

## Azure API Integration

### ⚠️ **CRITICAL API CONFIGURATION (Updated 2025-07-17)**

**Verified Working Configuration:**
```python
# CORRECT API Configuration (Tested and Verified)
AZURE_OPENAI_API_VERSION = "preview"  # MUST be "preview" - not date-based versions
AZURE_OPENAI_ENDPOINT = "https://your-resource.openai.azure.com/"
AZURE_OPENAI_DEPLOYMENT_NAME = "sora"

# CORRECT Endpoint Format
BASE_URL = f"{endpoint}/openai/v1/video/generations"
JOBS_URL = f"{BASE_URL}/jobs"  # CRITICAL: /jobs suffix required

# CORRECT Request Format
POST {JOBS_URL}?api-version=preview
Headers: {
    'api-key': 'your-api-key',
    'Content-Type': 'application/json'
}
```

**❌ Common Configuration Errors:**
- Using `api-version=2024-12-01-preview` → **FAILS with 404**
- Using `api-version=2022-12-01` → **FAILS with 404** 
- Missing `/jobs` suffix → **FAILS with 404**
- Using `/openai/deployments/sora/...` → **FAILS with 404**

### Authentication Patterns
```python
# API Key Authentication (Recommended for development)
client = SoraClient(
    endpoint="https://your-resource.openai.azure.com/",
    api_key="your-api-key",
    api_version="preview",  # MUST be "preview"
    deployment_name="sora"
)

# Azure Default Credentials (Production)
from azure.identity import DefaultAzureCredential
client = SoraClient(
    endpoint="https://your-resource.openai.azure.com/",
    credential=DefaultAzureCredential(),
    api_version="preview",  # MUST be "preview"
    deployment_name="sora"
)
```

### Resolution Presets
```python
# Azure Sora API requires exact resolution specifications
resolution_presets = {
    'SD': {'width': 854, 'height': 480},      # Azure API requires exactly 854x480
    'HD': {'width': 1280, 'height': 720},     # 1280x720 supported
    'Full HD': {'width': 1920, 'height': 1080} # 1920x1080 supported
}

# Supported resolutions: (480,480), (854,480), (720,720), (1280,720), (1080,1080), (1920,1080)
```

### Request Format
```python
# ✅ CORRECT Payload Format (Tested and Verified)
generation_params = {
    "prompt": "A cat playing in a garden",
    "width": 1280,       # Individual width parameter
    "height": 720,       # Individual height parameter  
    "n_seconds": 5,      # Duration in seconds (NOT "duration")
    "model": "sora"      # Model deployment name
}

# ❌ INCORRECT Formats (Do NOT use these)
# "size": "1280x720"     # Not supported - use width/height
# "duration": 5          # Not supported - use n_seconds
# "model": "sora-v1"     # Not supported - use "sora"
```

## Rate Limiting Integration

### Global Rate Limiter
```python
from src.rate_limiting.limiter import GlobalRateLimiter

rate_limiter = GlobalRateLimiter(
    redis_client=redis_client,
    requests_per_second=10,  # Azure API limit
    strategy="sliding_window"
)

# Integrated in HTTP client
http_client = SoraHttpClient(
    endpoint=endpoint,
    api_key=api_key,
    rate_limiter=rate_limiter
)
```

### Rate Limiting Pattern
```python
async def make_request(self, method: str, url: str, **kwargs):
    """Make rate-limited HTTP request."""
    # Check rate limit before request
    if not await self.rate_limiter.is_allowed("azure_api"):
        raise RateLimitExceeded("Azure API rate limit exceeded")
    
    # Make request with retry logic
    return await self._make_request_with_retry(method, url, **kwargs)
```

## Debugging Guide (Updated 2025-07-17)

### 🔍 **Common 404 Error Debugging**

If you encounter 404 errors, check these in order:

1. **API Version**: Must be `"preview"`
   ```bash
   # Check current setting
   grep AZURE_OPENAI_API_VERSION .env
   
   # Should show: AZURE_OPENAI_API_VERSION=preview
   ```

2. **Endpoint Format**: Must include `/jobs` suffix
   ```python
   # ✅ CORRECT
   url = f"{endpoint}/openai/v1/video/generations/jobs?api-version=preview"
   
   # ❌ WRONG (missing /jobs)
   url = f"{endpoint}/openai/v1/video/generations?api-version=preview"
   ```

3. **Deployment Exists**: Verify deployment is created
   ```python
   # Test deployment existence
   response = requests.get(
       f"{endpoint}/openai/deployments?api-version=2022-12-01",
       headers={'api-key': api_key}
   )
   # Should show deployment with id="sora" and status="succeeded"
   ```

### 🧪 **Quick Test Script**
```python
import requests
from src.config.factory import ConfigurationFactory

def test_sora_api():
    config = ConfigurationFactory.get_azure_config()
    url = f"{config['endpoint'].rstrip('/')}/openai/v1/video/generations/jobs?api-version=preview"
    
    response = requests.post(url, headers={'api-key': config['api_key']}, json={
        "prompt": "A cat playing with a ball",
        "width": 1280, "height": 720, "n_seconds": 5, "model": "sora"
    })
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    # Should return 201 with generation ID
```

## Error Handling Patterns

### HTTP Error Handling
```python
class AzureAPIError(Exception):
    """Azure API specific errors."""
    def __init__(self, status_code: int, message: str, response_data: dict = None):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data
        super().__init__(f"Azure API error {status_code}: {message}")

def handle_http_error(response):
    """Handle HTTP error responses from Azure API."""
    if response.status_code == 429:
        raise RateLimitExceeded("Azure API rate limit exceeded")
    elif response.status_code == 401:
        raise AuthenticationError("Invalid Azure API credentials")
    elif response.status_code >= 400:
        raise AzureAPIError(response.status_code, response.text, response.json())
```

### Retry Logic
```python
import asyncio
from typing import Callable, Any

async def retry_with_exponential_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0
) -> Any:
    """Retry function with exponential backoff."""
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries:
                raise e
            
            delay = min(base_delay * (exponential_base ** attempt), max_delay)
            await asyncio.sleep(delay)
```

## Security Implementation

### Header Sanitization
```python
def _sanitize_headers(self, headers: dict) -> dict:
    """Sanitize headers to prevent sensitive data exposure in logs."""
    # Implementation: src/features/sora_integration/client.py:80-94
    sensitive_keys = ['api-key', 'authorization', 'x-api-key', 'authentication']
    
    sanitized = {}
    for key, value in headers.items():
        if key.lower() in sensitive_keys:
            sanitized[key] = "***REDACTED***"
        else:
            sanitized[key] = value
    return sanitized
```

### File Security
```python
def validate_video_file(file_path: str) -> bool:
    """Validate downloaded video file for security."""
    # Check file size
    if os.path.getsize(file_path) > MAX_VIDEO_SIZE:
        return False
    
    # Check file type
    if not file_path.endswith(('.mp4', '.mov', '.avi')):
        return False
    
    # Check file content
    try:
        # Basic file header validation
        with open(file_path, 'rb') as f:
            header = f.read(16)
            # Validate video file signatures
            return validate_video_header(header)
    except Exception:
        return False
```

## Testing Patterns

### Component Testing
```python
import pytest
from unittest.mock import Mock, AsyncMock
from src.features.sora_integration.http_client import SoraHttpClient

class TestSoraHttpClient:
    """Test HTTP client component."""
    
    @pytest.fixture
    def http_client(self):
        """Create HTTP client with mocked dependencies."""
        rate_limiter = AsyncMock()
        return SoraHttpClient(
            endpoint="https://test.openai.azure.com/",
            api_key="test-key",
            rate_limiter=rate_limiter
        )
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, http_client):
        """Test successful HTTP request."""
        with patch('aiohttp.ClientSession.request') as mock_request:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {"status": "success"}
            mock_request.return_value.__aenter__.return_value = mock_response
            
            result = await http_client.make_request("POST", "/test")
            assert result["status"] == "success"
```

### Integration Testing
```python
class TestSoraClientIntegration:
    """Test complete Sora client integration."""
    
    def test_video_generation_workflow(self, mock_azure_api):
        """Test complete video generation workflow."""
        client = SoraClient(
            endpoint="https://test.openai.azure.com/",
            api_key="test-key"
        )
        
        # Test job creation
        job = client.create_video_job("Test prompt", {"duration": 5})
        assert job.generation_id is not None
        
        # Test job polling
        mock_azure_api.set_job_status("completed")
        completed_job = client.poll_job_status(job.job_id, job.generation_id)
        assert completed_job.status == "succeeded"
        assert completed_job.file_path is not None
```

## Configuration Patterns

### Environment Configuration
```python
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_SORA_DEPLOYMENT=sora

# Video Generation Settings
MAX_PROMPT_LENGTH=500
DEFAULT_VIDEO_DURATION=5
MAX_VIDEO_DURATION=20

# Rate Limiting
AZURE_API_RATE_LIMIT=10  # requests per second
RATE_LIMIT_STRATEGY=sliding_window
```

### Configuration Factory
```python
from src.config.environments import get_environment_config

def create_sora_client() -> SoraClient:
    """Factory function to create configured Sora client."""
    config = get_environment_config()
    
    return SoraClient(
        endpoint=config.azure_endpoint,
        api_key=config.azure_api_key,
        api_version=config.azure_api_version,
        deployment_name=config.azure_deployment,
        rate_limit=config.azure_rate_limit
    )
```

## Performance Optimization

### Connection Pooling
```python
import aiohttp

class SoraHttpClient:
    def __init__(self, ...):
        # Configure connection pooling
        connector = aiohttp.TCPConnector(
            limit=100,          # Total connection pool size
            limit_per_host=30,  # Connections per host
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=60),
            headers=self._get_default_headers()
        )
```

### Caching Strategy
```python
from functools import lru_cache
import time

class VideoJobManager:
    def __init__(self):
        self._status_cache = {}
        self._cache_ttl = 5  # seconds
    
    def get_job_status(self, job_id: str, generation_id: str):
        """Get job status with caching."""
        cache_key = f"{job_id}:{generation_id}"
        
        # Check cache
        if cache_key in self._status_cache:
            cached_data, timestamp = self._status_cache[cache_key]
            if time.time() - timestamp < self._cache_ttl:
                return cached_data
        
        # Fetch fresh data
        status = self._fetch_job_status(job_id, generation_id)
        self._status_cache[cache_key] = (status, time.time())
        return status
```

## Development Workflow

### Testing Commands
```bash
# Test Sora integration components
uv run pytest src/features/sora_integration/tests/ -v

# Test specific component
uv run pytest src/features/sora_integration/tests/test_http_client.py -v

# Test with Azure API mocking
uv run pytest src/features/sora_integration/tests/ -k "not live_api" -v

# Performance testing
uv run pytest src/features/sora_integration/tests/ -m "performance" -v
```

### Manual Testing
```bash
# Test Azure API connectivity
curl -H "Authorization: Bearer $AZURE_API_KEY" \
     -H "Content-Type: application/json" \
     "$AZURE_ENDPOINT/openai/deployments/$DEPLOYMENT/generations"

# Test video generation
python -c "
from src.features.sora_integration.client import SoraClient
client = SoraClient.from_env()
job = client.create_video_job('A cat playing', {'duration': 5})
print(f'Job created: {job.job_id}')
"
```

## Monitoring and Logging

### Request Logging
```python
import logging

logger = logging.getLogger(__name__)

class SoraHttpClient:
    async def make_request(self, method: str, url: str, **kwargs):
        """Make HTTP request with comprehensive logging."""
        # Log request (with sanitized headers)
        sanitized_headers = self._sanitize_headers(kwargs.get('headers', {}))
        logger.info(f"Azure API {method} {url}", extra={
            'headers': sanitized_headers,
            'request_id': self._generate_request_id()
        })
        
        start_time = time.time()
        try:
            response = await self._make_request(method, url, **kwargs)
            duration = time.time() - start_time
            
            # Log successful response
            logger.info(f"Azure API response {response.status}", extra={
                'duration_ms': int(duration * 1000),
                'status_code': response.status,
                'request_id': self._generate_request_id()
            })
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Azure API error: {e}", extra={
                'duration_ms': int(duration * 1000),
                'error_type': type(e).__name__,
                'request_id': self._generate_request_id()
            })
            raise
```

### Metrics Collection
```python
from src.monitoring.metrics import MetricsCollector

class SoraClient:
    def __init__(self, ...):
        self.metrics = MetricsCollector()
    
    def create_video_job(self, prompt: str, parameters: dict):
        """Create video job with metrics."""
        with self.metrics.timer('sora_job_creation'):
            try:
                job = self._create_job(prompt, parameters)
                self.metrics.increment('sora_jobs_created')
                return job
            except Exception as e:
                self.metrics.increment('sora_job_creation_errors')
                raise
```

## Best Practices

1. **Component Separation**: Keep HTTP, job management, and file operations in separate components
2. **Rate Limiting**: Always respect Azure API rate limits with proper backoff
3. **Authentication**: Use Azure managed identities in production
4. **Error Handling**: Implement comprehensive error handling with specific exception types
5. **Security**: Sanitize logs and validate all file operations
6. **Testing**: Mock external dependencies and test components in isolation
7. **Performance**: Use connection pooling and caching for optimal performance
8. **Monitoring**: Log all API interactions with request tracking
9. **Configuration**: Use environment-based configuration with validation
10. **Documentation**: Document all API interactions and error conditions

## Troubleshooting

### ⚠️ **CRITICAL: Common Configuration Issues** (Updated 2025-07-29)

#### 1. **Placeholder Hostname Issue** (Most Common)

**Symptoms:**
- Connection errors when testing Azure API
- 404 or connection refused errors
- "Invalid hostname" errors

**Root Cause:**
Users often copy `.env.example` without replacing placeholder values:
```bash
# ❌ WRONG - Using placeholder from .env.example
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# ✅ CORRECT - Real Azure resource hostname
AZURE_OPENAI_ENDPOINT=https://mycompany-eastus.openai.azure.com/
```

**Solution:**
1. **Replace placeholder hostnames with your actual Azure resource name:**
   ```bash
   # Find your Azure resource name in Azure Portal
   # Look for: "https://[YOUR-RESOURCE-NAME].openai.azure.com/"
   
   # Update .env file with real hostname
   AZURE_OPENAI_ENDPOINT=https://your-actual-resource-name.openai.azure.com/
   ```

2. **Quick validation test:**
   ```bash
   # Test endpoint accessibility
   curl -H "api-key: $AZURE_OPENAI_API_KEY" \
        "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=preview"
   ```

#### 2. **Azure Credentials Validation**

**Pre-deployment validation script:**
```python
# Create validate_azure.py
import requests
import os
from src.config.factory import ConfigurationFactory

def validate_azure_setup():
    """Validate Azure configuration before running application."""
    print("🔍 Validating Azure OpenAI Configuration...")
    
    try:
        # Get configuration
        config = ConfigurationFactory.get_azure_config()
        
        # Check required fields
        required_fields = ['api_key', 'endpoint', 'api_version', 'deployment_name']
        for field in required_fields:
            if not config.get(field):
                print(f"❌ Missing required field: {field}")
                return False
        
        # Test endpoint format
        endpoint = config['endpoint']
        if 'your-resource' in endpoint or 'placeholder' in endpoint:
            print(f"❌ Placeholder hostname detected: {endpoint}")
            print("   → Replace with your actual Azure resource name")
            return False
        
        # Test API connectivity
        test_url = f"{endpoint.rstrip('/')}/openai/deployments?api-version={config['api_version']}"
        response = requests.get(
            test_url,
            headers={'api-key': config['api_key']},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Azure API connection successful")
            deployments = response.json().get('data', [])
            sora_deployment = next((d for d in deployments if d['id'] == config['deployment_name']), None)
            
            if sora_deployment:
                print(f"✅ Sora deployment '{config['deployment_name']}' found and ready")
                return True
            else:
                print(f"❌ Sora deployment '{config['deployment_name']}' not found")
                print(f"   Available deployments: {[d['id'] for d in deployments]}")
                return False
        else:
            print(f"❌ Azure API connection failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

if __name__ == "__main__":
    success = validate_azure_setup()
    exit(0 if success else 1)
```

**Usage:**
```bash
# Run before starting application
uv run python validate_azure.py

# If validation passes, start application
./scripts/dev-local.sh
```

### Common Issues

**Rate Limiting Errors (429)**
- Check rate limiter configuration
- Implement exponential backoff
- Monitor request frequency

**Authentication Errors (401)**
- Run `validate_azure.py` to check credentials
- Verify API key is correct and not expired
- Check endpoint URL format (no placeholder hostnames)
- Validate deployment name matches Azure Portal

**Job Polling Timeout**
- Increase polling timeout
- Check Azure API status
- Verify generation parameters

**File Download Failures**
- Check file URL accessibility
- Validate file permissions
- Monitor disk space

### Debug Commands
```bash
# Check Azure API connectivity
curl -v -H "Authorization: Bearer $AZURE_API_KEY" "$AZURE_ENDPOINT/health"

# Test rate limiting
python -c "
from src.features.sora_integration.client import SoraClient
client = SoraClient.from_env()
print(f'Rate limit status: {client.rate_limiter.get_status()}')
"

# Check component health
uv run python -c "
from src.features.sora_integration import *
print('All components imported successfully')
"
```
## 🔄 Architectural Update - 2025-07-20 18:37

**New Patterns Detected:**
- `PRPs/scrips/prp_runner.py`: from typing import Any, Dict, Iterator...

**Action Required:** Review and update relevant documentation sections.

---
## 🔄 Architectural Update - 2025-07-20 18:38

**New Patterns Detected:**
- `PRPs/scrips/prp_runner.py`: from typing import Any, Dict, Iterator...

**Action Required:** Review and update relevant documentation sections.

---
## 🔄 Architectural Update - 2025-07-20 18:38

**New Patterns Detected:**
- `PRPs/scrips/prp_runner.py`: from typing import Any, Dict, Iterator...

**Action Required:** Review and update relevant documentation sections.

---
## 🔄 Architectural Update - 2025-07-20 18:39

**New Patterns Detected:**
- `PRPs/scrips/prp_runner.py`: from typing import Any, Dict, Iterator...

**Action Required:** Review and update relevant documentation sections.

---
