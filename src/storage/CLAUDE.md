# File Storage Module

Comprehensive file storage management system with local filesystem implementation, security validation, and abstraction layer for future cloud storage integration.

## Architecture Overview

**Local-First Storage**: Current implementation uses local filesystem storage with comprehensive file management, security validation, and automatic cleanup for video generation workflow.

**Cloud-Ready Abstraction**: Storage interfaces and patterns designed to support future migration to cloud storage services (Azure Blob Storage, AWS S3) without application changes.

**Security-First Design**: Path traversal protection, file validation, access controls, and secure filename handling integrated throughout the storage system.

```
Storage Architecture (Current):
├── Local Filesystem Storage
│   ├── uploads/ directory            # Physical video file storage
│   ├── FileHandler                   # Core file management
│   ├── File routes                   # HTTP endpoints for access
│   └── Security validation           # Path and access protection
├── Storage Interfaces
│   ├── FileHandlerInterface          # Abstract storage operations
│   ├── Storage configuration         # Environment-based settings
│   └── Future cloud integration      # Azure Blob Storage ready
└── Integration Points
    ├── Video generation workflow     # Azure API → Local storage
    ├── Database persistence          # File paths in SQLAlchemy
    ├── Health monitoring             # Disk space and cleanup
    └── API endpoints                 # Streaming and download
```

## Current Storage Implementation

### Core File Management (`src/features/sora_integration/file_handler.py`)

**FileHandler Class**: Primary component for video file management with comprehensive validation and security.

```python
class FileHandler:
    """
    Manages video file downloads, validation, storage, and cleanup.
    
    Features:
    - Secure filename generation with timestamps
    - File validation (size, extension, integrity)
    - Automatic cleanup of old files
    - Disk usage monitoring and space checks
    - Thread-safe operations with proper error handling
    """
    
    def __init__(self, upload_folder: str = "uploads"):
        self.upload_folder = upload_folder
        self.max_file_age_hours = 24
        self.max_disk_usage_percent = 80
        
    def download_video(self, url: str, job_id: str) -> str:
        """Download video from Azure API to local storage."""
        
    def validate_file(self, file_path: str) -> bool:
        """Validate file size, extension, and basic integrity."""
        
    def cleanup_old_files(self) -> int:
        """Remove files older than max_file_age_hours."""
        
    def check_disk_space(self) -> Dict[str, Any]:
        """Monitor disk usage and available space."""
```

**Key Features**:
- **Secure filename generation**: Timestamp-based with job ID isolation
- **File validation**: Size limits (100MB), extension checks, integrity validation
- **Automatic cleanup**: Background cleanup removes files older than 24 hours
- **Disk monitoring**: Space usage checks with configurable thresholds
- **Error handling**: Comprehensive exception handling with logging

### File Serving Routes (`src/api/file_routes.py`)

**HTTP Endpoints**: Secure file access with streaming and download capabilities.

```python
# Video streaming endpoint
@api_bp.route("/video/<job_id>")
def serve_video(job_id: str) -> Response:
    """Stream video file directly from local storage."""
    
    # Security validation
    if not _validate_file_path_security(job_id):
        abort(403, "Invalid file path")
    
    # Job completion check
    job = get_job_by_id(job_id)
    if not job or job.status != "succeeded":
        abort(404, "Video not available")
    
    # Secure file serving
    filename = secure_filename(f"video_{job_id}.mp4")
    return send_from_directory(
        app.config["UPLOAD_FOLDER"],
        filename,
        as_attachment=False,
        mimetype="video/mp4"
    )

# Download endpoint
@api_bp.route("/download/<job_id>")
def download_video(job_id: str) -> Response:
    """Provide downloadable video file."""
    
    # Similar security validation and serving logic
    return send_from_directory(
        app.config["UPLOAD_FOLDER"],
        filename,
        as_attachment=True,
        download_name=f"generated_video_{job_id}.mp4"
    )
```

**Security Features**:
- **Path traversal protection**: `_validate_file_path_security()` function
- **Access control**: Files only accessible for completed jobs
- **Secure filename handling**: Uses `werkzeug.secure_filename`
- **Content type validation**: Proper MIME type handling

### Storage Configuration (`src/config/environments.py`)

**Environment-Based Settings**: Configurable storage paths, limits, and cleanup policies.

```python
class BaseConfig:
    # Storage configuration
    UPLOAD_FOLDER: str = "uploads"
    MAX_CONTENT_LENGTH: int = 104857600  # 100MB
    
    # File management
    FILE_CLEANUP_ENABLED: bool = True
    FILE_MAX_AGE_HOURS: int = 24
    
    # Security settings
    ALLOWED_EXTENSIONS: Set[str] = {"mp4", "mov", "avi"}
    MAX_FILENAME_LENGTH: int = 255

class DevelopmentConfig(BaseConfig):
    UPLOAD_FOLDER = "uploads"
    MAX_CONTENT_LENGTH = 104857600  # 100MB

class TestingConfig(BaseConfig):
    UPLOAD_FOLDER = "/tmp/sora_test_uploads"
    MAX_CONTENT_LENGTH = 52428800   # 50MB
    FILE_MAX_AGE_HOURS = 1         # Quick cleanup in tests

class ProductionConfig(BaseConfig):
    UPLOAD_FOLDER = "/opt/sora/data/uploads"
    MAX_CONTENT_LENGTH = 52428800   # 50MB production limit
    FILE_CLEANUP_ENABLED = True
    FILE_MAX_AGE_HOURS = 24
```

## Storage Interfaces and Abstractions

### Abstract Storage Interface (`src/core/interfaces.py`)

**FileHandlerInterface**: Abstract base class for storage operations supporting future cloud integration.

```python
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List

class FileHandlerInterface(ABC):
    """Abstract interface for file storage operations."""
    
    @abstractmethod
    def save_file(self, file_path: str, content: bytes) -> bool:
        """Save file content to specified path."""
        pass
    
    @abstractmethod
    def get_file(self, file_path: str) -> Optional[bytes]:
        """Retrieve file content."""
        pass
    
    @abstractmethod
    def delete_file(self, file_path: str) -> bool:
        """Delete file."""
        pass
    
    @abstractmethod
    def list_files(self, prefix: str = "") -> List[str]:
        """List files with optional prefix filter."""
        pass
    
    @abstractmethod
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists."""
        pass
    
    @abstractmethod
    def get_file_size(self, file_path: str) -> Optional[int]:
        """Get file size in bytes."""
        pass
    
    @abstractmethod
    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """Clean up old files and return count of deleted files."""
        pass
    
    @abstractmethod
    def check_storage_health(self) -> Dict[str, Any]:
        """Check storage system health and return status."""
        pass
```

### Future Cloud Storage Implementation

**Azure Blob Storage Integration** (planned for production):

```python
class AzureBlobFileHandler(FileHandlerInterface):
    """Azure Blob Storage implementation of file handler interface."""
    
    def __init__(
        self,
        connection_string: str,
        container_name: str = "sora-videos"
    ):
        self.blob_service_client = BlobServiceClient.from_connection_string(
            connection_string
        )
        self.container_name = container_name
        
    def save_file(self, file_path: str, content: bytes) -> bool:
        """Upload file to Azure Blob Storage."""
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            blob_client.upload_blob(content, overwrite=True)
            return True
        except Exception as e:
            logger.error(f"Failed to upload file to Azure Blob: {e}")
            return False
    
    def get_file(self, file_path: str) -> Optional[bytes]:
        """Download file from Azure Blob Storage."""
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            return blob_client.download_blob().readall()
        except Exception:
            return None
```

## File Storage Workflow

### Video Generation and Storage Flow

```python
# 1. Video generation completed by Azure Sora API
azure_video_url = "https://azure.openai.com/generated/video123.mp4"

# 2. Download to local storage
file_handler = FileHandler(upload_folder="uploads")
local_file_path = file_handler.download_video(azure_video_url, job_id)

# 3. File validation
if file_handler.validate_file(local_file_path):
    # 4. Update job with file path
    job_repository.update_job(job_id, {
        "file_path": local_file_path,
        "status": "succeeded"
    })
else:
    # Handle validation failure
    job_repository.update_job(job_id, {
        "status": "failed",
        "error_message": "File validation failed"
    })

# 5. File becomes accessible via API endpoints
# GET /video/{job_id} - streaming
# GET /download/{job_id} - download
```

### Security Validation Flow

```python
def _validate_file_path_security(job_id: str) -> bool:
    """Prevent directory traversal attacks."""
    
    # Check for path traversal attempts
    if ".." in job_id or "/" in job_id or "\\" in job_id:
        logger.warning(f"Path traversal attempt detected: {job_id}")
        return False
    
    # Validate job ID format
    if not SecurityConfig.validate_job_id(job_id):
        logger.warning(f"Invalid job ID format: {job_id}")
        return False
    
    return True

def serve_file_securely(job_id: str) -> Response:
    """Secure file serving with access control."""
    
    # Security validation
    if not _validate_file_path_security(job_id):
        abort(403, "Access denied")
    
    # Job completion check
    job = job_repository.get_job_by_id(job_id)
    if not job or job.status != "succeeded":
        abort(404, "File not available")
    
    # Serve file with security headers
    response = send_from_directory(
        app.config["UPLOAD_FOLDER"],
        secure_filename(f"video_{job_id}.mp4")
    )
    
    # Add security headers
    response.headers.update(SecurityConfig.get_security_headers())
    return response
```

## Storage Configuration Management

### Environment-Based Storage Settings

```python
# Development configuration
UPLOAD_FOLDER = "uploads"                   # Local directory
MAX_CONTENT_LENGTH = 104857600             # 100MB limit
FILE_CLEANUP_ENABLED = True               # Auto cleanup
FILE_MAX_AGE_HOURS = 24                   # 24-hour retention

# Production configuration  
UPLOAD_FOLDER = "/opt/sora/data/uploads"   # Production path
MAX_CONTENT_LENGTH = 52428800             # 50MB production limit
FILE_CLEANUP_ENABLED = True              # Auto cleanup enabled
FILE_MAX_AGE_HOURS = 24                  # 24-hour retention

# Cloud storage configuration (future)
AZURE_STORAGE_CONNECTION_STRING = "..."   # Azure Blob connection
STORAGE_CONTAINER_NAME = "sora-videos"   # Container name
CDN_BASE_URL = "https://cdn.sora.com"    # CDN for video delivery
```

### Storage Factory Pattern

```python
class StorageFactory:
    """Factory for creating storage handlers based on configuration."""
    
    @staticmethod
    def create_file_handler(config: BaseConfig) -> FileHandlerInterface:
        """Create appropriate file handler based on configuration."""
        
        storage_type = config.STORAGE_TYPE
        
        if storage_type == "local":
            return LocalFileHandler(
                upload_folder=config.UPLOAD_FOLDER,
                max_file_age_hours=config.FILE_MAX_AGE_HOURS
            )
        elif storage_type == "azure_blob":
            return AzureBlobFileHandler(
                connection_string=config.AZURE_STORAGE_CONNECTION_STRING,
                container_name=config.STORAGE_CONTAINER_NAME
            )
        elif storage_type == "aws_s3":
            return S3FileHandler(
                bucket_name=config.S3_BUCKET_NAME,
                region=config.AWS_REGION
            )
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")
```

## Health Monitoring and Cleanup

### Storage Health Monitoring

```python
def check_storage_health() -> Dict[str, Any]:
    """Comprehensive storage system health check."""
    
    health_status = {
        "status": "healthy",
        "disk_usage": {},
        "file_count": 0,
        "cleanup_status": {},
        "errors": []
    }
    
    try:
        # Check disk usage
        disk_usage = file_handler.check_disk_space()
        health_status["disk_usage"] = disk_usage
        
        if disk_usage["usage_percent"] > 80:
            health_status["status"] = "warning"
            health_status["errors"].append("High disk usage")
        
        # Check file count
        upload_folder = app.config["UPLOAD_FOLDER"]
        file_count = len([f for f in os.listdir(upload_folder) if f.endswith('.mp4')])
        health_status["file_count"] = file_count
        
        # Check cleanup process
        if app.config["FILE_CLEANUP_ENABLED"]:
            deleted_count = file_handler.cleanup_old_files()
            health_status["cleanup_status"] = {
                "enabled": True,
                "last_cleanup_deleted": deleted_count
            }
        
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["errors"].append(str(e))
    
    return health_status
```

### Automated File Cleanup

```python
def cleanup_old_files() -> int:
    """Remove files older than max_file_age_hours."""
    
    upload_folder = app.config["UPLOAD_FOLDER"]
    max_age_hours = app.config["FILE_MAX_AGE_HOURS"]
    cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
    
    deleted_count = 0
    
    for filename in os.listdir(upload_folder):
        if not filename.endswith('.mp4'):
            continue
            
        file_path = os.path.join(upload_folder, filename)
        
        try:
            # Check file modification time
            file_mtime = datetime.utcfromtimestamp(os.path.getmtime(file_path))
            
            if file_mtime < cutoff_time:
                os.remove(file_path)
                deleted_count += 1
                logger.info(f"Deleted old file: {filename}")
                
        except OSError as e:
            logger.error(f"Error deleting file {filename}: {e}")
    
    return deleted_count

# Scheduled cleanup (called by Celery periodic task)
@celery.task
def scheduled_file_cleanup():
    """Celery task for automated file cleanup."""
    deleted_count = cleanup_old_files()
    logger.info(f"Scheduled cleanup completed: {deleted_count} files deleted")
    return deleted_count
```

## Integration Patterns

### Database Integration

**File Path Persistence**: Video file paths stored in SQLAlchemy models with job metadata.

```python
class VideoJobDB(db.Model):
    __tablename__ = "video_jobs"
    
    id = db.Column(db.String(36), primary_key=True)
    prompt = db.Column(db.Text, nullable=False)
    status = db.Column(db.Enum(VideoJobStatus), nullable=False)
    
    # File storage fields
    file_path = db.Column(db.String(512), nullable=True)
    download_url = db.Column(db.String(1024), nullable=True)
    file_size = db.Column(db.Integer, nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)
```

### Flask Application Integration

**Upload Directory Creation**: Automatic directory creation on application startup.

```python
def create_app(config_name: str = "development") -> Flask:
    """Create Flask application with storage initialization."""
    
    app = Flask(__name__)
    app.config.from_object(get_config(config_name))
    
    # Initialize storage directory
    upload_folder = app.config["UPLOAD_FOLDER"]
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder, exist_ok=True)
        logger.info(f"Created upload directory: {upload_folder}")
    
    # Register file routes
    from src.api.file_routes import api_bp
    app.register_blueprint(api_bp)
    
    return app
```

## Security Considerations

### File Access Security

```python
# Path traversal prevention
def validate_file_path(file_path: str) -> bool:
    """Prevent directory traversal attacks."""
    
    # Normalize path and check for traversal attempts
    normalized = os.path.normpath(file_path)
    if normalized.startswith("..") or "/" in file_path:
        return False
    
    # Validate against allowed patterns
    if not re.match(r'^[a-zA-Z0-9._-]+$', file_path):
        return False
    
    return True

# Access control
def check_file_access_permission(job_id: str, user_session: str) -> bool:
    """Verify user has permission to access file."""
    
    job = job_repository.get_job_by_id(job_id)
    if not job:
        return False
    
    # Check job ownership (for future multi-user)
    if hasattr(job, 'session_id') and job.session_id != user_session:
        return False
    
    # Check job completion
    if job.status != "succeeded":
        return False
    
    return True
```

### File Validation Security

```python
def validate_video_file(file_path: str) -> bool:
    """Comprehensive video file validation."""
    
    # Check file existence
    if not os.path.exists(file_path):
        return False
    
    # Check file size
    file_size = os.path.getsize(file_path)
    max_size = app.config["MAX_CONTENT_LENGTH"]
    if file_size > max_size:
        logger.warning(f"File too large: {file_size} > {max_size}")
        return False
    
    # Check file extension
    allowed_extensions = {".mp4", ".mov", ".avi"}
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in allowed_extensions:
        logger.warning(f"Invalid file extension: {file_ext}")
        return False
    
    # Basic file integrity check
    try:
        with open(file_path, 'rb') as f:
            # Read first few bytes to check for valid video header
            header = f.read(8)
            if not _is_valid_video_header(header):
                logger.warning("Invalid video file header")
                return False
    except IOError:
        return False
    
    return True
```

## Future Cloud Storage Migration

### Migration Strategy

**Phase 1: Interface Implementation**
```python
# Implement cloud storage handlers
class AzureBlobFileHandler(FileHandlerInterface):
    """Azure Blob Storage implementation."""
    pass

class S3FileHandler(FileHandlerInterface):  
    """AWS S3 implementation."""
    pass
```

**Phase 2: Configuration Updates**
```python
# Add cloud storage configuration
class ProductionConfig(BaseConfig):
    STORAGE_TYPE = "azure_blob"
    AZURE_STORAGE_CONNECTION_STRING = os.environ.get("AZURE_STORAGE_CONNECTION_STRING")
    STORAGE_CONTAINER_NAME = "sora-videos"
    CDN_BASE_URL = "https://cdn.sora.com"
```

**Phase 3: Gradual Migration**
```python
# Hybrid storage during migration
class HybridFileHandler(FileHandlerInterface):
    """Supports both local and cloud storage during migration."""
    
    def __init__(self, local_handler: FileHandlerInterface, cloud_handler: FileHandlerInterface):
        self.local_handler = local_handler
        self.cloud_handler = cloud_handler
        
    def save_file(self, file_path: str, content: bytes) -> bool:
        # Save to both local and cloud during transition
        local_success = self.local_handler.save_file(file_path, content)
        cloud_success = self.cloud_handler.save_file(file_path, content)
        return local_success and cloud_success
```

## Performance Considerations

### File Serving Optimization

```python
# Streaming for large files
def stream_video_file(file_path: str) -> Response:
    """Stream large video files efficiently."""
    
    def generate():
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(8192)  # 8KB chunks
                if not chunk:
                    break
                yield chunk
    
    return Response(
        generate(),
        mimetype='video/mp4',
        headers={
            'Content-Disposition': 'inline; filename=video.mp4',
            'Accept-Ranges': 'bytes'
        }
    )

# Range request support for video seeking
@api_bp.route("/video/<job_id>")
def serve_video_with_range(job_id: str) -> Response:
    """Support HTTP range requests for video seeking."""
    
    # Security validation
    if not _validate_file_path_security(job_id):
        abort(403)
    
    file_path = get_video_file_path(job_id)
    if not file_path or not os.path.exists(file_path):
        abort(404)
    
    # Handle range requests
    range_header = request.headers.get('Range', None)
    if range_header:
        return _serve_partial_content(file_path, range_header)
    else:
        return send_file(file_path, mimetype='video/mp4')
```

## Dependencies

### Core Dependencies
- **os**: File system operations
- **shutil**: File operations and cleanup
- **pathlib**: Path manipulation and validation
- **werkzeug**: Secure filename handling
- **Flask**: File serving capabilities

### Security Dependencies
- **re**: Pattern validation for filenames
- **hashlib**: File integrity checking
- **secrets**: Secure random generation

### Future Cloud Dependencies
- **azure-storage-blob**: Azure Blob Storage integration
- **boto3**: AWS S3 integration
- **google-cloud-storage**: Google Cloud Storage integration

## Development Guidelines

### Storage Implementation
- **Interface-first**: Always implement FileHandlerInterface for new storage backends
- **Security validation**: Validate all file paths and access permissions
- **Error handling**: Comprehensive exception handling with proper logging
- **Performance**: Use streaming for large files and implement range requests
- **Monitoring**: Include health checks and metrics for storage operations

### Testing Requirements
- **Unit tests**: Test all storage operations with mocked dependencies
- **Integration tests**: Test file operations with actual filesystem
- **Security tests**: Validate path traversal and access control protection
- **Performance tests**: Test file serving performance and concurrent access

### Migration Guidelines
- **Backward compatibility**: Maintain interface compatibility during migrations
- **Gradual rollout**: Use hybrid storage during cloud migration
- **Data consistency**: Ensure data integrity during migration process
- **Rollback plan**: Maintain ability to rollback to previous storage backend