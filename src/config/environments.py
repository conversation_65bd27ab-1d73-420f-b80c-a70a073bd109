"""Environment-specific configurations for Sora POC."""

import logging
import os
from typing import Any, Optional

from src.config.service import ConfigurationService


def safe_int_from_env(
    env_var: str,
    default: int,
    min_val: Optional[int] = None,
    max_val: Optional[int] = None,
) -> int:
    """
    DEPRECATED: Use ConfigurationService.get_int() instead.

    Safely parse integer from environment variable with validation and fallbacks.

    This function is kept for backward compatibility but new code should use:
    ConfigurationService.get_int(env_var, default, min_val, max_val)
    """
    return ConfigurationService.get_int(env_var, default, min_val, max_val)


class BaseConfig:
    """
    Base configuration with common settings.

    Provides default values and validation for all environments.
    """

    # Flask settings
    @property
    def SECRET_KEY(self) -> str:
        """Flask secret key for session security."""
        return ConfigurationService.get("SECRET_KEY", "dev-key-change-in-production")

    # Database settings
    @classmethod
    def get_database_url(cls) -> str:
        """Get database URL with absolute path, resolved at runtime."""
        # Use ConfigurationService to properly load from .env.local
        env_db_url = ConfigurationService.get("DATABASE_URL")
        if env_db_url:
            import logging

            logger = logging.getLogger(__name__)
            logger.info(f"🗄️ Database URL from ConfigurationService: {env_db_url}")
            return env_db_url

        # Generate absolute path for SQLite
        project_root = os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        db_path = os.path.join(project_root, "sora_poc.db")
        db_url = f"sqlite:///{db_path}"

        # Log the resolved path for debugging
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"🗄️ Database URL resolved to: {db_url}")

        return db_url

    @property
    def DATABASE_URL(self) -> str:
        """Database URL property that resolves at runtime."""
        return self.get_database_url()

    @property
    def SQLALCHEMY_TRACK_MODIFICATIONS(self) -> bool:
        """SQLAlchemy track modifications setting."""
        return ConfigurationService.get_bool("SQLALCHEMY_TRACK_MODIFICATIONS", False)

    # Multi-User Queue Configuration (optional - with defaults)
    @property
    def CELERY_BROKER_URL(self) -> str:
        """Celery broker URL for task queue."""
        return ConfigurationService.get("CELERY_BROKER_URL", "redis://localhost:6379/0")

    @property
    def CELERY_RESULT_BACKEND(self) -> str:
        """Celery result backend for task results."""
        return ConfigurationService.get(
            "CELERY_RESULT_BACKEND", "redis://localhost:6379/0"
        )

    # Redis Configuration
    @property
    def REDIS_HOST(self) -> str:
        """Redis host for connections."""
        return ConfigurationService.get("REDIS_HOST", "localhost")

    @property
    def REDIS_PORT(self) -> int:
        """Redis port for connections."""
        return ConfigurationService.get_int("REDIS_PORT", 6379, 1, 65535)

    @property
    def REDIS_DB(self) -> int:
        """Redis database number."""
        return ConfigurationService.get_int("REDIS_DB", 0, 0, 15)

    # Multi-User Session Management
    MAX_CONCURRENT_JOBS_PER_SESSION: int = safe_int_from_env(
        "MAX_CONCURRENT_JOBS_PER_SESSION", 3, 1, 10
    )
    SESSION_LIFETIME_HOURS: int = safe_int_from_env(
        "SESSION_LIFETIME_HOURS", 24, 1, 168
    )
    SESSION_CLEANUP_INTERVAL_HOURS: int = safe_int_from_env(
        "SESSION_CLEANUP_INTERVAL_HOURS", 6, 1, 48
    )
    MAX_SESSIONS_PER_IP: int = safe_int_from_env("MAX_SESSIONS_PER_IP", 10, 1, 100)

    # Queue Management
    QUEUE_PRIORITY_ENABLED: bool = (
        os.getenv("QUEUE_PRIORITY_ENABLED", "true").lower() == "true"
    )

    # Rate Limiting
    GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND: int = safe_int_from_env(
        "GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND", 10, 1, 100
    )

    # Azure OpenAI settings
    @property
    def AZURE_OPENAI_ENDPOINT(self) -> Optional[str]:
        """Azure OpenAI endpoint URL."""
        return ConfigurationService.get("AZURE_OPENAI_ENDPOINT")

    @property
    def AZURE_OPENAI_API_KEY(self) -> Optional[str]:
        """Azure OpenAI API key."""
        return ConfigurationService.get("AZURE_OPENAI_API_KEY")

    @property
    def AZURE_OPENAI_API_VERSION(self) -> str:
        """Azure OpenAI API version."""

        logger = logging.getLogger(__name__)
        logger.info("🔍 BaseConfig.AZURE_OPENAI_API_VERSION property accessed!")
        result = ConfigurationService.get("AZURE_OPENAI_API_VERSION", "preview")
        logger.info(f"🔍 BaseConfig.AZURE_OPENAI_API_VERSION returning: '{result}'")
        return result

    @property
    def AZURE_OPENAI_DEPLOYMENT_NAME(self) -> str:
        """Azure OpenAI deployment name."""
        return ConfigurationService.get("AZURE_OPENAI_DEPLOYMENT_NAME", "sora")

    # File upload settings
    @property
    def UPLOAD_FOLDER(self) -> str:
        """Upload folder for file storage."""
        return ConfigurationService.get("UPLOAD_FOLDER", "uploads")

    @property
    def MAX_CONTENT_LENGTH(self) -> int:
        """Maximum content length for uploads (100MB)."""
        return ConfigurationService.get_int(
            "MAX_CONTENT_LENGTH", 104857600, min_val=1024
        )

    # Server settings
    @property
    def PORT(self) -> int:
        """Server port number."""
        return ConfigurationService.get_int("PORT", 5000, min_val=1024, max_val=65535)

    # Video generation settings
    MAX_PROMPT_LENGTH: int = safe_int_from_env(
        "MAX_PROMPT_LENGTH", 500, min_val=1, max_val=2000
    )
    DEFAULT_VIDEO_DURATION: int = safe_int_from_env(
        "DEFAULT_VIDEO_DURATION", 2, min_val=1, max_val=60
    )
    MAX_VIDEO_DURATION: int = safe_int_from_env(
        "MAX_VIDEO_DURATION", 20, min_val=1, max_val=60
    )
    DEFAULT_VIDEO_WIDTH: int = safe_int_from_env(
        "DEFAULT_VIDEO_WIDTH", 1280, min_val=480, max_val=3840
    )
    DEFAULT_VIDEO_HEIGHT: int = safe_int_from_env(
        "DEFAULT_VIDEO_HEIGHT", 720, min_val=480, max_val=2160
    )
    MIN_VIDEO_DURATION: int = safe_int_from_env(
        "MIN_VIDEO_DURATION", 1, min_val=1, max_val=10
    )
    MAX_VIDEO_WIDTH: int = safe_int_from_env(
        "MAX_VIDEO_WIDTH", 1920, min_val=480, max_val=3840
    )
    MAX_VIDEO_HEIGHT: int = safe_int_from_env(
        "MAX_VIDEO_HEIGHT", 1920, min_val=480, max_val=2160
    )

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    SQL_DEBUG: bool = os.getenv("SQL_DEBUG", "false").lower() == "true"

    # Rate limiting settings
    RATE_LIMIT_ENABLED: bool = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = int(
        os.getenv("RATE_LIMIT_REQUESTS_PER_MINUTE", "60")
    )

    # File cleanup settings
    FILE_CLEANUP_ENABLED: bool = (
        os.getenv("FILE_CLEANUP_ENABLED", "true").lower() == "true"
    )
    FILE_MAX_AGE_HOURS: int = safe_int_from_env(
        "FILE_MAX_AGE_HOURS", 24, min_val=1, max_val=8760
    )  # Max 1 year

    # Health check settings
    HEALTH_CHECK_ENABLED: bool = (
        os.getenv("HEALTH_CHECK_ENABLED", "true").lower() == "true"
    )
    METRICS_ENABLED: bool = os.getenv("METRICS_ENABLED", "true").lower() == "true"

    @classmethod
    def validate_required_settings(cls) -> None:
        """
        Validate required environment variables.

        Raises:
            ValueError: If required settings are missing
        """
        required_settings = []

        if not cls.SECRET_KEY or cls.SECRET_KEY == "dev-key-change-in-production":
            required_settings.append("SECRET_KEY must be set for production")

        if not cls.AZURE_OPENAI_ENDPOINT:
            required_settings.append("AZURE_OPENAI_ENDPOINT is required")

        if not cls.AZURE_OPENAI_API_KEY and not os.getenv("AZURE_CLIENT_ID"):
            required_settings.append(
                "Either AZURE_OPENAI_API_KEY or Azure identity credentials required"
            )

        if required_settings:
            raise ValueError(
                f"Missing required configuration: {', '.join(required_settings)}"
            )

    @classmethod
    def to_dict(cls) -> dict[str, Any]:
        """
        Convert configuration to dictionary.

        Returns:
            dict[str, Any]: Configuration as dictionary
        """
        return {
            key: getattr(cls, key)
            for key in dir(cls)
            if not key.startswith("_") and not callable(getattr(cls, key))
        }


class DevelopmentConfig(BaseConfig):
    """
    Development environment configuration.

    Enables debugging and development-friendly settings.
    """

    DEBUG: bool = True
    TESTING: bool = False
    LOG_LEVEL: str = "DEBUG"
    SQL_DEBUG: bool = os.getenv("SQL_DEBUG", "true").lower() == "true"

    # Relaxed settings for development
    SECRET_KEY: str = os.getenv("SECRET_KEY", "dev-secret-key-not-for-production")
    RATE_LIMIT_ENABLED: bool = (
        os.getenv("RATE_LIMIT_ENABLED", "false").lower() == "true"
    )

    @classmethod
    def validate_required_settings(cls) -> None:
        """Skip validation for development environment."""
        pass


class TestingConfig(BaseConfig):
    """
    Testing environment configuration.

    Uses in-memory database and test-specific settings.
    """

    DEBUG: bool = False
    TESTING: bool = True
    LOG_LEVEL: str = "WARNING"

    # Test-specific settings
    DATABASE_URL: str = "sqlite:///:memory:"
    SECRET_KEY: str = "test-secret-key"
    UPLOAD_FOLDER: str = "/tmp/sora_test_uploads"

    # Disable external services for testing
    RATE_LIMIT_ENABLED: bool = False
    FILE_CLEANUP_ENABLED: bool = False
    HEALTH_CHECK_ENABLED: bool = False
    METRICS_ENABLED: bool = False

    @classmethod
    def validate_required_settings(cls) -> None:
        """Skip validation for testing environment."""
        pass


class ProductionConfig(BaseConfig):
    """
    Production environment configuration.

    Enforces security and performance settings for production deployment.
    """

    DEBUG: bool = False
    TESTING: bool = False

    # Production database
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL", "postgresql://user:pass@localhost/sora_prod"
    )

    # Enhanced security settings
    SESSION_COOKIE_SECURE: bool = True
    SESSION_COOKIE_HTTPONLY: bool = True
    SESSION_COOKIE_SAMESITE: str = "Lax"

    # Performance settings
    SQLALCHEMY_ENGINE_OPTIONS: dict[str, Any] = {
        "pool_size": 20,
        "max_overflow": 30,
        "pool_pre_ping": True,
        "pool_recycle": 3600,
    }

    # Stricter limits for production
    MAX_CONTENT_LENGTH: int = safe_int_from_env(
        "MAX_CONTENT_LENGTH", 52428800, min_val=1024
    )  # 50MB
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = safe_int_from_env(
        "RATE_LIMIT_REQUESTS_PER_MINUTE", 30, min_val=1, max_val=1000
    )

    @classmethod
    def validate_required_settings(cls) -> None:
        """
        Strict validation for production environment.

        Raises:
            ValueError: If any required production settings are missing
        """
        super().validate_required_settings()

        production_requirements = []

        if cls.SECRET_KEY == "dev-secret-key-not-for-production":
            production_requirements.append("Production SECRET_KEY must be set")

        if "sqlite" in cls.DATABASE_URL:
            production_requirements.append("Production requires PostgreSQL database")

        if not os.getenv("FLASK_ENV") == "production":
            production_requirements.append("FLASK_ENV must be set to 'production'")

        if production_requirements:
            raise ValueError(
                f"Production requirements not met: {', '.join(production_requirements)}"
            )


# Configuration mapping
config_by_name = {
    "development": DevelopmentConfig,
    "testing": TestingConfig,
    "production": ProductionConfig,
}


def get_config(config_name: Optional[str] = None) -> BaseConfig:
    """
    Get configuration class based on environment.

    Args:
        config_name (Optional[str]): Configuration name (development/testing/production)
                                   If None, uses FLASK_ENV environment variable

    Returns:
        BaseConfig: Configuration class instance

    Raises:
        ValueError: If configuration name is invalid
    """
    if config_name is None:
        config_name = os.getenv("FLASK_ENV", "development")

    config_class = config_by_name.get(config_name.lower())
    if not config_class:
        available_configs = ", ".join(config_by_name.keys())
        raise ValueError(
            f"Invalid configuration '{config_name}'. Available: {available_configs}"
        )

    # Validate configuration
    config_class.validate_required_settings()

    return config_class()


def get_environment_info() -> dict[str, Any]:
    """
    Get current environment information.

    Returns:
        dict[str, Any]: Environment information
    """
    config_name = os.getenv("FLASK_ENV", "development")
    config_class = get_config(config_name)

    return {
        "environment": config_name,
        "debug": getattr(config_class, "DEBUG", False),
        "testing": getattr(config_class, "TESTING", False),
        "database_type": "postgresql"
        if "postgresql" in config_class.DATABASE_URL
        else "sqlite",
        "upload_folder": config_class.UPLOAD_FOLDER,
        "rate_limiting": config_class.RATE_LIMIT_ENABLED,
        "health_checks": config_class.HEALTH_CHECK_ENABLED,
        "metrics": config_class.METRICS_ENABLED,
    }
