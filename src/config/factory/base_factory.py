"""Core configuration factory for dependency injection and base configuration management."""

import logging
from typing import Optional

from src.config.environments import BaseConfig, get_config
from src.config.veo3_settings import Veo3Settings, get_cached_veo3_settings
from src.config.video_config import VideoGenerationConfig

logger = logging.getLogger(__name__)


class ConfigurationFactory:
    """
    Core factory for creating configuration-aware objects.

    Provides centralized configuration management with environment-specific
    defaults and override capabilities for testing and UI customization.

    This factory implements the single source of truth pattern for all
    configuration-dependent objects in the system.

    NOTE: Caching removed for simplified debugging and deterministic behavior.
    """

    @classmethod
    def get_base_config(cls, config_name: Optional[str] = None) -> BaseConfig:
        """
        Get base configuration (no caching for simplified debugging).

        Args:
            config_name (Optional[str]): Configuration environment name
                                       (development/testing/production)
                                       If None, uses FLASK_ENV environment variable

        Returns:
            BaseConfig: Environment-specific configuration instance
        """
        return get_config(config_name)

    @classmethod
    def get_video_config(
        cls, config_name: Optional[str] = None
    ) -> VideoGenerationConfig:
        """
        Get video generation configuration (no caching for simplified debugging).

        Args:
            config_name (Optional[str]): Configuration environment name

        Returns:
            VideoGenerationConfig: Video generation configuration instance
        """
        base_config = cls.get_base_config(config_name)
        return VideoGenerationConfig.from_config(base_config)

    @classmethod
    def clear_cache(cls) -> None:
        """
        Clear configuration cache (for testing compatibility).

        NOTE: This method is kept for compatibility but does nothing
        since caching has been removed for simplified debugging.
        """
        pass  # No caching to clear

    @classmethod
    def create_generation_params_defaults(
        cls, config_name: Optional[str] = None
    ) -> dict:
        """
        Create GenerationParams defaults from configuration.

        Args:
            config_name (Optional[str]): Configuration environment name

        Returns:
            dict: Default values for GenerationParams creation
        """
        video_config = cls.get_video_config(config_name)

        return {
            "width": video_config.default_width,
            "height": video_config.default_height,
            "duration": video_config.default_duration,
            "model": video_config.default_model,
        }

    @classmethod
    def validate_generation_params(
        cls,
        width: int,
        height: int,
        duration: int,
        prompt: str,
        config_name: Optional[str] = None,
    ) -> dict:
        """
        Validate generation parameters against configuration constraints.

        Args:
            width (int): Video width in pixels
            height (int): Video height in pixels
            duration (int): Video duration in seconds
            prompt (str): Text prompt for video generation
            config_name (Optional[str]): Configuration environment name

        Returns:
            dict: Validation errors (empty if valid)
        """
        video_config = cls.get_video_config(config_name)
        return video_config.validate_parameters(width, height, duration, prompt)

    @classmethod
    def get_azure_config(cls, config_name: Optional[str] = None) -> dict:
        """
        Get Azure OpenAI configuration for client initialization.

        Args:
            config_name (Optional[str]): Configuration environment name

        Returns:
            dict: Azure configuration parameters
        """
        logger = logging.getLogger(__name__)
        base_config = cls.get_base_config(config_name)

        # DEBUG: Log environment variable access for debugging
        logger.info(
            f"⚙️ ConfigurationFactory.get_azure_config called with config_name: {config_name}"
        )
        logger.info(f"⚙️ Base config class: {type(base_config).__name__}")
        logger.info(
            f"⚙️ About to access AZURE_OPENAI_API_VERSION from {type(base_config).__name__}"
        )
        api_version = base_config.AZURE_OPENAI_API_VERSION
        logger.info(f"⚙️ AZURE_OPENAI_API_VERSION from base_config: '{api_version}'")
        logger.info(
            f"⚙️ AZURE_OPENAI_ENDPOINT from base_config: '{base_config.AZURE_OPENAI_ENDPOINT}'"
        )

        azure_config = {
            "endpoint": base_config.AZURE_OPENAI_ENDPOINT,
            "api_key": base_config.AZURE_OPENAI_API_KEY,
            "api_version": base_config.AZURE_OPENAI_API_VERSION,
            "deployment_name": base_config.AZURE_OPENAI_DEPLOYMENT_NAME,
        }

        logger.info(f"⚙️ ConfigurationFactory returning Azure config: {azure_config}")
        return azure_config

    @classmethod
    def get_app_config(cls, config_name: Optional[str] = None) -> dict:
        """
        Get Flask application configuration.

        Args:
            config_name (Optional[str]): Configuration environment name

        Returns:
            dict: Flask application configuration
        """
        base_config = cls.get_base_config(config_name)

        return {
            "SECRET_KEY": base_config.SECRET_KEY,
            "UPLOAD_FOLDER": base_config.UPLOAD_FOLDER,
            "MAX_CONTENT_LENGTH": base_config.MAX_CONTENT_LENGTH,
            "SQLALCHEMY_DATABASE_URI": base_config.get_database_url(),
            "SQLALCHEMY_TRACK_MODIFICATIONS": base_config.SQLALCHEMY_TRACK_MODIFICATIONS,
        }

    @classmethod
    def get_veo3_settings(cls, config_name: Optional[str] = None) -> Veo3Settings:
        """
        Get Veo3Settings with environment variable respect.

        Args:
            config_name (Optional[str]): Configuration environment name (unused for Veo3Settings)

        Returns:
            Veo3Settings: Cached Veo3Settings instance with environment variables loaded
        """
        logger.debug("ConfigurationFactory.get_veo3_settings() called")
        return get_cached_veo3_settings()
