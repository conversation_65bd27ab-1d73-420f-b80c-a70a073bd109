"""Configuration validation logic for providers and environment settings."""

import logging
from typing import Any, Dict, List, Literal

from pydantic import BaseModel, ConfigDict, Field

logger = logging.getLogger(__name__)


class ValidationResult(BaseModel):
    """Result of configuration validation."""

    valid: bool
    provider: str
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)

    model_config = ConfigDict(frozen=True)


class ConfigurationValidator:
    """
    Comprehensive configuration validation for providers and environments.

    This class provides validation logic for different provider configurations,
    environment settings, and deployment-specific requirements.
    """

    @staticmethod
    def validate_azure_sora_config(config: Dict[str, Any]) -> ValidationResult:
        """
        Validate Azure Sora provider configuration.

        Args:
            config: Azure Sora configuration dictionary

        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []

        # Required fields validation
        required_fields = ["endpoint", "api_key", "api_version", "deployment_name"]
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field}")

        # Endpoint validation
        endpoint = config.get("endpoint", "")
        if endpoint and not endpoint.startswith("https://"):
            errors.append("Azure endpoint must use HTTPS")

        # API version validation
        api_version = config.get("api_version", "")
        valid_versions = ["preview", "2023-12-01-preview", "2024-02-01-preview"]
        if api_version and api_version not in valid_versions:
            warnings.append(f"API version '{api_version}' may not be supported")

        # API key validation
        api_key = config.get("api_key", "")
        if api_key and len(api_key) < 32:
            warnings.append("API key appears to be shorter than expected")

        is_valid = len(errors) == 0
        return ValidationResult(
            valid=is_valid, provider="azure_sora", errors=errors, warnings=warnings
        )

    @staticmethod
    def validate_google_veo3_config(config: Dict[str, Any]) -> ValidationResult:
        """
        Validate Google Veo3 provider configuration.

        Args:
            config: Google Veo3 configuration dictionary

        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []

        # Required fields validation
        required_fields = ["project_id"]
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field}")

        # Project ID validation
        project_id = config.get("project_id", "")
        if (
            project_id
            and isinstance(project_id, str)
            and not project_id.replace("-", "").replace("_", "").isalnum()
        ):
            errors.append("Project ID contains invalid characters")
        elif project_id and not isinstance(project_id, str):
            errors.append("Project ID must be a string")

        # Mock mode validation
        use_mock = config.get("use_mock", False)
        if not isinstance(use_mock, bool):
            errors.append("use_mock must be a boolean value")

        # Timeout validation
        timeout = config.get("timeout", 300)
        if not isinstance(timeout, int) or timeout <= 0:
            errors.append("timeout must be a positive integer")
        elif timeout > 3600:
            warnings.append("timeout is very high (>1 hour)")

        # Retry configuration validation
        max_retries = config.get("max_retries", 3)
        if not isinstance(max_retries, int) or max_retries < 0:
            errors.append("max_retries must be a non-negative integer")
        elif max_retries > 10:
            warnings.append("max_retries is very high (>10)")

        # Authentication validation for non-mock mode
        if not use_mock:
            auth_fields = ["client_id", "client_secret"]
            missing_auth = [field for field in auth_fields if not config.get(field)]
            if missing_auth:
                warnings.append(
                    f"Authentication may fail: missing {', '.join(missing_auth)}"
                )

        is_valid = len(errors) == 0
        return ValidationResult(
            valid=is_valid, provider="google_veo3", errors=errors, warnings=warnings
        )

    @staticmethod
    def validate_provider_configuration(
        provider: Literal["azure_sora", "google_veo3"], config: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate configuration for any supported provider.

        Args:
            provider: Provider name
            config: Provider configuration dictionary

        Returns:
            ValidationResult with validation status and any errors

        Raises:
            ValueError: If provider is not supported
        """
        if provider == "azure_sora":
            return ConfigurationValidator.validate_azure_sora_config(config)
        elif provider == "google_veo3":
            return ConfigurationValidator.validate_google_veo3_config(config)
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    @staticmethod
    def validate_environment_compatibility(
        provider: str, deployment_type: str, config: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate provider configuration against deployment environment.

        Args:
            provider: Provider name
            deployment_type: Deployment type (local, docker, production)
            config: Provider configuration

        Returns:
            ValidationResult with environment-specific validation
        """
        errors = []
        warnings = []

        # Production environment validations
        if deployment_type == "production":
            # Mock mode should not be enabled in production
            if config.get("use_mock", False):
                errors.append("Mock mode must be disabled in production environment")

            # Ensure proper authentication in production
            if provider == "google_veo3" and not config.get("client_secret"):
                errors.append("Client secret is required in production environment")

            # Validate timeout settings for production
            timeout = config.get("timeout", 300)
            if timeout < 60:
                warnings.append("Timeout may be too short for production workloads")

        # Local development validations
        elif deployment_type == "local":
            # Recommend mock mode for local development
            if provider == "google_veo3" and not config.get("use_mock", False):
                warnings.append("Consider enabling mock mode for local development")

        # Docker environment validations
        elif deployment_type == "docker":
            # Ensure adequate timeouts for Docker
            timeout = config.get("timeout", 300)
            if timeout < 300:
                warnings.append("Consider increasing timeout for Docker environment")

        is_valid = len(errors) == 0
        return ValidationResult(
            valid=is_valid, provider=provider, errors=errors, warnings=warnings
        )

    @staticmethod
    def validate_provider_combination(
        configs: Dict[str, Dict[str, Any]],
    ) -> ValidationResult:
        """
        Validate multiple provider configurations for conflicts.

        Args:
            configs: Dictionary mapping provider names to their configurations

        Returns:
            ValidationResult for the overall provider combination
        """
        errors = []
        warnings = []

        # Check for duplicate resource usage
        project_ids = []
        endpoints = []

        for provider, config in configs.items():
            if provider == "google_veo3":
                project_id = config.get("project_id")
                if project_id:
                    if project_id in project_ids:
                        warnings.append(
                            f"Project ID '{project_id}' used by multiple providers"
                        )
                    project_ids.append(project_id)

            elif provider == "azure_sora":
                endpoint = config.get("endpoint")
                if endpoint:
                    if endpoint in endpoints:
                        warnings.append(
                            f"Endpoint '{endpoint}' used by multiple providers"
                        )
                    endpoints.append(endpoint)

        # Check for conflicting configurations
        mock_modes = {
            provider: config.get("use_mock", False)
            for provider, config in configs.items()
            if "use_mock" in config
        }

        if len(set(mock_modes.values())) > 1:
            warnings.append("Mixed mock and real provider configurations detected")

        is_valid = len(errors) == 0
        return ValidationResult(
            valid=is_valid, provider="combined", errors=errors, warnings=warnings
        )
