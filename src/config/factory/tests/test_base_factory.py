"""Comprehensive tests for base ConfigurationFactory functionality."""

from unittest.mock import MagicMock, patch

import pytest

from src.config.environments import BaseConfig
from src.config.factory.base_factory import ConfigurationFactory
from src.config.veo3_settings import Veo3Settings
from src.config.video_config import VideoGenerationConfig


class TestConfigurationFactoryBaseMethods:
    """Test suite for core ConfigurationFactory methods."""

    def test_get_base_config_default(self):
        """Test get_base_config with default parameters."""
        with patch("src.config.factory.base_factory.get_config") as mock_get_config:
            mock_config = MagicMock(spec=BaseConfig)
            mock_get_config.return_value = mock_config

            result = ConfigurationFactory.get_base_config()

            assert result is mock_config
            mock_get_config.assert_called_once_with(None)

    def test_get_base_config_with_config_name(self):
        """Test get_base_config with specific config name."""
        with patch("src.config.factory.base_factory.get_config") as mock_get_config:
            mock_config = MagicMock(spec=BaseConfig)
            mock_get_config.return_value = mock_config

            result = ConfigurationFactory.get_base_config("production")

            assert result is mock_config
            mock_get_config.assert_called_once_with("production")

    def test_get_video_config_default(self):
        """Test get_video_config with default parameters."""
        mock_base_config = MagicMock(spec=BaseConfig)
        mock_video_config = MagicMock(spec=VideoGenerationConfig)

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            with patch.object(VideoGenerationConfig, "from_config") as mock_from_config:
                mock_get_base.return_value = mock_base_config
                mock_from_config.return_value = mock_video_config

                result = ConfigurationFactory.get_video_config()

                assert result is mock_video_config
                mock_get_base.assert_called_once_with(None)
                mock_from_config.assert_called_once_with(mock_base_config)

    def test_get_video_config_with_config_name(self):
        """Test get_video_config with specific config name."""
        mock_base_config = MagicMock(spec=BaseConfig)
        mock_video_config = MagicMock(spec=VideoGenerationConfig)

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            with patch.object(VideoGenerationConfig, "from_config") as mock_from_config:
                mock_get_base.return_value = mock_base_config
                mock_from_config.return_value = mock_video_config

                result = ConfigurationFactory.get_video_config("testing")

                assert result is mock_video_config
                mock_get_base.assert_called_once_with("testing")
                mock_from_config.assert_called_once_with(mock_base_config)

    def test_clear_cache_compatibility(self):
        """Test clear_cache method maintains compatibility."""
        # Should not raise any errors and return None
        result = ConfigurationFactory.clear_cache()
        assert result is None

    def test_create_generation_params_defaults(self):
        """Test create_generation_params_defaults method."""
        mock_video_config = MagicMock()
        mock_video_config.default_width = 1920
        mock_video_config.default_height = 1080
        mock_video_config.default_duration = 5
        mock_video_config.default_model = "sora-v1"

        with patch.object(ConfigurationFactory, "get_video_config") as mock_get_video:
            mock_get_video.return_value = mock_video_config

            result = ConfigurationFactory.create_generation_params_defaults()

            expected = {
                "width": 1920,
                "height": 1080,
                "duration": 5,
                "model": "sora-v1",
            }
            assert result == expected
            mock_get_video.assert_called_once_with(None)

    def test_create_generation_params_defaults_with_config(self):
        """Test create_generation_params_defaults with specific config."""
        mock_video_config = MagicMock()
        mock_video_config.default_width = 1280
        mock_video_config.default_height = 720
        mock_video_config.default_duration = 10
        mock_video_config.default_model = "sora-v2"

        with patch.object(ConfigurationFactory, "get_video_config") as mock_get_video:
            mock_get_video.return_value = mock_video_config

            result = ConfigurationFactory.create_generation_params_defaults(
                "production"
            )

            expected = {
                "width": 1280,
                "height": 720,
                "duration": 10,
                "model": "sora-v2",
            }
            assert result == expected
            mock_get_video.assert_called_once_with("production")

    def test_validate_generation_params(self):
        """Test validate_generation_params method."""
        mock_video_config = MagicMock()
        mock_video_config.validate_parameters.return_value = {"error": "Invalid width"}

        with patch.object(ConfigurationFactory, "get_video_config") as mock_get_video:
            mock_get_video.return_value = mock_video_config

            result = ConfigurationFactory.validate_generation_params(
                width=1920, height=1080, duration=5, prompt="Test prompt"
            )

            assert result == {"error": "Invalid width"}
            mock_get_video.assert_called_once_with(None)
            mock_video_config.validate_parameters.assert_called_once_with(
                1920, 1080, 5, "Test prompt"
            )

    def test_validate_generation_params_with_config(self):
        """Test validate_generation_params with specific config."""
        mock_video_config = MagicMock()
        mock_video_config.validate_parameters.return_value = {}

        with patch.object(ConfigurationFactory, "get_video_config") as mock_get_video:
            mock_get_video.return_value = mock_video_config

            result = ConfigurationFactory.validate_generation_params(
                width=1280,
                height=720,
                duration=3,
                prompt="Test prompt",
                config_name="testing",
            )

            assert result == {}
            mock_get_video.assert_called_once_with("testing")
            mock_video_config.validate_parameters.assert_called_once_with(
                1280, 720, 3, "Test prompt"
            )


class TestConfigurationFactoryAzureConfig:
    """Test suite for Azure configuration methods."""

    def test_get_azure_config_default(self):
        """Test get_azure_config with default parameters."""
        mock_base_config = MagicMock()
        mock_base_config.AZURE_OPENAI_ENDPOINT = "https://test.azure.com"
        mock_base_config.AZURE_OPENAI_API_KEY = "test-key"
        mock_base_config.AZURE_OPENAI_API_VERSION = "preview"
        mock_base_config.AZURE_OPENAI_DEPLOYMENT_NAME = "sora"

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_get_base.return_value = mock_base_config

            result = ConfigurationFactory.get_azure_config()

            expected = {
                "endpoint": "https://test.azure.com",
                "api_key": "test-key",
                "api_version": "preview",
                "deployment_name": "sora",
            }
            assert result == expected
            mock_get_base.assert_called_once_with(None)

    def test_get_azure_config_with_config_name(self):
        """Test get_azure_config with specific config name."""
        mock_base_config = MagicMock()
        mock_base_config.AZURE_OPENAI_ENDPOINT = "https://prod.azure.com"
        mock_base_config.AZURE_OPENAI_API_KEY = "prod-key"
        mock_base_config.AZURE_OPENAI_API_VERSION = "2023-12-01-preview"
        mock_base_config.AZURE_OPENAI_DEPLOYMENT_NAME = "sora-prod"

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_get_base.return_value = mock_base_config

            result = ConfigurationFactory.get_azure_config("production")

            expected = {
                "endpoint": "https://prod.azure.com",
                "api_key": "prod-key",
                "api_version": "2023-12-01-preview",
                "deployment_name": "sora-prod",
            }
            assert result == expected
            mock_get_base.assert_called_once_with("production")

    def test_get_azure_config_logging(self):
        """Test that get_azure_config includes proper logging."""
        mock_base_config = MagicMock()
        mock_base_config.AZURE_OPENAI_ENDPOINT = "https://test.azure.com"
        mock_base_config.AZURE_OPENAI_API_KEY = "test-key"
        mock_base_config.AZURE_OPENAI_API_VERSION = "preview"
        mock_base_config.AZURE_OPENAI_DEPLOYMENT_NAME = "sora"

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            with patch("src.config.factory.base_factory.logger") as mock_logger:
                mock_get_base.return_value = mock_base_config

                result = ConfigurationFactory.get_azure_config()

                # Verify logging calls were made
                assert mock_logger.info.call_count >= 2
                log_messages = [
                    call.args[0] for call in mock_logger.info.call_args_list
                ]
                assert any("get_azure_config called" in msg for msg in log_messages)


class TestConfigurationFactoryAppConfig:
    """Test suite for Flask app configuration methods."""

    def test_get_app_config_default(self):
        """Test get_app_config with default parameters."""
        mock_base_config = MagicMock()
        mock_base_config.SECRET_KEY = "test-secret"
        mock_base_config.UPLOAD_FOLDER = "/tmp/uploads"
        mock_base_config.MAX_CONTENT_LENGTH = 16 * 1024 * 1024
        mock_base_config.get_database_url.return_value = "sqlite:///test.db"
        mock_base_config.SQLALCHEMY_TRACK_MODIFICATIONS = False

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_get_base.return_value = mock_base_config

            result = ConfigurationFactory.get_app_config()

            expected = {
                "SECRET_KEY": "test-secret",
                "UPLOAD_FOLDER": "/tmp/uploads",
                "MAX_CONTENT_LENGTH": 16 * 1024 * 1024,
                "SQLALCHEMY_DATABASE_URI": "sqlite:///test.db",
                "SQLALCHEMY_TRACK_MODIFICATIONS": False,
            }
            assert result == expected
            mock_get_base.assert_called_once_with(None)
            mock_base_config.get_database_url.assert_called_once()

    def test_get_app_config_with_config_name(self):
        """Test get_app_config with specific config name."""
        mock_base_config = MagicMock()
        mock_base_config.SECRET_KEY = "prod-secret"
        mock_base_config.UPLOAD_FOLDER = "/var/uploads"
        mock_base_config.MAX_CONTENT_LENGTH = 50 * 1024 * 1024
        mock_base_config.get_database_url.return_value = (
            "******************************"
        )
        mock_base_config.SQLALCHEMY_TRACK_MODIFICATIONS = False

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_get_base.return_value = mock_base_config

            result = ConfigurationFactory.get_app_config("production")

            expected = {
                "SECRET_KEY": "prod-secret",
                "UPLOAD_FOLDER": "/var/uploads",
                "MAX_CONTENT_LENGTH": 50 * 1024 * 1024,
                "SQLALCHEMY_DATABASE_URI": "******************************",
                "SQLALCHEMY_TRACK_MODIFICATIONS": False,
            }
            assert result == expected
            mock_get_base.assert_called_once_with("production")


class TestConfigurationFactoryVeo3Settings:
    """Test suite for Veo3Settings methods."""

    def test_get_veo3_settings_default(self):
        """Test get_veo3_settings method."""
        with patch(
            "src.config.factory.base_factory.get_cached_veo3_settings"
        ) as mock_cached:
            mock_settings = MagicMock(spec=Veo3Settings)
            mock_cached.return_value = mock_settings

            result = ConfigurationFactory.get_veo3_settings()

            assert result is mock_settings
            mock_cached.assert_called_once()

    def test_get_veo3_settings_ignores_config_name(self):
        """Test that get_veo3_settings ignores config_name parameter."""
        with patch(
            "src.config.factory.base_factory.get_cached_veo3_settings"
        ) as mock_cached:
            mock_settings = MagicMock(spec=Veo3Settings)
            mock_cached.return_value = mock_settings

            result = ConfigurationFactory.get_veo3_settings("production")

            # config_name should be ignored for Veo3Settings
            assert result is mock_settings
            mock_cached.assert_called_once()

    def test_get_veo3_settings_logging(self):
        """Test that get_veo3_settings includes debug logging."""
        with patch(
            "src.config.factory.base_factory.get_cached_veo3_settings"
        ) as mock_cached:
            with patch("src.config.factory.base_factory.logger") as mock_logger:
                mock_settings = MagicMock(spec=Veo3Settings)
                mock_cached.return_value = mock_settings

                result = ConfigurationFactory.get_veo3_settings()

                assert result is mock_settings
                mock_logger.debug.assert_called_once_with(
                    "ConfigurationFactory.get_veo3_settings() called"
                )


class TestConfigurationFactoryErrorHandling:
    """Test error handling in ConfigurationFactory methods."""

    def test_get_base_config_error_propagation(self):
        """Test that errors from get_config are properly propagated."""
        with patch("src.config.factory.base_factory.get_config") as mock_get_config:
            mock_get_config.side_effect = ValueError("Invalid config name")

            with pytest.raises(ValueError, match="Invalid config name"):
                ConfigurationFactory.get_base_config("invalid")

    def test_get_video_config_error_propagation(self):
        """Test that errors from VideoGenerationConfig.from_config are propagated."""
        mock_base_config = MagicMock(spec=BaseConfig)

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            with patch.object(VideoGenerationConfig, "from_config") as mock_from_config:
                mock_get_base.return_value = mock_base_config
                mock_from_config.side_effect = ValueError("Invalid video config")

                with pytest.raises(ValueError, match="Invalid video config"):
                    ConfigurationFactory.get_video_config()

    def test_get_azure_config_missing_attributes(self):
        """Test get_azure_config with missing base config attributes."""
        mock_base_config = MagicMock()
        # Remove one required attribute
        del mock_base_config.AZURE_OPENAI_API_VERSION

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_get_base.return_value = mock_base_config

            with pytest.raises(AttributeError):
                ConfigurationFactory.get_azure_config()


class TestConfigurationFactoryIntegration:
    """Integration tests for ConfigurationFactory methods."""

    def test_method_chaining_compatibility(self):
        """Test that methods can be chained properly."""
        mock_base_config = MagicMock()
        mock_video_config = MagicMock()
        mock_video_config.validate_parameters.return_value = {}

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            with patch.object(
                ConfigurationFactory, "get_video_config"
            ) as mock_get_video:
                mock_get_base.return_value = mock_base_config
                mock_get_video.return_value = mock_video_config

                # Test that methods work together
                base = ConfigurationFactory.get_base_config("test")
                video = ConfigurationFactory.get_video_config("test")
                validation = ConfigurationFactory.validate_generation_params(
                    1920, 1080, 5, "test", "test"
                )

                assert base is mock_base_config
                assert video is mock_video_config
                assert validation == {}

    def test_class_method_consistency(self):
        """Test that all methods are properly defined as class methods."""
        methods_to_check = [
            "get_base_config",
            "get_video_config",
            "clear_cache",
            "create_generation_params_defaults",
            "validate_generation_params",
            "get_azure_config",
            "get_app_config",
            "get_veo3_settings",
        ]

        for method_name in methods_to_check:
            method = getattr(ConfigurationFactory, method_name)
            assert callable(method), f"{method_name} should be callable"
            # Check it's a classmethod by verifying it can be called on the class
            assert hasattr(method, "__func__"), f"{method_name} should be a classmethod"
