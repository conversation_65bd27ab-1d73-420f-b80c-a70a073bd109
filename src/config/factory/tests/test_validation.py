"""Comprehensive tests for configuration validation functionality."""


import pytest

from src.config.factory.validation import ConfigurationValidator, ValidationResult


class TestValidationResult:
    """Test suite for ValidationResult model."""

    def test_validation_result_creation(self):
        """Test ValidationResult model creation."""
        result = ValidationResult(
            valid=True, provider="azure_sora", errors=[], warnings=["Minor warning"]
        )

        assert result.valid is True
        assert result.provider == "azure_sora"
        assert result.errors == []
        assert result.warnings == ["Minor warning"]

    def test_validation_result_defaults(self):
        """Test ValidationResult with default values."""
        result = ValidationResult(valid=False, provider="google_veo3")

        assert result.valid is False
        assert result.provider == "google_veo3"
        assert result.errors == []  # Default empty list
        assert result.warnings == []  # Default empty list

    def test_validation_result_immutable(self):
        """Test that ValidationResult is immutable."""
        result = ValidationResult(valid=True, provider="test")

        with pytest.raises(TypeError):
            result.valid = False  # Should not be modifiable


class TestConfigurationValidatorAzureSora:
    """Test suite for Azure Sora configuration validation."""

    def test_validate_azure_sora_config_valid(self):
        """Test validation of valid Azure Sora configuration."""
        config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-************************************",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is True
        assert result.provider == "azure_sora"
        assert len(result.errors) == 0

    def test_validate_azure_sora_config_missing_fields(self):
        """Test validation with missing required fields."""
        config = {
            "endpoint": "https://test.azure.com",
            # Missing api_key, api_version, deployment_name
        }

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is False
        assert result.provider == "azure_sora"
        assert len(result.errors) == 3  # Three missing fields

        error_messages = " ".join(result.errors)
        assert "api_key" in error_messages
        assert "api_version" in error_messages
        assert "deployment_name" in error_messages

    def test_validate_azure_sora_config_invalid_endpoint(self):
        """Test validation with invalid endpoint."""
        config = {
            "endpoint": "http://insecure.azure.com",  # HTTP instead of HTTPS
            "api_key": "test-************************************",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is False
        assert "HTTPS" in " ".join(result.errors)

    def test_validate_azure_sora_config_invalid_api_version(self):
        """Test validation with potentially invalid API version."""
        config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-************************************",
            "api_version": "unknown-version",
            "deployment_name": "sora",
        }

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is True  # Should be valid but with warnings
        assert len(result.warnings) > 0
        assert "may not be supported" in " ".join(result.warnings)

    def test_validate_azure_sora_config_short_api_key(self):
        """Test validation with short API key."""
        config = {
            "endpoint": "https://test.azure.com",
            "api_key": "short-key",  # Too short
            "api_version": "preview",
            "deployment_name": "sora",
        }

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is True  # Should be valid but with warnings
        assert len(result.warnings) > 0
        assert "shorter than expected" in " ".join(result.warnings)

    def test_validate_azure_sora_config_empty_values(self):
        """Test validation with empty string values."""
        config = {
            "endpoint": "",
            "api_key": "",
            "api_version": "",
            "deployment_name": "",
        }

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is False
        assert len(result.errors) == 4  # All fields are empty


class TestConfigurationValidatorGoogleVeo3:
    """Test suite for Google Veo3 configuration validation."""

    def test_validate_google_veo3_config_valid(self):
        """Test validation of valid Google Veo3 configuration."""
        config = {
            "project_id": "test-project-123",
            "use_mock": True,
            "client_id": "test-client-id",
            "client_secret": "test-client-secret",
            "timeout": 300,
            "max_retries": 3,
        }

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is True
        assert result.provider == "google_veo3"
        assert len(result.errors) == 0

    def test_validate_google_veo3_config_missing_project_id(self):
        """Test validation with missing project ID."""
        config = {"use_mock": True, "timeout": 300}

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is False
        assert "project_id" in " ".join(result.errors)

    def test_validate_google_veo3_config_invalid_project_id(self):
        """Test validation with invalid project ID characters."""
        config = {
            "project_id": "test@project#123",  # Invalid characters
            "use_mock": True,
        }

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is False
        assert "invalid characters" in " ".join(result.errors)

    def test_validate_google_veo3_config_invalid_mock_type(self):
        """Test validation with non-boolean mock value."""
        config = {"project_id": "test-project-123", "use_mock": "not-a-boolean"}

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is False
        assert "boolean" in " ".join(result.errors)

    def test_validate_google_veo3_config_invalid_timeout(self):
        """Test validation with invalid timeout values."""
        # Test negative timeout
        config = {"project_id": "test-project-123", "timeout": -1}

        result = ConfigurationValidator.validate_google_veo3_config(config)
        assert result.valid is False
        assert "positive integer" in " ".join(result.errors)

        # Test non-integer timeout
        config["timeout"] = "not-an-integer"
        result = ConfigurationValidator.validate_google_veo3_config(config)
        assert result.valid is False

    def test_validate_google_veo3_config_high_timeout_warning(self):
        """Test validation with very high timeout value."""
        config = {
            "project_id": "test-project-123",
            "timeout": 7200,  # 2 hours
        }

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is True
        assert len(result.warnings) > 0
        assert "very high" in " ".join(result.warnings)

    def test_validate_google_veo3_config_invalid_retries(self):
        """Test validation with invalid retry values."""
        # Test negative retries
        config = {"project_id": "test-project-123", "max_retries": -1}

        result = ConfigurationValidator.validate_google_veo3_config(config)
        assert result.valid is False
        assert "non-negative" in " ".join(result.errors)

    def test_validate_google_veo3_config_high_retries_warning(self):
        """Test validation with very high retry value."""
        config = {"project_id": "test-project-123", "max_retries": 15}

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is True
        assert len(result.warnings) > 0
        assert "very high" in " ".join(result.warnings)

    def test_validate_google_veo3_config_non_mock_missing_auth(self):
        """Test validation for non-mock mode with missing authentication."""
        config = {
            "project_id": "test-project-123",
            "use_mock": False,
            # Missing client_id and client_secret
        }

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is True  # Valid but with warnings
        assert len(result.warnings) > 0
        assert "Authentication may fail" in " ".join(result.warnings)


class TestConfigurationValidatorUnified:
    """Test suite for unified provider validation."""

    def test_validate_provider_configuration_azure(self):
        """Test unified validation for Azure Sora provider."""
        config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-************************************",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        result = ConfigurationValidator.validate_provider_configuration(
            "azure_sora", config
        )

        assert result.valid is True
        assert result.provider == "azure_sora"

    def test_validate_provider_configuration_google_veo3(self):
        """Test unified validation for Google Veo3 provider."""
        config = {"project_id": "test-project-123", "use_mock": True}

        result = ConfigurationValidator.validate_provider_configuration(
            "google_veo3", config
        )

        assert result.valid is True
        assert result.provider == "google_veo3"

    def test_validate_provider_configuration_unknown_provider(self):
        """Test unified validation with unknown provider."""
        config = {"test": "value"}

        with pytest.raises(ValueError, match="Unsupported provider: unknown"):
            ConfigurationValidator.validate_provider_configuration("unknown", config)


class TestConfigurationValidatorEnvironment:
    """Test suite for environment compatibility validation."""

    def test_validate_environment_compatibility_production_valid(self):
        """Test environment validation for valid production configuration."""
        config = {
            "project_id": "prod-project-123",
            "use_mock": False,
            "client_secret": "production-secret",
            "timeout": 300,
        }

        result = ConfigurationValidator.validate_environment_compatibility(
            "google_veo3", "production", config
        )

        assert result.valid is True
        assert result.provider == "google_veo3"

    def test_validate_environment_compatibility_production_mock_error(self):
        """Test environment validation for production with mock enabled."""
        config = {
            "project_id": "prod-project-123",
            "use_mock": True,  # Should not be enabled in production
        }

        result = ConfigurationValidator.validate_environment_compatibility(
            "google_veo3", "production", config
        )

        assert result.valid is False
        assert "Mock mode must be disabled" in " ".join(result.errors)

    def test_validate_environment_compatibility_production_missing_secret(self):
        """Test environment validation for production without client secret."""
        config = {
            "project_id": "prod-project-123",
            "use_mock": False,
            # Missing client_secret
        }

        result = ConfigurationValidator.validate_environment_compatibility(
            "google_veo3", "production", config
        )

        assert result.valid is False
        assert "Client secret is required" in " ".join(result.errors)

    def test_validate_environment_compatibility_production_short_timeout(self):
        """Test environment validation for production with short timeout."""
        config = {
            "project_id": "prod-project-123",
            "use_mock": False,
            "client_secret": "secret",
            "timeout": 30,  # Too short for production
        }

        result = ConfigurationValidator.validate_environment_compatibility(
            "google_veo3", "production", config
        )

        assert result.valid is True  # Valid but with warnings
        assert "too short for production" in " ".join(result.warnings)

    def test_validate_environment_compatibility_local_recommend_mock(self):
        """Test environment validation for local without mock mode."""
        config = {"project_id": "local-project-123", "use_mock": False}

        result = ConfigurationValidator.validate_environment_compatibility(
            "google_veo3", "local", config
        )

        assert result.valid is True
        assert "Consider enabling mock mode" in " ".join(result.warnings)

    def test_validate_environment_compatibility_docker_timeout(self):
        """Test environment validation for Docker with short timeout."""
        config = {
            "project_id": "docker-project-123",
            "timeout": 120,  # Short for Docker
        }

        result = ConfigurationValidator.validate_environment_compatibility(
            "google_veo3", "docker", config
        )

        assert result.valid is True
        assert "increasing timeout for Docker" in " ".join(result.warnings)


class TestConfigurationValidatorCombination:
    """Test suite for provider combination validation."""

    def test_validate_provider_combination_valid(self):
        """Test validation of valid provider combination."""
        configs = {
            "azure_sora": {"endpoint": "https://azure1.com", "api_key": "azure-key"},
            "google_veo3": {"project_id": "veo3-project-123", "use_mock": True},
        }

        result = ConfigurationValidator.validate_provider_combination(configs)

        assert result.valid is True
        assert result.provider == "combined"

    def test_validate_provider_combination_duplicate_project_id(self):
        """Test validation with duplicate project IDs."""
        configs = {
            "google_veo3_1": {"project_id": "shared-project-123"},
            "google_veo3_2": {
                "project_id": "shared-project-123"  # Duplicate
            },
        }

        result = ConfigurationValidator.validate_provider_combination(configs)

        assert result.valid is True  # Valid but with warnings
        assert "used by multiple providers" in " ".join(result.warnings)

    def test_validate_provider_combination_duplicate_endpoint(self):
        """Test validation with duplicate endpoints."""
        configs = {
            "azure_sora_1": {"endpoint": "https://shared.azure.com"},
            "azure_sora_2": {
                "endpoint": "https://shared.azure.com"  # Duplicate
            },
        }

        result = ConfigurationValidator.validate_provider_combination(configs)

        assert result.valid is True  # Valid but with warnings
        assert "used by multiple providers" in " ".join(result.warnings)

    def test_validate_provider_combination_mixed_mock_modes(self):
        """Test validation with mixed mock and real configurations."""
        configs = {
            "provider_1": {"use_mock": True},
            "provider_2": {
                "use_mock": False  # Different mock mode
            },
        }

        result = ConfigurationValidator.validate_provider_combination(configs)

        assert result.valid is True  # Valid but with warnings
        assert "Mixed mock and real provider" in " ".join(result.warnings)

    def test_validate_provider_combination_empty_configs(self):
        """Test validation with empty configuration set."""
        configs = {}

        result = ConfigurationValidator.validate_provider_combination(configs)

        assert result.valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 0


class TestConfigurationValidatorEdgeCases:
    """Test suite for edge cases and error conditions."""

    def test_validate_with_none_values(self):
        """Test validation with None values in configuration."""
        config = {"project_id": None, "use_mock": None, "timeout": None}

        result = ConfigurationValidator.validate_google_veo3_config(config)

        assert result.valid is False
        # Should handle None values gracefully

    def test_validate_with_empty_config(self):
        """Test validation with completely empty configuration."""
        config = {}

        result = ConfigurationValidator.validate_azure_sora_config(config)

        assert result.valid is False
        assert len(result.errors) >= 4  # All required fields missing

    def test_validate_with_unexpected_types(self):
        """Test validation with unexpected data types."""
        config = {
            "project_id": 12345,  # Should be string
            "use_mock": "maybe",  # Should be boolean
            "timeout": "fast",  # Should be integer
        }

        result = ConfigurationValidator.validate_google_veo3_config(config)

        # Should handle type errors gracefully
        assert result.valid is False or len(result.warnings) > 0
