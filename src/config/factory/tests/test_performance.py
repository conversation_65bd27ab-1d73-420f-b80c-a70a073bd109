"""Performance tests for configuration factory functionality."""

import time
from unittest.mock import MagicMock, patch

from src.config.factory.base_factory import ConfigurationFactory
from src.config.factory.provider_factory import ProviderConfigurationManager


class TestConfigurationFactoryPerformance:
    """Test suite for configuration factory performance requirements."""

    def test_base_config_creation_performance(self):
        """Test that base config creation meets <10ms target."""
        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_config = MagicMock()
            mock_get_base.return_value = mock_config

            start_time = time.time()

            # Test configuration creation
            for _ in range(10):  # Test multiple calls
                ConfigurationFactory.get_base_config()

            total_time = (time.time() - start_time) * 1000
            avg_time = total_time / 10

            assert avg_time < 10, (
                f"Base config creation took {avg_time:.2f}ms, should be <10ms"
            )

    def test_provider_config_creation_performance(self):
        """Test that provider config creation meets <10ms target."""
        mock_azure_config = {"endpoint": "https://test.azure.com"}

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config

            start_time = time.time()

            # Test Azure provider config creation
            for _ in range(10):
                ProviderConfigurationManager.create_provider_config("azure_sora")

            total_time = (time.time() - start_time) * 1000
            avg_time = total_time / 10

            assert avg_time < 10, (
                f"Provider config creation took {avg_time:.2f}ms, should be <10ms"
            )

    def test_provider_switching_performance(self):
        """Test that provider switching meets <50ms target."""
        mock_azure_config = {"endpoint": "https://test.azure.com"}
        mock_veo3_config = MagicMock()
        mock_veo3_config.to_dict.return_value = {"project_id": "test-123"}

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            with patch.object(
                ProviderConfigurationManager, "create_veo3_config"
            ) as mock_create_veo3:
                mock_get_azure.return_value = mock_azure_config
                mock_create_veo3.return_value = mock_veo3_config

                start_time = time.time()

                # Test switching between providers
                for i in range(5):
                    if i % 2 == 0:
                        ProviderConfigurationManager.create_provider_config(
                            "azure_sora"
                        )
                    else:
                        ProviderConfigurationManager.create_provider_config(
                            "google_veo3"
                        )

                total_time = (time.time() - start_time) * 1000
                avg_time = total_time / 5

                assert avg_time < 50, (
                    f"Provider switching took {avg_time:.2f}ms, should be <50ms"
                )

    def test_settings_load_performance(self):
        """Test that settings loading meets <50ms target."""
        with patch(
            "src.config.factory.base_factory.get_cached_veo3_settings"
        ) as mock_cached:
            mock_settings = MagicMock()
            mock_cached.return_value = mock_settings

            start_time = time.time()

            # Test settings loading
            for _ in range(10):
                ConfigurationFactory.get_veo3_settings()

            total_time = (time.time() - start_time) * 1000
            avg_time = total_time / 10

            assert avg_time < 50, (
                f"Settings loading took {avg_time:.2f}ms, should be <50ms"
            )

    def test_validation_performance(self):
        """Test that configuration validation meets <100ms target."""
        from src.config.factory.validation import ConfigurationValidator

        azure_config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-************************************",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        start_time = time.time()

        # Test validation performance
        for _ in range(10):
            ConfigurationValidator.validate_azure_sora_config(azure_config)

        total_time = (time.time() - start_time) * 1000
        avg_time = total_time / 10

        assert avg_time < 100, f"Validation took {avg_time:.2f}ms, should be <100ms"

    def test_combined_workflow_performance(self):
        """Test complete configuration workflow performance."""
        mock_azure_config = {"endpoint": "https://test.azure.com"}
        mock_settings = MagicMock()
        mock_settings.get_provider_availability.return_value = {"azure_sora": True}

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            with patch.object(
                ConfigurationFactory, "get_veo3_settings"
            ) as mock_get_settings:
                mock_get_azure.return_value = mock_azure_config
                mock_get_settings.return_value = mock_settings

                start_time = time.time()

                # Test complete workflow
                for _ in range(5):
                    # Get provider availability
                    ProviderConfigurationManager.get_provider_availability()

                    # Create provider config
                    ProviderConfigurationManager.create_provider_config("azure_sora")

                    # Validate config
                    from src.config.factory.validation import ConfigurationValidator

                    ConfigurationValidator.validate_azure_sora_config(mock_azure_config)

                total_time = (time.time() - start_time) * 1000
                avg_time = total_time / 5

                # Complete workflow should be reasonably fast
                assert avg_time < 200, (
                    f"Complete workflow took {avg_time:.2f}ms, should be <200ms"
                )

    def test_concurrent_access_performance(self):
        """Test that configuration factory handles concurrent access efficiently."""
        import queue
        import threading

        mock_azure_config = {"endpoint": "https://test.azure.com"}
        results = queue.Queue()

        def worker():
            start_time = time.time()
            with patch.object(
                ConfigurationFactory, "get_azure_config"
            ) as mock_get_azure:
                mock_get_azure.return_value = mock_azure_config

                for _ in range(5):
                    ProviderConfigurationManager.create_provider_config("azure_sora")

            duration = (time.time() - start_time) * 1000
            results.put(duration)

        # Run multiple threads concurrently
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Check that all threads completed in reasonable time
        max_duration = 0
        while not results.empty():
            duration = results.get()
            max_duration = max(max_duration, duration)

        assert max_duration < 500, (
            f"Concurrent access took {max_duration:.2f}ms, should be <500ms"
        )


class TestMemoryUsageOptimization:
    """Test suite for memory usage optimization."""

    def test_no_memory_leaks_in_repeated_calls(self):
        """Test that repeated configuration calls don't cause memory leaks."""
        import gc

        mock_azure_config = {"endpoint": "https://test.azure.com"}

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config

            # Force garbage collection before test
            gc.collect()
            initial_objects = len(gc.get_objects())

            # Perform many configuration operations
            for _ in range(100):
                ProviderConfigurationManager.create_provider_config("azure_sora")

            # Force garbage collection after test
            gc.collect()
            final_objects = len(gc.get_objects())

            # Object count should not grow significantly
            object_growth = final_objects - initial_objects

            # Allow some growth but not excessive
            assert object_growth < 1000, (
                f"Object count grew by {object_growth}, possible memory leak"
            )

    def test_configuration_object_reuse(self):
        """Test that configuration objects are efficiently reused."""
        mock_azure_config = {"endpoint": "https://test.azure.com"}

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config

            # Create multiple configurations
            configs = []
            for _ in range(10):
                config = ProviderConfigurationManager.create_provider_config(
                    "azure_sora"
                )
                configs.append(config)

            # All configs should have the same content but be separate instances
            for i in range(1, len(configs)):
                assert configs[i] == configs[0], (
                    "Configurations should have same content"
                )
                # Note: configs are separate instances which is expected behavior

    def test_efficient_validation_caching(self):
        """Test that validation is performed efficiently."""
        from src.config.factory.validation import ConfigurationValidator

        config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-key",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        start_time = time.time()

        # Perform same validation multiple times
        results = []
        for _ in range(20):
            result = ConfigurationValidator.validate_azure_sora_config(config)
            results.append(result)

        total_time = (time.time() - start_time) * 1000

        # Validation should be fast even when repeated
        assert total_time < 100, (
            f"Repeated validation took {total_time:.2f}ms, should be <100ms"
        )

        # All results should be identical
        for result in results[1:]:
            assert result.valid == results[0].valid
            assert result.errors == results[0].errors
