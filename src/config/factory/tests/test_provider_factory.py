"""Comprehensive tests for provider factory functionality."""

import os
from unittest.mock import MagicMock, patch

import pytest

from src.config.factory.provider_factory import (
    ProviderConfigurationFactory,
    ProviderConfigurationManager,
)
from src.config.veo3_settings import Veo3ProviderConfig


class TestProviderConfigurationManager:
    """Test suite for ProviderConfigurationManager class."""

    def test_create_veo3_config_with_environment_defaults(self):
        """Test create_veo3_config respects environment variables."""
        test_env = {
            "GOOGLE_PROJECT_ID": "test-project-123",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "test-client",
            "VEO3_TIMEOUT": "600",
            "VEO3_MAX_RETRIES": "5",
        }

        mock_veo3_settings = MagicMock()
        mock_veo3_settings.GOOGLE_PROJECT_ID = "test-project-123"
        mock_veo3_settings.USE_MOCK_VEO = True
        mock_veo3_settings.GOOGLE_CLIENT_ID = "test-client"
        mock_veo3_settings.GOOGLE_CLIENT_SECRET = None
        mock_veo3_settings.VEO3_TIMEOUT = 600
        mock_veo3_settings.VEO3_MAX_RETRIES = 5
        mock_veo3_settings.VEO3_MODEL_VERSION = "v1"
        mock_veo3_settings.VEO3_RETRY_DELAY = 2
        mock_veo3_settings.VEO3_GENERATION_TIMEOUT = 1800
        mock_veo3_settings.VEO3_RATE_LIMIT_RPM = 60

        with patch.dict(os.environ, test_env):
            with patch(
                "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
            ) as mock_get_settings:
                mock_get_settings.return_value = mock_veo3_settings

                config = ProviderConfigurationManager.create_veo3_config()

                assert isinstance(config, Veo3ProviderConfig)
                assert config.project_id == "test-project-123"
                assert config.use_mock is True
                assert config.client_id == "test-client"
                assert config.timeout == 600
                assert config.max_retries == 5

    def test_create_veo3_config_with_overrides(self):
        """Test create_veo3_config applies overrides over environment variables."""
        mock_veo3_settings = MagicMock()
        mock_veo3_settings.GOOGLE_PROJECT_ID = "env-project-id"
        mock_veo3_settings.USE_MOCK_VEO = True
        mock_veo3_settings.GOOGLE_CLIENT_ID = "env-client"
        mock_veo3_settings.GOOGLE_CLIENT_SECRET = None
        mock_veo3_settings.VEO3_TIMEOUT = 300
        mock_veo3_settings.VEO3_MAX_RETRIES = 3
        mock_veo3_settings.VEO3_MODEL_VERSION = "v1"
        mock_veo3_settings.VEO3_RETRY_DELAY = 2
        mock_veo3_settings.VEO3_GENERATION_TIMEOUT = 1800
        mock_veo3_settings.VEO3_RATE_LIMIT_RPM = 60

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_get_settings.return_value = mock_veo3_settings

            config = ProviderConfigurationManager.create_veo3_config(
                project_id="override-project-id", use_mock=False, timeout=900
            )

            # Overrides should take precedence
            assert config.project_id == "override-project-id"
            assert config.use_mock is False
            assert config.timeout == 900
            # Environment values for non-overridden fields
            assert config.max_retries == 3

    def test_create_veo3_config_missing_project_id(self):
        """Test create_veo3_config raises error when project_id is missing."""
        mock_veo3_settings = MagicMock()
        mock_veo3_settings.GOOGLE_PROJECT_ID = None

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_get_settings.return_value = mock_veo3_settings

            with pytest.raises(ValueError, match="GOOGLE_PROJECT_ID is required"):
                ProviderConfigurationManager.create_veo3_config()

    def test_create_veo3_config_secure_secret_handling(self):
        """Test secure handling of client secret."""
        mock_secret = MagicMock()
        mock_secret.get_secret_value.return_value = "env-secret"

        mock_veo3_settings = MagicMock()
        mock_veo3_settings.GOOGLE_PROJECT_ID = "test-project-123"
        mock_veo3_settings.USE_MOCK_VEO = False
        mock_veo3_settings.GOOGLE_CLIENT_ID = "test-client"
        mock_veo3_settings.GOOGLE_CLIENT_SECRET = mock_secret
        mock_veo3_settings.VEO3_TIMEOUT = 300
        mock_veo3_settings.VEO3_MAX_RETRIES = 3
        mock_veo3_settings.VEO3_MODEL_VERSION = "v1"
        mock_veo3_settings.VEO3_RETRY_DELAY = 2
        mock_veo3_settings.VEO3_GENERATION_TIMEOUT = 1800
        mock_veo3_settings.VEO3_RATE_LIMIT_RPM = 60

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_get_settings.return_value = mock_veo3_settings

            # Test environment secret is used
            config = ProviderConfigurationManager.create_veo3_config()
            assert config.client_secret == "env-secret"

            # Test override secret takes precedence
            config_override = ProviderConfigurationManager.create_veo3_config(
                client_secret="override-secret"
            )
            assert config_override.client_secret == "override-secret"

    def test_get_provider_availability(self):
        """Test get_provider_availability returns availability status."""
        mock_availability = {"azure_sora": True, "google_veo3": True}

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.get_provider_availability.return_value = mock_availability
            mock_get_settings.return_value = mock_settings

            availability = ProviderConfigurationManager.get_provider_availability()

            assert availability == mock_availability
            mock_settings.get_provider_availability.assert_called_once()

    def test_get_default_provider(self):
        """Test get_default_provider returns configured default."""
        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.DEFAULT_PROVIDER = "google_veo3"
            mock_get_settings.return_value = mock_settings

            default_provider = ProviderConfigurationManager.get_default_provider()

            assert default_provider == "google_veo3"

    def test_create_provider_config_azure_sora(self):
        """Test create_provider_config for Azure Sora provider."""
        mock_azure_config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-key",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_azure_config"
        ) as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config.copy()

            config = ProviderConfigurationManager.create_provider_config("azure_sora")

            assert config == mock_azure_config
            mock_get_azure.assert_called_once()

    def test_create_provider_config_azure_sora_with_overrides(self):
        """Test create_provider_config for Azure Sora with overrides."""
        mock_azure_config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-key",
            "api_version": "preview",
        }

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_azure_config"
        ) as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config.copy()

            config = ProviderConfigurationManager.create_provider_config(
                "azure_sora", api_version="2025-01-01"
            )

            assert config["api_version"] == "2025-01-01"  # Override applied
            assert config["endpoint"] == "https://test.azure.com"  # Original value

    def test_create_provider_config_google_veo3(self):
        """Test create_provider_config for Google Veo3 provider."""
        mock_veo3_config = MagicMock()
        mock_veo3_config.to_dict.return_value = {
            "project_id": "test-project-123",
            "use_mock": True,
            "timeout": 300,
        }

        with patch.object(
            ProviderConfigurationManager, "create_veo3_config"
        ) as mock_create_veo3:
            mock_create_veo3.return_value = mock_veo3_config

            config = ProviderConfigurationManager.create_provider_config("google_veo3")

            assert isinstance(config, dict)
            assert config["project_id"] == "test-project-123"
            assert "use_mock" in config
            assert "timeout" in config
            mock_create_veo3.assert_called_once()

    def test_create_provider_config_unknown_provider(self):
        """Test create_provider_config raises error for unknown provider."""
        with pytest.raises(ValueError, match="Unknown provider: unknown_provider"):
            ProviderConfigurationManager.create_provider_config("unknown_provider")

    def test_validate_provider_configuration(self):
        """Test validate_provider_configuration delegates to settings."""
        mock_validation_result = {"provider": "azure_sora", "valid": True, "errors": []}

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.validate_provider_configuration.return_value = (
                mock_validation_result
            )
            mock_get_settings.return_value = mock_settings

            result = ProviderConfigurationManager.validate_provider_configuration(
                "azure_sora"
            )

            assert result == mock_validation_result
            mock_settings.validate_provider_configuration.assert_called_once_with(
                "azure_sora"
            )

    def test_get_provider_performance_config_azure_sora(self):
        """Test get_provider_performance_config for Azure Sora."""
        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_base_config"
        ) as mock_get_base:
            mock_base_config = MagicMock()
            mock_get_base.return_value = mock_base_config

            perf_config = ProviderConfigurationManager.get_provider_performance_config(
                "azure_sora"
            )

            assert "timeouts" in perf_config
            assert "retry_config" in perf_config
            assert "targets" in perf_config
            assert perf_config["timeouts"]["api_timeout"] == 300
            assert perf_config["targets"]["settings_load_time_ms"] == 50

    def test_get_provider_performance_config_google_veo3(self):
        """Test get_provider_performance_config for Google Veo3."""
        mock_perf_config = {
            "timeouts": {"api_timeout": 300},
            "retry_config": {"max_retries": 3},
            "targets": {"settings_load_time_ms": 50},
        }

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.get_performance_config.return_value = mock_perf_config
            mock_get_settings.return_value = mock_settings

            perf_config = ProviderConfigurationManager.get_provider_performance_config(
                "google_veo3"
            )

            assert perf_config == mock_perf_config
            mock_settings.get_performance_config.assert_called_once()

    def test_get_provider_performance_config_unknown_provider(self):
        """Test get_provider_performance_config raises error for unknown provider."""
        with pytest.raises(ValueError, match="Unknown provider: unknown_provider"):
            ProviderConfigurationManager.get_provider_performance_config(
                "unknown_provider"
            )


class TestProviderConfigurationFactory:
    """Test suite for ProviderConfigurationFactory class."""

    @patch("src.config.factory.provider_factory.ConfigurationService")
    def test_create_environment_aware_config_local(self, mock_config_service):
        """Test environment-aware config creation for local environment."""
        mock_config_service._detect_deployment_type.return_value = "local"

        with patch.object(
            ProviderConfigurationManager, "create_provider_config"
        ) as mock_create:
            mock_create.return_value = {"project_id": "test-project-123"}

            config = ProviderConfigurationFactory.create_environment_aware_config(
                "google_veo3"
            )

            # Should call with local environment defaults
            mock_create.assert_called_once()
            args, kwargs = mock_create.call_args
            assert args[0] == "google_veo3"
            assert kwargs["use_mock"] is True  # Local default
            assert kwargs["timeout"] == 120  # Local default
            assert kwargs["max_retries"] == 2  # Local default

    @patch("src.config.factory.provider_factory.ConfigurationService")
    def test_create_environment_aware_config_docker(self, mock_config_service):
        """Test environment-aware config creation for Docker environment."""
        mock_config_service._detect_deployment_type.return_value = "docker"

        with patch.object(
            ProviderConfigurationManager, "create_provider_config"
        ) as mock_create:
            mock_create.return_value = {"project_id": "test-project-123"}

            config = ProviderConfigurationFactory.create_environment_aware_config(
                "google_veo3"
            )

            # Should call with Docker environment defaults
            args, kwargs = mock_create.call_args
            assert kwargs["timeout"] == 600  # Docker default
            assert kwargs["max_retries"] == 5  # Docker default

    @patch("src.config.factory.provider_factory.ConfigurationService")
    def test_create_environment_aware_config_production(self, mock_config_service):
        """Test environment-aware config creation for production environment."""
        mock_config_service._detect_deployment_type.return_value = "production"

        with patch.object(
            ProviderConfigurationManager, "create_provider_config"
        ) as mock_create:
            mock_create.return_value = {"project_id": "test-project-123"}

            config = ProviderConfigurationFactory.create_environment_aware_config(
                "google_veo3"
            )

            # Should call with production environment defaults
            args, kwargs = mock_create.call_args
            assert kwargs["use_mock"] is False  # Production default
            assert kwargs["timeout"] == 300  # Production default
            assert kwargs["max_retries"] == 3  # Production default

    def test_create_environment_aware_config_with_overrides(self):
        """Test environment-aware config with explicit overrides."""
        with patch(
            "src.config.factory.provider_factory.ConfigurationService"
        ) as mock_service:
            mock_service._detect_deployment_type.return_value = "local"

            with patch.object(
                ProviderConfigurationManager, "create_provider_config"
            ) as mock_create:
                mock_create.return_value = {"project_id": "test-project-123"}

                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3",
                    timeout=999,  # Explicit override
                    custom_param="custom_value",  # Custom parameter
                )

                # Explicit overrides should take precedence over environment defaults
                args, kwargs = mock_create.call_args
                assert kwargs["timeout"] == 999  # Override value
                assert kwargs["custom_param"] == "custom_value"  # Custom param
                assert kwargs["use_mock"] is True  # Environment default (local)

    def test_get_optimal_provider_config_default_available(self):
        """Test optimal provider selection when default provider is available."""
        mock_availability = {"azure_sora": True, "google_veo3": True}
        mock_default_provider = "google_veo3"

        with patch.object(
            ProviderConfigurationManager, "get_provider_availability"
        ) as mock_avail:
            with patch.object(
                ProviderConfigurationManager, "get_default_provider"
            ) as mock_default:
                with patch.object(
                    ProviderConfigurationFactory, "create_environment_aware_config"
                ) as mock_create:
                    mock_avail.return_value = mock_availability
                    mock_default.return_value = mock_default_provider
                    mock_create.return_value = {"project_id": "test-project"}

                    config = ProviderConfigurationFactory.get_optimal_provider_config()

                    # Should select the default provider
                    mock_create.assert_called_once_with("google_veo3")
                    assert config["selected_provider"] == "google_veo3"
                    assert config["provider_availability"] == mock_availability

    def test_get_optimal_provider_config_fallback_azure(self):
        """Test optimal provider selection falls back to Azure when default unavailable."""
        mock_availability = {
            "azure_sora": True,
            "google_veo3": False,  # Default unavailable
        }
        mock_default_provider = "google_veo3"

        with patch.object(
            ProviderConfigurationManager, "get_provider_availability"
        ) as mock_avail:
            with patch.object(
                ProviderConfigurationManager, "get_default_provider"
            ) as mock_default:
                with patch.object(
                    ProviderConfigurationFactory, "create_environment_aware_config"
                ) as mock_create:
                    mock_avail.return_value = mock_availability
                    mock_default.return_value = mock_default_provider
                    mock_create.return_value = {"endpoint": "https://azure.com"}

                    config = ProviderConfigurationFactory.get_optimal_provider_config()

                    # Should fallback to Azure Sora
                    mock_create.assert_called_once_with("azure_sora")
                    assert config["selected_provider"] == "azure_sora"

    def test_get_optimal_provider_config_no_providers_available(self):
        """Test optimal provider selection raises error when no providers available."""
        mock_availability = {"azure_sora": False, "google_veo3": False}

        with patch.object(
            ProviderConfigurationManager, "get_provider_availability"
        ) as mock_avail:
            with patch.object(
                ProviderConfigurationManager, "get_default_provider"
            ) as mock_default:
                mock_avail.return_value = mock_availability
                mock_default.return_value = "azure_sora"

                with pytest.raises(
                    ValueError, match="No video generation providers are available"
                ):
                    ProviderConfigurationFactory.get_optimal_provider_config()


class TestProviderFactoryErrorHandling:
    """Test error handling in provider factory classes."""

    def test_create_environment_aware_config_invalid_provider(self):
        """Test error handling for invalid provider in environment-aware config."""
        with patch(
            "src.config.factory.provider_factory.ConfigurationService"
        ) as mock_service:
            mock_service._detect_deployment_type.return_value = "local"

            with patch.object(
                ProviderConfigurationManager, "create_provider_config"
            ) as mock_create:
                mock_create.side_effect = ValueError("Unknown provider: invalid")

                with pytest.raises(ValueError, match="Unknown provider: invalid"):
                    ProviderConfigurationFactory.create_environment_aware_config(
                        "invalid"
                    )

    @patch("src.config.factory.provider_factory.logger")
    def test_logging_in_provider_config_creation(self, mock_logger):
        """Test proper logging in provider configuration creation."""
        mock_veo3_settings = MagicMock()
        mock_veo3_settings.GOOGLE_PROJECT_ID = "test-project-123"
        mock_veo3_settings.USE_MOCK_VEO = True
        mock_veo3_settings.GOOGLE_CLIENT_ID = "test-client"
        mock_veo3_settings.GOOGLE_CLIENT_SECRET = None
        mock_veo3_settings.VEO3_TIMEOUT = 300
        mock_veo3_settings.VEO3_MAX_RETRIES = 3
        mock_veo3_settings.VEO3_MODEL_VERSION = "v1"
        mock_veo3_settings.VEO3_RETRY_DELAY = 2
        mock_veo3_settings.VEO3_GENERATION_TIMEOUT = 1800
        mock_veo3_settings.VEO3_RATE_LIMIT_RPM = 60

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_veo3_settings"
        ) as mock_get_settings:
            mock_get_settings.return_value = mock_veo3_settings

            config = ProviderConfigurationManager.create_veo3_config()

            # Verify logging calls were made
            mock_logger.info.assert_called()

            # Check that log messages contain expected content
            log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
            assert any(
                "Creating Veo3 provider configuration" in msg for msg in log_calls
            )
            assert any("Veo3 config created" in msg for msg in log_calls)


class TestProviderFactoryPerformance:
    """Test performance aspects of provider factory."""

    def test_create_provider_config_performance_logging(self):
        """Test that provider config creation includes performance tracking."""
        with patch.object(
            ProviderConfigurationManager, "create_provider_config"
        ) as mock_create:
            with patch("src.config.factory.provider_factory.logger") as mock_logger:
                mock_create.return_value = {"endpoint": "test"}

                config = ProviderConfigurationManager.create_provider_config(
                    "azure_sora"
                )

                # Verify performance logging with test ID
                log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
                assert any("test_id:" in msg for msg in log_calls)

    def test_provider_config_caching_compatibility(self):
        """Test that provider configs work without caching."""
        # Multiple calls should work independently
        mock_azure_config = {"endpoint": "https://test.azure.com"}

        with patch(
            "src.config.factory.provider_factory.ConfigurationFactory.get_azure_config"
        ) as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config.copy()

            config1 = ProviderConfigurationManager.create_provider_config("azure_sora")
            config2 = ProviderConfigurationManager.create_provider_config("azure_sora")

            assert config1 == config2
            assert config1 is not config2  # Should be separate instances
            assert mock_get_azure.call_count == 2  # Called for each request
