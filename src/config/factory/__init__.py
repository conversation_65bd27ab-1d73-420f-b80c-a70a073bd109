"""Configuration factory module with refactored components.

This module provides the public API for configuration factory functionality
while maintaining backward compatibility with existing code.

The factory has been refactored into focused modules:
- base_factory: Core ConfigurationFactory with base configuration methods
- provider_factory: Provider-specific configuration management
- validation: Configuration validation logic
- credentials: Google Cloud credential management
"""

# Import core classes for backward compatibility
from .base_factory import ConfigurationFactory as BaseConfigurationFactory
from .credentials import GoogleCloudCredentialManager, GoogleCloudCredentials
from .provider_factory import ProviderConfigurationFactory, ProviderConfigurationManager
from .validation import ConfigurationValidator, ValidationResult


# Enhanced ConfigurationFactory with provider methods
# This maintains backward compatibility while adding provider methods
class ConfigurationFactory(BaseConfigurationFactory):
    """
    Enhanced ConfigurationFactory with provider methods.

    This class combines the base ConfigurationFactory with provider-specific
    methods to maintain backward compatibility while providing all functionality
    in a single class.
    """

    @classmethod
    def create_veo3_config(cls, **overrides):
        """Create Veo3 provider configuration with environment defaults and optional overrides."""
        return ProviderConfigurationManager.create_veo3_config(**overrides)

    @classmethod
    def get_provider_availability(cls):
        """Check availability of all video generation providers."""
        return ProviderConfigurationManager.get_provider_availability()

    @classmethod
    def get_default_provider(cls):
        """Get the default video generation provider from environment configuration."""
        return ProviderConfigurationManager.get_default_provider()

    @classmethod
    def create_provider_config(cls, provider, **overrides):
        """Create configuration for specified provider with environment variable respect."""
        return ProviderConfigurationManager.create_provider_config(
            provider, **overrides
        )

    @classmethod
    def validate_provider_configuration(cls, provider):
        """Validate configuration for specified provider."""
        return ProviderConfigurationManager.validate_provider_configuration(provider)

    @classmethod
    def get_provider_performance_config(cls, provider):
        """Get performance configuration for specified provider."""
        return ProviderConfigurationManager.get_provider_performance_config(provider)


# Backward compatibility alias
EnhancedConfigurationFactory = ConfigurationFactory


# Public API exports for backward compatibility
__all__ = [
    # Core factory classes
    "ConfigurationFactory",
    "EnhancedConfigurationFactory",
    "ProviderConfigurationFactory",
    # Provider management
    "ProviderConfigurationManager",
    # Validation
    "ConfigurationValidator",
    "ValidationResult",
    # Credentials
    "GoogleCloudCredentialManager",
    "GoogleCloudCredentials",
]
