"""Google Cloud credential management with multiple detection sources."""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, SecretStr

logger = logging.getLogger(__name__)


class GoogleCloudCredentials(BaseModel):
    """Google Cloud credentials model with secure handling."""

    project_id: str
    client_id: Optional[str] = None
    client_secret: Optional[SecretStr] = None
    credentials_path: Optional[str] = None
    credentials_source: str = Field(description="Source where credentials were found")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {SecretStr: lambda v: v.get_secret_value() if v else None}


class GoogleCloudCredentialManager:
    """
    Multi-source Google Cloud credential detection and management.

    This class provides comprehensive credential detection from multiple sources
    including environment variables, service account files, and application
    default credentials.
    """

    # Standard Google Cloud credential paths
    CREDENTIAL_PATHS = [
        "~/.config/gcloud/application_default_credentials.json",
        "~/.google/credentials.json",
        "/etc/google/credentials.json",
        "./credentials.json",
        "./google-credentials.json",
    ]

    @classmethod
    def detect_credentials(cls) -> Optional[GoogleCloudCredentials]:
        """
        Detect Google Cloud credentials from multiple sources.

        Detection priority:
        1. Environment variables (GOOGLE_PROJECT_ID, GOOGLE_CLIENT_ID, etc.)
        2. GOOGLE_APPLICATION_CREDENTIALS file
        3. Standard credential file locations
        4. Application default credentials

        Returns:
            GoogleCloudCredentials if found, None otherwise
        """
        logger.debug("Starting Google Cloud credential detection")

        # Try environment variables first
        credentials = cls._detect_from_environment()
        if credentials:
            return credentials

        # Try GOOGLE_APPLICATION_CREDENTIALS
        credentials = cls._detect_from_application_credentials()
        if credentials:
            return credentials

        # Try standard credential paths
        credentials = cls._detect_from_standard_paths()
        if credentials:
            return credentials

        logger.warning("No Google Cloud credentials found from any source")
        return None

    @classmethod
    def _detect_from_environment(cls) -> Optional[GoogleCloudCredentials]:
        """Detect credentials from environment variables."""
        project_id = os.getenv("GOOGLE_PROJECT_ID")
        if not project_id:
            return None

        client_id = os.getenv("GOOGLE_CLIENT_ID")
        client_secret = os.getenv("GOOGLE_CLIENT_SECRET")

        logger.info("Found Google Cloud credentials in environment variables")
        return GoogleCloudCredentials(
            project_id=project_id,
            client_id=client_id,
            client_secret=SecretStr(client_secret) if client_secret else None,
            credentials_source="environment_variables",
        )

    @classmethod
    def _detect_from_application_credentials(cls) -> Optional[GoogleCloudCredentials]:
        """Detect credentials from GOOGLE_APPLICATION_CREDENTIALS file."""
        cred_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        if not cred_path:
            return None

        return cls._load_from_file(cred_path, "GOOGLE_APPLICATION_CREDENTIALS")

    @classmethod
    def _detect_from_standard_paths(cls) -> Optional[GoogleCloudCredentials]:
        """Detect credentials from standard file locations."""
        for path_str in cls.CREDENTIAL_PATHS:
            expanded_path = Path(path_str).expanduser()
            if expanded_path.exists():
                credentials = cls._load_from_file(
                    str(expanded_path), f"standard_path:{path_str}"
                )
                if credentials:
                    return credentials

        return None

    @classmethod
    def _load_from_file(
        cls, file_path: str, source_description: str
    ) -> Optional[GoogleCloudCredentials]:
        """
        Load credentials from a JSON file.

        Args:
            file_path: Path to the credentials file
            source_description: Description of the source for logging

        Returns:
            GoogleCloudCredentials if successful, None otherwise
        """
        try:
            with open(file_path) as f:
                cred_data = json.load(f)

            # Extract project ID from various possible fields
            project_id = (
                cred_data.get("project_id")
                or cred_data.get("project")
                or cred_data.get("quota_project_id")
            )

            if not project_id:
                logger.warning(f"No project ID found in {file_path}")
                return None

            # Extract client information if present
            client_id = cred_data.get("client_id")
            client_secret = cred_data.get("client_secret")

            logger.info(f"Loaded Google Cloud credentials from {source_description}")
            return GoogleCloudCredentials(
                project_id=project_id,
                client_id=client_id,
                client_secret=SecretStr(client_secret) if client_secret else None,
                credentials_path=file_path,
                credentials_source=source_description,
            )

        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            logger.debug(f"Failed to load credentials from {file_path}: {e}")
            return None

    @classmethod
    def validate_credentials(
        cls, credentials: GoogleCloudCredentials
    ) -> Dict[str, Any]:
        """
        Validate Google Cloud credentials.

        Args:
            credentials: Credentials to validate

        Returns:
            Validation result with status and details
        """
        errors = []
        warnings = []

        # Project ID validation
        if not credentials.project_id:
            errors.append("Project ID is required")
        elif not credentials.project_id.replace("-", "").replace("_", "").isalnum():
            errors.append("Project ID contains invalid characters")

        # Authentication validation
        has_file_auth = credentials.credentials_path is not None
        has_oauth_auth = credentials.client_id and credentials.client_secret

        if not has_file_auth and not has_oauth_auth:
            warnings.append("No authentication method available")

        # File existence validation
        if credentials.credentials_path:
            path = Path(credentials.credentials_path)
            if not path.exists():
                errors.append(
                    f"Credentials file not found: {credentials.credentials_path}"
                )
            elif not path.is_file():
                errors.append(
                    f"Credentials path is not a file: {credentials.credentials_path}"
                )

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "source": credentials.credentials_source,
            "has_file_auth": has_file_auth,
            "has_oauth_auth": has_oauth_auth,
        }

    @classmethod
    def get_credentials_for_config(cls) -> Optional[Dict[str, Any]]:
        """
        Get Google Cloud credentials formatted for configuration use.

        Returns:
            Dictionary with credential information for configuration
        """
        credentials = cls.detect_credentials()
        if not credentials:
            return None

        validation = cls.validate_credentials(credentials)

        return {
            "project_id": credentials.project_id,
            "client_id": credentials.client_id,
            "client_secret": credentials.client_secret.get_secret_value()
            if credentials.client_secret
            else None,
            "credentials_path": credentials.credentials_path,
            "source": credentials.credentials_source,
            "validation": validation,
        }

    @classmethod
    def create_test_credentials(
        cls,
        project_id: str = "test-project-12345",
        client_id: str = "test-client-id",
        client_secret: str = "test-client-secret",
    ) -> GoogleCloudCredentials:
        """
        Create test credentials for development and testing.

        Args:
            project_id: Test project ID
            client_id: Test client ID
            client_secret: Test client secret

        Returns:
            Test GoogleCloudCredentials instance
        """
        return GoogleCloudCredentials(
            project_id=project_id,
            client_id=client_id,
            client_secret=SecretStr(client_secret),
            credentials_source="test_credentials",
        )
