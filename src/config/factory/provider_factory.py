"""Provider-specific configuration factory and management."""

import logging
from typing import Any, Dict, Literal, Optional
from uuid import uuid4

from src.config.factory.base_factory import ConfigurationFactory
from src.config.veo3_settings import Veo3ProviderConfig

logger = logging.getLogger(__name__)


class ProviderConfigurationManager:
    """
    Enhanced ConfigurationFactory with provider-specific methods.

    This class extends the base ConfigurationFactory with provider-specific
    functionality including Veo3 configuration, provider availability checks,
    and unified provider configuration management.
    """

    @classmethod
    def create_veo3_config(cls, **overrides) -> Veo3ProviderConfig:
        """
        Create Veo3 provider configuration with environment defaults and optional overrides.

        This method implements the environment variable respect pattern by loading
        all configuration from environment variables first, then applying any
        explicit overrides provided by the caller.

        Args:
            **overrides: Optional configuration overrides
                - use_mock (bool): Override USE_MOCK_VEO environment variable
                - project_id (str): Override GOOGLE_PROJECT_ID environment variable
                - client_id (str): Override GOOGLE_CLIENT_ID environment variable
                - client_secret (str): Override GOOGLE_CLIENT_SECRET environment variable
                - model_version (str): Override VEO3_MODEL_VERSION environment variable
                - timeout (int): Override VEO3_TIMEOUT environment variable
                - max_retries (int): Override VEO3_MAX_RETRIES environment variable
                - retry_delay (int): Override VEO3_RETRY_DELAY environment variable
                - generation_timeout (int): Override VEO3_GENERATION_TIMEOUT environment variable
                - rate_limit_rpm (int): Override VEO3_RATE_LIMIT_RPM environment variable

        Returns:
            Veo3ProviderConfig: Configuration object with environment values and overrides

        Raises:
            ValueError: If required configuration is missing

        Example:
            >>> # Use environment defaults
            >>> config = ProviderConfigurationManager.create_veo3_config()
            >>>
            >>> # Override specific values for testing
            >>> test_config = ProviderConfigurationManager.create_veo3_config(
            ...     use_mock=True,
            ...     project_id="test-project-12345",
            ...     timeout=120
            ... )
        """
        logger.info(
            "Creating Veo3 provider configuration with environment variable respect"
        )

        # Load environment-based settings first
        veo3_settings = ConfigurationFactory.get_veo3_settings()

        # Extract values from settings, applying overrides
        project_id = overrides.get("project_id", veo3_settings.GOOGLE_PROJECT_ID)
        use_mock = overrides.get("use_mock", veo3_settings.USE_MOCK_VEO)
        client_id = overrides.get("client_id", veo3_settings.GOOGLE_CLIENT_ID)

        # Handle client_secret securely
        client_secret = overrides.get("client_secret")
        if client_secret is None and veo3_settings.GOOGLE_CLIENT_SECRET:
            client_secret = veo3_settings.GOOGLE_CLIENT_SECRET.get_secret_value()

        # Apply other configuration overrides
        model_version = overrides.get("model_version", veo3_settings.VEO3_MODEL_VERSION)
        timeout = overrides.get("timeout", veo3_settings.VEO3_TIMEOUT)
        max_retries = overrides.get("max_retries", veo3_settings.VEO3_MAX_RETRIES)
        retry_delay = overrides.get("retry_delay", veo3_settings.VEO3_RETRY_DELAY)
        generation_timeout = overrides.get(
            "generation_timeout", veo3_settings.VEO3_GENERATION_TIMEOUT
        )
        rate_limit_rpm = overrides.get(
            "rate_limit_rpm", veo3_settings.VEO3_RATE_LIMIT_RPM
        )

        # Validate required configuration
        if not project_id:
            raise ValueError(
                "GOOGLE_PROJECT_ID is required for Veo3 configuration. "
                "Set via environment variable or provide as override."
            )

        logger.info(
            f"Veo3 config created: project_id={project_id}, mock={use_mock}, timeout={timeout}"
        )

        return Veo3ProviderConfig(
            project_id=project_id,
            use_mock=use_mock,
            client_id=client_id,
            client_secret=client_secret,
            model_version=model_version,
            timeout=timeout,
            max_retries=max_retries,
            retry_delay=retry_delay,
            generation_timeout=generation_timeout,
            rate_limit_rpm=rate_limit_rpm,
        )

    @classmethod
    def get_provider_availability(cls) -> Dict[str, bool]:
        """
        Check availability of all video generation providers.

        Returns:
            Dict mapping provider names to availability status

        Example:
            >>> availability = ProviderConfigurationManager.get_provider_availability()
            >>> if availability["google_veo3"]:
            ...     config = ProviderConfigurationManager.create_veo3_config()
        """
        veo3_settings = ConfigurationFactory.get_veo3_settings()
        return veo3_settings.get_provider_availability()

    @classmethod
    def get_default_provider(cls) -> Literal["azure_sora", "google_veo3"]:
        """
        Get the default video generation provider from environment configuration.

        Returns:
            Default provider name based on environment variable DEFAULT_PROVIDER
        """
        veo3_settings = ConfigurationFactory.get_veo3_settings()
        return veo3_settings.DEFAULT_PROVIDER

    @classmethod
    def create_provider_config(
        cls, provider: Literal["azure_sora", "google_veo3"], **overrides
    ) -> Dict[str, Any]:
        """
        Create configuration for specified provider with environment variable respect.

        This method provides a unified interface for creating provider configurations
        while respecting environment variables and applying optional overrides.

        Args:
            provider: Provider name ("azure_sora" or "google_veo3")
            **overrides: Provider-specific configuration overrides

        Returns:
            Provider configuration dictionary ready for client initialization

        Raises:
            ValueError: If provider is unknown or configuration is invalid

        Example:
            >>> # Create Azure Sora configuration
            >>> azure_config = ProviderConfigurationManager.create_provider_config("azure_sora")
            >>>
            >>> # Create Veo3 configuration with overrides
            >>> veo3_config = ProviderConfigurationManager.create_provider_config(
            ...     "google_veo3",
            ...     use_mock=True,
            ...     timeout=120
            ... )
        """
        test_id = str(uuid4())[:8]
        logger.info(f"Creating provider config for {provider} (test_id: {test_id})")

        if provider == "azure_sora":
            # Return existing Azure configuration
            azure_config = ConfigurationFactory.get_azure_config()

            # Apply any overrides
            for key, value in overrides.items():
                if key in azure_config:
                    azure_config[key] = value

            logger.info(f"Azure Sora config created (test_id: {test_id})")
            return azure_config

        elif provider == "google_veo3":
            # Create Veo3 configuration with overrides
            veo3_config = cls.create_veo3_config(**overrides)
            config_dict = veo3_config.to_dict()

            logger.info(f"Google Veo3 config created (test_id: {test_id})")
            return config_dict

        else:
            raise ValueError(
                f"Unknown provider: {provider}. Available: azure_sora, google_veo3"
            )

    @classmethod
    def validate_provider_configuration(cls, provider: str) -> Dict[str, Any]:
        """
        Validate configuration for specified provider.

        Args:
            provider: Provider name to validate

        Returns:
            Validation result with status and any errors
        """
        veo3_settings = ConfigurationFactory.get_veo3_settings()
        return veo3_settings.validate_provider_configuration(provider)

    @classmethod
    def get_provider_performance_config(cls, provider: str) -> Dict[str, Any]:
        """
        Get performance configuration for specified provider.

        Args:
            provider: Provider name

        Returns:
            Performance configuration including timeouts and rate limits
        """
        if provider == "azure_sora":
            # Return existing Azure performance config
            base_config = ConfigurationFactory.get_base_config()
            return {
                "timeouts": {
                    "api_timeout": 300,  # Default Azure timeout
                    "generation_timeout": 1800,
                },
                "retry_config": {"max_retries": 3, "retry_delay": 2},
                "targets": {
                    "settings_load_time_ms": 50,
                    "factory_creation_time_ms": 10,
                    "validation_time_ms": 100,
                },
            }

        elif provider == "google_veo3":
            veo3_settings = ConfigurationFactory.get_veo3_settings()
            return veo3_settings.get_performance_config()

        else:
            raise ValueError(f"Unknown provider: {provider}")


class ProviderConfigurationFactory:
    """
    Specialized factory for provider-specific configuration management.

    This class provides advanced provider configuration capabilities including
    environment detection, provider switching, and configuration validation
    while maintaining backward compatibility with existing systems.
    """

    @staticmethod
    def create_environment_aware_config(
        provider: str, environment: Optional[str] = None, **overrides
    ) -> Dict[str, Any]:
        """
        Create provider configuration with environment-aware defaults.

        This method automatically applies environment-specific configuration
        defaults based on deployment type detection, then applies any overrides.

        Args:
            provider: Provider name ("azure_sora" or "google_veo3")
            environment: Environment name (auto-detected if None)
            **overrides: Configuration overrides

        Returns:
            Environment-aware provider configuration
        """
        from src.config.service import ConfigurationService

        deployment_type = ConfigurationService._detect_deployment_type()
        logger.info(f"Creating {provider} config for {deployment_type} environment")

        # Environment-specific defaults
        env_overrides = {}

        if deployment_type == "docker":
            # Docker environment defaults
            env_overrides.update(
                {
                    "timeout": 600,  # Longer timeout for Docker
                    "max_retries": 5,  # More retries for Docker
                }
            )

        elif deployment_type == "production":
            # Production environment defaults
            env_overrides.update(
                {
                    "use_mock": False,  # Never use mock in production
                    "timeout": 300,  # Standard timeout
                    "max_retries": 3,  # Standard retries
                }
            )

        elif deployment_type == "local":
            # Local development defaults
            env_overrides.update(
                {
                    "use_mock": True,  # Use mock by default locally
                    "timeout": 120,  # Shorter timeout for development
                    "max_retries": 2,  # Fewer retries locally
                }
            )

        # Merge environment defaults with explicit overrides
        final_overrides = {**env_overrides, **overrides}

        return ProviderConfigurationManager.create_provider_config(
            provider, **final_overrides
        )

    @staticmethod
    def get_optimal_provider_config() -> Dict[str, Any]:
        """
        Get optimal provider configuration based on availability and environment.

        This method automatically selects the best available provider and
        creates an optimized configuration for the current environment.

        Returns:
            Optimal provider configuration with provider selection
        """
        availability = ProviderConfigurationManager.get_provider_availability()
        default_provider = ProviderConfigurationManager.get_default_provider()

        # Select provider based on availability and preference
        if availability.get(default_provider, False):
            selected_provider = default_provider
        elif availability.get("azure_sora", False):
            selected_provider = "azure_sora"
        elif availability.get("google_veo3", False):
            selected_provider = "google_veo3"
        else:
            raise ValueError("No video generation providers are available")

        logger.info(f"Selected optimal provider: {selected_provider}")

        # Create environment-aware configuration
        config = ProviderConfigurationFactory.create_environment_aware_config(
            selected_provider
        )
        config["selected_provider"] = selected_provider
        config["provider_availability"] = availability

        return config
