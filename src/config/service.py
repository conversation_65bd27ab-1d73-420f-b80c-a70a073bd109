"""Configuration Service - Runtime configuration resolution with guaranteed environment loading.

This module provides a centralized configuration service that ensures environment
variables are loaded before any configuration access, solving the issue of static
class attributes being evaluated at import time.
"""

import logging
import os
from typing import Any, Dict, Optional, Type, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar("T")


class ConfigurationService:
    """
    Centralized configuration service with guaranteed environment loading.

    This service ensures that .env files are loaded before any configuration
    access, solving issues with static class attributes and process forking.

    Example:
        >>> # Get string configuration
        >>> api_version = ConfigurationService.get("AZURE_OPENAI_API_VERSION", "2025-04-01-preview")

        >>> # Get integer configuration with validation
        >>> max_length = ConfigurationService.get("MAX_CONTENT_LENGTH", 104857600, int)

        >>> # Get boolean configuration
        >>> debug_mode = ConfigurationService.get("DEBUG", False, bool)
    """

    _initialized: bool = False
    _config_cache: Dict[str, Any] = {}
    _load_attempted: bool = False
    _process_id: Optional[int] = None

    @classmethod
    def reload(cls) -> None:
        """
        Force reload of configuration.

        This method clears the cache and forces a fresh load of environment variables.
        Useful for testing or when environment files change.
        """
        cls._initialized = False
        cls._load_attempted = False
        cls._config_cache.clear()
        cls._process_id = None
        logger.info("🔧 ConfigurationService: Configuration reloaded")

    @classmethod
    def _ensure_initialized(cls) -> None:
        """
        Ensure environment is loaded exactly once per process.

        This method is idempotent and thread-safe. It will only load the
        environment once per process, even in forked worker processes.
        Includes fork detection to automatically reload configuration.
        """
        current_pid = os.getpid()

        # Check if we're in a forked process
        if cls._process_id is not None and cls._process_id != current_pid:
            logger.info(
                f"🔄 Fork detected (PID {cls._process_id} → {current_pid}), reloading configuration"
            )
            cls._initialized = False
            cls._load_attempted = False
            cls._config_cache.clear()

        if not cls._initialized:
            try:
                from dotenv import load_dotenv

                # Load .env file (single source of truth)
                env_file = ".env"
                if os.path.exists(env_file):
                    result = load_dotenv(env_file)
                    logger.info(
                        f"🔧 ConfigurationService: Environment loaded from {env_file}, success: {result}"
                    )
                    logger.info(f"🔧 ConfigurationService: Process ID: {current_pid}")
                    cls._apply_environment_overrides()
                else:
                    logger.warning("🔧 ConfigurationService: No .env file found")
                    result = False

                cls._load_attempted = True
                cls._initialized = True
                cls._process_id = current_pid

                # Log some key environment variables for debugging
                logger.debug(
                    f"🔧 DEPLOYMENT_TYPE: {os.getenv('DEPLOYMENT_TYPE', 'auto-detect')}"
                )
                logger.debug(f"🔧 FLASK_ENV: {os.getenv('FLASK_ENV', 'NOT_SET')}")

            except Exception as e:
                logger.error(
                    f"❌ ConfigurationService: Failed to load environment: {e}"
                )
                cls._load_attempted = True
                cls._initialized = True  # Mark as initialized even on failure to avoid infinite retries
                cls._process_id = current_pid

    @classmethod
    def _apply_environment_overrides(cls) -> None:
        """
        Apply environment-specific configuration overrides.

        This method automatically detects the deployment type and applies
        appropriate configuration overrides for database URLs, Redis URLs, etc.
        """
        deployment_type = cls._detect_deployment_type()
        logger.info(
            f"🔧 ConfigurationService: Detected deployment type: {deployment_type}"
        )

        if deployment_type == "docker":
            cls._apply_docker_overrides()
        elif deployment_type == "production":
            cls._apply_production_overrides()
        elif deployment_type == "local":
            cls._apply_local_overrides()
        # Default: use .env file values as-is

    @classmethod
    def _detect_deployment_type(cls) -> str:
        """
        Detect the deployment type based on environment indicators.

        Returns:
            str: 'local', 'docker', or 'production'
        """
        # Check for explicit override
        deployment_type = os.getenv("DEPLOYMENT_TYPE", "").lower()
        if deployment_type in ["local", "docker", "production"]:
            return deployment_type

        # Auto-detect based on environment
        if os.path.exists("/.dockerenv"):
            return "docker"
        elif os.getenv("FLASK_ENV") == "production":
            return "production"
        else:
            return "local"

    @classmethod
    def _apply_docker_overrides(cls) -> None:
        """Apply Docker-specific configuration overrides."""
        # Override database URL for Docker
        if not os.getenv("DATABASE_URL_OVERRIDE"):
            db_password = os.getenv("DB_PASSWORD", "sora_secure_password_2024")
            docker_db_url = (
                f"postgresql://sora_user:{db_password}@postgres:5432/sora_production"
            )
            os.environ["DATABASE_URL"] = docker_db_url
            logger.info("🐳 Applied Docker database URL override")

        # Override Redis URLs for Docker
        if not os.getenv("CELERY_BROKER_URL_OVERRIDE"):
            os.environ["CELERY_BROKER_URL"] = "redis://redis:6379/0"
            os.environ["CELERY_RESULT_BACKEND"] = "redis://redis:6379/0"
            logger.info("🐳 Applied Docker Redis URL overrides")

        # Override Flask environment for Docker
        if not os.getenv("FLASK_ENV_OVERRIDE"):
            os.environ["FLASK_ENV"] = "production"
            os.environ["FLASK_DEBUG"] = "false"
            logger.info("🐳 Applied Docker Flask environment overrides")

        # Enable rate limiting for Docker
        if not os.getenv("RATE_LIMIT_ENABLED_OVERRIDE"):
            os.environ["RATE_LIMIT_ENABLED"] = "true"
            logger.info("🐳 Applied Docker rate limiting override")

    @classmethod
    def _apply_production_overrides(cls) -> None:
        """Apply production-specific configuration overrides."""
        # Ensure production Flask environment
        os.environ["FLASK_ENV"] = "production"
        os.environ["FLASK_DEBUG"] = "false"

        # Enable all security features
        os.environ["RATE_LIMIT_ENABLED"] = "true"
        os.environ["CSRF_ENABLED"] = "true"
        os.environ["SESSION_COOKIE_SECURE"] = "true"
        os.environ["SESSION_COOKIE_HTTPONLY"] = "true"

        logger.info("🔒 Applied production security overrides")

    @classmethod
    def _apply_local_overrides(cls) -> None:
        """Apply local development configuration overrides."""
        # Ensure local development settings
        if not os.getenv("FLASK_ENV_OVERRIDE"):
            os.environ["FLASK_ENV"] = "development"
            os.environ["FLASK_DEBUG"] = "true"
            logger.info("🏠 Applied local Flask environment overrides")

        # Disable rate limiting for local development
        if not os.getenv("RATE_LIMIT_ENABLED_OVERRIDE"):
            os.environ["RATE_LIMIT_ENABLED"] = "false"
            logger.info("🏠 Applied local rate limiting override")

        # Use SQLite for local development
        if not os.getenv("DATABASE_URL_OVERRIDE"):
            os.environ["DATABASE_URL"] = "sqlite:///sora_poc.db"
            logger.info("🏠 Applied local database URL override")

        # Use localhost for Redis/Celery
        if not os.getenv("CELERY_BROKER_URL_OVERRIDE"):
            os.environ["CELERY_BROKER_URL"] = "redis://localhost:6379/0"
            os.environ["CELERY_RESULT_BACKEND"] = "redis://localhost:6379/0"
            logger.info("🏠 Applied local Redis URL overrides")

    @classmethod
    def get(
        cls,
        key: str,
        default: T = None,
        config_type: Type[T] = str,
        required: bool = False,
    ) -> T:
        """
        Get configuration value with guaranteed environment loading.

        Args:
            key: Environment variable name
            default: Default value if not found
            config_type: Type to convert the value to (str, int, bool, float)
            required: If True, raises ValueError if key is missing

        Returns:
            Configuration value converted to specified type

        Raises:
            ValueError: If required=True and key is missing
            TypeError: If value cannot be converted to specified type

        Example:
            >>> api_version = ConfigurationService.get("AZURE_OPENAI_API_VERSION", "2025-04-01-preview")
            >>> max_length = ConfigurationService.get("MAX_CONTENT_LENGTH", 104857600, int)
            >>> debug = ConfigurationService.get("DEBUG", False, bool)
        """
        # DEBUG: Log all configuration access
        logger.info(
            f"🔧 ConfigurationService.get() called for key: '{key}', default: '{default}'"
        )

        # Ensure environment is loaded
        cls._ensure_initialized()

        # Check cache first for performance
        cache_key = f"{key}:{config_type.__name__}"
        if cache_key in cls._config_cache:
            return cls._config_cache[cache_key]

        # Get raw value from environment
        raw_value = os.getenv(key)

        # Handle missing values
        if raw_value is None:
            if required:
                raise ValueError(f"Required configuration key '{key}' is missing")
            if default is None:
                cls._config_cache[cache_key] = None
                return None
            raw_value = str(default)

        # Convert to target type
        try:
            converted_value = cls._convert_type(raw_value, config_type, default)
            cls._config_cache[cache_key] = converted_value
            return converted_value
        except (ValueError, TypeError) as e:
            logger.error(
                f"❌ Failed to convert '{key}={raw_value}' to {config_type.__name__}: {e}"
            )
            if default is not None:
                logger.warning(f"⚠️ Using default value for '{key}': {default}")
                cls._config_cache[cache_key] = default
                return default
            raise

    @classmethod
    def _convert_type(cls, value: str, target_type: Type[T], default: Any = None) -> T:
        """
        Convert string value to target type with proper error handling.

        Args:
            value: String value to convert
            target_type: Target type (str, int, bool, float)
            default: Default value for context in error messages

        Returns:
            Converted value

        Raises:
            ValueError: If conversion fails
        """
        if target_type == str:
            return value

        elif target_type == int:
            try:
                return int(value)
            except ValueError:
                raise ValueError(f"Cannot convert '{value}' to integer")

        elif target_type == float:
            try:
                return float(value)
            except ValueError:
                raise ValueError(f"Cannot convert '{value}' to float")

        elif target_type == bool:
            # Handle common boolean representations
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                lower_value = value.lower().strip()
                if lower_value in ("true", "1", "yes", "on", "enabled"):
                    return True
                elif lower_value in ("false", "0", "no", "off", "disabled", ""):
                    return False
                else:
                    raise ValueError(
                        f"Cannot convert '{value}' to boolean. Use true/false, 1/0, yes/no, on/off, enabled/disabled"
                    )
            else:
                return bool(value)

        else:
            # For other types, try direct conversion
            try:
                return target_type(value)
            except Exception as e:
                raise ValueError(
                    f"Cannot convert '{value}' to {target_type.__name__}: {e}"
                )

    @classmethod
    def get_int(
        cls,
        key: str,
        default: int = None,
        min_val: int = None,
        max_val: int = None,
        required: bool = False,
    ) -> int:
        """
        Get integer configuration with optional range validation.

        Args:
            key: Environment variable name
            default: Default value if not found
            min_val: Minimum allowed value
            max_val: Maximum allowed value
            required: If True, raises ValueError if key is missing

        Returns:
            Integer value within specified range

        Raises:
            ValueError: If value is outside allowed range or conversion fails
        """
        value = cls.get(key, default, int, required)

        if value is not None:
            if min_val is not None and value < min_val:
                logger.warning(
                    f"⚠️ Configuration '{key}={value}' below minimum {min_val}, using {min_val}"
                )
                return min_val
            if max_val is not None and value > max_val:
                logger.warning(
                    f"⚠️ Configuration '{key}={value}' above maximum {max_val}, using {max_val}"
                )
                return max_val

        return value

    @classmethod
    def get_bool(cls, key: str, default: bool = None, required: bool = False) -> bool:
        """
        Get boolean configuration with smart string parsing.

        Args:
            key: Environment variable name
            default: Default value if not found
            required: If True, raises ValueError if key is missing

        Returns:
            Boolean value

        Example:
            >>> debug = ConfigurationService.get_bool("DEBUG", False)
            >>> enabled = ConfigurationService.get_bool("FEATURE_ENABLED", True)
        """
        return cls.get(key, default, bool, required)

    @classmethod
    def clear_cache(cls) -> None:
        """
        Clear configuration cache.

        Useful for testing or when you need to reload configuration
        after environment changes.
        """
        cls._config_cache.clear()
        logger.debug("🔧 ConfigurationService: Cache cleared")

    @classmethod
    def reload(cls) -> None:
        """
        Force reload of environment and clear cache.

        This is useful for testing scenarios where you need to
        reload configuration after environment changes, or when
        called from Celery worker post-fork hooks.
        """
        cls._initialized = False
        cls._load_attempted = False
        cls._process_id = None  # Reset process ID to force reinitialization
        cls.clear_cache()
        cls._ensure_initialized()
        logger.info("🔧 ConfigurationService: Configuration reloaded")

    @classmethod
    def get_debug_info(cls) -> Dict[str, Any]:
        """
        Get debug information about configuration service state.

        Returns:
            Dictionary with debug information
        """
        return {
            "initialized": cls._initialized,
            "load_attempted": cls._load_attempted,
            "cache_size": len(cls._config_cache),
            "cached_keys": list(cls._config_cache.keys()),
            "env_file_exists": os.path.exists(".env"),
            "current_working_directory": os.getcwd(),
            "process_id": cls._process_id,
            "current_pid": os.getpid(),
        }


# Convenience functions for common configuration patterns
def get_database_url() -> str:
    """Get database URL with absolute path resolution for SQLite."""
    env_db_url = ConfigurationService.get("DATABASE_URL")
    if env_db_url:
        return env_db_url

    # Generate absolute path for SQLite
    project_root = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
    db_path = os.path.join(project_root, "sora_poc.db")
    db_url = f"sqlite:///{db_path}"

    logger.info(f"🗄️ Database URL resolved to: {db_url}")
    return db_url


def get_azure_config() -> Dict[str, str]:
    """Get Azure OpenAI configuration as a dictionary."""
    return {
        "endpoint": ConfigurationService.get("AZURE_OPENAI_ENDPOINT", required=True),
        "api_key": ConfigurationService.get("AZURE_OPENAI_API_KEY", required=True),
        "api_version": ConfigurationService.get(
            "AZURE_OPENAI_API_VERSION", "2025-04-01-preview"
        ),
        "deployment_name": ConfigurationService.get(
            "AZURE_OPENAI_DEPLOYMENT_NAME", "sora"
        ),
    }
