# F4 Docker Deployment Guide

**Comprehensive Docker deployment guide for F4 dual-provider video generation system with Azure Sora and Google Veo3 support.**

## Overview

F4 Docker deployment supports both Azure Sora and Google Veo3 providers in a production-ready containerized environment with automatic provider selection, environment-aware configuration, and comprehensive monitoring.

### F4 Docker Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    F4 Docker Stack                         │
├─────────────────────────────────────────────────────────────┤
│  Nginx (Load Balancer & SSL)                               │
├─────────────────────────────────────────────────────────────┤
│  Flask Apps (3x) - F4 Provider Interface                   │
│  ├── Azure Sora Integration                                │
│  ├── Google Veo3 Integration                               │
│  └── Provider Auto-Selection                               │
├─────────────────────────────────────────────────────────────┤
│  Celery Workers (4x) - Video Generation                    │
│  ├── Provider-Aware Task Processing                        │
│  └── Environment-Specific Configuration                    │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL Database                                        │
│  Redis (Message Broker & Cache)                            │
├─────────────────────────────────────────────────────────────┤
│  Monitoring Stack                                          │
│  ├── Prometheus + Grafana                                  │
│  ├── ELK Stack (Elasticsearch + Kibana)                   │
│  └── Celery Flower                                         │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### Zero-Configuration F4 Deployment

```bash
# 1. Clone and navigate to project
git clone <repository-url>
cd sora-poc

# 2. Copy F4 environment template
cp .env.example .env.docker

# 3. Configure F4 providers (minimal setup)
cat >> .env.docker << 'EOF'
# F4 Dual-Provider Configuration
USE_MOCK_VEO=false
DEFAULT_PROVIDER=azure_sora
GOOGLE_PROJECT_ID=your-google-project-id

# Azure Sora (Primary Provider)
AZURE_OPENAI_API_KEY=your-azure-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=preview
AZURE_OPENAI_DEPLOYMENT_NAME=sora

# Google Veo3 (Secondary Provider)
GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=600
EOF

# 4. Deploy F4 stack (simple 5-container setup)
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# 5. Access application at http://localhost
# ✅ F4 dual-provider system ready with automatic provider selection
```

### Production F4 Deployment

```bash
# 1. Configure secrets for production
cat >> .env.docker << 'EOF'
# Production Security
SECRET_KEY=your-production-secret-key
DB_PASSWORD=secure-database-password-2024
FLOWER_USERNAME=admin
FLOWER_PASSWORD=secure-flower-password

# F4 Production Provider Configuration
USE_MOCK_VEO=false
DEFAULT_PROVIDER=azure_sora
GOOGLE_PROJECT_ID=production-project-id
GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/production-google-credentials.json

# Production Monitoring
GRAFANA_PASSWORD=secure-grafana-password
EOF

# 2. Mount Google Cloud credentials
mkdir -p secrets
cp /path/to/production-google-credentials.json secrets/

# 3. Deploy full production stack
docker-compose -f src/deployment/docker/docker-compose.yml up -d

# ✅ Full production F4 deployment with monitoring
```

## F4 Environment Configuration

### Docker Environment Variables

**F4 Provider Configuration for Docker:**

```bash
# Provider Selection and Switching
USE_MOCK_VEO=false                    # Production: false, Development: true
DEFAULT_PROVIDER=azure_sora           # azure_sora | google_veo3

# Azure Sora Configuration (Primary Provider)
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=preview
AZURE_OPENAI_DEPLOYMENT_NAME=sora

# Google Veo3 Configuration (Secondary Provider)
GOOGLE_PROJECT_ID=your-google-project-id
GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=600                      # Longer timeout for Docker
VEO3_MAX_RETRIES=5                    # More retries for production
VEO3_GENERATION_TIMEOUT=1800
VEO3_RATE_LIMIT_RPM=30

# F4 Environment-Aware Configuration
DEPLOYMENT_TYPE=docker
FLASK_ENV=production
RATE_LIMIT_ENABLED=true
WORKER_CONCURRENCY=4                  # Scale based on CPU cores

# Docker Service Configuration
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
```

### Environment-Specific F4 Configuration

**Development Docker Configuration (`.env.docker.dev`):**

```bash
# F4 Development in Docker
USE_MOCK_VEO=true                     # Use mock for development
DEFAULT_PROVIDER=google_veo3          # Test Veo3 integration
GOOGLE_PROJECT_ID=134075247963        # Development project
VEO3_TIMEOUT=300                      # Shorter timeout for development
FLASK_ENV=development
FLASK_DEBUG=true
WORKER_CONCURRENCY=2                  # Fewer workers for development
LOG_LEVEL=DEBUG
VEO3_DEBUG_MODE=true
```

**Production Docker Configuration (`.env.docker.prod`):**

```bash
# F4 Production in Docker
USE_MOCK_VEO=false                    # Real providers only
DEFAULT_PROVIDER=azure_sora           # Stable primary provider
GOOGLE_PROJECT_ID=production-project-id
VEO3_TIMEOUT=600                      # Longer timeout for stability
FLASK_ENV=production
FLASK_DEBUG=false
WORKER_CONCURRENCY=8                  # Scale for production load
LOG_LEVEL=WARNING
RATE_LIMIT_ENABLED=true
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
```

### Docker Secrets Management

**Google Cloud Credentials Mount:**

```yaml
# docker-compose.yml secrets configuration
services:
  app1:
    volumes:
      - ./secrets/google-credentials.json:/app/secrets/google-credentials.json:ro
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json

  worker1:
    volumes:
      - ./secrets/google-credentials.json:/app/secrets/google-credentials.json:ro
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json
```

**Azure Credentials Security:**

```yaml
services:
  app1:
    environment:
      # Use Docker secrets for sensitive data
      - AZURE_OPENAI_API_KEY_FILE=/run/secrets/azure_api_key
    secrets:
      - azure_api_key

secrets:
  azure_api_key:
    file: ./secrets/azure_api_key.txt
```

## F4 Docker Compose Configurations

### Simple F4 Deployment (docker-compose.simple.yml)

**5-container minimal deployment for development and testing:**

```yaml
version: '3.8'

services:
  # F4 Application with dual-provider support
  app:
    build:
      context: ../../..
      dockerfile: src/deployment/docker/Dockerfile
    container_name: sora-app-simple
    environment:
      # F4 Provider Configuration
      - USE_MOCK_VEO=${USE_MOCK_VEO:-true}
      - DEFAULT_PROVIDER=${DEFAULT_PROVIDER:-azure_sora}
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json
      
      # Azure Sora Configuration
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION:-preview}
      - AZURE_OPENAI_DEPLOYMENT_NAME=${AZURE_OPENAI_DEPLOYMENT_NAME:-sora}
      
      # Veo3 Configuration
      - VEO3_MODEL_VERSION=${VEO3_MODEL_VERSION:-veo-3.0-generate-preview}
      - VEO3_TIMEOUT=${VEO3_TIMEOUT:-600}
      - VEO3_MAX_RETRIES=${VEO3_MAX_RETRIES:-5}
      
      # Environment Configuration
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./secrets:/app/secrets:ro
      - video_storage:/app/uploads
    ports:
      - "8090:5001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # F4 Worker with provider-aware task processing
  worker:
    build:
      context: ../../..
      dockerfile: src/deployment/docker/Dockerfile.worker
    container_name: sora-worker-simple
    environment:
      # Same F4 configuration as app
      - USE_MOCK_VEO=${USE_MOCK_VEO:-true}
      - DEFAULT_PROVIDER=${DEFAULT_PROVIDER:-azure_sora}
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - VEO3_TIMEOUT=${VEO3_TIMEOUT:-600}
      - WORKER_CONCURRENCY=${WORKER_CONCURRENCY:-2}
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./secrets:/app/secrets:ro
      - video_storage:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # PostgreSQL Database
  postgres:
    image: postgres:15.4-alpine
    container_name: sora-postgres-simple
    environment:
      - POSTGRES_DB=sora_production
      - POSTGRES_USER=sora_user
      - POSTGRES_PASSWORD=${DB_PASSWORD:-sora_secure_password_2024}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sora_user -d sora_production"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Message Broker
  redis:
    image: redis:7.2-alpine
    container_name: sora-redis-simple
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: sora-nginx-simple
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.simple.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
  video_storage:

networks:
  default:
    driver: bridge
```

### Production F4 Deployment (docker-compose.yml)

**Full production stack with load balancing and monitoring:**

- **3x Flask App instances** with F4 dual-provider support
- **4x Celery Workers** with provider-aware task processing
- **Load balancer** with health checks and failover
- **Monitoring stack** (Prometheus, Grafana, ELK)
- **SSL termination** and security hardening

## F4 Provider Testing in Docker

### Provider Availability Testing

```bash
# Test F4 provider availability in Docker
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
availability = ConfigurationFactory.get_provider_availability()
print(f'Provider availability: {availability}')
default = ConfigurationFactory.get_default_provider()
print(f'Default provider: {default}')
"
```

### Provider Configuration Testing

```bash
# Test Azure Sora configuration
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
azure_config = ConfigurationFactory.create_provider_config('azure_sora')
print(f'Azure endpoint: {azure_config.get(\"endpoint\", \"Not configured\")}')
validation = ConfigurationFactory.validate_provider_configuration('azure_sora')
print(f'Azure validation: {validation[\"valid\"]}')
"

# Test Google Veo3 configuration
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
veo3_config = ConfigurationFactory.create_provider_config('google_veo3')
print(f'Veo3 project: {veo3_config.get(\"project_id\", \"Not configured\")}')
print(f'Veo3 mock mode: {veo3_config.get(\"use_mock\", \"Unknown\")}')
validation = ConfigurationFactory.validate_provider_configuration('google_veo3')
print(f'Veo3 validation: {validation[\"valid\"]}')
"
```

### Environment-Aware Configuration Testing

```bash
# Test environment detection in Docker
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.service import ConfigurationService
from src.config.factory import ProviderConfigurationFactory

deployment_type = ConfigurationService._detect_deployment_type()
print(f'Detected deployment: {deployment_type}')

optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()
print(f'Optimal provider: {optimal_config.get(\"selected_provider\")}')
"
```

### F4 Integration Testing in Docker

```bash
# Run F4 validation inside Docker container
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/validate_config.py

# Run F4 basic usage examples
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/examples_f4_basic_usage.py

# Run F4 environment-aware examples
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/examples_f4_environment_aware.py

# Run F4 troubleshooting
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/troubleshoot_f4_config.py
```

## F4 Environment Switching

### Development to Production Switching

```bash
# Switch from development to production configuration
cp .env.docker.dev .env.docker
echo "USE_MOCK_VEO=false" >> .env.docker
echo "DEFAULT_PROVIDER=azure_sora" >> .env.docker
echo "FLASK_ENV=production" >> .env.docker

# Restart with production configuration
docker-compose -f src/deployment/docker/docker-compose.simple.yml down
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d
```

### Provider Switching

```bash
# Switch from Azure Sora to Google Veo3
echo "DEFAULT_PROVIDER=google_veo3" >> .env.docker
docker-compose -f src/deployment/docker/docker-compose.simple.yml restart app worker

# Verify provider switch
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
print(f'Current default provider: {ConfigurationFactory.get_default_provider()}')
"
```

### Mock to Real API Switching

```bash
# Switch from mock to real Veo3 API
echo "USE_MOCK_VEO=false" >> .env.docker

# Ensure authentication is configured
echo "GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json" >> .env.docker

# Restart services
docker-compose -f src/deployment/docker/docker-compose.simple.yml restart app worker

# Verify real API mode
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
veo3_config = ConfigurationFactory.create_provider_config('google_veo3')
print(f'Mock mode: {veo3_config.get(\"use_mock\")}')
"
```

## F4 Health Checks and Monitoring

### F4 Provider Health Endpoints

```bash
# Check overall F4 system health
curl http://localhost:8090/health

# Check provider-specific health
curl http://localhost:8090/health/providers

# Check Azure Sora health
curl http://localhost:8090/health/azure

# Check Google Veo3 health  
curl http://localhost:8090/health/veo3

# Check provider availability
curl http://localhost:8090/api/providers/availability
```

### F4 Configuration Validation Endpoints

```bash
# Validate F4 configuration
curl http://localhost:8090/api/config/validate

# Get current provider configuration
curl http://localhost:8090/api/config/providers

# Get environment information
curl http://localhost:8090/api/config/environment
```

### F4 Monitoring in Docker

**Prometheus F4 Metrics:**

```yaml
# prometheus.yml configuration for F4 metrics
  - job_name: 'f4-providers'
    static_configs:
      - targets: ['app1:5001', 'app2:5001', 'app3:5001']
    metrics_path: '/metrics/providers'
    scrape_interval: 30s
```

**Grafana F4 Dashboard:**

- Provider availability metrics
- Configuration creation performance
- Provider switching events
- Error rates by provider
- Environment-specific metrics

## Troubleshooting F4 Docker Deployment

### Common F4 Configuration Issues

**Issue: Provider not available in Docker**

```bash
# Debug provider availability
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import validate_veo3_environment

# Check availability
availability = ConfigurationFactory.get_provider_availability()
print('Provider availability:', availability)

# Validate environment
validation = validate_veo3_environment()
print('Environment validation:', validation['valid'])
if not validation['valid']:
    for error in validation['errors']:
        print(f'  Error: {error}')
"

# Solution: Check environment variables and credentials
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app printenv | grep -E "(GOOGLE|AZURE|USE_MOCK)"
```

**Issue: Google Cloud authentication failure**

```bash
# Check Google Cloud credentials
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app ls -la /app/secrets/
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
import os
creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
print(f'Credentials path: {creds_path}')
print(f'File exists: {os.path.exists(creds_path) if creds_path else False}')
"

# Solution: Ensure credentials are mounted correctly
# 1. Verify secrets directory exists: ls -la secrets/
# 2. Check volume mount in docker-compose.yml
# 3. Verify file permissions: chmod 644 secrets/google-credentials.json
```

**Issue: Environment detection incorrect**

```bash
# Check environment detection
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.service import ConfigurationService
deployment_type = ConfigurationService._detect_deployment_type()
print(f'Detected: {deployment_type}')
print(f'DEPLOYMENT_TYPE env var: {os.getenv(\"DEPLOYMENT_TYPE\", \"Not set\")}')
print(f'Docker env file exists: {os.path.exists(\"/.dockerenv\")}')
"

# Solution: Set explicit DEPLOYMENT_TYPE
echo "DEPLOYMENT_TYPE=docker" >> .env.docker
```

### F4 Performance Issues in Docker

**Issue: Slow configuration creation**

```bash
# Test configuration performance
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
import time
from src.config.factory import ConfigurationFactory

providers = ['azure_sora', 'google_veo3']
for provider in providers:
    start_time = time.time()
    config = ConfigurationFactory.create_provider_config(provider)
    creation_time = (time.time() - start_time) * 1000
    print(f'{provider}: {creation_time:.2f}ms')
"

# Solution: Check resource limits and network connectivity
docker stats
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app curl -w '%{time_total}' -s -o /dev/null http://google.com
```

### F4 Provider Switching Issues

**Issue: Provider switch not taking effect**

```bash
# Clear configuration cache
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python -c "
from src.config.factory import ConfigurationFactory
from src.config.service import ConfigurationService
ConfigurationFactory.clear_cache()
ConfigurationService.reload()
print('Configuration cache cleared')
"

# Restart services to pick up new configuration
docker-compose -f src/deployment/docker/docker-compose.simple.yml restart app worker
```

### F4 Validation Tools

**Run F4 troubleshooting inside Docker:**

```bash
# Interactive troubleshooting
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/troubleshoot_f4_config.py --interactive

# Auto-fix common issues
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/troubleshoot_f4_config.py --fix

# Test specific components
docker-compose -f src/deployment/docker/docker-compose.simple.yml exec app python /app/troubleshoot_f4_config.py --issue config
```

## F4 Production Deployment Checklist

### Pre-Deployment Validation

- [ ] **Provider Configuration**
  - [ ] Azure Sora credentials configured and tested
  - [ ] Google Veo3 project ID and authentication configured
  - [ ] Provider availability validated: `python validate_config.py`
  - [ ] Mock mode disabled: `USE_MOCK_VEO=false`

- [ ] **Environment Configuration**
  - [ ] Production environment variables set
  - [ ] Deployment type detection working: `DEPLOYMENT_TYPE=docker`
  - [ ] Security headers enabled: `FLASK_ENV=production`
  - [ ] Rate limiting enabled: `RATE_LIMIT_ENABLED=true`

- [ ] **Secrets Management**
  - [ ] Google Cloud credentials mounted securely
  - [ ] Azure API keys configured via environment or secrets
  - [ ] Database passwords generated and secured
  - [ ] Secret volumes properly configured

- [ ] **F4 Integration Testing**
  - [ ] Both providers available and validated
  - [ ] Provider switching functional
  - [ ] Environment-aware configuration working
  - [ ] Performance targets met (<50ms config creation)

### Post-Deployment Validation

- [ ] **System Health**
  - [ ] All containers healthy and running
  - [ ] F4 health endpoints responding: `/health/providers`
  - [ ] Provider availability confirmed: `/api/providers/availability`
  - [ ] Configuration validation passing: `/api/config/validate`

- [ ] **Functional Testing**
  - [ ] Video generation working with default provider
  - [ ] Provider failover functional
  - [ ] Real-time status updates working
  - [ ] Video playback validated in browser

- [ ] **Monitoring Setup**
  - [ ] F4 provider metrics collecting in Prometheus
  - [ ] Grafana F4 dashboard functional
  - [ ] Alert rules configured for provider failures
  - [ ] Log aggregation capturing F4 events

## Advanced F4 Docker Configuration

### Multi-Environment Deployment

**Staging Environment:**

```bash
# Create staging-specific configuration
cat > .env.docker.staging << 'EOF'
# F4 Staging Configuration
USE_MOCK_VEO=false
DEFAULT_PROVIDER=google_veo3
GOOGLE_PROJECT_ID=staging-project-id
VEO3_TIMEOUT=300
FLASK_ENV=staging
WORKER_CONCURRENCY=2
LOG_LEVEL=INFO
EOF

# Deploy staging with staging config
cp .env.docker.staging .env.docker
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d
```

### F4 Load Balancing Configuration

**Nginx F4 Provider-Aware Load Balancing:**

```nginx
# nginx.f4.conf
upstream f4_backend {
    # F4 app instances with health checks
    server app1:5001 max_fails=3 fail_timeout=30s;
    server app2:5001 max_fails=3 fail_timeout=30s;
    server app3:5001 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    
    # F4 provider health check endpoint
    location /health/providers {
        proxy_pass http://f4_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 5s;
        proxy_read_timeout 10s;
    }
    
    # F4 API endpoints
    location /api/providers/ {
        proxy_pass http://f4_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        # Longer timeout for provider operations
        proxy_connect_timeout 30s;
        proxy_read_timeout 60s;
    }
    
    # Standard application routes
    location / {
        proxy_pass http://f4_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### F4 Auto-Scaling Configuration

**Docker Swarm F4 Auto-Scaling:**

```yaml
# docker-stack.f4.yml for Docker Swarm
version: '3.8'

services:
  app:
    image: sora-app:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    environment:
      # F4 configuration for auto-scaling
      - USE_MOCK_VEO=false
      - DEFAULT_PROVIDER=azure_sora
      - DEPLOYMENT_TYPE=docker_swarm
    networks:
      - f4_network

  worker:
    image: sora-worker:latest
    deploy:
      replicas: 4
      # Auto-scale based on queue length
      labels:
        - "traefik.enable=false"
      resources:
        limits:
          cpus: '1.5'
          memory: 1.5G
    environment:
      # F4 worker configuration
      - WORKER_CONCURRENCY=3
      - USE_MOCK_VEO=false
    networks:
      - f4_network

networks:
  f4_network:
    driver: overlay
    attachable: true
```

## F4 Container Optimization

### F4 Performance Tuning

**Dockerfile optimizations for F4:**

```dockerfile
# Multi-stage build for F4 application
FROM python:3.11-slim AS f4-base

# Install system dependencies for F4
RUN apt-get update && apt-get install -y \
    curl \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# F4 Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM f4-base AS f4-app

# F4 application code
COPY src/ /app/src/
COPY validate_config.py /app/
COPY examples_f4_*.py /app/
COPY troubleshoot_f4_config.py /app/

# F4 configuration
ENV PYTHONPATH=/app
ENV F4_CONFIG_CACHE_SIZE=128
ENV F4_PERFORMANCE_MODE=production

# F4 health check
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
    CMD python -c "from src.config.factory import ConfigurationFactory; ConfigurationFactory.get_provider_availability()"

WORKDIR /app
EXPOSE 5001

CMD ["python", "-m", "src.main"]
```

### F4 Resource Allocation

**Resource limits for F4 components:**

```yaml
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    environment:
      # F4 memory optimization
      - F4_CONFIG_CACHE_SIZE=256
      - F4_WORKER_MEMORY_LIMIT=1536M

  worker:
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 1.5G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      # F4 worker optimization
      - WORKER_CONCURRENCY=3
      - F4_PROVIDER_TIMEOUT=300
```

---

## Summary

This F4 Docker deployment guide provides:

- **Complete F4 dual-provider setup** with Azure Sora and Google Veo3
- **Environment-aware configuration** for development, staging, and production
- **Provider testing and validation** tools for Docker environments
- **Comprehensive troubleshooting** for F4-specific issues
- **Production-ready deployment** patterns with monitoring and scaling
- **Security best practices** for secrets and credential management

The F4 Docker deployment supports seamless provider switching, automatic failover, and environment-specific optimizations while maintaining backward compatibility with existing Azure Sora deployments.

**Next Steps:**
1. Follow the Quick Start for immediate F4 deployment
2. Configure both Azure Sora and Google Veo3 providers
3. Test provider switching and failover scenarios
4. Set up monitoring and alerting for production deployment
5. Scale based on load and performance requirements

**Support:** See [ENVIRONMENT_SETUP.md](./src/config/ENVIRONMENT_SETUP.md) for detailed F4 configuration patterns and [validate_config.py](./validate_config.py) for automated configuration validation.