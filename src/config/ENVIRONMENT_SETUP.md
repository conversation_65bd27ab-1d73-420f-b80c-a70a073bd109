# F4 Environment Configuration Setup Guide

**Complete developer environment setup guide for F4 dual-provider video generation system with Azure Sora and Google Veo3 integration.**

## Quick Start

### Prerequisites

```bash
# Install UV package manager (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone repository and setup environment
git clone <repository-url>
cd sora-poc
uv venv && source .venv/bin/activate && uv sync
```

### Zero-Configuration Setup

```bash
# 1. Copy environment template
cp .env.example .env.local

# 2. Basic configuration for local development
cat > .env.local << 'EOF'
# F4 Local Development Configuration
USE_MOCK_VEO=true
DEFAULT_PROVIDER=azure_sora
GOOGLE_PROJECT_ID=************
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=300

# Required Azure configuration (set your values)
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=preview
AZURE_OPENAI_DEPLOYMENT_NAME=sora
EOF

# 3. Test configuration
uv run python -c "
from src.config.factory import ConfigurationFactory
print('✅ Configuration loaded successfully')
config = ConfigurationFactory.create_provider_config('azure_sora')
print(f'✅ Azure Sora configured: {bool(config)}')
config = ConfigurationFactory.create_provider_config('google_veo3')  
print(f'✅ Google Veo3 configured: {bool(config)}')
"
```

## Environment Files Priority System

**F4 supports multiple environment files for different deployment scenarios:**

```bash
# Priority order (highest to lowest)
1. .env.local    # Local development (localhost services)
2. .env          # Fallback/legacy support  
3. .env.docker   # Docker deployment (container hostnames)
```

### Environment File Selection

| Scenario | File | Purpose |
|----------|------|---------|
| **Local Development** | `.env.local` | Localhost services, mock providers |
| **Docker Development** | `.env.docker` | Container hostnames, production simulation |
| **Production** | `.env` | Production variables, secure configuration |
| **Fallback** | `.env.example` | Template and documentation |

## Provider Configuration Guide

### Azure Sora Configuration

**Required environment variables:**

```bash
# Azure OpenAI Sora API Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=preview
AZURE_OPENAI_DEPLOYMENT_NAME=sora
```

**Configuration example:**

```python
from src.config.factory import ConfigurationFactory

# Create Azure Sora configuration
azure_config = ConfigurationFactory.create_provider_config("azure_sora")
print(f"Azure endpoint: {azure_config['endpoint']}")
print(f"API version: {azure_config['api_version']}")
```

### Google Veo3 Configuration

**Environment variables:**

```bash
# Google Veo3 Provider Configuration
USE_MOCK_VEO=true                    # true=mock, false=real API
DEFAULT_PROVIDER=google_veo3         # azure_sora|google_veo3
GOOGLE_PROJECT_ID=************       # Required: Google Cloud project ID
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=300                     # API timeout in seconds
VEO3_MAX_RETRIES=3                   # Retry attempts for failures
VEO3_RETRY_DELAY=2                   # Delay between retries
VEO3_GENERATION_TIMEOUT=1800         # Video generation timeout
VEO3_RATE_LIMIT_RPM=30              # Requests per minute limit
```

**Authentication options:**

```bash
# Option 1: Service Account Key File (Recommended for production)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Option 2: OAuth Client Credentials (Development)
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret

# Option 3: gcloud CLI (Local development)
# Run: gcloud auth application-default login
```

**Configuration example:**

```python
from src.config.factory import ConfigurationFactory

# Create Google Veo3 configuration with environment defaults
veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
print(f"Project ID: {veo3_config['project_id']}")
print(f"Mock mode: {veo3_config['use_mock']}")

# Create with overrides
veo3_test_config = ConfigurationFactory.create_provider_config(
    "google_veo3",
    use_mock=True,
    timeout=120
)
```

## Development Environment Setup

### Local Development (.env.local)

**Complete local development configuration:**

```bash
# F4 Local Development Configuration
# This file takes priority over .env and .env.docker

# =============================================================================
# PROVIDER CONFIGURATION
# =============================================================================
USE_MOCK_VEO=true
DEFAULT_PROVIDER=azure_sora
GOOGLE_PROJECT_ID=************
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=300

# Mock configuration for development
MOCK_MIN_GENERATION_TIME=10
MOCK_MAX_GENERATION_TIME=30
MOCK_RESPONSE_DELAY=0.1
MOCK_SIMULATE_FAILURES=false
MOCK_FAILURE_RATE=0.05

# =============================================================================
# AZURE CONFIGURATION (Required)
# =============================================================================
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=preview
AZURE_OPENAI_DEPLOYMENT_NAME=sora

# =============================================================================
# LOCAL SERVICES (localhost)
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
DATABASE_URL=sqlite:///sora_poc.db
FLASK_ENV=development
FLASK_DEBUG=true
RATE_LIMIT_ENABLED=false

# =============================================================================
# DEVELOPMENT OPTIMIZATIONS
# =============================================================================
WORKER_CONCURRENCY=1
LOG_LEVEL=DEBUG
VEO3_DEBUG_MODE=true
HEALTH_CHECK_ENABLED=true
```

### Docker Development (.env.docker)

**Docker environment configuration:**

```bash
# F4 Docker Development Configuration
# Used when running in Docker containers

# =============================================================================
# PROVIDER CONFIGURATION
# =============================================================================
USE_MOCK_VEO=false
DEFAULT_PROVIDER=google_veo3
GOOGLE_PROJECT_ID=your_production_project_id
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=600

# =============================================================================
# DOCKER SERVICES (container hostnames)
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
FLASK_ENV=production
FLASK_DEBUG=false
RATE_LIMIT_ENABLED=true

# =============================================================================
# PRODUCTION SIMULATION
# =============================================================================
WORKER_CONCURRENCY=4
LOG_LEVEL=INFO
VEO3_DEBUG_MODE=false
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
```

### Production Environment

**Production configuration patterns:**

```bash
# F4 Production Configuration
# Set via environment variables or secure secret management

# =============================================================================
# PROVIDER CONFIGURATION (Production)
# =============================================================================
USE_MOCK_VEO=false
DEFAULT_PROVIDER=azure_sora
GOOGLE_PROJECT_ID=your_production_project_id
GOOGLE_APPLICATION_CREDENTIALS=/secure/path/to/service-account.json

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
AZURE_OPENAI_API_KEY=${AZURE_API_KEY_SECRET}
SECRET_KEY=${FLASK_SECRET_KEY}
DB_PASSWORD=${DATABASE_PASSWORD}

# =============================================================================
# PRODUCTION OPTIMIZATIONS
# =============================================================================
FLASK_ENV=production
FLASK_DEBUG=false
LOG_LEVEL=WARNING
WORKER_CONCURRENCY=8
RATE_LIMIT_ENABLED=true
```

## Configuration Factory Usage

### Basic Provider Configuration

```python
from src.config.factory import ConfigurationFactory

# Get environment-specific configuration
config = ConfigurationFactory.get_base_config()

# Create provider configurations
azure_config = ConfigurationFactory.create_provider_config("azure_sora")
veo3_config = ConfigurationFactory.create_provider_config("google_veo3")

# Check provider availability
availability = ConfigurationFactory.get_provider_availability()
print(f"Azure Sora available: {availability['azure_sora']}")
print(f"Google Veo3 available: {availability['google_veo3']}")
```

### Environment-Aware Configuration

```python
from src.config.factory import ProviderConfigurationFactory

# Auto-detect environment and create optimal configuration
optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()
print(f"Selected provider: {optimal_config['selected_provider']}")

# Create environment-aware configuration
local_config = ProviderConfigurationFactory.create_environment_aware_config(
    "google_veo3",
    environment="local"
)

docker_config = ProviderConfigurationFactory.create_environment_aware_config(
    "google_veo3", 
    environment="docker"
)
```

### Advanced Configuration Patterns

```python
from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import get_cached_veo3_settings

# Use cached settings for performance
veo3_settings = get_cached_veo3_settings()
print(f"Mock mode: {veo3_settings.USE_MOCK_VEO}")
print(f"Project ID: {veo3_settings.GOOGLE_PROJECT_ID}")

# Create configuration with overrides
test_config = ConfigurationFactory.create_veo3_config(
    use_mock=True,
    project_id="test-project-12345",
    timeout=120,
    max_retries=2
)

# Validate configuration
validation = ConfigurationFactory.validate_provider_configuration("google_veo3")
if not validation['valid']:
    print(f"Configuration errors: {validation['errors']}")
```

## Testing Configuration

### Configuration Validation Script

**Create `validate_config.py` for testing:**

```python
#!/usr/bin/env python3
"""F4 Configuration validation script."""

import sys
from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import validate_veo3_environment

def validate_configuration():
    """Validate F4 configuration setup."""
    print("🔧 F4 Configuration Validation")
    print("=" * 50)
    
    try:
        # Test provider availability
        availability = ConfigurationFactory.get_provider_availability()
        print(f"✅ Azure Sora available: {availability['azure_sora']}")
        print(f"✅ Google Veo3 available: {availability['google_veo3']}")
        
        # Test configuration creation
        try:
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            print("✅ Azure Sora configuration created successfully")
        except Exception as e:
            print(f"❌ Azure Sora configuration failed: {e}")
        
        try:
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
            print("✅ Google Veo3 configuration created successfully")
        except Exception as e:
            print(f"❌ Google Veo3 configuration failed: {e}")
        
        # Validate environment
        veo3_validation = validate_veo3_environment()
        print(f"✅ Veo3 environment validation: {veo3_validation['valid']}")
        if not veo3_validation['valid']:
            for error in veo3_validation['errors']:
                print(f"   ❌ {error}")
        
        print("\n✅ Configuration validation completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

if __name__ == "__main__":
    success = validate_configuration()
    sys.exit(0 if success else 1)
```

**Run validation:**

```bash
uv run python validate_config.py
```

### Test Environment Switching

```python
import os
from src.config.factory import ConfigurationFactory

# Test environment switching
original_mock = os.getenv("USE_MOCK_VEO")

# Test mock mode
os.environ["USE_MOCK_VEO"] = "true"
ConfigurationFactory.clear_cache()
mock_config = ConfigurationFactory.create_provider_config("google_veo3")
print(f"Mock mode: {mock_config['use_mock']}")

# Test real mode
os.environ["USE_MOCK_VEO"] = "false"
ConfigurationFactory.clear_cache()
real_config = ConfigurationFactory.create_provider_config("google_veo3")
print(f"Real mode: {real_config['use_mock']}")

# Restore original
if original_mock:
    os.environ["USE_MOCK_VEO"] = original_mock
else:
    os.environ.pop("USE_MOCK_VEO", None)
```

## Common Development Workflows

### Switching Between Providers

```bash
# Switch to Azure Sora
echo "DEFAULT_PROVIDER=azure_sora" >> .env.local

# Switch to Google Veo3
echo "DEFAULT_PROVIDER=google_veo3" >> .env.local

# Test provider switch
uv run python -c "
from src.config.factory import ConfigurationFactory
provider = ConfigurationFactory.get_default_provider()
print(f'Default provider: {provider}')
"
```

### Mock vs Real API Testing

```bash
# Enable mock mode for development
echo "USE_MOCK_VEO=true" >> .env.local

# Test with real API (requires authentication)
echo "USE_MOCK_VEO=false" >> .env.local

# Validate configuration
uv run python validate_config.py
```

### Local + Docker Parallel Development

```bash
# Run local development
./scripts/start_local.sh
# Uses .env.local with localhost services

# Run Docker in parallel (different terminal)
./scripts/start_docker.sh  
# Uses .env.docker with container hostnames

# Both can run simultaneously without conflicts
```

## Google Cloud Authentication Setup

### Option 1: gcloud CLI (Recommended for Local Development)

```bash
# Install gcloud CLI
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authenticate with your Google account
gcloud auth login

# Set application default credentials
gcloud auth application-default login

# Set project
gcloud config set project your-project-id

# Verify authentication
gcloud auth list
```

### Option 2: Service Account Key (Production)

```bash
# 1. Create service account in Google Cloud Console
# 2. Download service account key JSON file
# 3. Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# 4. Add to .env.local
echo "GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json" >> .env.local
```

### Option 3: OAuth Client Credentials

```bash
# 1. Create OAuth 2.0 client in Google Cloud Console
# 2. Add credentials to environment
cat >> .env.local << 'EOF'
GOOGLE_CLIENT_ID=your_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_client_secret
EOF
```

## Docker Configuration

### Docker Environment Setup

**Create `.env.docker` for Docker deployment:**

```bash
# Copy template
cp .env.example .env.docker

# Configure for Docker
cat >> .env.docker << 'EOF'
# Docker-specific overrides
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
FLASK_ENV=production
USE_MOCK_VEO=false
WORKER_CONCURRENCY=4
EOF
```

### Docker Secrets Management

**Mount secrets in Docker Compose:**

```yaml
# docker-compose.yml
services:
  app:
    volumes:
      - ./secrets/google-credentials.json:/app/secrets/google-credentials.json:ro
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/secrets/google-credentials.json
```

## Performance Optimization

### Configuration Caching

```python
from src.config.veo3_settings import get_cached_veo3_settings

# Settings are cached automatically
settings = get_cached_veo3_settings()  # Loaded once
settings2 = get_cached_veo3_settings()  # Returns cached instance
assert settings is settings2  # Same object
```

### Environment Variable Caching

```python
from src.config.service import ConfigurationService

# Values are cached after first access
value1 = ConfigurationService.get("GOOGLE_PROJECT_ID")  # Loads from env
value2 = ConfigurationService.get("GOOGLE_PROJECT_ID")  # Returns cached

# Clear cache if needed
ConfigurationService.clear_cache()
```

## Troubleshooting Guide

### Common Issues

#### Issue: "Required configuration key 'GOOGLE_PROJECT_ID' is missing"

```bash
# Solution: Set GOOGLE_PROJECT_ID
echo "GOOGLE_PROJECT_ID=your-project-id" >> .env.local

# Verify
uv run python -c "
from src.config.veo3_settings import get_cached_veo3_settings
settings = get_cached_veo3_settings()
print(f'Project ID: {settings.GOOGLE_PROJECT_ID}')
"
```

#### Issue: "Cannot convert 'USE_MOCK_VEO' to boolean"

```bash
# Solution: Use valid boolean values
# Valid: true, false, 1, 0, yes, no, on, off, enabled, disabled
echo "USE_MOCK_VEO=true" >> .env.local  # Correct
# Avoid: USE_MOCK_VEO=True or USE_MOCK_VEO=TRUE
```

#### Issue: Google Cloud authentication failures

```bash
# Check authentication status
gcloud auth list

# Re-authenticate if needed
gcloud auth application-default login

# Verify project
gcloud config get-value project

# Test API access
gcloud services list --enabled | grep aiplatform
```

#### Issue: Environment file not loaded

```bash
# Check current working directory
pwd
# Should be in project root where .env files exist

# Verify file exists
ls -la .env*

# Check file priority
uv run python -c "
import os
print('Files found:')
for f in ['.env.local', '.env', '.env.docker']:
    if os.path.exists(f):
        print(f'  ✅ {f}')
    else:
        print(f'  ❌ {f}')
"
```

#### Issue: Docker hostname conflicts

```bash
# Use separate environment files
# Local: .env.local (localhost services)
# Docker: .env.docker (container hostnames)

# Verify configuration
docker-compose exec app python -c "
from src.config.service import ConfigurationService
print(f'Broker URL: {ConfigurationService.get(\"CELERY_BROKER_URL\")}')
"
```

### Configuration Debug Information

```python
from src.config.service import ConfigurationService

# Get debug information
debug_info = ConfigurationService.get_debug_info()
print("Configuration Debug Info:")
for key, value in debug_info.items():
    print(f"  {key}: {value}")
```

### Validation Commands

```bash
# Validate complete configuration
uv run python validate_config.py

# Test provider switching
uv run python -c "
from src.config.factory import ProviderConfigurationFactory
config = ProviderConfigurationFactory.get_optimal_provider_config()
print(f'Optimal provider: {config[\"selected_provider\"]}')
"

# Check environment detection
uv run python -c "
from src.config.service import ConfigurationService
deployment_type = ConfigurationService._detect_deployment_type()
print(f'Deployment type: {deployment_type}')
"
```

## Performance Targets

**F4 configuration system performance benchmarks:**

| Operation | Target | Typical |
|-----------|--------|---------|
| Settings load time | < 50ms | ~20ms |
| Factory creation | < 10ms | ~5ms |
| Validation time | < 100ms | ~30ms |
| Cache hit time | < 1ms | ~0.5ms |

**Monitor performance:**

```python
import time
from src.config.factory import ConfigurationFactory

start_time = time.time()
config = ConfigurationFactory.create_provider_config("google_veo3")
load_time = (time.time() - start_time) * 1000
print(f"Configuration load time: {load_time:.2f}ms")
```

## Development Standards

### Code Quality Requirements

- **Type Safety**: All configuration objects use Pydantic v2 validation
- **Error Handling**: Comprehensive validation with meaningful error messages  
- **Documentation**: Google-style docstrings for all configuration methods
- **Testing**: Co-located tests in `src/config/tests/` directory
- **Caching**: LRU cache for expensive configuration operations

### Configuration Best Practices

- **Environment Variables**: Use environment variables for all configuration
- **Factory Pattern**: Use ConfigurationFactory for all configuration creation
- **Validation First**: Always validate configuration before use
- **Secure Defaults**: Default to secure/mock configuration for development
- **Documentation**: Document all environment variables and their purpose

---

**Next Steps:**
1. Copy appropriate environment template to `.env.local`
2. Configure Azure and/or Google Cloud credentials
3. Run validation script to verify setup
4. Start development with `./scripts/start_local.sh`

**Support:** See `src/config/CLAUDE.md` for detailed implementation patterns and `validate_config.py` for automated validation.