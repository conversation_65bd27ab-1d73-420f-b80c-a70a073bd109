"""Configuration health validation and system readiness checks."""

import logging
import time
from datetime import datetime, timezone
from typing import Any, Dict, List

from pydantic import BaseModel, Field

from src.config.environment_manager import EnvironmentVariableManager
from src.config.factory.credentials import GoogleCloudCredentialManager
from src.config.factory.provider_factory import ProviderConfigurationManager
from src.config.factory.validation import ConfigurationValidator

logger = logging.getLogger(__name__)


class HealthCheckResult(BaseModel):
    """Result of a configuration health check."""

    component: str
    status: str = Field(..., description="healthy, warning, unhealthy")
    message: str
    details: Dict[str, Any] = Field(default_factory=dict)
    check_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    class Config:
        frozen = True


class SystemHealthStatus(BaseModel):
    """Overall system health status."""

    overall_status: str = Field(..., description="healthy, warning, unhealthy")
    checks: List[HealthCheckResult]
    summary: Dict[str, int] = Field(default_factory=dict)
    check_duration_ms: float

    class Config:
        frozen = True


class ConfigurationHealthChecker:
    """
    Comprehensive configuration health validation.

    This class provides health checks for all configuration components
    including environment variables, provider configurations, credentials,
    and system readiness.
    """

    @classmethod
    def run_all_checks(cls) -> SystemHealthStatus:
        """
        Run all configuration health checks.

        Returns:
            SystemHealthStatus with overall status and individual check results
        """
        start_time = time.time()
        checks = []

        # Run individual health checks
        checks.append(cls.check_environment_configuration())
        checks.append(cls.check_provider_availability())
        checks.append(cls.check_google_credentials())
        checks.append(cls.check_azure_configuration())
        checks.append(cls.check_performance_targets())

        # Calculate summary
        summary = {
            "healthy": len([c for c in checks if c.status == "healthy"]),
            "warning": len([c for c in checks if c.status == "warning"]),
            "unhealthy": len([c for c in checks if c.status == "unhealthy"]),
        }

        # Determine overall status
        if summary["unhealthy"] > 0:
            overall_status = "unhealthy"
        elif summary["warning"] > 0:
            overall_status = "warning"
        else:
            overall_status = "healthy"

        duration_ms = (time.time() - start_time) * 1000

        return SystemHealthStatus(
            overall_status=overall_status,
            checks=checks,
            summary=summary,
            check_duration_ms=duration_ms,
        )

    @classmethod
    def check_environment_configuration(cls) -> HealthCheckResult:
        """Check environment variable configuration health."""
        try:
            validation = EnvironmentVariableManager.validate_environment_consistency()

            if validation["environment_consistent"]:
                status = "healthy"
                message = f"Environment consistent for {validation['deployment_type']} deployment"
            else:
                status = "warning"
                message = f"Environment issues detected: {len(validation['warnings'])} warnings"

            return HealthCheckResult(
                component="environment_configuration",
                status=status,
                message=message,
                details=validation,
            )

        except Exception as e:
            logger.exception("Environment configuration check failed")
            return HealthCheckResult(
                component="environment_configuration",
                status="unhealthy",
                message=f"Environment check failed: {str(e)}",
                details={"error": str(e)},
            )

    @classmethod
    def check_provider_availability(cls) -> HealthCheckResult:
        """Check video generation provider availability."""
        try:
            availability = ProviderConfigurationManager.get_provider_availability()
            available_providers = [
                p for p, available in availability.items() if available
            ]

            if len(available_providers) >= 1:
                status = "healthy"
                message = f"Providers available: {', '.join(available_providers)}"
            else:
                status = "unhealthy"
                message = "No video generation providers available"

            return HealthCheckResult(
                component="provider_availability",
                status=status,
                message=message,
                details={
                    "availability": availability,
                    "available_count": len(available_providers),
                },
            )

        except Exception as e:
            logger.exception("Provider availability check failed")
            return HealthCheckResult(
                component="provider_availability",
                status="unhealthy",
                message=f"Provider check failed: {str(e)}",
                details={"error": str(e)},
            )

    @classmethod
    def check_google_credentials(cls) -> HealthCheckResult:
        """Check Google Cloud credentials health."""
        try:
            cred_info = GoogleCloudCredentialManager.get_credentials_for_config()

            if cred_info is None:
                status = "warning"
                message = "No Google Cloud credentials found"
                details = {"found": False}
            else:
                validation = cred_info["validation"]
                if validation["valid"]:
                    status = "healthy"
                    message = f"Google credentials valid from {cred_info['source']}"
                else:
                    status = "warning"
                    message = (
                        f"Google credentials issues: {len(validation['errors'])} errors"
                    )

                details = {
                    "found": True,
                    "source": cred_info["source"],
                    "project_id": cred_info["project_id"],
                    "validation": validation,
                }

            return HealthCheckResult(
                component="google_credentials",
                status=status,
                message=message,
                details=details,
            )

        except Exception as e:
            logger.exception("Google credentials check failed")
            return HealthCheckResult(
                component="google_credentials",
                status="unhealthy",
                message=f"Credential check failed: {str(e)}",
                details={"error": str(e)},
            )

    @classmethod
    def check_azure_configuration(cls) -> HealthCheckResult:
        """Check Azure Sora configuration health."""
        try:
            config = ProviderConfigurationManager.create_provider_config("azure_sora")
            validation = ConfigurationValidator.validate_azure_sora_config(config)

            if validation.valid:
                status = "healthy"
                message = "Azure Sora configuration valid"
            else:
                status = "warning" if len(validation.warnings) > 0 else "unhealthy"
                message = f"Azure config issues: {len(validation.errors)} errors, {len(validation.warnings)} warnings"

            return HealthCheckResult(
                component="azure_configuration",
                status=status,
                message=message,
                details={
                    "validation": validation.dict(),
                    "endpoint_configured": bool(config.get("endpoint")),
                },
            )

        except Exception as e:
            logger.exception("Azure configuration check failed")
            return HealthCheckResult(
                component="azure_configuration",
                status="unhealthy",
                message=f"Azure config check failed: {str(e)}",
                details={"error": str(e)},
            )

    @classmethod
    def check_performance_targets(cls) -> HealthCheckResult:
        """Check configuration performance targets."""
        try:
            # Test configuration creation performance
            start_time = time.time()

            # Test base config creation
            config_start = time.time()
            ProviderConfigurationManager.get_provider_availability()
            config_time = (time.time() - config_start) * 1000

            # Test provider switching
            switch_start = time.time()
            ProviderConfigurationManager.create_provider_config("azure_sora")
            switch_time = (time.time() - switch_start) * 1000

            total_time = (time.time() - start_time) * 1000

            # Check against targets
            config_target = 10  # ms
            switch_target = 50  # ms

            performance_issues = []
            if config_time > config_target:
                performance_issues.append(
                    f"Config creation: {config_time:.1f}ms > {config_target}ms target"
                )

            if switch_time > switch_target:
                performance_issues.append(
                    f"Provider switching: {switch_time:.1f}ms > {switch_target}ms target"
                )

            if len(performance_issues) == 0:
                status = "healthy"
                message = f"Performance targets met ({total_time:.1f}ms total)"
            else:
                status = "warning"
                message = f"Performance issues: {'; '.join(performance_issues)}"

            return HealthCheckResult(
                component="performance_targets",
                status=status,
                message=message,
                details={
                    "config_creation_ms": config_time,
                    "provider_switching_ms": switch_time,
                    "total_time_ms": total_time,
                    "targets": {
                        "config_creation_target_ms": config_target,
                        "provider_switching_target_ms": switch_target,
                    },
                },
            )

        except Exception as e:
            logger.exception("Performance targets check failed")
            return HealthCheckResult(
                component="performance_targets",
                status="unhealthy",
                message=f"Performance check failed: {str(e)}",
                details={"error": str(e)},
            )

    @classmethod
    def get_configuration_summary(cls) -> Dict[str, Any]:
        """
        Get comprehensive configuration summary.

        Returns:
            Dictionary with configuration status and details
        """
        health_status = cls.run_all_checks()
        env_config = EnvironmentVariableManager.get_environment_config()

        return {
            "health_status": health_status.dict(),
            "environment": env_config.dict(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "ready_for_production": health_status.overall_status
            in ["healthy", "warning"],
        }
