#!/usr/bin/env python3
"""
F4 Configuration Troubleshooting Script

Comprehensive troubleshooting script for F4 dual-provider configuration issues.
Diagnoses common problems, provides solutions, and validates fixes.

Usage:
    python troubleshoot_f4_config.py                    # Full diagnostic
    python troubleshoot_f4_config.py --issue imports    # Test specific issue
    python troubleshoot_f4_config.py --fix              # Auto-fix common issues
    python troubleshoot_f4_config.py --interactive      # Interactive troubleshooting
"""

import argparse
import os
import sys
import time
from typing import List, Tuple


def check_environment_setup() -> Tuple[bool, List[str], List[str]]:
    """Check basic environment setup."""
    print("\n🔍 Checking Environment Setup")
    print("=" * 50)

    issues = []
    fixes = []

    # Check working directory
    current_dir = os.getcwd()
    expected_files = ["pyproject.toml", "src", ".env.example", "CLAUDE.md"]

    print(f"Current directory: {current_dir}")

    missing_files = []
    for file in expected_files:
        if os.path.exists(file):
            print(f"✅ {file} found")
        else:
            print(f"❌ {file} missing")
            missing_files.append(file)

    if missing_files:
        issues.append(f"Missing required files: {', '.join(missing_files)}")
        fixes.append("Ensure you're in the sora-poc project root directory")

    # Check Python environment
    try:
        import uv

        print("✅ UV package manager available")
    except ImportError:
        print("❌ UV package manager not found")
        issues.append("UV package manager not available")
        fixes.append("Install UV: curl -LsSf https://astral.sh/uv/install.sh | sh")

    # Check virtual environment
    if hasattr(sys, "real_prefix") or (
        hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
    ):
        print("✅ Virtual environment active")
    else:
        print("❌ No virtual environment detected")
        issues.append("Virtual environment not active")
        fixes.append("Activate virtual environment: source .venv/bin/activate")

    return len(issues) == 0, issues, fixes


def check_imports() -> Tuple[bool, List[str], List[str]]:
    """Check F4 module imports."""
    print("\n📦 Checking F4 Module Imports")
    print("=" * 50)

    issues = []
    fixes = []

    required_modules = [
        ("src.config.factory", "ConfigurationFactory"),
        ("src.config.veo3_settings", "get_cached_veo3_settings"),
        ("src.config.service", "ConfigurationService"),
        ("pydantic", "BaseModel"),
        ("pydantic_settings", "BaseSettings"),
    ]

    for module_name, class_name in required_modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
        except ImportError as e:
            print(f"❌ {module_name}.{class_name}: {e}")
            issues.append(f"Cannot import {module_name}.{class_name}")
            if "src.config" in module_name:
                fixes.append(
                    "Ensure you're in the project root and virtual environment is active"
                )
            else:
                fixes.append(
                    f"Install missing dependency: uv add {module_name.split('.')[0]}"
                )
        except AttributeError:
            print(f"❌ {module_name}.{class_name}: Class not found")
            issues.append(f"Class {class_name} not found in {module_name}")
            fixes.append(f"Check if {module_name} is up to date")

    return len(issues) == 0, issues, fixes


def check_environment_files() -> Tuple[bool, List[str], List[str]]:
    """Check environment files configuration."""
    print("\n📄 Checking Environment Files")
    print("=" * 50)

    issues = []
    fixes = []

    # Check environment file existence
    env_files = [".env.example", ".env", ".env.local", ".env.docker"]

    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"✅ {env_file} exists")
        else:
            print(f"❌ {env_file} missing")
            if env_file == ".env.example":
                issues.append("Template environment file missing")
                fixes.append("Restore .env.example from repository")
            elif env_file == ".env.local":
                fixes.append("Create .env.local: cp .env.example .env.local")

    # Check environment file priority
    print("\nEnvironment file priority:")
    priority_files = [".env.local", ".env", ".env.docker"]

    for i, env_file in enumerate(priority_files, 1):
        exists = os.path.exists(env_file)
        status = "✅" if exists else "❌"
        print(f"   {i}. {env_file}: {status}")

    # Check for at least one configuration file
    if not any(os.path.exists(f) for f in [".env", ".env.local"]):
        issues.append("No environment configuration files found")
        fixes.append("Create configuration: cp .env.example .env.local")

    return len(issues) == 0, issues, fixes


def check_required_environment_variables() -> Tuple[bool, List[str], List[str]]:
    """Check required environment variables."""
    print("\n🔧 Checking Required Environment Variables")
    print("=" * 50)

    issues = []
    fixes = []

    # Load environment if needed
    try:
        from dotenv import load_dotenv

        load_dotenv(".env.local")
        load_dotenv(".env")
    except ImportError:
        print("⚠️  python-dotenv not available, checking system environment only")

    # Required for Azure Sora
    azure_vars = {
        "AZURE_OPENAI_API_KEY": "Azure OpenAI API key",
        "AZURE_OPENAI_ENDPOINT": "Azure OpenAI endpoint URL",
        "AZURE_OPENAI_API_VERSION": "Azure OpenAI API version",
        "AZURE_OPENAI_DEPLOYMENT_NAME": "Azure OpenAI deployment name",
    }

    print("Azure Sora configuration:")
    azure_missing = []

    for var, description in azure_vars.items():
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "KEY" in var:
                display_value = f"{value[:8]}..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"   ✅ {var}: {display_value}")
        else:
            print(f"   ❌ {var}: Not set")
            azure_missing.append(var)

    if azure_missing:
        issues.append(f"Missing Azure variables: {', '.join(azure_missing)}")
        fixes.append("Set Azure variables in .env.local (see Azure OpenAI portal)")

    # Required for Google Veo3
    veo3_vars = {
        "GOOGLE_PROJECT_ID": "Google Cloud project ID",
        "USE_MOCK_VEO": "Enable mock mode for development",
    }

    print("\nGoogle Veo3 configuration:")
    veo3_missing = []

    for var, description in veo3_vars.items():
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: Not set")
            veo3_missing.append(var)

    if veo3_missing:
        issues.append(f"Missing Veo3 variables: {', '.join(veo3_missing)}")
        fixes.append(
            "Set Veo3 variables in .env.local (GOOGLE_PROJECT_ID required, USE_MOCK_VEO=true for development)"
        )

    # Authentication check for Veo3
    use_mock = os.getenv("USE_MOCK_VEO", "").lower() == "true"

    if not use_mock:
        auth_vars = ["GOOGLE_APPLICATION_CREDENTIALS", "GOOGLE_CLIENT_ID"]
        auth_available = any(os.getenv(var) for var in auth_vars)

        print("\nGoogle Veo3 authentication (mock mode disabled):")
        if auth_available:
            for var in auth_vars:
                value = os.getenv(var)
                if value:
                    print(f"   ✅ {var}: Set")
        else:
            print("   ❌ No authentication configured")
            issues.append(
                "Google Veo3 authentication not configured (mock mode disabled)"
            )
            fixes.append(
                "Set GOOGLE_APPLICATION_CREDENTIALS or enable mock mode: USE_MOCK_VEO=true"
            )

    return len(issues) == 0, issues, fixes


def check_configuration_creation() -> Tuple[bool, List[str], List[str]]:
    """Check configuration object creation."""
    print("\n⚙️  Checking Configuration Creation")
    print("=" * 50)

    issues = []
    fixes = []

    try:
        from src.config.factory import ConfigurationFactory

        # Test Azure Sora configuration
        print("Testing Azure Sora configuration creation...")
        try:
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            print("✅ Azure Sora configuration created")

            # Check required keys
            required_keys = ["endpoint", "api_key", "api_version", "deployment_name"]
            missing_keys = [key for key in required_keys if not azure_config.get(key)]

            if missing_keys:
                print(f"⚠️  Azure config missing keys: {missing_keys}")
                issues.append(f"Azure configuration incomplete: {missing_keys}")
                fixes.append("Check Azure environment variables are set correctly")

        except Exception as e:
            print(f"❌ Azure Sora configuration failed: {e}")
            issues.append(f"Azure configuration creation failed: {e}")
            fixes.append("Check Azure environment variables and values")

        # Test Google Veo3 configuration
        print("\nTesting Google Veo3 configuration creation...")
        try:
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
            print("✅ Google Veo3 configuration created")

            # Check configuration values
            project_id = veo3_config.get("project_id")
            use_mock = veo3_config.get("use_mock")

            if not project_id:
                print("⚠️  No project ID in Veo3 config")
                issues.append("Google Veo3 missing project ID")
                fixes.append("Set GOOGLE_PROJECT_ID in environment")

            print(f"   Project ID: {project_id}")
            print(f"   Mock mode: {use_mock}")

        except Exception as e:
            print(f"❌ Google Veo3 configuration failed: {e}")
            issues.append(f"Veo3 configuration creation failed: {e}")
            fixes.append("Check Google Veo3 environment variables")

    except ImportError as e:
        print(f"❌ Cannot import ConfigurationFactory: {e}")
        issues.append("Cannot import F4 configuration modules")
        fixes.append("Ensure you're in project root and virtual environment is active")

    return len(issues) == 0, issues, fixes


def check_provider_validation() -> Tuple[bool, List[str], List[str]]:
    """Check provider configuration validation."""
    print("\n✅ Checking Provider Validation")
    print("=" * 50)

    issues = []
    fixes = []

    try:
        from src.config.factory import ConfigurationFactory

        providers = ["azure_sora", "google_veo3"]

        for provider in providers:
            print(f"\nValidating {provider}...")

            try:
                validation = ConfigurationFactory.validate_provider_configuration(
                    provider
                )

                if validation.get("valid"):
                    print(f"✅ {provider} validation passed")
                else:
                    print(f"❌ {provider} validation failed")
                    errors = validation.get("errors", [])
                    for error in errors:
                        print(f"   • {error}")

                    issues.append(f"{provider} validation failed: {', '.join(errors)}")

                    if provider == "azure_sora":
                        fixes.append("Check Azure OpenAI configuration in Azure portal")
                    else:
                        fixes.append(
                            "Check Google Cloud project configuration and authentication"
                        )

                # Show configuration details
                config_details = validation.get("configuration", {})
                for key, value in config_details.items():
                    if isinstance(value, bool):
                        status = "✅" if value else "❌"
                        print(f"   {key}: {status}")
                    else:
                        print(f"   {key}: {value}")

            except Exception as e:
                print(f"❌ {provider} validation error: {e}")
                issues.append(f"{provider} validation error: {e}")
                fixes.append(f"Check {provider} configuration and dependencies")

    except ImportError as e:
        print(f"❌ Cannot import validation modules: {e}")
        issues.append("Cannot import validation modules")
        fixes.append("Check F4 module imports")

    return len(issues) == 0, issues, fixes


def check_common_issues() -> Tuple[bool, List[str], List[str]]:
    """Check for common F4 configuration issues."""
    print("\n🔍 Checking Common Issues")
    print("=" * 50)

    issues = []
    fixes = []

    # Check boolean environment variable format
    boolean_vars = ["USE_MOCK_VEO", "FLASK_DEBUG", "RATE_LIMIT_ENABLED"]

    print("Checking boolean environment variables...")
    for var in boolean_vars:
        value = os.getenv(var)
        if value:
            if value.lower() in [
                "true",
                "false",
                "1",
                "0",
                "yes",
                "no",
                "on",
                "off",
                "enabled",
                "disabled",
            ]:
                print(f"✅ {var}: {value} (valid format)")
            else:
                print(f"❌ {var}: {value} (invalid format)")
                issues.append(f"Invalid boolean format for {var}: {value}")
                fixes.append(
                    f"Set {var} to true/false, 1/0, yes/no, on/off, or enabled/disabled"
                )

    # Check numeric environment variables
    numeric_vars = ["VEO3_TIMEOUT", "VEO3_MAX_RETRIES", "PORT"]

    print("\nChecking numeric environment variables...")
    for var in numeric_vars:
        value = os.getenv(var)
        if value:
            try:
                int(value)
                print(f"✅ {var}: {value} (valid number)")
            except ValueError:
                print(f"❌ {var}: {value} (not a number)")
                issues.append(f"Invalid numeric format for {var}: {value}")
                fixes.append(f"Set {var} to a valid integer")

    # Check file paths
    file_vars = ["GOOGLE_APPLICATION_CREDENTIALS"]

    print("\nChecking file path variables...")
    for var in file_vars:
        value = os.getenv(var)
        if value:
            if os.path.exists(value):
                print(f"✅ {var}: File exists")
            else:
                print(f"❌ {var}: File not found: {value}")
                issues.append(f"File not found for {var}: {value}")
                fixes.append(f"Ensure file exists at {value} or update path")

    # Check for conflicting settings
    print("\nChecking for conflicting settings...")

    use_mock = os.getenv("USE_MOCK_VEO", "").lower() == "true"
    default_provider = os.getenv("DEFAULT_PROVIDER", "")

    if default_provider == "google_veo3" and not use_mock:
        google_project_id = os.getenv("GOOGLE_PROJECT_ID")
        auth_configured = os.getenv("GOOGLE_APPLICATION_CREDENTIALS") or os.getenv(
            "GOOGLE_CLIENT_ID"
        )

        if not google_project_id or not auth_configured:
            print("❌ Google Veo3 set as default but not properly configured")
            issues.append("Google Veo3 default provider not properly configured")
            fixes.append(
                "Either enable mock mode (USE_MOCK_VEO=true) or configure authentication"
            )

    return len(issues) == 0, issues, fixes


def check_performance_issues() -> Tuple[bool, List[str], List[str]]:
    """Check for performance-related issues."""
    print("\n⚡ Checking Performance Issues")
    print("=" * 50)

    issues = []
    fixes = []

    try:
        from src.config.factory import ConfigurationFactory
        from src.config.veo3_settings import get_cached_veo3_settings

        # Test configuration creation performance
        print("Testing configuration creation performance...")

        providers = ["azure_sora", "google_veo3"]
        performance_results = {}

        for provider in providers:
            try:
                # Measure configuration creation time
                start_time = time.time()
                config = ConfigurationFactory.create_provider_config(provider)
                creation_time = (time.time() - start_time) * 1000

                performance_results[provider] = creation_time

                # Check against target (50ms)
                if creation_time < 50:
                    print(f"✅ {provider}: {creation_time:.2f}ms (good)")
                elif creation_time < 100:
                    print(f"⚠️  {provider}: {creation_time:.2f}ms (acceptable)")
                else:
                    print(f"❌ {provider}: {creation_time:.2f}ms (slow)")
                    issues.append(
                        f"{provider} configuration creation slow: {creation_time:.2f}ms"
                    )
                    fixes.append(
                        "Check for network issues or complex environment variable parsing"
                    )

            except Exception as e:
                print(f"❌ {provider}: Error during performance test: {e}")

        # Test settings caching
        print("\nTesting settings caching...")

        # First load (should be slower)
        start_time = time.time()
        settings1 = get_cached_veo3_settings()
        first_load_time = (time.time() - start_time) * 1000

        # Second load (should be cached)
        start_time = time.time()
        settings2 = get_cached_veo3_settings()
        cached_load_time = (time.time() - start_time) * 1000

        print(f"First load: {first_load_time:.2f}ms")
        print(f"Cached load: {cached_load_time:.2f}ms")

        # Check cache effectiveness
        if settings1 is settings2:
            print("✅ Settings caching working correctly")
        else:
            print("❌ Settings caching not working")
            issues.append("Settings caching not effective")
            fixes.append("Check LRU cache implementation")

        # Check cache performance
        if cached_load_time > 1.0:  # 1ms target for cache hits
            print(f"⚠️  Cache access slower than expected: {cached_load_time:.2f}ms")
            issues.append("Cache access performance below target")
            fixes.append("Check system performance and cache implementation")

    except ImportError as e:
        print(f"❌ Cannot import performance test modules: {e}")
        issues.append("Cannot test performance - import error")
        fixes.append("Check F4 module imports")

    return len(issues) == 0, issues, fixes


def auto_fix_common_issues() -> Tuple[int, List[str]]:
    """Attempt to automatically fix common issues."""
    print("\n🔧 Auto-Fixing Common Issues")
    print("=" * 50)

    fixes_applied = []

    # Create .env.local if missing
    if not os.path.exists(".env.local") and os.path.exists(".env.example"):
        try:
            with open(".env.example") as src, open(".env.local", "w") as dst:
                content = src.read()
                # Add F4 development defaults
                content += """
# F4 Development Configuration (auto-generated)
USE_MOCK_VEO=true
DEFAULT_PROVIDER=google_veo3
GOOGLE_PROJECT_ID=134075247963
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=300
"""
                dst.write(content)

            print("✅ Created .env.local with F4 development defaults")
            fixes_applied.append("Created .env.local configuration file")

        except Exception as e:
            print(f"❌ Failed to create .env.local: {e}")

    # Fix common boolean variable formats
    boolean_fixes = {
        "True": "true",
        "False": "false",
        "TRUE": "true",
        "FALSE": "false",
        "Yes": "true",
        "No": "false",
    }

    if os.path.exists(".env.local"):
        try:
            with open(".env.local") as f:
                content = f.read()

            original_content = content

            for wrong, right in boolean_fixes.items():
                content = content.replace(f"={wrong}", f"={right}")

            if content != original_content:
                with open(".env.local", "w") as f:
                    f.write(content)

                print("✅ Fixed boolean variable formats in .env.local")
                fixes_applied.append("Fixed boolean environment variable formats")

        except Exception as e:
            print(f"❌ Failed to fix .env.local: {e}")

    # Set missing essential variables
    essential_vars = {
        "USE_MOCK_VEO": "true",
        "DEFAULT_PROVIDER": "google_veo3",
        "GOOGLE_PROJECT_ID": "134075247963",
    }

    env_additions = []

    for var, default_value in essential_vars.items():
        if not os.getenv(var):
            env_additions.append(f"{var}={default_value}")

    if env_additions and os.path.exists(".env.local"):
        try:
            with open(".env.local", "a") as f:
                f.write("\n# Auto-added essential F4 variables\n")
                for addition in env_additions:
                    f.write(f"{addition}\n")

            print(f"✅ Added {len(env_additions)} essential variables to .env.local")
            fixes_applied.append(
                f"Added {len(env_additions)} essential environment variables"
            )

        except Exception as e:
            print(f"❌ Failed to add variables to .env.local: {e}")

    return len(fixes_applied), fixes_applied


def interactive_troubleshooting():
    """Interactive troubleshooting session."""
    print("\n🔧 Interactive F4 Troubleshooting")
    print("=" * 50)

    print("This will guide you through common F4 configuration issues.")
    print("Press Enter to continue or Ctrl+C to exit.\n")

    try:
        input()
    except KeyboardInterrupt:
        print("\nTroubleshooting cancelled.")
        return

    # Step 1: Basic setup
    print("Step 1: Checking basic setup...")
    setup_ok, setup_issues, setup_fixes = check_environment_setup()

    if not setup_ok:
        print("\n❌ Basic setup issues found:")
        for issue in setup_issues:
            print(f"   • {issue}")

        print("\nSuggested fixes:")
        for fix in setup_fixes:
            print(f"   • {fix}")

        response = input("\nWould you like me to help fix these? (y/n): ").lower()

        if response == "y":
            print("Please apply the suggested fixes and run this script again.")
            return

    # Step 2: Environment files
    print("\nStep 2: Checking environment files...")
    env_ok, env_issues, env_fixes = check_environment_files()

    if not env_ok:
        print("\n❌ Environment file issues found:")
        for issue in env_issues:
            print(f"   • {issue}")

        response = input(
            "\nWould you like me to auto-fix environment files? (y/n): "
        ).lower()

        if response == "y":
            fixes_count, fixes_applied = auto_fix_common_issues()
            if fixes_count > 0:
                print(f"✅ Applied {fixes_count} automatic fixes")
                for fix in fixes_applied:
                    print(f"   • {fix}")

    # Step 3: Configuration testing
    print("\nStep 3: Testing configuration creation...")
    config_ok, config_issues, config_fixes = check_configuration_creation()

    if not config_ok:
        print("\n❌ Configuration issues found:")
        for issue in config_issues:
            print(f"   • {issue}")

        print("\nSuggested fixes:")
        for fix in config_fixes:
            print(f"   • {fix}")

        response = input(
            "\nWould you like specific help with any provider? (azure/veo3/n): "
        ).lower()

        if response == "azure":
            print("\nAzure Sora troubleshooting:")
            print("1. Get API key from Azure OpenAI portal")
            print("2. Set AZURE_OPENAI_API_KEY in .env.local")
            print("3. Set AZURE_OPENAI_ENDPOINT to your resource URL")
            print("4. Set AZURE_OPENAI_DEPLOYMENT_NAME to 'sora'")

        elif response == "veo3":
            print("\nGoogle Veo3 troubleshooting:")
            print("1. Set GOOGLE_PROJECT_ID to your Google Cloud project")
            print("2. For development: USE_MOCK_VEO=true")
            print("3. For production: Set up authentication")
            print("   - Service account: GOOGLE_APPLICATION_CREDENTIALS")
            print("   - OAuth: GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET")

    # Final validation
    print("\nStep 4: Final validation...")

    try:
        from src.config.factory import ConfigurationFactory

        availability = ConfigurationFactory.get_provider_availability()
        available_providers = [p for p, avail in availability.items() if avail]

        if available_providers:
            print(f"✅ Available providers: {', '.join(available_providers)}")
            print("🚀 F4 configuration troubleshooting completed!")
            print("\nYou can now use:")
            print("   python validate_config.py  # Full validation")
            print("   python examples_f4_basic_usage.py  # Test usage patterns")
        else:
            print("❌ No providers available after troubleshooting")
            print("📖 See ENVIRONMENT_SETUP.md for detailed setup instructions")

    except Exception as e:
        print(f"❌ Final validation failed: {e}")
        print("📖 Check imports and basic setup")


def main():
    """Main troubleshooting function."""
    parser = argparse.ArgumentParser(description="F4 Configuration Troubleshooting")
    parser.add_argument(
        "--issue",
        choices=["imports", "env", "config", "validation", "performance"],
        help="Check specific issue type",
    )
    parser.add_argument("--fix", action="store_true", help="Auto-fix common issues")
    parser.add_argument(
        "--interactive", action="store_true", help="Interactive troubleshooting"
    )
    args = parser.parse_args()

    print("🔧 F4 Configuration Troubleshooting")
    print("=" * 50)
    print("Diagnosing F4 dual-provider configuration issues")
    print("")

    if args.interactive:
        interactive_troubleshooting()
        return

    if args.fix:
        fixes_count, fixes_applied = auto_fix_common_issues()
        if fixes_count > 0:
            print(f"✅ Applied {fixes_count} automatic fixes:")
            for fix in fixes_applied:
                print(f"   • {fix}")
            print("\nRun the script again to validate fixes.")
        else:
            print("ℹ️  No automatic fixes available or needed.")
        return

    # Run specific checks or all checks
    if args.issue:
        check_functions = {
            "imports": check_imports,
            "env": check_environment_files,
            "config": check_configuration_creation,
            "validation": check_provider_validation,
            "performance": check_performance_issues,
        }

        if args.issue in check_functions:
            success, issues, fixes = check_functions[args.issue]()

            if success:
                print(f"✅ {args.issue} check passed")
            else:
                print(f"❌ {args.issue} issues found:")
                for issue in issues:
                    print(f"   • {issue}")
                print("\nSuggested fixes:")
                for fix in fixes:
                    print(f"   • {fix}")

        return

    # Full diagnostic
    all_checks = [
        ("Environment Setup", check_environment_setup),
        ("Module Imports", check_imports),
        ("Environment Files", check_environment_files),
        ("Environment Variables", check_required_environment_variables),
        ("Configuration Creation", check_configuration_creation),
        ("Provider Validation", check_provider_validation),
        ("Common Issues", check_common_issues),
        ("Performance", check_performance_issues),
    ]

    results = {}
    all_issues = []
    all_fixes = []

    for check_name, check_func in all_checks:
        try:
            success, issues, fixes = check_func()
            results[check_name] = success
            all_issues.extend(issues)
            all_fixes.extend(fixes)
        except Exception as e:
            print(f"❌ {check_name} check failed: {e}")
            results[check_name] = False
            all_issues.append(f"{check_name} check failed: {e}")

    # Summary
    print("\n📋 Troubleshooting Summary")
    print("=" * 50)

    successful_checks = sum(1 for success in results.values() if success)
    total_checks = len(results)

    for check_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {check_name}")

    print(f"\nOverall: {successful_checks}/{total_checks} checks passed")

    if successful_checks == total_checks:
        print("🚀 All F4 configuration checks passed!")
        print("✅ F4 dual-provider system is properly configured")
        print("\n💡 Next steps:")
        print("   python validate_config.py  # Validate configuration")
        print("   python examples_f4_basic_usage.py  # Test usage patterns")
    else:
        print(f"\n❌ {len(all_issues)} issues found:")
        for issue in all_issues[:10]:  # Show first 10 issues
            print(f"   • {issue}")

        if len(all_issues) > 10:
            print(f"   ... and {len(all_issues) - 10} more")

        print("\n🔧 Suggested fixes:")
        unique_fixes = list(dict.fromkeys(all_fixes))  # Remove duplicates
        for fix in unique_fixes[:10]:  # Show first 10 fixes
            print(f"   • {fix}")

        if len(unique_fixes) > 10:
            print(f"   ... and {len(unique_fixes) - 10} more")

        print("\n💡 Quick fixes:")
        print("   python troubleshoot_f4_config.py --fix  # Auto-fix common issues")
        print("   python troubleshoot_f4_config.py --interactive  # Interactive help")
        print("   📖 See ENVIRONMENT_SETUP.md for detailed setup guide")


if __name__ == "__main__":
    main()
