#!/usr/bin/env python3
"""
F4 Configuration Validation Script

Comprehensive validation script for F4 dual-provider configuration system.
Tests Azure Sora and Google Veo3 provider configurations, environment setup,
and provider switching capabilities.

Usage:
    python validate_config.py                    # Full validation
    python validate_config.py --provider azure   # Test Azure Sora only
    python validate_config.py --provider veo3    # Test Google Veo3 only
    python validate_config.py --environment      # Test environment detection
    python validate_config.py --performance      # Test performance metrics
"""

import argparse
import sys
import time
from typing import Any, Dict, List, Tuple


def validate_imports() -> Tuple[bool, List[str]]:
    """Validate that all required modules can be imported."""
    errors = []

    try:
        from src.config.factory import (
            ConfigurationFactory,
            ProviderConfigurationFactory,
        )
        from src.config.service import ConfigurationService
        from src.config.veo3_settings import (
            get_cached_veo3_settings,
            validate_veo3_environment,
        )

        print("✅ All F4 configuration modules imported successfully")
        return True, []
    except ImportError as e:
        errors.append(f"Import error: {e}")
        print(f"❌ Import error: {e}")
        return False, errors


def validate_provider_availability() -> Tuple[bool, Dict[str, Any]]:
    """Test provider availability detection."""
    print("\n🔍 Testing Provider Availability")
    print("=" * 50)

    try:
        from src.config.factory import ConfigurationFactory

        availability = ConfigurationFactory.get_provider_availability()

        print(
            f"Azure Sora available: {'✅' if availability['azure_sora'] else '❌'} {availability['azure_sora']}"
        )
        print(
            f"Google Veo3 available: {'✅' if availability['google_veo3'] else '❌'} {availability['google_veo3']}"
        )

        # Get default provider
        default_provider = ConfigurationFactory.get_default_provider()
        print(f"Default provider: {default_provider}")

        return True, {
            "availability": availability,
            "default_provider": default_provider,
        }

    except Exception as e:
        print(f"❌ Provider availability check failed: {e}")
        return False, {"error": str(e)}


def validate_azure_sora_configuration() -> Tuple[bool, Dict[str, Any]]:
    """Test Azure Sora configuration creation and validation."""
    print("\n🔵 Testing Azure Sora Configuration")
    print("=" * 50)

    try:
        from src.config.factory import ConfigurationFactory

        # Test configuration creation
        azure_config = ConfigurationFactory.create_provider_config("azure_sora")
        print("✅ Azure Sora configuration created successfully")

        # Test configuration validation
        validation = ConfigurationFactory.validate_provider_configuration("azure_sora")

        if validation["valid"]:
            print("✅ Azure Sora configuration validation passed")
        else:
            print("❌ Azure Sora configuration validation failed:")
            for error in validation["errors"]:
                print(f"   • {error}")

        # Test configuration content
        required_keys = ["endpoint", "api_key", "api_version", "deployment_name"]
        missing_keys = [key for key in required_keys if key not in azure_config]

        if missing_keys:
            print(f"⚠️  Missing configuration keys: {missing_keys}")
        else:
            print("✅ All required Azure configuration keys present")

        return validation["valid"], {
            "config_created": True,
            "validation": validation,
            "config_keys": list(azure_config.keys()),
            "missing_keys": missing_keys,
        }

    except Exception as e:
        print(f"❌ Azure Sora configuration failed: {e}")
        return False, {"error": str(e)}


def validate_google_veo3_configuration() -> Tuple[bool, Dict[str, Any]]:
    """Test Google Veo3 configuration creation and validation."""
    print("\n🟢 Testing Google Veo3 Configuration")
    print("=" * 50)

    try:
        from src.config.factory import ConfigurationFactory
        from src.config.veo3_settings import get_cached_veo3_settings

        # Test settings loading
        settings = get_cached_veo3_settings()
        print("✅ Veo3 settings loaded successfully")
        print(f"   Mock mode: {settings.USE_MOCK_VEO}")
        print(f"   Project ID: {settings.GOOGLE_PROJECT_ID}")
        print(f"   Model version: {settings.VEO3_MODEL_VERSION}")

        # Test configuration creation
        veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
        print("✅ Google Veo3 configuration created successfully")

        # Test configuration validation
        validation = ConfigurationFactory.validate_provider_configuration("google_veo3")

        if validation["valid"]:
            print("✅ Google Veo3 configuration validation passed")
        else:
            print("❌ Google Veo3 configuration validation failed:")
            for error in validation["errors"]:
                print(f"   • {error}")

        # Test with overrides
        test_config = ConfigurationFactory.create_veo3_config(
            use_mock=True, project_id="test-project-12345", timeout=60
        )
        print("✅ Veo3 configuration with overrides created successfully")
        print(f"   Test project ID: {test_config.project_id}")
        print(f"   Test mock mode: {test_config.use_mock}")
        print(f"   Test timeout: {test_config.timeout}")

        return validation["valid"], {
            "settings_loaded": True,
            "config_created": True,
            "validation": validation,
            "test_config": {
                "project_id": test_config.project_id,
                "use_mock": test_config.use_mock,
                "timeout": test_config.timeout,
            },
        }

    except Exception as e:
        print(f"❌ Google Veo3 configuration failed: {e}")
        return False, {"error": str(e)}


def validate_environment_awareness() -> Tuple[bool, Dict[str, Any]]:
    """Test environment-aware configuration features."""
    print("\n🌍 Testing Environment-Aware Configuration")
    print("=" * 50)

    try:
        from src.config.factory import ProviderConfigurationFactory
        from src.config.service import ConfigurationService

        # Test deployment type detection
        deployment_type = ConfigurationService._detect_deployment_type()
        print(f"Detected deployment type: {deployment_type}")

        # Test environment-specific configuration
        environments = ["local", "docker", "production"]
        env_configs = {}

        for env in environments:
            try:
                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3", environment=env
                )
                env_configs[env] = {
                    "use_mock": config.get("use_mock"),
                    "timeout": config.get("timeout"),
                    "max_retries": config.get("max_retries"),
                }
                print(f"✅ {env.capitalize()} environment configuration created")
                print(f"   Mock mode: {config.get('use_mock')}")
                print(f"   Timeout: {config.get('timeout')}s")

            except Exception as e:
                print(f"❌ Failed to create {env} configuration: {e}")
                env_configs[env] = {"error": str(e)}

        # Test optimal provider selection
        try:
            optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()
            selected_provider = optimal_config.get("selected_provider")
            print(f"✅ Optimal provider selected: {selected_provider}")

        except Exception as e:
            print(f"❌ Optimal provider selection failed: {e}")
            optimal_config = {"error": str(e)}

        return True, {
            "deployment_type": deployment_type,
            "environment_configs": env_configs,
            "optimal_config": optimal_config,
        }

    except Exception as e:
        print(f"❌ Environment awareness test failed: {e}")
        return False, {"error": str(e)}


def validate_environment_complete() -> Tuple[bool, Dict[str, Any]]:
    """Test complete environment validation."""
    print("\n🔬 Testing Complete Environment Validation")
    print("=" * 50)

    try:
        from src.config.veo3_settings import validate_veo3_environment

        validation_result = validate_veo3_environment()

        print(
            f"Environment valid: {'✅' if validation_result['valid'] else '❌'} {validation_result['valid']}"
        )

        if validation_result.get("warnings"):
            print("⚠️  Warnings:")
            for warning in validation_result["warnings"]:
                print(f"   • {warning}")

        if not validation_result["valid"]:
            print("❌ Errors:")
            for error in validation_result["errors"]:
                print(f"   • {error}")

        # Display configuration summary
        config = validation_result.get("configuration", {})
        if "provider_availability" in config:
            print(f"Provider availability: {config['provider_availability']}")

        if "performance" in config:
            perf = config["performance"]
            print(f"Performance config loaded: {bool(perf)}")

        return validation_result["valid"], validation_result

    except Exception as e:
        print(f"❌ Complete environment validation failed: {e}")
        return False, {"error": str(e)}


def test_performance_metrics() -> Tuple[bool, Dict[str, Any]]:
    """Test configuration performance metrics."""
    print("\n⚡ Testing Performance Metrics")
    print("=" * 50)

    performance_results = {}

    try:
        from src.config.factory import ConfigurationFactory
        from src.config.veo3_settings import get_cached_veo3_settings

        # Test settings load time
        start_time = time.time()
        settings = get_cached_veo3_settings()
        settings_load_time = (time.time() - start_time) * 1000
        performance_results["settings_load_time_ms"] = settings_load_time

        # Test factory creation time
        start_time = time.time()
        config = ConfigurationFactory.create_provider_config("google_veo3")
        factory_creation_time = (time.time() - start_time) * 1000
        performance_results["factory_creation_time_ms"] = factory_creation_time

        # Test cache hit time
        start_time = time.time()
        cached_settings = get_cached_veo3_settings()  # Should be cached
        cache_hit_time = (time.time() - start_time) * 1000
        performance_results["cache_hit_time_ms"] = cache_hit_time

        # Performance targets
        targets = {
            "settings_load_time_ms": 50,
            "factory_creation_time_ms": 10,
            "cache_hit_time_ms": 1,
        }

        print("Performance Results:")
        all_targets_met = True

        for metric, value in performance_results.items():
            target = targets.get(metric, float("inf"))
            status = "✅" if value <= target else "⚠️"
            if value > target:
                all_targets_met = False
            print(f"   {status} {metric}: {value:.2f}ms (target: <{target}ms)")

        if all_targets_met:
            print("✅ All performance targets met")
        else:
            print("⚠️  Some performance targets not met (acceptable for development)")

        return True, {
            "performance_results": performance_results,
            "targets": targets,
            "all_targets_met": all_targets_met,
        }

    except Exception as e:
        print(f"❌ Performance testing failed: {e}")
        return False, {"error": str(e)}


def test_configuration_switching() -> Tuple[bool, Dict[str, Any]]:
    """Test provider and environment switching capabilities."""
    print("\n🔄 Testing Configuration Switching")
    print("=" * 50)

    try:
        import os

        from src.config.factory import ConfigurationFactory
        from src.config.service import ConfigurationService

        # Save original environment
        original_provider = os.getenv("DEFAULT_PROVIDER")
        original_mock = os.getenv("USE_MOCK_VEO")

        # Test provider switching
        providers = ["azure_sora", "google_veo3"]
        switch_results = {}

        for provider in providers:
            try:
                config = ConfigurationFactory.create_provider_config(provider)
                switch_results[provider] = {
                    "success": True,
                    "config_keys": list(config.keys()),
                }
                print(f"✅ Successfully switched to {provider}")
            except Exception as e:
                switch_results[provider] = {"success": False, "error": str(e)}
                print(f"❌ Failed to switch to {provider}: {e}")

        # Test mock mode switching
        mock_modes = [True, False]
        mock_results = {}

        for mock_mode in mock_modes:
            try:
                # Temporarily set environment variable
                os.environ["USE_MOCK_VEO"] = str(mock_mode).lower()
                ConfigurationService.clear_cache()  # Clear cache to pick up changes

                config = ConfigurationFactory.create_provider_config("google_veo3")
                mock_results[f"mock_{mock_mode}"] = {
                    "success": True,
                    "use_mock": config.get("use_mock"),
                }
                print(f"✅ Mock mode {mock_mode} configuration created")

            except Exception as e:
                mock_results[f"mock_{mock_mode}"] = {"success": False, "error": str(e)}
                print(f"❌ Mock mode {mock_mode} failed: {e}")

        # Restore original environment
        if original_provider:
            os.environ["DEFAULT_PROVIDER"] = original_provider
        else:
            os.environ.pop("DEFAULT_PROVIDER", None)

        if original_mock:
            os.environ["USE_MOCK_VEO"] = original_mock
        else:
            os.environ.pop("USE_MOCK_VEO", None)

        ConfigurationService.reload()  # Reload to restore original state

        return True, {
            "provider_switching": switch_results,
            "mock_switching": mock_results,
        }

    except Exception as e:
        print(f"❌ Configuration switching test failed: {e}")
        return False, {"error": str(e)}


def main():
    """Main validation function."""
    parser = argparse.ArgumentParser(description="F4 Configuration Validation Script")
    parser.add_argument(
        "--provider", choices=["azure", "veo3"], help="Test specific provider only"
    )
    parser.add_argument(
        "--environment", action="store_true", help="Test environment detection only"
    )
    parser.add_argument(
        "--performance", action="store_true", help="Test performance metrics only"
    )
    parser.add_argument(
        "--switching", action="store_true", help="Test configuration switching only"
    )
    args = parser.parse_args()

    print("🔧 F4 Configuration Validation")
    print("=" * 50)
    print("Testing F4 dual-provider configuration system")
    print("Azure Sora (existing) + Google Veo3 (F4 enhancement)")
    print("")

    # Track overall results
    all_tests_passed = True
    validation_results = {}

    # Test imports first
    imports_ok, import_errors = validate_imports()
    if not imports_ok:
        print("\n❌ Critical: Import validation failed")
        for error in import_errors:
            print(f"   {error}")
        print(
            "\nPlease ensure you're in the project root directory and have activated the virtual environment:"
        )
        print("   cd /path/to/sora-poc")
        print("   source .venv/bin/activate")
        return False

    # Run specific tests based on arguments
    if args.performance:
        success, results = test_performance_metrics()
        validation_results["performance"] = results
        all_tests_passed &= success

    elif args.switching:
        success, results = test_configuration_switching()
        validation_results["switching"] = results
        all_tests_passed &= success

    elif args.environment:
        success, results = validate_environment_awareness()
        validation_results["environment"] = results
        all_tests_passed &= success

    elif args.provider == "azure":
        success, results = validate_azure_sora_configuration()
        validation_results["azure"] = results
        all_tests_passed &= success

    elif args.provider == "veo3":
        success, results = validate_google_veo3_configuration()
        validation_results["veo3"] = results
        all_tests_passed &= success

    else:
        # Run full validation suite
        tests = [
            ("provider_availability", validate_provider_availability),
            ("azure_sora", validate_azure_sora_configuration),
            ("google_veo3", validate_google_veo3_configuration),
            ("environment_awareness", validate_environment_awareness),
            ("complete_environment", validate_environment_complete),
            ("performance", test_performance_metrics),
            ("switching", test_configuration_switching),
        ]

        for test_name, test_func in tests:
            success, results = test_func()
            validation_results[test_name] = results
            all_tests_passed &= success

    # Print summary
    print("\n📋 Validation Summary")
    print("=" * 50)

    if all_tests_passed:
        print("✅ All configuration validation tests passed!")
        print("🚀 F4 dual-provider system is ready for development")

        # Print quick usage reminder
        print("\n💡 Quick Usage:")
        print("   from src.config.factory import ConfigurationFactory")
        print(
            "   azure_config = ConfigurationFactory.create_provider_config('azure_sora')"
        )
        print(
            "   veo3_config = ConfigurationFactory.create_provider_config('google_veo3')"
        )

    else:
        print("❌ Some validation tests failed")
        print("📖 See ENVIRONMENT_SETUP.md for configuration help")
        print("🔧 Common solutions:")
        print("   1. Set required environment variables in .env.local")
        print("   2. Configure Azure OpenAI credentials")
        print("   3. Set GOOGLE_PROJECT_ID for Veo3 testing")

    return all_tests_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
