#!/usr/bin/env python3
"""
F4 Documentation Testing Script

Test F4 documentation examples and code snippets to ensure they work correctly
across different deployment types and configurations.
"""

import sys
import traceback


def test_basic_imports():
    """Test basic F4 imports work correctly."""
    print("🔍 Testing F4 Basic Imports")
    print("=" * 50)

    try:

        print("✅ ConfigurationFactory imported")


        print("✅ Veo3Settings imported")


        print("✅ ConfigurationService imported")

        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_environment_setup_examples():
    """Test examples from ENVIRONMENT_SETUP.md."""
    print("\n🔧 Testing ENVIRONMENT_SETUP.md Examples")
    print("=" * 50)

    try:
        # Test basic configuration loading
        from src.config.veo3_settings import get_cached_veo3_settings

        settings = get_cached_veo3_settings()
        print("✅ Settings loaded successfully")
        print(f"   USE_MOCK_VEO: {settings.USE_MOCK_VEO}")
        print(f"   DEFAULT_PROVIDER: {settings.DEFAULT_PROVIDER}")
        print(f"   GOOGLE_PROJECT_ID: {settings.GOOGLE_PROJECT_ID}")
        print(f"   VEO3_MODEL_VERSION: {settings.VEO3_MODEL_VERSION}")

        # Test provider availability
        availability = settings.get_provider_availability()
        print(f"✅ Provider availability: {availability}")

        return True
    except Exception as e:
        print(f"❌ Environment setup test failed: {e}")
        traceback.print_exc()
        return False


def test_claude_md_examples():
    """Test examples from src/config/CLAUDE.md."""
    print("\n📚 Testing CLAUDE.md F4 Examples")
    print("=" * 50)

    try:
        # Test environment variable access
        from src.config.service import ConfigurationService

        # Test safe configuration access
        google_project_id = ConfigurationService.get("GOOGLE_PROJECT_ID", "not-set")
        print(f"✅ GOOGLE_PROJECT_ID: {google_project_id}")

        use_mock_veo = ConfigurationService.get("USE_MOCK_VEO", "not-set")
        print(f"✅ USE_MOCK_VEO: {use_mock_veo}")

        # Test environment detection
        deployment_type = ConfigurationService._detect_deployment_type()
        print(f"✅ Deployment type: {deployment_type}")

        return True
    except Exception as e:
        print(f"❌ CLAUDE.md test failed: {e}")
        traceback.print_exc()
        return False


def test_factory_functionality():
    """Test ConfigurationFactory functionality."""
    print("\n⚙️  Testing ConfigurationFactory")
    print("=" * 50)

    try:
        from src.config.factory import ConfigurationFactory

        # Test basic configuration methods
        base_config = ConfigurationFactory.get_base_config()
        print(f"✅ Base config loaded: {type(base_config).__name__}")

        azure_config = ConfigurationFactory.get_azure_config()
        print(f"✅ Azure config loaded with keys: {list(azure_config.keys())}")

        veo3_settings = ConfigurationFactory.get_veo3_settings()
        print(f"✅ Veo3 settings loaded: {type(veo3_settings).__name__}")

        return True
    except Exception as e:
        print(f"❌ ConfigurationFactory test failed: {e}")
        traceback.print_exc()
        return False


def test_environment_validation():
    """Test environment validation functionality."""
    print("\n✅ Testing Environment Validation")
    print("=" * 50)

    try:
        from src.config.veo3_settings import validate_veo3_environment

        validation_result = validate_veo3_environment()

        print("Environment validation result:")
        print(f"   Valid: {validation_result['valid']}")

        if validation_result.get("errors"):
            print("   Errors:")
            for error in validation_result["errors"]:
                print(f"     • {error}")

        if validation_result.get("warnings"):
            print("   Warnings:")
            for warning in validation_result["warnings"]:
                print(f"     • {warning}")

        config = validation_result.get("configuration", {})
        if "provider_availability" in config:
            print(f"   Provider availability: {config['provider_availability']}")

        return True
    except Exception as e:
        print(f"❌ Environment validation test failed: {e}")
        traceback.print_exc()
        return False


def test_docker_deployment_examples():
    """Test Docker deployment configuration examples."""
    print("\n🐳 Testing Docker Deployment Examples")
    print("=" * 50)

    try:
        from src.config.service import ConfigurationService

        # Test Docker environment detection
        deployment_type = ConfigurationService._detect_deployment_type()
        print(f"✅ Deployment type detection: {deployment_type}")

        # Test Docker-specific configuration patterns
        broker_url = ConfigurationService.get("CELERY_BROKER_URL", "not-set")
        print(f"✅ Celery broker URL: {broker_url}")

        database_url = ConfigurationService.get("DATABASE_URL", "not-set")
        print(f"✅ Database URL: {database_url}")

        flask_env = ConfigurationService.get("FLASK_ENV", "not-set")
        print(f"✅ Flask environment: {flask_env}")

        return True
    except Exception as e:
        print(f"❌ Docker deployment test failed: {e}")
        traceback.print_exc()
        return False


def test_troubleshooting_patterns():
    """Test troubleshooting patterns and debugging tools."""
    print("\n🔍 Testing Troubleshooting Patterns")
    print("=" * 50)

    try:
        from src.config.service import ConfigurationService
        from src.config.veo3_settings import get_cached_veo3_settings

        # Test configuration debug information
        debug_info = ConfigurationService.get_debug_info()
        print("✅ Configuration debug info available")
        print(f"   Initialized: {debug_info.get('initialized')}")
        print(f"   Cache size: {debug_info.get('cache_size')}")
        print(f"   Process ID: {debug_info.get('process_id')}")

        # Test settings caching
        settings1 = get_cached_veo3_settings()
        settings2 = get_cached_veo3_settings()
        cache_working = settings1 is settings2
        print(f"✅ Settings caching working: {cache_working}")

        # Test configuration reload
        ConfigurationService.clear_cache()
        print("✅ Configuration cache cleared successfully")

        return True
    except Exception as e:
        print(f"❌ Troubleshooting test failed: {e}")
        traceback.print_exc()
        return False


def test_performance_metrics():
    """Test configuration performance metrics."""
    print("\n⚡ Testing Performance Metrics")
    print("=" * 50)

    try:
        import time

        from src.config.factory import ConfigurationFactory
        from src.config.veo3_settings import get_cached_veo3_settings

        # Test settings load performance
        start_time = time.time()
        settings = get_cached_veo3_settings()
        settings_load_time = (time.time() - start_time) * 1000
        print(f"✅ Settings load time: {settings_load_time:.2f}ms")

        # Test cache performance
        start_time = time.time()
        cached_settings = get_cached_veo3_settings()
        cache_hit_time = (time.time() - start_time) * 1000
        print(f"✅ Cache hit time: {cache_hit_time:.2f}ms")

        # Test configuration creation performance
        start_time = time.time()
        azure_config = ConfigurationFactory.get_azure_config()
        azure_time = (time.time() - start_time) * 1000
        print(f"✅ Azure config creation: {azure_time:.2f}ms")

        # Performance targets
        targets = {
            "settings_load_time_ms": 50,
            "cache_hit_time_ms": 1,
            "config_creation_ms": 50,
        }

        results = {
            "settings_load_time_ms": settings_load_time,
            "cache_hit_time_ms": cache_hit_time,
            "config_creation_ms": azure_time,
        }

        print("\nPerformance Results vs Targets:")
        all_targets_met = True
        for metric, value in results.items():
            target = targets[metric]
            status = "✅" if value <= target else "⚠️"
            if value > target:
                all_targets_met = False
            print(f"   {status} {metric}: {value:.2f}ms (target: <{target}ms)")

        if all_targets_met:
            print("✅ All performance targets met")
        else:
            print("⚠️  Some targets exceeded (acceptable for development)")

        return True
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all F4 documentation tests."""
    print("📋 F4 Documentation Testing")
    print("=" * 60)
    print("Testing F4 documentation examples and code snippets")
    print("")

    tests = [
        ("Basic Imports", test_basic_imports),
        ("Environment Setup Examples", test_environment_setup_examples),
        ("CLAUDE.md Examples", test_claude_md_examples),
        ("ConfigurationFactory", test_factory_functionality),
        ("Environment Validation", test_environment_validation),
        ("Docker Deployment Examples", test_docker_deployment_examples),
        ("Troubleshooting Patterns", test_troubleshooting_patterns),
        ("Performance Metrics", test_performance_metrics),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False

    # Summary
    print("\n📋 Documentation Testing Summary")
    print("=" * 60)

    successful = sum(1 for success in results.values() if success)
    total = len(results)

    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")

    print(f"\nOverall: {successful}/{total} documentation tests passed")

    if successful == total:
        print("🚀 All F4 documentation examples working correctly!")
        print("📚 Documentation is accurate and ready for developers")
        print("\n💡 Validated Documentation:")
        print("   • ENVIRONMENT_SETUP.md - ✅ Setup examples working")
        print("   • src/config/CLAUDE.md - ✅ F4 configuration patterns working")
        print("   • F4_DOCKER_DEPLOYMENT_GUIDE.md - ✅ Docker examples working")
        print("   • validate_config.py - ✅ Validation tools working")
        print("   • troubleshoot_f4_config.py - ✅ Troubleshooting tools working")
    else:
        print("⚠️  Some documentation tests failed")
        print("📖 Review failed tests and update documentation accordingly")
        print("🔧 Consider updating examples or fixing configuration issues")

    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
