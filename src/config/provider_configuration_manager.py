"""
Provider Configuration Manager - F4 Environment System Integration.

Integrates with the F4 environment configuration system to provide intelligent
provider availability detection, automatic mock switching, and environment-aware
provider configuration management for the C2 Provider Selection UI system.
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .environments import get_environment_info
from .factory import ConfigurationFactory, ProviderConfigurationFactory
from .service import ConfigurationService

logger = logging.getLogger(__name__)


class ProviderEnvironmentStatus(str, Enum):
    """Provider status within different environments."""

    AVAILABLE = "available"  # Provider configured and available
    UNAVAILABLE = "unavailable"  # Provider not configured
    MOCK_ONLY = "mock_only"  # Provider configured for mock mode only
    DEGRADED = "degraded"  # Provider partially configured
    MAINTENANCE = "maintenance"  # Provider in maintenance mode


@dataclass
class ProviderEnvironmentConfig:
    """Provider configuration for specific environment."""

    provider_id: str
    environment_type: str  # local, docker, production
    status: ProviderEnvironmentStatus
    is_mock: bool
    configuration: Dict[str, Any] = field(default_factory=dict)
    capabilities: Dict[str, bool] = field(default_factory=dict)
    health_check_url: Optional[str] = None
    last_validated: Optional[datetime] = None
    validation_errors: List[str] = field(default_factory=list)

    @property
    def is_healthy(self) -> bool:
        """Check if provider configuration is healthy."""
        return (
            self.status == ProviderEnvironmentStatus.AVAILABLE
            and len(self.validation_errors) == 0
            and self.last_validated
            and (datetime.utcnow() - self.last_validated) < timedelta(minutes=30)
        )


class ProviderAvailabilityMatrix(BaseModel):
    """Matrix of provider availability across environments."""

    azure_sora: Dict[str, ProviderEnvironmentConfig] = Field(default_factory=dict)
    google_veo3: Dict[str, ProviderEnvironmentConfig] = Field(default_factory=dict)

    @property
    def available_providers(self) -> List[str]:
        """Get list of providers available in current environment."""
        current_env = ConfigurationService._detect_deployment_type()
        available = []

        if (
            self.azure_sora.get(current_env, {}).status
            == ProviderEnvironmentStatus.AVAILABLE
        ):
            available.append("azure_sora")

        if (
            self.google_veo3.get(current_env, {}).status
            == ProviderEnvironmentStatus.AVAILABLE
        ):
            available.append("google_veo3")

        return available

    @property
    def default_provider(self) -> Optional[str]:
        """Get default provider for current environment."""
        available = self.available_providers

        if not available:
            return None

        # Prefer Azure Sora for production, Google Veo3 for development
        current_env = ConfigurationService._detect_deployment_type()

        if current_env == "production":
            return "azure_sora" if "azure_sora" in available else available[0]
        else:
            return "google_veo3" if "google_veo3" in available else available[0]


class ProviderConfigurationManager:
    """
    F4 Environment-Aware Provider Configuration Manager.

    Integrates with the F4 environment system to provide intelligent provider
    configuration management, automatic mock switching, and environment-aware
    provider availability detection for the C2 Provider Selection UI system.
    """

    def __init__(self):
        """Initialize provider configuration manager."""
        self.current_environment = ConfigurationService._detect_deployment_type()
        self._availability_matrix: Optional[ProviderAvailabilityMatrix] = None
        self._last_refresh = None
        self._refresh_interval = timedelta(minutes=5)

        # F4 Environment-specific configurations
        self._environment_configs = {
            "local": {
                "prefer_mock": True,
                "allow_fallback": True,
                "health_check_timeout": 5,
                "validation_interval": 300,  # 5 minutes
            },
            "docker": {
                "prefer_mock": False,
                "allow_fallback": True,
                "health_check_timeout": 10,
                "validation_interval": 180,  # 3 minutes
            },
            "production": {
                "prefer_mock": False,
                "allow_fallback": False,  # Strict production mode
                "health_check_timeout": 15,
                "validation_interval": 60,  # 1 minute
            },
        }

        logger.info(
            f"ProviderConfigurationManager initialized for {self.current_environment} environment"
        )

    async def refresh_provider_availability(self) -> ProviderAvailabilityMatrix:
        """
        Refresh provider availability matrix with F4 environment detection.

        Returns:
            ProviderAvailabilityMatrix: Updated availability matrix
        """
        logger.info(
            "Refreshing provider availability matrix with F4 environment detection"
        )

        # Get current environment info
        env_info = get_environment_info()
        current_env = self.current_environment

        # Initialize availability matrix
        matrix = ProviderAvailabilityMatrix()

        # Check Azure Sora availability
        azure_config = await self._check_azure_sora_availability(current_env)
        matrix.azure_sora[current_env] = azure_config

        # Check Google Veo3 availability
        veo3_config = await self._check_google_veo3_availability(current_env)
        matrix.google_veo3[current_env] = veo3_config

        # Apply environment-specific rules
        matrix = self._apply_environment_rules(matrix, current_env)

        self._availability_matrix = matrix
        self._last_refresh = datetime.utcnow()

        logger.info(f"Provider availability refreshed: {matrix.available_providers}")
        return matrix

    async def _check_azure_sora_availability(
        self, environment: str
    ) -> ProviderEnvironmentConfig:
        """Check Azure Sora provider availability for environment."""
        try:
            # Get Azure configuration from F4 system
            azure_config = ConfigurationFactory.get_azure_config()

            # Validate required configuration
            validation_errors = []
            if not azure_config.get("endpoint"):
                validation_errors.append("AZURE_OPENAI_ENDPOINT not configured")
            if not azure_config.get("api_key"):
                validation_errors.append("AZURE_OPENAI_API_KEY not configured")
            if not azure_config.get("deployment_name"):
                validation_errors.append("AZURE_OPENAI_DEPLOYMENT_NAME not configured")

            # Determine status based on environment and configuration
            if validation_errors:
                status = ProviderEnvironmentStatus.UNAVAILABLE
            elif (
                environment == "local"
                and self._environment_configs[environment]["prefer_mock"]
            ):
                status = ProviderEnvironmentStatus.MOCK_ONLY
            else:
                status = ProviderEnvironmentStatus.AVAILABLE

            # Azure Sora capabilities
            capabilities = {
                "text_to_video": True,
                "image_to_video": False,  # Not supported by Azure Sora
                "video_editing": False,
                "batch_processing": True,
                "realtime_generation": False,
            }

            return ProviderEnvironmentConfig(
                provider_id="azure_sora",
                environment_type=environment,
                status=status,
                is_mock=status == ProviderEnvironmentStatus.MOCK_ONLY,
                configuration=azure_config,
                capabilities=capabilities,
                health_check_url=f"{azure_config.get('endpoint', '')}/health"
                if azure_config.get("endpoint")
                else None,
                last_validated=datetime.utcnow(),
                validation_errors=validation_errors,
            )

        except Exception as e:
            logger.error(f"Error checking Azure Sora availability: {e}")
            return ProviderEnvironmentConfig(
                provider_id="azure_sora",
                environment_type=environment,
                status=ProviderEnvironmentStatus.UNAVAILABLE,
                is_mock=False,
                validation_errors=[f"Configuration error: {str(e)}"],
            )

    async def _check_google_veo3_availability(
        self, environment: str
    ) -> ProviderEnvironmentConfig:
        """Check Google Veo3 provider availability for environment."""
        try:
            # Get Veo3 configuration from F4 system
            veo3_settings = ConfigurationFactory.get_veo3_settings()

            # Create environment-aware configuration
            env_overrides = {}
            if environment == "local":
                env_overrides["use_mock"] = True
            elif environment == "docker":
                env_overrides["use_mock"] = False  # Use real API in Docker
            elif environment == "production":
                env_overrides["use_mock"] = False  # Never mock in production

            veo3_config = ConfigurationFactory.create_veo3_config(**env_overrides)

            # Validate configuration
            validation_result = ConfigurationFactory.validate_provider_configuration(
                "google_veo3"
            )
            validation_errors = validation_result.get("errors", [])

            # Determine status
            if validation_errors:
                status = (
                    ProviderEnvironmentStatus.DEGRADED
                    if len(validation_errors) < 3
                    else ProviderEnvironmentStatus.UNAVAILABLE
                )
            elif veo3_config.use_mock:
                status = ProviderEnvironmentStatus.MOCK_ONLY
            else:
                status = ProviderEnvironmentStatus.AVAILABLE

            # Google Veo3 capabilities
            capabilities = {
                "text_to_video": True,
                "image_to_video": True,  # Veo3 supports image-to-video
                "video_editing": True,
                "batch_processing": False,
                "realtime_generation": True,
            }

            return ProviderEnvironmentConfig(
                provider_id="google_veo3",
                environment_type=environment,
                status=status,
                is_mock=veo3_config.use_mock,
                configuration=veo3_config.to_dict(),
                capabilities=capabilities,
                health_check_url="https://veo3-api.googleapis.com/health"
                if not veo3_config.use_mock
                else None,
                last_validated=datetime.utcnow(),
                validation_errors=validation_errors,
            )

        except Exception as e:
            logger.error(f"Error checking Google Veo3 availability: {e}")
            return ProviderEnvironmentConfig(
                provider_id="google_veo3",
                environment_type=environment,
                status=ProviderEnvironmentStatus.UNAVAILABLE,
                is_mock=False,
                validation_errors=[f"Configuration error: {str(e)}"],
            )

    def _apply_environment_rules(
        self, matrix: ProviderAvailabilityMatrix, environment: str
    ) -> ProviderAvailabilityMatrix:
        """Apply environment-specific rules to availability matrix."""
        env_config = self._environment_configs.get(environment, {})

        # Local environment rules
        if environment == "local":
            # Prefer mock providers for local development
            for provider_configs in [matrix.azure_sora, matrix.google_veo3]:
                if environment in provider_configs:
                    config = provider_configs[environment]
                    if (
                        config.status == ProviderEnvironmentStatus.AVAILABLE
                        and env_config.get("prefer_mock")
                    ):
                        config.status = ProviderEnvironmentStatus.MOCK_ONLY
                        config.is_mock = True

        # Production environment rules
        elif environment == "production":
            # Ensure no mock providers in production
            for provider_configs in [matrix.azure_sora, matrix.google_veo3]:
                if environment in provider_configs:
                    config = provider_configs[environment]
                    if config.is_mock:
                        logger.warning(
                            f"Mock provider {config.provider_id} detected in production - marking unavailable"
                        )
                        config.status = ProviderEnvironmentStatus.UNAVAILABLE
                        config.is_mock = False

        # Docker environment rules
        elif environment == "docker":
            # Allow both mock and real providers in Docker
            # Real providers should be preferred but mock is acceptable
            pass

        return matrix

    async def get_optimal_provider_config(
        self, session_preferences: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Get optimal provider configuration based on F4 environment and user preferences.

        Args:
            session_preferences: User session preferences for provider selection

        Returns:
            Optimal provider configuration
        """
        # Refresh availability if needed
        if self._needs_refresh():
            await self.refresh_provider_availability()

        matrix = self._availability_matrix
        if not matrix:
            raise ValueError("Provider availability matrix not initialized")

        # Get available providers
        available_providers = matrix.available_providers
        if not available_providers:
            raise ValueError("No providers available in current environment")

        # Apply user preferences
        selected_provider = self._select_provider_with_preferences(
            available_providers, session_preferences
        )

        # Create environment-aware configuration
        config = ProviderConfigurationFactory.create_environment_aware_config(
            selected_provider, environment=self.current_environment
        )

        # Add provider matrix information
        config.update(
            {
                "selected_provider": selected_provider,
                "available_providers": available_providers,
                "environment": self.current_environment,
                "provider_matrix": matrix.model_dump(),
                "is_mock_environment": self._is_mock_environment(),
            }
        )

        logger.info(
            f"Optimal provider config selected: {selected_provider} for {self.current_environment}"
        )
        return config

    def _select_provider_with_preferences(
        self, available_providers: List[str], session_preferences: Optional[Dict]
    ) -> str:
        """Select provider based on availability and user preferences."""
        if not available_providers:
            raise ValueError("No available providers")

        # Check user preferred provider
        if session_preferences:
            preferred = session_preferences.get("preferred_provider")
            if preferred and preferred in available_providers:
                return preferred

            # Check fallback providers
            fallbacks = session_preferences.get("fallback_providers", [])
            for fallback in fallbacks:
                if fallback in available_providers:
                    return fallback

        # Use matrix default provider
        matrix_default = self._availability_matrix.default_provider
        if matrix_default and matrix_default in available_providers:
            return matrix_default

        # Fallback to first available
        return available_providers[0]

    def _needs_refresh(self) -> bool:
        """Check if availability matrix needs refresh."""
        if not self._availability_matrix or not self._last_refresh:
            return True

        return (datetime.utcnow() - self._last_refresh) > self._refresh_interval

    def _is_mock_environment(self) -> bool:
        """Check if current environment should use mock providers."""
        return (
            self.current_environment == "local"
            and self._environment_configs["local"]["prefer_mock"]
        )

    async def validate_provider_configuration(self, provider_id: str) -> Dict[str, Any]:
        """
        Validate provider configuration for current environment.

        Args:
            provider_id: Provider identifier to validate

        Returns:
            Validation result with status and details
        """
        if not self._availability_matrix:
            await self.refresh_provider_availability()

        # Get provider configuration for current environment
        provider_configs = getattr(self._availability_matrix, provider_id, {})
        env_config = provider_configs.get(self.current_environment)

        if not env_config:
            return {
                "valid": False,
                "provider_id": provider_id,
                "environment": self.current_environment,
                "errors": [
                    f"Provider {provider_id} not configured for {self.current_environment}"
                ],
                "status": "unavailable",
            }

        # Perform comprehensive validation
        validation_errors = env_config.validation_errors.copy()
        warnings = []

        # Environment-specific validation
        if self.current_environment == "production":
            if env_config.is_mock:
                validation_errors.append("Mock providers not allowed in production")
            if not env_config.health_check_url:
                warnings.append("No health check URL configured for production")

        elif self.current_environment == "local":
            if (
                not env_config.is_mock
                and not self._environment_configs["local"]["allow_fallback"]
            ):
                warnings.append("Real provider configured in local environment")

        # Check configuration completeness
        required_config_keys = self._get_required_config_keys(provider_id)
        missing_config = [
            key
            for key in required_config_keys
            if key not in env_config.configuration or not env_config.configuration[key]
        ]

        if missing_config:
            validation_errors.extend(
                [f"Missing configuration: {key}" for key in missing_config]
            )

        is_valid = len(validation_errors) == 0

        return {
            "valid": is_valid,
            "provider_id": provider_id,
            "environment": self.current_environment,
            "status": env_config.status.value,
            "is_mock": env_config.is_mock,
            "errors": validation_errors,
            "warnings": warnings,
            "capabilities": env_config.capabilities,
            "last_validated": env_config.last_validated.isoformat()
            if env_config.last_validated
            else None,
            "configuration_keys": list(env_config.configuration.keys()),
        }

    def _get_required_config_keys(self, provider_id: str) -> List[str]:
        """Get required configuration keys for a provider."""
        if provider_id == "azure_sora":
            return ["endpoint", "api_key", "api_version", "deployment_name"]
        elif provider_id == "google_veo3":
            return ["project_id", "client_id"]
        else:
            return []

    async def get_provider_switching_recommendations(
        self, current_provider: str, failure_reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get recommendations for provider switching based on F4 environment rules.

        Args:
            current_provider: Currently failing provider
            failure_reason: Reason for provider failure

        Returns:
            Provider switching recommendations
        """
        if not self._availability_matrix:
            await self.refresh_provider_availability()

        available_providers = self._availability_matrix.available_providers
        alternative_providers = [
            p for p in available_providers if p != current_provider
        ]

        recommendations = {
            "current_provider": current_provider,
            "failure_reason": failure_reason,
            "environment": self.current_environment,
            "alternative_providers": alternative_providers,
            "recommended_action": "maintain",  # Default
            "switching_allowed": self._environment_configs[self.current_environment][
                "allow_fallback"
            ],
            "recommendations": [],
        }

        if not alternative_providers:
            recommendations["recommended_action"] = "retry"
            recommendations["recommendations"].append(
                {
                    "action": "retry_current",
                    "provider": current_provider,
                    "reason": "No alternative providers available",
                    "priority": "high",
                }
            )

        elif failure_reason and "rate_limit" in failure_reason.lower():
            # Rate limiting failure - recommend switching
            if alternative_providers:
                recommendations["recommended_action"] = "switch"
                recommendations["recommendations"].append(
                    {
                        "action": "switch_provider",
                        "provider": alternative_providers[0],
                        "reason": "Rate limit exceeded on current provider",
                        "priority": "medium",
                    }
                )

        elif failure_reason and "configuration" in failure_reason.lower():
            # Configuration failure - recommend fixing config
            recommendations["recommended_action"] = "fix_config"
            recommendations["recommendations"].append(
                {
                    "action": "fix_configuration",
                    "provider": current_provider,
                    "reason": "Provider configuration issues detected",
                    "priority": "high",
                }
            )

        else:
            # General failure - recommend switching if allowed
            if (
                self._environment_configs[self.current_environment]["allow_fallback"]
                and alternative_providers
            ):
                recommendations["recommended_action"] = "switch"
                recommendations["recommendations"].append(
                    {
                        "action": "switch_provider",
                        "provider": alternative_providers[0],
                        "reason": "Current provider experiencing issues",
                        "priority": "medium",
                    }
                )

        return recommendations

    def get_environment_configuration_summary(self) -> Dict[str, Any]:
        """Get comprehensive environment configuration summary."""
        env_info = get_environment_info()

        return {
            "environment": {
                "type": self.current_environment,
                "info": env_info,
                "config": self._environment_configs.get(self.current_environment, {}),
            },
            "providers": {
                "availability_matrix": self._availability_matrix.model_dump()
                if self._availability_matrix
                else {},
                "available_providers": self._availability_matrix.available_providers
                if self._availability_matrix
                else [],
                "default_provider": self._availability_matrix.default_provider
                if self._availability_matrix
                else None,
            },
            "f4_system": {
                "deployment_detection": ConfigurationService._detect_deployment_type(),
                "configuration_service_initialized": ConfigurationService._initialized,
                "last_refresh": self._last_refresh.isoformat()
                if self._last_refresh
                else None,
                "refresh_interval_minutes": self._refresh_interval.total_seconds() / 60,
            },
        }


# Global provider configuration manager instance
_provider_config_manager: Optional[ProviderConfigurationManager] = None


def get_provider_configuration_manager() -> ProviderConfigurationManager:
    """
    Get global provider configuration manager instance (singleton pattern).

    Returns:
        ProviderConfigurationManager: Global manager instance
    """
    global _provider_config_manager

    if _provider_config_manager is None:
        _provider_config_manager = ProviderConfigurationManager()

    return _provider_config_manager


async def initialize_provider_configuration_manager() -> None:
    """Initialize provider configuration manager with F4 environment detection."""
    manager = get_provider_configuration_manager()
    await manager.refresh_provider_availability()

    logger.info("Provider configuration manager initialized with F4 environment system")


async def cleanup_provider_configuration_manager() -> None:
    """Cleanup provider configuration manager resources."""
    global _provider_config_manager
    _provider_config_manager = None

    logger.info("Provider configuration manager cleaned up")
