"""Enhanced environment variable management with USE_MOCK_VEO switching."""

import logging
import os
from typing import Any, Dict, Literal, Union

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class EnvironmentConfig(BaseModel):
    """Environment configuration with deployment detection."""

    deployment_type: Literal["local", "docker", "production"]
    flask_env: str = Field(default="development")
    debug_mode: bool = Field(default=False)
    use_mock_veo: bool = Field(default=True)
    mock_mode_reason: str = Field(description="Reason for mock mode setting")

    class Config:
        frozen = True


class EnvironmentVariableManager:
    """
    F4 Enhanced environment variable management with provider-specific variables.

    This class provides comprehensive environment variable handling with deployment
    type detection, mock mode switching, provider configuration, and validation.

    F4 Integration Features:
    - Provider-specific environment variable management
    - Zero-configuration environment switching
    - Comprehensive validation and dependency checking
    - Performance monitoring and caching
    """

    # F4 Provider-specific environment variables
    PROVIDER_VARS = {
        "azure_sora": [
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_VERSION",
            "AZURE_OPENAI_DEPLOYMENT_NAME",
            "AZURE_OPENAI_SORA_DEPLOYMENT",
            "AZURE_TIMEOUT",
            "AZURE_MAX_RETRIES",
        ],
        "google_veo3": [
            "GOOGLE_PROJECT_ID",
            "USE_MOCK_VEO",
            "USE_MOCK_VEO3",
            "GOOGLE_APPLICATION_CREDENTIALS",
            "GOOGLE_APPLICATION_CREDENTIALS_FILE",
            "GOOGLE_CLIENT_ID",
            "GOOGLE_CLIENT_SECRET",
            "GOOGLE_LOCATION",
            "GOOGLE_SCOPES",
            "VEO3_MODEL_VERSION",
            "VEO3_TIMEOUT",
            "VEO3_MAX_RETRIES",
            "VEO3_RETRY_DELAY",
            "VEO3_GENERATION_TIMEOUT",
            "VEO3_RATE_LIMIT_RPM",
            "CREDENTIAL_RETRY_ATTEMPTS",
            "CREDENTIAL_RETRY_DELAY",
            "CREDENTIAL_TIMEOUT",
            "CREDENTIAL_CACHE_TTL",
            "CREDENTIAL_DEBUG_ENABLED",
        ],
    }

    # Environment variable mappings (legacy support)
    MOCK_VEO_VARS = ["USE_MOCK_VEO", "USE_MOCK_VEO3", "MOCK_VEO", "VEO_MOCK_MODE"]
    DEPLOYMENT_INDICATORS = {
        "docker": [
            "DOCKER_CONTAINER",
            "CONTAINER_ID",
            "KUBERNETES_SERVICE_HOST",
            "/.dockerenv",
        ],
        "production": ["PROD", "PRODUCTION", "VERCEL", "HEROKU", "AWS_EXECUTION_ENV"],
        "testing": ["TEST", "TESTING", "CI", "GITHUB_ACTIONS"],
    }

    @classmethod
    def detect_deployment_type(cls) -> Literal["local", "docker", "production"]:
        """
        Detect deployment type from environment indicators.

        Returns:
            Deployment type based on environment analysis
        """
        # Check for explicit deployment type
        explicit_type = os.getenv("DEPLOYMENT_TYPE", "").lower()
        if explicit_type in ["local", "docker", "production"]:
            logger.info(f"Explicit deployment type detected: {explicit_type}")
            return explicit_type

        # Check Docker indicators
        for indicator in cls.DEPLOYMENT_INDICATORS["docker"]:
            if os.getenv(indicator):
                logger.info(f"Docker deployment detected via {indicator}")
                return "docker"

        # Check production indicators
        for indicator in cls.DEPLOYMENT_INDICATORS["production"]:
            if os.getenv(indicator):
                logger.info(f"Production deployment detected via {indicator}")
                return "production"

        # Default to local
        logger.info("Local deployment type detected (default)")
        return "local"

    @classmethod
    def get_mock_veo_setting(cls) -> bool:
        """
        Get USE_MOCK_VEO setting with intelligent defaults.

        Returns:
            Boolean indicating whether to use mock Veo3 mode
        """
        # Check all possible mock environment variables
        for var_name in cls.MOCK_VEO_VARS:
            value = os.getenv(var_name)
            if value is not None:
                is_mock = cls._parse_boolean_env(value)
                logger.info(f"Mock VEO setting from {var_name}: {is_mock}")
                return is_mock

        # Apply deployment-specific defaults
        deployment_type = cls.detect_deployment_type()

        if deployment_type == "production":
            logger.info("Mock VEO disabled for production deployment")
            return False
        elif deployment_type == "docker":
            logger.info("Mock VEO enabled for Docker deployment (default)")
            return True
        else:  # local
            logger.info("Mock VEO enabled for local deployment (default)")
            return True

    @classmethod
    def get_environment_config(cls) -> EnvironmentConfig:
        """
        Get comprehensive environment configuration.

        Returns:
            EnvironmentConfig with deployment type and settings
        """
        deployment_type = cls.detect_deployment_type()
        flask_env = os.getenv("FLASK_ENV", "development")
        debug_mode = cls._parse_boolean_env(os.getenv("DEBUG", "false"))
        use_mock_veo = cls.get_mock_veo_setting()

        # Determine mock mode reason
        mock_reason = cls._get_mock_mode_reason(deployment_type, use_mock_veo)

        return EnvironmentConfig(
            deployment_type=deployment_type,
            flask_env=flask_env,
            debug_mode=debug_mode,
            use_mock_veo=use_mock_veo,
            mock_mode_reason=mock_reason,
        )

    @classmethod
    def _parse_boolean_env(cls, value: str) -> bool:
        """
        Parse boolean value from environment variable.

        Args:
            value: String value from environment

        Returns:
            Boolean interpretation of the value
        """
        if not value:
            return False

        return value.lower() in ("true", "1", "yes", "on", "enabled")

    @classmethod
    def _get_mock_mode_reason(cls, deployment_type: str, use_mock: bool) -> str:
        """
        Get explanation for mock mode setting.

        Args:
            deployment_type: Current deployment type
            use_mock: Current mock setting

        Returns:
            Human-readable explanation for the mock setting
        """
        if use_mock:
            if deployment_type == "local":
                return "Mock mode enabled for local development"
            elif deployment_type == "docker":
                return "Mock mode enabled for Docker environment"
            else:
                return "Mock mode explicitly enabled"
        else:
            if deployment_type == "production":
                return "Mock mode disabled for production environment"
            else:
                return "Mock mode explicitly disabled"

    @classmethod
    def validate_environment_consistency(cls) -> Dict[str, Any]:
        """
        Validate environment variable consistency.

        Returns:
            Validation results with warnings and recommendations
        """
        warnings = []
        recommendations = []

        deployment_type = cls.detect_deployment_type()
        use_mock_veo = cls.get_mock_veo_setting()
        debug_mode = cls._parse_boolean_env(os.getenv("DEBUG", "false"))

        # Production environment checks
        if deployment_type == "production":
            if use_mock_veo:
                warnings.append("Mock mode is enabled in production environment")
                recommendations.append("Set USE_MOCK_VEO=false for production")

            if debug_mode:
                warnings.append("Debug mode is enabled in production")
                recommendations.append("Set DEBUG=false for production")

        # Development environment checks
        elif deployment_type == "local":
            if not use_mock_veo:
                recommendations.append(
                    "Consider enabling USE_MOCK_VEO=true for local development"
                )

        # Check for conflicting environment variables
        mock_values = {}
        for var_name in cls.MOCK_VEO_VARS:
            value = os.getenv(var_name)
            if value is not None:
                mock_values[var_name] = cls._parse_boolean_env(value)

        if len(set(mock_values.values())) > 1:
            warnings.append(f"Conflicting mock VEO settings: {mock_values}")
            recommendations.append("Use only USE_MOCK_VEO for consistency")

        return {
            "deployment_type": deployment_type,
            "use_mock_veo": use_mock_veo,
            "debug_mode": debug_mode,
            "warnings": warnings,
            "recommendations": recommendations,
            "environment_consistent": len(warnings) == 0,
        }

    @classmethod
    def set_development_defaults(cls) -> None:
        """Set development-friendly environment defaults."""
        if not os.getenv("USE_MOCK_VEO"):
            os.environ["USE_MOCK_VEO"] = "true"
            logger.info("Set USE_MOCK_VEO=true for development")

        if not os.getenv("DEBUG"):
            os.environ["DEBUG"] = "true"
            logger.info("Set DEBUG=true for development")

        if not os.getenv("FLASK_ENV"):
            os.environ["FLASK_ENV"] = "development"
            logger.info("Set FLASK_ENV=development")

    @classmethod
    def override_for_testing(cls, **overrides: Union[str, bool]) -> None:
        """
        Override environment variables for testing.

        Args:
            **overrides: Environment variable overrides
        """
        for key, value in overrides.items():
            if isinstance(value, bool):
                os.environ[key] = "true" if value else "false"
            else:
                os.environ[key] = str(value)
            logger.debug(f"Override {key}={value} for testing")

    # F4 Enhanced Methods
    @classmethod
    def get_provider_variables(cls, provider: str) -> list[str]:
        """
        F4: Get environment variables for specific provider.

        Args:
            provider: Provider name (azure_sora, google_veo3)

        Returns:
            List of environment variable names for the provider

        Raises:
            ValueError: If provider is unknown
        """
        if provider not in cls.PROVIDER_VARS:
            available = ", ".join(cls.PROVIDER_VARS.keys())
            raise ValueError(f"Unknown provider: {provider}. Available: {available}")

        return cls.PROVIDER_VARS[provider].copy()

    @classmethod
    def get_all_provider_variables(cls) -> Dict[str, list[str]]:
        """
        F4: Get all provider-specific environment variables.

        Returns:
            Dictionary mapping provider names to their variables
        """
        return cls.PROVIDER_VARS.copy()

    @classmethod
    def validate_provider_configuration(cls, provider: str) -> Dict[str, Any]:
        """
        F4: Validate configuration for specific provider.

        Args:
            provider: Provider name to validate

        Returns:
            Validation result with status and missing variables
        """
        if provider not in cls.PROVIDER_VARS:
            return {
                "provider": provider,
                "valid": False,
                "error": f"Unknown provider: {provider}",
                "missing_variables": [],
                "available_variables": [],
            }

        provider_vars = cls.get_provider_variables(provider)
        missing_vars = []
        available_vars = []

        for var in provider_vars:
            if os.getenv(var):
                available_vars.append(var)
            else:
                missing_vars.append(var)

        # Provider-specific validation logic
        is_valid = True
        error_message = None

        if provider == "azure_sora":
            required_azure = ["AZURE_OPENAI_API_KEY", "AZURE_OPENAI_ENDPOINT"]
            missing_required = [var for var in required_azure if not os.getenv(var)]
            if missing_required:
                is_valid = False
                error_message = f"Missing required Azure variables: {missing_required}"

        elif provider == "google_veo3":
            use_mock = cls.get_mock_veo_setting()
            if not use_mock:
                # Real API requires credentials
                google_project = os.getenv("GOOGLE_PROJECT_ID")
                google_creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
                google_creds_file = os.getenv("GOOGLE_APPLICATION_CREDENTIALS_FILE")
                google_client_id = os.getenv("GOOGLE_CLIENT_ID")

                if not google_project:
                    is_valid = False
                    error_message = "GOOGLE_PROJECT_ID required for real Veo3 API"
                elif not any([google_creds, google_creds_file, google_client_id]):
                    is_valid = False
                    error_message = "Google credentials required: set GOOGLE_APPLICATION_CREDENTIALS, GOOGLE_APPLICATION_CREDENTIALS_FILE, or GOOGLE_CLIENT_ID/SECRET"
            else:
                # Mock mode is always valid
                pass

        return {
            "provider": provider,
            "valid": is_valid,
            "error": error_message,
            "missing_variables": missing_vars,
            "available_variables": available_vars,
            "mock_mode": provider == "google_veo3" and cls.get_mock_veo_setting(),
            "deployment_type": cls.detect_deployment_type(),
        }

    @classmethod
    def get_environment_summary(cls) -> Dict[str, Any]:
        """
        F4: Get comprehensive environment summary with provider status.

        Returns:
            Complete environment information including provider configurations
        """
        deployment_type = cls.detect_deployment_type()
        environment_config = cls.get_environment_config()
        validation_results = cls.validate_environment_consistency()

        # Validate all providers
        provider_status = {}
        for provider in cls.PROVIDER_VARS.keys():
            provider_status[provider] = cls.validate_provider_configuration(provider)

        return {
            "deployment_type": deployment_type,
            "environment_config": environment_config.dict(),
            "validation_results": validation_results,
            "provider_status": provider_status,
            "provider_variables": cls.PROVIDER_VARS,
            "total_variables": {
                "azure_sora": len(cls.PROVIDER_VARS["azure_sora"]),
                "google_veo3": len(cls.PROVIDER_VARS["google_veo3"]),
                "total": sum(len(vars) for vars in cls.PROVIDER_VARS.values()),
            },
        }
