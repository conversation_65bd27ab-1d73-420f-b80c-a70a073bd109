"""Google Cloud credential management with multi-source detection and Docker secrets support.

This module implements comprehensive Google Cloud credential management following
the configuration factory pattern with:
- Multi-source credential detection (service account → OAuth → default → compute engine)
- Docker secrets integration with _FILE suffix pattern
- Retry logic and fallback mechanisms
- Environment-aware configuration with zero-configuration switching
- Performance optimization and caching

Credential Source Priority (highest to lowest):
1. Docker secrets (GOOGLE_APPLICATION_CREDENTIALS_FILE)
2. Service account file (GOOGLE_APPLICATION_CREDENTIALS)
3. OAuth credentials (GOOGLE_CLIENT_ID + GOOGLE_CLIENT_SECRET)
4. Default application credentials (gcloud/workload identity)
5. Compute Engine metadata service

Example:
    >>> credential_manager = GoogleCloudCredentialManager()
    >>> credentials = await credential_manager.get_credentials()
    >>> config = credential_manager.create_veo3_config()
"""

import asyncio
import json
import logging
import os
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from src.config.service import ConfigurationService

logger = logging.getLogger(__name__)


class GoogleCloudCredentialError(Exception):
    """Base exception for Google Cloud credential errors."""

    pass


class CredentialNotFoundError(GoogleCloudCredentialError):
    """Raised when no valid credentials are found."""

    pass


class CredentialValidationError(GoogleCloudCredentialError):
    """Raised when credential validation fails."""

    pass


class DockerSecretsError(GoogleCloudCredentialError):
    """Raised when Docker secrets mounting fails."""

    pass


class GoogleCloudCredentials(BaseModel):
    """
    Google Cloud credentials model with validation.

    Supports multiple credential types:
    - Service account key files
    - OAuth client credentials
    - Application default credentials
    - Compute Engine metadata
    """

    credential_type: str = Field(
        description="Type of credential (service_account, oauth, default, compute_engine)"
    )
    project_id: Optional[str] = Field(None, description="Google Cloud project ID")
    client_id: Optional[str] = Field(None, description="OAuth client ID")
    client_secret: Optional[str] = Field(
        None, description="OAuth client secret (secure)"
    )
    service_account_path: Optional[str] = Field(
        None, description="Path to service account JSON file"
    )
    service_account_info: Optional[Dict[str, Any]] = Field(
        None, description="Service account JSON content"
    )
    token: Optional[str] = Field(None, description="OAuth access token")
    refresh_token: Optional[str] = Field(None, description="OAuth refresh token")
    expires_at: Optional[int] = Field(None, description="Token expiration timestamp")
    scopes: List[str] = Field(
        default_factory=lambda: ["https://www.googleapis.com/auth/cloud-platform"]
    )

    @field_validator("project_id")
    @classmethod
    def validate_project_id(cls, v: Optional[str]) -> Optional[str]:
        """Validate Google Cloud project ID format."""
        if v is None:
            return v

        # Basic validation for project ID format
        if not v.replace("-", "").replace("_", "").isalnum():
            raise ValueError(
                "Project ID must contain only letters, digits, hyphens, and underscores"
            )

        if len(v) < 6 or len(v) > 30:
            raise ValueError("Project ID must be 6-30 characters long")

        return v.lower()

    @field_validator("service_account_path")
    @classmethod
    def validate_service_account_path(cls, v: Optional[str]) -> Optional[str]:
        """Validate service account file path exists and is readable."""
        if v is None:
            return v

        path = Path(v)
        if not path.exists():
            raise ValueError(f"Service account file does not exist: {v}")

        if not path.is_file():
            raise ValueError(f"Service account path is not a file: {v}")

        try:
            with open(path) as f:
                content = f.read()
                if not content.strip():
                    raise ValueError(f"Service account file is empty: {v}")
                # Basic JSON validation
                json.loads(content)
        except json.JSONDecodeError:
            raise ValueError(f"Service account file is not valid JSON: {v}")
        except PermissionError:
            raise ValueError(f"Cannot read service account file: {v}")

        return str(path.absolute())

    def is_valid(self) -> bool:
        """Check if credentials are valid and complete."""
        if self.credential_type == "service_account":
            return bool(self.service_account_path or self.service_account_info)
        elif self.credential_type == "oauth":
            return bool(self.client_id and self.client_secret)
        elif self.credential_type in ["default", "compute_engine"]:
            return True  # These are environment-dependent
        else:
            return False

    def to_auth_dict(self) -> Dict[str, Any]:
        """Convert credentials to authentication dictionary for Google client libraries."""
        if self.credential_type == "service_account":
            if self.service_account_info:
                return {"service_account_info": self.service_account_info}
            elif self.service_account_path:
                return {"service_account_path": self.service_account_path}
        elif self.credential_type == "oauth":
            return {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "token": self.token,
                "refresh_token": self.refresh_token,
            }

        return {}


class GoogleCloudCredentialSettings(BaseSettings):
    """
    Google Cloud credential configuration with environment variable support.

    Environment Variables:
        GOOGLE_PROJECT_ID: Google Cloud project ID
        GOOGLE_APPLICATION_CREDENTIALS: Path to service account JSON file
        GOOGLE_APPLICATION_CREDENTIALS_FILE: Docker secret file path
        GOOGLE_CLIENT_ID: OAuth client ID
        GOOGLE_CLIENT_SECRET: OAuth client secret
        GOOGLE_LOCATION: Google Cloud region/location
        GOOGLE_SCOPES: Comma-separated list of OAuth scopes
        CREDENTIAL_RETRY_ATTEMPTS: Maximum retry attempts for credential loading
        CREDENTIAL_RETRY_DELAY: Delay between retry attempts in seconds
        CREDENTIAL_TIMEOUT: Timeout for credential operations in seconds
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        validate_default=True,
    )

    # Basic Google Cloud configuration
    GOOGLE_PROJECT_ID: Optional[str] = Field(
        None, description="Google Cloud project ID"
    )
    GOOGLE_LOCATION: str = Field(
        "us-central1", description="Google Cloud region/location"
    )
    GOOGLE_SCOPES: str = Field(
        "https://www.googleapis.com/auth/cloud-platform",
        description="Comma-separated OAuth scopes",
    )

    # Service account configuration
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = Field(
        None, description="Path to service account JSON file"
    )
    GOOGLE_APPLICATION_CREDENTIALS_FILE: Optional[str] = Field(
        None, description="Docker secret file path for service account"
    )

    # OAuth configuration
    GOOGLE_CLIENT_ID: Optional[str] = Field(None, description="OAuth client ID")
    GOOGLE_CLIENT_SECRET: Optional[str] = Field(None, description="OAuth client secret")

    # Retry and timeout configuration
    CREDENTIAL_RETRY_ATTEMPTS: int = Field(
        3, ge=1, le=10, description="Maximum retry attempts"
    )
    CREDENTIAL_RETRY_DELAY: int = Field(
        2, ge=1, le=30, description="Retry delay in seconds"
    )
    CREDENTIAL_TIMEOUT: int = Field(
        30, ge=5, le=300, description="Credential operation timeout"
    )

    # Performance and debugging
    CREDENTIAL_CACHE_TTL: int = Field(
        3600, ge=300, le=86400, description="Credential cache TTL in seconds"
    )
    CREDENTIAL_DEBUG_ENABLED: bool = Field(
        False, description="Enable credential debug logging"
    )

    def get_scopes_list(self) -> List[str]:
        """Get OAuth scopes as a list."""
        return [
            scope.strip() for scope in self.GOOGLE_SCOPES.split(",") if scope.strip()
        ]


@lru_cache(maxsize=1)
def get_cached_credential_settings() -> GoogleCloudCredentialSettings:
    """Get cached Google Cloud credential settings instance."""
    logger.debug("Loading GoogleCloudCredentialSettings (cached)")
    return GoogleCloudCredentialSettings()


class GoogleCloudCredentialManager:
    """
    Comprehensive Google Cloud credential manager with multi-source detection.

    Implements the configuration factory pattern with:
    - Docker secrets integration (_FILE suffix pattern)
    - Multi-source credential detection with priority ordering
    - Retry logic and fallback mechanisms
    - Environment-aware configuration
    - Performance optimization with caching

    Credential Detection Priority:
    1. Docker secrets (GOOGLE_APPLICATION_CREDENTIALS_FILE)
    2. Service account file (GOOGLE_APPLICATION_CREDENTIALS)
    3. OAuth credentials (GOOGLE_CLIENT_ID + GOOGLE_CLIENT_SECRET)
    4. Default application credentials (gcloud, workload identity)
    5. Compute Engine metadata service
    """

    def __init__(self, settings: Optional[GoogleCloudCredentialSettings] = None):
        """
        Initialize credential manager with configuration.

        Args:
            settings: Optional credential settings override for testing
        """
        self.settings = settings or get_cached_credential_settings()
        self._credential_cache: Dict[str, GoogleCloudCredentials] = {}
        self._last_detection_attempt: Optional[float] = None

        # Performance tracking
        self._detection_attempts = 0
        self._cache_hits = 0

        logger.info(
            f"GoogleCloudCredentialManager initialized with {len(self._credential_cache)} cached credentials"
        )

    async def get_credentials(
        self, force_refresh: bool = False
    ) -> GoogleCloudCredentials:
        """
        Get Google Cloud credentials with multi-source detection.

        Implements credential detection priority with retry logic and caching.

        Args:
            force_refresh: Force refresh of cached credentials

        Returns:
            GoogleCloudCredentials: Validated credentials

        Raises:
            CredentialNotFoundError: If no valid credentials found
            CredentialValidationError: If credential validation fails
        """
        cache_key = "default"

        # Return cached credentials if available and not forcing refresh
        if not force_refresh and cache_key in self._credential_cache:
            self._cache_hits += 1
            logger.debug(
                f"Returning cached credentials (cache hits: {self._cache_hits})"
            )
            return self._credential_cache[cache_key]

        self._detection_attempts += 1
        detection_id = str(uuid4())[:8]
        logger.info(
            f"Starting credential detection (attempt {self._detection_attempts}, id: {detection_id})"
        )

        # Try each credential source with retry logic
        for attempt in range(self.settings.CREDENTIAL_RETRY_ATTEMPTS):
            try:
                credentials = await self._detect_credentials_with_priority()

                # Validate credentials
                if not credentials.is_valid():
                    raise CredentialValidationError(
                        f"Invalid credentials detected: {credentials.credential_type}"
                    )

                # Cache successful credentials
                self._credential_cache[cache_key] = credentials
                logger.info(
                    f"Credentials successfully detected and cached: {credentials.credential_type} (id: {detection_id})"
                )

                return credentials

            except Exception as e:
                logger.warning(
                    f"Credential detection attempt {attempt + 1} failed: {e}"
                )

                if attempt < self.settings.CREDENTIAL_RETRY_ATTEMPTS - 1:
                    delay = self.settings.CREDENTIAL_RETRY_DELAY * (
                        attempt + 1
                    )  # Exponential backoff
                    logger.info(f"Retrying credential detection in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"All credential detection attempts failed (id: {detection_id})"
                    )
                    raise CredentialNotFoundError(
                        f"Failed to detect valid Google Cloud credentials after {self.settings.CREDENTIAL_RETRY_ATTEMPTS} attempts. "
                        f"Ensure GOOGLE_APPLICATION_CREDENTIALS, GOOGLE_CLIENT_ID/SECRET, or default credentials are configured."
                    )

    async def _detect_credentials_with_priority(self) -> GoogleCloudCredentials:
        """
        Detect credentials using priority-based multi-source detection.

        Returns:
            GoogleCloudCredentials: First valid credentials found

        Raises:
            CredentialNotFoundError: If no valid credentials found
        """
        detection_methods = [
            ("docker_secrets", self._detect_docker_secrets_credentials),
            ("service_account", self._detect_service_account_credentials),
            ("oauth", self._detect_oauth_credentials),
            ("default", self._detect_default_credentials),
            ("compute_engine", self._detect_compute_engine_credentials),
        ]

        for method_name, method in detection_methods:
            try:
                logger.debug(f"Trying credential detection method: {method_name}")
                credentials = await method()

                if credentials and credentials.is_valid():
                    logger.info(f"Valid credentials found using method: {method_name}")
                    return credentials
                else:
                    logger.debug(f"No valid credentials from method: {method_name}")

            except Exception as e:
                logger.debug(f"Credential detection method {method_name} failed: {e}")
                continue

        raise CredentialNotFoundError(
            "No valid credentials found from any detection method"
        )

    async def _detect_docker_secrets_credentials(
        self,
    ) -> Optional[GoogleCloudCredentials]:
        """
        Detect credentials from Docker secrets using _FILE suffix pattern.

        Supports both GOOGLE_APPLICATION_CREDENTIALS_FILE and legacy patterns.

        Returns:
            GoogleCloudCredentials: Service account credentials from Docker secrets
        """
        # Check for Docker secrets file
        secrets_file = ConfigurationService.get("GOOGLE_APPLICATION_CREDENTIALS_FILE")
        if not secrets_file:
            return None

        try:
            secrets_path = Path(secrets_file)
            if not secrets_path.exists():
                raise DockerSecretsError(
                    f"Docker secrets file not found: {secrets_file}"
                )

            # Read service account JSON from Docker secret
            with open(secrets_path) as f:
                service_account_content = f.read().strip()

            if not service_account_content:
                raise DockerSecretsError(
                    f"Docker secrets file is empty: {secrets_file}"
                )

            # Parse and validate service account JSON
            try:
                service_account_info = json.loads(service_account_content)
            except json.JSONDecodeError:
                raise DockerSecretsError(
                    f"Docker secrets file contains invalid JSON: {secrets_file}"
                )

            # Extract project ID from service account or use environment variable
            project_id = service_account_info.get(
                "project_id"
            ) or ConfigurationService.get("GOOGLE_PROJECT_ID")

            if not project_id:
                logger.warning(
                    "No project_id found in service account file or GOOGLE_PROJECT_ID environment variable"
                )

            logger.info(f"Docker secrets credentials loaded from: {secrets_file}")

            return GoogleCloudCredentials(
                credential_type="service_account",
                project_id=project_id,
                service_account_info=service_account_info,
                scopes=self.settings.get_scopes_list(),
            )

        except Exception as e:
            logger.error(f"Failed to load Docker secrets credentials: {e}")
            raise DockerSecretsError(f"Docker secrets credential loading failed: {e}")

    async def _detect_service_account_credentials(
        self,
    ) -> Optional[GoogleCloudCredentials]:
        """
        Detect credentials from service account file.

        Returns:
            GoogleCloudCredentials: Service account credentials from file
        """
        service_account_path = ConfigurationService.get(
            "GOOGLE_APPLICATION_CREDENTIALS"
        )
        if not service_account_path:
            return None

        try:
            path = Path(service_account_path)
            if not path.exists():
                logger.warning(
                    f"Service account file not found: {service_account_path}"
                )
                return None

            # Read and validate service account file
            with open(path) as f:
                service_account_info = json.load(f)

            # Extract project ID
            project_id = service_account_info.get(
                "project_id"
            ) or ConfigurationService.get("GOOGLE_PROJECT_ID")

            logger.info(
                f"Service account credentials loaded from: {service_account_path}"
            )

            return GoogleCloudCredentials(
                credential_type="service_account",
                project_id=project_id,
                service_account_path=str(path.absolute()),
                service_account_info=service_account_info,
                scopes=self.settings.get_scopes_list(),
            )

        except Exception as e:
            logger.error(f"Failed to load service account credentials: {e}")
            return None

    async def _detect_oauth_credentials(self) -> Optional[GoogleCloudCredentials]:
        """
        Detect OAuth credentials from environment variables.

        Returns:
            GoogleCloudCredentials: OAuth credentials
        """
        client_id = ConfigurationService.get("GOOGLE_CLIENT_ID")
        client_secret = ConfigurationService.get("GOOGLE_CLIENT_SECRET")

        if not client_id or not client_secret:
            return None

        project_id = ConfigurationService.get("GOOGLE_PROJECT_ID")

        logger.info("OAuth credentials detected from environment variables")

        return GoogleCloudCredentials(
            credential_type="oauth",
            project_id=project_id,
            client_id=client_id,
            client_secret=client_secret,
            scopes=self.settings.get_scopes_list(),
        )

    async def _detect_default_credentials(self) -> Optional[GoogleCloudCredentials]:
        """
        Detect default application credentials (gcloud, workload identity).

        Returns:
            GoogleCloudCredentials: Default credentials
        """
        # Check if gcloud is configured or workload identity is available
        try:
            # Try to detect default credentials without importing Google libraries
            # This is a lightweight check for common default credential indicators

            gcloud_config_dir = Path.home() / ".config" / "gcloud"
            workload_identity_token = os.getenv("GOOGLE_APPLICATION_CREDENTIALS_JSON")

            if gcloud_config_dir.exists() or workload_identity_token:
                project_id = ConfigurationService.get("GOOGLE_PROJECT_ID")

                logger.info("Default application credentials detected")

                return GoogleCloudCredentials(
                    credential_type="default",
                    project_id=project_id,
                    scopes=self.settings.get_scopes_list(),
                )

        except Exception as e:
            logger.debug(f"Default credentials detection failed: {e}")

        return None

    async def _detect_compute_engine_credentials(
        self,
    ) -> Optional[GoogleCloudCredentials]:
        """
        Detect Compute Engine metadata service credentials.

        Returns:
            GoogleCloudCredentials: Compute Engine credentials
        """
        # Check if running on Compute Engine by testing metadata service
        try:
            # This is a lightweight check for Compute Engine environment
            metadata_server = os.getenv("GCE_METADATA_HOST", "***************")

            # Check for common Compute Engine indicators
            if os.path.exists("/sys/class/dmi/id/product_name"):
                with open("/sys/class/dmi/id/product_name") as f:
                    product_name = f.read().strip()
                    if (
                        "Google" in product_name
                        or "Google Compute Engine" in product_name
                    ):
                        project_id = ConfigurationService.get("GOOGLE_PROJECT_ID")

                        logger.info("Compute Engine credentials detected")

                        return GoogleCloudCredentials(
                            credential_type="compute_engine",
                            project_id=project_id,
                            scopes=self.settings.get_scopes_list(),
                        )

        except Exception as e:
            logger.debug(f"Compute Engine credentials detection failed: {e}")

        return None

    def create_veo3_config(self, **overrides) -> Dict[str, Any]:
        """
        Create Veo3 provider configuration using detected credentials.

        This method integrates with the existing Veo3 configuration system
        while applying Google Cloud credentials from multi-source detection.

        Args:
            **overrides: Configuration overrides

        Returns:
            Veo3 configuration dictionary with credentials
        """
        try:
            # Get credentials (this will use cached if available)
            import asyncio

            if asyncio.get_event_loop().is_running():
                # If called from async context, credentials should already be cached
                cache_key = "default"
                if cache_key in self._credential_cache:
                    credentials = self._credential_cache[cache_key]
                else:
                    # Fallback to sync detection
                    credentials = self._sync_get_credentials()
            else:
                # Sync context
                credentials = self._sync_get_credentials()

            # Base configuration from environment
            base_config = {
                "project_id": credentials.project_id
                or ConfigurationService.get("GOOGLE_PROJECT_ID"),
                "location": ConfigurationService.get("GOOGLE_LOCATION", "us-central1"),
                "use_mock": ConfigurationService.get_bool("USE_MOCK_VEO", True),
                "timeout": ConfigurationService.get_int("VEO3_TIMEOUT", 300),
                "max_retries": ConfigurationService.get_int("VEO3_MAX_RETRIES", 3),
                "retry_delay": ConfigurationService.get_int("VEO3_RETRY_DELAY", 2),
                "generation_timeout": ConfigurationService.get_int(
                    "VEO3_GENERATION_TIMEOUT", 1800
                ),
                "rate_limit_rpm": ConfigurationService.get_int(
                    "VEO3_RATE_LIMIT_RPM", 30
                ),
                "model_version": ConfigurationService.get(
                    "VEO3_MODEL_VERSION", "veo-3.0-generate-preview"
                ),
            }

            # Add credential-specific configuration
            auth_config = credentials.to_auth_dict()
            base_config.update(auth_config)

            # Add credential metadata
            base_config["credential_type"] = credentials.credential_type
            base_config["scopes"] = credentials.scopes

            # Apply overrides
            base_config.update(overrides)

            logger.info(
                f"Veo3 configuration created with {credentials.credential_type} credentials"
            )

            return base_config

        except Exception as e:
            logger.error(f"Failed to create Veo3 configuration: {e}")

            # Fallback to environment-only configuration
            logger.warning("Falling back to environment-only Veo3 configuration")

            fallback_config = {
                "project_id": ConfigurationService.get("GOOGLE_PROJECT_ID"),
                "location": ConfigurationService.get("GOOGLE_LOCATION", "us-central1"),
                "use_mock": ConfigurationService.get_bool("USE_MOCK_VEO", True),
                "timeout": ConfigurationService.get_int("VEO3_TIMEOUT", 300),
                "max_retries": ConfigurationService.get_int("VEO3_MAX_RETRIES", 3),
                "retry_delay": ConfigurationService.get_int("VEO3_RETRY_DELAY", 2),
                "generation_timeout": ConfigurationService.get_int(
                    "VEO3_GENERATION_TIMEOUT", 1800
                ),
                "rate_limit_rpm": ConfigurationService.get_int(
                    "VEO3_RATE_LIMIT_RPM", 30
                ),
                "model_version": ConfigurationService.get(
                    "VEO3_MODEL_VERSION", "veo-3.0-generate-preview"
                ),
                "credential_type": "environment_fallback",
            }

            fallback_config.update(overrides)
            return fallback_config

    def _sync_get_credentials(self) -> GoogleCloudCredentials:
        """
        Synchronous credential detection fallback.

        This method provides synchronous credential detection for cases where
        async detection is not available.

        Returns:
            GoogleCloudCredentials: Basic credentials from environment
        """
        # Try Docker secrets first
        secrets_file = ConfigurationService.get("GOOGLE_APPLICATION_CREDENTIALS_FILE")
        if secrets_file and Path(secrets_file).exists():
            try:
                with open(secrets_file) as f:
                    service_account_info = json.load(f)

                return GoogleCloudCredentials(
                    credential_type="service_account",
                    project_id=service_account_info.get("project_id")
                    or ConfigurationService.get("GOOGLE_PROJECT_ID"),
                    service_account_info=service_account_info,
                    scopes=self.settings.get_scopes_list(),
                )
            except Exception as e:
                logger.error(f"Sync Docker secrets loading failed: {e}")

        # Try service account file
        service_account_path = ConfigurationService.get(
            "GOOGLE_APPLICATION_CREDENTIALS"
        )
        if service_account_path and Path(service_account_path).exists():
            try:
                with open(service_account_path) as f:
                    service_account_info = json.load(f)

                return GoogleCloudCredentials(
                    credential_type="service_account",
                    project_id=service_account_info.get("project_id")
                    or ConfigurationService.get("GOOGLE_PROJECT_ID"),
                    service_account_path=service_account_path,
                    service_account_info=service_account_info,
                    scopes=self.settings.get_scopes_list(),
                )
            except Exception as e:
                logger.error(f"Sync service account loading failed: {e}")

        # Try OAuth credentials
        client_id = ConfigurationService.get("GOOGLE_CLIENT_ID")
        client_secret = ConfigurationService.get("GOOGLE_CLIENT_SECRET")
        if client_id and client_secret:
            return GoogleCloudCredentials(
                credential_type="oauth",
                project_id=ConfigurationService.get("GOOGLE_PROJECT_ID"),
                client_id=client_id,
                client_secret=client_secret,
                scopes=self.settings.get_scopes_list(),
            )

        # Fallback to basic environment configuration
        return GoogleCloudCredentials(
            credential_type="default",
            project_id=ConfigurationService.get("GOOGLE_PROJECT_ID"),
            scopes=self.settings.get_scopes_list(),
        )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get credential manager performance metrics.

        Returns:
            Performance metrics dictionary
        """
        return {
            "detection_attempts": self._detection_attempts,
            "cache_hits": self._cache_hits,
            "cached_credentials": len(self._credential_cache),
            "cache_hit_ratio": self._cache_hits / max(self._detection_attempts, 1),
            "last_detection_attempt": self._last_detection_attempt,
        }

    def clear_cache(self) -> None:
        """Clear credential cache for testing or refresh."""
        self._credential_cache.clear()
        self._cache_hits = 0
        logger.info("Credential cache cleared")


# Module-level convenience functions following the global reuse pattern
_global_credential_manager: Optional[GoogleCloudCredentialManager] = None


def get_credential_manager() -> GoogleCloudCredentialManager:
    """
    Get global credential manager instance (singleton pattern).

    Following the AI agent reuse pattern from development standards,
    this ensures credential manager is instantiated once per process.

    Returns:
        GoogleCloudCredentialManager: Global credential manager instance
    """
    global _global_credential_manager
    if _global_credential_manager is None:
        _global_credential_manager = GoogleCloudCredentialManager()
        logger.info("Global GoogleCloudCredentialManager instance created")
    return _global_credential_manager


async def get_google_cloud_credentials(
    force_refresh: bool = False,
) -> GoogleCloudCredentials:
    """
    Get Google Cloud credentials using global manager.

    Args:
        force_refresh: Force refresh of cached credentials

    Returns:
        GoogleCloudCredentials: Validated credentials
    """
    manager = get_credential_manager()
    return await manager.get_credentials(force_refresh)


def create_veo3_config_with_credentials(**overrides) -> Dict[str, Any]:
    """
    Create Veo3 configuration with Google Cloud credentials.

    This function integrates with the existing configuration factory
    pattern while adding Google Cloud credential management.

    Args:
        **overrides: Configuration overrides

    Returns:
        Veo3 configuration dictionary with credentials
    """
    manager = get_credential_manager()
    return manager.create_veo3_config(**overrides)


def validate_google_cloud_environment() -> Dict[str, Any]:
    """
    Validate Google Cloud environment configuration.

    Returns:
        Validation result with status, errors, and configuration details
    """
    validation_result = {
        "valid": True,
        "errors": [],
        "warnings": [],
        "configuration": {},
        "credential_sources": {},
    }

    try:
        settings = get_cached_credential_settings()
        validation_result["configuration"]["settings_loaded"] = True

        # Check each credential source
        sources = {
            "docker_secrets": ConfigurationService.get(
                "GOOGLE_APPLICATION_CREDENTIALS_FILE"
            ),
            "service_account": ConfigurationService.get(
                "GOOGLE_APPLICATION_CREDENTIALS"
            ),
            "oauth": bool(
                ConfigurationService.get("GOOGLE_CLIENT_ID")
                and ConfigurationService.get("GOOGLE_CLIENT_SECRET")
            ),
            "default": True,  # Always potentially available
            "compute_engine": True,  # Environment-dependent
        }

        for source, available in sources.items():
            validation_result["credential_sources"][source] = bool(available)

        # Check for project ID
        project_id = ConfigurationService.get("GOOGLE_PROJECT_ID")
        if not project_id:
            validation_result["warnings"].append(
                "GOOGLE_PROJECT_ID not set - may be required for some operations"
            )

        # Validate credential file paths if provided
        for file_env, file_path in [
            ("GOOGLE_APPLICATION_CREDENTIALS", settings.GOOGLE_APPLICATION_CREDENTIALS),
            (
                "GOOGLE_APPLICATION_CREDENTIALS_FILE",
                settings.GOOGLE_APPLICATION_CREDENTIALS_FILE,
            ),
        ]:
            if file_path and not Path(file_path).exists():
                validation_result["errors"].append(
                    f"{file_env} file not found: {file_path}"
                )
                validation_result["valid"] = False

        # Check if at least one credential source is available
        if not any(sources.values()):
            validation_result["warnings"].append(
                "No Google Cloud credential sources detected"
            )

        validation_result["configuration"]["performance"] = {
            "retry_attempts": settings.CREDENTIAL_RETRY_ATTEMPTS,
            "retry_delay": settings.CREDENTIAL_RETRY_DELAY,
            "timeout": settings.CREDENTIAL_TIMEOUT,
            "cache_ttl": settings.CREDENTIAL_CACHE_TTL,
        }

        logger.info(
            f"Google Cloud environment validation completed - Valid: {validation_result['valid']}"
        )

    except Exception as e:
        validation_result["valid"] = False
        validation_result["errors"].append(f"Validation failed: {str(e)}")
        logger.error(f"Google Cloud environment validation failed: {e}")

    return validation_result
