"""Veo3 provider configuration with Pydantic v2 validation and environment variable support.

This module provides comprehensive Google Veo3 API configuration management with:
- Environment variable loading with ConfigurationService integration
- Pydantic v2 BaseSettings for type safety and validation
- Provider switching (mock/real API) for development flexibility
- Configuration factory pattern compliance
- Performance validation and caching optimization
"""

import logging
import os
from functools import lru_cache
from typing import Any, Dict, Literal, Optional
from uuid import uuid4

from pydantic import Field, SecretStr, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from src.config.service import ConfigurationService

logger = logging.getLogger(__name__)


class Veo3Settings(BaseSettings):
    """
    Google Veo3 API configuration with environment variable support.

    Provides type-safe configuration management for Google Veo3 video generation
    with comprehensive validation, environment variable respect, and factory patterns.

    Environment Variables:
        USE_MOCK_VEO: Enable mock Veo3 API for development (default: True)
        DEFAULT_PROVIDER: Default provider selection (azure_sora|google_veo3)
        GOOGLE_PROJECT_ID: Google Cloud project ID for Veo3 API
        GOOGLE_CLIENT_ID: Google Cloud OAuth client ID
        GOOGLE_CLIENT_SECRET: Google Cloud OAuth client secret
        VEO3_TIMEOUT: API request timeout in seconds (default: 300)
        VEO3_MAX_RETRIES: Maximum retry attempts for API requests (default: 3)
        VEO3_RETRY_DELAY: Delay between retries in seconds (default: 2)
        VEO3_GENERATION_TIMEOUT: Video generation timeout in seconds (default: 1800)
        VEO3_MODEL_VERSION: Veo3 model version to use (default: "veo3-v1")
        VEO3_RATE_LIMIT_RPM: Rate limit requests per minute (default: 30)

    Example:
        >>> settings = Veo3Settings()
        >>> config = Veo3ProviderConfig.from_settings(settings)
        >>> client = Veo3Client(config)
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        validate_default=True,
        env_nested_delimiter="__",
        # Integration with ConfigurationService patterns
        protected_namespaces=("model_",),  # Avoid conflicts with Pydantic model_ prefix
    )

    # Provider switching configuration - NO hardcoded defaults
    USE_MOCK_VEO: bool = Field(
        default=False, description="Use mock Veo3 API for development and testing"
    )

    DEFAULT_PROVIDER: Literal["azure_sora", "google_veo3"] = Field(
        default="azure_sora", description="Default video generation provider"
    )

    # Google Cloud authentication configuration
    GOOGLE_PROJECT_ID: Optional[str] = Field(
        default="veo3-cf",
        description="Google Cloud project ID for Veo3 API access",
    )

    GOOGLE_CLIENT_ID: Optional[str] = Field(
        default=None, description="Google Cloud OAuth client ID"
    )

    GOOGLE_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="Google Cloud OAuth client secret (secure)"
    )

    # API configuration with performance targets
    VEO3_TIMEOUT: int = Field(
        default=300, ge=30, le=1800, description="API request timeout in seconds"
    )

    VEO3_MAX_RETRIES: int = Field(
        default=3,
        ge=0,
        le=10,
        description="Maximum retry attempts for failed API requests",
    )

    VEO3_RETRY_DELAY: int = Field(
        default=2, ge=1, le=30, description="Delay between retries in seconds"
    )

    VEO3_GENERATION_TIMEOUT: int = Field(
        default=1800,
        ge=300,
        le=7200,
        description="Video generation timeout in seconds (30 minutes default)",
    )

    # Model configuration
    VEO3_MODEL_VERSION: str = Field(
        default="veo-3.0-generate-preview",
        description="Veo3 model version to use for generation",
    )

    # Rate limiting configuration
    VEO3_RATE_LIMIT_RPM: int = Field(
        default=30, ge=1, le=1000, description="Rate limit requests per minute"
    )

    @field_validator("GOOGLE_PROJECT_ID")
    @classmethod
    def validate_google_project_id(cls, v: Optional[str]) -> Optional[str]:
        """
        Validate Google Cloud project ID format.

        Args:
            v: Project ID value to validate

        Returns:
            Validated project ID or None

        Raises:
            ValueError: If project ID format is invalid
        """
        if v is None:
            return v

        # Google project IDs must be 6-30 characters, lowercase letters, digits, hyphens
        if not v.replace("-", "").replace("_", "").isalnum():
            raise ValueError(
                "Project ID must contain only lowercase letters, digits, and hyphens"
            )

        if len(v) < 6 or len(v) > 30:
            raise ValueError("Project ID must be 6-30 characters long")

        if v.startswith("-") or v.endswith("-"):
            raise ValueError("Project ID cannot start or end with hyphen")

        return v.lower()

    @field_validator("VEO3_MODEL_VERSION")
    @classmethod
    def validate_model_version(cls, v: str) -> str:
        """
        Validate Veo3 model version format.

        Args:
            v: Model version to validate

        Returns:
            Validated model version

        Raises:
            ValueError: If model version format is invalid
        """
        valid_versions = [
            "veo-3.0-generate-preview",
            "veo3-v1",
            "veo3-preview",
            "veo3-beta",
        ]
        if v not in valid_versions:
            raise ValueError(
                f"Model version must be one of: {', '.join(valid_versions)}"
            )

        return v

    def get_provider_availability(self) -> Dict[str, bool]:
        """
        Check availability of video generation providers.

        Returns:
            Dict mapping provider names to availability status
        """
        availability = {
            "azure_sora": True,  # Always available (existing system)
            "google_veo3": False,  # Default to unavailable
        }

        # Check Google Veo3 availability
        if self.USE_MOCK_VEO:
            availability["google_veo3"] = True  # Mock is always available
        else:
            # Check for required authentication configuration
            availability["google_veo3"] = bool(
                self.GOOGLE_PROJECT_ID
                and (
                    self.GOOGLE_CLIENT_ID or os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
                )
            )

        return availability

    def validate_provider_configuration(self, provider: str) -> Dict[str, Any]:
        """
        Validate configuration for specified provider.

        Args:
            provider: Provider name to validate ("azure_sora" or "google_veo3")

        Returns:
            Validation result with status and any errors
        """
        test_id = str(uuid4())[:8]

        if provider == "azure_sora":
            # Azure Sora validation (existing system)
            azure_endpoint = ConfigurationService.get("AZURE_OPENAI_ENDPOINT")
            azure_key = ConfigurationService.get("AZURE_OPENAI_API_KEY")

            return {
                "provider": provider,
                "test_id": test_id,
                "valid": bool(azure_endpoint and azure_key),
                "errors": []
                if azure_endpoint and azure_key
                else [
                    "AZURE_OPENAI_ENDPOINT required",
                    "AZURE_OPENAI_API_KEY required",
                ],
                "configuration": {
                    "endpoint": bool(azure_endpoint),
                    "api_key": bool(azure_key),
                    "api_version": ConfigurationService.get(
                        "AZURE_OPENAI_API_VERSION", "preview"
                    ),
                },
            }

        elif provider == "google_veo3":
            # Google Veo3 validation
            errors = []

            if not self.GOOGLE_PROJECT_ID:
                errors.append("GOOGLE_PROJECT_ID required for Veo3")

            # Check both USE_MOCK_VEO and USE_MOCK_VEO3 environment variables
            use_mock_veo3 = os.getenv("USE_MOCK_VEO3", "").lower() == "true"
            use_mock = self.USE_MOCK_VEO or use_mock_veo3

            if not use_mock:
                if not self.GOOGLE_CLIENT_ID and not os.getenv(
                    "GOOGLE_APPLICATION_CREDENTIALS"
                ):
                    errors.append(
                        "GOOGLE_CLIENT_ID or GOOGLE_APPLICATION_CREDENTIALS required"
                    )

                if self.GOOGLE_CLIENT_ID and not self.GOOGLE_CLIENT_SECRET:
                    errors.append(
                        "GOOGLE_CLIENT_SECRET required when using GOOGLE_CLIENT_ID"
                    )

            return {
                "provider": provider,
                "test_id": test_id,
                "valid": len(errors) == 0,
                "errors": errors,
                "configuration": {
                    "project_id": bool(self.GOOGLE_PROJECT_ID),
                    "mock_mode": self.USE_MOCK_VEO,
                    "client_id": bool(self.GOOGLE_CLIENT_ID),
                    "client_secret": bool(self.GOOGLE_CLIENT_SECRET),
                    "application_credentials": bool(
                        os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
                    ),
                    "model_version": self.VEO3_MODEL_VERSION,
                    "timeout": self.VEO3_TIMEOUT,
                    "rate_limit_rpm": self.VEO3_RATE_LIMIT_RPM,
                },
            }

        else:
            return {
                "provider": provider,
                "test_id": test_id,
                "valid": False,
                "errors": [f"Unknown provider: {provider}"],
                "configuration": {},
            }

    def get_performance_config(self) -> Dict[str, Any]:
        """
        Get performance-related configuration for monitoring and optimization.

        Returns:
            Performance configuration dictionary
        """
        return {
            "timeouts": {
                "api_timeout": self.VEO3_TIMEOUT,
                "generation_timeout": self.VEO3_GENERATION_TIMEOUT,
                "retry_delay": self.VEO3_RETRY_DELAY,
            },
            "retry_config": {
                "max_retries": self.VEO3_MAX_RETRIES,
                "retry_delay": self.VEO3_RETRY_DELAY,
            },
            "rate_limiting": {"requests_per_minute": self.VEO3_RATE_LIMIT_RPM},
            "targets": {
                "settings_load_time_ms": 50,
                "factory_creation_time_ms": 10,
                "validation_time_ms": 100,
            },
        }


# Note: Configuration integration now handled via model_config above
# ConfigurationService integration is managed through environment variable loading


@lru_cache(maxsize=1)
def get_cached_veo3_settings() -> Veo3Settings:
    """
    Get cached Veo3Settings instance for performance optimization.

    Uses LRU cache to ensure settings are loaded once per process,
    following the existing project's caching patterns.

    Returns:
        Cached Veo3Settings instance
    """
    logger.debug("Loading Veo3Settings (cached)")
    return Veo3Settings()


class Veo3ProviderConfig:
    """
    Google Veo3 provider configuration for client initialization.

    Provides a typed configuration object for initializing Veo3 API clients
    with all required authentication and performance parameters.
    """

    def __init__(
        self,
        project_id: str,
        use_mock: bool = True,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        model_version: str = "veo3-v1",
        timeout: int = 300,
        max_retries: int = 3,
        retry_delay: int = 2,
        generation_timeout: int = 1800,
        rate_limit_rpm: int = 30,
    ):
        """
        Initialize Veo3 provider configuration.

        Args:
            project_id: Google Cloud project ID
            use_mock: Use mock API for development
            client_id: OAuth client ID (optional if using service account)
            client_secret: OAuth client secret (optional if using service account)
            model_version: Veo3 model version
            timeout: API request timeout in seconds
            max_retries: Maximum retry attempts
            retry_delay: Delay between retries
            generation_timeout: Video generation timeout
            rate_limit_rpm: Rate limit requests per minute
        """
        self.project_id = project_id
        self.use_mock = use_mock
        self.client_id = client_id
        self.client_secret = client_secret
        self.model_version = model_version
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.generation_timeout = generation_timeout
        self.rate_limit_rpm = rate_limit_rpm

    @classmethod
    def from_settings(cls, settings: Veo3Settings) -> "Veo3ProviderConfig":
        """
        Create provider configuration from Veo3Settings.

        Args:
            settings: Veo3Settings instance

        Returns:
            Veo3ProviderConfig instance

        Raises:
            ValueError: If required settings are missing
        """
        if not settings.GOOGLE_PROJECT_ID:
            raise ValueError(
                "GOOGLE_PROJECT_ID is required for Veo3 provider configuration"
            )

        client_secret = None
        if settings.GOOGLE_CLIENT_SECRET:
            client_secret = settings.GOOGLE_CLIENT_SECRET.get_secret_value()

        return cls(
            project_id=settings.GOOGLE_PROJECT_ID,
            use_mock=settings.USE_MOCK_VEO,
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=client_secret,
            model_version=settings.VEO3_MODEL_VERSION,
            timeout=settings.VEO3_TIMEOUT,
            max_retries=settings.VEO3_MAX_RETRIES,
            retry_delay=settings.VEO3_RETRY_DELAY,
            generation_timeout=settings.VEO3_GENERATION_TIMEOUT,
            rate_limit_rpm=settings.VEO3_RATE_LIMIT_RPM,
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary for serialization.

        Returns:
            Configuration dictionary (excluding sensitive values)
        """
        return {
            "project_id": self.project_id,
            "use_mock": self.use_mock,
            "client_id": self.client_id,
            "model_version": self.model_version,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "generation_timeout": self.generation_timeout,
            "rate_limit_rpm": self.rate_limit_rpm,
            # Exclude client_secret for security
        }


def validate_veo3_environment() -> Dict[str, Any]:
    """
    Validate Veo3 environment configuration and dependencies.

    Performs comprehensive validation of Veo3 configuration including
    environment variables, authentication, and integration compatibility.

    Returns:
        Validation result with status, errors, and configuration details
    """
    test_id = str(uuid4())[:8]
    validation_result = {
        "test_id": test_id,
        "timestamp": logger.info("Starting Veo3 environment validation"),
        "valid": True,
        "errors": [],
        "warnings": [],
        "configuration": {},
    }

    try:
        # Load and validate settings
        settings = get_cached_veo3_settings()
        validation_result["configuration"]["settings_loaded"] = True

        # Validate provider availability
        availability = settings.get_provider_availability()
        validation_result["configuration"]["provider_availability"] = availability

        # Validate Google Veo3 configuration
        veo3_validation = settings.validate_provider_configuration("google_veo3")
        if not veo3_validation["valid"]:
            validation_result["errors"].extend(veo3_validation["errors"])
            validation_result["valid"] = False

        # Validate Azure Sora configuration (existing provider)
        azure_validation = settings.validate_provider_configuration("azure_sora")
        validation_result["configuration"]["azure_validation"] = azure_validation

        # Performance configuration validation
        perf_config = settings.get_performance_config()
        validation_result["configuration"]["performance"] = perf_config

        # Integration warnings
        if (
            settings.DEFAULT_PROVIDER == "google_veo3"
            and not availability["google_veo3"]
        ):
            validation_result["warnings"].append(
                "DEFAULT_PROVIDER set to google_veo3 but Veo3 is not available"
            )

        logger.info(
            f"Veo3 environment validation completed - Valid: {validation_result['valid']}"
        )

    except Exception as e:
        validation_result["valid"] = False
        validation_result["errors"].append(f"Validation failed: {str(e)}")
        logger.error(f"Veo3 environment validation failed: {e}")

    return validation_result
