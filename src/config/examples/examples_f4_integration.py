#!/usr/bin/env python3
"""
F4 Integration Examples

Demonstrates how to integrate F4 dual-provider configuration into real
application components including Flask apps, Celery tasks, testing frameworks,
and production deployment scenarios.

These examples show practical integration patterns developers will use
in their applications.
"""

import logging
import sys
import time
from typing import Any, Dict, Optional

# Setup logging for examples
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def example_flask_application_integration():
    """Example 1: Flask application with F4 provider configuration."""
    print("\n" + "=" * 60)
    print("Example 1: Flask Application Integration")
    print("=" * 60)

    try:
        # Simulate Flask application setup with F4 configuration
        print("Setting up Flask application with F4 dual-provider support...")

        from src.config.factory import (
            ConfigurationFactory,
            ProviderConfigurationFactory,
        )

        # Application startup configuration
        print("1. Application startup - detecting optimal provider...")

        optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()
        selected_provider = optimal_config.get("selected_provider")
        availability = optimal_config.get("provider_availability", {})

        print(f"   Selected provider: {selected_provider}")
        print(f"   Provider availability: {availability}")

        # Provider-specific application setup
        print("2. Provider-specific application setup...")

        if selected_provider == "azure_sora":
            print("   Configuring for Azure Sora:")
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            print(f"     Endpoint: {azure_config.get('endpoint', 'Not configured')}")
            print(f"     API Version: {azure_config.get('api_version')}")
            print("     → Would initialize AzureOpenAI client")

        elif selected_provider == "google_veo3":
            print("   Configuring for Google Veo3:")
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
            print(f"     Project ID: {veo3_config.get('project_id')}")
            print(f"     Mock mode: {veo3_config.get('use_mock')}")
            print(f"     Model: {veo3_config.get('model_version')}")
            print("     → Would initialize Veo3 client")

        # Request handling simulation
        print("3. Request handling simulation...")

        def simulate_video_generation_request(
            prompt: str, provider: Optional[str] = None
        ):
            """Simulate handling a video generation request."""

            # Use default provider if none specified
            if provider is None:
                provider = selected_provider

            # Validate provider availability
            if not availability.get(provider, False):
                return {
                    "error": f"Provider {provider} not available",
                    "status": "failed",
                }

            # Get provider configuration
            try:
                config = ConfigurationFactory.create_provider_config(provider)

                # Simulate request processing
                return {
                    "status": "success",
                    "provider": provider,
                    "prompt": prompt,
                    "config_used": True,
                    "mock_mode": config.get("use_mock", False),
                }

            except Exception as e:
                return {"error": str(e), "status": "failed"}

        # Test request simulation
        test_requests = [
            ("A cat playing piano", None),  # Use default provider
            ("A dog dancing", "azure_sora"),  # Specific provider
            ("A bird singing", "google_veo3"),  # Specific provider
        ]

        for prompt, provider in test_requests:
            result = simulate_video_generation_request(prompt, provider)
            if result["status"] == "success":
                print(
                    f"   ✅ Request processed: {prompt[:20]}... → {result['provider']}"
                )
            else:
                print(f"   ❌ Request failed: {result['error']}")

        print("✅ Flask application integration simulation completed")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_celery_task_integration():
    """Example 2: Celery task with provider-aware configuration."""
    print("\n" + "=" * 60)
    print("Example 2: Celery Task Integration")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        print("Simulating Celery task with F4 provider configuration...")

        # Simulate Celery task function
        def video_generation_task(
            prompt: str, provider: Optional[str] = None, **kwargs
        ):
            """
            Simulated Celery task for video generation.

            Args:
                prompt: Text prompt for video generation
                provider: Optional provider override
                **kwargs: Additional generation parameters
            """
            task_id = f"task-{int(time.time())}"

            print(f"   📋 Task {task_id} started")
            print(f"      Prompt: {prompt}")
            print(f"      Requested provider: {provider or 'auto-select'}")

            # Provider selection logic
            if provider is None:
                provider = ConfigurationFactory.get_default_provider()
                print(f"      Using default provider: {provider}")

            # Validate provider availability
            availability = ConfigurationFactory.get_provider_availability()
            if not availability.get(provider, False):
                error_msg = f"Provider {provider} not available"
                print(f"      ❌ {error_msg}")
                return {"task_id": task_id, "status": "failed", "error": error_msg}

            # Create provider configuration
            try:
                config = ConfigurationFactory.create_provider_config(provider)
                print(f"      ✅ Configuration created for {provider}")

                # Simulate provider-specific processing
                if provider == "azure_sora":
                    print(
                        f"         Azure endpoint: {config.get('endpoint', 'Not set')}"
                    )
                    print("         Processing with Azure Sora...")

                elif provider == "google_veo3":
                    print(f"         Project ID: {config.get('project_id')}")
                    print(f"         Mock mode: {config.get('use_mock')}")
                    print("         Processing with Google Veo3...")

                # Simulate processing time
                time.sleep(0.1)

                # Return success result
                result = {
                    "task_id": task_id,
                    "status": "completed",
                    "provider": provider,
                    "prompt": prompt,
                    "mock_mode": config.get("use_mock", False),
                    "processing_time": 0.1,
                }

                print(f"      ✅ Task {task_id} completed successfully")
                return result

            except Exception as e:
                error_msg = f"Configuration error: {e}"
                print(f"      ❌ {error_msg}")
                return {"task_id": task_id, "status": "failed", "error": error_msg}

        # Simulate multiple task executions
        print("\n1. Testing task execution with different providers...")

        test_tasks = [
            ("A sunset over mountains", None),
            ("A busy city street", "azure_sora"),
            ("Ocean waves crashing", "google_veo3"),
        ]

        task_results = []

        for prompt, provider in test_tasks:
            print(f"\n   Executing task: {prompt[:30]}...")
            result = video_generation_task(prompt, provider)
            task_results.append(result)

        # Summary of results
        print("\n2. Task execution summary:")
        successful_tasks = [r for r in task_results if r["status"] == "completed"]
        failed_tasks = [r for r in task_results if r["status"] == "failed"]

        print(f"   Successful tasks: {len(successful_tasks)}/{len(task_results)}")

        for result in successful_tasks:
            print(
                f"   ✅ {result['task_id']}: {result['provider']} (mock: {result['mock_mode']})"
            )

        for result in failed_tasks:
            print(f"   ❌ {result['task_id']}: {result['error']}")

        # Simulate task error handling
        print("\n3. Testing error handling...")

        error_result = video_generation_task("Test prompt", "invalid_provider")
        if error_result["status"] == "failed":
            print(f"   ✅ Error handling working: {error_result['error']}")

        print("✅ Celery task integration simulation completed")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_testing_integration():
    """Example 3: Testing framework integration with F4."""
    print("\n" + "=" * 60)
    print("Example 3: Testing Framework Integration")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        print("Demonstrating F4 testing patterns...")

        # Test fixture simulation
        def create_test_veo3_config():
            """Test fixture for creating Veo3 test configuration."""
            return ConfigurationFactory.create_veo3_config(
                use_mock=True,
                project_id="test-project-12345",
                timeout=60,
                max_retries=1,
                model_version="veo3-test",
            )

        # Test case simulations
        def test_provider_configuration_creation():
            """Test provider configuration creation."""
            print("   Running: test_provider_configuration_creation")

            # Test Azure configuration
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            assert "endpoint" in azure_config, "Azure config missing endpoint"
            assert "api_version" in azure_config, "Azure config missing API version"

            # Test Veo3 configuration
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
            assert "project_id" in veo3_config, "Veo3 config missing project ID"
            assert "use_mock" in veo3_config, "Veo3 config missing mock setting"

            print("     ✅ Provider configurations created successfully")
            return True

        def test_mock_configuration():
            """Test mock configuration for testing."""
            print("   Running: test_mock_configuration")

            # Create test configuration
            test_config = create_test_veo3_config()

            assert test_config.use_mock == True, "Test config should use mock"
            assert test_config.project_id == "test-project-12345", (
                "Test project ID mismatch"
            )
            assert test_config.timeout == 60, "Test timeout mismatch"

            print("     ✅ Mock configuration validated")
            return True

        def test_environment_aware_configuration():
            """Test environment-aware configuration."""
            print("   Running: test_environment_aware_configuration")

            from src.config.factory import ProviderConfigurationFactory

            # Test local environment
            local_config = ProviderConfigurationFactory.create_environment_aware_config(
                "google_veo3", environment="local"
            )

            # Local should typically use mock
            assert local_config.get("use_mock") == True, "Local config should use mock"

            # Test production environment
            prod_config = ProviderConfigurationFactory.create_environment_aware_config(
                "google_veo3", environment="production"
            )

            # Production should never use mock
            assert prod_config.get("use_mock") == False, (
                "Production config should not use mock"
            )

            print("     ✅ Environment-aware configuration validated")
            return True

        def test_configuration_validation():
            """Test configuration validation."""
            print("   Running: test_configuration_validation")

            # Test Azure validation
            azure_validation = ConfigurationFactory.validate_provider_configuration(
                "azure_sora"
            )
            assert "valid" in azure_validation, "Validation missing 'valid' field"
            assert "errors" in azure_validation, "Validation missing 'errors' field"

            # Test Veo3 validation
            veo3_validation = ConfigurationFactory.validate_provider_configuration(
                "google_veo3"
            )
            assert "valid" in veo3_validation, "Validation missing 'valid' field"
            assert "configuration" in veo3_validation, (
                "Validation missing 'configuration' field"
            )

            print("     ✅ Configuration validation working")
            return True

        def test_provider_availability():
            """Test provider availability checking."""
            print("   Running: test_provider_availability")

            availability = ConfigurationFactory.get_provider_availability()

            assert "azure_sora" in availability, "Azure Sora missing from availability"
            assert "google_veo3" in availability, (
                "Google Veo3 missing from availability"
            )
            assert isinstance(availability["azure_sora"], bool), (
                "Azure availability not boolean"
            )
            assert isinstance(availability["google_veo3"], bool), (
                "Veo3 availability not boolean"
            )

            print("     ✅ Provider availability checking working")
            return True

        # Run test cases
        print("\n1. Running test cases...")

        test_cases = [
            test_provider_configuration_creation,
            test_mock_configuration,
            test_environment_aware_configuration,
            test_configuration_validation,
            test_provider_availability,
        ]

        test_results = []

        for test_case in test_cases:
            try:
                success = test_case()
                test_results.append((test_case.__name__, success, None))
            except Exception as e:
                test_results.append((test_case.__name__, False, str(e)))
                print(f"     ❌ {test_case.__name__} failed: {e}")

        # Test results summary
        print("\n2. Test results summary:")

        passed_tests = [r for r in test_results if r[1]]
        failed_tests = [r for r in test_results if not r[1]]

        print(f"   Passed: {len(passed_tests)}/{len(test_results)}")

        for test_name, success, error in test_results:
            status = "✅" if success else "❌"
            print(f"   {status} {test_name}")
            if error:
                print(f"      Error: {error}")

        # Demonstrate test isolation
        print("\n3. Testing configuration isolation...")

        # Create separate test configurations
        test_config_1 = create_test_veo3_config()
        test_config_2 = ConfigurationFactory.create_veo3_config(
            use_mock=True, project_id="different-project-67890", timeout=30
        )

        assert test_config_1.project_id != test_config_2.project_id, (
            "Test configs not isolated"
        )
        assert test_config_1.timeout != test_config_2.timeout, (
            "Test timeouts not isolated"
        )

        print("   ✅ Test configuration isolation working")

        print("✅ Testing framework integration completed")
        return len(failed_tests) == 0

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_production_deployment_integration():
    """Example 4: Production deployment integration patterns."""
    print("\n" + "=" * 60)
    print("Example 4: Production Deployment Integration")
    print("=" * 60)

    try:
        from src.config.factory import (
            ConfigurationFactory,
            ProviderConfigurationFactory,
        )

        print("Simulating production deployment with F4 configuration...")

        # 1. Production startup sequence
        print("\n1. Production application startup sequence...")

        def production_startup():
            """Simulate production application startup."""

            print("   Step 1: Validate environment...")

            # Ensure we're in production mode
            from src.config.service import ConfigurationService

            deployment_type = ConfigurationService._detect_deployment_type()
            print(f"      Detected deployment: {deployment_type}")

            print("   Step 2: Validate provider configurations...")

            # Validate all providers
            providers = ["azure_sora", "google_veo3"]
            validation_results = {}

            for provider in providers:
                validation = ConfigurationFactory.validate_provider_configuration(
                    provider
                )
                validation_results[provider] = validation

                if validation["valid"]:
                    print(f"      ✅ {provider} configuration valid")
                else:
                    print(f"      ⚠️  {provider} configuration issues:")
                    for error in validation["errors"]:
                        print(f"         • {error}")

            print("   Step 3: Select optimal provider...")

            # Get optimal provider for production
            optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()
            selected_provider = optimal_config.get("selected_provider")

            print(f"      Selected provider: {selected_provider}")

            # Ensure production security
            if selected_provider == "google_veo3":
                veo3_config = optimal_config
                if veo3_config.get("use_mock", False):
                    print("      ⚠️  WARNING: Mock mode enabled in production!")
                else:
                    print("      ✅ Production mode confirmed (mock disabled)")

            return {
                "deployment_type": deployment_type,
                "selected_provider": selected_provider,
                "validations": validation_results,
                "production_ready": True,
            }

        startup_result = production_startup()

        # 2. Health check simulation
        print("\n2. Production health check simulation...")

        def health_check():
            """Simulate production health check."""

            health_status = {"status": "healthy", "checks": {}}

            # Check provider availability
            availability = ConfigurationFactory.get_provider_availability()
            health_status["checks"]["provider_availability"] = availability

            available_count = sum(1 for avail in availability.values() if avail)
            if available_count == 0:
                health_status["status"] = "unhealthy"
                health_status["error"] = "No providers available"

            # Check configuration validity
            providers = ["azure_sora", "google_veo3"]
            config_health = {}

            for provider in providers:
                if availability.get(provider, False):
                    validation = ConfigurationFactory.validate_provider_configuration(
                        provider
                    )
                    config_health[provider] = validation["valid"]
                else:
                    config_health[provider] = False

            health_status["checks"]["configuration_health"] = config_health

            # Check performance
            start_time = time.time()
            ConfigurationFactory.create_provider_config("google_veo3")
            config_creation_time = (time.time() - start_time) * 1000

            health_status["checks"]["performance"] = {
                "config_creation_time_ms": config_creation_time,
                "within_target": config_creation_time < 100,  # 100ms target
            }

            return health_status

        health_result = health_check()
        print(f"   Health status: {health_result['status']}")

        for check_name, check_result in health_result["checks"].items():
            print(f"   {check_name}:")
            if isinstance(check_result, dict):
                for key, value in check_result.items():
                    status = "✅" if value else "❌"
                    print(f"      {status} {key}")
            else:
                status = "✅" if check_result else "❌"
                print(f"      {status} {check_result}")

        # 3. Production error handling
        print("\n3. Production error handling simulation...")

        def handle_provider_failure(failed_provider: str):
            """Simulate handling provider failure in production."""

            print(f"   Simulating {failed_provider} failure...")

            # Get available providers
            availability = ConfigurationFactory.get_provider_availability()
            available_providers = [
                p for p, avail in availability.items() if avail and p != failed_provider
            ]

            if available_providers:
                fallback_provider = available_providers[0]
                print(f"   Falling back to {fallback_provider}")

                # Create fallback configuration
                fallback_config = ConfigurationFactory.create_provider_config(
                    fallback_provider
                )

                return {
                    "status": "recovered",
                    "fallback_provider": fallback_provider,
                    "config_created": True,
                }
            else:
                print("   ❌ No fallback providers available")
                return {"status": "failed", "error": "No fallback providers available"}

        # Test fallback scenarios
        fallback_scenarios = ["azure_sora", "google_veo3"]

        for scenario in fallback_scenarios:
            result = handle_provider_failure(scenario)
            if result["status"] == "recovered":
                print(f"   ✅ Fallback successful for {scenario}")
            else:
                print(f"   ❌ Fallback failed for {scenario}: {result.get('error')}")

        print("✅ Production deployment integration completed")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_monitoring_and_observability():
    """Example 5: Monitoring and observability integration."""
    print("\n" + "=" * 60)
    print("Example 5: Monitoring and Observability")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory
        from src.config.veo3_settings import get_cached_veo3_settings

        print("Demonstrating F4 monitoring and observability features...")

        # 1. Configuration metrics
        print("\n1. Configuration metrics collection...")

        def collect_configuration_metrics():
            """Collect configuration-related metrics."""

            metrics = {
                "timestamp": time.time(),
                "provider_availability": {},
                "configuration_performance": {},
                "configuration_health": {},
            }

            # Provider availability metrics
            availability = ConfigurationFactory.get_provider_availability()
            metrics["provider_availability"] = availability

            print(f"   Provider availability collected: {availability}")

            # Configuration creation performance
            providers = ["azure_sora", "google_veo3"]

            for provider in providers:
                if availability.get(provider, False):
                    # Measure configuration creation time
                    start_time = time.time()
                    config = ConfigurationFactory.create_provider_config(provider)
                    creation_time = (time.time() - start_time) * 1000

                    metrics["configuration_performance"][provider] = {
                        "creation_time_ms": creation_time,
                        "within_target": creation_time < 50,  # 50ms target
                    }

                    print(f"   {provider} creation time: {creation_time:.2f}ms")

            # Configuration health
            for provider in providers:
                validation = ConfigurationFactory.validate_provider_configuration(
                    provider
                )
                metrics["configuration_health"][provider] = {
                    "valid": validation["valid"],
                    "error_count": len(validation.get("errors", [])),
                }

            return metrics

        metrics = collect_configuration_metrics()

        # 2. Performance monitoring
        print("\n2. Performance monitoring...")

        def monitor_configuration_performance(iterations: int = 5):
            """Monitor configuration performance over multiple iterations."""

            performance_data = {
                "azure_sora": [],
                "google_veo3": [],
                "settings_load": [],
            }

            for i in range(iterations):
                # Monitor Azure configuration
                start_time = time.time()
                ConfigurationFactory.create_provider_config("azure_sora")
                azure_time = (time.time() - start_time) * 1000
                performance_data["azure_sora"].append(azure_time)

                # Monitor Veo3 configuration
                start_time = time.time()
                ConfigurationFactory.create_provider_config("google_veo3")
                veo3_time = (time.time() - start_time) * 1000
                performance_data["google_veo3"].append(veo3_time)

                # Monitor settings load
                start_time = time.time()
                get_cached_veo3_settings()
                settings_time = (time.time() - start_time) * 1000
                performance_data["settings_load"].append(settings_time)

            # Calculate statistics
            stats = {}
            for operation, times in performance_data.items():
                stats[operation] = {
                    "avg_ms": sum(times) / len(times),
                    "min_ms": min(times),
                    "max_ms": max(times),
                    "within_target": all(t < 50 for t in times),  # 50ms target
                }

            return stats

        perf_stats = monitor_configuration_performance()

        print("   Performance statistics (5 iterations):")
        for operation, stats in perf_stats.items():
            target_status = "✅" if stats["within_target"] else "⚠️"
            print(f"   {target_status} {operation}:")
            print(f"      Average: {stats['avg_ms']:.2f}ms")
            print(f"      Range: {stats['min_ms']:.2f}-{stats['max_ms']:.2f}ms")

        # 3. Error monitoring
        print("\n3. Error monitoring simulation...")

        def monitor_configuration_errors():
            """Monitor configuration errors and issues."""

            error_metrics = {
                "validation_errors": {},
                "creation_errors": {},
                "total_error_count": 0,
            }

            providers = ["azure_sora", "google_veo3"]

            for provider in providers:
                # Check validation errors
                validation = ConfigurationFactory.validate_provider_configuration(
                    provider
                )
                error_count = len(validation.get("errors", []))
                error_metrics["validation_errors"][provider] = error_count
                error_metrics["total_error_count"] += error_count

                if error_count > 0:
                    print(f"   ⚠️  {provider} has {error_count} validation errors")
                else:
                    print(f"   ✅ {provider} validation clean")

                # Test configuration creation errors
                try:
                    ConfigurationFactory.create_provider_config(provider)
                    error_metrics["creation_errors"][provider] = 0
                except Exception as e:
                    error_metrics["creation_errors"][provider] = 1
                    error_metrics["total_error_count"] += 1
                    print(f"   ❌ {provider} creation error: {e}")

            return error_metrics

        error_metrics = monitor_configuration_errors()

        # 4. Alerting simulation
        print("\n4. Alerting simulation...")

        def check_alerting_conditions(
            metrics: Dict[str, Any], error_metrics: Dict[str, Any]
        ):
            """Check conditions that would trigger alerts."""

            alerts = []

            # Check provider availability
            availability = metrics["provider_availability"]
            available_count = sum(1 for avail in availability.values() if avail)

            if available_count == 0:
                alerts.append(
                    {
                        "severity": "critical",
                        "message": "No video generation providers available",
                        "metric": "provider_availability",
                    }
                )
            elif available_count == 1:
                alerts.append(
                    {
                        "severity": "warning",
                        "message": "Only one video generation provider available",
                        "metric": "provider_availability",
                    }
                )

            # Check performance
            for provider, perf in metrics["configuration_performance"].items():
                if not perf["within_target"]:
                    alerts.append(
                        {
                            "severity": "warning",
                            "message": f"{provider} configuration creation time above target",
                            "metric": "performance",
                            "value": f"{perf['creation_time_ms']:.2f}ms",
                        }
                    )

            # Check errors
            if error_metrics["total_error_count"] > 0:
                alerts.append(
                    {
                        "severity": "warning",
                        "message": f"Configuration errors detected: {error_metrics['total_error_count']}",
                        "metric": "configuration_errors",
                    }
                )

            return alerts

        alerts = check_alerting_conditions(metrics, error_metrics)

        if alerts:
            print(f"   Alerts triggered: {len(alerts)}")
            for alert in alerts:
                severity_icon = "🔴" if alert["severity"] == "critical" else "🟡"
                print(
                    f"   {severity_icon} {alert['severity'].upper()}: {alert['message']}"
                )
        else:
            print("   ✅ No alerts triggered - system healthy")

        print("✅ Monitoring and observability integration completed")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run all F4 integration examples."""
    print("🔗 F4 Integration Examples")
    print("=" * 60)
    print("Demonstrating F4 integration patterns for real applications")
    print("")

    examples = [
        ("Flask Application Integration", example_flask_application_integration),
        ("Celery Task Integration", example_celery_task_integration),
        ("Testing Framework Integration", example_testing_integration),
        (
            "Production Deployment Integration",
            example_production_deployment_integration,
        ),
        ("Monitoring and Observability", example_monitoring_and_observability),
    ]

    results = {}

    for example_name, example_func in examples:
        print(f"\nRunning: {example_name}")
        try:
            success = example_func()
            results[example_name] = success
            if success:
                print(f"✅ {example_name} completed successfully")
            else:
                print(f"❌ {example_name} failed")
        except Exception as e:
            print(f"❌ {example_name} failed with exception: {e}")
            results[example_name] = False

    # Summary
    print("\n" + "=" * 60)
    print("Integration Examples Summary")
    print("=" * 60)

    successful = sum(1 for success in results.values() if success)
    total = len(results)

    for example_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {example_name}")

    print(f"\nOverall: {successful}/{total} integration examples successful")

    if successful == total:
        print("🚀 All F4 integration patterns working correctly!")
        print("🏗️  Ready for production integration")
        print("\n💡 Integration Quick Reference:")
        print(
            "   • Flask: Use ProviderConfigurationFactory.get_optimal_provider_config()"
        )
        print("   • Celery: Use ConfigurationFactory.create_provider_config(provider)")
        print(
            "   • Testing: Use ConfigurationFactory.create_veo3_config(use_mock=True)"
        )
        print(
            "   • Production: Validate with ConfigurationFactory.validate_provider_configuration()"
        )
    else:
        print("⚠️  Some integration examples failed")
        print("📖 Check logs above for specific issues")
        print("📖 See ENVIRONMENT_SETUP.md for configuration help")

    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
