# Production Environment - Image Security Configuration
# Use SORA_IMAGE_ prefix for all image security settings

# Environment
SORA_IMAGE_ENVIRONMENT=production
SORA_IMAGE_DEVELOPMENT_MODE=false

# File size limits (strict for production)
SORA_IMAGE_MAX_FILE_SIZE_MB=8
SORA_IMAGE_MAX_WIDTH=3840  # 4K width limit
SORA_IMAGE_MAX_HEIGHT=2160  # 4K height limit

# Security validation settings (maximum security)
SORA_IMAGE_ENABLE_MAGIC_NUMBER_CHECK=true
SORA_IMAGE_ENABLE_PIL_VALIDATION=true
SORA_IMAGE_ENABLE_MALWARE_SCAN=true  # Enabled for production security
SORA_IMAGE_STRICT_MIME_TYPE_CHECK=true
SORA_IMAGE_STRIP_EXIF_DATA=true

# Processing settings
SORA_IMAGE_AUTO_RESIZE=true
SORA_IMAGE_JPEG_QUALITY=80  # Slightly lower for bandwidth efficiency
SORA_IMAGE_PNG_OPTIMIZE=true

# Performance settings (conservative for production)
SORA_IMAGE_PROCESSING_TIMEOUT_SECONDS=45
SORA_IMAGE_MAX_CONCURRENT_UPLOADS=8

# PIL security settings (maximum security)
SORA_IMAGE_PIL_LOAD_TRUNCATED_IMAGES=false
SORA_IMAGE_PIL_MAX_IMAGE_PIXELS=89478485  # Conservative limit (half of PIL default)

# Provider integration
SORA_IMAGE_DEFAULT_PROVIDER=google_veo3
SORA_IMAGE_PROVIDER_TIMEOUT_SECONDS=60

# Security enforcement (maximum)
SORA_IMAGE_ENFORCE_STRICT_VALIDATION=true
SORA_IMAGE_LOG_SECURITY_EVENTS=true

# Allowed formats (restricted set for production)
SORA_IMAGE_ALLOWED_MIME_TYPES=["image/jpeg", "image/png", "image/webp"]
SORA_IMAGE_ALLOWED_EXTENSIONS=[".jpg", ".jpeg", ".png", ".webp"]