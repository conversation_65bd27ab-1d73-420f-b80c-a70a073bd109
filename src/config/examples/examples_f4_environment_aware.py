#!/usr/bin/env python3
"""
F4 Environment-Aware Configuration Examples

Demonstrates F4's environment detection and environment-specific configuration
capabilities across local development, Docker, and production environments.

These examples show how F4 automatically adapts configuration based on
deployment type and provides environment-specific optimizations.
"""

import logging
import os
import sys

# Setup logging for examples
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def example_environment_detection():
    """Example 1: Automatic environment detection."""
    print("\n" + "=" * 60)
    print("Example 1: Environment Detection")
    print("=" * 60)

    try:
        from src.config.service import ConfigurationService

        print("Detecting current deployment environment...")

        # Get current environment detection
        deployment_type = ConfigurationService._detect_deployment_type()
        print(f"Detected deployment type: {deployment_type}")

        # Show detection criteria
        print("\nDetection criteria:")
        print(f"   /.dockerenv exists: {os.path.exists('/.dockerenv')}")
        print(f"   DEPLOYMENT_TYPE env var: {os.getenv('DEPLOYMENT_TYPE', 'Not set')}")
        print(f"   FLASK_ENV: {os.getenv('FLASK_ENV', 'Not set')}")

        # Show what each environment means
        environment_descriptions = {
            "local": "Local development (localhost services, debugging enabled)",
            "docker": "Docker deployment (container hostnames, production simulation)",
            "production": "Production deployment (security hardened, optimized)",
        }

        print(f"\nCurrent environment: {environment_descriptions.get(deployment_type)}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_environment_specific_configuration():
    """Example 2: Environment-specific configuration creation."""
    print("\n" + "=" * 60)
    print("Example 2: Environment-Specific Configuration")
    print("=" * 60)

    try:
        from src.config.factory import ProviderConfigurationFactory

        environments = ["local", "docker", "production"]
        provider = "google_veo3"

        print(f"Creating {provider} configuration for different environments...")

        env_configs = {}

        for env in environments:
            print(f"\n--- {env.upper()} Environment ---")

            try:
                config = ProviderConfigurationFactory.create_environment_aware_config(
                    provider, environment=env
                )

                env_configs[env] = config

                print(f"✅ {env} configuration created")
                print(f"   Mock mode: {config.get('use_mock')}")
                print(f"   Timeout: {config.get('timeout')}s")
                print(f"   Max retries: {config.get('max_retries')}")
                print(f"   Project ID: {config.get('project_id')}")

                # Show environment-specific behavior
                if env == "local":
                    print(
                        "   → Optimized for development (shorter timeouts, mock enabled)"
                    )
                elif env == "docker":
                    print(
                        "   → Optimized for containers (longer timeouts, higher retries)"
                    )
                elif env == "production":
                    print(
                        "   → Optimized for production (mock disabled, standard timeouts)"
                    )

            except Exception as e:
                print(f"❌ Failed to create {env} configuration: {e}")
                env_configs[env] = None

        # Compare configurations
        print("\n--- Configuration Comparison ---")
        if all(env_configs.values()):
            print("Setting comparison across environments:")

            settings_to_compare = ["use_mock", "timeout", "max_retries"]

            for setting in settings_to_compare:
                print(f"\n{setting}:")
                for env in environments:
                    value = env_configs[env].get(setting)
                    print(f"   {env}: {value}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_optimal_provider_selection():
    """Example 3: Optimal provider selection based on environment."""
    print("\n" + "=" * 60)
    print("Example 3: Optimal Provider Selection")
    print("=" * 60)

    try:
        from src.config.factory import ProviderConfigurationFactory

        print("Getting optimal provider configuration for current environment...")

        optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()

        selected_provider = optimal_config.get("selected_provider")
        availability = optimal_config.get("provider_availability", {})

        print(f"Selected provider: {selected_provider}")
        print(f"Provider availability: {availability}")

        # Show selection logic
        print("\nSelection logic:")
        print("1. Check provider availability")
        print("2. Use DEFAULT_PROVIDER if available")
        print("3. Fallback to first available provider")
        print("4. Apply environment-specific optimizations")

        # Show configuration details
        config_keys = [
            k
            for k in optimal_config.keys()
            if k not in ["selected_provider", "provider_availability"]
        ]
        if config_keys:
            print(f"\nConfiguration includes: {config_keys}")

        # Validate selection makes sense
        if selected_provider in availability and availability[selected_provider]:
            print(f"✅ Selection valid: {selected_provider} is available")
        else:
            print(
                f"⚠️  Selection issue: {selected_provider} may not be properly available"
            )

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_environment_variable_priority():
    """Example 4: Environment variable priority system."""
    print("\n" + "=" * 60)
    print("Example 4: Environment Variable Priority")
    print("=" * 60)

    try:
        from src.config.service import ConfigurationService

        print("Demonstrating environment variable priority system...")

        # Show current environment files
        env_files = [".env.local", ".env", ".env.docker"]
        print("Environment file priority (highest to lowest):")

        for i, env_file in enumerate(env_files, 1):
            exists = os.path.exists(env_file)
            status = "✅ Found" if exists else "❌ Not found"
            print(f"   {i}. {env_file}: {status}")

        # Test configuration service
        test_vars = [
            "USE_MOCK_VEO",
            "DEFAULT_PROVIDER",
            "GOOGLE_PROJECT_ID",
            "VEO3_TIMEOUT",
        ]

        print("\nCurrent configuration values:")
        for var in test_vars:
            value = ConfigurationService.get(var, "Not set")
            source = "environment" if os.getenv(var) else "default"
            print(f"   {var}: {value} (from {source})")

        # Show debug information
        debug_info = ConfigurationService.get_debug_info()
        print("\nConfiguration service debug info:")
        print(f"   Initialized: {debug_info.get('initialized')}")
        print(f"   Cache size: {debug_info.get('cache_size')}")
        print(
            f"   Current working directory: {debug_info.get('current_working_directory')}"
        )
        print(f"   Process ID: {debug_info.get('process_id')}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_docker_vs_local_configuration():
    """Example 5: Docker vs Local configuration differences."""
    print("\n" + "=" * 60)
    print("Example 5: Docker vs Local Configuration")
    print("=" * 60)

    try:
        from src.config.factory import ProviderConfigurationFactory

        print("Comparing Docker vs Local configuration patterns...")

        # Create configurations for both environments
        local_config = ProviderConfigurationFactory.create_environment_aware_config(
            "google_veo3", environment="local"
        )

        docker_config = ProviderConfigurationFactory.create_environment_aware_config(
            "google_veo3", environment="docker"
        )

        print("Configuration comparison:")

        # Compare key settings
        comparison_settings = [
            ("use_mock", "Mock API usage"),
            ("timeout", "API timeout (seconds)"),
            ("max_retries", "Maximum retry attempts"),
            ("project_id", "Google Cloud project ID"),
        ]

        print(f"{'Setting':<15} {'Local':<15} {'Docker':<15} {'Description'}")
        print("-" * 70)

        for setting, description in comparison_settings:
            local_val = local_config.get(setting, "N/A")
            docker_val = docker_config.get(setting, "N/A")
            print(
                f"{setting:<15} {str(local_val):<15} {str(docker_val):<15} {description}"
            )

        # Explain differences
        print("\nKey differences:")
        print(
            "• Local development typically uses shorter timeouts for faster feedback"
        )
        print(
            "• Docker environments use longer timeouts to account for container overhead"
        )
        print("• Local may default to mock mode for easier development")
        print("• Docker simulates production conditions more closely")

        # Show service URLs that would differ
        print("\nService URL patterns:")
        print("• Local: redis://localhost:6379, postgresql://localhost:5432")
        print("• Docker: redis://redis:6379, postgresql://postgres:5432")
        print("• This enables parallel development environments")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_production_security_hardening():
    """Example 6: Production security and optimization features."""
    print("\n" + "=" * 60)
    print("Example 6: Production Security Hardening")
    print("=" * 60)

    try:
        from src.config.factory import ProviderConfigurationFactory

        print("Demonstrating production security features...")

        # Create production configuration
        production_config = (
            ProviderConfigurationFactory.create_environment_aware_config(
                "google_veo3", environment="production"
            )
        )

        print("Production configuration features:")
        print(f"   Mock mode disabled: {not production_config.get('use_mock', True)}")
        print(f"   Timeout optimized: {production_config.get('timeout')}s")
        print(f"   Retry configuration: {production_config.get('max_retries')} retries")

        # Show security considerations
        print("\nSecurity hardening in production:")
        print("• Mock APIs automatically disabled")
        print("• Environment variables validated")
        print("• Sensitive values (client_secret) excluded from logs")
        print("• Rate limiting enforced")
        print("• Timeouts set for stability")

        # Validate production readiness
        from src.config.factory import ConfigurationFactory

        validation = ConfigurationFactory.validate_provider_configuration("google_veo3")

        print("\nProduction readiness check:")
        if validation["valid"]:
            print("✅ Configuration is production-ready")

            config_details = validation.get("configuration", {})
            security_items = [
                ("mock_mode", "Mock mode disabled"),
                ("project_id", "Project ID configured"),
                ("application_credentials", "Authentication configured"),
            ]

            for key, description in security_items:
                if key in config_details:
                    value = config_details[key]
                    if key == "mock_mode":
                        status = "✅" if not value else "⚠️"
                    else:
                        status = "✅" if value else "⚠️"
                    print(f"   {status} {description}")
        else:
            print("❌ Configuration not production-ready:")
            for error in validation.get("errors", []):
                print(f"   • {error}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_environment_switching_simulation():
    """Example 7: Simulate environment switching."""
    print("\n" + "=" * 60)
    print("Example 7: Environment Switching Simulation")
    print("=" * 60)

    try:
        import os

        from src.config.factory import ProviderConfigurationFactory
        from src.config.service import ConfigurationService

        # Save original environment
        original_deployment = os.getenv("DEPLOYMENT_TYPE")
        original_flask_env = os.getenv("FLASK_ENV")

        environments_to_test = [
            ("local", {"DEPLOYMENT_TYPE": "local", "FLASK_ENV": "development"}),
            ("docker", {"DEPLOYMENT_TYPE": "docker", "FLASK_ENV": "production"}),
            (
                "production",
                {"DEPLOYMENT_TYPE": "production", "FLASK_ENV": "production"},
            ),
        ]

        print("Simulating environment switching...")

        results = {}

        for env_name, env_vars in environments_to_test:
            print(f"\n--- Switching to {env_name.upper()} ---")

            # Temporarily set environment variables
            for key, value in env_vars.items():
                os.environ[key] = value

            # Force configuration reload
            ConfigurationService.reload()

            # Test environment detection
            detected_type = ConfigurationService._detect_deployment_type()
            print(f"Detected environment: {detected_type}")

            # Create environment-aware configuration
            try:
                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )

                results[env_name] = {
                    "detected_type": detected_type,
                    "use_mock": config.get("use_mock"),
                    "timeout": config.get("timeout"),
                    "success": True,
                }

                print("✅ Configuration created successfully")
                print(f"   Mock mode: {config.get('use_mock')}")
                print(f"   Timeout: {config.get('timeout')}s")

            except Exception as e:
                results[env_name] = {
                    "detected_type": detected_type,
                    "success": False,
                    "error": str(e),
                }
                print(f"❌ Configuration failed: {e}")

        # Restore original environment
        if original_deployment:
            os.environ["DEPLOYMENT_TYPE"] = original_deployment
        else:
            os.environ.pop("DEPLOYMENT_TYPE", None)

        if original_flask_env:
            os.environ["FLASK_ENV"] = original_flask_env
        else:
            os.environ.pop("FLASK_ENV", None)

        ConfigurationService.reload()

        # Summary
        print("\n--- Environment Switching Summary ---")
        successful_switches = sum(
            1 for result in results.values() if result.get("success")
        )

        print(f"Successful environment switches: {successful_switches}/{len(results)}")

        for env_name, result in results.items():
            if result.get("success"):
                print(f"✅ {env_name}: detected as {result['detected_type']}")
            else:
                print(f"❌ {env_name}: {result.get('error', 'Unknown error')}")

        return successful_switches == len(results)

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run all F4 environment-aware configuration examples."""
    print("🌍 F4 Environment-Aware Configuration Examples")
    print("=" * 60)
    print("Demonstrating F4 environment detection and adaptation")
    print("")

    examples = [
        ("Environment Detection", example_environment_detection),
        (
            "Environment-Specific Configuration",
            example_environment_specific_configuration,
        ),
        ("Optimal Provider Selection", example_optimal_provider_selection),
        ("Environment Variable Priority", example_environment_variable_priority),
        ("Docker vs Local Configuration", example_docker_vs_local_configuration),
        ("Production Security Hardening", example_production_security_hardening),
        ("Environment Switching Simulation", example_environment_switching_simulation),
    ]

    results = {}

    for example_name, example_func in examples:
        print(f"\nRunning: {example_name}")
        try:
            success = example_func()
            results[example_name] = success
            if success:
                print(f"✅ {example_name} completed successfully")
            else:
                print(f"❌ {example_name} failed")
        except Exception as e:
            print(f"❌ {example_name} failed with exception: {e}")
            results[example_name] = False

    # Summary
    print("\n" + "=" * 60)
    print("Environment-Aware Examples Summary")
    print("=" * 60)

    successful = sum(1 for success in results.values() if success)
    total = len(results)

    for example_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {example_name}")

    print(f"\nOverall: {successful}/{total} examples successful")

    if successful == total:
        print("🚀 All F4 environment-aware features working correctly!")
        print("🏗️  Ready for deployment across all environments")
    else:
        print("⚠️  Some examples failed - check environment configuration")
        print("📖 See ENVIRONMENT_SETUP.md for environment setup help")

    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
