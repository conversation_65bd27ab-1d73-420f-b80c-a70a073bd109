# Development Environment - Image Security Configuration
# Use SORA_IMAGE_ prefix for all image security settings

# Environment
SORA_IMAGE_ENVIRONMENT=development
SORA_IMAGE_DEVELOPMENT_MODE=true

# File size limits (more generous for development)
SORA_IMAGE_MAX_FILE_SIZE_MB=15
SORA_IMAGE_MAX_WIDTH=4096
SORA_IMAGE_MAX_HEIGHT=4096

# Security validation settings (secure but not restrictive)
SORA_IMAGE_ENABLE_MAGIC_NUMBER_CHECK=true
SORA_IMAGE_ENABLE_PIL_VALIDATION=true
SORA_IMAGE_ENABLE_MALWARE_SCAN=false  # Disabled for faster development
SORA_IMAGE_STRICT_MIME_TYPE_CHECK=true
SORA_IMAGE_STRIP_EXIF_DATA=true

# Processing settings
SORA_IMAGE_AUTO_RESIZE=true
SORA_IMAGE_JPEG_QUALITY=85
SORA_IMAGE_PNG_OPTIMIZE=true

# Performance settings (more lenient for development)
SORA_IMAGE_PROCESSING_TIMEOUT_SECONDS=60
SORA_IMAGE_MAX_CONCURRENT_UPLOADS=20

# PIL security settings
SORA_IMAGE_PIL_LOAD_TRUNCATED_IMAGES=false
SORA_IMAGE_PIL_MAX_IMAGE_PIXELS=178956970

# Provider integration
SORA_IMAGE_DEFAULT_PROVIDER=google_veo3
SORA_IMAGE_PROVIDER_TIMEOUT_SECONDS=90

# Security enforcement
SORA_IMAGE_ENFORCE_STRICT_VALIDATION=true
SORA_IMAGE_LOG_SECURITY_EVENTS=true

# Allowed formats
SORA_IMAGE_ALLOWED_MIME_TYPES=["image/jpeg", "image/png", "image/webp", "image/gif"]
SORA_IMAGE_ALLOWED_EXTENSIONS=[".jpg", ".jpeg", ".png", ".webp", ".gif"]