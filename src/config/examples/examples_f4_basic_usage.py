#!/usr/bin/env python3
"""
F4 Basic Usage Examples

Demonstrates fundamental F4 dual-provider configuration patterns for
Azure Sora and Google Veo3 video generation providers.

These examples show the most common configuration patterns developers
will use in their applications.
"""

import logging
import sys

# Setup logging for examples
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def example_basic_provider_configuration():
    """Example 1: Basic provider configuration creation."""
    print("\n" + "=" * 60)
    print("Example 1: Basic Provider Configuration")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        print("Creating Azure Sora configuration...")
        azure_config = ConfigurationFactory.create_provider_config("azure_sora")
        print(f"✅ Azure Sora config created with keys: {list(azure_config.keys())}")
        print(f"   Endpoint: {azure_config.get('endpoint', 'Not set')}")
        print(f"   API Version: {azure_config.get('api_version', 'Not set')}")

        print("\nCreating Google Veo3 configuration...")
        veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
        print(f"✅ Google Veo3 config created with keys: {list(veo3_config.keys())}")
        print(f"   Project ID: {veo3_config.get('project_id', 'Not set')}")
        print(f"   Mock Mode: {veo3_config.get('use_mock', 'Not set')}")
        print(f"   Timeout: {veo3_config.get('timeout', 'Not set')}s")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_provider_availability_check():
    """Example 2: Check provider availability before use."""
    print("\n" + "=" * 60)
    print("Example 2: Provider Availability Check")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        # Check which providers are available
        availability = ConfigurationFactory.get_provider_availability()
        print("Provider Availability:")

        for provider, available in availability.items():
            status = "✅ Available" if available else "❌ Not Available"
            print(f"   {provider}: {status}")

        # Get default provider
        default_provider = ConfigurationFactory.get_default_provider()
        print(f"\nDefault provider: {default_provider}")

        # Only create configuration for available providers
        for provider, available in availability.items():
            if available:
                try:
                    config = ConfigurationFactory.create_provider_config(provider)
                    print(f"✅ {provider} configuration created successfully")
                except Exception as e:
                    print(f"❌ Failed to create {provider} configuration: {e}")
            else:
                print(f"⏭️  Skipping {provider} (not available)")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_configuration_with_overrides():
    """Example 3: Configuration with custom overrides."""
    print("\n" + "=" * 60)
    print("Example 3: Configuration with Overrides")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        print("Creating Google Veo3 configuration with custom overrides...")

        # Configuration with environment defaults
        default_config = ConfigurationFactory.create_provider_config("google_veo3")
        print("Default configuration:")
        print(f"   Mock mode: {default_config.get('use_mock')}")
        print(f"   Timeout: {default_config.get('timeout')}s")
        print(f"   Max retries: {default_config.get('max_retries')}")

        # Configuration with custom overrides
        custom_config = ConfigurationFactory.create_provider_config(
            "google_veo3",
            use_mock=True,
            timeout=120,
            max_retries=5,
            project_id="custom-project-12345",
        )

        print("\nCustom configuration:")
        print(f"   Mock mode: {custom_config.get('use_mock')} (overridden)")
        print(f"   Timeout: {custom_config.get('timeout')}s (overridden)")
        print(f"   Max retries: {custom_config.get('max_retries')} (overridden)")
        print(f"   Project ID: {custom_config.get('project_id')} (overridden)")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_veo3_specific_configuration():
    """Example 4: Google Veo3 specific configuration patterns."""
    print("\n" + "=" * 60)
    print("Example 4: Google Veo3 Specific Configuration")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory
        from src.config.veo3_settings import get_cached_veo3_settings

        print("Loading Veo3 settings from environment...")
        settings = get_cached_veo3_settings()

        print("Environment Settings:")
        print(f"   USE_MOCK_VEO: {settings.USE_MOCK_VEO}")
        print(f"   DEFAULT_PROVIDER: {settings.DEFAULT_PROVIDER}")
        print(f"   GOOGLE_PROJECT_ID: {settings.GOOGLE_PROJECT_ID}")
        print(f"   VEO3_MODEL_VERSION: {settings.VEO3_MODEL_VERSION}")
        print(f"   VEO3_TIMEOUT: {settings.VEO3_TIMEOUT}s")
        print(f"   VEO3_RATE_LIMIT_RPM: {settings.VEO3_RATE_LIMIT_RPM}")

        print("\nCreating Veo3-specific configuration object...")
        veo3_provider_config = ConfigurationFactory.create_veo3_config()

        print("Veo3 Provider Configuration:")
        print(f"   Project ID: {veo3_provider_config.project_id}")
        print(f"   Mock mode: {veo3_provider_config.use_mock}")
        print(f"   Model version: {veo3_provider_config.model_version}")
        print(f"   Timeout: {veo3_provider_config.timeout}s")
        print(f"   Generation timeout: {veo3_provider_config.generation_timeout}s")
        print(f"   Rate limit: {veo3_provider_config.rate_limit_rpm} RPM")

        # Convert to dictionary for API usage
        config_dict = veo3_provider_config.to_dict()
        print(f"\nConfiguration as dictionary: {list(config_dict.keys())}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_configuration_validation():
    """Example 5: Configuration validation patterns."""
    print("\n" + "=" * 60)
    print("Example 5: Configuration Validation")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        providers = ["azure_sora", "google_veo3"]

        for provider in providers:
            print(f"\nValidating {provider} configuration...")

            # Validate provider configuration
            validation = ConfigurationFactory.validate_provider_configuration(provider)

            if validation["valid"]:
                print(f"✅ {provider} configuration is valid")

                # Show configuration details
                config_details = validation.get("configuration", {})
                for key, value in config_details.items():
                    if isinstance(value, bool):
                        status = "✅" if value else "❌"
                        print(f"   {key}: {status}")
                    else:
                        print(f"   {key}: {value}")
            else:
                print(f"❌ {provider} configuration is invalid")
                for error in validation.get("errors", []):
                    print(f"   • {error}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_performance_configuration():
    """Example 6: Performance-focused configuration."""
    print("\n" + "=" * 60)
    print("Example 6: Performance Configuration")
    print("=" * 60)

    try:
        import time

        from src.config.factory import ConfigurationFactory

        # Test configuration creation performance
        providers = ["azure_sora", "google_veo3"]

        for provider in providers:
            print(f"\nTesting {provider} performance...")

            # Time configuration creation
            start_time = time.time()
            config = ConfigurationFactory.create_provider_config(provider)
            creation_time = (time.time() - start_time) * 1000

            print(f"   Configuration creation: {creation_time:.2f}ms")

            # Get performance configuration
            perf_config = ConfigurationFactory.get_provider_performance_config(provider)

            print("   Performance settings:")
            if "timeouts" in perf_config:
                timeouts = perf_config["timeouts"]
                for timeout_type, value in timeouts.items():
                    print(f"     {timeout_type}: {value}s")

            if "retry_config" in perf_config:
                retry_config = perf_config["retry_config"]
                print(f"     max_retries: {retry_config.get('max_retries')}")
                print(f"     retry_delay: {retry_config.get('retry_delay')}s")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def example_realistic_application_usage():
    """Example 7: Realistic application usage pattern."""
    print("\n" + "=" * 60)
    print("Example 7: Realistic Application Usage")
    print("=" * 60)

    try:
        from src.config.factory import ConfigurationFactory

        print("Simulating application startup configuration...")

        # 1. Check provider availability
        availability = ConfigurationFactory.get_provider_availability()
        available_providers = [p for p, avail in availability.items() if avail]

        print(f"Available providers: {available_providers}")

        if not available_providers:
            print("❌ No providers available!")
            return False

        # 2. Use default provider or fallback
        default_provider = ConfigurationFactory.get_default_provider()

        if default_provider in available_providers:
            selected_provider = default_provider
        else:
            selected_provider = available_providers[0]
            print(
                f"⚠️  Default provider {default_provider} not available, using {selected_provider}"
            )

        print(f"Selected provider: {selected_provider}")

        # 3. Create configuration for selected provider
        config = ConfigurationFactory.create_provider_config(selected_provider)
        print(f"✅ Configuration created for {selected_provider}")

        # 4. Application-specific configuration
        if selected_provider == "azure_sora":
            print("Configuring Azure Sora client...")
            print(f"   Endpoint: {config.get('endpoint')}")
            print(f"   API Version: {config.get('api_version')}")
            print("   Would initialize AzureOpenAI client here")

        elif selected_provider == "google_veo3":
            print("Configuring Google Veo3 client...")
            print(f"   Project ID: {config.get('project_id')}")
            print(f"   Mock mode: {config.get('use_mock')}")
            print(f"   Model version: {config.get('model_version')}")
            print("   Would initialize Veo3 client here")

        # 5. Validate configuration before use
        validation = ConfigurationFactory.validate_provider_configuration(
            selected_provider
        )
        if not validation["valid"]:
            print("❌ Configuration validation failed!")
            for error in validation["errors"]:
                print(f"   • {error}")
            return False

        print("✅ Application configured successfully")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run all F4 basic usage examples."""
    print("🔧 F4 Basic Usage Examples")
    print("=" * 60)
    print("Demonstrating F4 dual-provider configuration patterns")
    print("")

    examples = [
        ("Basic Provider Configuration", example_basic_provider_configuration),
        ("Provider Availability Check", example_provider_availability_check),
        ("Configuration with Overrides", example_configuration_with_overrides),
        ("Veo3 Specific Configuration", example_veo3_specific_configuration),
        ("Configuration Validation", example_configuration_validation),
        ("Performance Configuration", example_performance_configuration),
        ("Realistic Application Usage", example_realistic_application_usage),
    ]

    results = {}

    for example_name, example_func in examples:
        print(f"\nRunning: {example_name}")
        try:
            success = example_func()
            results[example_name] = success
            if success:
                print(f"✅ {example_name} completed successfully")
            else:
                print(f"❌ {example_name} failed")
        except Exception as e:
            print(f"❌ {example_name} failed with exception: {e}")
            results[example_name] = False

    # Summary
    print("\n" + "=" * 60)
    print("Examples Summary")
    print("=" * 60)

    successful = sum(1 for success in results.values() if success)
    total = len(results)

    for example_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {example_name}")

    print(f"\nOverall: {successful}/{total} examples successful")

    if successful == total:
        print("🚀 All F4 basic usage examples working correctly!")
        print("📖 Ready to integrate F4 dual-provider configuration")
    else:
        print("⚠️  Some examples failed - check configuration")
        print("📖 See ENVIRONMENT_SETUP.md for troubleshooting")

    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
