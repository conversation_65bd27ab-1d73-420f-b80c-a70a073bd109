# Configuration Management Module

Comprehensive environment-specific configuration management system with factory patterns, security validation, and production-ready architecture.

## Architecture Overview

**Configuration Factory Pattern**: Centralized configuration management with dependency injection and environment-specific inheritance. Provides type-safe configuration objects with comprehensive validation and security hardening.

**Environment Inheritance**: Three-tier configuration system (development → testing → production) with progressive security hardening and feature configuration.

**Security-First Design**: Comprehensive security validation, input sanitization, and production readiness checks integrated throughout the configuration system.

```
src/config/
├── __init__.py                    # Module exports and configuration factory
├── environments.py                # Environment-specific configuration classes
├── factory.py                     # Configuration factory with dependency injection
├── security.py                    # Security validation and hardening utilities
├── video_config.py                # Video generation parameter management
└── tests/
    ├── test_environments.py       # Environment configuration tests (99% coverage)
    └── test_security.py           # Security validation tests (98% coverage)
```

## Core Components

### Environment Configuration (`environments.py`)

**BaseConfig**: Foundation configuration with environment variable parsing and safe defaults.

```python
class BaseConfig:
    SECRET_KEY: str = "dev-secret-key-change-in-production"
    SQLALCHEMY_DATABASE_URI: str = "sqlite:///sora_poc.db"
    AZURE_OPENAI_API_KEY: str = ""
    AZURE_OPENAI_ENDPOINT: str = ""
    
    # Safe environment variable parsing with validation
    MAX_CONTENT_LENGTH: int = safe_int_from_env(
        "MAX_CONTENT_LENGTH", 104857600, min_val=1048576, max_val=1073741824
    )
```

**Environment-Specific Configuration**:
- **DevelopmentConfig**: Debug enabled, relaxed security, extensive logging
- **TestingConfig**: Isolated testing environment with in-memory database
- **ProductionConfig**: Security hardened, performance optimized, comprehensive monitoring

**Safe Environment Variable Parsing**:
```python
def safe_int_from_env(
    env_var: str, 
    default: int, 
    min_val: Optional[int] = None, 
    max_val: Optional[int] = None
) -> int:
    """
    Safely parse integer environment variables with validation and fallback.
    
    Args:
        env_var: Environment variable name
        default: Default value if parsing fails
        min_val: Minimum allowed value (optional)
        max_val: Maximum allowed value (optional)
        
    Returns:
        Validated integer value
        
    Raises:
        ValueError: If value is outside allowed range
    """
```

### Configuration Factory (`factory.py`)

**ConfigurationFactory**: Centralized factory for creating configuration-aware objects with dependency injection.

```python
class ConfigurationFactory:
    @staticmethod
    def get_app_config() -> Type[BaseConfig]:
        """Get Flask application configuration based on environment."""
        
    @staticmethod
    def get_azure_config() -> Dict[str, Any]:
        """Get Azure OpenAI configuration with validation."""
        
    @staticmethod
    def get_video_config() -> VideoGenerationConfig:
        """Get video generation configuration with caching."""
        
    @staticmethod
    def get_database_config() -> Dict[str, Any]:
        """Get database configuration with connection pooling."""
```

**Usage Pattern**:
```python
# Get environment-specific configuration
config = ConfigurationFactory.get_app_config()

# Get Azure OpenAI configuration
azure_config = ConfigurationFactory.get_azure_config()
client = AzureOpenAI(**azure_config)

# Get cached video configuration
video_config = ConfigurationFactory.get_video_config()
```

### Security Configuration (`security.py`)

**SecurityConfig**: Comprehensive security validation and hardening utilities.

```python
class SecurityConfig:
    # Input validation patterns
    VALID_PROMPT_PATTERN: re.Pattern = re.compile(r'^[a-zA-Z0-9\s\.,!?;:\'"()-]+$')
    VALID_JOB_ID_PATTERN: re.Pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
    VALID_FILENAME_PATTERN: re.Pattern = re.compile(r'^[a-zA-Z0-9._-]+$')
    
    # Security validation methods
    @staticmethod
    def validate_prompt(prompt: str) -> bool:
        """Validate video generation prompt for security."""
        
    @staticmethod
    def sanitize_prompt(prompt: str) -> str:
        """Sanitize prompt by removing potentially dangerous content."""
        
    @staticmethod
    def validate_job_id(job_id: str) -> bool:
        """Validate job ID format for security."""
        
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """Get comprehensive security headers for HTTP responses."""
```

**Security Headers**:
```python
SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'",
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
}
```

### Video Configuration (`video_config.py`)

**VideoGenerationConfig**: Centralized video generation parameter management with Azure API constraints.

```python
class VideoGenerationConfig:
    # Duration constraints
    MIN_DURATION: int = 1
    MAX_DURATION: int = 20
    DEFAULT_DURATION: int = 5
    
    # Resolution presets (Azure API compliant)
    RESOLUTION_PRESETS: Dict[str, Dict[str, int]] = {
        'SD': {'width': 854, 'height': 480},
        'HD': {'width': 1280, 'height': 720},
        'Full HD': {'width': 1920, 'height': 1080}
    }
    
    # Azure API supported resolutions
    SUPPORTED_RESOLUTIONS: List[Tuple[int, int]] = [
        (480, 480), (854, 480), (720, 720),
        (1280, 720), (1080, 1080), (1920, 1080)
    ]
```

**Configuration Validation**:
```python
def validate_video_parameters(
    duration: int, 
    width: int, 
    height: int
) -> Dict[str, Any]:
    """
    Validate video generation parameters against Azure API constraints.
    
    Returns:
        Validation result with constraints and recommendations
    """
```

## Integration Patterns

### Flask Application Integration

```python
from src.config.factory import ConfigurationFactory

# Application configuration
app_config = ConfigurationFactory.get_app_config()
app = Flask(__name__)
app.config.from_object(app_config)

# Security headers middleware
@app.after_request
def add_security_headers(response):
    headers = SecurityConfig.get_security_headers()
    for header, value in headers.items():
        response.headers[header] = value
    return response
```

### Pydantic Integration

```python
from src.config.factory import ConfigurationFactory

# Cached configuration for Pydantic models
def get_cached_video_config() -> VideoGenerationConfig:
    """Get cached video configuration for Pydantic validation."""
    return ConfigurationFactory.get_video_config()

# Usage in Pydantic models
class GenerationParams(BaseModel):
    duration: int = Field(
        default=get_cached_video_config().DEFAULT_DURATION,
        ge=get_cached_video_config().MIN_DURATION,
        le=get_cached_video_config().MAX_DURATION
    )
```

### Azure OpenAI Integration

```python
from src.config.factory import ConfigurationFactory

# Azure client configuration
azure_config = ConfigurationFactory.get_azure_config()
client = AzureOpenAI(
    api_key=azure_config["api_key"],
    api_version=azure_config["api_version"],
    azure_endpoint=azure_config["endpoint"]
)
```

## Testing Strategy

### Test Coverage Status
- **environments.py**: 99% coverage with comprehensive test scenarios
- **security.py**: 98% coverage with extensive security validation tests
- **factory.py**: Missing dedicated test file
- **video_config.py**: Missing dedicated test file

### Test Infrastructure

**Environment Configuration Tests**:
```python
def test_safe_int_from_env_valid_range():
    """Test safe integer parsing with range validation."""
    
def test_development_config_debug_enabled():
    """Test development configuration has debug enabled."""
    
def test_production_config_security_headers():
    """Test production configuration includes security headers."""
```

**Security Validation Tests**:
```python
def test_validate_prompt_valid_input():
    """Test prompt validation with valid characters."""
    
def test_validate_prompt_invalid_input():
    """Test prompt validation rejects malicious input."""
    
def test_security_headers_comprehensive():
    """Test security headers include all required protections."""
```

## Configuration Management Patterns

### Environment File Priority System

The configuration system uses environment-specific files to support both local development and Docker deployment:

```python
# Environment file priority order (highest to lowest)
1. .env.local    # Local development (localhost services)
2. .env          # Fallback/legacy support
3. .env.docker   # Docker deployment (container hostnames)
```

**Implementation Details**:
- **ConfigurationService**: Loads environment files in priority order
- **Celery App**: Uses same priority order for consistent behavior
- **Environment Validator**: Validates using same priority order
- **Scripts**: Smart detection and automatic file selection

**Local Development Example (`.env.local`)**:
```bash
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
DATABASE_URL=sqlite:///sora_poc.db
FLASK_ENV=development
RATE_LIMIT_ENABLED=false
```

**Docker Deployment Example (`.env.docker`)**:
```bash
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
FLASK_ENV=production
RATE_LIMIT_ENABLED=true
```

### Environment Variable Priority

```python
# Complete priority order (highest to lowest)
1. Explicit environment variables
2. Environment-specific files (.env.local, .env, .env.docker)
3. Configuration class defaults
4. BaseConfig fallback values
```

### Configuration Factory Usage

```python
# Single source of truth for configuration
config = ConfigurationFactory.get_app_config()

# Cached configuration for performance
video_config = ConfigurationFactory.get_video_config()  # Cached

# Environment-specific behavior
if isinstance(config, ProductionConfig):
    # Production-specific logic
    pass
```

### Security Configuration Patterns

```python
# Input validation before processing
if not SecurityConfig.validate_prompt(user_prompt):
    raise ValueError("Invalid prompt format")

# Sanitization for safe processing
safe_prompt = SecurityConfig.sanitize_prompt(user_prompt)

# Security headers for all responses
headers = SecurityConfig.get_security_headers()
```

## Performance Considerations

### Configuration Caching

```python
# Expensive configuration objects are cached
@lru_cache(maxsize=1)
def get_cached_video_config() -> VideoGenerationConfig:
    """Cache video configuration for performance."""
    return VideoGenerationConfig()
```

### Environment Variable Parsing

```python
# Safe parsing with validation and fallback
MAX_CONTENT_LENGTH = safe_int_from_env(
    "MAX_CONTENT_LENGTH", 
    default=104857600,
    min_val=1048576,    # 1MB minimum
    max_val=1073741824  # 1GB maximum
)
```

## Security Considerations

**Input Validation**: Validate all external inputs and API parameters using Pydantic v2
**Error Disclosure**: Avoid exposing sensitive information in error messages  
**Authentication**: Multi-user session management with secure isolation
**Authorization**: Proper access controls and rate limiting per user
**Data Protection**: Secure file handling and temporary file cleanup for video generation

## Security Hardening

### Production Security Features

```python
class ProductionConfig(BaseConfig):
    # Security hardening
    DEBUG = False
    TESTING = False
    WTF_CSRF_ENABLED = True
    
    # Database security
    SQLALCHEMY_ECHO = False
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session security
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
```

### Input Validation Patterns

```python
# Comprehensive validation patterns
VALID_PROMPT_PATTERN = re.compile(r'^[a-zA-Z0-9\s\.,!?;:\'"()-]+$')
VALID_JOB_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
VALID_FILENAME_PATTERN = re.compile(r'^[a-zA-Z0-9._-]+$')
VALID_URL_PATTERN = re.compile(r'^https?://[^\s/$.?#].[^\s]*$')
```

## Usage Examples

### Basic Configuration Setup

```python
from src.config.factory import ConfigurationFactory

# Get application configuration
config = ConfigurationFactory.get_app_config()
app = Flask(__name__)
app.config.from_object(config)

# Get Azure configuration
azure_config = ConfigurationFactory.get_azure_config()
```

### Security Validation

```python
from src.config.security import SecurityConfig

# Validate user input
if not SecurityConfig.validate_prompt(user_prompt):
    return {"error": "Invalid prompt format"}, 400

# Sanitize input
safe_prompt = SecurityConfig.sanitize_prompt(user_prompt)
```

### Video Parameter Validation

```python
from src.config.video_config import VideoGenerationConfig

config = VideoGenerationConfig()

# Validate resolution
if (width, height) not in config.SUPPORTED_RESOLUTIONS:
    return {"error": "Unsupported resolution"}, 400

# Get preset resolution
preset = config.RESOLUTION_PRESETS['HD']
width, height = preset['width'], preset['height']
```

## Technology Stack Integration

### Core Technologies
- **Python**: 3.11+ with UV package management
- **Web Framework**: Flask with WebSocket support for real-time updates
- **Data Validation**: Pydantic v2 for all models and validation
- **Database**: SQLAlchemy ORM with connection pooling
- **Message Queue**: Celery with Redis for background processing
- **AI Integration**: Azure OpenAI Sora API for video generation
- **Code Quality**: Ruff for linting and formatting, MyPy for type checking
- **Testing**: Pytest for comprehensive test coverage

### Package Management
- **CRITICAL**: Always use `uv add/remove` - never edit pyproject.toml directly
- **Environment**: UV virtual environments exclusively
- **Dependencies**: 3,160+ Python files with comprehensive dependency management
- **Commands**: `uv run pytest`, `uv run ruff check .`, `uv run mypy src/`
- **Development**: Use `uv sync` for dependency synchronization

## Dependencies

### Core Dependencies
- **typing**: Type hints and generic types
- **os**: Environment variable access
- **re**: Regular expression patterns for validation
- **functools**: LRU cache for configuration objects
- **logging**: Configuration logging and error tracking

### Integration Dependencies
- **Flask**: Application configuration integration
- **SQLAlchemy**: Database configuration
- **Pydantic**: Model validation with cached configuration
- **Azure OpenAI**: API client configuration

## Environment Configuration System

### Environment-Specific Configuration Files

The configuration system uses environment-specific files to support both local development and Docker deployment:

**Environment-Specific Configuration Files**:
- **`.env.local`**: Local development (localhost services) - highest priority
- **`.env.docker`**: Docker deployment (container hostnames) - lowest priority  
- **`.env`**: Fallback/legacy support - middle priority

**Configuration Loading Priority**:
1. **ConfigurationService**: `.env.local` → `.env` → `.env.docker`
2. **Celery App**: Same priority order for consistent behavior
3. **Environment Validator**: Same priority order for validation
4. **Scripts**: Smart detection and automatic file selection

**Development Workflow**:
- **Local Development**: `./scripts/start_local.sh` or `./scripts/start_celery_clean.sh`
- **Docker Development**: `./scripts/start_docker.sh`
- **Manual Setup**: Create `.env.local` with localhost services

**Key Benefits**:
- **Parallel Development**: Run local and Docker environments simultaneously
- **Zero Configuration Changes**: Existing code continues to work
- **Smart Detection**: Scripts automatically detect and use correct environment files
- **Conflict Prevention**: Eliminates Docker vs Local hostname conflicts

### Local Development Example (`.env.local`)
```bash
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
DATABASE_URL=sqlite:///sora_poc.db
FLASK_ENV=development
RATE_LIMIT_ENABLED=false
```

### Docker Deployment Example (`.env.docker`)
```bash
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
FLASK_ENV=production
RATE_LIMIT_ENABLED=true
```

### Environment Variable Priority
```python
# Complete priority order (highest to lowest)
1. Explicit environment variables
2. Environment-specific files (.env.local, .env, .env.docker)
3. Configuration class defaults
4. BaseConfig fallback values
```

## Development Guidelines

### Configuration Management
- **Environment-specific**: Use appropriate configuration class for each environment
- **Validation First**: Always validate configuration values before use
- **Security by Default**: Apply security hardening in all environments
- **Caching**: Cache expensive configuration objects for performance

### Testing Requirements
- **Test all environments**: Validate development, testing, and production configurations
- **Security testing**: Verify all security validation patterns work correctly
- **Environment isolation**: Ensure test configuration doesn't affect other environments
- **Edge case coverage**: Test boundary conditions and error scenarios

### Security Guidelines
- **Input validation**: Always validate user input before processing
- **Environment variables**: Use safe parsing with validation and fallbacks
- **Production hardening**: Apply comprehensive security measures in production
- **Regular updates**: Keep security patterns updated with latest best practices