"""Docker integration tests for F4 Environment Configuration.

This module provides comprehensive Docker integration testing for F4 including:
- Docker secrets mounting and credential loading
- Environment variable propagation in Docker containers
- Docker compose service integration
- Container environment detection and configuration
- Docker-specific credential file handling

Test Coverage:
- Docker secrets mounting with _FILE suffix pattern
- Environment variable validation in Docker context
- Docker compose service configuration validation
- Container filesystem credential detection
- Docker network and service discovery
- Production deployment simulation with Docker

Docker Test Scenarios:
- Docker secrets mounting at /run/secrets/google_service_account
- Environment variable inheritance from docker-compose.yml
- Container filesystem validation and credential detection
- Docker network service discovery and configuration
- Production-like Docker deployment testing
"""

import json
import os
import time
from pathlib import Path

import pytest

from src.config.environment_manager import EnvironmentVariableManager
from src.config.google_cloud_credentials import (
    DockerSecretsError,
    GoogleCloudCredentialManager,
    get_credential_manager,
)
from src.config.service import ConfigurationService


class TestF4DockerSecretsIntegration:
    """Test F4 Docker secrets integration and mounting."""

    @pytest.fixture
    def mock_docker_environment(self):
        """Setup mock Docker environment for testing."""
        original_env = dict(os.environ)

        # Clear relevant environment variables
        docker_vars = [
            "GOOGLE_APPLICATION_CREDENTIALS_FILE",
            "GOOGLE_SERVICE_ACCOUNT_SECRET_FILE",
            "DOCKER_CONTAINER",
            "DEPLOYMENT_TYPE",
        ]

        for var in docker_vars:
            os.environ.pop(var, None)

        # Set Docker environment indicators
        os.environ["DEPLOYMENT_TYPE"] = "docker"
        os.environ["DOCKER_CONTAINER"] = "sora-app-simple"

        yield

        # Restore environment
        os.environ.clear()
        os.environ.update(original_env)

    @pytest.fixture
    def sample_service_account(self):
        """Create sample service account JSON for Docker secrets."""
        return {
            "type": "service_account",
            "project_id": "docker-test-project-12345",
            "private_key_id": "dockerkey123",
            "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n",
            "client_email": "<EMAIL>",
            "client_id": "12345678901234567890",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
        }

    @pytest.fixture
    def mock_docker_secret_mount(self, sample_service_account):
        """Create mock Docker secret mount point."""
        # Create temporary directory to simulate Docker secret mount
        secret_dir = Path("/tmp/test_docker_secrets_f4")
        secret_dir.mkdir(exist_ok=True)

        secret_file = secret_dir / "google_service_account"

        # Write service account JSON to mock Docker secret location
        with open(secret_file, "w") as f:
            json.dump(sample_service_account, f)

        yield str(secret_file)

        # Cleanup mock Docker secret
        secret_file.unlink(missing_ok=True)
        secret_dir.rmdir()

    async def test_docker_secrets_file_detection(
        self, mock_docker_environment, mock_docker_secret_mount
    ):
        """Test F4 Docker secrets file detection and loading."""
        # Set Docker secrets environment variable
        os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = mock_docker_secret_mount

        # Test credential manager detects Docker secrets
        manager = GoogleCloudCredentialManager()
        credentials = await manager.get_credentials()

        # Validate Docker secrets credentials
        assert credentials.credential_type == "service_account"
        assert credentials.project_id == "docker-test-project-12345"
        assert credentials.service_account_info is not None
        assert credentials.service_account_info["type"] == "service_account"
        assert credentials.is_valid()

        # Test Docker-specific configuration
        config = manager.create_veo3_config()
        assert config["project_id"] == "docker-test-project-12345"
        assert config["credential_type"] == "service_account"
        assert "service_account_info" in config

    def test_docker_secrets_error_handling(self, mock_docker_environment):
        """Test F4 Docker secrets error handling."""
        # Test with nonexistent Docker secret file
        os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = (
            "/run/secrets/nonexistent_secret"
        )

        manager = GoogleCloudCredentialManager()

        # Should handle missing Docker secret gracefully
        try:
            config = manager.create_veo3_config()
            # Should fall back to environment configuration
            assert "credential_type" in config
            assert config["credential_type"] in [
                "environment_fallback",
                "legacy_environment",
            ]
        except DockerSecretsError:
            # DockerSecretsError is acceptable for missing files
            pass

    def test_docker_secrets_file_permissions(
        self, mock_docker_environment, sample_service_account
    ):
        """Test F4 Docker secrets file permissions and security."""
        # Create Docker secret with restricted permissions
        secret_dir = Path("/tmp/test_docker_perms")
        secret_dir.mkdir(exist_ok=True, mode=0o700)

        secret_file = secret_dir / "google_service_account"

        try:
            # Write service account with restricted permissions
            with open(secret_file, "w") as f:
                json.dump(sample_service_account, f)

            # Set restrictive permissions (typical for Docker secrets)
            secret_file.chmod(0o600)

            os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = str(secret_file)

            # F4 should handle restricted permissions correctly
            manager = GoogleCloudCredentialManager()
            config = manager.create_veo3_config()

            assert config["project_id"] == "docker-test-project-12345"

        finally:
            # Cleanup
            secret_file.unlink(missing_ok=True)
            secret_dir.rmdir()

    def test_docker_secrets_validation(
        self, mock_docker_environment, mock_docker_secret_mount
    ):
        """Test F4 Docker secrets validation and content verification."""
        os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = mock_docker_secret_mount

        # Test environment validation recognizes Docker secrets
        validation = EnvironmentVariableManager.validate_provider_configuration(
            "google_veo3"
        )

        assert validation["provider"] == "google_veo3"
        assert validation["deployment_type"] == "docker"

        # Should recognize Docker secrets in available variables
        available_vars = validation["available_variables"]
        assert "GOOGLE_APPLICATION_CREDENTIALS_FILE" in available_vars


class TestF4DockerEnvironmentVariables:
    """Test F4 Docker environment variable handling."""

    @pytest.fixture
    def mock_docker_environment(self):
        """Setup Docker environment simulation."""
        original_env = dict(os.environ)

        # Simulate Docker environment
        os.environ.update(
            {
                "DEPLOYMENT_TYPE": "docker",
                "FLASK_ENV": "production",
                "DATABASE_URL": "*********************************************/sora_production",
                "CELERY_BROKER_URL": "redis://redis:6379/0",
                "CELERY_RESULT_BACKEND": "redis://redis:6379/0",
                "USE_MOCK_VEO3": "true",
                "GOOGLE_PROJECT_ID": "docker-env-test",
                "VEO3_TIMEOUT": "600",
                "CREDENTIAL_RETRY_ATTEMPTS": "5",
            }
        )

        yield

        # Restore environment
        os.environ.clear()
        os.environ.update(original_env)

    def test_docker_environment_detection(self, mock_docker_environment):
        """Test F4 Docker environment detection."""
        # Test deployment type detection
        deployment_type = EnvironmentVariableManager.detect_deployment_type()
        assert deployment_type == "docker"

        # Test environment configuration
        env_config = EnvironmentVariableManager.get_environment_config()
        assert env_config.deployment_type == "docker"
        assert env_config.flask_env == "production"

    def test_docker_environment_variable_propagation(self, mock_docker_environment):
        """Test F4 environment variable propagation in Docker."""
        # Test ConfigurationService can access Docker environment variables
        project_id = ConfigurationService.get("GOOGLE_PROJECT_ID")
        assert project_id == "docker-env-test"

        timeout = ConfigurationService.get_int("VEO3_TIMEOUT", 300)
        assert timeout == 600

        use_mock = ConfigurationService.get_bool("USE_MOCK_VEO3", False)
        assert use_mock is True

        # Test Docker-specific URLs
        database_url = ConfigurationService.get("DATABASE_URL")
        assert "postgres:5432" in database_url  # Docker service name

        celery_url = ConfigurationService.get("CELERY_BROKER_URL")
        assert "redis:6379" in celery_url  # Docker service name

    def test_docker_provider_variable_validation(self, mock_docker_environment):
        """Test F4 provider variable validation in Docker environment."""
        # Test Google Veo3 provider validation
        validation = EnvironmentVariableManager.validate_provider_configuration(
            "google_veo3"
        )

        assert validation["provider"] == "google_veo3"
        assert validation["valid"] is True
        assert validation["deployment_type"] == "docker"
        assert validation["mock_mode"] is True

        # Should have required variables available
        available_vars = validation["available_variables"]
        assert "GOOGLE_PROJECT_ID" in available_vars
        assert "USE_MOCK_VEO3" in available_vars
        assert "VEO3_TIMEOUT" in available_vars

    def test_docker_environment_consistency(self, mock_docker_environment):
        """Test F4 environment consistency validation in Docker."""
        validation = EnvironmentVariableManager.validate_environment_consistency()

        assert validation["deployment_type"] == "docker"
        assert validation["use_mock_veo"] is True

        # Docker environment should be consistent
        assert validation["environment_consistent"] is True

        # Should have minimal warnings for proper Docker setup
        warnings = validation.get("warnings", [])
        # Filter out expected Docker warnings
        significant_warnings = [w for w in warnings if "production" not in w.lower()]
        assert len(significant_warnings) == 0


class TestF4DockerComposeIntegration:
    """Test F4 integration with Docker Compose configuration."""

    def test_docker_compose_environment_variables(self):
        """Test F4 environment variables in Docker Compose context."""
        # Simulate docker-compose environment variable loading
        compose_env = {
            "USE_MOCK_VEO": "true",
            "USE_MOCK_VEO3": "true",
            "DEFAULT_PROVIDER": "azure_sora",
            "GOOGLE_PROJECT_ID": "compose-test-project",
            "GOOGLE_APPLICATION_CREDENTIALS_FILE": "/run/secrets/google_service_account",
            "VEO3_TIMEOUT": "300",
            "VEO3_MAX_RETRIES": "3",
            "CREDENTIAL_RETRY_ATTEMPTS": "3",
            "CREDENTIAL_TIMEOUT": "30",
            "DEPLOYMENT_TYPE": "docker",
        }

        # Store original environment
        original_env = dict(os.environ)

        try:
            # Apply compose environment
            os.environ.update(compose_env)

            # Test F4 recognizes compose configuration
            deployment_type = EnvironmentVariableManager.detect_deployment_type()
            assert deployment_type == "docker"

            # Test provider variables are available
            google_vars = EnvironmentVariableManager.get_provider_variables(
                "google_veo3"
            )
            for var in ["USE_MOCK_VEO3", "GOOGLE_PROJECT_ID", "VEO3_TIMEOUT"]:
                assert var in google_vars
                assert os.getenv(var) is not None

            # Test environment summary includes compose configuration
            summary = EnvironmentVariableManager.get_environment_summary()
            assert summary["deployment_type"] == "docker"

            provider_status = summary["provider_status"]["google_veo3"]
            assert provider_status["valid"] is True
            assert provider_status["mock_mode"] is True

        finally:
            # Restore environment
            os.environ.clear()
            os.environ.update(original_env)

    def test_docker_compose_secrets_configuration(self):
        """Test F4 Docker Compose secrets configuration."""
        # Test that F4 expects correct Docker secrets path
        expected_secrets_path = "/run/secrets/google_service_account"

        # Test environment variable management recognizes secrets
        google_vars = EnvironmentVariableManager.get_provider_variables("google_veo3")
        assert "GOOGLE_APPLICATION_CREDENTIALS_FILE" in google_vars

        # Test with compose-style secrets environment
        original_env = dict(os.environ)

        try:
            os.environ.update(
                {
                    "GOOGLE_APPLICATION_CREDENTIALS_FILE": expected_secrets_path,
                    "DEPLOYMENT_TYPE": "docker",
                    "USE_MOCK_VEO3": "true",
                    "GOOGLE_PROJECT_ID": "compose-secrets-test",
                }
            )

            # Test credential manager recognizes compose secrets path
            manager = GoogleCloudCredentialManager()

            # Even if file doesn't exist, should recognize the configuration
            config = manager.create_veo3_config()
            assert "credential_type" in config

        finally:
            os.environ.clear()
            os.environ.update(original_env)


class TestF4DockerProductionSimulation:
    """Test F4 in production-like Docker deployment."""

    @pytest.fixture
    def production_docker_environment(self):
        """Setup production-like Docker environment."""
        original_env = dict(os.environ)

        # Simulate production Docker deployment
        production_env = {
            "DEPLOYMENT_TYPE": "docker",
            "FLASK_ENV": "production",
            "DEBUG": "false",
            "USE_MOCK_VEO": "false",  # Production should use real API
            "USE_MOCK_VEO3": "false",
            "DEFAULT_PROVIDER": "google_veo3",
            "GOOGLE_PROJECT_ID": "production-docker-project",
            "GOOGLE_APPLICATION_CREDENTIALS_FILE": "/run/secrets/google_service_account",
            "VEO3_TIMEOUT": "300",
            "VEO3_MAX_RETRIES": "3",
            "CREDENTIAL_RETRY_ATTEMPTS": "3",
            "RATE_LIMIT_ENABLED": "true",
            "CONFIG_VALIDATION_ENABLED": "true",
        }

        os.environ.clear()
        os.environ.update(production_env)

        yield

        # Restore environment
        os.environ.clear()
        os.environ.update(original_env)

    def test_production_docker_environment_detection(
        self, production_docker_environment
    ):
        """Test F4 production Docker environment detection."""
        # Should detect Docker deployment
        deployment_type = EnvironmentVariableManager.detect_deployment_type()
        assert deployment_type == "docker"

        # Should have production characteristics
        env_config = EnvironmentVariableManager.get_environment_config()
        assert env_config.deployment_type == "docker"
        assert env_config.flask_env == "production"
        assert env_config.debug_mode is False

    def test_production_docker_provider_configuration(
        self, production_docker_environment
    ):
        """Test F4 provider configuration in production Docker."""
        # Test Google Veo3 configuration for production
        validation = EnvironmentVariableManager.validate_provider_configuration(
            "google_veo3"
        )

        assert validation["provider"] == "google_veo3"
        assert validation["deployment_type"] == "docker"
        assert validation["mock_mode"] is False  # Production should not use mock

        # Real API requires proper credentials
        if not validation["valid"]:
            # Should indicate missing credentials for real API
            error = validation.get("error", "")
            assert "credential" in error.lower() or "project" in error.lower()

    def test_production_docker_environment_validation(
        self, production_docker_environment
    ):
        """Test F4 environment validation in production Docker."""
        validation = EnvironmentVariableManager.validate_environment_consistency()

        assert validation["deployment_type"] == "docker"
        assert validation["use_mock_veo"] is False  # Production setting
        assert validation["debug_mode"] is False  # Production setting

        # Check for production warnings
        warnings = validation.get("warnings", [])
        recommendations = validation.get("recommendations", [])

        # Should not warn about production settings being enabled
        production_warnings = [w for w in warnings if "production" in w.lower()]
        assert len(production_warnings) == 0  # No production warnings in production

    def test_production_docker_credential_requirements(
        self, production_docker_environment
    ):
        """Test F4 credential requirements in production Docker."""
        # Production Docker should require real credentials
        manager = GoogleCloudCredentialManager()

        try:
            config = manager.create_veo3_config()

            # Should indicate real API configuration
            assert config["use_mock"] is False
            assert config["project_id"] == "production-docker-project"

            # Should attempt real credential detection
            if "credential_type" in config:
                assert config["credential_type"] in [
                    "service_account",
                    "oauth",
                    "default",
                    "compute_engine",
                    "environment_fallback",
                ]

        except Exception as e:
            # Credential loading might fail in test environment
            # but should attempt real credential detection
            error_msg = str(e).lower()
            # Should be about missing real credentials, not F4 integration
            assert "f4" not in error_msg


class TestF4DockerPerformanceAndMonitoring:
    """Test F4 performance and monitoring in Docker environment."""

    def test_docker_credential_loading_performance(self):
        """Test F4 credential loading performance in Docker."""
        # Setup Docker environment
        original_env = dict(os.environ)

        try:
            os.environ.update(
                {
                    "DEPLOYMENT_TYPE": "docker",
                    "GOOGLE_PROJECT_ID": "performance-test",
                    "USE_MOCK_VEO3": "true",
                    "CREDENTIAL_CACHE_TTL": "3600",
                }
            )

            # Test credential manager performance
            manager = GoogleCloudCredentialManager()

            # First call should establish baseline
            start_time = time.time()
            config1 = manager.create_veo3_config()
            first_call_time = time.time() - start_time

            # Second call should benefit from caching
            start_time = time.time()
            config2 = manager.create_veo3_config()
            second_call_time = time.time() - start_time

            # Verify caching improves performance
            assert config1["project_id"] == config2["project_id"]
            assert second_call_time <= first_call_time  # Cache should be same or faster

            # Test performance metrics
            metrics = manager.get_performance_metrics()
            assert "detection_attempts" in metrics
            assert "cache_hits" in metrics

        finally:
            os.environ.clear()
            os.environ.update(original_env)

    def test_docker_environment_monitoring(self):
        """Test F4 environment monitoring in Docker."""
        original_env = dict(os.environ)

        try:
            # Setup Docker monitoring environment
            os.environ.update(
                {
                    "DEPLOYMENT_TYPE": "docker",
                    "GOOGLE_PROJECT_ID": "monitoring-test",
                    "USE_MOCK_VEO3": "true",
                    "VEO3_TIMEOUT": "300",
                    "CREDENTIAL_DEBUG_ENABLED": "true",
                }
            )

            # Test environment summary for monitoring
            summary = EnvironmentVariableManager.get_environment_summary()

            # Should include monitoring information
            assert summary["deployment_type"] == "docker"
            assert "provider_status" in summary
            assert "total_variables" in summary

            # Test provider monitoring
            google_status = summary["provider_status"]["google_veo3"]
            assert "valid" in google_status
            assert "deployment_type" in google_status

            # Test variable counts for monitoring
            total_vars = summary["total_variables"]
            assert "google_veo3" in total_vars
            assert total_vars["total"] > 0

        finally:
            os.environ.clear()
            os.environ.update(original_env)


# Module-level Docker integration test functions
def test_f4_docker_module_compatibility():
    """Test F4 Docker module compatibility and integration."""
    # Test that F4 modules work in Docker-like environment
    original_env = dict(os.environ)

    try:
        # Simulate Docker environment
        os.environ.update(
            {
                "DEPLOYMENT_TYPE": "docker",
                "DOCKER_CONTAINER": "test-container",
                "HOSTNAME": "docker-hostname-123",
            }
        )

        # Test deployment detection
        deployment_type = EnvironmentVariableManager.detect_deployment_type()
        assert deployment_type == "docker"

        # Test credential manager works in Docker context
        manager = get_credential_manager()
        assert hasattr(manager, "create_veo3_config")

        # Test environment variable management
        google_vars = EnvironmentVariableManager.get_provider_variables("google_veo3")
        assert "GOOGLE_APPLICATION_CREDENTIALS_FILE" in google_vars

    finally:
        os.environ.clear()
        os.environ.update(original_env)


@pytest.mark.asyncio
async def test_f4_docker_end_to_end_workflow():
    """End-to-end Docker workflow test for F4."""
    original_env = dict(os.environ)

    try:
        # Setup complete Docker environment
        docker_env = {
            "DEPLOYMENT_TYPE": "docker",
            "FLASK_ENV": "production",
            "USE_MOCK_VEO3": "true",
            "GOOGLE_PROJECT_ID": "docker-e2e-test",
            "GOOGLE_CLIENT_ID": "docker-client-id",
            "GOOGLE_CLIENT_SECRET": "docker-secret",
            "VEO3_TIMEOUT": "450",
            "DEFAULT_PROVIDER": "google_veo3",
            "DATABASE_URL": "************************************/db",
            "CELERY_BROKER_URL": "redis://redis:6379/0",
        }

        os.environ.clear()
        os.environ.update(docker_env)

        # Test E2E Docker workflow

        # 1. Environment detection
        deployment_type = EnvironmentVariableManager.detect_deployment_type()
        assert deployment_type == "docker"

        # 2. Provider validation
        validation = EnvironmentVariableManager.validate_provider_configuration(
            "google_veo3"
        )
        assert validation["valid"] is True
        assert validation["deployment_type"] == "docker"
        assert validation["mock_mode"] is True

        # 3. Credential management
        manager = get_credential_manager()
        credentials = await manager.get_credentials()
        assert credentials.credential_type == "oauth"
        assert credentials.project_id == "docker-e2e-test"

        # 4. Configuration creation with Docker settings
        config = manager.create_veo3_config()
        assert config["project_id"] == "docker-e2e-test"
        assert config["timeout"] == 450
        assert config["use_mock"] is True

        # 5. Environment summary for Docker monitoring
        summary = EnvironmentVariableManager.get_environment_summary()
        assert summary["deployment_type"] == "docker"
        assert summary["provider_status"]["google_veo3"]["valid"] is True

    finally:
        os.environ.clear()
        os.environ.update(original_env)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
