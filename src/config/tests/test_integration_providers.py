"""Integration tests for F4 Environment Configuration module.

Tests cross-module integration, Docker environment compatibility, and
end-to-end provider configuration workflows.
"""

import os
import time
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from src.config.environment import (
    EnvironmentDetector,
    get_environment_summary,
)
from src.config.factory import ConfigurationFactory, ProviderConfigurationFactory
from src.config.veo3_settings import Veo3Settings, validate_veo3_environment


class TestProviderConfigurationIntegration:
    """Integration tests for provider configuration workflows."""

    def test_end_to_end_veo3_configuration_workflow(self):
        """Test complete Veo3 configuration workflow from environment to client config."""
        test_id = str(uuid4())[:8]
        test_env = {
            "GOOGLE_PROJECT_ID": f"test-project-{test_id}",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": f"test-client-{test_id}",
            "GOOGLE_CLIENT_SECRET": f"test-secret-{test_id}",
            "VEO3_TIMEOUT": "600",
            "VEO3_MODEL_VERSION": "veo3-preview",
            "DEFAULT_PROVIDER": "google_veo3",
        }

        with patch.dict(os.environ, test_env):
            # Step 1: Load settings from environment
            settings = Veo3Settings()
            assert settings.GOOGLE_PROJECT_ID == f"test-project-{test_id}"
            assert settings.USE_MOCK_VEO is True
            assert settings.VEO3_TIMEOUT == 600

            # Step 2: Check provider availability
            availability = settings.get_provider_availability()
            assert availability["google_veo3"] is True
            assert availability["azure_sora"] is True

            # Step 3: Create provider configuration via factory
            config = ConfigurationFactory.create_veo3_config()
            assert config.project_id == f"test-project-{test_id}"
            assert config.use_mock is True
            assert config.timeout == 600
            assert config.model_version == "veo3-preview"

            # Step 4: Validate configuration
            validation = settings.validate_provider_configuration("google_veo3")
            assert validation["valid"] is True
            assert len(validation["errors"]) == 0

    def test_provider_switching_integration(self):
        """Test dynamic provider switching based on availability."""
        # Start with Veo3 as default but unavailable
        test_env = {
            "DEFAULT_PROVIDER": "google_veo3",
            "USE_MOCK_VEO": "false",
            # No GOOGLE_PROJECT_ID - makes Veo3 unavailable
        }

        with patch.dict(os.environ, test_env):
            # Mock Azure configuration as available
            with patch.object(ConfigurationFactory, "get_azure_config") as mock_azure:
                mock_azure.return_value = {
                    "endpoint": "https://test.azure.com",
                    "api_key": "test-key",
                    "api_version": "preview",
                }

                # Get optimal provider config - should fallback to Azure
                optimal_config = (
                    ProviderConfigurationFactory.get_optimal_provider_config()
                )

                assert optimal_config["selected_provider"] == "azure_sora"
                assert optimal_config["provider_availability"]["google_veo3"] is False
                assert optimal_config["provider_availability"]["azure_sora"] is True

    def test_environment_aware_configuration_integration(self):
        """Test environment-aware configuration with ConfigurationService integration."""
        test_env = {
            "GOOGLE_PROJECT_ID": "integration-test-project",
            "FLASK_ENV": "production",
            "USE_MOCK_VEO": "false",
        }

        with patch.dict(os.environ, test_env):
            # Mock deployment type detection
            with patch(
                "src.config.factory.ConfigurationService._detect_deployment_type"
            ) as mock_detect:
                mock_detect.return_value = "production"

                # Create environment-aware config
                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )

                # Should have production-appropriate settings
                assert config.get("use_mock") is False  # Production should not use mock
                assert config.get("timeout") == 300  # Production timeout
                assert config.get("max_retries") == 3  # Production retries

    @patch("src.config.veo3_settings.ConfigurationService")
    def test_configuration_service_integration(self, mock_config_service):
        """Test integration with existing ConfigurationService."""
        # Mock ConfigurationService responses
        mock_config_service._ensure_initialized.return_value = None
        mock_config_service.get.side_effect = lambda key, default=None: {
            "AZURE_OPENAI_ENDPOINT": "https://integration.openai.azure.com",
            "AZURE_OPENAI_API_KEY": "integration-key",
            "AZURE_OPENAI_API_VERSION": "preview",
        }.get(key, default)

        # Test Azure configuration validation through Veo3Settings
        settings = Veo3Settings()
        azure_validation = settings.validate_provider_configuration("azure_sora")

        assert azure_validation["valid"] is True
        assert azure_validation["configuration"]["endpoint"] is True
        assert azure_validation["configuration"]["api_key"] is True
        mock_config_service.get.assert_called()


class TestDockerEnvironmentIntegration:
    """Integration tests for Docker environment compatibility."""

    @patch("src.config.environment.ConfigurationService")
    def test_docker_environment_detection_integration(self, mock_config_service):
        """Test Docker environment detection and configuration optimization."""
        # Mock Docker environment detection
        mock_config_service._detect_deployment_type.return_value = "docker"
        mock_config_service.get.return_value = "production"
        mock_config_service.get_bool.side_effect = [
            False,
            False,
            True,
            True,
        ]  # debug, testing, monitoring, rate_limit

        # Get environment info
        env_info = EnvironmentDetector.get_environment_info()

        assert env_info.deployment_type == "docker"
        assert env_info.security_level == "strict"  # Production security in Docker
        assert env_info.performance_profile == "standard"  # Docker performance profile

        # Test Docker-specific provider defaults
        assert env_info.provider_defaults["default_timeout"] == 600  # Docker timeout
        assert env_info.provider_defaults["max_retries"] == 5  # Docker retries

    def test_docker_provider_configuration_integration(self):
        """Test provider configuration optimization for Docker environment."""
        test_env = {
            "GOOGLE_PROJECT_ID": "docker-test-project",
            "USE_MOCK_VEO": "false",  # Real API in Docker
        }

        with patch.dict(os.environ, test_env):
            with patch(
                "src.config.factory.ConfigurationService._detect_deployment_type"
            ) as mock_detect:
                mock_detect.return_value = "docker"

                # Create Docker-optimized configuration
                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )

                # Should have Docker-appropriate optimizations
                assert config.get("timeout") == 600  # Longer timeout for Docker
                assert config.get("max_retries") == 5  # More retries for Docker

    @patch("os.path.exists")
    @patch("src.config.environment.ConfigurationService")
    def test_docker_container_detection(self, mock_config_service, mock_path_exists):
        """Test Docker container detection via /.dockerenv file."""
        # Mock /.dockerenv existence
        mock_path_exists.return_value = True
        mock_config_service.get.return_value = "development"
        mock_config_service.get_bool.return_value = False

        # Should detect Docker deployment type
        env_info = EnvironmentDetector.get_environment_info()

        # ConfigurationService._detect_deployment_type would be called
        # but we're testing the integration pattern
        mock_path_exists.assert_called_with("/.dockerenv")


class TestPerformanceIntegration:
    """Integration tests for performance optimization across modules."""

    def test_settings_loading_performance_integration(self):
        """Test settings loading performance meets targets."""
        test_env = {
            "GOOGLE_PROJECT_ID": "performance-test-project",
            "USE_MOCK_VEO": "true",
            "VEO3_TIMEOUT": "300",
        }

        with patch.dict(os.environ, test_env):
            # Measure settings loading time
            start_time = time.time()
            settings = Veo3Settings()
            load_time_ms = (time.time() - start_time) * 1000

            # Should meet performance target (<50ms)
            assert load_time_ms < 50, (
                f"Settings loading took {load_time_ms:.2f}ms, target is <50ms"
            )

            # Verify settings are loaded correctly
            assert settings.GOOGLE_PROJECT_ID == "performance-test-project"

    def test_factory_creation_performance_integration(self):
        """Test factory configuration creation performance."""
        test_env = {
            "GOOGLE_PROJECT_ID": "factory-performance-test",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            # Measure factory creation time
            start_time = time.time()
            config = ConfigurationFactory.create_veo3_config()
            creation_time_ms = (time.time() - start_time) * 1000

            # Should meet performance target (<10ms)
            assert creation_time_ms < 10, (
                f"Factory creation took {creation_time_ms:.2f}ms, target is <10ms"
            )

            # Verify configuration is created correctly
            assert config.project_id == "factory-performance-test"

    def test_validation_performance_integration(self):
        """Test configuration validation performance."""
        test_env = {
            "GOOGLE_PROJECT_ID": "validation-performance-test",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()

            # Measure validation time
            start_time = time.time()
            validation_result = settings.validate_provider_configuration("google_veo3")
            validation_time_ms = (time.time() - start_time) * 1000

            # Should meet performance target (<100ms)
            assert validation_time_ms < 100, (
                f"Validation took {validation_time_ms:.2f}ms, target is <100ms"
            )

            # Verify validation succeeded
            assert validation_result["valid"] is True


class TestEnvironmentValidationIntegration:
    """Integration tests for environment validation workflows."""

    def test_comprehensive_environment_validation_integration(self):
        """Test complete environment validation workflow."""
        test_env = {
            "GOOGLE_PROJECT_ID": "validation-integration-test",
            "USE_MOCK_VEO": "true",
            "DEFAULT_PROVIDER": "google_veo3",
            "FLASK_ENV": "development",
        }

        with patch.dict(os.environ, test_env):
            # Perform comprehensive validation
            validation_result = validate_veo3_environment()

            assert validation_result["valid"] is True
            assert "test_id" in validation_result
            assert validation_result["configuration"]["settings_loaded"] is True
            assert "provider_availability" in validation_result["configuration"]

            # Check provider availability was tested
            availability = validation_result["configuration"]["provider_availability"]
            assert "azure_sora" in availability
            assert "google_veo3" in availability

    def test_environment_summary_integration(self):
        """Test environment summary generation with all components."""
        test_env = {
            "GOOGLE_PROJECT_ID": "summary-integration-test",
            "FLASK_ENV": "development",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            with patch(
                "src.config.environment.ConfigurationService._detect_deployment_type"
            ) as mock_detect:
                mock_detect.return_value = "local"

                # Generate comprehensive environment summary
                summary = get_environment_summary()

                # Verify summary structure
                assert "test_id" in summary
                assert "environment" in summary
                assert "provider_defaults" in summary
                assert "compatibility" in summary
                assert "optimized_configs" in summary

                # Verify environment details
                assert summary["environment"]["deployment_type"] == "local"
                assert summary["environment"]["environment_name"] == "development"

                # Verify optimized configs were generated
                assert "veo3" in summary["optimized_configs"]
                assert "azure" in summary["optimized_configs"]

                # Verify compatibility check was performed
                assert "compatible" in summary["compatibility"]
                assert "warnings_count" in summary["compatibility"]


class TestErrorHandlingIntegration:
    """Integration tests for error handling across modules."""

    def test_missing_configuration_error_handling(self):
        """Test error handling when required configuration is missing."""
        # Clear environment to simulate missing configuration
        with patch.dict(os.environ, {}, clear=True):
            # Should handle missing project ID gracefully
            with pytest.raises(ValueError, match="GOOGLE_PROJECT_ID is required"):
                ConfigurationFactory.create_veo3_config()

    def test_invalid_provider_error_handling_integration(self):
        """Test error handling for invalid provider across modules."""
        # Test factory error handling
        with pytest.raises(ValueError, match="Unknown provider: invalid_provider"):
            ConfigurationFactory.create_provider_config("invalid_provider")

        # Test environment optimizer error handling
        with pytest.raises(ValueError, match="Unknown provider: invalid_provider"):
            ConfigurationFactory.get_provider_performance_config("invalid_provider")

    def test_configuration_validation_error_integration(self):
        """Test configuration validation error handling integration."""
        test_env = {
            "GOOGLE_PROJECT_ID": "ab",  # Too short, will cause validation error
        }

        with patch.dict(os.environ, test_env):
            # Pydantic validation should catch invalid project ID
            with pytest.raises(Exception):  # ValidationError from Pydantic
                Veo3Settings()


class TestBackwardCompatibilityIntegration:
    """Integration tests for backward compatibility with existing system."""

    def test_existing_azure_configuration_compatibility(self):
        """Test that existing Azure configuration still works."""
        # Mock existing Azure configuration
        with patch.object(ConfigurationFactory, "get_azure_config") as mock_azure:
            mock_azure.return_value = {
                "endpoint": "https://existing.azure.com",
                "api_key": "existing-key",
                "api_version": "preview",
                "deployment_name": "sora",
            }

            # Should still work with existing interface
            azure_config = ConfigurationFactory.get_azure_config()

            assert azure_config["endpoint"] == "https://existing.azure.com"
            assert azure_config["api_key"] == "existing-key"
            assert azure_config["deployment_name"] == "sora"

    def test_existing_configuration_factory_methods_compatibility(self):
        """Test that existing ConfigurationFactory methods still work."""
        # Test get_base_config still works
        with patch("src.config.factory.get_config") as mock_get_config:
            mock_base_config = MagicMock()
            mock_get_config.return_value = mock_base_config

            base_config = ConfigurationFactory.get_base_config()
            assert base_config is mock_base_config

        # Test clear_cache still works
        result = ConfigurationFactory.clear_cache()
        assert result is None  # Should return None as documented

    def test_existing_video_config_integration(self):
        """Test integration with existing video configuration."""
        with patch("src.config.factory.VideoGenerationConfig") as mock_video_config:
            with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
                mock_base_config = MagicMock()
                mock_get_base.return_value = mock_base_config
                mock_video_config.from_config.return_value = MagicMock()

                # Should still work with existing video config
                video_config = ConfigurationFactory.get_video_config()

                mock_video_config.from_config.assert_called_once_with(mock_base_config)


class TestConcurrencyIntegration:
    """Integration tests for concurrent access patterns."""

    def test_cached_settings_thread_safety(self):
        """Test that cached settings work safely with concurrent access."""
        from src.config.veo3_settings import get_cached_veo3_settings

        test_env = {
            "GOOGLE_PROJECT_ID": "concurrency-test-project",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            # Clear cache first
            get_cached_veo3_settings.cache_clear()

            # Multiple calls should return same cached instance
            settings1 = get_cached_veo3_settings()
            settings2 = get_cached_veo3_settings()

            assert settings1 is settings2
            assert settings1.GOOGLE_PROJECT_ID == "concurrency-test-project"

    def test_configuration_service_integration_stability(self):
        """Test ConfigurationService integration remains stable under load."""
        test_env = {
            "GOOGLE_PROJECT_ID": "stability-test-project",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            # Multiple configuration creations should be stable
            configs = []
            for i in range(10):
                config = ConfigurationFactory.create_veo3_config()
                configs.append(config)

            # All configurations should have same project_id
            for config in configs:
                assert config.project_id == "stability-test-project"
                assert config.use_mock is True


class TestRealWorldScenarios:
    """Integration tests for real-world usage scenarios."""

    def test_development_to_production_migration_scenario(self):
        """Test configuration migration from development to production."""
        # Start with development configuration
        dev_env = {
            "GOOGLE_PROJECT_ID": "dev-project-123",
            "USE_MOCK_VEO": "true",
            "FLASK_ENV": "development",
            "DEFAULT_PROVIDER": "google_veo3",
        }

        with patch.dict(os.environ, dev_env):
            dev_config = ConfigurationFactory.create_veo3_config()
            assert dev_config.use_mock is True

            # Simulate production migration
            prod_overrides = {
                "use_mock": False,
                "project_id": "prod-project-456",
                "timeout": 300,
                "max_retries": 3,
            }

            prod_config = ConfigurationFactory.create_veo3_config(**prod_overrides)
            assert prod_config.use_mock is False
            assert prod_config.project_id == "prod-project-456"
            assert prod_config.timeout == 300

    def test_multi_provider_deployment_scenario(self):
        """Test scenario with multiple providers available."""
        test_env = {
            "GOOGLE_PROJECT_ID": "multi-provider-project",
            "USE_MOCK_VEO": "false",
            "GOOGLE_CLIENT_ID": "test-client",
            "DEFAULT_PROVIDER": "google_veo3",
        }

        with patch.dict(os.environ, test_env):
            # Mock Azure availability
            with patch.object(ConfigurationFactory, "get_azure_config") as mock_azure:
                mock_azure.return_value = {
                    "endpoint": "https://multi.azure.com",
                    "api_key": "multi-key",
                }

                # Check both providers are available
                availability = ConfigurationFactory.get_provider_availability()
                assert availability["google_veo3"] is True
                assert availability["azure_sora"] is True

                # Should select default provider
                default_provider = ConfigurationFactory.get_default_provider()
                assert default_provider == "google_veo3"

                # Can create configuration for either provider
                veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
                azure_config = ConfigurationFactory.create_provider_config("azure_sora")

                assert veo3_config["project_id"] == "multi-provider-project"
                assert azure_config["endpoint"] == "https://multi.azure.com"
