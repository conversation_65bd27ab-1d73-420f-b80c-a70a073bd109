"""Comprehensive F4 Environment Configuration Quality Assurance Testing.

This module implements the complete Quality & Validation Specialist testing framework
for F4 Environment Configuration with:
- 95+ test scenarios covering unit, integration, and system testing
- Performance validation with <10ms config creation targets
- Automated quality gates with 100% pass rate enforcement
- Environment variable management and Docker secrets integration
- Cross-module integration testing with F2/C2/I1 interaction validation
- Security testing for credential handling and configuration validation

Test Categories:
1. Configuration Factory Testing (Unit)
2. Environment Detection Testing (Integration)
3. Provider Configuration Testing (System)
4. Performance Validation Testing (Benchmarking)
5. Quality Gates Testing (Validation)
6. Security Testing (Security)
7. Cross-Module Integration Testing (E2E)
"""

import os
import tempfile
import time
from pathlib import Path
from typing import Any, Dict
from unittest.mock import MagicMock, mock_open, patch

import pytest

from src.config.environments import (
    BaseConfig,
    DevelopmentConfig,
    ProductionConfig,
    TestingConfig,
)
from src.config.factory import ConfigurationFactory, ProviderConfigurationFactory
from src.config.service import ConfigurationService
from src.config.veo3_settings import (
    Veo3ProviderConfig,
    Veo3Settings,
    get_cached_veo3_settings,
)


class TestF4ConfigurationFactoryUnit:
    """Unit tests for F4 Configuration Factory with comprehensive coverage."""

    def setup_method(self):
        """Setup for each test method with clean environment."""
        # Clear configuration cache
        ConfigurationFactory.clear_cache()
        ConfigurationService.clear_cache()
        ConfigurationService.reload()

        # Clear any cached Veo3Settings
        get_cached_veo3_settings.cache_clear()

    def teardown_method(self):
        """Cleanup after each test method."""
        # Ensure clean state for next test
        ConfigurationFactory.clear_cache()
        ConfigurationService.clear_cache()
        get_cached_veo3_settings.cache_clear()

    def test_configuration_factory_base_config_creation(self):
        """Test F4.01: Base configuration creation with environment detection."""
        start_time = time.perf_counter()

        config = ConfigurationFactory.get_base_config()

        creation_time = (time.perf_counter() - start_time) * 1000

        # Validate configuration created successfully
        assert config is not None
        assert isinstance(config, BaseConfig)

        # Validate performance target: <10ms
        assert creation_time < 10, (
            f"Config creation took {creation_time:.2f}ms, target: <10ms"
        )

    def test_configuration_factory_environment_specific_configs(self):
        """Test F4.02: Environment-specific configuration creation."""
        environments = ["development", "testing", "production"]
        expected_types = [DevelopmentConfig, TestingConfig, ProductionConfig]

        for env, expected_type in zip(environments, expected_types):
            start_time = time.perf_counter()

            config = ConfigurationFactory.get_base_config(env)

            creation_time = (time.perf_counter() - start_time) * 1000

            # Validate correct configuration type
            assert isinstance(config, expected_type), (
                f"Expected {expected_type}, got {type(config)}"
            )

            # Validate performance target
            assert creation_time < 10, (
                f"Config creation for {env} took {creation_time:.2f}ms"
            )

    def test_configuration_factory_video_config_creation(self):
        """Test F4.03: Video configuration creation with validation."""
        start_time = time.perf_counter()

        video_config = ConfigurationFactory.get_video_config()

        creation_time = (time.perf_counter() - start_time) * 1000

        # Validate video configuration
        assert video_config is not None
        assert hasattr(video_config, "default_width")
        assert hasattr(video_config, "default_height")
        assert hasattr(video_config, "default_duration")

        # Validate performance target
        assert creation_time < 10, f"Video config creation took {creation_time:.2f}ms"

    def test_configuration_factory_generation_params_defaults(self):
        """Test F4.04: Generation parameters default creation."""
        defaults = ConfigurationFactory.create_generation_params_defaults()

        # Validate required fields
        required_fields = ["width", "height", "duration", "model"]
        for field in required_fields:
            assert field in defaults, f"Missing required field: {field}"

        # Validate value types
        assert isinstance(defaults["width"], int)
        assert isinstance(defaults["height"], int)
        assert isinstance(defaults["duration"], int)
        assert isinstance(defaults["model"], str)

    def test_configuration_factory_azure_config_creation(self):
        """Test F4.05: Azure configuration creation with validation."""
        mock_config = MagicMock()
        mock_config.AZURE_OPENAI_ENDPOINT = "https://test.openai.azure.com"
        mock_config.AZURE_OPENAI_API_KEY = "test-key-123"
        mock_config.AZURE_OPENAI_API_VERSION = "2025-04-01-preview"
        mock_config.AZURE_OPENAI_DEPLOYMENT_NAME = "sora"

        with patch.object(
            ConfigurationFactory, "get_base_config", return_value=mock_config
        ):
            start_time = time.perf_counter()

            azure_config = ConfigurationFactory.get_azure_config()

            creation_time = (time.perf_counter() - start_time) * 1000

            # Validate Azure configuration structure
            expected_keys = ["endpoint", "api_key", "api_version", "deployment_name"]
            for key in expected_keys:
                assert key in azure_config, f"Missing Azure config key: {key}"

            # Validate values
            assert azure_config["endpoint"] == "https://test.openai.azure.com"
            assert azure_config["api_key"] == "test-key-123"
            assert azure_config["api_version"] == "2025-04-01-preview"
            assert azure_config["deployment_name"] == "sora"

            # Validate performance target
            assert creation_time < 10, (
                f"Azure config creation took {creation_time:.2f}ms"
            )

    def test_configuration_factory_app_config_creation(self):
        """Test F4.06: Flask application configuration creation."""
        mock_config = MagicMock()
        mock_config.SECRET_KEY = "test-secret-key"
        mock_config.UPLOAD_FOLDER = "/tmp/uploads"
        mock_config.MAX_CONTENT_LENGTH = 104857600
        mock_config.SQLALCHEMY_TRACK_MODIFICATIONS = False
        mock_config.get_database_url.return_value = "sqlite:///test.db"

        with patch.object(
            ConfigurationFactory, "get_base_config", return_value=mock_config
        ):
            app_config = ConfigurationFactory.get_app_config()

            # Validate Flask configuration structure
            expected_keys = [
                "SECRET_KEY",
                "UPLOAD_FOLDER",
                "MAX_CONTENT_LENGTH",
                "SQLALCHEMY_DATABASE_URI",
                "SQLALCHEMY_TRACK_MODIFICATIONS",
            ]
            for key in expected_keys:
                assert key in app_config, f"Missing Flask config key: {key}"

    def test_configuration_factory_veo3_settings_creation(self):
        """Test F4.07: Veo3 settings creation with caching."""
        start_time = time.perf_counter()

        veo3_settings = ConfigurationFactory.get_veo3_settings()

        creation_time = (time.perf_counter() - start_time) * 1000

        # Validate Veo3 settings
        assert veo3_settings is not None
        assert isinstance(veo3_settings, Veo3Settings)

        # Validate performance target
        assert creation_time < 50, f"Veo3 settings creation took {creation_time:.2f}ms"

        # Test caching - second call should be faster
        start_time = time.perf_counter()
        veo3_settings_cached = ConfigurationFactory.get_veo3_settings()
        cache_time = (time.perf_counter() - start_time) * 1000

        assert veo3_settings_cached is veo3_settings  # Same instance due to caching
        assert cache_time < 5, f"Cached settings access took {cache_time:.2f}ms"


class TestF4EnvironmentDetection:
    """Integration tests for F4 environment detection and deployment type recognition."""

    def setup_method(self):
        """Setup for environment detection tests."""
        ConfigurationService.reload()

    def test_environment_detection_local_development(self):
        """Test F4.08: Local development environment detection."""
        test_env = {
            "FLASK_ENV": "development",
            "DEPLOYMENT_TYPE": "",
            "DATABASE_URL": "",
        }

        with patch.dict(os.environ, test_env, clear=False):
            with patch("os.path.exists") as mock_exists:
                mock_exists.return_value = False  # No .dockerenv file

                start_time = time.perf_counter()

                deployment_type = ConfigurationService._detect_deployment_type()

                detection_time = (time.perf_counter() - start_time) * 1000

                # Validate detection result
                assert deployment_type == "local"

                # Validate performance target: <5ms
                assert detection_time < 5, (
                    f"Environment detection took {detection_time:.2f}ms"
                )

    def test_environment_detection_docker_deployment(self):
        """Test F4.09: Docker deployment environment detection."""
        test_env = {"DEPLOYMENT_TYPE": "", "FLASK_ENV": "production"}

        with patch.dict(os.environ, test_env, clear=False):
            with patch("os.path.exists") as mock_exists:
                mock_exists.return_value = True  # .dockerenv file exists

                start_time = time.perf_counter()

                deployment_type = ConfigurationService._detect_deployment_type()

                detection_time = (time.perf_counter() - start_time) * 1000

                # Validate detection result
                assert deployment_type == "docker"

                # Validate performance target
                assert detection_time < 5, (
                    f"Environment detection took {detection_time:.2f}ms"
                )

    def test_environment_detection_production_deployment(self):
        """Test F4.10: Production environment detection."""
        test_env = {"DEPLOYMENT_TYPE": "", "FLASK_ENV": "production"}

        with patch.dict(os.environ, test_env, clear=False):
            with patch("os.path.exists") as mock_exists:
                mock_exists.return_value = False  # No .dockerenv file

                start_time = time.perf_counter()

                deployment_type = ConfigurationService._detect_deployment_type()

                detection_time = (time.perf_counter() - start_time) * 1000

                # Validate detection result
                assert deployment_type == "production"

                # Validate performance target
                assert detection_time < 5, (
                    f"Environment detection took {detection_time:.2f}ms"
                )

    def test_environment_detection_explicit_override(self):
        """Test F4.11: Explicit deployment type override."""
        for deployment_type in ["local", "docker", "production"]:
            test_env = {"DEPLOYMENT_TYPE": deployment_type}

            with patch.dict(os.environ, test_env, clear=False):
                detected_type = ConfigurationService._detect_deployment_type()

                assert detected_type == deployment_type

    def test_environment_variable_parsing_matrix(self):
        """Test F4.12: Environment variable parsing for various types."""
        test_cases = [
            # (env_value, expected_value, target_type)
            ("true", True, bool),
            ("True", True, bool),
            ("1", True, bool),
            ("yes", True, bool),
            ("false", False, bool),
            ("False", False, bool),
            ("0", False, bool),
            ("no", False, bool),
            ("", False, bool),
            ("123", 123, int),
            ("0", 0, int),
            ("-456", -456, int),
            ("3.14", 3.14, float),
            ("-2.5", -2.5, float),
            ("hello", "hello", str),
            ("", "", str),
        ]

        for env_value, expected_value, target_type in test_cases:
            with patch.dict(os.environ, {"TEST_VAR": env_value}, clear=False):
                result = ConfigurationService.get("TEST_VAR", None, target_type)

                assert result == expected_value, (
                    f"Failed parsing '{env_value}' as {target_type.__name__}"
                )
                assert type(result) == target_type


class TestF4ProviderConfiguration:
    """System tests for F4 provider configuration and switching."""

    def setup_method(self):
        """Setup for provider configuration tests."""
        ConfigurationFactory.clear_cache()
        get_cached_veo3_settings.cache_clear()

    def test_veo3_provider_config_creation_with_environment(self):
        """Test F4.13: Veo3 provider configuration creation with environment variables."""
        test_env = {
            "GOOGLE_PROJECT_ID": "test-project-f4-123",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "test-client-id",
            "GOOGLE_CLIENT_SECRET": "test-client-secret",
            "VEO3_TIMEOUT": "600",
            "VEO3_MAX_RETRIES": "5",
            "VEO3_RETRY_DELAY": "3",
            "VEO3_GENERATION_TIMEOUT": "1800",
            "VEO3_MODEL_VERSION": "veo-3.0-generate-preview",
            "VEO3_RATE_LIMIT_RPM": "50",
        }

        with patch.dict(os.environ, test_env, clear=True):
            start_time = time.perf_counter()

            config = ConfigurationFactory.create_veo3_config()

            creation_time = (time.perf_counter() - start_time) * 1000

            # Validate configuration values
            assert isinstance(config, Veo3ProviderConfig)
            assert config.project_id == "test-project-f4-123"
            assert config.use_mock is True
            assert config.client_id == "test-client-id"
            assert config.client_secret == "test-client-secret"
            assert config.timeout == 600
            assert config.max_retries == 5
            assert config.retry_delay == 3
            assert config.generation_timeout == 1800
            assert config.model_version == "veo-3.0-generate-preview"
            assert config.rate_limit_rpm == 50

            # Validate performance target
            assert creation_time < 10, (
                f"Veo3 config creation took {creation_time:.2f}ms"
            )

    def test_veo3_provider_config_creation_with_overrides(self):
        """Test F4.14: Veo3 provider configuration with explicit overrides."""
        test_env = {
            "GOOGLE_PROJECT_ID": "env-project-id",
            "USE_MOCK_VEO": "false",
            "VEO3_TIMEOUT": "300",
        }

        with patch.dict(os.environ, test_env, clear=True):
            config = ConfigurationFactory.create_veo3_config(
                project_id="override-project-id",
                use_mock=True,
                timeout=900,
                max_retries=7,
            )

            # Validate overrides take precedence
            assert config.project_id == "override-project-id"
            assert config.use_mock is True
            assert config.timeout == 900
            assert config.max_retries == 7

    def test_veo3_provider_config_missing_required_fields(self):
        """Test F4.15: Veo3 provider configuration error handling for missing fields."""
        # Clear environment to ensure no GOOGLE_PROJECT_ID
        with patch.dict(os.environ, {}, clear=True):
            # Override the default value in Veo3Settings to test missing project ID
            with patch.object(Veo3Settings, "GOOGLE_PROJECT_ID", None):
                with pytest.raises(ValueError, match="GOOGLE_PROJECT_ID is required"):
                    ConfigurationFactory.create_veo3_config()

    def test_provider_configuration_unified_interface(self):
        """Test F4.16: Unified provider configuration interface."""
        # Test Azure Sora provider
        mock_azure_config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-key",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        with patch.object(
            ConfigurationFactory, "get_azure_config", return_value=mock_azure_config
        ):
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")

            assert azure_config == mock_azure_config

        # Test Google Veo3 provider
        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env, clear=True):
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")

            assert isinstance(veo3_config, dict)
            assert "project_id" in veo3_config
            assert veo3_config["project_id"] == "test-project-123"

    def test_provider_switching_performance(self):
        """Test F4.17: Provider switching performance validation."""
        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env, clear=True):
            # Test Azure to Veo3 switching
            start_time = time.perf_counter()

            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")

            switching_time = (time.perf_counter() - start_time) * 1000

            # Validate both configurations created
            assert azure_config is not None
            assert veo3_config is not None

            # Validate performance target: <50ms for switching
            assert switching_time < 50, (
                f"Provider switching took {switching_time:.2f}ms"
            )

    def test_provider_availability_detection(self):
        """Test F4.18: Provider availability detection."""
        start_time = time.perf_counter()

        availability = ConfigurationFactory.get_provider_availability()

        detection_time = (time.perf_counter() - start_time) * 1000

        # Validate availability structure
        assert isinstance(availability, dict)
        assert "azure_sora" in availability
        assert "google_veo3" in availability
        assert isinstance(availability["azure_sora"], bool)
        assert isinstance(availability["google_veo3"], bool)

        # Validate performance target
        assert detection_time < 100, (
            f"Provider availability detection took {detection_time:.2f}ms"
        )


class TestF4PerformanceValidation:
    """Performance validation tests for F4 configuration operations."""

    def test_configuration_creation_performance_matrix(self):
        """Test F4.19: Comprehensive configuration creation performance matrix."""
        performance_targets = {
            "base_config": 10,  # <10ms
            "video_config": 10,  # <10ms
            "azure_config": 10,  # <10ms
            "veo3_settings": 50,  # <50ms
            "environment_detection": 5,  # <5ms
        }

        test_env = {"GOOGLE_PROJECT_ID": "perf-test-project"}

        with patch.dict(os.environ, test_env, clear=True):
            results = {}

            # Test base configuration creation
            start_time = time.perf_counter()
            ConfigurationFactory.get_base_config()
            results["base_config"] = (time.perf_counter() - start_time) * 1000

            # Test video configuration creation
            start_time = time.perf_counter()
            ConfigurationFactory.get_video_config()
            results["video_config"] = (time.perf_counter() - start_time) * 1000

            # Test Azure configuration creation
            start_time = time.perf_counter()
            ConfigurationFactory.get_azure_config()
            results["azure_config"] = (time.perf_counter() - start_time) * 1000

            # Test Veo3 settings creation
            start_time = time.perf_counter()
            ConfigurationFactory.get_veo3_settings()
            results["veo3_settings"] = (time.perf_counter() - start_time) * 1000

            # Test environment detection
            start_time = time.perf_counter()
            ConfigurationService._detect_deployment_type()
            results["environment_detection"] = (time.perf_counter() - start_time) * 1000

            # Validate all performance targets
            for operation, target_ms in performance_targets.items():
                actual_ms = results[operation]
                assert actual_ms < target_ms, (
                    f"{operation} took {actual_ms:.2f}ms, target: <{target_ms}ms"
                )

    def test_concurrent_configuration_access_performance(self):
        """Test F4.20: Concurrent configuration access performance."""
        import queue
        import threading

        test_env = {"GOOGLE_PROJECT_ID": "concurrent-test-project"}

        with patch.dict(os.environ, test_env, clear=True):
            results_queue = queue.Queue()
            num_threads = 10

            def config_access_worker():
                """Worker function for concurrent configuration access."""
                start_time = time.perf_counter()

                # Access multiple configuration types
                ConfigurationFactory.get_base_config()
                ConfigurationFactory.get_video_config()
                ConfigurationFactory.get_veo3_settings()

                access_time = (time.perf_counter() - start_time) * 1000
                results_queue.put(access_time)

            # Start concurrent threads
            threads = []
            overall_start = time.perf_counter()

            for _ in range(num_threads):
                thread = threading.Thread(target=config_access_worker)
                thread.start()
                threads.append(thread)

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            overall_time = (time.perf_counter() - overall_start) * 1000

            # Collect results
            access_times = []
            while not results_queue.empty():
                access_times.append(results_queue.get())

            # Validate concurrent access performance
            assert len(access_times) == num_threads
            assert all(time < 100 for time in access_times), (
                "Individual access should be <100ms"
            )
            assert overall_time < 500, (
                f"Concurrent access took {overall_time:.2f}ms, target: <500ms"
            )

    def test_configuration_caching_efficiency(self):
        """Test F4.21: Configuration caching efficiency validation."""
        test_env = {"GOOGLE_PROJECT_ID": "cache-test-project"}

        with patch.dict(os.environ, test_env, clear=True):
            # First access (cache miss)
            start_time = time.perf_counter()
            config1 = ConfigurationFactory.get_veo3_settings()
            first_access_time = (time.perf_counter() - start_time) * 1000

            # Second access (cache hit)
            start_time = time.perf_counter()
            config2 = ConfigurationFactory.get_veo3_settings()
            second_access_time = (time.perf_counter() - start_time) * 1000

            # Validate caching behavior
            assert config2 is config1  # Same instance due to caching
            assert second_access_time < 5, (
                f"Cached access took {second_access_time:.2f}ms"
            )
            assert second_access_time < first_access_time * 0.1  # At least 10x faster


class TestF4QualityGates:
    """Quality gates testing for F4 configuration validation."""

    def test_configuration_validation_quality_gate(self):
        """Test F4.22: Configuration validation quality gate enforcement."""
        # Test valid configuration passes quality gate
        test_env = {
            "GOOGLE_PROJECT_ID": "valid-project-123",
            "USE_MOCK_VEO": "true",
            "VEO3_TIMEOUT": "300",
        }

        with patch.dict(os.environ, test_env, clear=True):
            validation_result = ConfigurationFactory.validate_provider_configuration(
                "google_veo3"
            )

            # Quality gate: Configuration must be valid
            assert validation_result["valid"] is True
            assert len(validation_result["errors"]) == 0
            assert "test_id" in validation_result
            assert "configuration" in validation_result

    def test_provider_configuration_completeness_gate(self):
        """Test F4.23: Provider configuration completeness quality gate."""
        # Test Azure Sora configuration completeness
        azure_validation = ConfigurationFactory.validate_provider_configuration(
            "azure_sora"
        )

        # Quality gate: Must have proper structure
        required_keys = ["provider", "valid", "errors", "configuration", "test_id"]
        for key in required_keys:
            assert key in azure_validation, f"Missing validation key: {key}"

        # Test Google Veo3 configuration completeness
        test_env = {"GOOGLE_PROJECT_ID": "completeness-test-project"}

        with patch.dict(os.environ, test_env, clear=True):
            veo3_validation = ConfigurationFactory.validate_provider_configuration(
                "google_veo3"
            )

            for key in required_keys:
                assert key in veo3_validation, f"Missing validation key: {key}"

    def test_performance_quality_gate_enforcement(self):
        """Test F4.24: Performance quality gate enforcement."""
        test_env = {"GOOGLE_PROJECT_ID": "perf-gate-project"}

        with patch.dict(os.environ, test_env, clear=True):
            # Quality gate: All operations must meet performance targets
            operations = [
                ("base_config", lambda: ConfigurationFactory.get_base_config(), 10),
                ("video_config", lambda: ConfigurationFactory.get_video_config(), 10),
                ("azure_config", lambda: ConfigurationFactory.get_azure_config(), 10),
                (
                    "env_detection",
                    lambda: ConfigurationService._detect_deployment_type(),
                    5,
                ),
            ]

            for operation_name, operation_func, target_ms in operations:
                start_time = time.perf_counter()
                operation_func()
                execution_time = (time.perf_counter() - start_time) * 1000

                # Quality gate: Must meet performance target
                assert execution_time < target_ms, (
                    f"Quality gate failure: {operation_name} took {execution_time:.2f}ms, target: <{target_ms}ms"
                )

    def test_environment_variable_parsing_quality_gate(self):
        """Test F4.25: Environment variable parsing quality gate."""
        # Quality gate: All standard boolean representations must be parsed correctly
        boolean_test_cases = [
            ("true", True),
            ("True", True),
            ("TRUE", True),
            ("false", False),
            ("False", False),
            ("FALSE", False),
            ("1", True),
            ("0", False),
            ("yes", True),
            ("no", False),
            ("YES", True),
            ("NO", False),
            ("on", True),
            ("off", False),
            ("ON", True),
            ("OFF", False),
            ("enabled", True),
            ("disabled", False),
            ("ENABLED", True),
            ("DISABLED", False),
            ("", False),
        ]

        for env_value, expected_result in boolean_test_cases:
            with patch.dict(os.environ, {"TEST_BOOL": env_value}, clear=False):
                result = ConfigurationService.get_bool("TEST_BOOL", None)

                assert result == expected_result, (
                    f"Quality gate failure: '{env_value}' should parse to {expected_result}, got {result}"
                )


class TestF4SecurityTesting:
    """Security testing for F4 configuration credential handling."""

    def test_secret_environment_variable_handling(self):
        """Test F4.26: Secure handling of secret environment variables."""
        test_env = {
            "GOOGLE_PROJECT_ID": "security-test-project",
            "GOOGLE_CLIENT_SECRET": "super-secret-password-123",
        }

        with patch.dict(os.environ, test_env, clear=True):
            config = ConfigurationFactory.create_veo3_config()

            # Validate secret is properly handled
            assert config.client_secret == "super-secret-password-123"

            # Validate secret is excluded from serialization
            config_dict = config.to_dict()
            assert "client_secret" not in config_dict, (
                "Secret should not be in serialized form"
            )

    def test_docker_secrets_file_loading(self):
        """Test F4.27: Docker secrets file loading with _FILE pattern."""
        with tempfile.TemporaryDirectory() as temp_dir:
            secret_file = Path(temp_dir) / "client_secret"
            secret_file.write_text("file-based-secret-456")

            test_env = {
                "GOOGLE_PROJECT_ID": "docker-secrets-test",
                "GOOGLE_CLIENT_SECRET_FILE": str(secret_file),
            }

            with patch.dict(os.environ, test_env, clear=True):
                # Mock file reading in configuration system
                with patch(
                    "builtins.open", mock_open(read_data="file-based-secret-456")
                ):
                    with patch("os.path.exists", return_value=True):
                        # Test that file-based secrets are properly loaded
                        # Note: This tests the pattern, actual implementation may vary
                        settings = ConfigurationFactory.get_veo3_settings()

                        # Validate the configuration system can handle file-based secrets
                        assert settings is not None

    def test_configuration_validation_security_checks(self):
        """Test F4.28: Configuration validation includes security checks."""
        # Test invalid project ID patterns
        invalid_project_ids = [
            "../etc/passwd",  # Path traversal attempt
            "SELECT * FROM users",  # SQL injection attempt
            '<script>alert("xss")</script>',  # XSS attempt
            "project id with spaces",  # Invalid format
            "a" * 31,  # Too long
            "a" * 5,  # Too short
            "-invalid",  # Starts with hyphen
            "invalid-",  # Ends with hyphen
        ]

        for invalid_id in invalid_project_ids:
            test_env = {"GOOGLE_PROJECT_ID": invalid_id}

            with patch.dict(os.environ, test_env, clear=True):
                with pytest.raises(ValueError):
                    # Should raise validation error for invalid project ID
                    settings = Veo3Settings()

    def test_environment_variable_injection_protection(self):
        """Test F4.29: Protection against environment variable injection."""
        # Test potential injection patterns
        injection_attempts = [
            "normal_value; rm -rf /",  # Command injection
            "value\nMALICIOUS_VAR=bad",  # Environment variable injection
            "value\x00null_byte",  # Null byte injection
            "value$(whoami)",  # Command substitution
            "value`whoami`",  # Command substitution (backticks)
        ]

        for injection_value in injection_attempts:
            test_env = {
                "GOOGLE_PROJECT_ID": "safe-project-123",
                "VEO3_TIMEOUT": injection_value,
            }

            with patch.dict(os.environ, test_env, clear=True):
                # Configuration system should handle injection attempts safely
                try:
                    ConfigurationFactory.get_veo3_settings()
                    # If it doesn't crash, the injection was handled safely
                except (ValueError, TypeError):
                    # Expected for malformed values
                    pass
                except Exception as e:
                    # Unexpected errors might indicate security issues
                    pytest.fail(
                        f"Unexpected error with injection attempt '{injection_value}': {e}"
                    )


class TestF4CrossModuleIntegration:
    """Cross-module integration tests for F4 with F2/C2/I1 interaction validation."""

    def test_f2_provider_factory_integration(self):
        """Test F4.30: Integration with F2 Provider Interface Factory."""
        test_env = {"GOOGLE_PROJECT_ID": "f2-integration-test"}

        with patch.dict(os.environ, test_env, clear=True):
            # Test provider configuration for F2 integration
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")

            # Validate configurations are compatible with F2 Provider Factory
            assert isinstance(veo3_config, dict)
            assert isinstance(azure_config, dict)

            # Validate required keys for provider factory integration
            assert "project_id" in veo3_config or "endpoint" in azure_config

    def test_c2_job_queue_integration(self):
        """Test F4.31: Integration with C2 Job Queue management."""
        # Test configuration for job queue integration
        app_config = ConfigurationFactory.get_app_config()

        # Validate job queue compatible configuration
        assert "SQLALCHEMY_DATABASE_URI" in app_config
        assert app_config["SQLALCHEMY_DATABASE_URI"] is not None

    def test_i1_monitoring_integration(self):
        """Test F4.32: Integration with I1 monitoring and health checks."""
        # Test performance configuration for monitoring integration
        azure_perf = ConfigurationFactory.get_provider_performance_config("azure_sora")

        test_env = {"GOOGLE_PROJECT_ID": "i1-integration-test"}

        with patch.dict(os.environ, test_env, clear=True):
            veo3_perf = ConfigurationFactory.get_provider_performance_config(
                "google_veo3"
            )

            # Validate monitoring-compatible performance configuration
            for perf_config in [azure_perf, veo3_perf]:
                assert "timeouts" in perf_config
                assert "retry_config" in perf_config
                assert "targets" in perf_config

    def test_environment_aware_provider_selection(self):
        """Test F4.33: Environment-aware provider selection integration."""
        # Test different deployment environments
        environments = ["local", "docker", "production"]

        for env in environments:
            with patch(
                "src.config.service.ConfigurationService._detect_deployment_type",
                return_value=env,
            ):
                test_env = {"GOOGLE_PROJECT_ID": f"{env}-provider-test"}

                with patch.dict(os.environ, test_env, clear=True):
                    config = (
                        ProviderConfigurationFactory.create_environment_aware_config(
                            "google_veo3"
                        )
                    )

                    # Validate environment-specific configuration
                    assert config is not None
                    assert "project_id" in config

    def test_optimal_provider_configuration_workflow(self):
        """Test F4.34: End-to-end optimal provider configuration workflow."""
        test_env = {"GOOGLE_PROJECT_ID": "optimal-workflow-test"}

        with patch.dict(os.environ, test_env, clear=True):
            # Test complete workflow
            availability = ConfigurationFactory.get_provider_availability()
            default_provider = ConfigurationFactory.get_default_provider()
            optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()

            # Validate workflow results
            assert isinstance(availability, dict)
            assert default_provider in ["azure_sora", "google_veo3"]
            assert isinstance(optimal_config, dict)
            assert "selected_provider" in optimal_config
            assert "provider_availability" in optimal_config


class TestF4ErrorHandlingAndRecovery:
    """Error handling and recovery testing for F4 configuration system."""

    def test_configuration_error_recovery(self):
        """Test F4.35: Configuration error recovery mechanisms."""
        # Test recovery from invalid environment variables
        test_env = {
            "VEO3_TIMEOUT": "invalid_number",
            "VEO3_MAX_RETRIES": "not_a_number",
        }

        with patch.dict(os.environ, test_env, clear=True):
            # Should fall back to defaults or handle gracefully
            try:
                settings = ConfigurationFactory.get_veo3_settings()
                # If successful, defaults were used
                assert settings.VEO3_TIMEOUT > 0
                assert settings.VEO3_MAX_RETRIES > 0
            except ValueError:
                # Expected for some invalid values
                pass

    def test_missing_environment_file_handling(self):
        """Test F4.36: Handling of missing .env files."""
        # Test configuration service behavior without .env file
        with patch("os.path.exists", return_value=False):
            with patch("dotenv.load_dotenv", return_value=False):
                # Should handle missing .env file gracefully
                ConfigurationService.reload()

                # Basic configuration should still work with system environment
                config = ConfigurationFactory.get_base_config()
                assert config is not None

    def test_concurrent_configuration_access_safety(self):
        """Test F4.37: Thread safety of concurrent configuration access."""
        import threading
        import time

        errors = []
        test_env = {"GOOGLE_PROJECT_ID": "concurrent-safety-test"}

        def config_worker():
            """Worker function that accesses configuration concurrently."""
            try:
                for _ in range(10):
                    ConfigurationFactory.get_veo3_settings()
                    ConfigurationService._detect_deployment_type()
                    time.sleep(0.001)  # Small delay to increase contention
            except Exception as e:
                errors.append(e)

        with patch.dict(os.environ, test_env, clear=True):
            # Start multiple concurrent threads
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=config_worker)
                thread.start()
                threads.append(thread)

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # Validate no thread safety errors occurred
            assert len(errors) == 0, f"Thread safety errors: {errors}"


# Performance benchmarking utilities
class F4PerformanceBenchmark:
    """Performance benchmarking utilities for F4 configuration operations."""

    @staticmethod
    def benchmark_operation(
        operation_func, target_ms: float, operation_name: str
    ) -> Dict[str, Any]:
        """
        Benchmark a configuration operation against performance targets.

        Args:
            operation_func: Function to benchmark
            target_ms: Target execution time in milliseconds
            operation_name: Name of the operation for reporting

        Returns:
            Benchmark result dictionary
        """
        # Warm up
        operation_func()

        # Benchmark
        times = []
        for _ in range(10):
            start_time = time.perf_counter()
            operation_func()
            execution_time = (time.perf_counter() - start_time) * 1000
            times.append(execution_time)

        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        return {
            "operation": operation_name,
            "avg_time_ms": avg_time,
            "min_time_ms": min_time,
            "max_time_ms": max_time,
            "target_ms": target_ms,
            "meets_target": avg_time < target_ms,
            "performance_ratio": avg_time / target_ms,
        }


# Quality gate validation utilities
class F4QualityGateValidator:
    """Quality gate validation utilities for F4 configuration system."""

    @staticmethod
    def validate_all_quality_gates() -> Dict[str, Any]:
        """
        Validate all F4 configuration quality gates.

        Returns:
            Comprehensive quality gate validation result
        """
        test_env = {"GOOGLE_PROJECT_ID": "quality-gate-validation"}

        with patch.dict(os.environ, test_env, clear=True):
            results = {
                "performance_gates": [],
                "validation_gates": [],
                "security_gates": [],
                "overall_status": True,
            }

            # Performance quality gates
            performance_tests = [
                (
                    lambda: ConfigurationFactory.get_base_config(),
                    10,
                    "base_config_creation",
                ),
                (
                    lambda: ConfigurationFactory.get_video_config(),
                    10,
                    "video_config_creation",
                ),
                (
                    lambda: ConfigurationFactory.get_azure_config(),
                    10,
                    "azure_config_creation",
                ),
                (
                    lambda: ConfigurationService._detect_deployment_type(),
                    5,
                    "environment_detection",
                ),
            ]

            for operation_func, target_ms, operation_name in performance_tests:
                result = F4PerformanceBenchmark.benchmark_operation(
                    operation_func, target_ms, operation_name
                )
                results["performance_gates"].append(result)
                if not result["meets_target"]:
                    results["overall_status"] = False

            # Validation quality gates
            try:
                azure_validation = ConfigurationFactory.validate_provider_configuration(
                    "azure_sora"
                )
                veo3_validation = ConfigurationFactory.validate_provider_configuration(
                    "google_veo3"
                )

                results["validation_gates"].extend(
                    [
                        {
                            "validator": "azure_provider",
                            "valid": azure_validation["valid"],
                        },
                        {
                            "validator": "veo3_provider",
                            "valid": veo3_validation["valid"],
                        },
                    ]
                )

                if not azure_validation["valid"] or not veo3_validation["valid"]:
                    results["overall_status"] = False

            except Exception as e:
                results["validation_gates"].append(
                    {
                        "validator": "provider_validation",
                        "valid": False,
                        "error": str(e),
                    }
                )
                results["overall_status"] = False

            # Security quality gates
            try:
                # Test environment variable parsing security
                ConfigurationService.get_bool("NONEXISTENT_BOOL", False)
                ConfigurationService.get_int("NONEXISTENT_INT", 100)

                results["security_gates"].append(
                    {"security_check": "environment_variable_parsing", "secure": True}
                )

            except Exception as e:
                results["security_gates"].append(
                    {
                        "security_check": "environment_variable_parsing",
                        "secure": False,
                        "error": str(e),
                    }
                )
                results["overall_status"] = False

            return results


@pytest.mark.integration
class TestF4IntegrationScenarios:
    """Integration scenarios testing for F4 comprehensive workflows."""

    def test_complete_f4_configuration_workflow(self):
        """Test F4.38: Complete F4 configuration workflow integration."""
        test_env = {
            "GOOGLE_PROJECT_ID": "complete-workflow-test",
            "USE_MOCK_VEO": "true",
            "DEPLOYMENT_TYPE": "local",
        }

        with patch.dict(os.environ, test_env, clear=True):
            # Step 1: Environment detection
            deployment_type = ConfigurationService._detect_deployment_type()
            assert deployment_type == "local"

            # Step 2: Provider availability check
            availability = ConfigurationFactory.get_provider_availability()
            assert "azure_sora" in availability
            assert "google_veo3" in availability

            # Step 3: Provider configuration creation
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")

            assert azure_config is not None
            assert veo3_config is not None

            # Step 4: Environment-aware configuration
            env_aware_config = (
                ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )
            )
            assert env_aware_config is not None

            # Step 5: Optimal provider selection
            optimal_config = ProviderConfigurationFactory.get_optimal_provider_config()
            assert "selected_provider" in optimal_config

            # Step 6: Performance validation
            quality_gates = F4QualityGateValidator.validate_all_quality_gates()
            assert quality_gates["overall_status"] is True

    def test_f4_stress_testing_scenario(self):
        """Test F4.39: F4 configuration system under stress conditions."""
        test_env = {"GOOGLE_PROJECT_ID": "stress-test-project"}

        with patch.dict(os.environ, test_env, clear=True):
            # Rapid configuration access
            for i in range(100):
                config = ConfigurationFactory.get_base_config()
                assert config is not None

                if i % 10 == 0:
                    # Clear cache periodically to test cache miss performance
                    ConfigurationFactory.clear_cache()

            # Concurrent access stress test
            import threading

            def stress_worker():
                for _ in range(50):
                    ConfigurationFactory.get_veo3_settings()
                    ConfigurationService._detect_deployment_type()

            threads = []
            for _ in range(5):
                thread = threading.Thread(target=stress_worker)
                thread.start()
                threads.append(thread)

            for thread in threads:
                thread.join()

            # System should remain stable after stress testing
            final_config = ConfigurationFactory.get_base_config()
            assert final_config is not None
