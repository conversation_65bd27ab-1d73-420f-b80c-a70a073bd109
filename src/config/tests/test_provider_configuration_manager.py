"""
Comprehensive tests for Provider Configuration Manager F4 integration.

Tests the ProviderConfigurationManager functionality including environment
detection, provider availability checking, and automatic mock switching
for the C2 Provider Selection UI system.
"""

from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest

from ..provider_configuration_manager import (
    ProviderAvailabilityMatrix,
    ProviderConfigurationManager,
    ProviderEnvironmentConfig,
    ProviderEnvironmentStatus,
    get_provider_configuration_manager,
    initialize_provider_configuration_manager,
)


@pytest.fixture
def mock_config_service():
    """Mock ConfigurationService for testing."""
    with patch(
        "src.config.provider_configuration_manager.ConfigurationService"
    ) as mock:
        mock._detect_deployment_type.return_value = "local"
        mock.get.return_value = "test_value"
        mock.reload.return_value = None
        yield mock


@pytest.fixture
def mock_config_factory():
    """Mock ConfigurationFactory for testing."""
    with patch(
        "src.config.provider_configuration_manager.ConfigurationFactory"
    ) as mock:
        # Mock Azure configuration
        mock.get_azure_config.return_value = {
            "endpoint": "https://test.openai.azure.com",
            "api_key": "test_api_key",
            "api_version": "2024-08-01-preview",
            "deployment_name": "sora",
        }

        # Mock Veo3 settings
        mock_veo3_settings = MagicMock()
        mock_veo3_settings.GOOGLE_PROJECT_ID = "test-project-123"
        mock_veo3_settings.USE_MOCK_VEO = True
        mock.get_veo3_settings.return_value = mock_veo3_settings

        # Mock Veo3 config
        mock_veo3_config = MagicMock()
        mock_veo3_config.use_mock = True
        mock_veo3_config.to_dict.return_value = {
            "project_id": "test-project-123",
            "use_mock": True,
            "client_id": "test_client_id",
        }
        mock.create_veo3_config.return_value = mock_veo3_config

        # Mock validation
        mock.validate_provider_configuration.return_value = {"errors": []}

        yield mock


@pytest.fixture
def mock_environment_info():
    """Mock environment info."""
    with patch(
        "src.config.provider_configuration_manager.get_environment_info"
    ) as mock:
        mock.return_value = {
            "environment": "local",
            "debug": True,
            "testing": False,
            "database_type": "sqlite",
            "rate_limiting": False,
        }
        yield mock


@pytest.fixture
async def provider_config_manager(
    mock_config_service, mock_config_factory, mock_environment_info
):
    """Provide configured provider configuration manager."""
    manager = ProviderConfigurationManager()
    return manager


class TestProviderEnvironmentConfig:
    """Test provider environment configuration."""

    def test_environment_config_creation(self):
        """Test environment configuration creation."""
        config = ProviderEnvironmentConfig(
            provider_id="test_provider",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
            configuration={"key": "value"},
            capabilities={"feature": True},
            last_validated=datetime.utcnow(),
        )

        assert config.provider_id == "test_provider"
        assert config.environment_type == "local"
        assert config.status == ProviderEnvironmentStatus.AVAILABLE
        assert not config.is_mock
        assert config.configuration == {"key": "value"}
        assert config.capabilities == {"feature": True}
        assert config.is_healthy  # Should be healthy with recent validation

    def test_environment_config_health_check(self):
        """Test environment configuration health checking."""
        # Healthy configuration
        healthy_config = ProviderEnvironmentConfig(
            provider_id="test_provider",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
            last_validated=datetime.utcnow(),
            validation_errors=[],
        )
        assert healthy_config.is_healthy

        # Unhealthy - status not available
        unhealthy_status = ProviderEnvironmentConfig(
            provider_id="test_provider",
            environment_type="local",
            status=ProviderEnvironmentStatus.UNAVAILABLE,
            is_mock=False,
            last_validated=datetime.utcnow(),
            validation_errors=[],
        )
        assert not unhealthy_status.is_healthy

        # Unhealthy - has validation errors
        unhealthy_errors = ProviderEnvironmentConfig(
            provider_id="test_provider",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
            last_validated=datetime.utcnow(),
            validation_errors=["Configuration error"],
        )
        assert not unhealthy_errors.is_healthy

        # Unhealthy - validation too old
        unhealthy_old = ProviderEnvironmentConfig(
            provider_id="test_provider",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
            last_validated=datetime.utcnow() - timedelta(hours=1),
            validation_errors=[],
        )
        assert not unhealthy_old.is_healthy


class TestProviderAvailabilityMatrix:
    """Test provider availability matrix."""

    def test_availability_matrix_creation(self):
        """Test availability matrix creation."""
        matrix = ProviderAvailabilityMatrix()

        # Add configurations
        matrix.azure_sora["local"] = ProviderEnvironmentConfig(
            provider_id="azure_sora",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
        )

        matrix.google_veo3["local"] = ProviderEnvironmentConfig(
            provider_id="google_veo3",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=True,
        )

        assert len(matrix.azure_sora) == 1
        assert len(matrix.google_veo3) == 1

    @patch(
        "src.config.provider_configuration_manager.ConfigurationService._detect_deployment_type"
    )
    def test_available_providers(self, mock_detect):
        """Test getting available providers."""
        mock_detect.return_value = "local"

        matrix = ProviderAvailabilityMatrix()

        # Add available Azure Sora
        matrix.azure_sora["local"] = ProviderEnvironmentConfig(
            provider_id="azure_sora",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
        )

        # Add unavailable Google Veo3
        matrix.google_veo3["local"] = ProviderEnvironmentConfig(
            provider_id="google_veo3",
            environment_type="local",
            status=ProviderEnvironmentStatus.UNAVAILABLE,
            is_mock=False,
        )

        available = matrix.available_providers
        assert "azure_sora" in available
        assert "google_veo3" not in available

    @patch(
        "src.config.provider_configuration_manager.ConfigurationService._detect_deployment_type"
    )
    def test_default_provider_selection(self, mock_detect):
        """Test default provider selection logic."""
        # Test production environment - prefer Azure Sora
        mock_detect.return_value = "production"

        matrix = ProviderAvailabilityMatrix()
        matrix.azure_sora["production"] = ProviderEnvironmentConfig(
            provider_id="azure_sora",
            environment_type="production",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
        )
        matrix.google_veo3["production"] = ProviderEnvironmentConfig(
            provider_id="google_veo3",
            environment_type="production",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
        )

        assert matrix.default_provider == "azure_sora"

        # Test local environment - prefer Google Veo3
        mock_detect.return_value = "local"

        matrix = ProviderAvailabilityMatrix()
        matrix.azure_sora["local"] = ProviderEnvironmentConfig(
            provider_id="azure_sora",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
        )
        matrix.google_veo3["local"] = ProviderEnvironmentConfig(
            provider_id="google_veo3",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=True,
        )

        assert matrix.default_provider == "google_veo3"


class TestProviderConfigurationManager:
    """Test provider configuration manager functionality."""

    @pytest.mark.asyncio
    async def test_manager_initialization(self, mock_config_service):
        """Test manager initialization."""
        manager = ProviderConfigurationManager()

        assert manager.current_environment == "local"
        assert manager._availability_matrix is None
        assert manager._last_refresh is None

        # Check environment configs
        assert "local" in manager._environment_configs
        assert "docker" in manager._environment_configs
        assert "production" in manager._environment_configs

    @pytest.mark.asyncio
    async def test_azure_sora_availability_check(
        self, provider_config_manager, mock_config_factory
    ):
        """Test Azure Sora availability checking."""
        config = await provider_config_manager._check_azure_sora_availability("local")

        assert config.provider_id == "azure_sora"
        assert config.environment_type == "local"
        assert (
            config.status == ProviderEnvironmentStatus.MOCK_ONLY
        )  # Local prefers mock
        assert config.is_mock
        assert len(config.validation_errors) == 0

        # Test capabilities
        assert config.capabilities["text_to_video"]
        assert not config.capabilities[
            "image_to_video"
        ]  # Azure Sora doesn't support this

    @pytest.mark.asyncio
    async def test_google_veo3_availability_check(
        self, provider_config_manager, mock_config_factory
    ):
        """Test Google Veo3 availability checking."""
        config = await provider_config_manager._check_google_veo3_availability("local")

        assert config.provider_id == "google_veo3"
        assert config.environment_type == "local"
        assert config.status == ProviderEnvironmentStatus.MOCK_ONLY
        assert config.is_mock

        # Test capabilities
        assert config.capabilities["text_to_video"]
        assert config.capabilities["image_to_video"]  # Veo3 supports this
        assert config.capabilities["video_editing"]

    @pytest.mark.asyncio
    async def test_refresh_provider_availability(self, provider_config_manager):
        """Test provider availability refresh."""
        matrix = await provider_config_manager.refresh_provider_availability()

        assert isinstance(matrix, ProviderAvailabilityMatrix)
        assert provider_config_manager._availability_matrix is matrix
        assert provider_config_manager._last_refresh is not None

        # Check that both providers were checked
        assert "local" in matrix.azure_sora
        assert "local" in matrix.google_veo3

    @pytest.mark.asyncio
    async def test_environment_rules_application(self, provider_config_manager):
        """Test application of environment-specific rules."""
        # Create test matrix
        matrix = ProviderAvailabilityMatrix()

        # Add Azure Sora config
        matrix.azure_sora["local"] = ProviderEnvironmentConfig(
            provider_id="azure_sora",
            environment_type="local",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=False,
        )

        # Apply local environment rules
        result_matrix = provider_config_manager._apply_environment_rules(
            matrix, "local"
        )

        # Should prefer mock in local environment
        local_config = result_matrix.azure_sora["local"]
        assert local_config.status == ProviderEnvironmentStatus.MOCK_ONLY
        assert local_config.is_mock

    @pytest.mark.asyncio
    async def test_production_environment_rules(self, mock_config_service):
        """Test production environment rules."""
        mock_config_service._detect_deployment_type.return_value = "production"

        manager = ProviderConfigurationManager()

        # Create matrix with mock provider
        matrix = ProviderAvailabilityMatrix()
        matrix.azure_sora["production"] = ProviderEnvironmentConfig(
            provider_id="azure_sora",
            environment_type="production",
            status=ProviderEnvironmentStatus.AVAILABLE,
            is_mock=True,  # This should be forbidden in production
        )

        # Apply production rules
        result_matrix = manager._apply_environment_rules(matrix, "production")

        # Mock provider should be marked unavailable in production
        prod_config = result_matrix.azure_sora["production"]
        assert prod_config.status == ProviderEnvironmentStatus.UNAVAILABLE
        assert not prod_config.is_mock

    @pytest.mark.asyncio
    async def test_optimal_provider_config(self, provider_config_manager):
        """Test getting optimal provider configuration."""
        # Refresh availability first
        await provider_config_manager.refresh_provider_availability()

        # Get optimal config
        config = await provider_config_manager.get_optimal_provider_config()

        assert "selected_provider" in config
        assert "available_providers" in config
        assert "environment" in config
        assert config["environment"] == "local"
        assert "provider_matrix" in config
        assert "is_mock_environment" in config

    @pytest.mark.asyncio
    async def test_provider_selection_with_preferences(self, provider_config_manager):
        """Test provider selection with user preferences."""
        await provider_config_manager.refresh_provider_availability()

        # Test with preferred provider
        session_prefs = {
            "preferred_provider": "azure_sora",
            "fallback_providers": ["google_veo3"],
        }

        config = await provider_config_manager.get_optimal_provider_config(
            session_prefs
        )

        # Should select preferred provider if available
        assert config["selected_provider"] in ["azure_sora", "google_veo3"]

    @pytest.mark.asyncio
    async def test_provider_configuration_validation(self, provider_config_manager):
        """Test provider configuration validation."""
        await provider_config_manager.refresh_provider_availability()

        # Validate Azure Sora
        azure_validation = (
            await provider_config_manager.validate_provider_configuration("azure_sora")
        )

        assert azure_validation["provider_id"] == "azure_sora"
        assert azure_validation["environment"] == "local"
        assert "valid" in azure_validation
        assert "status" in azure_validation
        assert "capabilities" in azure_validation

        # Validate Google Veo3
        veo3_validation = await provider_config_manager.validate_provider_configuration(
            "google_veo3"
        )

        assert veo3_validation["provider_id"] == "google_veo3"
        assert veo3_validation["is_mock"]  # Should be mock in local environment

    @pytest.mark.asyncio
    async def test_provider_switching_recommendations(self, provider_config_manager):
        """Test provider switching recommendations."""
        await provider_config_manager.refresh_provider_availability()

        # Test rate limit failure
        recommendations = (
            await provider_config_manager.get_provider_switching_recommendations(
                "azure_sora", "rate_limit exceeded"
            )
        )

        assert recommendations["current_provider"] == "azure_sora"
        assert recommendations["failure_reason"] == "rate_limit exceeded"
        assert recommendations["environment"] == "local"
        assert "alternative_providers" in recommendations
        assert "recommended_action" in recommendations
        assert recommendations["switching_allowed"]  # Local allows fallback

        # Test configuration failure
        config_recommendations = (
            await provider_config_manager.get_provider_switching_recommendations(
                "google_veo3", "configuration error"
            )
        )

        assert config_recommendations["recommended_action"] == "fix_config"

    @pytest.mark.asyncio
    async def test_environment_configuration_summary(self, provider_config_manager):
        """Test environment configuration summary."""
        await provider_config_manager.refresh_provider_availability()

        summary = provider_config_manager.get_environment_configuration_summary()

        # Check structure
        assert "environment" in summary
        assert "providers" in summary
        assert "f4_system" in summary

        # Check environment info
        env_info = summary["environment"]
        assert env_info["type"] == "local"
        assert "info" in env_info
        assert "config" in env_info

        # Check provider info
        provider_info = summary["providers"]
        assert "availability_matrix" in provider_info
        assert "available_providers" in provider_info
        assert "default_provider" in provider_info

        # Check F4 system info
        f4_info = summary["f4_system"]
        assert "deployment_detection" in f4_info
        assert "configuration_service_initialized" in f4_info
        assert "refresh_interval_minutes" in f4_info

    def test_needs_refresh_logic(self, provider_config_manager):
        """Test refresh logic."""
        # Should need refresh initially
        assert provider_config_manager._needs_refresh()

        # Set recent refresh
        provider_config_manager._availability_matrix = ProviderAvailabilityMatrix()
        provider_config_manager._last_refresh = datetime.utcnow()

        # Should not need refresh
        assert not provider_config_manager._needs_refresh()

        # Set old refresh
        provider_config_manager._last_refresh = datetime.utcnow() - timedelta(
            minutes=10
        )

        # Should need refresh
        assert provider_config_manager._needs_refresh()

    def test_mock_environment_detection(self, provider_config_manager):
        """Test mock environment detection."""
        # Local environment should use mock
        assert provider_config_manager._is_mock_environment()

    @pytest.mark.asyncio
    async def test_missing_provider_validation(self, provider_config_manager):
        """Test validation of missing provider."""
        await provider_config_manager.refresh_provider_availability()

        validation = await provider_config_manager.validate_provider_configuration(
            "nonexistent_provider"
        )

        assert not validation["valid"]
        assert validation["provider_id"] == "nonexistent_provider"
        assert validation["status"] == "unavailable"
        assert len(validation["errors"]) > 0

    def test_required_config_keys(self, provider_config_manager):
        """Test required configuration keys."""
        azure_keys = provider_config_manager._get_required_config_keys("azure_sora")
        assert "endpoint" in azure_keys
        assert "api_key" in azure_keys
        assert "api_version" in azure_keys
        assert "deployment_name" in azure_keys

        veo3_keys = provider_config_manager._get_required_config_keys("google_veo3")
        assert "project_id" in veo3_keys
        assert "client_id" in veo3_keys

        unknown_keys = provider_config_manager._get_required_config_keys(
            "unknown_provider"
        )
        assert len(unknown_keys) == 0


class TestProviderConfigurationManagerGlobal:
    """Test global provider configuration manager functions."""

    def test_get_provider_configuration_manager(self):
        """Test getting global manager instance."""
        manager1 = get_provider_configuration_manager()
        manager2 = get_provider_configuration_manager()

        # Should return same instance (singleton)
        assert manager1 is manager2
        assert isinstance(manager1, ProviderConfigurationManager)

    @pytest.mark.asyncio
    async def test_initialize_provider_configuration_manager(
        self, mock_config_service, mock_config_factory, mock_environment_info
    ):
        """Test global manager initialization."""
        await initialize_provider_configuration_manager()

        manager = get_provider_configuration_manager()
        assert manager._availability_matrix is not None
        assert manager._last_refresh is not None


# Integration tests for full F4 system integration
class TestF4SystemIntegration:
    """Test full F4 system integration."""

    @pytest.mark.asyncio
    @patch(
        "src.config.provider_configuration_manager.ConfigurationService._detect_deployment_type"
    )
    async def test_docker_environment_integration(
        self, mock_detect, mock_config_factory, mock_environment_info
    ):
        """Test Docker environment integration."""
        mock_detect.return_value = "docker"

        manager = ProviderConfigurationManager()
        await manager.refresh_provider_availability()

        assert manager.current_environment == "docker"

        # Docker environment should not prefer mock
        docker_config = manager._environment_configs["docker"]
        assert not docker_config["prefer_mock"]
        assert docker_config["allow_fallback"]

        # Check provider configurations
        summary = manager.get_environment_configuration_summary()
        assert summary["environment"]["type"] == "docker"

    @pytest.mark.asyncio
    @patch(
        "src.config.provider_configuration_manager.ConfigurationService._detect_deployment_type"
    )
    async def test_production_environment_integration(
        self, mock_detect, mock_config_factory, mock_environment_info
    ):
        """Test production environment integration."""
        mock_detect.return_value = "production"

        manager = ProviderConfigurationManager()
        await manager.refresh_provider_availability()

        assert manager.current_environment == "production"

        # Production environment should be strict
        prod_config = manager._environment_configs["production"]
        assert not prod_config["prefer_mock"]
        assert not prod_config["allow_fallback"]  # Strict production mode

        # Test provider switching recommendations in production
        recommendations = await manager.get_provider_switching_recommendations(
            "azure_sora", "test_failure"
        )
        assert not recommendations["switching_allowed"]  # No fallback in production

    @pytest.mark.asyncio
    async def test_environment_configuration_error_handling(
        self, mock_config_service, mock_config_factory
    ):
        """Test error handling in environment configuration."""
        # Mock configuration error
        mock_config_factory.get_azure_config.side_effect = Exception(
            "Configuration error"
        )

        manager = ProviderConfigurationManager()
        config = await manager._check_azure_sora_availability("local")

        assert config.status == ProviderEnvironmentStatus.UNAVAILABLE
        assert len(config.validation_errors) > 0
        assert "Configuration error" in config.validation_errors[0]
