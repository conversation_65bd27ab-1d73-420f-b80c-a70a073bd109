"""Unit tests for security configuration and validation.

Tests input validation, sanitization, security headers,
and environment security checks for production readiness.
"""

import os
import re
from unittest.mock import patch

import pytest

from src.config.security import SecurityConfig


@pytest.mark.unit
class TestSecurityConfig:
    """Test SecurityConfig functionality.

    Tests configuration constants, validation patterns,
    rate limiting settings, and CORS origins.
    """

    def test_security_config_constants(self):
        """Test SecurityConfig constant values."""
        assert SecurityConfig.ALLOWED_EXTENSIONS == ["mp4", "mov", "avi", "mkv"]
        assert SecurityConfig.MAX_FILENAME_LENGTH == 255

        # Test patterns are compiled regex
        assert isinstance(
            SecurityConfig.PROMPT_VALIDATION_PATTERN, type(re.compile(""))
        )
        assert isinstance(SecurityConfig.JOB_ID_PATTERN, type(re.compile("")))

        # Test dangerous patterns
        assert len(SecurityConfig.DANGEROUS_PATTERNS) > 0
        for pattern in SecurityConfig.DANGEROUS_PATTERNS:
            assert isinstance(pattern, type(re.compile("")))

    def test_security_config_rate_limits(self):
        """Test rate limiting configuration."""
        rate_limits = SecurityConfig.RATE_LIMITS

        assert "generate_video" in rate_limits
        assert "status_check" in rate_limits
        assert "health_check" in rate_limits

        # Check structure
        for _endpoint, limits in rate_limits.items():
            assert "requests_per_minute" in limits
            assert "requests_per_hour" in limits
            assert isinstance(limits["requests_per_minute"], int)
            assert isinstance(limits["requests_per_hour"], int)

    def test_cors_origins(self):
        """Test CORS origins configuration."""
        origins = SecurityConfig.CORS_ORIGINS
        assert isinstance(origins, list)
        assert len(origins) > 0

        # Should include localhost variations
        localhost_origins = [
            origin
            for origin in origins
            if "localhost" in origin or "127.0.0.1" in origin
        ]
        assert len(localhost_origins) > 0


@pytest.mark.unit
class TestPromptValidation:
    """Test prompt validation functionality."""

    def test_validate_prompt_valid_inputs(self):
        """Test prompt validation with valid inputs."""
        valid_prompts = [
            "A cat playing piano",
            "Beautiful sunset over the ocean",
            "City skyline at night, realistic style",
            "Dancing in the rain, happy mood",
            "A dog running in the park, slow motion",
            "Coffee shop scene, warm lighting",
        ]

        for prompt in valid_prompts:
            assert SecurityConfig.validate_prompt(prompt) is True

    def test_validate_prompt_invalid_inputs(self):
        """Test prompt validation with invalid inputs."""
        invalid_prompts = [
            "",  # Empty string
            None,  # None value
            123,  # Non-string type
            [],  # Non-string type
            "x" * 501,  # Too long (>500 chars)
        ]

        for prompt in invalid_prompts:
            assert SecurityConfig.validate_prompt(prompt) is False

    def test_validate_prompt_dangerous_patterns(self):
        """Test prompt validation rejects dangerous patterns."""
        dangerous_prompts = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "onclick=alert('xss')",
            "eval(malicious_code)",
            "expression(alert('xss'))",
            "A cat <script>evil</script> playing",
            "Beautiful sunset javascript:hack()",
            "onclick=steal() dancing",
        ]

        for prompt in dangerous_prompts:
            assert SecurityConfig.validate_prompt(prompt) is False

    def test_validate_prompt_character_restrictions(self):
        """Test prompt validation character restrictions."""
        # Test invalid characters
        invalid_character_prompts = [
            "A cat with @#$%^&*() symbols",
            "Test with unicode: ñáéíóú",
            "Test with newlines\nand\ttabs",
            "Test with | pipes & ampersands",
            "Test with <brackets> and [squares]",
        ]

        for prompt in invalid_character_prompts:
            assert SecurityConfig.validate_prompt(prompt) is False

    def test_validate_prompt_edge_cases(self):
        """Test prompt validation edge cases."""
        # Whitespace-only strings
        assert SecurityConfig.validate_prompt("   ") is True  # Valid characters
        assert SecurityConfig.validate_prompt("\t\n") is False  # Invalid characters

        # Exactly 500 characters
        valid_500_char = "A" * 500
        assert SecurityConfig.validate_prompt(valid_500_char) is True

        # Exactly 501 characters
        invalid_501_char = "A" * 501
        assert SecurityConfig.validate_prompt(invalid_501_char) is False


@pytest.mark.unit
class TestJobIdValidation:
    """Test job ID validation functionality."""

    def test_validate_job_id_valid_uuids(self):
        """Test job ID validation with valid UUIDs."""
        valid_uuids = [
            "123e4567-e89b-12d3-a456-************",
            "550e8400-e29b-41d4-a716-************",
            "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
            "f47ac10b-58cc-4372-a567-0e02b2c3d479",
        ]

        for uuid in valid_uuids:
            assert SecurityConfig.validate_job_id(uuid) is True

    def test_validate_job_id_invalid_inputs(self):
        """Test job ID validation with invalid inputs."""
        invalid_inputs = [
            "",  # Empty string
            None,  # None value
            123,  # Non-string type
            "not-a-uuid",  # Invalid format
            "123e4567-e89b-12d3-a456-42661417400",  # Too short
            "123e4567-e89b-12d3-a456-************0",  # Too long
            "123e4567_e89b_12d3_a456_************",  # Wrong separators
            "123g4567-e89b-12d3-a456-************",  # Invalid character 'g'
            "123E4567-E89B-12D3-A456-************",  # Uppercase (case sensitive)
        ]

        for job_id in invalid_inputs:
            assert SecurityConfig.validate_job_id(job_id) is False


@pytest.mark.unit
class TestFilenameValidation:
    """Test filename validation functionality."""

    def test_validate_filename_valid_inputs(self):
        """Test filename validation with valid inputs."""
        valid_filenames = [
            "video.mp4",
            "movie.mov",
            "animation.avi",
            "presentation.mkv",
            "test_video_123.mp4",
        ]

        for filename in valid_filenames:
            assert SecurityConfig.validate_filename(filename) is True

    def test_validate_filename_invalid_extensions(self):
        """Test filename validation with invalid extensions."""
        invalid_filenames = [
            "document.pdf",
            "image.jpg",
            "script.exe",
            "data.csv",
            "video.mp3",  # Audio, not video
            "file.txt",
            "noextension",
        ]

        for filename in invalid_filenames:
            assert SecurityConfig.validate_filename(filename) is False

    def test_validate_filename_security_issues(self):
        """Test filename validation rejects security issues."""
        dangerous_filenames = [
            "../../../etc/passwd",  # Directory traversal
            "..\\windows\\system32\\cmd.exe",  # Windows directory traversal
            "video/with/slashes.mp4",  # Path separators
            "video<script>.mp4",  # Script injection
            "video javascript:alert().mp4",  # JavaScript
            "x" * 256 + ".mp4",  # Too long
        ]

        for filename in dangerous_filenames:
            assert SecurityConfig.validate_filename(filename) is False

    def test_validate_filename_invalid_types(self):
        """Test filename validation with invalid input types."""
        invalid_inputs = [
            None,
            123,
            [],
            {},
            "",
        ]

        for filename in invalid_inputs:
            assert SecurityConfig.validate_filename(filename) is False


@pytest.mark.unit
class TestPromptSanitization:
    """Test prompt sanitization functionality."""

    def test_sanitize_prompt_valid_input(self):
        """Test sanitizing valid prompts."""
        test_cases = [
            ("A cat playing piano", "A cat playing piano"),
            ("  Multiple   spaces  ", "Multiple spaces"),
            ("Trailing spaces   ", "Trailing spaces"),
            ("   Leading spaces", "Leading spaces"),
        ]

        for input_prompt, expected in test_cases:
            result = SecurityConfig.sanitize_prompt(input_prompt)
            assert result == expected

    def test_sanitize_prompt_remove_dangerous_patterns(self):
        """Test sanitizing removes dangerous patterns."""
        test_cases = [
            ("<script>alert('xss')</script>A cat", "A cat"),
            ("Beautiful javascript:hack() sunset", "Beautiful  sunset"),
            ("onclick=steal() dancing in rain", " dancing in rain"),
            ("eval(bad) good content", " good content"),
            ("expression(evil) nice video", " nice video"),
        ]

        for input_prompt, expected in test_cases:
            result = SecurityConfig.sanitize_prompt(input_prompt)
            assert result == expected

    def test_sanitize_prompt_length_limit(self):
        """Test sanitizing enforces length limit."""
        long_prompt = "A" * 600  # Longer than 500 limit
        result = SecurityConfig.sanitize_prompt(long_prompt)
        assert len(result) == 500
        assert result == "A" * 500

    def test_sanitize_prompt_empty_input(self):
        """Test sanitizing empty or None input."""
        assert SecurityConfig.sanitize_prompt("") == ""
        assert SecurityConfig.sanitize_prompt(None) == ""

    def test_sanitize_prompt_whitespace_normalization(self):
        """Test sanitizing normalizes whitespace."""
        test_cases = [
            ("Multiple\n\nlines", "Multiple lines"),
            ("Tabs\t\tand\tspaces", "Tabs and spaces"),
            ("Mixed\t \n\r\nwhitespace", "Mixed whitespace"),
            ("   \t\n   ", ""),  # Only whitespace becomes empty
        ]

        for input_prompt, expected in test_cases:
            result = SecurityConfig.sanitize_prompt(input_prompt)
            assert result == expected


@pytest.mark.unit
class TestUrlValidation:
    """Test URL validation functionality."""

    def test_validate_url_valid_urls(self):
        """Test URL validation with valid URLs."""
        valid_urls = [
            "https://example.com",
            "http://test.org",
            "https://api.openai.com/v1/video",
            "http://localhost:8080/test",  # Allowed in development
            "https://subdomain.domain.com/path?query=value",
        ]

        for url in valid_urls:
            assert SecurityConfig.validate_url(url) is True

    def test_validate_url_invalid_schemes(self):
        """Test URL validation rejects invalid schemes."""
        invalid_urls = [
            "ftp://example.com",
            "file:///etc/passwd",
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "mailto:<EMAIL>",
        ]

        for url in invalid_urls:
            assert SecurityConfig.validate_url(url) is False

    def test_validate_url_custom_schemes(self):
        """Test URL validation with custom allowed schemes."""
        # Test with custom schemes
        assert SecurityConfig.validate_url("ftp://example.com", ["ftp"]) is True
        assert SecurityConfig.validate_url("https://example.com", ["ftp"]) is False

    def test_validate_url_production_restrictions(self):
        """Test URL validation in production environment."""
        production_restricted_urls = [
            "http://localhost:8080",
            "https://127.0.0.1:5000",
            "http://0.0.0.0:3000",
            "https://********/internal",
            "http://**********/private",
            "https://***********/local",
        ]

        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            for url in production_restricted_urls:
                assert SecurityConfig.validate_url(url) is False

    def test_validate_url_development_allowances(self):
        """Test URL validation allows local URLs in development."""
        local_urls = [
            "http://localhost:8080",
            "https://127.0.0.1:5000",
        ]

        with patch.dict(os.environ, {"FLASK_ENV": "development"}):
            for url in local_urls:
                assert SecurityConfig.validate_url(url) is True

    def test_validate_url_invalid_inputs(self):
        """Test URL validation with invalid inputs."""
        invalid_inputs = [
            None,
            "",
            123,
            [],
            "not-a-url",
            "://invalid",
        ]

        for url in invalid_inputs:
            assert SecurityConfig.validate_url(url) is False


@pytest.mark.unit
class TestSecureTokenGeneration:
    """Test secure token generation."""

    def test_generate_secure_token_default_length(self):
        """Test secure token generation with default length."""
        token = SecurityConfig.generate_secure_token()

        # Default length is 32 bytes = 64 hex characters
        assert len(token) == 64
        assert isinstance(token, str)

        # Should be hex characters only
        assert all(c in "0123456789abcdef" for c in token)

    def test_generate_secure_token_custom_length(self):
        """Test secure token generation with custom length."""
        lengths = [8, 16, 24, 64]

        for length in lengths:
            token = SecurityConfig.generate_secure_token(length)
            assert len(token) == length * 2  # Hex encoding doubles the length
            assert isinstance(token, str)

    def test_generate_secure_token_uniqueness(self):
        """Test that generated tokens are unique."""
        tokens = [SecurityConfig.generate_secure_token() for _ in range(100)]

        # All tokens should be unique
        assert len(set(tokens)) == 100


@pytest.mark.unit
class TestSecurityHeaders:
    """Test security headers functionality."""

    def test_get_security_headers_structure(self):
        """Test security headers structure and presence."""
        headers = SecurityConfig.get_security_headers()

        assert isinstance(headers, dict)

        required_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options",
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy",
            "Referrer-Policy",
            "Permissions-Policy",
        ]

        for header in required_headers:
            assert header in headers
            assert isinstance(headers[header], str)
            assert len(headers[header]) > 0

    def test_get_security_headers_values(self):
        """Test specific security header values."""
        headers = SecurityConfig.get_security_headers()

        assert headers["X-Content-Type-Options"] == "nosniff"
        assert headers["X-Frame-Options"] == "DENY"
        assert "1; mode=block" in headers["X-XSS-Protection"]
        assert "max-age=" in headers["Strict-Transport-Security"]
        assert "default-src 'self'" in headers["Content-Security-Policy"]


@pytest.mark.unit
class TestEnvironmentSecurityCheck:
    """Test environment security checking."""

    def setup_method(self):
        """Set up test environment."""
        self.original_env = {}
        self.test_vars = [
            "SECRET_KEY",
            "FLASK_ENV",
            "DATABASE_URL",
            "FORCE_HTTPS",
            "RATE_LIMIT_ENABLED",
        ]

        for var in self.test_vars:
            self.original_env[var] = os.getenv(var)

    def teardown_method(self):
        """Clean up test environment."""
        for var in self.test_vars:
            if self.original_env[var] is None:
                os.environ.pop(var, None)
            else:
                os.environ[var] = self.original_env[var]

    def test_check_environment_security_secure_setup(self):
        """Test security check with secure environment setup."""
        os.environ["SECRET_KEY"] = "very-secure-32-character-secret-key"
        os.environ["FLASK_ENV"] = "production"
        os.environ["DATABASE_URL"] = "postgresql://user:pass@localhost/db"
        os.environ["FORCE_HTTPS"] = "true"
        os.environ["RATE_LIMIT_ENABLED"] = "true"

        checks = SecurityConfig.check_environment_security()

        assert checks["secure_secret_key"] is True
        assert checks["debug_disabled"] is True
        assert checks["secure_database"] is True
        assert checks["https_enforced"] is True
        assert checks["rate_limiting_enabled"] is True

    def test_check_environment_security_insecure_setup(self):
        """Test security check with insecure environment setup."""
        os.environ["SECRET_KEY"] = "dev-key-change-in-production"
        os.environ["FLASK_ENV"] = "development"
        os.environ["DATABASE_URL"] = "sqlite:///test.db"
        os.environ["FORCE_HTTPS"] = "false"
        os.environ["RATE_LIMIT_ENABLED"] = "false"

        checks = SecurityConfig.check_environment_security()

        assert checks["secure_secret_key"] is False
        assert checks["debug_disabled"] is False
        assert checks["secure_database"] is False
        assert checks["https_enforced"] is False
        assert checks["rate_limiting_enabled"] is False

    def test_check_environment_security_missing_values(self):
        """Test security check with missing environment values."""
        for var in self.test_vars:
            os.environ.pop(var, None)

        checks = SecurityConfig.check_environment_security()

        # Should handle missing values gracefully
        assert isinstance(checks, dict)
        assert "secure_secret_key" in checks
        assert "debug_disabled" in checks
        assert "secure_database" in checks

    def test_get_security_recommendations_all_secure(self):
        """Test security recommendations with secure setup."""
        with patch.object(SecurityConfig, "check_environment_security") as mock_check:
            mock_check.return_value = {
                "secure_secret_key": True,
                "debug_disabled": True,
                "secure_database": True,
                "https_enforced": True,
                "rate_limiting_enabled": True,
            }

            recommendations = SecurityConfig.get_security_recommendations()
            assert recommendations == []

    def test_get_security_recommendations_all_insecure(self):
        """Test security recommendations with insecure setup."""
        with patch.object(SecurityConfig, "check_environment_security") as mock_check:
            mock_check.return_value = {
                "secure_secret_key": False,
                "debug_disabled": False,
                "secure_database": False,
                "https_enforced": False,
                "rate_limiting_enabled": False,
            }

            recommendations = SecurityConfig.get_security_recommendations()

            assert len(recommendations) == 5
            assert any("SECRET_KEY" in rec for rec in recommendations)
            assert any("debug mode" in rec for rec in recommendations)
            assert any("PostgreSQL" in rec for rec in recommendations)
            assert any("HTTPS" in rec for rec in recommendations)
            assert any("rate limiting" in rec for rec in recommendations)

    def test_get_security_recommendations_partial_issues(self):
        """Test security recommendations with partial security issues."""
        with patch.object(SecurityConfig, "check_environment_security") as mock_check:
            mock_check.return_value = {
                "secure_secret_key": False,
                "debug_disabled": True,
                "secure_database": True,
                "https_enforced": False,
                "rate_limiting_enabled": True,
            }

            recommendations = SecurityConfig.get_security_recommendations()

            assert len(recommendations) == 2
            assert any("SECRET_KEY" in rec for rec in recommendations)
            assert any("HTTPS" in rec for rec in recommendations)
