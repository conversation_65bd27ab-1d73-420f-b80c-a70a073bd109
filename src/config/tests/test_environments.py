"""Unit tests for environment-specific configuration classes.

Tests configuration loading, validation, and environment-specific
settings for development, testing, and production environments.
"""

import os
from unittest.mock import patch

import pytest

from src.config.environments import (
    BaseConfig,
    DevelopmentConfig,
    ProductionConfig,
    TestingConfig,
    config_by_name,
    get_config,
    get_environment_info,
)


@pytest.mark.unit
class TestBaseConfig:
    """Test BaseConfig functionality.
    
    Tests default values, environment variable overrides,
    validation logic, and configuration serialization.
    """

    def setup_method(self):
        """Set up test environment variables."""
        self.original_env = {}
        self.test_env_vars = [
            "SECRET_KEY",
            "DATABASE_URL",
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "UPLOAD_FOLDER",
            "FLASK_ENV",
        ]

        # Store original values
        for var in self.test_env_vars:
            self.original_env[var] = os.getenv(var)

    def teardown_method(self):
        """Clean up environment variables."""
        for var in self.test_env_vars:
            if self.original_env[var] is None:
                os.environ.pop(var, None)
            else:
                os.environ[var] = self.original_env[var]

    def test_base_config_defaults(self):
        """Test BaseConfig default values."""
        # Clear environment variables to test defaults
        with patch.dict(os.environ, {}, clear=True):
            # Reload module to pick up clean environment
            import importlib
            from src.config import environments
            importlib.reload(environments)
            
            assert environments.BaseConfig.SECRET_KEY == "dev-key-change-in-production"
            # DATABASE_URL is now a property method, need to access it properly
            config_instance = environments.BaseConfig()
            assert "sqlite:///" in config_instance.DATABASE_URL
            assert "sora_poc.db" in config_instance.DATABASE_URL
            assert environments.BaseConfig.AZURE_OPENAI_API_VERSION == "2025-04-01-preview"
            assert environments.BaseConfig.AZURE_OPENAI_DEPLOYMENT_NAME == "sora"
            assert environments.BaseConfig.UPLOAD_FOLDER == "uploads"
            assert environments.BaseConfig.MAX_CONTENT_LENGTH == 104857600  # 100MB
            assert environments.BaseConfig.MAX_PROMPT_LENGTH == 500
            assert environments.BaseConfig.DEFAULT_VIDEO_DURATION == 2
            assert environments.BaseConfig.MAX_VIDEO_DURATION == 20
            assert environments.BaseConfig.LOG_LEVEL == "INFO"
            assert environments.BaseConfig.SQL_DEBUG is False
            assert environments.BaseConfig.RATE_LIMIT_ENABLED is True
            assert environments.BaseConfig.RATE_LIMIT_REQUESTS_PER_MINUTE == 60
            assert environments.BaseConfig.FILE_CLEANUP_ENABLED is True
            assert environments.BaseConfig.FILE_MAX_AGE_HOURS == 24
            assert environments.BaseConfig.HEALTH_CHECK_ENABLED is True
            assert environments.BaseConfig.METRICS_ENABLED is True

    def test_base_config_environment_override(self):
        """Test BaseConfig with environment variable overrides."""
        test_env = {
            "SECRET_KEY": "test-secret-key",
            "DATABASE_URL": "postgresql://test:test@localhost/test",
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
            "MAX_CONTENT_LENGTH": "52428800",  # 50MB
            "RATE_LIMIT_REQUESTS_PER_MINUTE": "30"
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            # Need to reload the class to pick up new environment variables
            import importlib
            from src.config import environments
            importlib.reload(environments)

            assert environments.BaseConfig.SECRET_KEY == "test-secret-key"
            # DATABASE_URL is now a property method, need to access it properly
            config_instance = environments.BaseConfig()
            assert config_instance.DATABASE_URL == "postgresql://test:test@localhost/test"
            assert (
                environments.BaseConfig.AZURE_OPENAI_ENDPOINT
                == "https://test.openai.azure.com/"
            )
            assert environments.BaseConfig.AZURE_OPENAI_API_KEY == "test-key"
            assert environments.BaseConfig.MAX_CONTENT_LENGTH == 52428800
            assert environments.BaseConfig.RATE_LIMIT_REQUESTS_PER_MINUTE == 30

    def test_base_config_boolean_parsing(self):
        """Test boolean environment variable parsing."""
        test_cases = [
            ("true", True),
            ("True", True),
            ("TRUE", True),
            ("false", False),
            ("False", False),
            ("FALSE", False),
            ("1", False),  # Only "true" should be True
            ("0", False),
            ("", False),
        ]

        for value, expected in test_cases:
            os.environ["RATE_LIMIT_ENABLED"] = value

            import importlib

            from src.config import environments

            importlib.reload(environments)

            assert environments.BaseConfig.RATE_LIMIT_ENABLED is expected

    def test_validate_required_settings_success(self):
        """Test successful validation with all required settings."""
        os.environ["SECRET_KEY"] = "secure-secret-key-32-characters"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        # Should not raise any exception
        environments.BaseConfig.validate_required_settings()

    def test_validate_required_settings_missing_secret_key(self):
        """Test validation fails with default secret key."""
        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
            # SECRET_KEY will use default value (dev-key-change-in-production)
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            import importlib
            from src.config import environments
            importlib.reload(environments)

            with pytest.raises(ValueError, match="SECRET_KEY must be set"):
                environments.BaseConfig.validate_required_settings()

    def test_validate_required_settings_missing_endpoint(self):
        """Test validation fails with missing Azure endpoint."""
        os.environ["SECRET_KEY"] = "secure-secret-key-32-characters"
        os.environ.pop("AZURE_OPENAI_ENDPOINT", None)
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        with pytest.raises(ValueError, match="AZURE_OPENAI_ENDPOINT is required"):
            environments.BaseConfig.validate_required_settings()

    def test_validate_required_settings_missing_auth(self):
        """Test validation fails with missing authentication."""
        os.environ["SECRET_KEY"] = "secure-secret-key-32-characters"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ.pop("AZURE_OPENAI_API_KEY", None)
        os.environ.pop("AZURE_CLIENT_ID", None)

        import importlib

        from src.config import environments

        importlib.reload(environments)

        with pytest.raises(
            ValueError, match="Either AZURE_OPENAI_API_KEY or Azure identity"
        ):
            environments.BaseConfig.validate_required_settings()

    def test_validate_required_settings_with_azure_identity(self):
        """Test validation succeeds with Azure identity credentials."""
        os.environ["SECRET_KEY"] = "secure-secret-key-32-characters"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ.pop("AZURE_OPENAI_API_KEY", None)
        os.environ["AZURE_CLIENT_ID"] = "test-client-id"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        # Should not raise any exception
        environments.BaseConfig.validate_required_settings()

    def test_to_dict(self):
        """Test configuration conversion to dictionary."""
        config_dict = BaseConfig.to_dict()

        # Should contain configuration attributes
        assert "SECRET_KEY" in config_dict
        assert "DATABASE_URL" in config_dict
        assert "AZURE_OPENAI_ENDPOINT" in config_dict
        assert "MAX_CONTENT_LENGTH" in config_dict

        # Should not contain methods or private attributes
        assert "validate_required_settings" not in config_dict
        assert "to_dict" not in config_dict
        assert "_private_attr" not in config_dict


@pytest.mark.unit
class TestDevelopmentConfig:
    """Test DevelopmentConfig functionality."""

    def test_development_config_attributes(self):
        """Test development-specific configuration."""
        # Clear environment to test defaults
        with patch.dict(os.environ, {}, clear=True):
            import importlib
            from src.config import environments
            importlib.reload(environments)
            
            assert environments.DevelopmentConfig.DEBUG is True
            assert environments.DevelopmentConfig.TESTING is False
            assert environments.DevelopmentConfig.LOG_LEVEL == "DEBUG"
            assert environments.DevelopmentConfig.SQL_DEBUG is True  # Default for development
            assert environments.DevelopmentConfig.SECRET_KEY == "dev-secret-key-not-for-production"
            assert environments.DevelopmentConfig.RATE_LIMIT_ENABLED is False  # Default for development

    def test_development_config_inheritance(self):
        """Test that DevelopmentConfig inherits from BaseConfig."""
        # Clear environment to test defaults
        with patch.dict(os.environ, {}, clear=True):
            import importlib
            from src.config import environments
            importlib.reload(environments)
            
            assert issubclass(environments.DevelopmentConfig, environments.BaseConfig)
            assert environments.DevelopmentConfig.AZURE_OPENAI_API_VERSION == "2025-04-01-preview"
            assert environments.DevelopmentConfig.MAX_PROMPT_LENGTH == 500

    def test_development_config_validation_skip(self):
        """Test that validation is skipped in development."""
        # Should not raise exception even without required settings
        DevelopmentConfig.validate_required_settings()


@pytest.mark.unit
class TestTestingConfig:
    """Test TestingConfig functionality."""

    def test_testing_config_attributes(self):
        """Test testing-specific configuration."""
        assert TestingConfig.DEBUG is False
        assert TestingConfig.TESTING is True
        assert TestingConfig.LOG_LEVEL == "WARNING"
        assert TestingConfig.DATABASE_URL == "sqlite:///:memory:"
        assert TestingConfig.SECRET_KEY == "test-secret-key"
        assert TestingConfig.UPLOAD_FOLDER == "/tmp/sora_test_uploads"
        assert TestingConfig.RATE_LIMIT_ENABLED is False
        assert TestingConfig.FILE_CLEANUP_ENABLED is False
        assert TestingConfig.HEALTH_CHECK_ENABLED is False
        assert TestingConfig.METRICS_ENABLED is False

    def test_testing_config_inheritance(self):
        """Test that TestingConfig inherits from BaseConfig."""
        assert issubclass(TestingConfig, BaseConfig)
        assert TestingConfig.MAX_PROMPT_LENGTH == 500

    def test_testing_config_validation_skip(self):
        """Test that validation is skipped in testing."""
        # Should not raise exception even without required settings
        TestingConfig.validate_required_settings()


@pytest.mark.unit
class TestProductionConfig:
    """Test ProductionConfig functionality."""

    def setup_method(self):
        """Set up environment for production tests."""
        self.original_env = {}
        self.test_env_vars = ["FLASK_ENV", "SECRET_KEY", "DATABASE_URL"]

        for var in self.test_env_vars:
            self.original_env[var] = os.getenv(var)

    def teardown_method(self):
        """Clean up environment variables."""
        for var in self.test_env_vars:
            if self.original_env[var] is None:
                os.environ.pop(var, None)
            else:
                os.environ[var] = self.original_env[var]

    def test_production_config_attributes(self):
        """Test production-specific configuration."""
        # Clear environment to test defaults
        with patch.dict(os.environ, {}, clear=True):
            import importlib
            from src.config import environments
            importlib.reload(environments)
            
            assert environments.ProductionConfig.DEBUG is False
            assert environments.ProductionConfig.TESTING is False
            assert (
                environments.ProductionConfig.DATABASE_URL
                == "postgresql://user:pass@localhost/sora_prod"
            )
            assert environments.ProductionConfig.SESSION_COOKIE_SECURE is True
            assert environments.ProductionConfig.SESSION_COOKIE_HTTPONLY is True
            assert environments.ProductionConfig.SESSION_COOKIE_SAMESITE == "Lax"
            assert environments.ProductionConfig.MAX_CONTENT_LENGTH == 52428800  # 50MB
            assert environments.ProductionConfig.RATE_LIMIT_REQUESTS_PER_MINUTE == 30

    def test_production_config_sqlalchemy_options(self):
        """Test SQLAlchemy engine options for production."""
        options = ProductionConfig.SQLALCHEMY_ENGINE_OPTIONS
        assert options["pool_size"] == 20
        assert options["max_overflow"] == 30
        assert options["pool_pre_ping"] is True
        assert options["pool_recycle"] == 3600

    def test_production_config_inheritance(self):
        """Test that ProductionConfig inherits from BaseConfig."""
        assert issubclass(ProductionConfig, BaseConfig)

    def test_production_config_validation_success(self):
        """Test successful production validation."""
        os.environ["SECRET_KEY"] = "production-secret-key-32-chars"
        os.environ["DATABASE_URL"] = "postgresql://user:pass@localhost/prod"
        os.environ["FLASK_ENV"] = "production"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        # Should not raise exception
        environments.ProductionConfig.validate_required_settings()

    def test_production_config_validation_dev_secret_key(self):
        """Test production validation fails with development secret key."""
        os.environ["SECRET_KEY"] = "dev-secret-key-not-for-production"
        os.environ["DATABASE_URL"] = "postgresql://user:pass@localhost/prod"
        os.environ["FLASK_ENV"] = "production"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        with pytest.raises(ValueError, match="Production SECRET_KEY must be set"):
            environments.ProductionConfig.validate_required_settings()

    def test_production_config_validation_sqlite_database(self):
        """Test production validation fails with SQLite database."""
        os.environ["SECRET_KEY"] = "production-secret-key-32-chars"
        os.environ["DATABASE_URL"] = "sqlite:///prod.db"
        os.environ["FLASK_ENV"] = "production"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        with pytest.raises(ValueError, match="Production requires PostgreSQL"):
            environments.ProductionConfig.validate_required_settings()

    def test_production_config_validation_wrong_flask_env(self):
        """Test production validation fails with wrong FLASK_ENV."""
        os.environ["SECRET_KEY"] = "production-secret-key-32-chars"
        os.environ["DATABASE_URL"] = "postgresql://user:pass@localhost/prod"
        os.environ["FLASK_ENV"] = "development"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com/"
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"

        import importlib

        from src.config import environments

        importlib.reload(environments)

        with pytest.raises(ValueError, match="FLASK_ENV must be set to 'production'"):
            environments.ProductionConfig.validate_required_settings()


@pytest.mark.unit
class TestConfigMapping:
    """Test configuration mapping and selection."""

    def test_config_by_name_mapping(self):
        """Test configuration name mapping."""
        assert config_by_name["development"] is DevelopmentConfig
        assert config_by_name["testing"] is TestingConfig
        assert config_by_name["production"] is ProductionConfig

    def test_get_config_development(self):
        """Test getting development configuration."""
        config = get_config("development")
        assert type(config).__name__ == "DevelopmentConfig"

    def test_get_config_testing(self):
        """Test getting testing configuration."""
        config = get_config("testing")
        assert type(config).__name__ == "TestingConfig"

    def test_get_config_production_valid(self):
        """Test getting production configuration with valid settings."""
        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            with patch.object(ProductionConfig, "validate_required_settings"):
                config = get_config("production")
                assert type(config).__name__ == "ProductionConfig"

    def test_get_config_invalid_name(self):
        """Test getting configuration with invalid name."""
        with pytest.raises(ValueError, match="Invalid configuration 'invalid'"):
            get_config("invalid")

    def test_get_config_case_insensitive(self):
        """Test that configuration names are case insensitive."""
        assert type(get_config("DEVELOPMENT")).__name__ == "DevelopmentConfig"
        assert type(get_config("Testing")).__name__ == "TestingConfig"

    def test_get_config_from_environment(self):
        """Test getting configuration from FLASK_ENV environment variable."""
        with patch.dict(os.environ, {"FLASK_ENV": "testing"}):
            config = get_config()  # No explicit name
            assert type(config).__name__ == "TestingConfig"

    def test_get_config_default_environment(self):
        """Test getting configuration with no FLASK_ENV set."""
        with patch.dict(os.environ, {}, clear=True):
            config = get_config()
            assert type(config).__name__ == "DevelopmentConfig"  # Default


@pytest.mark.unit
class TestEnvironmentInfo:
    """Test environment information functionality."""

    def setup_method(self):
        """Set up environment for tests."""
        self.original_env = os.getenv("FLASK_ENV")

    def teardown_method(self):
        """Clean up environment."""
        if self.original_env is None:
            os.environ.pop("FLASK_ENV", None)
        else:
            os.environ["FLASK_ENV"] = self.original_env

    def test_get_environment_info_development(self):
        """Test environment info for development."""
        os.environ["FLASK_ENV"] = "development"

        with patch.object(DevelopmentConfig, "validate_required_settings"):
            info = get_environment_info()

        assert info["environment"] == "development"
        assert info["debug"] is True
        assert info["testing"] is False
        assert info["database_type"] == "sqlite"
        assert info["rate_limiting"] is False

    def test_get_environment_info_testing(self):
        """Test environment info for testing."""
        os.environ["FLASK_ENV"] = "testing"

        info = get_environment_info()

        assert info["environment"] == "testing"
        assert info["debug"] is False
        assert info["testing"] is True
        assert info["database_type"] == "sqlite"
        assert info["rate_limiting"] is False
        assert info["health_checks"] is False
        assert info["metrics"] is False

    def test_get_environment_info_production(self):
        """Test environment info for production."""
        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            with patch("src.config.environments.get_config") as mock_get_config:
                mock_config = type(
                    "MockConfig",
                    (),
                    {
                        "DEBUG": False,
                        "TESTING": False,
                        "DATABASE_URL": "postgresql://user:pass@localhost/db",
                        "UPLOAD_FOLDER": "uploads",
                        "RATE_LIMIT_ENABLED": True,
                        "HEALTH_CHECK_ENABLED": True,
                        "METRICS_ENABLED": True,
                    },
                )
                mock_get_config.return_value = mock_config
                info = get_environment_info()

            assert info["environment"] == "production"
            assert info["debug"] is False
            assert info["testing"] is False
            assert info["database_type"] == "postgresql"
            assert info["rate_limiting"] is True

    def test_get_environment_info_database_type_detection(self):
        """Test database type detection in environment info."""
        # Test SQLite detection
        with patch("src.config.environments.get_config") as mock_get_config:
            mock_config = type(
                "MockConfig",
                (),
                {
                    "DATABASE_URL": "sqlite:///test.db",
                    "UPLOAD_FOLDER": "uploads",
                    "RATE_LIMIT_ENABLED": True,
                    "HEALTH_CHECK_ENABLED": True,
                    "METRICS_ENABLED": True,
                },
            )
            mock_get_config.return_value = mock_config

            info = get_environment_info()
            assert info["database_type"] == "sqlite"

        # Test PostgreSQL detection
        with patch("src.config.environments.get_config") as mock_get_config:
            mock_config = type(
                "MockConfig",
                (),
                {
                    "DATABASE_URL": "postgresql://user:pass@localhost/db",
                    "UPLOAD_FOLDER": "uploads",
                    "RATE_LIMIT_ENABLED": True,
                    "HEALTH_CHECK_ENABLED": True,
                    "METRICS_ENABLED": True,
                },
            )
            mock_get_config.return_value = mock_config

            info = get_environment_info()
            assert info["database_type"] == "postgresql"
