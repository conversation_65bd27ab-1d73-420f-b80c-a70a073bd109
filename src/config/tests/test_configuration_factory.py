"""Comprehensive unit tests for extended ConfigurationFactory with provider support.

Tests cover provider configuration factory methods, environment variable respect patterns,
and integration with existing configuration system.
"""

import os
from unittest.mock import MagicMock, patch

import pytest

from src.config.factory import ConfigurationFactory, ProviderConfigurationFactory
from src.config.veo3_settings import Veo3ProviderConfig, Veo3Settings


class TestConfigurationFactoryProviderMethods:
    """Test suite for ConfigurationFactory provider-specific methods."""

    def test_get_veo3_settings(self):
        """Test get_veo3_settings method returns cached settings."""
        with patch("src.config.factory.get_cached_veo3_settings") as mock_cached:
            mock_settings = MagicMock(spec=Veo3Settings)
            mock_cached.return_value = mock_settings

            result = ConfigurationFactory.get_veo3_settings()

            assert result is mock_settings
            mock_cached.assert_called_once()

    def test_get_veo3_settings_with_config_name(self):
        """Test get_veo3_settings ignores config_name parameter."""
        with patch("src.config.factory.get_cached_veo3_settings") as mock_cached:
            mock_settings = MagicMock(spec=Veo3Settings)
            mock_cached.return_value = mock_settings

            result = ConfigurationFactory.get_veo3_settings(config_name="production")

            # config_name should be ignored for Veo3Settings
            assert result is mock_settings
            mock_cached.assert_called_once()

    def test_create_veo3_config_with_environment_defaults(self):
        """Test create_veo3_config respects environment variables."""
        test_env = {
            "GOOGLE_PROJECT_ID": "test-project-123",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "test-client",
            "VEO3_TIMEOUT": "600",
            "VEO3_MAX_RETRIES": "5",
        }

        with patch.dict(os.environ, test_env):
            config = ConfigurationFactory.create_veo3_config()

            assert isinstance(config, Veo3ProviderConfig)
            assert config.project_id == "test-project-123"
            assert config.use_mock is True
            assert config.client_id == "test-client"
            assert config.timeout == 600
            assert config.max_retries == 5

    def test_create_veo3_config_with_overrides(self):
        """Test create_veo3_config applies overrides over environment variables."""
        test_env = {
            "GOOGLE_PROJECT_ID": "env-project-id",
            "USE_MOCK_VEO": "true",
            "VEO3_TIMEOUT": "300",
        }

        with patch.dict(os.environ, test_env):
            config = ConfigurationFactory.create_veo3_config(
                project_id="override-project-id", use_mock=False, timeout=900
            )

            # Overrides should take precedence
            assert config.project_id == "override-project-id"
            assert config.use_mock is False
            assert config.timeout == 900

    def test_create_veo3_config_missing_project_id(self):
        """Test create_veo3_config raises error when project_id is missing."""
        # Clear any existing GOOGLE_PROJECT_ID
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="GOOGLE_PROJECT_ID is required"):
                ConfigurationFactory.create_veo3_config()

    def test_create_veo3_config_secure_secret_handling(self):
        """Test secure handling of client secret."""
        test_env = {
            "GOOGLE_PROJECT_ID": "test-project-123",
            "GOOGLE_CLIENT_SECRET": "env-secret",
        }

        with patch.dict(os.environ, test_env):
            # Test environment secret is used
            config = ConfigurationFactory.create_veo3_config()
            assert config.client_secret == "env-secret"

            # Test override secret takes precedence
            config_override = ConfigurationFactory.create_veo3_config(
                client_secret="override-secret"
            )
            assert config_override.client_secret == "override-secret"

    def test_get_provider_availability(self):
        """Test get_provider_availability returns availability status."""
        with patch.object(
            ConfigurationFactory, "get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.get_provider_availability.return_value = {
                "azure_sora": True,
                "google_veo3": True,
            }
            mock_get_settings.return_value = mock_settings

            availability = ConfigurationFactory.get_provider_availability()

            assert availability["azure_sora"] is True
            assert availability["google_veo3"] is True
            mock_settings.get_provider_availability.assert_called_once()

    def test_get_default_provider(self):
        """Test get_default_provider returns configured default."""
        with patch.object(
            ConfigurationFactory, "get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.DEFAULT_PROVIDER = "google_veo3"
            mock_get_settings.return_value = mock_settings

            default_provider = ConfigurationFactory.get_default_provider()

            assert default_provider == "google_veo3"

    def test_create_provider_config_azure_sora(self):
        """Test create_provider_config for Azure Sora provider."""
        mock_azure_config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-key",
            "api_version": "preview",
            "deployment_name": "sora",
        }

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config.copy()

            config = ConfigurationFactory.create_provider_config("azure_sora")

            assert config == mock_azure_config
            mock_get_azure.assert_called_once()

    def test_create_provider_config_azure_sora_with_overrides(self):
        """Test create_provider_config for Azure Sora with overrides."""
        mock_azure_config = {
            "endpoint": "https://test.azure.com",
            "api_key": "test-key",
            "api_version": "preview",
        }

        with patch.object(ConfigurationFactory, "get_azure_config") as mock_get_azure:
            mock_get_azure.return_value = mock_azure_config.copy()

            config = ConfigurationFactory.create_provider_config(
                "azure_sora", api_version="2025-01-01"
            )

            assert config["api_version"] == "2025-01-01"  # Override applied
            assert config["endpoint"] == "https://test.azure.com"  # Original value

    def test_create_provider_config_google_veo3(self):
        """Test create_provider_config for Google Veo3 provider."""
        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env):
            config = ConfigurationFactory.create_provider_config("google_veo3")

            assert isinstance(config, dict)
            assert config["project_id"] == "test-project-123"
            assert "use_mock" in config
            assert "timeout" in config

    def test_create_provider_config_unknown_provider(self):
        """Test create_provider_config raises error for unknown provider."""
        with pytest.raises(ValueError, match="Unknown provider: unknown_provider"):
            ConfigurationFactory.create_provider_config("unknown_provider")

    def test_validate_provider_configuration(self):
        """Test validate_provider_configuration delegates to settings."""
        mock_validation_result = {"provider": "azure_sora", "valid": True, "errors": []}

        with patch.object(
            ConfigurationFactory, "get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.validate_provider_configuration.return_value = (
                mock_validation_result
            )
            mock_get_settings.return_value = mock_settings

            result = ConfigurationFactory.validate_provider_configuration("azure_sora")

            assert result == mock_validation_result
            mock_settings.validate_provider_configuration.assert_called_once_with(
                "azure_sora"
            )

    def test_get_provider_performance_config_azure_sora(self):
        """Test get_provider_performance_config for Azure Sora."""
        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_base_config = MagicMock()
            mock_get_base.return_value = mock_base_config

            perf_config = ConfigurationFactory.get_provider_performance_config(
                "azure_sora"
            )

            assert "timeouts" in perf_config
            assert "retry_config" in perf_config
            assert "targets" in perf_config
            assert perf_config["timeouts"]["api_timeout"] == 300
            assert perf_config["targets"]["settings_load_time_ms"] == 50

    def test_get_provider_performance_config_google_veo3(self):
        """Test get_provider_performance_config for Google Veo3."""
        mock_perf_config = {
            "timeouts": {"api_timeout": 300},
            "retry_config": {"max_retries": 3},
            "targets": {"settings_load_time_ms": 50},
        }

        with patch.object(
            ConfigurationFactory, "get_veo3_settings"
        ) as mock_get_settings:
            mock_settings = MagicMock()
            mock_settings.get_performance_config.return_value = mock_perf_config
            mock_get_settings.return_value = mock_settings

            perf_config = ConfigurationFactory.get_provider_performance_config(
                "google_veo3"
            )

            assert perf_config == mock_perf_config
            mock_settings.get_performance_config.assert_called_once()

    def test_get_provider_performance_config_unknown_provider(self):
        """Test get_provider_performance_config raises error for unknown provider."""
        with pytest.raises(ValueError, match="Unknown provider: unknown_provider"):
            ConfigurationFactory.get_provider_performance_config("unknown_provider")


class TestProviderConfigurationFactory:
    """Test suite for ProviderConfigurationFactory class."""

    @patch("src.config.factory.ConfigurationService")
    def test_create_environment_aware_config_local(self, mock_config_service):
        """Test environment-aware config creation for local environment."""
        mock_config_service._detect_deployment_type.return_value = "local"

        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env):
            with patch.object(
                ConfigurationFactory, "create_provider_config"
            ) as mock_create:
                mock_create.return_value = {"project_id": "test-project-123"}

                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )

                # Should call with local environment defaults
                mock_create.assert_called_once()
                args, kwargs = mock_create.call_args
                assert args[0] == "google_veo3"
                assert kwargs["use_mock"] is True  # Local default
                assert kwargs["timeout"] == 120  # Local default
                assert kwargs["max_retries"] == 2  # Local default

    @patch("src.config.factory.ConfigurationService")
    def test_create_environment_aware_config_docker(self, mock_config_service):
        """Test environment-aware config creation for Docker environment."""
        mock_config_service._detect_deployment_type.return_value = "docker"

        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env):
            with patch.object(
                ConfigurationFactory, "create_provider_config"
            ) as mock_create:
                mock_create.return_value = {"project_id": "test-project-123"}

                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )

                # Should call with Docker environment defaults
                args, kwargs = mock_create.call_args
                assert kwargs["timeout"] == 600  # Docker default
                assert kwargs["max_retries"] == 5  # Docker default

    @patch("src.config.factory.ConfigurationService")
    def test_create_environment_aware_config_production(self, mock_config_service):
        """Test environment-aware config creation for production environment."""
        mock_config_service._detect_deployment_type.return_value = "production"

        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env):
            with patch.object(
                ConfigurationFactory, "create_provider_config"
            ) as mock_create:
                mock_create.return_value = {"project_id": "test-project-123"}

                config = ProviderConfigurationFactory.create_environment_aware_config(
                    "google_veo3"
                )

                # Should call with production environment defaults
                args, kwargs = mock_create.call_args
                assert kwargs["use_mock"] is False  # Production default
                assert kwargs["timeout"] == 300  # Production default
                assert kwargs["max_retries"] == 3  # Production default

    def test_create_environment_aware_config_with_overrides(self):
        """Test environment-aware config with explicit overrides."""
        with patch("src.config.factory.ConfigurationService") as mock_service:
            mock_service._detect_deployment_type.return_value = "local"

            test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

            with patch.dict(os.environ, test_env):
                with patch.object(
                    ConfigurationFactory, "create_provider_config"
                ) as mock_create:
                    mock_create.return_value = {"project_id": "test-project-123"}

                    config = (
                        ProviderConfigurationFactory.create_environment_aware_config(
                            "google_veo3",
                            timeout=999,  # Explicit override
                            custom_param="custom_value",  # Custom parameter
                        )
                    )

                    # Explicit overrides should take precedence over environment defaults
                    args, kwargs = mock_create.call_args
                    assert kwargs["timeout"] == 999  # Override value
                    assert kwargs["custom_param"] == "custom_value"  # Custom param
                    assert kwargs["use_mock"] is True  # Environment default (local)

    def test_get_optimal_provider_config_default_available(self):
        """Test optimal provider selection when default provider is available."""
        mock_availability = {"azure_sora": True, "google_veo3": True}
        mock_default_provider = "google_veo3"

        with patch.object(
            ConfigurationFactory, "get_provider_availability"
        ) as mock_avail:
            with patch.object(
                ConfigurationFactory, "get_default_provider"
            ) as mock_default:
                with patch.object(
                    ProviderConfigurationFactory, "create_environment_aware_config"
                ) as mock_create:
                    mock_avail.return_value = mock_availability
                    mock_default.return_value = mock_default_provider
                    mock_create.return_value = {"project_id": "test-project"}

                    config = ProviderConfigurationFactory.get_optimal_provider_config()

                    # Should select the default provider
                    mock_create.assert_called_once_with("google_veo3")
                    assert config["selected_provider"] == "google_veo3"
                    assert config["provider_availability"] == mock_availability

    def test_get_optimal_provider_config_fallback_azure(self):
        """Test optimal provider selection falls back to Azure when default unavailable."""
        mock_availability = {
            "azure_sora": True,
            "google_veo3": False,  # Default unavailable
        }
        mock_default_provider = "google_veo3"

        with patch.object(
            ConfigurationFactory, "get_provider_availability"
        ) as mock_avail:
            with patch.object(
                ConfigurationFactory, "get_default_provider"
            ) as mock_default:
                with patch.object(
                    ProviderConfigurationFactory, "create_environment_aware_config"
                ) as mock_create:
                    mock_avail.return_value = mock_availability
                    mock_default.return_value = mock_default_provider
                    mock_create.return_value = {"endpoint": "https://azure.com"}

                    config = ProviderConfigurationFactory.get_optimal_provider_config()

                    # Should fallback to Azure Sora
                    mock_create.assert_called_once_with("azure_sora")
                    assert config["selected_provider"] == "azure_sora"

    def test_get_optimal_provider_config_no_providers_available(self):
        """Test optimal provider selection raises error when no providers available."""
        mock_availability = {"azure_sora": False, "google_veo3": False}

        with patch.object(
            ConfigurationFactory, "get_provider_availability"
        ) as mock_avail:
            with patch.object(
                ConfigurationFactory, "get_default_provider"
            ) as mock_default:
                mock_avail.return_value = mock_availability
                mock_default.return_value = "azure_sora"

                with pytest.raises(
                    ValueError, match="No video generation providers are available"
                ):
                    ProviderConfigurationFactory.get_optimal_provider_config()


class TestConfigurationFactoryIntegration:
    """Integration tests for ConfigurationFactory with existing methods."""

    def test_existing_methods_still_work(self):
        """Test that existing ConfigurationFactory methods are not broken."""
        # Test existing get_base_config method
        with patch("src.config.factory.get_config") as mock_get_config:
            mock_config = MagicMock()
            mock_get_config.return_value = mock_config

            config = ConfigurationFactory.get_base_config()

            assert config is mock_config
            mock_get_config.assert_called_once_with(None)

    def test_existing_get_azure_config_integration(self):
        """Test integration with existing get_azure_config method."""
        # This method should still work as before
        mock_base_config = MagicMock()
        mock_base_config.AZURE_OPENAI_ENDPOINT = "https://test.azure.com"
        mock_base_config.AZURE_OPENAI_API_KEY = "test-key"
        mock_base_config.AZURE_OPENAI_API_VERSION = "preview"
        mock_base_config.AZURE_OPENAI_DEPLOYMENT_NAME = "sora"

        with patch.object(ConfigurationFactory, "get_base_config") as mock_get_base:
            mock_get_base.return_value = mock_base_config

            azure_config = ConfigurationFactory.get_azure_config()

            assert azure_config["endpoint"] == "https://test.azure.com"
            assert azure_config["api_key"] == "test-key"
            assert azure_config["api_version"] == "preview"
            assert azure_config["deployment_name"] == "sora"

    def test_clear_cache_compatibility(self):
        """Test that clear_cache method works with new functionality."""
        # Should not raise any errors
        ConfigurationFactory.clear_cache()

        # Should still return None as documented
        assert ConfigurationFactory.clear_cache() is None


class TestProviderConfigurationFactoryErrorHandling:
    """Test error handling in ProviderConfigurationFactory."""

    def test_create_environment_aware_config_invalid_provider(self):
        """Test error handling for invalid provider in environment-aware config."""
        with patch("src.config.factory.ConfigurationService") as mock_service:
            mock_service._detect_deployment_type.return_value = "local"

            with patch.object(
                ConfigurationFactory, "create_provider_config"
            ) as mock_create:
                mock_create.side_effect = ValueError("Unknown provider: invalid")

                with pytest.raises(ValueError, match="Unknown provider: invalid"):
                    ProviderConfigurationFactory.create_environment_aware_config(
                        "invalid"
                    )

    @patch("src.config.factory.logger")
    def test_logging_in_provider_config_creation(self, mock_logger):
        """Test proper logging in provider configuration creation."""
        test_env = {"GOOGLE_PROJECT_ID": "test-project-123"}

        with patch.dict(os.environ, test_env):
            config = ConfigurationFactory.create_veo3_config()

            # Verify logging calls were made
            mock_logger.info.assert_called()

            # Check that log messages contain expected content
            log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
            assert any(
                "Creating Veo3 provider configuration" in msg for msg in log_calls
            )
            assert any("Veo3 config created" in msg for msg in log_calls)
