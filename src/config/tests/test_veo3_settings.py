"""Comprehensive unit tests for Veo3Settings configuration.

Tests cover Pydantic v2 validation, environment variable loading, caching,
and configuration factory integration with comprehensive coverage targeting.
"""

import os
from unittest.mock import patch
from uuid import uuid4

import pytest
from pydantic import ValidationError
from pydantic_settings import SettingsConfigDict

from src.config.veo3_settings import (
    Veo3ProviderConfig,
    Veo3Settings,
    get_cached_veo3_settings,
    validate_veo3_environment,
)


class TestVeo3Settings:
    """Test suite for Veo3Settings with comprehensive validation coverage."""

    def test_veo3_settings_initialization_defaults(self):
        """Test Veo3Settings initialization with default values."""
        settings = Veo3Settings()

        # Test default values match specifications
        assert settings.USE_MOCK_VEO is True
        assert settings.DEFAULT_PROVIDER == "azure_sora"
        assert settings.VEO3_TIMEOUT == 300
        assert settings.VEO3_MAX_RETRIES == 3
        assert settings.VEO3_RETRY_DELAY == 2
        assert settings.VEO3_GENERATION_TIMEOUT == 1800
        assert settings.VEO3_MODEL_VERSION == "veo3-v1"
        assert settings.VEO3_RATE_LIMIT_RPM == 30

        # Test optional fields default to None
        assert settings.GOOGLE_PROJECT_ID is None
        assert settings.GOOGLE_CLIENT_ID is None
        assert settings.GOOGLE_CLIENT_SECRET is None

    def test_veo3_settings_environment_variable_loading(self):
        """Test environment variables are properly loaded and typed."""
        test_id = str(uuid4())
        test_env = {
            "USE_MOCK_VEO": "false",
            "DEFAULT_PROVIDER": "google_veo3",
            "GOOGLE_PROJECT_ID": f"test-project-{test_id}",
            "GOOGLE_CLIENT_ID": f"test-client-{test_id}",
            "GOOGLE_CLIENT_SECRET": f"test-secret-{test_id}",
            "VEO3_TIMEOUT": "600",
            "VEO3_MAX_RETRIES": "5",
            "VEO3_RETRY_DELAY": "3",
            "VEO3_GENERATION_TIMEOUT": "3600",
            "VEO3_MODEL_VERSION": "veo3-preview",
            "VEO3_RATE_LIMIT_RPM": "60",
        }

        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()

            # Test boolean conversion
            assert settings.USE_MOCK_VEO is False

            # Test string literal validation
            assert settings.DEFAULT_PROVIDER == "google_veo3"

            # Test string fields
            assert settings.GOOGLE_PROJECT_ID == f"test-project-{test_id}"
            assert settings.GOOGLE_CLIENT_ID == f"test-client-{test_id}"
            assert (
                settings.GOOGLE_CLIENT_SECRET.get_secret_value()
                == f"test-secret-{test_id}"
            )

            # Test integer conversion and validation
            assert settings.VEO3_TIMEOUT == 600
            assert settings.VEO3_MAX_RETRIES == 5
            assert settings.VEO3_RETRY_DELAY == 3
            assert settings.VEO3_GENERATION_TIMEOUT == 3600
            assert settings.VEO3_RATE_LIMIT_RPM == 60

            # Test validated string field
            assert settings.VEO3_MODEL_VERSION == "veo3-preview"

    def test_google_project_id_validation(self):
        """Test Google project ID validation rules."""
        # Test valid project IDs
        with patch.dict(os.environ, {"GOOGLE_PROJECT_ID": "test-project-123"}):
            settings = Veo3Settings()
            assert settings.GOOGLE_PROJECT_ID == "test-project-123"

        with patch.dict(os.environ, {"GOOGLE_PROJECT_ID": "my-test-project"}):
            settings = Veo3Settings()
            assert settings.GOOGLE_PROJECT_ID == "my-test-project"

        # Test case insensitive conversion
        with patch.dict(os.environ, {"GOOGLE_PROJECT_ID": "Test-Project-ABC"}):
            settings = Veo3Settings()
            assert settings.GOOGLE_PROJECT_ID == "test-project-abc"

        # Test invalid project IDs raise ValidationError
        invalid_project_ids = [
            "ab",  # Too short
            "a" * 31,  # Too long
            "-invalid-start",  # Starts with hyphen
            "invalid-end-",  # Ends with hyphen
            "invalid@project",  # Invalid characters
            "invalid project",  # Spaces not allowed
        ]

        for invalid_id in invalid_project_ids:
            with patch.dict(os.environ, {"GOOGLE_PROJECT_ID": invalid_id}):
                with pytest.raises(ValidationError):
                    Veo3Settings()

    def test_model_version_validation(self):
        """Test Veo3 model version validation."""
        valid_versions = ["veo3-v1", "veo3-preview", "veo3-beta"]

        for version in valid_versions:
            with patch.dict(os.environ, {"VEO3_MODEL_VERSION": version}):
                settings = Veo3Settings()
                assert settings.VEO3_MODEL_VERSION == version

        # Test invalid model version
        with patch.dict(os.environ, {"VEO3_MODEL_VERSION": "invalid-version"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

    def test_integer_field_validation(self):
        """Test integer field validation with min/max constraints."""
        # Test VEO3_TIMEOUT constraints (30-1800 seconds)
        with patch.dict(os.environ, {"VEO3_TIMEOUT": "29"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

        with patch.dict(os.environ, {"VEO3_TIMEOUT": "1801"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

        # Test VEO3_MAX_RETRIES constraints (0-10)
        with patch.dict(os.environ, {"VEO3_MAX_RETRIES": "-1"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

        with patch.dict(os.environ, {"VEO3_MAX_RETRIES": "11"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

        # Test VEO3_GENERATION_TIMEOUT constraints (300-7200 seconds)
        with patch.dict(os.environ, {"VEO3_GENERATION_TIMEOUT": "299"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

        with patch.dict(os.environ, {"VEO3_GENERATION_TIMEOUT": "7201"}):
            with pytest.raises(ValidationError):
                Veo3Settings()

    def test_get_provider_availability(self):
        """Test provider availability detection logic."""
        settings = Veo3Settings()

        # Test mock mode availability
        availability = settings.get_provider_availability()
        assert availability["azure_sora"] is True  # Always available
        assert availability["google_veo3"] is True  # Mock mode enabled by default

        # Test real mode with no authentication
        with patch.dict(os.environ, {"USE_MOCK_VEO": "false"}):
            settings = Veo3Settings()
            availability = settings.get_provider_availability()
            assert availability["google_veo3"] is False  # No auth configured

        # Test real mode with authentication
        test_env = {
            "USE_MOCK_VEO": "false",
            "GOOGLE_PROJECT_ID": "test-project-123",
            "GOOGLE_CLIENT_ID": "test-client-id",
        }
        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()
            availability = settings.get_provider_availability()
            assert availability["google_veo3"] is True  # Auth configured

    def test_validate_provider_configuration_azure_sora(self):
        """Test Azure Sora provider configuration validation."""
        settings = Veo3Settings()

        # Mock ConfigurationService for Azure config
        with patch("src.config.veo3_settings.ConfigurationService") as mock_service:
            mock_service.get.side_effect = lambda key, default=None: {
                "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com",
                "AZURE_OPENAI_API_KEY": "test-api-key",
                "AZURE_OPENAI_API_VERSION": "preview",
            }.get(key, default)

            result = settings.validate_provider_configuration("azure_sora")

            assert result["provider"] == "azure_sora"
            assert result["valid"] is True
            assert len(result["errors"]) == 0
            assert result["configuration"]["endpoint"] is True
            assert result["configuration"]["api_key"] is True
            assert result["configuration"]["api_version"] == "preview"

    def test_validate_provider_configuration_google_veo3(self):
        """Test Google Veo3 provider configuration validation."""
        test_env = {"GOOGLE_PROJECT_ID": "test-project-123", "USE_MOCK_VEO": "true"}

        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()
            result = settings.validate_provider_configuration("google_veo3")

            assert result["provider"] == "google_veo3"
            assert result["valid"] is True
            assert len(result["errors"]) == 0
            assert result["configuration"]["project_id"] is True
            assert result["configuration"]["mock_mode"] is True

    def test_validate_provider_configuration_invalid_provider(self):
        """Test validation with invalid provider name."""
        settings = Veo3Settings()
        result = settings.validate_provider_configuration("invalid_provider")

        assert result["provider"] == "invalid_provider"
        assert result["valid"] is False
        assert "Unknown provider: invalid_provider" in result["errors"]

    def test_get_performance_config(self):
        """Test performance configuration retrieval."""
        settings = Veo3Settings()
        perf_config = settings.get_performance_config()

        # Test structure
        assert "timeouts" in perf_config
        assert "retry_config" in perf_config
        assert "rate_limiting" in perf_config
        assert "targets" in perf_config

        # Test timeout values
        assert perf_config["timeouts"]["api_timeout"] == 300
        assert perf_config["timeouts"]["generation_timeout"] == 1800
        assert perf_config["timeouts"]["retry_delay"] == 2

        # Test retry configuration
        assert perf_config["retry_config"]["max_retries"] == 3
        assert perf_config["retry_config"]["retry_delay"] == 2

        # Test rate limiting
        assert perf_config["rate_limiting"]["requests_per_minute"] == 30

        # Test performance targets
        assert perf_config["targets"]["settings_load_time_ms"] == 50
        assert perf_config["targets"]["factory_creation_time_ms"] == 10
        assert perf_config["targets"]["validation_time_ms"] == 100


class TestVeo3ProviderConfig:
    """Test suite for Veo3ProviderConfig class."""

    def test_veo3_provider_config_initialization(self):
        """Test Veo3ProviderConfig initialization."""
        config = Veo3ProviderConfig(
            project_id="test-project-123",
            use_mock=True,
            client_id="test-client",
            client_secret="test-secret",
            model_version="veo3-v1",
            timeout=300,
            max_retries=3,
            retry_delay=2,
            generation_timeout=1800,
            rate_limit_rpm=30,
        )

        assert config.project_id == "test-project-123"
        assert config.use_mock is True
        assert config.client_id == "test-client"
        assert config.client_secret == "test-secret"
        assert config.model_version == "veo3-v1"
        assert config.timeout == 300
        assert config.max_retries == 3
        assert config.retry_delay == 2
        assert config.generation_timeout == 1800
        assert config.rate_limit_rpm == 30

    def test_from_settings_success(self):
        """Test creating Veo3ProviderConfig from valid settings."""
        test_env = {
            "GOOGLE_PROJECT_ID": "test-project-123",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "test-client",
            "GOOGLE_CLIENT_SECRET": "test-secret",
            "VEO3_MODEL_VERSION": "veo3-preview",
            "VEO3_TIMEOUT": "600",
        }

        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()
            config = Veo3ProviderConfig.from_settings(settings)

            assert config.project_id == "test-project-123"
            assert config.use_mock is True
            assert config.client_id == "test-client"
            assert config.client_secret == "test-secret"
            assert config.model_version == "veo3-preview"
            assert config.timeout == 600

    def test_from_settings_missing_project_id(self):
        """Test error when project ID is missing."""
        settings = Veo3Settings()

        with pytest.raises(ValueError, match="GOOGLE_PROJECT_ID is required"):
            Veo3ProviderConfig.from_settings(settings)

    def test_to_dict_excludes_sensitive_data(self):
        """Test to_dict excludes sensitive information."""
        config = Veo3ProviderConfig(
            project_id="test-project-123", client_secret="sensitive-secret"
        )

        config_dict = config.to_dict()

        assert "project_id" in config_dict
        assert "client_secret" not in config_dict  # Should be excluded
        assert config_dict["project_id"] == "test-project-123"


class TestVeo3SettingsCaching:
    """Test suite for Veo3Settings caching functionality."""

    def test_get_cached_veo3_settings_returns_same_instance(self):
        """Test caching returns same instance on multiple calls."""
        # Clear cache first
        get_cached_veo3_settings.cache_clear()

        settings1 = get_cached_veo3_settings()
        settings2 = get_cached_veo3_settings()

        # Should return the same cached instance
        assert settings1 is settings2

    def test_cache_clear_forces_new_instance(self):
        """Test cache clearing forces new instance creation."""
        settings1 = get_cached_veo3_settings()

        # Clear cache and get new instance
        get_cached_veo3_settings.cache_clear()
        settings2 = get_cached_veo3_settings()

        # Should be different instances after cache clear
        assert settings1 is not settings2


class TestValidateVeo3Environment:
    """Test suite for validate_veo3_environment function."""

    def test_validate_veo3_environment_success(self):
        """Test successful environment validation."""
        test_env = {
            "GOOGLE_PROJECT_ID": "test-project-123",
            "USE_MOCK_VEO": "true",
            "DEFAULT_PROVIDER": "azure_sora",
        }

        with patch.dict(os.environ, test_env):
            # Clear cache to ensure fresh settings
            get_cached_veo3_settings.cache_clear()

            result = validate_veo3_environment()

            assert "test_id" in result
            assert result["valid"] is True
            assert isinstance(result["errors"], list)
            assert isinstance(result["warnings"], list)
            assert "configuration" in result
            assert result["configuration"]["settings_loaded"] is True

    def test_validate_veo3_environment_with_warnings(self):
        """Test environment validation with warnings."""
        test_env = {
            "DEFAULT_PROVIDER": "google_veo3",
            "USE_MOCK_VEO": "true",
            # Missing GOOGLE_PROJECT_ID will make Veo3 unavailable
        }

        with patch.dict(os.environ, test_env):
            get_cached_veo3_settings.cache_clear()

            result = validate_veo3_environment()

            # Should have warnings about default provider being unavailable
            assert len(result["warnings"]) > 0
            assert any(
                "google_veo3 but Veo3 is not available" in warning
                for warning in result["warnings"]
            )

    @patch("src.config.veo3_settings.get_cached_veo3_settings")
    def test_validate_veo3_environment_exception_handling(self, mock_settings):
        """Test exception handling in environment validation."""
        mock_settings.side_effect = Exception("Settings loading failed")

        result = validate_veo3_environment()

        assert result["valid"] is False
        assert len(result["errors"]) > 0
        assert "Validation failed: Settings loading failed" in result["errors"]


class TestVeo3SettingsModelConfig:
    """Test suite for Veo3Settings model configuration."""

    def test_model_config_attributes(self):
        """Test Pydantic model configuration attributes."""
        settings = Veo3Settings()
        config = settings.model_config

        assert isinstance(config, SettingsConfigDict)
        assert config["env_file"] == ".env"
        assert config["env_file_encoding"] == "utf-8"
        assert config["case_sensitive"] is False
        assert config["extra"] == "ignore"
        assert config["validate_default"] is True
        assert config["env_nested_delimiter"] == "__"

    def test_protected_namespaces_configuration(self):
        """Test protected namespaces are properly configured."""
        settings = Veo3Settings()
        config = settings.model_config

        assert "protected_namespaces" in config
        assert "model_" in config["protected_namespaces"]


class TestVeo3SettingsIntegration:
    """Integration tests for Veo3Settings with ConfigurationService."""

    @patch("src.config.veo3_settings.ConfigurationService._ensure_initialized")
    def test_configuration_service_integration(self, mock_ensure_initialized):
        """Test integration with ConfigurationService initialization."""
        # Create settings instance
        settings = Veo3Settings()

        # ConfigurationService should be initialized during validation
        # This is handled by the customise_sources method
        assert settings is not None

        # The _ensure_initialized should be called during source customization
        # but since we're mocking it, we can't verify the call here
        # This test ensures no exceptions are raised during integration

    def test_environment_variable_precedence(self):
        """Test environment variable precedence over defaults."""
        test_env = {
            "USE_MOCK_VEO": "false",
            "VEO3_TIMEOUT": "900",
            "VEO3_RATE_LIMIT_RPM": "15",
        }

        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()

            # Environment variables should override defaults
            assert settings.USE_MOCK_VEO is False
            assert settings.VEO3_TIMEOUT == 900
            assert settings.VEO3_RATE_LIMIT_RPM == 15

            # Non-overridden defaults should remain
            assert settings.VEO3_MAX_RETRIES == 3  # Default
            assert settings.DEFAULT_PROVIDER == "azure_sora"  # Default
