"""Performance tests for F4 Environment Configuration module.

Tests validate performance targets: settings <50ms, factory <10ms, validation <100ms
as specified in the PRP requirements.
"""

import os
import statistics
import time
from unittest.mock import patch

import pytest

from src.config.environment import EnvironmentDetector, EnvironmentOptimizer
from src.config.factory import ConfigurationFactory, ProviderConfigurationFactory
from src.config.veo3_settings import Veo3Settings, get_cached_veo3_settings


class TestSettingsLoadingPerformance:
    """Performance tests for settings loading (<50ms target)."""

    def test_veo3_settings_loading_performance_target(self):
        """Test Veo3Settings loading meets <50ms target."""
        test_env = {
            "GOOGLE_PROJECT_ID": "performance-test-project",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "test-client",
            "VEO3_TIMEOUT": "300",
            "VEO3_MAX_RETRIES": "3",
        }

        with patch.dict(os.environ, test_env):
            # Measure multiple iterations for statistical accuracy
            load_times = []
            iterations = 10

            for i in range(iterations):
                start_time = time.perf_counter()
                settings = Veo3Settings()
                end_time = time.perf_counter()

                load_time_ms = (end_time - start_time) * 1000
                load_times.append(load_time_ms)

                # Verify settings loaded correctly
                assert settings.GOOGLE_PROJECT_ID == "performance-test-project"
                assert settings.USE_MOCK_VEO is True
                assert settings.VEO3_TIMEOUT == 300

            # Calculate statistics
            avg_time = statistics.mean(load_times)
            max_time = max(load_times)
            min_time = min(load_times)

            # Performance assertions
            assert avg_time < 50, (
                f"Average settings loading time {avg_time:.2f}ms exceeds 50ms target"
            )
            assert max_time < 100, (
                f"Maximum settings loading time {max_time:.2f}ms exceeds reasonable limit"
            )

            print(
                f"Settings loading performance: avg={avg_time:.2f}ms, min={min_time:.2f}ms, max={max_time:.2f}ms"
            )

    def test_cached_settings_performance(self):
        """Test cached settings loading performance is even faster."""
        test_env = {
            "GOOGLE_PROJECT_ID": "cache-performance-test",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            # Clear cache first
            get_cached_veo3_settings.cache_clear()

            # First call (cache miss)
            start_time = time.perf_counter()
            settings1 = get_cached_veo3_settings()
            first_call_time = (time.perf_counter() - start_time) * 1000

            # Subsequent calls (cache hits)
            cache_hit_times = []
            for i in range(5):
                start_time = time.perf_counter()
                settings = get_cached_veo3_settings()
                cache_hit_time = (time.perf_counter() - start_time) * 1000
                cache_hit_times.append(cache_hit_time)

                # Should return same cached instance
                assert settings is settings1

            avg_cache_hit_time = statistics.mean(cache_hit_times)

            # Cache hits should be much faster than first load
            assert avg_cache_hit_time < first_call_time / 2, (
                f"Cache hit time {avg_cache_hit_time:.2f}ms not significantly faster than first load {first_call_time:.2f}ms"
            )

            # Cache hits should be very fast
            assert avg_cache_hit_time < 5, (
                f"Cache hit time {avg_cache_hit_time:.2f}ms exceeds 5ms target"
            )

            print(
                f"Cache performance: first={first_call_time:.2f}ms, avg_hit={avg_cache_hit_time:.2f}ms"
            )

    def test_environment_loading_performance(self):
        """Test environment detection loading performance."""
        with patch("src.config.environment.ConfigurationService") as mock_service:
            mock_service._detect_deployment_type.return_value = "local"
            mock_service.get.return_value = "development"
            mock_service.get_bool.return_value = False

            # Measure environment detection performance
            load_times = []
            iterations = 5

            for i in range(iterations):
                start_time = time.perf_counter()
                env_info = EnvironmentDetector.get_environment_info()
                end_time = time.perf_counter()

                load_time_ms = (end_time - start_time) * 1000
                load_times.append(load_time_ms)

                # Verify environment detected correctly
                assert env_info.deployment_type == "local"
                assert env_info.environment_name == "development"

            avg_time = statistics.mean(load_times)
            max_time = max(load_times)

            # Should meet performance targets
            assert avg_time < 50, (
                f"Environment detection time {avg_time:.2f}ms exceeds 50ms target"
            )
            assert max_time < 100, (
                f"Maximum environment detection time {max_time:.2f}ms exceeds limit"
            )

            print(
                f"Environment detection performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms"
            )


class TestFactoryCreationPerformance:
    """Performance tests for factory creation (<10ms target)."""

    def test_veo3_config_creation_performance_target(self):
        """Test Veo3 configuration creation meets <10ms target."""
        test_env = {
            "GOOGLE_PROJECT_ID": "factory-performance-test",
            "USE_MOCK_VEO": "true",
            "VEO3_TIMEOUT": "300",
        }

        with patch.dict(os.environ, test_env):
            # Measure multiple iterations
            creation_times = []
            iterations = 20

            for i in range(iterations):
                start_time = time.perf_counter()
                config = ConfigurationFactory.create_veo3_config()
                end_time = time.perf_counter()

                creation_time_ms = (end_time - start_time) * 1000
                creation_times.append(creation_time_ms)

                # Verify configuration created correctly
                assert config.project_id == "factory-performance-test"
                assert config.use_mock is True
                assert config.timeout == 300

            # Calculate statistics
            avg_time = statistics.mean(creation_times)
            max_time = max(creation_times)
            percentile_95 = statistics.quantiles(creation_times, n=20)[
                18
            ]  # 95th percentile

            # Performance assertions
            assert avg_time < 10, (
                f"Average factory creation time {avg_time:.2f}ms exceeds 10ms target"
            )
            assert percentile_95 < 15, (
                f"95th percentile factory creation time {percentile_95:.2f}ms exceeds reasonable limit"
            )

            print(
                f"Factory creation performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms, 95th={percentile_95:.2f}ms"
            )

    def test_provider_config_creation_performance(self):
        """Test provider configuration creation performance for both providers."""
        test_env = {
            "GOOGLE_PROJECT_ID": "provider-performance-test",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            with patch.object(ConfigurationFactory, "get_azure_config") as mock_azure:
                mock_azure.return_value = {
                    "endpoint": "https://test.azure.com",
                    "api_key": "test-key",
                    "api_version": "preview",
                }

                # Test Veo3 provider creation performance
                veo3_times = []
                azure_times = []
                iterations = 10

                for i in range(iterations):
                    # Veo3 provider
                    start_time = time.perf_counter()
                    veo3_config = ConfigurationFactory.create_provider_config(
                        "google_veo3"
                    )
                    veo3_time = (time.perf_counter() - start_time) * 1000
                    veo3_times.append(veo3_time)

                    # Azure provider
                    start_time = time.perf_counter()
                    azure_config = ConfigurationFactory.create_provider_config(
                        "azure_sora"
                    )
                    azure_time = (time.perf_counter() - start_time) * 1000
                    azure_times.append(azure_time)

                    # Verify configurations
                    assert veo3_config["project_id"] == "provider-performance-test"
                    assert azure_config["endpoint"] == "https://test.azure.com"

                avg_veo3_time = statistics.mean(veo3_times)
                avg_azure_time = statistics.mean(azure_times)

                # Both should meet performance targets
                assert avg_veo3_time < 10, (
                    f"Veo3 provider creation {avg_veo3_time:.2f}ms exceeds 10ms target"
                )
                assert avg_azure_time < 10, (
                    f"Azure provider creation {avg_azure_time:.2f}ms exceeds 10ms target"
                )

                print(
                    f"Provider creation performance: veo3={avg_veo3_time:.2f}ms, azure={avg_azure_time:.2f}ms"
                )

    def test_environment_aware_config_creation_performance(self):
        """Test environment-aware configuration creation performance."""
        test_env = {
            "GOOGLE_PROJECT_ID": "env-aware-performance-test",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            with patch(
                "src.config.factory.ConfigurationService._detect_deployment_type"
            ) as mock_detect:
                mock_detect.return_value = "local"

                # Measure environment-aware config creation
                creation_times = []
                iterations = 10

                for i in range(iterations):
                    start_time = time.perf_counter()
                    config = (
                        ProviderConfigurationFactory.create_environment_aware_config(
                            "google_veo3"
                        )
                    )
                    end_time = time.perf_counter()

                    creation_time_ms = (end_time - start_time) * 1000
                    creation_times.append(creation_time_ms)

                    # Verify configuration
                    assert config.get("project_id") == "env-aware-performance-test"

                avg_time = statistics.mean(creation_times)
                max_time = max(creation_times)

                # Should meet performance targets
                assert avg_time < 15, (
                    f"Environment-aware creation {avg_time:.2f}ms exceeds 15ms target"
                )
                assert max_time < 25, (
                    f"Maximum environment-aware creation {max_time:.2f}ms exceeds limit"
                )

                print(
                    f"Environment-aware creation performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms"
                )


class TestValidationPerformance:
    """Performance tests for configuration validation (<100ms target)."""

    def test_provider_configuration_validation_performance(self):
        """Test provider configuration validation meets <100ms target."""
        test_env = {
            "GOOGLE_PROJECT_ID": "validation-performance-test",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "test-client",
        }

        with patch.dict(os.environ, test_env):
            settings = Veo3Settings()

            # Test validation performance for both providers
            validation_times = []
            iterations = 10

            for i in range(iterations):
                # Veo3 validation
                start_time = time.perf_counter()
                veo3_result = settings.validate_provider_configuration("google_veo3")
                veo3_time = (time.perf_counter() - start_time) * 1000
                validation_times.append(veo3_time)

                # Azure validation (with mocked ConfigurationService)
                with patch(
                    "src.config.veo3_settings.ConfigurationService"
                ) as mock_service:
                    mock_service.get.side_effect = lambda key, default=None: {
                        "AZURE_OPENAI_ENDPOINT": "https://test.azure.com",
                        "AZURE_OPENAI_API_KEY": "test-key",
                        "AZURE_OPENAI_API_VERSION": "preview",
                    }.get(key, default)

                    start_time = time.perf_counter()
                    azure_result = settings.validate_provider_configuration(
                        "azure_sora"
                    )
                    azure_time = (time.perf_counter() - start_time) * 1000
                    validation_times.append(azure_time)

                # Verify validations succeeded
                assert veo3_result["valid"] is True
                assert azure_result["valid"] is True

            avg_time = statistics.mean(validation_times)
            max_time = max(validation_times)

            # Performance assertions
            assert avg_time < 100, (
                f"Average validation time {avg_time:.2f}ms exceeds 100ms target"
            )
            assert max_time < 200, (
                f"Maximum validation time {max_time:.2f}ms exceeds reasonable limit"
            )

            print(f"Validation performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms")

    def test_environment_validation_performance(self):
        """Test environment validation performance."""
        from src.config.veo3_settings import validate_veo3_environment

        test_env = {
            "GOOGLE_PROJECT_ID": "env-validation-performance-test",
            "USE_MOCK_VEO": "true",
            "DEFAULT_PROVIDER": "google_veo3",
        }

        with patch.dict(os.environ, test_env):
            # Clear cache to ensure fresh validation
            get_cached_veo3_settings.cache_clear()

            # Measure environment validation performance
            validation_times = []
            iterations = 5  # Fewer iterations due to complexity

            for i in range(iterations):
                start_time = time.perf_counter()
                result = validate_veo3_environment()
                end_time = time.perf_counter()

                validation_time_ms = (end_time - start_time) * 1000
                validation_times.append(validation_time_ms)

                # Verify validation succeeded
                assert result["valid"] is True
                assert "test_id" in result

            avg_time = statistics.mean(validation_times)
            max_time = max(validation_times)

            # Should meet performance targets
            assert avg_time < 150, (
                f"Environment validation {avg_time:.2f}ms exceeds 150ms target"
            )
            assert max_time < 300, (
                f"Maximum environment validation {max_time:.2f}ms exceeds limit"
            )

            print(
                f"Environment validation performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms"
            )

    def test_environment_compatibility_validation_performance(self):
        """Test environment compatibility validation performance."""
        with patch("src.config.environment.ConfigurationService") as mock_service:
            mock_service._detect_deployment_type.return_value = "local"
            mock_service.get.return_value = "development"
            mock_service.get_bool.return_value = False

            # Measure compatibility validation performance
            validation_times = []
            iterations = 5

            for i in range(iterations):
                start_time = time.perf_counter()
                result = EnvironmentDetector.validate_environment_compatibility()
                end_time = time.perf_counter()

                validation_time_ms = (end_time - start_time) * 1000
                validation_times.append(validation_time_ms)

                # Verify validation completed
                assert "compatible" in result
                assert "test_id" in result

            avg_time = statistics.mean(validation_times)
            max_time = max(validation_times)

            # Should meet performance targets
            assert avg_time < 100, (
                f"Compatibility validation {avg_time:.2f}ms exceeds 100ms target"
            )
            assert max_time < 200, (
                f"Maximum compatibility validation {max_time:.2f}ms exceeds limit"
            )

            print(
                f"Compatibility validation performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms"
            )


class TestOptimizationPerformance:
    """Performance tests for configuration optimization."""

    def test_provider_optimization_performance(self):
        """Test provider configuration optimization performance."""
        with patch(
            "src.config.environment.EnvironmentDetector.get_environment_info"
        ) as mock_env:
            from src.config.environment import EnvironmentInfo

            mock_env_info = EnvironmentInfo(
                deployment_type="production",
                environment_name="production",
                debug_enabled=False,
                testing_mode=False,
                provider_defaults={"timeout": 300, "retries": 3},
                security_level="strict",
                performance_profile="optimized",
                monitoring_enabled=True,
                rate_limiting_enabled=True,
            )
            mock_env.return_value = mock_env_info

            # Measure optimization performance
            optimization_times = []
            iterations = 10

            for provider in ["google_veo3", "azure_sora"]:
                for i in range(iterations):
                    start_time = time.perf_counter()
                    optimized_config = (
                        EnvironmentOptimizer.get_optimized_provider_config(provider)
                    )
                    end_time = time.perf_counter()

                    optimization_time_ms = (end_time - start_time) * 1000
                    optimization_times.append(optimization_time_ms)

                    # Verify optimization applied
                    assert "default_timeout" in optimized_config
                    assert "ssl_verify" in optimized_config

            avg_time = statistics.mean(optimization_times)
            max_time = max(optimization_times)

            # Should be very fast
            assert avg_time < 5, (
                f"Optimization time {avg_time:.2f}ms exceeds 5ms target"
            )
            assert max_time < 15, (
                f"Maximum optimization time {max_time:.2f}ms exceeds limit"
            )

            print(
                f"Optimization performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms"
            )

    def test_optimal_provider_selection_performance(self):
        """Test optimal provider selection performance."""
        test_env = {
            "GOOGLE_PROJECT_ID": "optimal-selection-test",
            "USE_MOCK_VEO": "true",
            "DEFAULT_PROVIDER": "google_veo3",
        }

        with patch.dict(os.environ, test_env):
            with patch.object(ConfigurationFactory, "get_azure_config") as mock_azure:
                mock_azure.return_value = {"endpoint": "https://test.azure.com"}

                # Measure optimal provider selection performance
                selection_times = []
                iterations = 10

                for i in range(iterations):
                    start_time = time.perf_counter()
                    optimal_config = (
                        ProviderConfigurationFactory.get_optimal_provider_config()
                    )
                    end_time = time.perf_counter()

                    selection_time_ms = (end_time - start_time) * 1000
                    selection_times.append(selection_time_ms)

                    # Verify selection worked
                    assert "selected_provider" in optimal_config
                    assert "provider_availability" in optimal_config

                avg_time = statistics.mean(selection_times)
                max_time = max(selection_times)

                # Should be reasonably fast
                assert avg_time < 20, (
                    f"Optimal selection time {avg_time:.2f}ms exceeds 20ms target"
                )
                assert max_time < 50, (
                    f"Maximum optimal selection time {max_time:.2f}ms exceeds limit"
                )

                print(
                    f"Optimal selection performance: avg={avg_time:.2f}ms, max={max_time:.2f}ms"
                )


class TestPerformanceRegression:
    """Performance regression tests to ensure performance doesn't degrade."""

    def test_performance_consistency(self):
        """Test performance consistency across multiple runs."""
        test_env = {
            "GOOGLE_PROJECT_ID": "consistency-test-project",
            "USE_MOCK_VEO": "true",
        }

        with patch.dict(os.environ, test_env):
            # Measure performance across multiple runs
            all_times = []
            batch_size = 5
            num_batches = 3

            for batch in range(num_batches):
                batch_times = []

                for i in range(batch_size):
                    start_time = time.perf_counter()

                    # Perform complete workflow
                    settings = Veo3Settings()
                    config = ConfigurationFactory.create_veo3_config()
                    validation = settings.validate_provider_configuration("google_veo3")

                    end_time = time.perf_counter()
                    total_time_ms = (end_time - start_time) * 1000
                    batch_times.append(total_time_ms)

                    # Verify workflow succeeded
                    assert settings.GOOGLE_PROJECT_ID == "consistency-test-project"
                    assert config.project_id == "consistency-test-project"
                    assert validation["valid"] is True

                batch_avg = statistics.mean(batch_times)
                all_times.extend(batch_times)

                print(f"Batch {batch + 1} average: {batch_avg:.2f}ms")

            # Analyze consistency
            overall_avg = statistics.mean(all_times)
            std_dev = statistics.stdev(all_times)
            coefficient_of_variation = std_dev / overall_avg

            # Performance should be consistent (low variation)
            assert coefficient_of_variation < 0.5, (
                f"Performance inconsistent: CV={coefficient_of_variation:.2f} (target <0.5)"
            )

            print(
                f"Performance consistency: avg={overall_avg:.2f}ms, std={std_dev:.2f}ms, CV={coefficient_of_variation:.2f}"
            )

    def test_memory_performance_profile(self):
        """Test memory usage during configuration operations."""
        import tracemalloc

        test_env = {"GOOGLE_PROJECT_ID": "memory-test-project", "USE_MOCK_VEO": "true"}

        with patch.dict(os.environ, test_env):
            # Start memory tracing
            tracemalloc.start()

            # Perform configuration operations
            settings = Veo3Settings()
            config = ConfigurationFactory.create_veo3_config()
            validation = settings.validate_provider_configuration("google_veo3")

            # Get memory usage
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            current_mb = current / 1024 / 1024
            peak_mb = peak / 1024 / 1024

            # Memory usage should be reasonable
            assert current_mb < 10, (
                f"Current memory usage {current_mb:.2f}MB exceeds 10MB limit"
            )
            assert peak_mb < 20, f"Peak memory usage {peak_mb:.2f}MB exceeds 20MB limit"

            print(f"Memory profile: current={current_mb:.2f}MB, peak={peak_mb:.2f}MB")


@pytest.mark.performance
class TestPerformanceBenchmarks:
    """Comprehensive performance benchmarks for configuration system."""

    def test_comprehensive_performance_benchmark(self):
        """Comprehensive performance benchmark covering all major operations."""
        test_env = {
            "GOOGLE_PROJECT_ID": "benchmark-test-project",
            "USE_MOCK_VEO": "true",
            "GOOGLE_CLIENT_ID": "benchmark-client",
            "VEO3_TIMEOUT": "300",
        }

        with patch.dict(os.environ, test_env):
            # Clear caches for accurate measurement
            get_cached_veo3_settings.cache_clear()

            benchmark_results = {}
            iterations = 10

            # Benchmark 1: Settings Loading
            settings_times = []
            for i in range(iterations):
                start_time = time.perf_counter()
                settings = Veo3Settings()
                settings_times.append((time.perf_counter() - start_time) * 1000)

            benchmark_results["settings_loading"] = {
                "avg": statistics.mean(settings_times),
                "max": max(settings_times),
                "target": 50,
            }

            # Benchmark 2: Factory Creation
            factory_times = []
            for i in range(iterations):
                start_time = time.perf_counter()
                config = ConfigurationFactory.create_veo3_config()
                factory_times.append((time.perf_counter() - start_time) * 1000)

            benchmark_results["factory_creation"] = {
                "avg": statistics.mean(factory_times),
                "max": max(factory_times),
                "target": 10,
            }

            # Benchmark 3: Validation
            validation_times = []
            for i in range(iterations):
                start_time = time.perf_counter()
                validation = settings.validate_provider_configuration("google_veo3")
                validation_times.append((time.perf_counter() - start_time) * 1000)

            benchmark_results["validation"] = {
                "avg": statistics.mean(validation_times),
                "max": max(validation_times),
                "target": 100,
            }

            # Print benchmark results
            print("\n=== F4 Environment Configuration Performance Benchmark ===")
            for operation, metrics in benchmark_results.items():
                status_avg = (
                    "✅ PASS" if metrics["avg"] < metrics["target"] else "❌ FAIL"
                )
                status_max = (
                    "✅ PASS" if metrics["max"] < metrics["target"] * 2 else "❌ FAIL"
                )

                print(f"{operation.replace('_', ' ').title()}:")
                print(
                    f"  Average: {metrics['avg']:.2f}ms (target: <{metrics['target']}ms) {status_avg}"
                )
                print(
                    f"  Maximum: {metrics['max']:.2f}ms (target: <{metrics['target'] * 2}ms) {status_max}"
                )

            # Assert all benchmarks pass
            for operation, metrics in benchmark_results.items():
                assert metrics["avg"] < metrics["target"], (
                    f"{operation} average time {metrics['avg']:.2f}ms exceeds {metrics['target']}ms target"
                )

            print("=== All Performance Targets Met ===")


if __name__ == "__main__":
    # Run performance tests when executed directly
    pytest.main([__file__, "-v", "-m", "performance"])
