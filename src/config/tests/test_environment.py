"""Comprehensive unit tests for environment detection utilities.

Tests cover environment detection, configuration optimization, and integration
with ConfigurationService patterns.
"""

from unittest.mock import patch

from src.config.environment import (
    EnvironmentDetector,
    EnvironmentInfo,
    EnvironmentOptimizer,
    get_environment_summary,
    get_recommended_provider,
    is_development_environment,
    is_docker_deployment,
    is_production_environment,
)


class TestEnvironmentInfo:
    """Test suite for EnvironmentInfo dataclass."""

    def test_environment_info_initialization(self):
        """Test EnvironmentInfo dataclass initialization."""
        provider_defaults = {"use_mock_providers": True, "timeout": 120}

        env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,
            testing_mode=False,
            provider_defaults=provider_defaults,
            security_level="development",
            performance_profile="development",
            monitoring_enabled=True,
            rate_limiting_enabled=False,
        )

        assert env_info.deployment_type == "local"
        assert env_info.environment_name == "development"
        assert env_info.debug_enabled is True
        assert env_info.testing_mode is False
        assert env_info.provider_defaults == provider_defaults
        assert env_info.security_level == "development"
        assert env_info.performance_profile == "development"
        assert env_info.monitoring_enabled is True
        assert env_info.rate_limiting_enabled is False


class TestEnvironmentDetector:
    """Test suite for EnvironmentDetector class."""

    @patch("src.config.environment.ConfigurationService")
    def test_get_environment_info_local_development(self, mock_config_service):
        """Test environment info detection for local development."""
        mock_config_service._detect_deployment_type.return_value = "local"
        mock_config_service.get.return_value = "development"
        mock_config_service.get_bool.side_effect = [
            False,
            False,
            True,
            False,
        ]  # debug, testing, monitoring, rate_limit

        env_info = EnvironmentDetector.get_environment_info()

        assert env_info.deployment_type == "local"
        assert env_info.environment_name == "development"
        assert env_info.debug_enabled is False
        assert env_info.testing_mode is False
        assert env_info.security_level == "development"
        assert env_info.performance_profile == "development"
        assert env_info.monitoring_enabled is True
        assert env_info.rate_limiting_enabled is False

        # Check provider defaults for development
        assert env_info.provider_defaults["use_mock_providers"] is True
        assert env_info.provider_defaults["default_timeout"] == 120
        assert env_info.provider_defaults["max_retries"] == 2

    @patch("src.config.environment.ConfigurationService")
    def test_get_environment_info_docker_deployment(self, mock_config_service):
        """Test environment info detection for Docker deployment."""
        mock_config_service._detect_deployment_type.return_value = "docker"
        mock_config_service.get.return_value = "production"
        mock_config_service.get_bool.side_effect = [False, False, True, True]

        env_info = EnvironmentDetector.get_environment_info()

        assert env_info.deployment_type == "docker"
        assert env_info.environment_name == "production"
        assert env_info.security_level == "strict"  # Production security
        assert env_info.performance_profile == "standard"  # Docker performance

        # Check Docker-specific provider defaults
        assert env_info.provider_defaults["default_timeout"] == 600
        assert env_info.provider_defaults["max_retries"] == 5

    @patch("src.config.environment.ConfigurationService")
    def test_get_environment_info_production(self, mock_config_service):
        """Test environment info detection for production environment."""
        mock_config_service._detect_deployment_type.return_value = "production"
        mock_config_service.get.return_value = "production"
        mock_config_service.get_bool.side_effect = [False, False, True, true]

        env_info = EnvironmentDetector.get_environment_info()

        assert env_info.deployment_type == "production"
        assert env_info.environment_name == "production"
        assert env_info.security_level == "strict"
        assert env_info.performance_profile == "optimized"

        # Check production provider defaults
        assert env_info.provider_defaults["use_mock_providers"] is False
        assert env_info.provider_defaults["default_timeout"] == 300
        assert env_info.provider_defaults["max_retries"] == 3
        assert env_info.provider_defaults["rate_limit_rpm"] == 30

    @patch("src.config.environment.ConfigurationService")
    def test_get_environment_info_testing_environment(self, mock_config_service):
        """Test environment info detection for testing environment."""
        mock_config_service._detect_deployment_type.return_value = "local"
        mock_config_service.get.return_value = "testing"
        mock_config_service.get_bool.side_effect = [False, True, False, False]

        env_info = EnvironmentDetector.get_environment_info()

        assert env_info.environment_name == "testing"
        assert env_info.testing_mode is True
        assert env_info.security_level == "standard"  # Testing security

        # Check testing-specific provider defaults
        assert env_info.provider_defaults["default_timeout"] == 60
        assert env_info.provider_defaults["max_retries"] == 1
        assert env_info.provider_defaults["rate_limit_rpm"] == 120

    def test_determine_security_level(self):
        """Test security level determination logic."""
        # Production environment
        security_level = EnvironmentDetector._determine_security_level(
            "production", "production"
        )
        assert security_level == "strict"

        # Testing environment
        security_level = EnvironmentDetector._determine_security_level(
            "local", "testing"
        )
        assert security_level == "standard"

        # Docker deployment
        security_level = EnvironmentDetector._determine_security_level(
            "docker", "development"
        )
        assert security_level == "standard"

        # Development environment
        security_level = EnvironmentDetector._determine_security_level(
            "local", "development"
        )
        assert security_level == "development"

    def test_determine_performance_profile(self):
        """Test performance profile determination logic."""
        # Production environment
        profile = EnvironmentDetector._determine_performance_profile(
            "production", "production"
        )
        assert profile == "optimized"

        # Docker deployment
        profile = EnvironmentDetector._determine_performance_profile(
            "docker", "development"
        )
        assert profile == "standard"

        # Local development
        profile = EnvironmentDetector._determine_performance_profile(
            "local", "development"
        )
        assert profile == "development"

    def test_get_environment_provider_defaults(self):
        """Test provider defaults for different environments."""
        # Production defaults
        defaults = EnvironmentDetector._get_environment_provider_defaults(
            "production", "production"
        )
        assert defaults["use_mock_providers"] is False
        assert defaults["default_timeout"] == 300
        assert defaults["max_retries"] == 3
        assert defaults["rate_limit_rpm"] == 30

        # Docker defaults
        defaults = EnvironmentDetector._get_environment_provider_defaults(
            "docker", "development"
        )
        assert defaults["use_mock_providers"] is False
        assert defaults["default_timeout"] == 600
        assert defaults["max_retries"] == 5
        assert defaults["rate_limit_rpm"] == 45

        # Testing defaults
        defaults = EnvironmentDetector._get_environment_provider_defaults(
            "local", "testing"
        )
        assert defaults["use_mock_providers"] is True
        assert defaults["default_timeout"] == 60
        assert defaults["max_retries"] == 1
        assert defaults["rate_limit_rpm"] == 120

        # Development defaults (fallback)
        defaults = EnvironmentDetector._get_environment_provider_defaults(
            "local", "development"
        )
        assert defaults["use_mock_providers"] is True
        assert defaults["default_timeout"] == 120
        assert defaults["max_retries"] == 2
        assert defaults["rate_limit_rpm"] == 60

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_validate_environment_compatibility_success(self, mock_get_env_info):
        """Test successful environment compatibility validation."""
        mock_env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,  # OK for development
            testing_mode=False,
            provider_defaults={"use_mock_providers": True},  # OK for development
            security_level="development",
            performance_profile="development",
            monitoring_enabled=True,
            rate_limiting_enabled=False,
        )
        mock_get_env_info.return_value = mock_env_info

        result = EnvironmentDetector.validate_environment_compatibility()

        assert result["compatible"] is True
        assert len(result["errors"]) == 0
        assert "test_id" in result
        assert result["environment_info"]["deployment_type"] == "local"

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_validate_environment_compatibility_warnings(self, mock_get_env_info):
        """Test environment compatibility validation with warnings."""
        mock_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=True,  # WARNING: debug in production
            testing_mode=False,
            provider_defaults={"use_mock_providers": False},
            security_level="development",  # WARNING: dev security in production
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )
        mock_get_env_info.return_value = mock_env_info

        result = EnvironmentDetector.validate_environment_compatibility()

        assert result["compatible"] is True  # Warnings don't make it incompatible
        assert len(result["warnings"]) >= 2  # At least debug and security warnings
        assert any(
            "Debug mode enabled in production" in warning
            for warning in result["warnings"]
        )
        assert any(
            "Development security level" in warning for warning in result["warnings"]
        )
        assert len(result["recommendations"]) >= 2

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_validate_environment_compatibility_errors(self, mock_get_env_info):
        """Test environment compatibility validation with errors."""
        mock_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={"use_mock_providers": True},  # ERROR: mock in production
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )
        mock_get_env_info.return_value = mock_env_info

        result = EnvironmentDetector.validate_environment_compatibility()

        assert result["compatible"] is False
        assert len(result["errors"]) >= 1
        assert any(
            "Mock providers enabled in production" in error
            for error in result["errors"]
        )
        assert any("Disable mock providers" in rec for rec in result["recommendations"])

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_validate_environment_compatibility_exception_handling(
        self, mock_get_env_info
    ):
        """Test exception handling in environment compatibility validation."""
        mock_get_env_info.side_effect = Exception("Environment detection failed")

        result = EnvironmentDetector.validate_environment_compatibility()

        assert result["compatible"] is False
        assert len(result["errors"]) == 1
        assert (
            "Environment validation failed: Environment detection failed"
            in result["errors"]
        )


class TestEnvironmentOptimizer:
    """Test suite for EnvironmentOptimizer class."""

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_get_optimized_provider_config_veo3_optimized(self, mock_get_env_info):
        """Test optimized provider config for Veo3 in optimized environment."""
        mock_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={"default_timeout": 300, "max_retries": 3},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )
        mock_get_env_info.return_value = mock_env_info

        config = EnvironmentOptimizer.get_optimized_provider_config("google_veo3")

        # Should include base provider defaults
        assert config["default_timeout"] == 300
        assert config["max_retries"] == 3

        # Should include Veo3-specific optimizations
        assert config["generation_timeout"] == 3600  # Extended for production
        assert config["rate_limit_rpm"] == 20  # Conservative for production
        assert config["batch_size"] == 5  # Batch requests in production

        # Should include security optimizations
        assert config["ssl_verify"] is True
        assert config["request_signing"] is True
        assert config["audit_logging"] is True

        # Should include performance optimizations
        assert config["connection_pooling"] is True
        assert config["request_caching"] is True
        assert config["compression"] is True

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_get_optimized_provider_config_veo3_development(self, mock_get_env_info):
        """Test optimized provider config for Veo3 in development environment."""
        mock_env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,
            testing_mode=False,
            provider_defaults={"default_timeout": 120, "max_retries": 2},
            security_level="development",
            performance_profile="development",
            monitoring_enabled=False,
            rate_limiting_enabled=False,
        )
        mock_get_env_info.return_value = mock_env_info

        config = EnvironmentOptimizer.get_optimized_provider_config("google_veo3")

        # Should include development-specific Veo3 optimizations
        assert config["generation_timeout"] == 900  # Shorter for development
        assert config["rate_limit_rpm"] == 60  # Higher for development
        assert config["batch_size"] == 1  # Single requests for debugging

        # Should include development security settings
        assert config["ssl_verify"] is False  # For local development
        assert config["request_signing"] is False

        # Should include development performance settings
        assert config["connection_pooling"] is False  # Simpler debugging
        assert config["request_caching"] is False

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_get_optimized_provider_config_azure_optimized(self, mock_get_env_info):
        """Test optimized provider config for Azure in optimized environment."""
        mock_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={"default_timeout": 300, "max_retries": 3},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )
        mock_get_env_info.return_value = mock_env_info

        config = EnvironmentOptimizer.get_optimized_provider_config("azure_sora")

        # Should include Azure-specific optimizations
        assert config["connection_pool_size"] == 20
        assert config["request_timeout"] == 300
        assert config["retry_backoff"] == 2.0

    def test_optimize_veo3_config(self):
        """Test Veo3-specific configuration optimization."""
        optimized_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )

        veo3_config = EnvironmentOptimizer._optimize_veo3_config(optimized_env_info)

        assert veo3_config["generation_timeout"] == 3600
        assert veo3_config["rate_limit_rpm"] == 20
        assert veo3_config["batch_size"] == 5

        # Test development profile
        dev_env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,
            testing_mode=False,
            provider_defaults={},
            security_level="development",
            performance_profile="development",
            monitoring_enabled=False,
            rate_limiting_enabled=False,
        )

        dev_veo3_config = EnvironmentOptimizer._optimize_veo3_config(dev_env_info)

        assert dev_veo3_config["generation_timeout"] == 900
        assert dev_veo3_config["rate_limit_rpm"] == 60
        assert dev_veo3_config["batch_size"] == 1

    def test_optimize_azure_config(self):
        """Test Azure-specific configuration optimization."""
        optimized_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )

        azure_config = EnvironmentOptimizer._optimize_azure_config(optimized_env_info)

        assert azure_config["connection_pool_size"] == 20
        assert azure_config["request_timeout"] == 300
        assert azure_config["retry_backoff"] == 2.0

    def test_apply_security_optimizations(self):
        """Test security optimization application."""
        strict_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )

        security_config = EnvironmentOptimizer._apply_security_optimizations(
            strict_env_info
        )

        assert security_config["ssl_verify"] is True
        assert security_config["request_signing"] is True
        assert security_config["audit_logging"] is True
        assert security_config["credential_rotation"] is True

    def test_apply_performance_optimizations(self):
        """Test performance optimization application."""
        optimized_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )

        perf_config = EnvironmentOptimizer._apply_performance_optimizations(
            optimized_env_info
        )

        assert perf_config["connection_pooling"] is True
        assert perf_config["request_caching"] is True
        assert perf_config["compression"] is True
        assert perf_config["keep_alive"] is True


class TestEnvironmentUtilityFunctions:
    """Test suite for environment utility functions."""

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_get_environment_summary_success(self, mock_get_env_info):
        """Test successful environment summary generation."""
        mock_env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,
            testing_mode=False,
            provider_defaults={"timeout": 120},
            security_level="development",
            performance_profile="development",
            monitoring_enabled=True,
            rate_limiting_enabled=False,
        )
        mock_get_env_info.return_value = mock_env_info

        with patch(
            "src.config.environment.EnvironmentDetector.validate_environment_compatibility"
        ) as mock_validate:
            with patch(
                "src.config.environment.EnvironmentOptimizer.get_optimized_provider_config"
            ) as mock_optimize:
                mock_validate.return_value = {
                    "compatible": True,
                    "warnings": [],
                    "errors": [],
                    "recommendations": [],
                }
                mock_optimize.side_effect = [
                    {"veo3_config": True},  # For veo3
                    {"azure_config": True},  # For azure
                ]

                summary = get_environment_summary()

                assert "test_id" in summary
                assert summary["environment"]["deployment_type"] == "local"
                assert summary["environment"]["environment_name"] == "development"
                assert summary["compatibility"]["compatible"] is True
                assert "optimized_configs" in summary
                assert "veo3" in summary["optimized_configs"]
                assert "azure" in summary["optimized_configs"]

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_get_environment_summary_exception_handling(self, mock_get_env_info):
        """Test exception handling in environment summary generation."""
        mock_get_env_info.side_effect = Exception("Detection failed")

        summary = get_environment_summary()

        assert "error" in summary
        assert summary["error"] == "Detection failed"
        assert summary["environment"]["deployment_type"] == "unknown"

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_is_production_environment(self, mock_get_env_info):
        """Test is_production_environment utility function."""
        mock_env_info = EnvironmentInfo(
            deployment_type="production",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={},
            security_level="strict",
            performance_profile="optimized",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )
        mock_get_env_info.return_value = mock_env_info

        assert is_production_environment() is True

        # Test non-production
        mock_env_info.environment_name = "development"
        assert is_production_environment() is False

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_is_development_environment(self, mock_get_env_info):
        """Test is_development_environment utility function."""
        mock_env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,
            testing_mode=False,
            provider_defaults={},
            security_level="development",
            performance_profile="development",
            monitoring_enabled=True,
            rate_limiting_enabled=False,
        )
        mock_get_env_info.return_value = mock_env_info

        assert is_development_environment() is True

        # Test non-development
        mock_env_info.environment_name = "production"
        assert is_development_environment() is False

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    def test_is_docker_deployment(self, mock_get_env_info):
        """Test is_docker_deployment utility function."""
        mock_env_info = EnvironmentInfo(
            deployment_type="docker",
            environment_name="production",
            debug_enabled=False,
            testing_mode=False,
            provider_defaults={},
            security_level="standard",
            performance_profile="standard",
            monitoring_enabled=True,
            rate_limiting_enabled=True,
        )
        mock_get_env_info.return_value = mock_env_info

        assert is_docker_deployment() is True

        # Test non-Docker
        mock_env_info.deployment_type = "local"
        assert is_docker_deployment() is False

    @patch("src.config.environment.EnvironmentDetector.get_environment_info")
    @patch("src.config.environment.ConfigurationService")
    def test_get_recommended_provider(self, mock_config_service, mock_get_env_info):
        """Test get_recommended_provider utility function."""
        # Test mock mode (development)
        mock_env_info = EnvironmentInfo(
            deployment_type="local",
            environment_name="development",
            debug_enabled=True,
            testing_mode=False,
            provider_defaults={"use_mock_providers": True},
            security_level="development",
            performance_profile="development",
            monitoring_enabled=True,
            rate_limiting_enabled=False,
        )
        mock_get_env_info.return_value = mock_env_info

        provider = get_recommended_provider()
        assert provider == "azure_sora"  # Default for mock mode

        # Test production mode
        mock_env_info.provider_defaults = {"use_mock_providers": False}
        mock_config_service.get.return_value = "google_veo3"

        provider = get_recommended_provider()
        assert provider == "google_veo3"  # From DEFAULT_PROVIDER setting


class TestEnvironmentDetectorEdgeCases:
    """Test edge cases and error conditions in environment detection."""

    @patch("src.config.environment.ConfigurationService")
    def test_invalid_flask_env_fallback(self, mock_config_service):
        """Test fallback when FLASK_ENV has invalid value."""
        mock_config_service._detect_deployment_type.return_value = "local"
        mock_config_service.get.return_value = "invalid_env"  # Invalid environment
        mock_config_service.get_bool.side_effect = [False, False, True, False]

        env_info = EnvironmentDetector.get_environment_info()

        # Should fallback to development
        assert env_info.environment_name == "development"

    @patch("src.config.environment.ConfigurationService")
    def test_environment_detection_with_minimal_config(self, mock_config_service):
        """Test environment detection with minimal configuration."""
        mock_config_service._detect_deployment_type.return_value = "local"
        mock_config_service.get.return_value = "development"
        mock_config_service.get_bool.return_value = False  # All booleans false

        env_info = EnvironmentDetector.get_environment_info()

        assert env_info.deployment_type == "local"
        assert env_info.environment_name == "development"
        assert env_info.debug_enabled is False
        assert env_info.testing_mode is False
        assert env_info.monitoring_enabled is False
        assert env_info.rate_limiting_enabled is False
