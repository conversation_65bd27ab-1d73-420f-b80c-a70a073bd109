"""Comprehensive integration tests for F4 Environment Configuration.

This module provides comprehensive integration testing for the F4 Environment
Configuration module including:
- Google Cloud credential detection across all sources
- Multi-source credential fallback mechanisms
- Provider interface integration
- Docker secrets integration
- Environment-specific configuration switching

Test Coverage:
- Docker secrets credential loading (_FILE suffix pattern)
- Service account file credential detection
- OAuth credential validation
- Default application credentials detection
- Compute Engine metadata service detection
- Provider factory integration
- Configuration factory integration
- Environment variable management
"""

import asyncio
import json
import os
import tempfile
from pathlib import Path
from unittest.mock import mock_open, patch

import pytest

from src.config.environment_manager import EnvironmentVariableManager
from src.config.google_cloud_credentials import (
    CredentialNotFoundError,
    GoogleCloudCredentialManager,
    get_credential_manager,
    validate_google_cloud_environment,
)
from src.config.service import ConfigurationService


class TestF4GoogleCloudCredentialIntegration:
    """Integration tests for Google Cloud credential management."""

    @pytest.fixture
    def mock_environment(self):
        """Setup mock environment for testing."""
        # Store original environment
        original_env = dict(os.environ)

        # Clear relevant environment variables
        env_vars_to_clear = [
            "GOOGLE_PROJECT_ID",
            "GOOGLE_APPLICATION_CREDENTIALS",
            "GOOGLE_APPLICATION_CREDENTIALS_FILE",
            "GOOGLE_CLIENT_ID",
            "GOOGLE_CLIENT_SECRET",
            "USE_MOCK_VEO",
            "USE_MOCK_VEO3",
        ]

        for var in env_vars_to_clear:
            os.environ.pop(var, None)

        yield

        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

    @pytest.fixture
    def sample_service_account(self):
        """Create sample service account JSON."""
        return {
            "type": "service_account",
            "project_id": "test-project-12345",
            "private_key_id": "key123",
            "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n",
            "client_email": "<EMAIL>",
            "client_id": "*********0*********0",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
        }

    @pytest.fixture
    def temporary_service_account_file(self, sample_service_account):
        """Create temporary service account file."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump(sample_service_account, f)
            temp_path = f.name

        yield temp_path

        # Cleanup
        Path(temp_path).unlink(missing_ok=True)

    @pytest.fixture
    def credential_manager(self):
        """Create credential manager instance."""
        manager = GoogleCloudCredentialManager()
        manager.clear_cache()  # Start with clean cache
        return manager

    async def test_docker_secrets_credential_detection(
        self, mock_environment, credential_manager, sample_service_account
    ):
        """Test Docker secrets credential detection with _FILE suffix pattern."""
        # Create temporary Docker secret file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump(sample_service_account, f)
            secrets_file = f.name

        try:
            # Set Docker secrets environment variable
            os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = secrets_file

            # Test credential detection
            credentials = await credential_manager.get_credentials()

            # Validate Docker secrets credentials
            assert credentials.credential_type == "service_account"
            assert credentials.project_id == "test-project-12345"
            assert credentials.service_account_info is not None
            assert credentials.service_account_info["type"] == "service_account"
            assert credentials.is_valid()

            # Test caching
            cached_credentials = await credential_manager.get_credentials()
            assert cached_credentials.credential_type == "service_account"

            # Test configuration creation
            config = credential_manager.create_veo3_config()
            assert config["project_id"] == "test-project-12345"
            assert config["credential_type"] == "service_account"
            assert "service_account_info" in config

        finally:
            Path(secrets_file).unlink(missing_ok=True)

    async def test_service_account_file_credential_detection(
        self, mock_environment, credential_manager, temporary_service_account_file
    ):
        """Test service account file credential detection."""
        # Set service account environment variable
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temporary_service_account_file

        # Test credential detection
        credentials = await credential_manager.get_credentials()

        # Validate service account credentials
        assert credentials.credential_type == "service_account"
        assert credentials.project_id == "test-project-12345"
        assert credentials.service_account_path == str(
            Path(temporary_service_account_file).absolute()
        )
        assert credentials.service_account_info is not None
        assert credentials.is_valid()

        # Test configuration creation
        config = credential_manager.create_veo3_config()
        assert config["project_id"] == "test-project-12345"
        assert config["credential_type"] == "service_account"
        assert "service_account_path" in config

    async def test_oauth_credential_detection(
        self, mock_environment, credential_manager
    ):
        """Test OAuth credential detection."""
        # Set OAuth environment variables
        os.environ["GOOGLE_PROJECT_ID"] = "oauth-test-project"
        os.environ["GOOGLE_CLIENT_ID"] = "*********.apps.googleusercontent.com"
        os.environ["GOOGLE_CLIENT_SECRET"] = "oauth-secret-key"

        # Test credential detection
        credentials = await credential_manager.get_credentials()

        # Validate OAuth credentials
        assert credentials.credential_type == "oauth"
        assert credentials.project_id == "oauth-test-project"
        assert credentials.client_id == "*********.apps.googleusercontent.com"
        assert credentials.client_secret == "oauth-secret-key"
        assert credentials.is_valid()

        # Test configuration creation
        config = credential_manager.create_veo3_config()
        assert config["project_id"] == "oauth-test-project"
        assert config["credential_type"] == "oauth"
        assert config["client_id"] == "*********.apps.googleusercontent.com"

    @patch("pathlib.Path.exists")
    async def test_default_credentials_detection(
        self, mock_path_exists, mock_environment, credential_manager
    ):
        """Test default application credentials detection."""
        # Mock gcloud configuration directory
        mock_path_exists.return_value = True

        # Set project ID
        os.environ["GOOGLE_PROJECT_ID"] = "default-creds-project"

        # Test credential detection
        credentials = await credential_manager.get_credentials()

        # Validate default credentials
        assert credentials.credential_type == "default"
        assert credentials.project_id == "default-creds-project"
        assert credentials.is_valid()

        # Test configuration creation
        config = credential_manager.create_veo3_config()
        assert config["project_id"] == "default-creds-project"
        assert config["credential_type"] == "default"

    @patch("builtins.open", new_callable=mock_open, read_data="Google Compute Engine")
    @patch("pathlib.Path.exists")
    async def test_compute_engine_credentials_detection(
        self, mock_path_exists, mock_file_open, mock_environment, credential_manager
    ):
        """Test Compute Engine metadata service credentials detection."""
        # Mock Compute Engine environment
        mock_path_exists.return_value = True

        # Set project ID
        os.environ["GOOGLE_PROJECT_ID"] = "compute-engine-project"

        # Test credential detection
        credentials = await credential_manager.get_credentials()

        # Validate Compute Engine credentials
        assert credentials.credential_type == "compute_engine"
        assert credentials.project_id == "compute-engine-project"
        assert credentials.is_valid()

        # Test configuration creation
        config = credential_manager.create_veo3_config()
        assert config["project_id"] == "compute-engine-project"
        assert config["credential_type"] == "compute_engine"

    async def test_credential_priority_ordering(
        self, mock_environment, credential_manager, sample_service_account
    ):
        """Test credential detection priority ordering."""
        # Create Docker secrets file (highest priority)
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            docker_account = sample_service_account.copy()
            docker_account["project_id"] = "docker-secrets-project"
            json.dump(docker_account, f)
            docker_secrets_file = f.name

        # Create service account file (second priority)
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            service_account = sample_service_account.copy()
            service_account["project_id"] = "service-account-project"
            json.dump(service_account, f)
            service_account_file = f.name

        try:
            # Set all credential sources
            os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = (
                docker_secrets_file  # Highest priority
            )
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = (
                service_account_file  # Second priority
            )
            os.environ["GOOGLE_CLIENT_ID"] = "oauth-client-id"  # Third priority
            os.environ["GOOGLE_CLIENT_SECRET"] = "oauth-secret"
            os.environ["GOOGLE_PROJECT_ID"] = "oauth-project"

            # Test that Docker secrets take priority
            credentials = await credential_manager.get_credentials()
            assert credentials.credential_type == "service_account"
            assert credentials.project_id == "docker-secrets-project"

            # Remove Docker secrets, should fall back to service account file
            del os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"]
            credential_manager.clear_cache()

            credentials = await credential_manager.get_credentials()
            assert credentials.credential_type == "service_account"
            assert credentials.project_id == "service-account-project"

            # Remove service account file, should fall back to OAuth
            del os.environ["GOOGLE_APPLICATION_CREDENTIALS"]
            credential_manager.clear_cache()

            credentials = await credential_manager.get_credentials()
            assert credentials.credential_type == "oauth"
            assert credentials.project_id == "oauth-project"

        finally:
            Path(docker_secrets_file).unlink(missing_ok=True)
            Path(service_account_file).unlink(missing_ok=True)

    async def test_credential_validation_errors(
        self, mock_environment, credential_manager
    ):
        """Test credential validation error handling."""
        # Test with invalid Docker secrets file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            f.write("invalid json content")
            invalid_secrets_file = f.name

        try:
            os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = invalid_secrets_file

            with pytest.raises(CredentialNotFoundError):
                await credential_manager.get_credentials()

        finally:
            Path(invalid_secrets_file).unlink(missing_ok=True)

        # Test with missing Docker secrets file
        os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = (
            "/nonexistent/path/secret.json"
        )

        with pytest.raises(CredentialNotFoundError):
            await credential_manager.get_credentials()

    async def test_retry_logic_and_fallback(self, mock_environment, credential_manager):
        """Test retry logic and fallback mechanisms."""
        # Setup mock that fails first attempt then succeeds
        original_detect = credential_manager._detect_credentials_with_priority
        call_count = 0

        async def mock_detect_with_failure():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Simulated failure")
            else:
                # Return valid OAuth credentials
                os.environ["GOOGLE_PROJECT_ID"] = "retry-test-project"
                os.environ["GOOGLE_CLIENT_ID"] = "retry-client-id"
                os.environ["GOOGLE_CLIENT_SECRET"] = "retry-secret"
                return await original_detect()

        credential_manager._detect_credentials_with_priority = mock_detect_with_failure

        # Test that retry logic works
        credentials = await credential_manager.get_credentials()
        assert credentials.credential_type == "oauth"
        assert credentials.project_id == "retry-test-project"
        assert call_count == 2  # Failed once, succeeded on retry

    def test_environment_variable_management_integration(self, mock_environment):
        """Test integration with environment variable manager."""
        # Test provider variable retrieval
        google_vars = EnvironmentVariableManager.get_provider_variables("google_veo3")
        assert "GOOGLE_PROJECT_ID" in google_vars
        assert "GOOGLE_APPLICATION_CREDENTIALS_FILE" in google_vars
        assert "VEO3_TIMEOUT" in google_vars

        azure_vars = EnvironmentVariableManager.get_provider_variables("azure_sora")
        assert "AZURE_OPENAI_API_KEY" in azure_vars
        assert "AZURE_OPENAI_ENDPOINT" in azure_vars

        # Test provider configuration validation
        os.environ["GOOGLE_PROJECT_ID"] = "env-manager-test"
        os.environ["USE_MOCK_VEO3"] = "true"

        validation = EnvironmentVariableManager.validate_provider_configuration(
            "google_veo3"
        )
        assert validation["provider"] == "google_veo3"
        assert validation["mock_mode"] is True
        assert validation["deployment_type"] in ["local", "docker", "production"]

        # Test environment summary
        summary = EnvironmentVariableManager.get_environment_summary()
        assert "deployment_type" in summary
        assert "provider_status" in summary
        assert "google_veo3" in summary["provider_status"]
        assert "azure_sora" in summary["provider_status"]

    async def test_provider_factory_integration(
        self, mock_environment, temporary_service_account_file
    ):
        """Test integration with video provider factory."""
        from src.features.video_generation.provider_factory import VideoProviderFactory

        # Setup Google Veo3 credentials
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temporary_service_account_file
        os.environ["USE_MOCK_VEO3"] = "true"

        # Test provider factory availability check
        factory = VideoProviderFactory()
        available_providers = factory.get_available_providers()

        # Google Veo3 should be available with mock mode
        assert "google_veo3" in available_providers

        # Test provider creation with F4 credentials
        try:
            provider = factory.create_provider("google_veo3")
            # Provider should be created successfully with F4 credential integration
            assert provider is not None
        except Exception as e:
            # If provider creation fails, it should be due to missing provider class,
            # not credential issues
            assert "not registered" in str(e).lower() or "not found" in str(e).lower()

    def test_configuration_service_integration(self, mock_environment):
        """Test integration with configuration service."""
        # Test that F4 credentials work with ConfigurationService
        os.environ["GOOGLE_PROJECT_ID"] = "config-service-test"
        os.environ["VEO3_TIMEOUT"] = "600"
        os.environ["CREDENTIAL_RETRY_ATTEMPTS"] = "5"

        # Test ConfigurationService can retrieve F4 variables
        project_id = ConfigurationService.get("GOOGLE_PROJECT_ID")
        assert project_id == "config-service-test"

        timeout = ConfigurationService.get_int("VEO3_TIMEOUT", 300)
        assert timeout == 600

        retry_attempts = ConfigurationService.get_int("CREDENTIAL_RETRY_ATTEMPTS", 3)
        assert retry_attempts == 5

        # Test boolean parsing for mock variables
        os.environ["USE_MOCK_VEO3"] = "true"
        use_mock = ConfigurationService.get_bool("USE_MOCK_VEO3", False)
        assert use_mock is True

    def test_google_cloud_environment_validation(self, mock_environment):
        """Test comprehensive Google Cloud environment validation."""
        # Test validation with minimal configuration
        validation = validate_google_cloud_environment()
        assert "valid" in validation
        assert "credential_sources" in validation

        # Test validation with full configuration
        os.environ["GOOGLE_PROJECT_ID"] = "validation-test-project"
        os.environ["USE_MOCK_VEO3"] = "true"
        os.environ["VEO3_TIMEOUT"] = "300"
        os.environ["CREDENTIAL_RETRY_ATTEMPTS"] = "3"

        validation = validate_google_cloud_environment()
        assert validation["valid"] is True
        assert validation["credential_sources"]["oauth"] is False  # No OAuth creds
        assert (
            validation["credential_sources"]["default"] is True
        )  # Always potentially available

        # Test validation with OAuth credentials
        os.environ["GOOGLE_CLIENT_ID"] = "test-client-id"
        os.environ["GOOGLE_CLIENT_SECRET"] = "test-secret"

        validation = validate_google_cloud_environment()
        assert validation["credential_sources"]["oauth"] is True

    async def test_performance_and_caching(self, mock_environment, credential_manager):
        """Test performance optimization and caching behavior."""
        # Setup OAuth credentials
        os.environ["GOOGLE_PROJECT_ID"] = "performance-test"
        os.environ["GOOGLE_CLIENT_ID"] = "perf-client-id"
        os.environ["GOOGLE_CLIENT_SECRET"] = "perf-secret"

        # First call should detect credentials
        start_time = asyncio.get_event_loop().time()
        credentials1 = await credential_manager.get_credentials()
        first_call_time = asyncio.get_event_loop().time() - start_time

        # Second call should use cache
        start_time = asyncio.get_event_loop().time()
        credentials2 = await credential_manager.get_credentials()
        second_call_time = asyncio.get_event_loop().time() - start_time

        # Verify caching worked
        assert credentials1.credential_type == credentials2.credential_type
        assert credentials1.project_id == credentials2.project_id
        assert second_call_time < first_call_time  # Cache should be faster

        # Test performance metrics
        metrics = credential_manager.get_performance_metrics()
        assert metrics["detection_attempts"] >= 1
        assert metrics["cache_hits"] >= 1
        assert metrics["cache_hit_ratio"] > 0

    async def test_docker_integration_simulation(
        self, mock_environment, sample_service_account
    ):
        """Test Docker integration simulation with mounted secrets."""
        # Simulate Docker environment
        os.environ["DEPLOYMENT_TYPE"] = "docker"

        # Create mock Docker secret mount point
        docker_secret_dir = Path("/tmp/test_docker_secrets")
        docker_secret_dir.mkdir(exist_ok=True)
        docker_secret_file = docker_secret_dir / "google_service_account"

        try:
            # Write service account to mock Docker secret location
            with open(docker_secret_file, "w") as f:
                json.dump(sample_service_account, f)

            # Set Docker secrets environment variable
            os.environ["GOOGLE_APPLICATION_CREDENTIALS_FILE"] = str(docker_secret_file)

            # Test credential detection in Docker environment
            manager = GoogleCloudCredentialManager()
            credentials = await manager.get_credentials()

            assert credentials.credential_type == "service_account"
            assert credentials.project_id == "test-project-12345"

            # Test environment detection
            deployment_type = EnvironmentVariableManager.detect_deployment_type()
            assert deployment_type == "docker"

            # Test Docker-specific configuration
            config = manager.create_veo3_config()
            assert config["project_id"] == "test-project-12345"
            assert config["credential_type"] == "service_account"

        finally:
            # Cleanup mock Docker secret
            docker_secret_file.unlink(missing_ok=True)
            docker_secret_dir.rmdir()


class TestF4CrossModuleIntegration:
    """Test cross-module integration for F4 configuration."""

    def test_configuration_factory_integration(self):
        """Test F4 integration with configuration factory."""
        from src.config.factory import ConfigurationFactory

        # Test that configuration factory can create Veo3 config with F4 integration
        try:
            veo3_config = ConfigurationFactory.create_veo3_config(use_mock=True)
            assert "project_id" in veo3_config
            assert "use_mock" in veo3_config
            assert veo3_config["use_mock"] is True
        except Exception as e:
            # Configuration creation might fail due to missing dependencies,
            # but should not fail due to F4 integration issues
            assert "credential" not in str(e).lower()

    def test_environment_manager_provider_variables(self):
        """Test provider variable management."""
        # Test Google Veo3 variables
        google_vars = EnvironmentVariableManager.get_provider_variables("google_veo3")
        expected_google_vars = [
            "GOOGLE_PROJECT_ID",
            "USE_MOCK_VEO",
            "USE_MOCK_VEO3",
            "GOOGLE_APPLICATION_CREDENTIALS",
            "GOOGLE_APPLICATION_CREDENTIALS_FILE",
            "GOOGLE_CLIENT_ID",
            "GOOGLE_CLIENT_SECRET",
            "VEO3_TIMEOUT",
            "CREDENTIAL_RETRY_ATTEMPTS",
        ]

        for var in expected_google_vars:
            assert var in google_vars

        # Test Azure Sora variables
        azure_vars = EnvironmentVariableManager.get_provider_variables("azure_sora")
        expected_azure_vars = [
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_VERSION",
            "AZURE_OPENAI_DEPLOYMENT_NAME",
        ]

        for var in expected_azure_vars:
            assert var in azure_vars

        # Test unknown provider
        with pytest.raises(ValueError, match="Unknown provider"):
            EnvironmentVariableManager.get_provider_variables("unknown_provider")


# Module-level integration test functions
def test_f4_module_imports():
    """Test that all F4 modules can be imported without errors."""
    # Test core F4 imports
    from src.config.environment_manager import EnvironmentVariableManager
    from src.config.google_cloud_credentials import (
        GoogleCloudCredentialManager,
        get_credential_manager,
    )

    # Test that instances can be created
    credential_manager = get_credential_manager()
    assert isinstance(credential_manager, GoogleCloudCredentialManager)

    env_manager = EnvironmentVariableManager()
    assert hasattr(env_manager, "get_provider_variables")


def test_f4_global_singletons():
    """Test F4 global singleton pattern implementation."""
    from src.config.google_cloud_credentials import get_credential_manager

    # Test that multiple calls return same instance
    manager1 = get_credential_manager()
    manager2 = get_credential_manager()
    assert manager1 is manager2  # Same instance

    # Test that manager has expected interface
    assert hasattr(manager1, "get_credentials")
    assert hasattr(manager1, "create_veo3_config")
    assert hasattr(manager1, "clear_cache")


@pytest.mark.asyncio
async def test_f4_end_to_end_integration():
    """End-to-end integration test for F4 environment configuration."""
    # Store original environment
    original_env = dict(os.environ)

    try:
        # Clear relevant environment variables
        for var in [
            "GOOGLE_PROJECT_ID",
            "GOOGLE_CLIENT_ID",
            "GOOGLE_CLIENT_SECRET",
            "USE_MOCK_VEO3",
        ]:
            os.environ.pop(var, None)

        # Setup test environment
        os.environ["GOOGLE_PROJECT_ID"] = "e2e-test-project"
        os.environ["GOOGLE_CLIENT_ID"] = "e2e-client-id"
        os.environ["GOOGLE_CLIENT_SECRET"] = "e2e-secret"
        os.environ["USE_MOCK_VEO3"] = "true"

        # Test credential manager
        manager = get_credential_manager()
        credentials = await manager.get_credentials()

        assert credentials.credential_type == "oauth"
        assert credentials.project_id == "e2e-test-project"

        # Test configuration creation
        config = manager.create_veo3_config()
        assert config["project_id"] == "e2e-test-project"
        assert config["use_mock"] is True

        # Test environment manager
        env_validation = EnvironmentVariableManager.validate_provider_configuration(
            "google_veo3"
        )
        assert env_validation["provider"] == "google_veo3"
        assert env_validation["mock_mode"] is True

        # Test environment validation
        gcp_validation = validate_google_cloud_environment()
        assert gcp_validation["valid"] is True
        assert gcp_validation["credential_sources"]["oauth"] is True

    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
