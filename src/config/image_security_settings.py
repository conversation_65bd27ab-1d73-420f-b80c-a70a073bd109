"""
Image Security Configuration Settings.

Environment-aware configuration for C1 Image Upload Security Pipeline
with PIL/Pillow integration and comprehensive security settings.
"""

import logging
from typing import Any, Dict, List, Literal, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


class ImageSecurityEnvironmentSettings(BaseSettings):
    """
    Environment-specific image security settings using configuration factory pattern.

    Loads from environment variables with SORA_IMAGE_ prefix for security isolation.
    """

    model_config = SettingsConfigDict(
        env_prefix="SORA_IMAGE_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    # File size limits (MB)
    max_file_size_mb: int = Field(
        default=10, ge=1, le=50, description="Maximum image file size in megabytes"
    )

    # Image dimension limits
    max_width: int = Field(
        default=4096, ge=100, le=8192, description="Maximum image width in pixels"
    )

    max_height: int = Field(
        default=4096, ge=100, le=8192, description="Maximum image height in pixels"
    )

    # Security validation settings
    enable_magic_number_check: bool = Field(
        default=True, description="Enable magic number validation for security"
    )

    enable_pil_validation: bool = Field(
        default=True, description="Enable PIL format validation"
    )

    enable_malware_scan: bool = Field(
        default=False, description="Enable basic malware/steganography scanning"
    )

    strict_mime_type_check: bool = Field(
        default=True, description="Enable strict MIME type validation"
    )

    strip_exif_data: bool = Field(
        default=True, description="Strip EXIF data for security and privacy"
    )

    # Processing settings
    auto_resize: bool = Field(
        default=True, description="Automatically resize oversized images"
    )

    jpeg_quality: int = Field(
        default=85, ge=1, le=100, description="JPEG compression quality"
    )

    png_optimize: bool = Field(default=True, description="Optimize PNG files for size")

    # Performance settings
    processing_timeout_seconds: int = Field(
        default=30, ge=5, le=120, description="Maximum processing time per image"
    )

    max_concurrent_uploads: int = Field(
        default=15, ge=1, le=50, description="Maximum concurrent image uploads"
    )

    # PIL-specific security settings
    pil_load_truncated_images: bool = Field(
        default=False,
        description="Allow PIL to load truncated images (security risk if True)",
    )

    pil_max_image_pixels: Optional[int] = Field(
        default=178956970,  # PIL default: 89478485 * 2
        ge=1000000,
        description="Maximum pixel count for PIL processing (decompression bomb protection)",
    )

    # Allowed formats and extensions
    allowed_mime_types: List[str] = Field(
        default=["image/jpeg", "image/png", "image/webp", "image/gif"],
        description="Allowed MIME types for image uploads",
    )

    allowed_extensions: List[str] = Field(
        default=[".jpg", ".jpeg", ".png", ".webp", ".gif"],
        description="Allowed file extensions for image uploads",
    )

    # Provider integration settings
    default_provider: Literal["google_veo3", "azure_sora"] = Field(
        default="google_veo3",
        description="Default video generation provider for image processing",
    )

    provider_timeout_seconds: int = Field(
        default=60,
        ge=10,
        le=300,
        description="Provider API timeout for image processing",
    )

    # Environment-specific settings
    environment: Literal["development", "staging", "production"] = Field(
        default="development", description="Current deployment environment"
    )

    # Security policy enforcement
    enforce_strict_validation: bool = Field(
        default=True, description="Enforce strict security validation policies"
    )

    log_security_events: bool = Field(
        default=True, description="Log security validation events for monitoring"
    )

    # Development vs Production differences
    development_mode: bool = Field(
        default=True,
        description="Enable development mode with relaxed some security settings",
    )

    @field_validator("allowed_mime_types")
    @classmethod
    def validate_mime_types(cls, v: List[str]) -> List[str]:
        """Validate allowed MIME types for security."""
        valid_types = {
            "image/jpeg",
            "image/png",
            "image/webp",
            "image/gif",
            "image/bmp",
            "image/tiff",
        }

        for mime_type in v:
            if mime_type not in valid_types:
                raise ValueError(f"Unsupported MIME type: {mime_type}")

        return v

    @field_validator("allowed_extensions")
    @classmethod
    def validate_extensions(cls, v: List[str]) -> List[str]:
        """Validate allowed file extensions."""
        valid_extensions = {
            ".jpg",
            ".jpeg",
            ".png",
            ".webp",
            ".gif",
            ".bmp",
            ".tiff",
            ".tif",
        }

        for ext in v:
            if ext.lower() not in valid_extensions:
                raise ValueError(f"Unsupported file extension: {ext}")

        return [ext.lower() for ext in v]

    def get_pil_security_config(self) -> Dict[str, Any]:
        """
        Get PIL-specific security configuration.

        Returns:
            Dict containing PIL security settings
        """
        return {
            "load_truncated_images": self.pil_load_truncated_images,
            "max_image_pixels": self.pil_max_image_pixels,
            "enforce_format_validation": self.enable_pil_validation,
            "strip_exif": self.strip_exif_data,
            "jpeg_quality": self.jpeg_quality,
            "png_optimize": self.png_optimize,
        }

    def get_security_policy(self) -> Dict[str, Any]:
        """
        Get complete security policy configuration.

        Returns:
            Dict containing complete security policy
        """
        return {
            "file_size_limits": {
                "max_file_size_mb": self.max_file_size_mb,
                "max_width": self.max_width,
                "max_height": self.max_height,
            },
            "validation_settings": {
                "magic_number_check": self.enable_magic_number_check,
                "pil_validation": self.enable_pil_validation,
                "malware_scan": self.enable_malware_scan,
                "strict_mime_check": self.strict_mime_type_check,
                "strip_exif": self.strip_exif_data,
            },
            "allowed_formats": {
                "mime_types": self.allowed_mime_types,
                "extensions": self.allowed_extensions,
            },
            "performance_limits": {
                "processing_timeout": self.processing_timeout_seconds,
                "max_concurrent": self.max_concurrent_uploads,
                "pil_max_pixels": self.pil_max_image_pixels,
            },
            "enforcement": {
                "strict_validation": self.enforce_strict_validation,
                "log_events": self.log_security_events,
                "development_mode": self.development_mode,
            },
        }

    def apply_environment_overrides(self) -> None:
        """
        Apply environment-specific security overrides.

        Production: Strictest security settings
        Staging: Production-like with some debugging
        Development: Relaxed settings for development workflow
        """
        if self.environment == "production":
            # Production: Maximum security
            self.enforce_strict_validation = True
            self.enable_malware_scan = True
            self.pil_load_truncated_images = False
            self.development_mode = False
            self.log_security_events = True
            self.strict_mime_type_check = True
            self.strip_exif_data = True

            # Smaller limits for production security
            self.max_file_size_mb = min(self.max_file_size_mb, 10)
            self.max_concurrent_uploads = min(self.max_concurrent_uploads, 10)
            self.processing_timeout_seconds = min(self.processing_timeout_seconds, 60)

            logger.info("Applied production security overrides")

        elif self.environment == "staging":
            # Staging: Production-like but with more logging
            self.enforce_strict_validation = True
            self.enable_malware_scan = False  # Disable for performance in staging
            self.pil_load_truncated_images = False
            self.development_mode = False
            self.log_security_events = True
            self.strict_mime_type_check = True

            logger.info("Applied staging security overrides")

        elif self.environment == "development":
            # Development: Relaxed for development workflow
            self.enforce_strict_validation = True  # Still validate, but allow more
            self.enable_malware_scan = False  # Disable for faster development
            self.pil_load_truncated_images = False  # Keep secure even in dev
            self.development_mode = True
            self.log_security_events = True

            # More generous limits for development
            self.max_file_size_mb = min(self.max_file_size_mb, 20)
            self.processing_timeout_seconds = min(self.processing_timeout_seconds, 90)

            logger.info("Applied development security overrides")


class ImageDependencyValidator:
    """
    Validates and checks image processing dependencies.

    Ensures PIL/Pillow and python-magic are properly installed
    and configured for secure image processing.
    """

    def __init__(self):
        """Initialize dependency validator."""
        self._validation_results: Dict[str, Any] = {}

    def validate_all_dependencies(self) -> Dict[str, Any]:
        """
        Validate all image processing dependencies.

        Returns:
            Dict containing validation results for all dependencies
        """
        results = {
            "timestamp": logger.info.__module__,  # Use a available timestamp
            "overall_status": "unknown",
            "dependencies": {},
            "security_checks": {},
            "recommendations": [],
        }

        try:
            # Validate PIL/Pillow
            results["dependencies"]["pillow"] = self._validate_pillow()

            # Validate python-magic
            results["dependencies"]["python_magic"] = self._validate_python_magic()

            # Validate system dependencies
            results["dependencies"]["system"] = self._validate_system_dependencies()

            # Security configuration checks
            results["security_checks"] = self._validate_security_config()

            # Overall status
            all_deps_ok = all(
                dep.get("status") == "ok" for dep in results["dependencies"].values()
            )
            all_security_ok = all(
                check.get("status") == "ok"
                for check in results["security_checks"].values()
            )

            if all_deps_ok and all_security_ok:
                results["overall_status"] = "ok"
            elif all_deps_ok:
                results["overall_status"] = (
                    "warning"  # Dependencies OK but security issues
                )
            else:
                results["overall_status"] = "error"

            # Generate recommendations
            results["recommendations"] = self._generate_recommendations(results)

            self._validation_results = results
            return results

        except Exception as e:
            logger.error(f"Error during dependency validation: {e}")
            results["overall_status"] = "error"
            results["error"] = str(e)
            return results

    def _validate_pillow(self) -> Dict[str, Any]:
        """Validate PIL/Pillow installation and configuration."""
        try:
            from PIL import Image, ImageFile, ImageOps
            from PIL import __version__ as pil_version
            from PIL.ExifTags import TAGS

            # Check version
            version_info = {"version": pil_version, "status": "ok", "features": []}

            # Check supported formats
            supported_formats = []
            try:
                supported_formats = list(Image.registered_extensions().keys())
                version_info["supported_formats"] = supported_formats
                version_info["features"].append("format_support")
            except Exception as e:
                version_info["warnings"] = [f"Could not check supported formats: {e}"]

            # Check EXIF support
            try:
                if TAGS:
                    version_info["features"].append("exif_support")
            except Exception:
                version_info["warnings"] = version_info.get("warnings", [])
                version_info["warnings"].append("EXIF support not available")

            # Check image operations
            try:
                # Test basic functionality
                from io import BytesIO

                test_img = Image.new("RGB", (100, 100), color="red")
                buffer = BytesIO()
                test_img.save(buffer, format="JPEG")

                if buffer.getvalue():
                    version_info["features"].append("image_processing")
            except Exception as e:
                version_info["status"] = "warning"
                version_info["warnings"] = version_info.get("warnings", [])
                version_info["warnings"].append(f"Image processing test failed: {e}")

            logger.info(f"PIL/Pillow validation successful: {pil_version}")
            return version_info

        except ImportError as e:
            logger.error(f"PIL/Pillow not available: {e}")
            return {
                "status": "error",
                "error": f"PIL/Pillow not installed: {e}",
                "recommendation": "Install with: uv add pillow",
            }
        except Exception as e:
            logger.error(f"PIL/Pillow validation error: {e}")
            return {"status": "error", "error": str(e)}

    def _validate_python_magic(self) -> Dict[str, Any]:
        """Validate python-magic installation and configuration."""
        try:
            import magic

            # Test magic functionality
            test_result = {"status": "ok", "features": []}

            # Test MIME detection
            try:
                mime_detector = magic.Magic(mime=True)
                # Test with a known pattern
                test_data = b"\xff\xd8\xff\xe0\x00\x10JFIF"  # JPEG header
                detected_mime = mime_detector.from_buffer(test_data)

                if "image" in detected_mime:
                    test_result["features"].append("mime_detection")
                    test_result["test_result"] = detected_mime
                else:
                    test_result["warnings"] = [
                        f"Unexpected MIME detection: {detected_mime}"
                    ]

            except Exception as e:
                test_result["status"] = "warning"
                test_result["warnings"] = [f"MIME detection test failed: {e}"]

            # Check version if available
            try:
                if hasattr(magic, "__version__"):
                    test_result["version"] = magic.__version__
            except Exception:
                pass

            logger.info("python-magic validation successful")
            return test_result

        except ImportError as e:
            logger.error(f"python-magic not available: {e}")
            return {
                "status": "error",
                "error": f"python-magic not installed: {e}",
                "recommendation": "Install with: uv add python-magic",
            }
        except Exception as e:
            logger.error(f"python-magic validation error: {e}")
            return {"status": "error", "error": str(e)}

    def _validate_system_dependencies(self) -> Dict[str, Any]:
        """Validate system-level dependencies."""
        try:
            system_info = {"status": "ok", "checks": {}}

            # Check libmagic availability (for python-magic)
            try:
                import magic

                magic.Magic()  # This will fail if libmagic is not available
                system_info["checks"]["libmagic"] = "available"
            except Exception as e:
                system_info["checks"]["libmagic"] = f"error: {e}"
                system_info["status"] = "warning"
                system_info["recommendations"] = [
                    "Install libmagic: brew install libmagic (macOS) or apt-get install libmagic1 (Ubuntu)"
                ]

            # Check available memory for image processing
            try:
                import psutil

                memory = psutil.virtual_memory()
                if memory.available > 500 * 1024 * 1024:  # 500MB
                    system_info["checks"]["memory"] = (
                        f"sufficient: {memory.available // (1024 * 1024)}MB available"
                    )
                else:
                    system_info["checks"]["memory"] = (
                        f"low: {memory.available // (1024 * 1024)}MB available"
                    )
                    system_info["status"] = "warning"
            except ImportError:
                system_info["checks"]["memory"] = (
                    "unable to check (psutil not available)"
                )
            except Exception as e:
                system_info["checks"]["memory"] = f"error checking: {e}"

            return system_info

        except Exception as e:
            logger.error(f"System dependency validation error: {e}")
            return {"status": "error", "error": str(e)}

    def _validate_security_config(self) -> Dict[str, Any]:
        """Validate security configuration."""
        try:
            from PIL import ImageFile

            security_checks = {}

            # Check PIL security settings
            security_checks["pil_truncated_images"] = {
                "current_value": ImageFile.LOAD_TRUNCATED_IMAGES,
                "recommended_value": False,
                "status": "ok" if not ImageFile.LOAD_TRUNCATED_IMAGES else "warning",
                "description": "LOAD_TRUNCATED_IMAGES should be False for security",
            }

            # Check maximum image pixels setting
            try:
                from PIL import Image

                max_pixels = Image.MAX_IMAGE_PIXELS
                security_checks["pil_max_pixels"] = {
                    "current_value": max_pixels,
                    "status": "ok" if max_pixels and max_pixels > 0 else "warning",
                    "description": "MAX_IMAGE_PIXELS should be set to prevent decompression bombs",
                }
            except Exception:
                security_checks["pil_max_pixels"] = {
                    "status": "unknown",
                    "description": "Could not check MAX_IMAGE_PIXELS setting",
                }

            return security_checks

        except Exception as e:
            logger.error(f"Security configuration validation error: {e}")
            return {"error": str(e), "status": "error"}

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        # Check PIL/Pillow status
        pillow_status = results["dependencies"].get("pillow", {}).get("status")
        if pillow_status == "error":
            recommendations.append("Install PIL/Pillow: uv add pillow")
        elif pillow_status == "warning":
            recommendations.append("Review PIL/Pillow configuration and warnings")

        # Check python-magic status
        magic_status = results["dependencies"].get("python_magic", {}).get("status")
        if magic_status == "error":
            recommendations.append("Install python-magic: uv add python-magic")
            recommendations.append("Install system libmagic library if not available")

        # Check security configuration
        security_checks = results.get("security_checks", {})
        for check_name, check_info in security_checks.items():
            if check_info.get("status") == "warning":
                recommendations.append(f"Review security setting: {check_name}")

        # System recommendations
        system_status = results["dependencies"].get("system", {}).get("status")
        if system_status == "warning":
            system_recs = results["dependencies"]["system"].get("recommendations", [])
            recommendations.extend(system_recs)

        return recommendations

    def get_dependency_health_check(self) -> Dict[str, Any]:
        """
        Get a quick health check for image processing dependencies.

        Returns:
            Dict containing health check results
        """
        try:
            # Quick validation without full checks
            health = {
                "healthy": True,
                "timestamp": "now",  # Placeholder
                "components": {},
            }

            # Check PIL basic import
            try:
                from PIL import Image

                health["components"]["pillow"] = {"status": "ok"}
            except ImportError:
                health["components"]["pillow"] = {
                    "status": "error",
                    "error": "Import failed",
                }
                health["healthy"] = False

            # Check python-magic basic import
            try:
                import magic

                health["components"]["python_magic"] = {"status": "ok"}
            except ImportError:
                health["components"]["python_magic"] = {
                    "status": "error",
                    "error": "Import failed",
                }
                health["healthy"] = False

            return health

        except Exception as e:
            logger.error(f"Health check error: {e}")
            return {"healthy": False, "error": str(e), "timestamp": "now"}
