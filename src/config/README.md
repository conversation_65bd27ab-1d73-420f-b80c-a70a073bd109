# Configuration Module

## Purpose
Environment-specific configuration management with factory patterns, security validation, and production-ready architecture for the multi-user video generation system.

## Key Components
- `factory.py`: Configuration factory with automatic environment detection
- `environments.py`: Environment-specific configuration classes
- `security.py`: Security validation and hardening utilities
- `video_config.py`: Video generation parameter management

## Usage Example
```python
from src.config.factory import ConfigurationFactory

# Auto-detect environment and get appropriate config
config = ConfigurationFactory.get_base_config()
azure_config = ConfigurationFactory.get_azure_config()
video_config = ConfigurationFactory.get_video_config()

print(f"Environment: {config.ENVIRONMENT}")
print(f"Azure endpoint: {azure_config.AZURE_OPENAI_ENDPOINT}")
```

## Environment Support
| Environment | Database | Security | Features |
|-------------|----------|----------|----------|
| Development | SQLite | Relaxed | Debug enabled |
| Testing | In-memory | Isolated | Test fixtures |
| Production | PostgreSQL | Hardened | Monitoring |

## Configuration Classes
- **BaseConfig**: Foundation with safe environment variable parsing
- **DevelopmentConfig**: Debug mode with extensive logging
- **TestingConfig**: Isolated testing with in-memory database
- **ProductionConfig**: Security hardened with performance optimization

## Security Features
```python
# Security validation example
from src.config.security import SecurityValidator

validator = SecurityValidator()
security_status = validator.validate_production_config(config)

if not security_status.is_secure:
    raise SecurityError(f"Configuration not secure: {security_status.issues}")
```

## Testing
```bash
# Run configuration tests
uv run pytest src/config/tests/ -v

# Test environment-specific configs
uv run pytest src/config/tests/test_environments.py

# Test security validation
uv run pytest src/config/tests/test_security.py
```

## Environment Variables
```bash
# Core configuration
ENVIRONMENT=development|testing|production
SECRET_KEY=your-secure-secret-key
DATABASE_URL=your-database-connection-string

# Azure OpenAI
AZURE_OPENAI_API_KEY=your-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# Video generation
MAX_CONTENT_LENGTH=104857600
MAX_PROMPT_LENGTH=500
DEFAULT_VIDEO_DURATION=5
```

## Architecture
- **Factory Pattern**: Centralized configuration creation with caching
- **Environment Detection**: Automatic environment-specific configuration
- **Security-First**: Production readiness validation and hardening
- **Type Safety**: Full type hints with Pydantic validation where applicable

## See Also
- [Azure Integration](../features/sora_integration/)
- [Video Configuration](../features/video_generation/)
- [Security Guidelines](../../docs/SECURITY.md)