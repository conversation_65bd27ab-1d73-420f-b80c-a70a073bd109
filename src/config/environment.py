"""Environment detection and configuration utilities for F4 Environment Configuration.

This module provides specialized environment detection utilities that build on
the existing ConfigurationService patterns to provide higher-level environment
management capabilities for provider configuration and deployment optimization.
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, Literal
from uuid import uuid4

from src.config.service import ConfigurationService

logger = logging.getLogger(__name__)


@dataclass
class EnvironmentInfo:
    """
    Comprehensive environment information with deployment context.

    Attributes:
        deployment_type: Type of deployment (local/docker/production)
        environment_name: Environment name (development/staging/production)
        debug_enabled: Whether debug mode is enabled
        testing_mode: Whether testing mode is active
        provider_defaults: Default provider configuration for this environment
        security_level: Security configuration level
        performance_profile: Performance optimization profile
        monitoring_enabled: Whether monitoring is enabled
        rate_limiting_enabled: Whether rate limiting is active
    """

    deployment_type: Literal["local", "docker", "production"]
    environment_name: Literal["development", "testing", "production"]
    debug_enabled: bool
    testing_mode: bool
    provider_defaults: Dict[str, Any]
    security_level: Literal["development", "standard", "strict"]
    performance_profile: Literal["development", "standard", "optimized"]
    monitoring_enabled: bool
    rate_limiting_enabled: bool


class EnvironmentDetector:
    """
    Advanced environment detection with provider-specific optimizations.

    This class builds on ConfigurationService deployment detection to provide
    specialized environment analysis for provider configuration and optimization.
    """

    @staticmethod
    def get_environment_info() -> EnvironmentInfo:
        """
        Get comprehensive environment information for configuration optimization.

        Returns:
            EnvironmentInfo with complete environment context
        """
        logger.debug("Detecting comprehensive environment information")

        # Get base deployment type from ConfigurationService
        deployment_type = ConfigurationService._detect_deployment_type()

        # Get Flask environment
        flask_env = ConfigurationService.get("FLASK_ENV", "development")
        environment_name = (
            flask_env
            if flask_env in ["development", "testing", "production"]
            else "development"
        )

        # Detect debug and testing modes
        debug_enabled = ConfigurationService.get_bool("FLASK_DEBUG", False)
        testing_mode = ConfigurationService.get_bool("TESTING", False)

        # Determine security level
        security_level = EnvironmentDetector._determine_security_level(
            deployment_type, environment_name
        )

        # Determine performance profile
        performance_profile = EnvironmentDetector._determine_performance_profile(
            deployment_type, environment_name
        )

        # Get provider defaults for this environment
        provider_defaults = EnvironmentDetector._get_environment_provider_defaults(
            deployment_type, environment_name
        )

        # Check monitoring and rate limiting
        monitoring_enabled = ConfigurationService.get_bool("METRICS_ENABLED", True)
        rate_limiting_enabled = ConfigurationService.get_bool(
            "RATE_LIMIT_ENABLED", True
        )

        env_info = EnvironmentInfo(
            deployment_type=deployment_type,
            environment_name=environment_name,
            debug_enabled=debug_enabled,
            testing_mode=testing_mode,
            provider_defaults=provider_defaults,
            security_level=security_level,
            performance_profile=performance_profile,
            monitoring_enabled=monitoring_enabled,
            rate_limiting_enabled=rate_limiting_enabled,
        )

        logger.info(
            f"Environment detected: {deployment_type}/{environment_name} "
            f"(security: {security_level}, performance: {performance_profile})"
        )

        return env_info

    @staticmethod
    def _determine_security_level(
        deployment_type: str, environment_name: str
    ) -> Literal["development", "standard", "strict"]:
        """
        Determine security level based on deployment context.

        Args:
            deployment_type: Deployment type from ConfigurationService
            environment_name: Environment name

        Returns:
            Security level classification
        """
        if environment_name == "production":
            return "strict"
        elif environment_name == "testing" or deployment_type == "docker":
            return "standard"
        else:
            return "development"

    @staticmethod
    def _determine_performance_profile(
        deployment_type: str, environment_name: str
    ) -> Literal["development", "standard", "optimized"]:
        """
        Determine performance profile based on deployment context.

        Args:
            deployment_type: Deployment type from ConfigurationService
            environment_name: Environment name

        Returns:
            Performance profile classification
        """
        if environment_name == "production":
            return "optimized"
        elif deployment_type == "docker":
            return "standard"
        else:
            return "development"

    @staticmethod
    def _get_environment_provider_defaults(
        deployment_type: str, environment_name: str
    ) -> Dict[str, Any]:
        """
        Get provider-specific defaults for environment.

        Args:
            deployment_type: Deployment type
            environment_name: Environment name

        Returns:
            Provider defaults dictionary
        """
        defaults = {
            "use_mock_providers": True,
            "default_timeout": 120,
            "max_retries": 2,
            "rate_limit_rpm": 60,
        }

        if environment_name == "production":
            defaults.update(
                {
                    "use_mock_providers": False,
                    "default_timeout": 300,
                    "max_retries": 3,
                    "rate_limit_rpm": 30,
                }
            )
        elif deployment_type == "docker":
            defaults.update(
                {
                    "use_mock_providers": False,
                    "default_timeout": 600,
                    "max_retries": 5,
                    "rate_limit_rpm": 45,
                }
            )
        elif environment_name == "testing":
            defaults.update(
                {
                    "use_mock_providers": True,
                    "default_timeout": 60,
                    "max_retries": 1,
                    "rate_limit_rpm": 120,
                }
            )

        return defaults

    @staticmethod
    def validate_environment_compatibility() -> Dict[str, Any]:
        """
        Validate environment configuration for provider compatibility.

        Returns:
            Validation result with compatibility status and recommendations
        """
        test_id = str(uuid4())[:8]
        logger.info(f"Validating environment compatibility (test_id: {test_id})")

        validation_result = {
            "test_id": test_id,
            "compatible": True,
            "warnings": [],
            "errors": [],
            "recommendations": [],
            "environment_info": {},
        }

        try:
            # Get environment information
            env_info = EnvironmentDetector.get_environment_info()
            validation_result["environment_info"] = {
                "deployment_type": env_info.deployment_type,
                "environment_name": env_info.environment_name,
                "security_level": env_info.security_level,
                "performance_profile": env_info.performance_profile,
            }

            # Validate deployment type consistency
            if env_info.deployment_type == "production" and env_info.debug_enabled:
                validation_result["warnings"].append(
                    "Debug mode enabled in production deployment"
                )
                validation_result["recommendations"].append(
                    "Disable debug mode for production (set FLASK_DEBUG=false)"
                )

            # Validate security configuration
            if (
                env_info.security_level == "development"
                and env_info.deployment_type != "local"
            ):
                validation_result["warnings"].append(
                    "Development security level detected in non-local deployment"
                )
                validation_result["recommendations"].append(
                    "Review security configuration for deployment environment"
                )

            # Validate provider configuration alignment
            if (
                env_info.provider_defaults.get("use_mock_providers")
                and env_info.environment_name == "production"
            ):
                validation_result["errors"].append(
                    "Mock providers enabled in production environment"
                )
                validation_result["compatible"] = False
                validation_result["recommendations"].append(
                    "Disable mock providers for production (set USE_MOCK_VEO=false)"
                )

            # Validate performance configuration
            if (
                env_info.performance_profile == "development"
                and env_info.deployment_type == "production"
            ):
                validation_result["warnings"].append(
                    "Development performance profile in production deployment"
                )
                validation_result["recommendations"].append(
                    "Optimize performance configuration for production workload"
                )

            logger.info(
                f"Environment compatibility validation completed - Compatible: {validation_result['compatible']}"
            )

        except Exception as e:
            validation_result["compatible"] = False
            validation_result["errors"].append(
                f"Environment validation failed: {str(e)}"
            )
            logger.error(f"Environment compatibility validation failed: {e}")

        return validation_result


class EnvironmentOptimizer:
    """
    Environment-specific configuration optimization utilities.

    Provides automated configuration optimization based on environment detection
    and deployment context analysis.
    """

    @staticmethod
    def get_optimized_provider_config(provider: str) -> Dict[str, Any]:
        """
        Get environment-optimized configuration for specified provider.

        Args:
            provider: Provider name ("azure_sora" or "google_veo3")

        Returns:
            Optimized provider configuration
        """
        env_info = EnvironmentDetector.get_environment_info()
        logger.info(
            f"Optimizing {provider} config for {env_info.deployment_type}/{env_info.environment_name}"
        )

        # Base optimization from environment defaults
        optimized_config = env_info.provider_defaults.copy()

        # Provider-specific optimizations
        if provider == "google_veo3":
            optimized_config.update(
                EnvironmentOptimizer._optimize_veo3_config(env_info)
            )
        elif provider == "azure_sora":
            optimized_config.update(
                EnvironmentOptimizer._optimize_azure_config(env_info)
            )

        # Apply security optimizations
        optimized_config.update(
            EnvironmentOptimizer._apply_security_optimizations(env_info)
        )

        # Apply performance optimizations
        optimized_config.update(
            EnvironmentOptimizer._apply_performance_optimizations(env_info)
        )

        logger.info(
            f"Provider config optimized: timeout={optimized_config.get('default_timeout')}, "
            f"retries={optimized_config.get('max_retries')}"
        )

        return optimized_config

    @staticmethod
    def _optimize_veo3_config(env_info: EnvironmentInfo) -> Dict[str, Any]:
        """Optimize Google Veo3 provider configuration."""
        veo3_optimizations = {}

        if env_info.performance_profile == "optimized":
            veo3_optimizations.update(
                {
                    "generation_timeout": 3600,  # Extended for production
                    "rate_limit_rpm": 20,  # Conservative for production
                    "batch_size": 5,  # Batch requests in production
                }
            )
        elif env_info.performance_profile == "development":
            veo3_optimizations.update(
                {
                    "generation_timeout": 900,  # Shorter for development
                    "rate_limit_rpm": 60,  # Higher for development
                    "batch_size": 1,  # Single requests for debugging
                }
            )

        return veo3_optimizations

    @staticmethod
    def _optimize_azure_config(env_info: EnvironmentInfo) -> Dict[str, Any]:
        """Optimize Azure Sora provider configuration."""
        azure_optimizations = {}

        if env_info.performance_profile == "optimized":
            azure_optimizations.update(
                {
                    "connection_pool_size": 20,
                    "request_timeout": 300,
                    "retry_backoff": 2.0,
                }
            )
        elif env_info.performance_profile == "development":
            azure_optimizations.update(
                {
                    "connection_pool_size": 5,
                    "request_timeout": 120,
                    "retry_backoff": 1.0,
                }
            )

        return azure_optimizations

    @staticmethod
    def _apply_security_optimizations(env_info: EnvironmentInfo) -> Dict[str, Any]:
        """Apply security-based configuration optimizations."""
        security_optimizations = {}

        if env_info.security_level == "strict":
            security_optimizations.update(
                {
                    "ssl_verify": True,
                    "request_signing": True,
                    "audit_logging": True,
                    "credential_rotation": True,
                }
            )
        elif env_info.security_level == "development":
            security_optimizations.update(
                {
                    "ssl_verify": False,  # For local development
                    "request_signing": False,
                    "audit_logging": False,
                    "credential_rotation": False,
                }
            )

        return security_optimizations

    @staticmethod
    def _apply_performance_optimizations(env_info: EnvironmentInfo) -> Dict[str, Any]:
        """Apply performance-based configuration optimizations."""
        performance_optimizations = {}

        if env_info.performance_profile == "optimized":
            performance_optimizations.update(
                {
                    "connection_pooling": True,
                    "request_caching": True,
                    "compression": True,
                    "keep_alive": True,
                }
            )
        elif env_info.performance_profile == "development":
            performance_optimizations.update(
                {
                    "connection_pooling": False,  # Simpler debugging
                    "request_caching": False,
                    "compression": False,
                    "keep_alive": False,
                }
            )

        return performance_optimizations


def get_environment_summary() -> Dict[str, Any]:
    """
    Get comprehensive environment summary for debugging and monitoring.

    Returns:
        Environment summary with configuration and recommendations
    """
    test_id = str(uuid4())[:8]
    logger.info(f"Generating environment summary (test_id: {test_id})")

    try:
        env_info = EnvironmentDetector.get_environment_info()
        compatibility = EnvironmentDetector.validate_environment_compatibility()

        summary = {
            "test_id": test_id,
            "environment": {
                "deployment_type": env_info.deployment_type,
                "environment_name": env_info.environment_name,
                "debug_enabled": env_info.debug_enabled,
                "testing_mode": env_info.testing_mode,
                "security_level": env_info.security_level,
                "performance_profile": env_info.performance_profile,
                "monitoring_enabled": env_info.monitoring_enabled,
                "rate_limiting_enabled": env_info.rate_limiting_enabled,
            },
            "provider_defaults": env_info.provider_defaults,
            "compatibility": {
                "compatible": compatibility["compatible"],
                "warnings_count": len(compatibility["warnings"]),
                "errors_count": len(compatibility["errors"]),
                "recommendations_count": len(compatibility["recommendations"]),
            },
            "optimized_configs": {
                "veo3": EnvironmentOptimizer.get_optimized_provider_config(
                    "google_veo3"
                ),
                "azure": EnvironmentOptimizer.get_optimized_provider_config(
                    "azure_sora"
                ),
            },
        }

        logger.info(
            f"Environment summary generated: {env_info.deployment_type}/{env_info.environment_name}"
        )
        return summary

    except Exception as e:
        logger.error(f"Failed to generate environment summary: {e}")
        return {
            "test_id": test_id,
            "error": str(e),
            "environment": {
                "deployment_type": "unknown",
                "environment_name": "unknown",
            },
        }


# Convenience functions for common environment operations
def is_production_environment() -> bool:
    """Check if running in production environment."""
    env_info = EnvironmentDetector.get_environment_info()
    return env_info.environment_name == "production"


def is_development_environment() -> bool:
    """Check if running in development environment."""
    env_info = EnvironmentDetector.get_environment_info()
    return env_info.environment_name == "development"


def is_docker_deployment() -> bool:
    """Check if running in Docker deployment."""
    env_info = EnvironmentDetector.get_environment_info()
    return env_info.deployment_type == "docker"


def get_recommended_provider() -> str:
    """Get recommended provider for current environment."""
    env_info = EnvironmentDetector.get_environment_info()

    if env_info.provider_defaults.get("use_mock_providers", True):
        return "azure_sora"  # Existing stable provider
    else:
        # In production, prefer the default provider setting
        return ConfigurationService.get("DEFAULT_PROVIDER", "azure_sora")
