"""
WebSocket event handlers for real-time communication.

Defines custom event types and handlers for video generation workflow events,
including job status updates, queue position changes, and error notifications.
"""

import logging
from datetime import datetime
from enum import Enum
from typing import Optional

from flask_socketio import emit
from pydantic import BaseModel, Field

from src.core.models import APIResponse

logger = logging.getLogger(__name__)


def _safe_emit(event: str, data=None, room=None, **kwargs) -> bool:
    """
    Safely emit to SocketIO with error handling for Celery worker context.

    Args:
        event: Event name to emit
        data: Data to emit
        room: Room to emit to (optional)
        **kwargs: Additional emit kwargs

    Returns:
        bool: True if successful, False if SocketIO unavailable
    """
    # Always use socketio instance (works in both Flask and Celery contexts)
    from src.realtime.websocket import get_socketio

    socketio = get_socketio()
    if socketio is not None:
        try:
            if room:
                socketio.emit(event, data, room=room, **kwargs)
            else:
                socketio.emit(event, data, **kwargs)
            return True
        except Exception as e:
            logger.debug(f"SocketIO emit failed: {e}")
            return False
    else:
        logger.debug(
            f"SocketIO not available for event '{event}' (normal in Celery worker context)"
        )
        return False


class EventType(str, Enum):
    """WebSocket event types for video generation workflow."""

    JOB_STATUS_UPDATE = "job_status_update"
    QUEUE_POSITION_UPDATE = "queue_position_update"
    JOB_COMPLETED = "job_completed"
    JOB_FAILED = "job_failed"
    QUEUE_STATUS_UPDATE = "queue_status_update"
    SYSTEM_NOTIFICATION = "system_notification"
    ERROR_NOTIFICATION = "error_notification"

    # C2 Provider Selection UI Events
    PROVIDER_STATUS_UPDATE = "provider_status_update"
    PROVIDER_CAPABILITY_CHANGE = "provider_capability_change"
    PROVIDER_AVAILABILITY_CHANGE = "provider_availability_change"
    PROVIDER_PERFORMANCE_UPDATE = "provider_performance_update"
    PROVIDER_HEALTH_CHECK = "provider_health_check"


class JobStatusEvent(BaseModel):
    """Job status update event data."""

    job_id: str = Field(..., description="Video job identifier")
    status: str = Field(..., description="Current job status")
    progress: Optional[int] = Field(None, description="Progress percentage (0-100)")
    message: Optional[str] = Field(None, description="Status message")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")
    estimated_completion: Optional[str] = Field(
        None, description="Estimated completion time"
    )


class QueuePositionEvent(BaseModel):
    """Queue position update event data."""

    job_id: str = Field(..., description="Video job identifier")
    position: int = Field(..., description="Current position in queue")
    total_jobs: int = Field(..., description="Total jobs in queue")
    estimated_wait_minutes: int = Field(
        ..., description="Estimated wait time in minutes"
    )
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class JobCompletedEvent(BaseModel):
    """Job completion event data."""

    job_id: str = Field(..., description="Video job identifier")
    download_url: Optional[str] = Field(None, description="Video download URL")
    file_size: Optional[int] = Field(None, description="Video file size in bytes")
    duration: Optional[float] = Field(None, description="Video duration in seconds")
    processing_time: Optional[float] = Field(
        None, description="Total processing time in seconds"
    )
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class JobFailedEvent(BaseModel):
    """Job failure event data."""

    job_id: str = Field(..., description="Video job identifier")
    error_message: str = Field(..., description="Error description")
    error_code: Optional[str] = Field(None, description="Error code for debugging")
    retry_possible: bool = Field(default=True, description="Whether retry is possible")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class QueueStatusEvent(BaseModel):
    """Global queue status update event data."""

    total_jobs: int = Field(..., description="Total jobs in queue")
    active_jobs: int = Field(..., description="Currently processing jobs")
    completed_jobs_today: int = Field(..., description="Jobs completed today")
    average_processing_time: Optional[float] = Field(
        None, description="Average processing time in minutes"
    )
    system_load: Optional[float] = Field(None, description="System load percentage")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class SystemNotificationEvent(BaseModel):
    """System-wide notification event data."""

    level: str = Field(..., description="Notification level (info, warning, error)")
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    action_required: bool = Field(
        default=False, description="Whether user action is required"
    )
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


# C2 Provider Selection UI Event Models


class ProviderStatusEvent(BaseModel):
    """Provider status update event data."""

    provider_id: str = Field(..., description="Provider identifier")
    status: str = Field(
        ..., description="Provider status (online, degraded, offline, maintenance)"
    )
    previous_status: Optional[str] = Field(None, description="Previous provider status")
    response_time_ms: Optional[float] = Field(
        None, description="Current response time in milliseconds"
    )
    success_rate: float = Field(0.0, description="Success rate percentage (0-100)")
    error_count_24h: int = Field(0, description="Error count in last 24 hours")
    is_configured: bool = Field(False, description="Provider properly configured")
    configuration_errors: list = Field(
        default_factory=list, description="Configuration issues"
    )
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class ProviderCapabilityEvent(BaseModel):
    """Provider capability change event data."""

    provider_id: str = Field(..., description="Provider identifier")
    capabilities: dict = Field(..., description="Updated provider capabilities")
    previous_capabilities: Optional[dict] = Field(
        None, description="Previous capabilities"
    )
    capability_changes: dict = Field(
        default_factory=dict, description="Specific capability changes"
    )
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class ProviderAvailabilityEvent(BaseModel):
    """Provider availability change event data."""

    provider_id: str = Field(..., description="Provider identifier")
    is_available: bool = Field(..., description="Provider availability status")
    availability_reason: Optional[str] = Field(
        None, description="Reason for availability change"
    )
    estimated_recovery_time: Optional[str] = Field(
        None, description="Estimated recovery time if unavailable"
    )
    affected_features: list = Field(
        default_factory=list, description="Features affected by availability change"
    )
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class ProviderPerformanceEvent(BaseModel):
    """Provider performance metrics update event data."""

    provider_id: str = Field(..., description="Provider identifier")
    average_response_time: float = Field(
        0.0, description="Average response time in milliseconds"
    )
    total_requests: int = Field(0, description="Total requests processed")
    successful_requests: int = Field(0, description="Successful requests")
    failed_requests: int = Field(0, description="Failed requests")
    concurrent_jobs: int = Field(0, description="Current concurrent jobs")
    queue_length: int = Field(0, description="Current queue length")
    uptime_percentage: float = Field(0.0, description="Uptime percentage")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class ProviderHealthCheckEvent(BaseModel):
    """Provider health check result event data."""

    provider_id: str = Field(..., description="Provider identifier")
    health_status: dict = Field(..., description="Health check results")
    check_duration_ms: float = Field(
        ..., description="Health check duration in milliseconds"
    )
    health_score: float = Field(0.0, description="Overall health score (0-100)")
    warnings: list = Field(default_factory=list, description="Health check warnings")
    errors: list = Field(default_factory=list, description="Health check errors")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")


class EventHandler:
    """Handles WebSocket event emission and formatting."""

    @staticmethod
    def emit_job_status_update(
        job_id: str,
        status: str,
        room: str,
        progress: Optional[int] = None,
        message: Optional[str] = None,
        estimated_completion: Optional[str] = None,
    ) -> None:
        """
        Emit job status update to specific room.

        Args:
            job_id: Video job identifier
            status: Current job status
            room: WebSocket room to emit to
            progress: Optional progress percentage
            message: Optional status message
            estimated_completion: Optional estimated completion time
        """
        event_data = JobStatusEvent(
            job_id=job_id,
            status=status,
            progress=progress,
            message=message,
            estimated_completion=estimated_completion,
        )

        response = APIResponse(
            success=True,
            message=f"Job status updated to {status}",
            data=event_data.model_dump(),
        )

        _safe_emit(EventType.JOB_STATUS_UPDATE, response.model_dump(), room=room)

        logger.info(
            f"Emitted job status update - Job: {job_id}, Status: {status}, Room: {room}"
        )

    @staticmethod
    def emit_queue_position_update(
        job_id: str,
        position: int,
        total_jobs: int,
        estimated_wait_minutes: int,
        room: str,
    ) -> None:
        """
        Emit queue position update to specific room.

        Args:
            job_id: Video job identifier
            position: Current position in queue
            total_jobs: Total jobs in queue
            estimated_wait_minutes: Estimated wait time
            room: WebSocket room to emit to
        """
        event_data = QueuePositionEvent(
            job_id=job_id,
            position=position,
            total_jobs=total_jobs,
            estimated_wait_minutes=estimated_wait_minutes,
        )

        response = APIResponse(
            success=True,
            message=f"Queue position: {position} of {total_jobs}",
            data=event_data.model_dump(),
        )

        emit(EventType.QUEUE_POSITION_UPDATE, response.model_dump(), room=room)

        logger.info(
            f"Emitted queue position update - Job: {job_id}, Position: {position}"
        )

    @staticmethod
    def emit_job_completed(
        job_id: str,
        room: str,
        download_url: Optional[str] = None,
        file_size: Optional[int] = None,
        duration: Optional[float] = None,
        processing_time: Optional[float] = None,
    ) -> None:
        """
        Emit job completion event to specific room.

        Args:
            job_id: Video job identifier
            room: WebSocket room to emit to
            download_url: Optional video download URL
            file_size: Optional video file size
            duration: Optional video duration
            processing_time: Optional processing time
        """
        event_data = JobCompletedEvent(
            job_id=job_id,
            download_url=download_url,
            file_size=file_size,
            duration=duration,
            processing_time=processing_time,
        )

        response = APIResponse(
            success=True,
            message="Video generation completed successfully",
            data=event_data.model_dump(),
        )

        emit(EventType.JOB_COMPLETED, response.model_dump(), room=room)

        logger.info(f"Emitted job completion - Job: {job_id}, Room: {room}")

    @staticmethod
    def emit_job_failed(
        job_id: str,
        error_message: str,
        room: str,
        error_code: Optional[str] = None,
        retry_possible: bool = True,
    ) -> None:
        """
        Emit job failure event to specific room.

        Args:
            job_id: Video job identifier
            error_message: Error description
            room: WebSocket room to emit to
            error_code: Optional error code
            retry_possible: Whether retry is possible
        """
        event_data = JobFailedEvent(
            job_id=job_id,
            error_message=error_message,
            error_code=error_code,
            retry_possible=retry_possible,
        )

        response = APIResponse(
            success=False,
            message="Video generation failed",
            data=event_data.model_dump(),
        )

        emit(EventType.JOB_FAILED, response.model_dump(), room=room)

        logger.error(f"Emitted job failure - Job: {job_id}, Error: {error_message}")

    @staticmethod
    def emit_queue_status_update(
        total_jobs: int,
        active_jobs: int,
        completed_jobs_today: int,
        average_processing_time: Optional[float] = None,
        system_load: Optional[float] = None,
    ) -> None:
        """
        Emit global queue status update to all connected clients.

        Args:
            total_jobs: Total jobs in queue
            active_jobs: Currently processing jobs
            completed_jobs_today: Jobs completed today
            average_processing_time: Optional average processing time
            system_load: Optional system load percentage
        """
        event_data = QueueStatusEvent(
            total_jobs=total_jobs,
            active_jobs=active_jobs,
            completed_jobs_today=completed_jobs_today,
            average_processing_time=average_processing_time,
            system_load=system_load,
        )

        response = APIResponse(
            success=True, message="Queue status update", data=event_data.model_dump()
        )

        emit(EventType.QUEUE_STATUS_UPDATE, response.model_dump(), broadcast=True)

        logger.info(
            f"Emitted queue status update - Total: {total_jobs}, Active: {active_jobs}"
        )

    @staticmethod
    def emit_system_notification(
        level: str,
        title: str,
        message: str,
        action_required: bool = False,
        room: Optional[str] = None,
    ) -> None:
        """
        Emit system notification to specific room or all clients.

        Args:
            level: Notification level (info, warning, error)
            title: Notification title
            message: Notification message
            action_required: Whether user action is required
            room: Optional specific room (broadcasts to all if None)
        """
        event_data = SystemNotificationEvent(
            level=level, title=title, message=message, action_required=action_required
        )

        response = APIResponse(
            success=True,
            message=f"System notification: {level}",
            data=event_data.model_dump(),
        )

        emit_kwargs = {
            "event": EventType.SYSTEM_NOTIFICATION,
            "data": response.model_dump(),
        }

        if room:
            emit_kwargs["room"] = room
        else:
            emit_kwargs["broadcast"] = True

        emit(**emit_kwargs)

        logger.info(f"Emitted system notification - Level: {level}, Title: {title}")

    # C2 Provider Selection UI Event Handlers

    @staticmethod
    def emit_provider_status_update(
        provider_id: str,
        status: str,
        previous_status: Optional[str] = None,
        response_time_ms: Optional[float] = None,
        success_rate: float = 0.0,
        error_count_24h: int = 0,
        is_configured: bool = True,
        configuration_errors: list = None,
        room: Optional[str] = None,
    ) -> None:
        """
        Emit provider status update to WebSocket clients.

        Args:
            provider_id: Provider identifier
            status: Current provider status
            previous_status: Previous provider status
            response_time_ms: Current response time
            success_rate: Success rate percentage
            error_count_24h: Error count in last 24 hours
            is_configured: Provider configuration status
            configuration_errors: Configuration issues
            room: Optional specific room (broadcasts to all if None)
        """
        event_data = ProviderStatusEvent(
            provider_id=provider_id,
            status=status,
            previous_status=previous_status,
            response_time_ms=response_time_ms,
            success_rate=success_rate,
            error_count_24h=error_count_24h,
            is_configured=is_configured,
            configuration_errors=configuration_errors or [],
        )

        response = APIResponse(
            success=True,
            message=f"Provider {provider_id} status updated to {status}",
            data=event_data.model_dump(),
        )

        emit_kwargs = {
            "event": EventType.PROVIDER_STATUS_UPDATE,
            "data": response.model_dump(),
        }

        if room:
            emit_kwargs["room"] = room
        else:
            emit_kwargs["broadcast"] = True

        emit(**emit_kwargs)

        logger.info(
            f"Emitted provider status update - Provider: {provider_id}, Status: {status}"
        )

    @staticmethod
    def emit_provider_capability_change(
        provider_id: str,
        capabilities: dict,
        previous_capabilities: Optional[dict] = None,
        capability_changes: dict = None,
        room: Optional[str] = None,
    ) -> None:
        """
        Emit provider capability change to WebSocket clients.

        Args:
            provider_id: Provider identifier
            capabilities: Updated provider capabilities
            previous_capabilities: Previous capabilities
            capability_changes: Specific capability changes
            room: Optional specific room (broadcasts to all if None)
        """
        event_data = ProviderCapabilityEvent(
            provider_id=provider_id,
            capabilities=capabilities,
            previous_capabilities=previous_capabilities,
            capability_changes=capability_changes or {},
        )

        response = APIResponse(
            success=True,
            message=f"Provider {provider_id} capabilities updated",
            data=event_data.model_dump(),
        )

        emit_kwargs = {
            "event": EventType.PROVIDER_CAPABILITY_CHANGE,
            "data": response.model_dump(),
        }

        if room:
            emit_kwargs["room"] = room
        else:
            emit_kwargs["broadcast"] = True

        emit(**emit_kwargs)

        logger.info(f"Emitted provider capability change - Provider: {provider_id}")

    @staticmethod
    def emit_provider_availability_change(
        provider_id: str,
        is_available: bool,
        availability_reason: Optional[str] = None,
        estimated_recovery_time: Optional[str] = None,
        affected_features: list = None,
        room: Optional[str] = None,
    ) -> None:
        """
        Emit provider availability change to WebSocket clients.

        Args:
            provider_id: Provider identifier
            is_available: Provider availability status
            availability_reason: Reason for availability change
            estimated_recovery_time: Estimated recovery time
            affected_features: Features affected by change
            room: Optional specific room (broadcasts to all if None)
        """
        event_data = ProviderAvailabilityEvent(
            provider_id=provider_id,
            is_available=is_available,
            availability_reason=availability_reason,
            estimated_recovery_time=estimated_recovery_time,
            affected_features=affected_features or [],
        )

        response = APIResponse(
            success=True,
            message=f"Provider {provider_id} availability changed to {'available' if is_available else 'unavailable'}",
            data=event_data.model_dump(),
        )

        emit_kwargs = {
            "event": EventType.PROVIDER_AVAILABILITY_CHANGE,
            "data": response.model_dump(),
        }

        if room:
            emit_kwargs["room"] = room
        else:
            emit_kwargs["broadcast"] = True

        emit(**emit_kwargs)

        status_word = "available" if is_available else "unavailable"
        logger.info(
            f"Emitted provider availability change - Provider: {provider_id}, Status: {status_word}"
        )

    @staticmethod
    def emit_provider_performance_update(
        provider_id: str,
        average_response_time: float = 0.0,
        total_requests: int = 0,
        successful_requests: int = 0,
        failed_requests: int = 0,
        concurrent_jobs: int = 0,
        queue_length: int = 0,
        uptime_percentage: float = 0.0,
        room: Optional[str] = None,
    ) -> None:
        """
        Emit provider performance metrics to WebSocket clients.

        Args:
            provider_id: Provider identifier
            average_response_time: Average response time in milliseconds
            total_requests: Total requests processed
            successful_requests: Successful requests
            failed_requests: Failed requests
            concurrent_jobs: Current concurrent jobs
            queue_length: Current queue length
            uptime_percentage: Uptime percentage
            room: Optional specific room (broadcasts to all if None)
        """
        event_data = ProviderPerformanceEvent(
            provider_id=provider_id,
            average_response_time=average_response_time,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            concurrent_jobs=concurrent_jobs,
            queue_length=queue_length,
            uptime_percentage=uptime_percentage,
        )

        response = APIResponse(
            success=True,
            message=f"Provider {provider_id} performance metrics updated",
            data=event_data.model_dump(),
        )

        emit_kwargs = {
            "event": EventType.PROVIDER_PERFORMANCE_UPDATE,
            "data": response.model_dump(),
        }

        if room:
            emit_kwargs["room"] = room
        else:
            emit_kwargs["broadcast"] = True

        emit(**emit_kwargs)

        logger.info(f"Emitted provider performance update - Provider: {provider_id}")

    @staticmethod
    def emit_provider_health_check(
        provider_id: str,
        health_status: dict,
        check_duration_ms: float,
        health_score: float = 0.0,
        warnings: list = None,
        errors: list = None,
        room: Optional[str] = None,
    ) -> None:
        """
        Emit provider health check results to WebSocket clients.

        Args:
            provider_id: Provider identifier
            health_status: Health check results
            check_duration_ms: Health check duration
            health_score: Overall health score
            warnings: Health check warnings
            errors: Health check errors
            room: Optional specific room (broadcasts to all if None)
        """
        event_data = ProviderHealthCheckEvent(
            provider_id=provider_id,
            health_status=health_status,
            check_duration_ms=check_duration_ms,
            health_score=health_score,
            warnings=warnings or [],
            errors=errors or [],
        )

        response = APIResponse(
            success=True,
            message=f"Provider {provider_id} health check completed",
            data=event_data.model_dump(),
        )

        emit_kwargs = {
            "event": EventType.PROVIDER_HEALTH_CHECK,
            "data": response.model_dump(),
        }

        if room:
            emit_kwargs["room"] = room
        else:
            emit_kwargs["broadcast"] = True

        emit(**emit_kwargs)

        logger.info(
            f"Emitted provider health check - Provider: {provider_id}, Score: {health_score}"
        )


# Create global event handler instance
event_handler = EventHandler()
