# Realtime Module Documentation

## Overview

Production-ready WebSocket implementation using Flask-SocketIO for real-time job status updates, user notifications, and bi-directional communication. Supports session-based room management, event broadcasting, and connection state management.

## Module Structure

```
src/realtime/
├── websocket.py         # Flask-SocketIO implementation
├── events.py           # WebSocket event handlers
├── broadcaster.py      # Status broadcasting and room management
└── tests/              # Co-located realtime tests
    ├── test_websocket.py
    ├── test_broadcaster.py
    └── test_events.py
```

## WebSocket Implementation

### Flask-SocketIO Setup
```python
# src/realtime/websocket.py
from flask import Flask, request
from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections and real-time communication."""
    
    def __init__(self, app: Flask):
        self.socketio = SocketIO(
            app,
            cors_allowed_origins="*",  # Configure for production
            async_mode='threading',    # Use threading for compatibility
            logger=False,              # Disable debug logging
            engineio_logger=False
        )
        
        self.connected_clients = {}  # Track connected clients
        self.session_rooms = {}      # Map sessions to rooms
        
        # Register event handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register WebSocket event handlers."""
        
        @self.socketio.on('connect')
        def handle_connect(auth):
            """Handle client connection."""
            client_id = request.sid
            client_ip = request.environ.get('REMOTE_ADDR', 'unknown')
            
            logger.info(f"Client connected: {client_id} from {client_ip}")
            
            # Track connection
            self.connected_clients[client_id] = {
                'ip': client_ip,
                'connected_at': time.time(),
                'session_id': None,
                'user_agent': request.headers.get('User-Agent', 'unknown')
            }
            
            # Send welcome message
            emit('status', {
                'type': 'connection',
                'message': 'Connected to real-time updates',
                'client_id': client_id
            })
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection."""
            client_id = request.sid
            
            if client_id in self.connected_clients:
                client_info = self.connected_clients[client_id]
                session_id = client_info.get('session_id')
                
                # Leave session room if connected
                if session_id:
                    self._leave_session_room(client_id, session_id)
                
                # Remove from tracking
                del self.connected_clients[client_id]
                
                logger.info(f"Client disconnected: {client_id}")
        
        @self.socketio.on('join_session')
        def handle_join_session(data):
            """Handle client joining session room."""
            client_id = request.sid
            session_id = data.get('session_id')
            
            if not session_id:
                emit('error', {'message': 'Session ID required'})
                return
            
            # Validate session (implement session validation)
            if not self._validate_session(session_id, request.environ.get('REMOTE_ADDR')):
                emit('error', {'message': 'Invalid session'})
                return
            
            # Join session room
            self._join_session_room(client_id, session_id)
            
            emit('status', {
                'type': 'session_joined',
                'session_id': session_id,
                'message': f'Joined session room: {session_id}'
            })
        
        @self.socketio.on('job_status_request')
        def handle_job_status_request(data):
            """Handle request for job status updates."""
            client_id = request.sid
            job_id = data.get('job_id')
            
            if not job_id:
                emit('error', {'message': 'Job ID required'})
                return
            
            # Get current job status
            job_status = self._get_job_status(job_id)
            if job_status:
                emit('job_status', job_status)
            else:
                emit('error', {'message': f'Job not found: {job_id}'})
    
    def _join_session_room(self, client_id: str, session_id: str):
        """Join client to session room."""
        room_name = f"session_{session_id}"
        join_room(room_name)
        
        # Track room membership
        if client_id in self.connected_clients:
            self.connected_clients[client_id]['session_id'] = session_id
        
        if session_id not in self.session_rooms:
            self.session_rooms[session_id] = set()
        self.session_rooms[session_id].add(client_id)
        
        logger.info(f"Client {client_id} joined session room: {room_name}")
    
    def _leave_session_room(self, client_id: str, session_id: str):
        """Remove client from session room."""
        room_name = f"session_{session_id}"
        leave_room(room_name)
        
        # Update tracking
        if client_id in self.connected_clients:
            self.connected_clients[client_id]['session_id'] = None
        
        if session_id in self.session_rooms:
            self.session_rooms[session_id].discard(client_id)
            if not self.session_rooms[session_id]:
                del self.session_rooms[session_id]
        
        logger.info(f"Client {client_id} left session room: {room_name}")
    
    def _validate_session(self, session_id: str, client_ip: str) -> bool:
        """Validate session ID and IP consistency."""
        # Implement session validation logic
        # This should integrate with your session manager
        try:
            from src.session.manager import SessionManager
            from redis import Redis
            
            session_manager = SessionManager(Redis())
            return session_manager.validate_session(session_id, client_ip)
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return False
    
    def _get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get current job status from database."""
        try:
            from src.api.job_repository import JobRepository
            
            job_repository = JobRepository()
            job = job_repository.get_job_by_id(job_id)
            
            if job:
                return {
                    'job_id': job_id,
                    'status': job.status,
                    'progress': job.progress,
                    'message': job.message,
                    'updated_at': job.updated_at.isoformat() if job.updated_at else None
                }
            return None
        except Exception as e:
            logger.error(f"Error getting job status: {e}")
            return None
```

## Event Broadcasting

### StatusBroadcaster Class
```python
# src/realtime/broadcaster.py
from typing import Dict, Any, Optional, List
from flask_socketio import SocketIO, emit
import logging

logger = logging.getLogger(__name__)

class StatusBroadcaster:
    """Handles broadcasting of status updates to connected clients."""
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
    
    def broadcast_job_status(self, job_id: str, status_data: Dict[str, Any], session_id: Optional[str] = None):
        """
        Broadcast job status update to relevant clients.
        
        Args:
            job_id: Job identifier
            status_data: Status information to broadcast
            session_id: Optional session ID to limit broadcast scope
        """
        message = {
            'type': 'job_status_update',
            'job_id': job_id,
            'timestamp': time.time(),
            **status_data
        }
        
        if session_id:
            # Broadcast to specific session room
            room_name = f"session_{session_id}"
            self.socketio.emit('job_status', message, room=room_name)
            logger.info(f"Broadcasted job status to session {session_id}: {job_id}")
        else:
            # Broadcast to all connected clients
            self.socketio.emit('job_status', message)
            logger.info(f"Broadcasted job status globally: {job_id}")
    
    def broadcast_queue_update(self, queue_stats: Dict[str, Any], session_id: Optional[str] = None):
        """Broadcast queue statistics update."""
        message = {
            'type': 'queue_update',
            'timestamp': time.time(),
            **queue_stats
        }
        
        if session_id:
            room_name = f"session_{session_id}"
            self.socketio.emit('queue_status', message, room=room_name)
        else:
            self.socketio.emit('queue_status', message)
    
    def broadcast_system_notification(self, notification: Dict[str, Any], session_id: Optional[str] = None):
        """Broadcast system-wide notifications."""
        message = {
            'type': 'system_notification',
            'timestamp': time.time(),
            **notification
        }
        
        if session_id:
            room_name = f"session_{session_id}"
            self.socketio.emit('notification', message, room=room_name)
        else:
            self.socketio.emit('notification', message)
        
        logger.info(f"System notification broadcasted: {notification.get('message', 'Unknown')}")
    
    def broadcast_error(self, error_data: Dict[str, Any], session_id: Optional[str] = None):
        """Broadcast error messages to clients."""
        message = {
            'type': 'error',
            'timestamp': time.time(),
            **error_data
        }
        
        if session_id:
            room_name = f"session_{session_id}"
            self.socketio.emit('error', message, room=room_name)
        else:
            self.socketio.emit('error', message)
        
        logger.warning(f"Error broadcasted: {error_data.get('message', 'Unknown error')}")
    
    def get_connected_clients_count(self) -> int:
        """Get count of connected WebSocket clients."""
        # This is an approximation - exact count requires tracking
        return len(self.socketio.server.manager.rooms.get('/', {}))
    
    def get_session_clients_count(self, session_id: str) -> int:
        """Get count of clients in session room."""
        room_name = f"session_{session_id}"
        room_clients = self.socketio.server.manager.rooms.get('/', {}).get(room_name, set())
        return len(room_clients)
```

## Event Handlers

### Custom Event Handlers
```python
# src/realtime/events.py
from typing import Dict, Any, Callable
from flask_socketio import emit
import logging

logger = logging.getLogger(__name__)

class EventHandlers:
    """Custom event handlers for WebSocket communication."""
    
    def __init__(self, websocket_manager, status_broadcaster):
        self.websocket_manager = websocket_manager
        self.status_broadcaster = status_broadcaster
        self.event_callbacks = {}
    
    def register_callback(self, event_type: str, callback: Callable):
        """Register custom callback for event type."""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        self.event_callbacks[event_type].append(callback)
    
    def handle_video_generation_start(self, job_data: Dict[str, Any]):
        """Handle video generation start event."""
        session_id = job_data.get('session_id')
        job_id = job_data.get('job_id')
        
        status_data = {
            'status': 'started',
            'message': 'Video generation has started',
            'progress': 0,
            'estimated_duration': job_data.get('estimated_duration', 'Unknown')
        }
        
        self.status_broadcaster.broadcast_job_status(job_id, status_data, session_id)
        
        # Execute custom callbacks
        self._execute_callbacks('video_generation_start', job_data)
    
    def handle_video_generation_progress(self, job_data: Dict[str, Any]):
        """Handle video generation progress updates."""
        session_id = job_data.get('session_id')
        job_id = job_data.get('job_id')
        progress = job_data.get('progress', 0)
        
        status_data = {
            'status': 'in_progress',
            'progress': progress,
            'message': f'Video generation {progress}% complete'
        }
        
        self.status_broadcaster.broadcast_job_status(job_id, status_data, session_id)
        
        # Execute custom callbacks
        self._execute_callbacks('video_generation_progress', job_data)
    
    def handle_video_generation_complete(self, job_data: Dict[str, Any]):
        """Handle video generation completion."""
        session_id = job_data.get('session_id')
        job_id = job_data.get('job_id')
        
        status_data = {
            'status': 'completed',
            'progress': 100,
            'message': 'Video generation completed successfully',
            'download_url': job_data.get('download_url'),
            'file_size': job_data.get('file_size'),
            'duration': job_data.get('video_duration')
        }
        
        self.status_broadcaster.broadcast_job_status(job_id, status_data, session_id)
        
        # Execute custom callbacks
        self._execute_callbacks('video_generation_complete', job_data)
    
    def handle_video_generation_error(self, job_data: Dict[str, Any]):
        """Handle video generation errors."""
        session_id = job_data.get('session_id')
        job_id = job_data.get('job_id')
        error_message = job_data.get('error', 'Unknown error occurred')
        
        status_data = {
            'status': 'failed',
            'message': f'Video generation failed: {error_message}',
            'error': error_message,
            'retry_possible': job_data.get('retry_possible', False)
        }
        
        self.status_broadcaster.broadcast_job_status(job_id, status_data, session_id)
        
        # Execute custom callbacks
        self._execute_callbacks('video_generation_error', job_data)
    
    def handle_queue_status_change(self, queue_data: Dict[str, Any]):
        """Handle queue status changes."""
        session_id = queue_data.get('session_id')
        
        self.status_broadcaster.broadcast_queue_update(queue_data, session_id)
        
        # Execute custom callbacks
        self._execute_callbacks('queue_status_change', queue_data)
    
    def _execute_callbacks(self, event_type: str, data: Dict[str, Any]):
        """Execute registered callbacks for event type."""
        if event_type in self.event_callbacks:
            for callback in self.event_callbacks[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error executing callback for {event_type}: {e}")
```

## Integration Patterns

### Job Queue Integration
```python
# Integration with Celery tasks for real-time updates
from src.realtime.broadcaster import StatusBroadcaster
from src.job_queue.celery_app import celery_app

def broadcast_job_status(job_id: str, status: str, session_id: str, **kwargs):
    """Broadcast job status update from Celery task."""
    try:
        # Get SocketIO instance (this requires proper setup)
        from src.main import socketio  # Import from main app
        
        broadcaster = StatusBroadcaster(socketio)
        
        status_data = {
            'status': status,
            'timestamp': time.time(),
            **kwargs
        }
        
        broadcaster.broadcast_job_status(job_id, status_data, session_id)
    except Exception as e:
        logger.error(f"Failed to broadcast job status: {e}")

# Usage in Celery tasks
@celery_app.task(bind=True)
def process_video_generation_with_updates(self, session_id: str, job_id: str, params: dict):
    """Video generation task with real-time updates."""
    try:
        # Broadcast start
        broadcast_job_status(job_id, 'running', session_id, message='Starting video generation')
        
        # Process video generation
        # ... (implementation)
        
        # Broadcast progress updates
        for progress in range(0, 101, 20):
            broadcast_job_status(job_id, 'in_progress', session_id, progress=progress)
            time.sleep(1)  # Simulate work
        
        # Broadcast completion
        broadcast_job_status(job_id, 'completed', session_id, 
                           message='Video generation completed',
                           download_url='/download/video.mp4')
        
    except Exception as exc:
        # Broadcast error
        broadcast_job_status(job_id, 'failed', session_id, 
                           error=str(exc),
                           message='Video generation failed')
        raise
```

### Flask Application Integration
```python
# src/main.py integration
from flask import Flask
from src.realtime.websocket import WebSocketManager
from src.realtime.broadcaster import StatusBroadcaster
from src.realtime.events import EventHandlers

def create_app():
    """Create Flask application with WebSocket support."""
    app = Flask(__name__)
    
    # Initialize WebSocket manager
    websocket_manager = WebSocketManager(app)
    socketio = websocket_manager.socketio
    
    # Initialize broadcaster and event handlers
    status_broadcaster = StatusBroadcaster(socketio)
    event_handlers = EventHandlers(websocket_manager, status_broadcaster)
    
    # Make available globally for other modules
    app.websocket_manager = websocket_manager
    app.status_broadcaster = status_broadcaster
    app.event_handlers = event_handlers
    
    return app, socketio

# Usage
app, socketio = create_app()

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5001, debug=True)
```

## Client-Side JavaScript

### WebSocket Client Implementation
```javascript
// Frontend WebSocket client
class VideoGenerationClient {
    constructor(serverUrl = 'http://localhost:5001') {
        this.socket = io(serverUrl);
        this.sessionId = null;
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.onConnectionStatusChange(true);
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.onConnectionStatusChange(false);
        });
        
        this.socket.on('job_status', (data) => {
            this.handleJobStatusUpdate(data);
        });
        
        this.socket.on('queue_status', (data) => {
            this.handleQueueUpdate(data);
        });
        
        this.socket.on('notification', (data) => {
            this.handleNotification(data);
        });
        
        this.socket.on('error', (data) => {
            this.handleError(data);
        });
    }
    
    joinSession(sessionId) {
        this.sessionId = sessionId;
        this.socket.emit('join_session', { session_id: sessionId });
    }
    
    requestJobStatus(jobId) {
        this.socket.emit('job_status_request', { job_id: jobId });
    }
    
    handleJobStatusUpdate(data) {
        const { job_id, status, progress, message } = data;
        
        // Update UI elements
        const jobElement = document.getElementById(`job-${job_id}`);
        if (jobElement) {
            this.updateJobDisplay(jobElement, status, progress, message);
        }
        
        // Handle specific status changes
        switch (status) {
            case 'completed':
                this.onJobCompleted(data);
                break;
            case 'failed':
                this.onJobFailed(data);
                break;
            case 'in_progress':
                this.onJobProgress(data);
                break;
        }
    }
    
    updateJobDisplay(element, status, progress, message) {
        // Update status badge
        const statusBadge = element.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.textContent = status;
            statusBadge.className = `status-badge status-${status}`;
        }
        
        // Update progress bar
        const progressBar = element.querySelector('.progress-bar');
        if (progressBar && progress !== undefined) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
        
        // Update message
        const messageElement = element.querySelector('.status-message');
        if (messageElement && message) {
            messageElement.textContent = message;
        }
    }
    
    onJobCompleted(data) {
        // Show completion notification
        this.showNotification('Video generation completed!', 'success');
        
        // Enable download button if available
        if (data.download_url) {
            const downloadBtn = document.getElementById(`download-${data.job_id}`);
            if (downloadBtn) {
                downloadBtn.href = data.download_url;
                downloadBtn.style.display = 'block';
            }
        }
    }
    
    onJobFailed(data) {
        this.showNotification(`Job failed: ${data.error}`, 'error');
    }
    
    onJobProgress(data) {
        // Optional: Update progress indicator
        if (data.progress) {
            this.updateProgressDisplay(data.job_id, data.progress);
        }
    }
    
    showNotification(message, type = 'info') {
        // Create and show notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize client
const client = new VideoGenerationClient();

// Join session when available
if (window.sessionId) {
    client.joinSession(window.sessionId);
}
```

## Testing Patterns

### WebSocket Testing
```python
import pytest
from unittest.mock import Mock, patch
from src.realtime.websocket import WebSocketManager
from src.realtime.broadcaster import StatusBroadcaster

class TestWebSocketManager:
    """Test WebSocket functionality."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        from flask import Flask
        app = Flask(__name__)
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def websocket_manager(self, app):
        """Create WebSocket manager for testing."""
        return WebSocketManager(app)
    
    def test_client_connection(self, websocket_manager):
        """Test client connection handling."""
        # Mock SocketIO for testing
        with patch.object(websocket_manager.socketio, 'emit') as mock_emit:
            # Simulate connection
            websocket_manager._register_handlers()
            
            # Verify handlers are registered
            assert hasattr(websocket_manager.socketio, 'on')
    
    def test_session_room_management(self, websocket_manager):
        """Test session room join/leave functionality."""
        client_id = 'test_client_123'
        session_id = 'test_session_456'
        
        # Test joining room
        websocket_manager._join_session_room(client_id, session_id)
        assert client_id in websocket_manager.session_rooms[session_id]
        
        # Test leaving room
        websocket_manager._leave_session_room(client_id, session_id)
        assert session_id not in websocket_manager.session_rooms

class TestStatusBroadcaster:
    """Test status broadcasting functionality."""
    
    @pytest.fixture
    def socketio_mock(self):
        """Mock SocketIO for testing."""
        return Mock()
    
    @pytest.fixture
    def broadcaster(self, socketio_mock):
        """Create status broadcaster with mocked SocketIO."""
        return StatusBroadcaster(socketio_mock)
    
    def test_job_status_broadcast(self, broadcaster, socketio_mock):
        """Test job status broadcasting."""
        job_id = 'test_job_123'
        status_data = {'status': 'completed', 'progress': 100}
        session_id = 'test_session_456'
        
        broadcaster.broadcast_job_status(job_id, status_data, session_id)
        
        # Verify SocketIO emit was called
        socketio_mock.emit.assert_called_once()
        
        # Verify message structure
        call_args = socketio_mock.emit.call_args
        event_name = call_args[0][0]
        message = call_args[0][1]
        
        assert event_name == 'job_status'
        assert message['job_id'] == job_id
        assert message['status'] == 'completed'
        assert message['progress'] == 100
```

## Performance Optimization

### Connection Management
```python
class OptimizedWebSocketManager(WebSocketManager):
    """WebSocket manager with performance optimizations."""
    
    def __init__(self, app: Flask):
        super().__init__(app)
        self.connection_pool_size = 1000
        self.heartbeat_interval = 30
        self.message_queue_size = 100
        
        # Configure SocketIO for performance
        self.socketio.server_options.update({
            'ping_timeout': 60,
            'ping_interval': 25,
            'max_http_buffer_size': 1000000
        })
    
    def cleanup_stale_connections(self):
        """Clean up stale WebSocket connections."""
        current_time = time.time()
        stale_timeout = 300  # 5 minutes
        
        stale_clients = []
        for client_id, info in self.connected_clients.items():
            if current_time - info['connected_at'] > stale_timeout:
                # Check if connection is still active
                if not self._is_client_active(client_id):
                    stale_clients.append(client_id)
        
        # Remove stale connections
        for client_id in stale_clients:
            self._cleanup_client(client_id)
        
        return len(stale_clients)
    
    def _is_client_active(self, client_id: str) -> bool:
        """Check if client connection is still active."""
        try:
            # This would need to be implemented based on your SocketIO version
            return client_id in self.socketio.server.manager.rooms.get('/', {})
        except Exception:
            return False
```

### Message Batching
```python
class BatchedStatusBroadcaster(StatusBroadcaster):
    """Status broadcaster with message batching for performance."""
    
    def __init__(self, socketio: SocketIO, batch_size: int = 10, batch_timeout: float = 1.0):
        super().__init__(socketio)
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.message_batch = []
        self.last_batch_time = time.time()
    
    def broadcast_job_status_batched(self, job_id: str, status_data: Dict[str, Any], session_id: Optional[str] = None):
        """Add message to batch for efficient broadcasting."""
        message = {
            'type': 'job_status_update',
            'job_id': job_id,
            'session_id': session_id,
            'timestamp': time.time(),
            **status_data
        }
        
        self.message_batch.append(message)
        
        # Check if batch should be sent
        if (len(self.message_batch) >= self.batch_size or 
            time.time() - self.last_batch_time > self.batch_timeout):
            self._flush_batch()
    
    def _flush_batch(self):
        """Send all batched messages."""
        if not self.message_batch:
            return
        
        # Group messages by session for efficient broadcasting
        session_messages = {}
        global_messages = []
        
        for message in self.message_batch:
            session_id = message.get('session_id')
            if session_id:
                if session_id not in session_messages:
                    session_messages[session_id] = []
                session_messages[session_id].append(message)
            else:
                global_messages.append(message)
        
        # Broadcast to session rooms
        for session_id, messages in session_messages.items():
            room_name = f"session_{session_id}"
            self.socketio.emit('batch_job_status', {'messages': messages}, room=room_name)
        
        # Broadcast global messages
        if global_messages:
            self.socketio.emit('batch_job_status', {'messages': global_messages})
        
        # Clear batch
        self.message_batch.clear()
        self.last_batch_time = time.time()
```

## Best Practices

1. **Session Management**: Always validate sessions before joining rooms
2. **Error Handling**: Implement comprehensive error handling for connection issues
3. **Performance**: Use message batching for high-frequency updates
4. **Security**: Validate all incoming WebSocket data
5. **Testing**: Mock SocketIO for unit testing
6. **Monitoring**: Track connection metrics and message rates
7. **Cleanup**: Implement connection cleanup for stale connections
8. **Documentation**: Document all WebSocket events and message formats
9. **Scalability**: Consider using Redis adapter for multi-server deployments
10. **Client Resilience**: Implement reconnection logic on the client side

## Troubleshooting

### Common Issues

**WebSocket Connection Failures**
```bash
# Check if SocketIO is properly initialized
python -c "
from src.main import create_app
app, socketio = create_app()
print(f'SocketIO initialized: {socketio is not None}')
"

# Test WebSocket endpoint
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://localhost:5001/socket.io/
```

**Client Not Receiving Updates**
```javascript
// Debug client connection
console.log('Socket connected:', socket.connected);
console.log('Socket ID:', socket.id);

// Check if events are being received
socket.onAny((event, ...args) => {
    console.log('Received event:', event, args);
});
```

**High Memory Usage**
```bash
# Check connected clients count
python -c "
from src.realtime.websocket import WebSocketManager
# Check connected clients count and cleanup
"

# Monitor WebSocket memory usage
ps aux | grep python | grep socket
```

### Development Commands
```bash
# Test WebSocket functionality
uv run pytest src/realtime/tests/ -v

# Test with real WebSocket connections
uv run pytest src/realtime/tests/test_websocket.py -v --capture=no

# Start development server with WebSocket support
uv run python src/main.py
```