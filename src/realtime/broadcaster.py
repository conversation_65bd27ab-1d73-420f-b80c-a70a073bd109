"""
Status broadcasting system for WebSocket real-time updates.

Provides centralized broadcasting functionality to send job status updates,
queue position changes, and system notifications to connected WebSocket clients
with proper room-based isolation.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import redis

from src.realtime.events import <PERSON><PERSON>and<PERSON>
from src.realtime.websocket import get_socketio

logger = logging.getLogger(__name__)


class RedisBroadcaster:
    """Redis-based message broadcasting for distributed WebSocket updates."""

    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        """
        Initialize Redis broadcaster.

        Args:
            redis_url: Redis connection URL
        """
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub: Optional[redis.client.PubSub] = None

    def connect(self) -> None:
        """Establish Redis connection for pub/sub messaging."""
        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            self.pubsub = self.redis_client.pubsub()

            # Test connection
            self.redis_client.ping()
            logger.info("Redis broadcaster connected successfully")

        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
            self.pubsub = None

    def disconnect(self) -> None:
        """Close Redis connections."""
        if self.pubsub:
            self.pubsub.close()
        if self.redis_client:
            self.redis_client.close()
        logger.info("Redis broadcaster disconnected")

    def publish_job_status(self, job_id: str, status_data: Dict[str, Any]) -> None:
        """
        Publish job status update to Redis for distributed workers.

        Args:
            job_id: Video job identifier
            status_data: Status update data
        """
        if not self.redis_client:
            logger.warning("Redis not connected, skipping job status publish")
            return

        channel = f"job_status:{job_id}"
        message = {
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            **status_data,
        }

        try:
            self.redis_client.publish(channel, str(message))
            logger.debug(
                f"Published job status to Redis - Job: {job_id}, Channel: {channel}"
            )
        except redis.RedisError as e:
            logger.error(f"Failed to publish job status to Redis: {e}")

    def subscribe_job_updates(self) -> None:
        """Subscribe to job update channels for processing distributed messages."""
        if not self.pubsub:
            logger.warning("Redis pubsub not available, skipping subscription")
            return

        # Subscribe to all job status channels
        self.pubsub.psubscribe("job_status:*")
        logger.info("Subscribed to job status updates on Redis")


# Global Redis broadcaster instance
redis_broadcaster = RedisBroadcaster()


def _safe_socketio_emit(
    event: str, data: Any, room: Optional[str] = None, broadcast: bool = False
) -> bool:
    """
    Safely emit to SocketIO with error handling for Celery worker context.

    Args:
        event: Event name to emit
        data: Data to emit
        room: Room to emit to (optional)
        broadcast: Whether to broadcast to all clients

    Returns:
        bool: True if successful, False if SocketIO unavailable
    """
    socketio = get_socketio()
    if socketio is not None:
        try:
            if room:
                socketio.emit(event, data, room=room)
            elif broadcast:
                socketio.emit(event, data, broadcast=True)
            else:
                socketio.emit(event, data)
            return True
        except Exception as e:
            logger.debug(f"SocketIO emit failed: {e}")
            return False
    else:
        logger.debug(
            f"SocketIO not available for event '{event}' (normal in Celery worker context)"
        )
        return False


def broadcast_job_status(
    job_id: str,
    status: str,
    session_id: Optional[str] = None,
    progress: Optional[int] = None,
    message: Optional[str] = None,
    estimated_completion: Optional[str] = None,
) -> None:
    """
    Broadcast job status update to WebSocket clients and Redis.

    Args:
        job_id: Video job identifier
        status: Current job status
        session_id: Optional session ID for targeted updates
        progress: Optional progress percentage (0-100)
        message: Optional status message
        estimated_completion: Optional estimated completion time
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping job status broadcast")
        return

    # Determine target room(s)
    rooms = [f"job_{job_id}"]
    if session_id:
        rooms.append(f"session_{session_id}")

    # Broadcast to each room
    for room in rooms:
        EventHandler.emit_job_status_update(
            job_id=job_id,
            status=status,
            room=room,
            progress=progress,
            message=message,
            estimated_completion=estimated_completion,
        )

    # Publish to Redis for distributed workers
    status_data = {
        "status": status,
        "progress": progress,
        "message": message,
        "estimated_completion": estimated_completion,
        "session_id": session_id,
    }
    redis_broadcaster.publish_job_status(job_id, status_data)

    logger.info(f"Broadcasted job status update - Job: {job_id}, Status: {status}")


def broadcast_queue_update(
    job_id: str,
    position: int,
    total_jobs: int,
    estimated_wait_minutes: int,
    session_id: Optional[str] = None,
) -> None:
    """
    Broadcast queue position update to WebSocket clients.

    Args:
        job_id: Video job identifier
        position: Current position in queue
        total_jobs: Total jobs in queue
        estimated_wait_minutes: Estimated wait time in minutes
        session_id: Optional session ID for targeted updates
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping queue update broadcast")
        return

    # Determine target room(s)
    rooms = [f"job_{job_id}"]
    if session_id:
        rooms.append(f"session_{session_id}")

    # Broadcast to each room
    for room in rooms:
        EventHandler.emit_queue_position_update(
            job_id=job_id,
            position=position,
            total_jobs=total_jobs,
            estimated_wait_minutes=estimated_wait_minutes,
            room=room,
        )

    logger.info(
        f"Broadcasted queue update - Job: {job_id}, Position: {position}/{total_jobs}"
    )


def broadcast_job_completed(
    job_id: str,
    session_id: Optional[str] = None,
    download_url: Optional[str] = None,
    file_size: Optional[int] = None,
    duration: Optional[float] = None,
    processing_time: Optional[float] = None,
) -> None:
    """
    Broadcast job completion to WebSocket clients.

    Args:
        job_id: Video job identifier
        session_id: Optional session ID for targeted updates
        download_url: Optional video download URL
        file_size: Optional video file size
        duration: Optional video duration
        processing_time: Optional processing time
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping job completion broadcast")
        return

    # Determine target room(s)
    rooms = [f"job_{job_id}"]
    if session_id:
        rooms.append(f"session_{session_id}")

    # Broadcast to each room
    for room in rooms:
        EventHandler.emit_job_completed(
            job_id=job_id,
            room=room,
            download_url=download_url,
            file_size=file_size,
            duration=duration,
            processing_time=processing_time,
        )

    logger.info(f"Broadcasted job completion - Job: {job_id}")


def broadcast_job_failed(
    job_id: str,
    error_message: str,
    session_id: Optional[str] = None,
    error_code: Optional[str] = None,
    retry_possible: bool = True,
) -> None:
    """
    Broadcast job failure to WebSocket clients.

    Args:
        job_id: Video job identifier
        error_message: Error description
        session_id: Optional session ID for targeted updates
        error_code: Optional error code
        retry_possible: Whether retry is possible
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping job failure broadcast")
        return

    # Determine target room(s)
    rooms = [f"job_{job_id}"]
    if session_id:
        rooms.append(f"session_{session_id}")

    # Broadcast to each room
    for room in rooms:
        EventHandler.emit_job_failed(
            job_id=job_id,
            error_message=error_message,
            room=room,
            error_code=error_code,
            retry_possible=retry_possible,
        )

    logger.error(f"Broadcasted job failure - Job: {job_id}, Error: {error_message}")


def broadcast_system_notification(
    level: str,
    title: str,
    message: str,
    action_required: bool = False,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast system notification to WebSocket clients.

    Args:
        level: Notification level (info, warning, error)
        title: Notification title
        message: Notification message
        action_required: Whether user action is required
        target_sessions: Optional list of session IDs to target (broadcasts to all if None)
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping system notification broadcast"
        )
        return

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            room = f"session_{session_id}"
            EventHandler.emit_system_notification(
                level=level,
                title=title,
                message=message,
                action_required=action_required,
                room=room,
            )
    else:
        # Broadcast to all connected clients
        EventHandler.emit_system_notification(
            level=level, title=title, message=message, action_required=action_required
        )

    logger.info(f"Broadcasted system notification - Level: {level}, Title: {title}")


def initialize_broadcaster(redis_url: str = "redis://localhost:6379/0") -> None:
    """
    Initialize the Redis broadcaster with connection.

    Args:
        redis_url: Redis connection URL
    """
    global redis_broadcaster
    redis_broadcaster = RedisBroadcaster(redis_url)
    redis_broadcaster.connect()


def cleanup_broadcaster() -> None:
    """Cleanup Redis broadcaster connections."""
    redis_broadcaster.disconnect()


# Provider-aware broadcasting extensions for C3 integration


def broadcast_provider_job_status(
    job_id: str,
    provider: str,
    status: str,
    session_id: Optional[str] = None,
    progress: Optional[int] = None,
    message: Optional[str] = None,
    estimated_completion: Optional[str] = None,
    provider_metadata: Optional[dict] = None,
) -> None:
    """
    Broadcast provider-specific job status update to WebSocket clients.

    Args:
        job_id: Video job identifier
        provider: Provider name ('azure_sora' or 'google_veo3')
        status: Current job status
        session_id: Optional session ID for targeted updates
        progress: Optional progress percentage (0-100)
        message: Optional status message
        estimated_completion: Optional estimated completion time
        provider_metadata: Optional provider-specific metadata
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider job status broadcast"
        )
        return

    # Determine target room(s) with provider-specific rooms
    rooms = [f"job_{job_id}", f"provider_{provider}"]
    if session_id:
        rooms.extend(
            [f"session_{session_id}", f"session_{session_id}_provider_{provider}"]
        )

    # Enhanced status data with provider information
    provider_status_data = {
        "job_id": job_id,
        "provider": provider,
        "status": status,
        "progress": progress,
        "message": message,
        "estimated_completion": estimated_completion,
        "provider_metadata": provider_metadata or {},
        "timestamp": datetime.now().isoformat(),
    }

    # Broadcast to each room with provider-aware event
    if socketio is not None:
        for room in rooms:
            _safe_socketio_emit(
                "provider_job_status_update", provider_status_data, room=room
            )
    else:
        logger.warning(
            f"SocketIO not available for provider job status broadcast - Job: {job_id}, Provider: {provider}"
        )

    # Also broadcast legacy event for backward compatibility
    broadcast_job_status(
        job_id=job_id,
        status=status,
        session_id=session_id,
        progress=progress,
        message=message,
        estimated_completion=estimated_completion,
    )

    # Publish to Redis with provider information
    redis_status_data = {
        "provider": provider,
        "status": status,
        "progress": progress,
        "message": message,
        "estimated_completion": estimated_completion,
        "session_id": session_id,
        "provider_metadata": provider_metadata,
    }
    redis_broadcaster.publish_job_status(job_id, redis_status_data)

    logger.info(
        f"Broadcasted provider job status - Job: {job_id}, Provider: {provider}, Status: {status}"
    )


def broadcast_provider_queue_update(
    job_id: str,
    provider: str,
    position: int,
    total_jobs: int,
    estimated_wait_minutes: int,
    session_id: Optional[str] = None,
    provider_queue_size: Optional[int] = None,
) -> None:
    """
    Broadcast provider-specific queue position update to WebSocket clients.

    Args:
        job_id: Video job identifier
        provider: Provider name
        position: Current position in provider queue
        total_jobs: Total jobs across all providers
        estimated_wait_minutes: Estimated wait time in minutes
        session_id: Optional session ID for targeted updates
        provider_queue_size: Optional size of provider-specific queue
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider queue update broadcast"
        )
        return

    # Target rooms with provider specificity
    rooms = [f"job_{job_id}", f"provider_{provider}"]
    if session_id:
        rooms.extend(
            [f"session_{session_id}", f"session_{session_id}_provider_{provider}"]
        )

    # Provider-aware queue data
    provider_queue_data = {
        "job_id": job_id,
        "provider": provider,
        "provider_position": position,
        "provider_queue_size": provider_queue_size or position,
        "total_jobs": total_jobs,
        "estimated_wait_minutes": estimated_wait_minutes,
        "timestamp": datetime.now().isoformat(),
    }

    # Broadcast provider-specific queue update
    for room in rooms:
        _safe_socketio_emit("provider_queue_update", provider_queue_data, room=room)

    # Also broadcast legacy event for backward compatibility
    broadcast_queue_update(
        job_id=job_id,
        position=position,
        total_jobs=total_jobs,
        estimated_wait_minutes=estimated_wait_minutes,
        session_id=session_id,
    )

    logger.info(
        f"Broadcasted provider queue update - Job: {job_id}, Provider: {provider}, Position: {position}/{provider_queue_size}"
    )


def broadcast_provider_health_update(
    provider: str,
    health_status: str,
    health_details: dict,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider health status update to WebSocket clients.

    Args:
        provider: Provider name
        health_status: Health status ('healthy', 'degraded', 'unhealthy')
        health_details: Detailed health information
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping provider health broadcast")
        return

    health_data = {
        "provider": provider,
        "health_status": health_status,
        "health_details": health_details,
        "timestamp": datetime.now().isoformat(),
    }

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            rooms = [
                f"session_{session_id}",
                f"session_{session_id}_provider_{provider}",
            ]
            for room in rooms:
                socketio.emit("provider_health_update", health_data, room=room)
    else:
        # Broadcast to all provider subscribers
        socketio.emit(
            "provider_health_update", health_data, room=f"provider_{provider}"
        )

    logger.info(
        f"Broadcasted provider health update - Provider: {provider}, Status: {health_status}"
    )


def broadcast_provider_capability_update(
    provider: str, capabilities: dict, available: bool = True
) -> None:
    """
    Broadcast provider capability update to WebSocket clients.

    Args:
        provider: Provider name
        capabilities: Provider capabilities dictionary
        available: Whether provider is currently available
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider capability broadcast"
        )
        return

    capability_data = {
        "provider": provider,
        "capabilities": capabilities,
        "available": available,
        "timestamp": datetime.now().isoformat(),
    }

    # Broadcast to all provider subscribers
    socketio.emit(
        "provider_capability_update", capability_data, room=f"provider_{provider}"
    )

    # Also broadcast to general system updates
    socketio.emit("system_capability_update", capability_data)

    logger.info(
        f"Broadcasted provider capability update - Provider: {provider}, Available: {available}"
    )


def broadcast_multi_provider_status(
    session_id: str, provider_statuses: Dict[str, Dict]
) -> None:
    """
    Broadcast comprehensive multi-provider status to a user session.

    Args:
        session_id: User session identifier
        provider_statuses: Dictionary of provider statuses keyed by provider name
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping multi-provider status broadcast"
        )
        return

    multi_provider_data = {
        "session_id": session_id,
        "providers": provider_statuses,
        "total_providers": len(provider_statuses),
        "healthy_providers": sum(
            1
            for status in provider_statuses.values()
            if status.get("health_status") == "healthy"
        ),
        "timestamp": datetime.now().isoformat(),
    }

    # Target session-specific rooms
    rooms = [f"session_{session_id}"]
    for room in rooms:
        socketio.emit("multi_provider_status", multi_provider_data, room=room)

    logger.info(
        f"Broadcasted multi-provider status - Session: {session_id}, Providers: {list(provider_statuses.keys())}"
    )


def subscribe_to_provider_updates(session_id: str, providers: List[str]) -> None:
    """
    Subscribe a session to provider-specific update rooms.

    Args:
        session_id: User session identifier
        providers: List of provider names to subscribe to
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping provider subscription")
        return

    # Join provider-specific rooms for this session
    for provider in providers:
        room_name = f"session_{session_id}_provider_{provider}"
        # Note: This would typically be called from WebSocket connection handler
        # socketio.join_room(room_name, sid=session_id)
        logger.info(f"Subscribed session {session_id} to provider {provider} updates")


def unsubscribe_from_provider_updates(session_id: str, providers: List[str]) -> None:
    """
    Unsubscribe a session from provider-specific update rooms.

    Args:
        session_id: User session identifier
        providers: List of provider names to unsubscribe from
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping provider unsubscription")
        return

    # Leave provider-specific rooms for this session
    for provider in providers:
        room_name = f"session_{session_id}_provider_{provider}"
        # Note: This would typically be called from WebSocket disconnection handler
        # socketio.leave_room(room_name, sid=session_id)
        logger.info(
            f"Unsubscribed session {session_id} from provider {provider} updates"
        )


# C2 Provider Selection UI Broadcasting Functions


def broadcast_provider_status_change(
    provider_id: str,
    status: str,
    previous_status: Optional[str] = None,
    response_time_ms: Optional[float] = None,
    success_rate: float = 0.0,
    error_count_24h: int = 0,
    is_configured: bool = True,
    configuration_errors: list = None,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider status change using enhanced EventHandler.

    Args:
        provider_id: Provider identifier
        status: Current provider status
        previous_status: Previous provider status
        response_time_ms: Current response time
        success_rate: Success rate percentage
        error_count_24h: Error count in last 24 hours
        is_configured: Provider configuration status
        configuration_errors: Configuration issues
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning("SocketIO not initialized, skipping provider status broadcast")
        return

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            room = f"session_{session_id}"
            EventHandler.emit_provider_status_update(
                provider_id=provider_id,
                status=status,
                previous_status=previous_status,
                response_time_ms=response_time_ms,
                success_rate=success_rate,
                error_count_24h=error_count_24h,
                is_configured=is_configured,
                configuration_errors=configuration_errors,
                room=room,
            )
    else:
        # Broadcast to all clients
        EventHandler.emit_provider_status_update(
            provider_id=provider_id,
            status=status,
            previous_status=previous_status,
            response_time_ms=response_time_ms,
            success_rate=success_rate,
            error_count_24h=error_count_24h,
            is_configured=is_configured,
            configuration_errors=configuration_errors,
        )

    # Publish to Redis for distributed updates
    redis_data = {
        "provider_id": provider_id,
        "status": status,
        "previous_status": previous_status,
        "response_time_ms": response_time_ms,
        "success_rate": success_rate,
        "error_count_24h": error_count_24h,
        "is_configured": is_configured,
        "configuration_errors": configuration_errors or [],
    }

    if redis_broadcaster.redis_client:
        try:
            redis_broadcaster.redis_client.publish(
                f"provider_status:{provider_id}", str(redis_data)
            )
        except Exception as e:
            logger.error(f"Failed to publish provider status to Redis: {e}")

    logger.info(
        f"Broadcasted provider status change - Provider: {provider_id}, Status: {status}"
    )


def broadcast_provider_capability_change(
    provider_id: str,
    capabilities: dict,
    previous_capabilities: Optional[dict] = None,
    capability_changes: dict = None,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider capability change using enhanced EventHandler.

    Args:
        provider_id: Provider identifier
        capabilities: Updated provider capabilities
        previous_capabilities: Previous capabilities
        capability_changes: Specific capability changes
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider capability broadcast"
        )
        return

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            room = f"session_{session_id}"
            EventHandler.emit_provider_capability_change(
                provider_id=provider_id,
                capabilities=capabilities,
                previous_capabilities=previous_capabilities,
                capability_changes=capability_changes,
                room=room,
            )
    else:
        # Broadcast to all clients
        EventHandler.emit_provider_capability_change(
            provider_id=provider_id,
            capabilities=capabilities,
            previous_capabilities=previous_capabilities,
            capability_changes=capability_changes,
        )

    logger.info(f"Broadcasted provider capability change - Provider: {provider_id}")


def broadcast_provider_availability_change(
    provider_id: str,
    is_available: bool,
    availability_reason: Optional[str] = None,
    estimated_recovery_time: Optional[str] = None,
    affected_features: list = None,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider availability change using enhanced EventHandler.

    Args:
        provider_id: Provider identifier
        is_available: Provider availability status
        availability_reason: Reason for availability change
        estimated_recovery_time: Estimated recovery time
        affected_features: Features affected by change
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider availability broadcast"
        )
        return

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            room = f"session_{session_id}"
            EventHandler.emit_provider_availability_change(
                provider_id=provider_id,
                is_available=is_available,
                availability_reason=availability_reason,
                estimated_recovery_time=estimated_recovery_time,
                affected_features=affected_features,
                room=room,
            )
    else:
        # Broadcast to all clients
        EventHandler.emit_provider_availability_change(
            provider_id=provider_id,
            is_available=is_available,
            availability_reason=availability_reason,
            estimated_recovery_time=estimated_recovery_time,
            affected_features=affected_features,
        )

    status_word = "available" if is_available else "unavailable"
    logger.info(
        f"Broadcasted provider availability change - Provider: {provider_id}, Status: {status_word}"
    )


def broadcast_provider_performance_update(
    provider_id: str,
    average_response_time: float = 0.0,
    total_requests: int = 0,
    successful_requests: int = 0,
    failed_requests: int = 0,
    concurrent_jobs: int = 0,
    queue_length: int = 0,
    uptime_percentage: float = 0.0,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider performance metrics using enhanced EventHandler.

    Args:
        provider_id: Provider identifier
        average_response_time: Average response time in milliseconds
        total_requests: Total requests processed
        successful_requests: Successful requests
        failed_requests: Failed requests
        concurrent_jobs: Current concurrent jobs
        queue_length: Current queue length
        uptime_percentage: Uptime percentage
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider performance broadcast"
        )
        return

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            room = f"session_{session_id}"
            EventHandler.emit_provider_performance_update(
                provider_id=provider_id,
                average_response_time=average_response_time,
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                concurrent_jobs=concurrent_jobs,
                queue_length=queue_length,
                uptime_percentage=uptime_percentage,
                room=room,
            )
    else:
        # Broadcast to all clients
        EventHandler.emit_provider_performance_update(
            provider_id=provider_id,
            average_response_time=average_response_time,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            concurrent_jobs=concurrent_jobs,
            queue_length=queue_length,
            uptime_percentage=uptime_percentage,
        )

    logger.info(f"Broadcasted provider performance update - Provider: {provider_id}")


def broadcast_provider_health_check_result(
    provider_id: str,
    health_status: dict,
    check_duration_ms: float,
    health_score: float = 0.0,
    warnings: list = None,
    errors: list = None,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider health check results using enhanced EventHandler.

    Args:
        provider_id: Provider identifier
        health_status: Health check results
        check_duration_ms: Health check duration
        health_score: Overall health score
        warnings: Health check warnings
        errors: Health check errors
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider health check broadcast"
        )
        return

    if target_sessions:
        # Send to specific sessions
        for session_id in target_sessions:
            room = f"session_{session_id}"
            EventHandler.emit_provider_health_check(
                provider_id=provider_id,
                health_status=health_status,
                check_duration_ms=check_duration_ms,
                health_score=health_score,
                warnings=warnings,
                errors=errors,
                room=room,
            )
    else:
        # Broadcast to all clients
        EventHandler.emit_provider_health_check(
            provider_id=provider_id,
            health_status=health_status,
            check_duration_ms=check_duration_ms,
            health_score=health_score,
            warnings=warnings,
            errors=errors,
        )

    logger.info(
        f"Broadcasted provider health check - Provider: {provider_id}, Score: {health_score}"
    )


def broadcast_all_provider_statuses(
    provider_statuses: Dict[str, Dict], target_sessions: Optional[List[str]] = None
) -> None:
    """
    Broadcast comprehensive status for all providers.

    Args:
        provider_statuses: Dictionary of provider status data keyed by provider ID
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping all provider status broadcast"
        )
        return

    # Broadcast individual provider updates
    for provider_id, status_data in provider_statuses.items():
        broadcast_provider_status_change(
            provider_id=provider_id, target_sessions=target_sessions, **status_data
        )

    # Broadcast summary event
    summary_data = {
        "total_providers": len(provider_statuses),
        "online_providers": sum(
            1
            for status in provider_statuses.values()
            if status.get("status") == "online"
        ),
        "degraded_providers": sum(
            1
            for status in provider_statuses.values()
            if status.get("status") == "degraded"
        ),
        "offline_providers": sum(
            1
            for status in provider_statuses.values()
            if status.get("status") == "offline"
        ),
        "timestamp": datetime.utcnow().isoformat() + "Z",
    }

    if target_sessions:
        for session_id in target_sessions:
            room = f"session_{session_id}"
            socketio.emit("all_providers_status_summary", summary_data, room=room)
    else:
        socketio.emit("all_providers_status_summary", summary_data, broadcast=True)

    logger.info(
        f"Broadcasted all provider statuses - Total: {len(provider_statuses)} providers"
    )


def broadcast_provider_recommendation_change(
    session_id: str,
    preferred_provider: Optional[str] = None,
    fallback_providers: Optional[List[str]] = None,
    performance_priority: Optional[str] = None,
    target_sessions: Optional[List[str]] = None,
) -> None:
    """
    Broadcast provider recommendation change to WebSocket clients.

    Args:
        session_id: User session identifier
        preferred_provider: User's preferred provider
        fallback_providers: List of fallback providers
        performance_priority: Performance priority setting
        target_sessions: Optional list of session IDs to target
    """
    socketio = get_socketio()
    if not socketio:
        logger.warning(
            "SocketIO not initialized, skipping provider recommendation broadcast"
        )
        return

    recommendation_data = {
        "session_id": session_id,
        "preferred_provider": preferred_provider,
        "fallback_providers": fallback_providers or [],
        "performance_priority": performance_priority,
        "timestamp": datetime.now().isoformat(),
    }

    if target_sessions:
        # Send to specific sessions
        for target_session_id in target_sessions:
            room = f"session_{target_session_id}"
            socketio.emit(
                "provider_recommendation_change", recommendation_data, room=room
            )
    else:
        # Send to the user's session
        room = f"session_{session_id}"
        socketio.emit("provider_recommendation_change", recommendation_data, room=room)

    logger.info(
        f"Broadcasted provider recommendation change - Session: {session_id}, Preferred: {preferred_provider}"
    )
