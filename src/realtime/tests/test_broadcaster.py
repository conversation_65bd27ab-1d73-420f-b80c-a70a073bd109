"""Tests for WebSocket broadcaster functionality."""

from unittest.mock import MagicMock, patch

import pytest
import redis

from src.realtime.broadcaster import (
    RedisBroadcaster,
    broadcast_job_completed,
    broadcast_job_failed,
    broadcast_job_status,
    broadcast_queue_update,
    broadcast_system_notification,
    cleanup_broadcaster,
    initialize_broadcaster,
)


@pytest.mark.unit
class TestRedisBroadcaster:
    """Test Redis broadcaster functionality."""

    @pytest.fixture
    def mock_redis_client(self):
        """Create mock Redis client."""
        mock_client = MagicMock()
        mock_pubsub = MagicMock()
        mock_client.pubsub.return_value = mock_pubsub
        return mock_client, mock_pubsub

    def test_broadcaster_initialization(self):
        """Test broadcaster initialization."""
        broadcaster = RedisBroadcaster("redis://localhost:6379/1")

        assert broadcaster.redis_url == "redis://localhost:6379/1"
        assert broadcaster.redis_client is None
        assert broadcaster.pubsub is None

    @patch('redis.from_url')
    def test_connect_success(self, mock_redis_from_url):
        """Test successful Redis connection."""
        mock_client = MagicMock()
        mock_pubsub = MagicMock()
        mock_client.pubsub.return_value = mock_pubsub
        mock_redis_from_url.return_value = mock_client

        broadcaster = RedisBroadcaster()
        broadcaster.connect()

        assert broadcaster.redis_client == mock_client
        assert broadcaster.pubsub == mock_pubsub
        mock_client.ping.assert_called_once()

    @patch('redis.from_url')
    def test_connect_failure(self, mock_redis_from_url):
        """Test Redis connection failure."""
        mock_redis_from_url.side_effect = redis.ConnectionError("Connection failed")

        broadcaster = RedisBroadcaster()
        broadcaster.connect()

        assert broadcaster.redis_client is None
        assert broadcaster.pubsub is None

    def test_redis_disconnect(self):
        """Test Redis disconnection."""
        broadcaster = RedisBroadcaster()

        # Mock connected state
        mock_client = MagicMock()
        mock_pubsub = MagicMock()
        broadcaster.redis_client = mock_client
        broadcaster.pubsub = mock_pubsub

        broadcaster.disconnect()

        mock_pubsub.close.assert_called_once()
        mock_client.close.assert_called_once()

    def test_publish_job_status_success(self):
        """Test successful job status publishing."""
        broadcaster = RedisBroadcaster()

        # Mock connected state
        mock_client = MagicMock()
        broadcaster.redis_client = mock_client

        status_data = {"status": "running", "progress": 50}
        broadcaster.publish_job_status("job-123", status_data)

        # Verify publish was called
        mock_client.publish.assert_called_once()
        call_args = mock_client.publish.call_args
        assert call_args[0][0] == "job_status:job-123"  # Channel
        assert "job-123" in call_args[0][1]  # Message contains job_id
        assert "running" in call_args[0][1]  # Message contains status

    def test_publish_job_status_not_connected(self):
        """Test job status publishing when not connected."""
        broadcaster = RedisBroadcaster()
        # redis_client is None (not connected)

        status_data = {"status": "running"}
        broadcaster.publish_job_status("job-123", status_data)

        # Should not raise exception, just log warning

    def test_publish_job_status_redis_error(self):
        """Test job status publishing with Redis error."""
        broadcaster = RedisBroadcaster()

        # Mock connected state with error
        mock_client = MagicMock()
        mock_client.publish.side_effect = redis.RedisError("Publish failed")
        broadcaster.redis_client = mock_client

        status_data = {"status": "running"}
        broadcaster.publish_job_status("job-123", status_data)

        # Should not raise exception, just log error

    def test_subscribe_job_updates_success(self):
        """Test successful job update subscription."""
        broadcaster = RedisBroadcaster()

        # Mock connected state
        mock_pubsub = MagicMock()
        broadcaster.pubsub = mock_pubsub

        broadcaster.subscribe_job_updates()

        mock_pubsub.psubscribe.assert_called_once_with("job_status:*")

    def test_subscribe_job_updates_not_connected(self):
        """Test job update subscription when not connected."""
        broadcaster = RedisBroadcaster()
        # pubsub is None (not connected)

        broadcaster.subscribe_job_updates()

        # Should not raise exception, just log warning


@pytest.mark.unit
class TestBroadcastFunctions:
    """Test global broadcast functions."""

    @patch('src.realtime.broadcaster.get_socketio')
    @patch('src.realtime.broadcaster.EventHandler')
    @patch('src.realtime.broadcaster.redis_broadcaster')
    def test_broadcast_job_status(self, mock_redis_broadcaster, mock_event_handler, mock_get_socketio):
        """Test job status broadcasting."""
        # Mock SocketIO
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        broadcast_job_status(
            job_id="job-123",
            status="running",
            session_id="session-456",
            progress=75,
            message="Processing video"
        )

        # Verify EventHandler was called for both rooms
        assert mock_event_handler.emit_job_status_update.call_count == 2

        # Verify Redis publishing
        mock_redis_broadcaster.publish_job_status.assert_called_once()

    @patch('src.realtime.broadcaster.get_socketio')
    def test_broadcast_job_status_no_socketio(self, mock_get_socketio):
        """Test job status broadcasting when SocketIO not initialized."""
        mock_get_socketio.return_value = None

        broadcast_job_status("job-123", "running")

        # Should not raise exception, just log warning

    @patch('src.realtime.broadcaster.get_socketio')
    @patch('src.realtime.broadcaster.EventHandler')
    def test_broadcast_queue_update(self, mock_event_handler, mock_get_socketio):
        """Test queue position update broadcasting."""
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        broadcast_queue_update(
            job_id="job-123",
            position=3,
            total_jobs=10,
            estimated_wait_minutes=5,
            session_id="session-456"
        )

        # Verify EventHandler was called for both rooms
        assert mock_event_handler.emit_queue_position_update.call_count == 2

    @patch('src.realtime.broadcaster.get_socketio')
    @patch('src.realtime.broadcaster.EventHandler')
    def test_broadcast_job_completed(self, mock_event_handler, mock_get_socketio):
        """Test job completion broadcasting."""
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        broadcast_job_completed(
            job_id="job-123",
            session_id="session-456",
            download_url="https://example.com/video.mp4",
            file_size=1024000,
            duration=10.5,
            processing_time=120.0
        )

        # Verify EventHandler was called for both rooms
        assert mock_event_handler.emit_job_completed.call_count == 2

    @patch('src.realtime.broadcaster.get_socketio')
    @patch('src.realtime.broadcaster.EventHandler')
    def test_broadcast_job_failed(self, mock_event_handler, mock_get_socketio):
        """Test job failure broadcasting."""
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        broadcast_job_failed(
            job_id="job-123",
            error_message="Azure API error",
            session_id="session-456",
            error_code="AZURE_500",
            retry_possible=True
        )

        # Verify EventHandler was called for both rooms
        assert mock_event_handler.emit_job_failed.call_count == 2

    @patch('src.realtime.broadcaster.get_socketio')
    @patch('src.realtime.broadcaster.EventHandler')
    def test_broadcast_system_notification_all_users(self, mock_event_handler, mock_get_socketio):
        """Test system notification to all users."""
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        broadcast_system_notification(
            level="warning",
            title="System Maintenance",
            message="Scheduled maintenance in 10 minutes",
            action_required=False
        )

        # Verify EventHandler was called for broadcast
        mock_event_handler.emit_system_notification.assert_called_once()

    @patch('src.realtime.broadcaster.get_socketio')
    @patch('src.realtime.broadcaster.EventHandler')
    def test_broadcast_system_notification_targeted(self, mock_event_handler, mock_get_socketio):
        """Test system notification to specific sessions."""
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        broadcast_system_notification(
            level="error",
            title="Account Issue",
            message="Your account needs attention",
            action_required=True,
            target_sessions=["session-123", "session-456"]
        )

        # Verify EventHandler was called for each session
        assert mock_event_handler.emit_system_notification.call_count == 2


@pytest.mark.unit
class TestBroadcasterLifecycle:
    """Test broadcaster lifecycle management."""

    @patch('src.realtime.broadcaster.RedisBroadcaster')
    def test_initialize_broadcaster(self, mock_broadcaster_class):
        """Test broadcaster initialization."""
        mock_broadcaster = MagicMock()
        mock_broadcaster_class.return_value = mock_broadcaster

        initialize_broadcaster("redis://localhost:6379/2")

        mock_broadcaster_class.assert_called_once_with("redis://localhost:6379/2")
        mock_broadcaster.connect.assert_called_once()

    @patch('src.realtime.broadcaster.redis_broadcaster')
    def test_cleanup_broadcaster(self, mock_redis_broadcaster):
        """Test broadcaster cleanup."""
        cleanup_broadcaster()

        mock_redis_broadcaster.disconnect.assert_called_once()


@pytest.mark.integration
class TestBroadcasterIntegration:
    """Integration tests for broadcaster with real Redis."""

    @pytest.fixture
    def redis_client(self):
        """Create real Redis client for integration tests."""
        try:
            client = redis.Redis(host='localhost', port=6379, db=15, decode_responses=True)
            client.ping()  # Test connection
            yield client
            # Cleanup after test
            client.flushdb()
        except redis.ConnectionError:
            pytest.skip("Redis not available for integration tests")

    def test_redis_broadcaster_real_connection(self, redis_client):
        """Test broadcaster with real Redis connection."""
        broadcaster = RedisBroadcaster("redis://localhost:6379/15")

        try:
            broadcaster.connect()

            assert broadcaster.redis_client is not None
            assert broadcaster.pubsub is not None

            # Test publishing
            status_data = {"status": "running", "progress": 50}
            broadcaster.publish_job_status("test-job", status_data)

            # Should not raise exception

        finally:
            broadcaster.disconnect()

    def test_redis_pub_sub_functionality(self, redis_client):
        """Test Redis pub/sub functionality."""
        broadcaster = RedisBroadcaster("redis://localhost:6379/15")

        try:
            broadcaster.connect()

            # Subscribe to job updates
            broadcaster.subscribe_job_updates()

            # Publish a message
            status_data = {"status": "completed"}
            broadcaster.publish_job_status("integration-test-job", status_data)

            # In a real scenario, we would check for received messages
            # For this test, we just verify no exceptions are raised

        finally:
            broadcaster.disconnect()

    @patch('src.realtime.broadcaster.get_socketio')
    def test_end_to_end_broadcasting(self, mock_get_socketio, redis_client):
        """Test end-to-end broadcasting flow."""
        # Mock SocketIO
        mock_socketio = MagicMock()
        mock_get_socketio.return_value = mock_socketio

        # Initialize broadcaster with real Redis
        initialize_broadcaster("redis://localhost:6379/15")

        try:
            # Test broadcasting
            broadcast_job_status(
                job_id="integration-job",
                status="running",
                session_id="integration-session",
                progress=25
            )

            # Verify SocketIO was called (mocked)
            # And Redis operations completed without error

        finally:
            cleanup_broadcaster()
