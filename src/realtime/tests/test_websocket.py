"""Tests for WebSocket implementation."""

from unittest.mock import MagicMock, patch

import pytest
from flask import Flask

from src.realtime.websocket import WebSocketConfig, get_socketio, init_socketio


@pytest.mark.unit
class TestWebSocketConfig:
    """Test WebSocket configuration."""

    def test_default_config(self):
        """Test default WebSocket configuration values."""
        config = WebSocketConfig()

        assert config.cors_allowed_origins == "*"
        assert config.async_mode == "threading"
        assert config.logger_enabled is True
        assert config.ping_timeout == 60
        assert config.ping_interval == 25

    def test_custom_config(self):
        """Test custom WebSocket configuration."""
        config = WebSocketConfig(
            cors_allowed_origins="http://localhost:3000",
            async_mode="eventlet",
            logger_enabled=False,
            ping_timeout=30,
            ping_interval=10
        )

        assert config.cors_allowed_origins == "http://localhost:3000"
        assert config.async_mode == "eventlet"
        assert config.logger_enabled is False
        assert config.ping_timeout == 30
        assert config.ping_interval == 10


@pytest.mark.integration
class TestWebSocketInitialization:
    """Test WebSocket initialization."""

    @patch('src.realtime.websocket.SocketIO')
    def test_init_socketio_default_config(self, mock_socketio_class):
        """Test WebSocket initialization with default config."""
        app = Flask(__name__)
        mock_socketio = MagicMock()
        mock_socketio_class.return_value = mock_socketio

        result = init_socketio(app)

        # Verify SocketIO was created with correct parameters
        mock_socketio_class.assert_called_once_with(
            app,
            cors_allowed_origins="*",
            async_mode="threading",
            logger=True,
            engineio_logger=True,
            ping_timeout=60,
            ping_interval=25
        )

        assert result == mock_socketio

    @patch('src.realtime.websocket.SocketIO')
    def test_init_socketio_custom_config(self, mock_socketio_class):
        """Test WebSocket initialization with custom config."""
        app = Flask(__name__)
        mock_socketio = MagicMock()
        mock_socketio_class.return_value = mock_socketio

        config = WebSocketConfig(
            cors_allowed_origins="http://localhost:3000",
            async_mode="eventlet"
        )

        result = init_socketio(app, config)

        mock_socketio_class.assert_called_once_with(
            app,
            cors_allowed_origins="http://localhost:3000",
            async_mode="eventlet",
            logger=True,
            engineio_logger=True,
            ping_timeout=60,
            ping_interval=25
        )

        assert result == mock_socketio

    def test_get_socketio_when_not_initialized(self):
        """Test getting SocketIO instance when not initialized."""
        # Reset global state
        import src.realtime.websocket
        src.realtime.websocket.socketio = None

        result = get_socketio()
        assert result is None

    @patch('src.realtime.websocket.SocketIO')
    def test_get_socketio_when_initialized(self, mock_socketio_class):
        """Test getting SocketIO instance when initialized."""
        app = Flask(__name__)
        mock_socketio = MagicMock()
        mock_socketio_class.return_value = mock_socketio

        init_socketio(app)
        result = get_socketio()

        assert result == mock_socketio


@pytest.mark.integration
class TestWebSocketEventHandlers:
    """Test WebSocket event handlers."""

    @pytest.fixture
    def app(self):
        """Create Flask app for testing."""
        app = Flask(__name__)
        app.config['TESTING'] = True
        return app

    @pytest.fixture
    def socketio_instance(self, app):
        """Create SocketIO instance for testing."""
        return init_socketio(app)

    def test_session_id_extraction(self):
        """Test session ID extraction from request context."""
        from src.realtime.websocket import _get_session_id

        with patch('flask.session', {'session_id': 'test-session-123'}):
            session_id = _get_session_id()
            assert session_id == 'test-session-123'

    def test_session_id_fallback(self):
        """Test session ID fallback to request SID."""
        from src.realtime.websocket import _get_session_id

        with patch('flask.session', {}):
            with patch('flask.request') as mock_request:
                mock_request.sid = 'fallback-sid-456'
                session_id = _get_session_id()
                assert session_id == 'fallback-sid-456'

    def test_job_ownership_validation_placeholder(self):
        """Test job ownership validation (placeholder implementation)."""
        from src.realtime.websocket import _validate_job_ownership

        # Current implementation allows all jobs
        result = _validate_job_ownership('session-123', 'job-456')
        assert result is True

    def test_timestamp_generation(self):
        """Test timestamp generation format."""
        from src.realtime.websocket import _get_timestamp

        timestamp = _get_timestamp()

        # Should be ISO format with Z suffix
        assert timestamp.endswith('Z')
        assert 'T' in timestamp  # ISO format includes T separator


@pytest.mark.integration
@pytest.mark.integration
class TestWebSocketIntegration:
    """Integration tests for WebSocket functionality."""

    @pytest.fixture
    def app(self):
        """Create Flask app with testing config."""
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.config['SECRET_KEY'] = 'test-secret-key'
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()

    @pytest.fixture
    def socketio_instance(self, app):
        """Create SocketIO instance for testing."""
        return init_socketio(app)

    def test_socketio_test_client_creation(self, app, socketio_instance):
        """Test that SocketIO test client can be created."""
        test_client = socketio_instance.test_client(app)
        assert test_client is not None

    @patch('src.realtime.websocket._get_session_id')
    def test_connect_event_handling(self, mock_get_session_id, app, socketio_instance):
        """Test WebSocket connect event handling."""
        mock_get_session_id.return_value = 'test-session-123'

        client = socketio_instance.test_client(app)

        # Should receive connect event
        received = client.get_received()
        assert len(received) == 1

        event = received[0]
        assert event['name'] == 'connected'
        assert event['args'][0]['success'] is True
        assert event['args'][0]['data']['session_id'] == 'test-session-123'

    @patch('src.realtime.websocket._get_session_id')
    @patch('src.realtime.websocket._validate_job_ownership')
    def test_job_subscription_authorized(self, mock_validate, mock_get_session_id, app, socketio_instance):
        """Test job subscription when authorized."""
        mock_get_session_id.return_value = 'test-session-123'
        mock_validate.return_value = True

        client = socketio_instance.test_client(app)
        client.get_received()  # Clear connect message

        # Subscribe to job updates
        client.emit('subscribe_job_updates', {'job_id': 'job-456'})

        received = client.get_received()
        assert len(received) == 1

        event = received[0]
        assert event['name'] == 'subscription_confirmed'
        assert event['args'][0]['success'] is True
        assert event['args'][0]['data']['job_id'] == 'job-456'

    @patch('src.realtime.websocket._get_session_id')
    @patch('src.realtime.websocket._validate_job_ownership')
    def test_job_subscription_unauthorized(self, mock_validate, mock_get_session_id, app, socketio_instance):
        """Test job subscription when unauthorized."""
        mock_get_session_id.return_value = 'test-session-123'
        mock_validate.return_value = False

        client = socketio_instance.test_client(app)
        client.get_received()  # Clear connect message

        # Try to subscribe to unauthorized job
        client.emit('subscribe_job_updates', {'job_id': 'unauthorized-job'})

        received = client.get_received()
        assert len(received) == 1

        event = received[0]
        assert event['name'] == 'error'
        assert 'Unauthorized job access' in event['args'][0]['message']

    def test_job_subscription_missing_job_id(self, app, socketio_instance):
        """Test job subscription with missing job_id."""
        client = socketio_instance.test_client(app)
        client.get_received()  # Clear connect message

        # Try to subscribe without job_id
        client.emit('subscribe_job_updates', {})

        received = client.get_received()
        assert len(received) == 1

        event = received[0]
        assert event['name'] == 'error'
        assert 'Missing job_id parameter' in event['args'][0]['message']

    @patch('src.realtime.websocket._get_session_id')
    def test_connection_info_event(self, mock_get_session_id, app, socketio_instance):
        """Test connection info event."""
        mock_get_session_id.return_value = 'test-session-123'

        client = socketio_instance.test_client(app)
        client.get_received()  # Clear connect message

        # Request connection info
        client.emit('get_connection_info')

        received = client.get_received()
        assert len(received) == 1

        event = received[0]
        assert event['name'] == 'connection_info'
        assert event['args'][0]['success'] is True
        assert event['args'][0]['data']['session_id'] == 'test-session-123'
