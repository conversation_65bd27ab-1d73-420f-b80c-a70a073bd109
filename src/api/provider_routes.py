"""
Provider-specific API routes for C2 Provider Selection UI and C3 job queue integration.

Provides comprehensive endpoints for provider selection, health monitoring, capability
discovery, user preference management, rate limiting, and queue management in the
dual-provider video generation system with C2 Provider Selection UI integration.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

from flask import Blueprint, g, jsonify, request
from pydantic import BaseModel, Field, ValidationError

from src.config.provider_configuration_manager import get_provider_configuration_manager
from src.core.models import APIResponse
from src.features.video_generation.provider_factory import get_provider_factory
from src.features.video_generation.provider_status_service import (
    get_provider_status_service,
)
from src.job_queue.manager import QueueManager
from src.job_queue.provider_integration import (
    get_health_monitor,
    get_load_balancer,
    get_provider_router,
)
from src.rate_limiting.provider_rate_limiter import get_provider_rate_limiter
from src.realtime.broadcaster import (
    broadcast_provider_capability_update,
    broadcast_provider_health_update,
    broadcast_provider_recommendation_change,
    broadcast_provider_status_change,
)

# C2 Provider Selection UI Integration Imports
from src.session.manager import get_session_manager

logger = logging.getLogger(__name__)

# Create blueprint for provider routes
provider_bp = Blueprint("provider", __name__)

# Module-global instances for performance optimization
provider_factory = get_provider_factory()
queue_manager = QueueManager()
provider_router = get_provider_router(queue_manager)
health_monitor = get_health_monitor()
load_balancer = get_load_balancer(queue_manager)


# C2 Provider Selection UI - Pydantic models for request/response validation
class ProviderPreferenceRequest(BaseModel):
    """Request model for setting provider preferences."""

    preferred_provider: Optional[str] = Field(None, description="Preferred provider ID")
    fallback_providers: List[str] = Field(
        default_factory=list, description="Ordered fallback providers"
    )
    performance_priority: str = Field(
        default="balanced", description="Performance priority: speed, quality, balanced"
    )
    auto_failover: bool = Field(default=True, description="Enable automatic failover")


class ProviderDisableRequest(BaseModel):
    """Request model for disabling/enabling providers."""

    provider_id: str = Field(..., description="Provider ID to disable/enable")
    disabled: bool = Field(..., description="True to disable, False to enable")
    reason: Optional[str] = Field(None, description="Reason for disabling")


class ProviderThrottleRequest(BaseModel):
    """Request model for manual provider throttling."""

    provider_id: str = Field(..., description="Provider ID to throttle")
    throttle: bool = Field(..., description="True to throttle, False to unthrottle")
    reason: str = Field(..., description="Reason for throttling")


# C2 Utility functions
def _get_session_id() -> str:
    """Get session ID from request context."""
    session_id = getattr(g, "session_id", None)
    if not session_id:
        raise ValueError("Session ID not found in request context")
    return session_id


def _handle_async_request(coro):
    """Handle async function calls within Flask routes."""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is already running, create a new task
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        # No event loop, create one
        return asyncio.run(coro)


@provider_bp.route("/providers", methods=["GET"])
def get_available_providers():
    """
    Get list of available providers with basic information.

    Returns:
        JSON response with available providers and their status
    """
    try:
        available_providers = provider_factory.get_available_providers()

        provider_info = {}
        for provider_name in available_providers:
            try:
                provider = provider_factory.create_provider(provider_name)
                capabilities = provider.supported_features

                provider_info[provider_name] = {
                    "name": provider_name,
                    "available": True,
                    "capabilities": capabilities,
                    "description": _get_provider_description(provider_name),
                    "last_checked": datetime.now().isoformat(),
                }
            except Exception as e:
                logger.error(f"Failed to get info for provider {provider_name}: {e}")
                provider_info[provider_name] = {
                    "name": provider_name,
                    "available": False,
                    "error": str(e),
                    "last_checked": datetime.now().isoformat(),
                }

        return jsonify(
            {
                "success": True,
                "data": {
                    "providers": provider_info,
                    "total_count": len(provider_info),
                    "available_count": sum(
                        1 for p in provider_info.values() if p.get("available", False)
                    ),
                },
            }
        ), 200

    except Exception as e:
        logger.error(f"Failed to get available providers: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to retrieve providers: {str(e)}"}
        ), 500


@provider_bp.route("/providers/<provider_name>/health", methods=["GET"])
async def get_provider_health(provider_name: str):
    """
    Get detailed health status for a specific provider.

    Args:
        provider_name: Name of the provider to check

    Returns:
        JSON response with provider health details
    """
    try:
        # Check if provider exists
        available_providers = provider_factory.get_available_providers()
        if provider_name not in available_providers:
            return jsonify(
                {"success": False, "error": f"Provider '{provider_name}' not found"}
            ), 404

        # Get health status
        health_status = await health_monitor._check_single_provider_health(
            provider_name
        )

        # Get queue status for additional health metrics
        queue_status = queue_manager.get_queue_status_by_provider(
            "system", provider_name
        )

        # Combine health information
        comprehensive_health = {
            **health_status,
            "queue_metrics": {
                "pending_jobs": queue_status.get("total_pending_jobs", 0),
                "running_jobs": queue_status.get("total_running_jobs", 0),
                "active_workers": queue_status.get("active_workers", 0),
                "provider_available": queue_status.get("provider_available", False),
            },
        }

        return jsonify({"success": True, "data": comprehensive_health}), 200

    except Exception as e:
        logger.error(f"Failed to get health for provider {provider_name}: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to get provider health: {str(e)}"}
        ), 500


@provider_bp.route("/providers/health", methods=["GET"])
async def get_all_providers_health():
    """
    Get health status for all available providers.

    Returns:
        JSON response with health status for all providers
    """
    try:
        health_results = await health_monitor.check_all_providers_health()

        # Add queue metrics for each provider
        for provider_name, health_data in health_results.items():
            queue_status = queue_manager.get_queue_status_by_provider(
                "system", provider_name
            )
            health_data["queue_metrics"] = {
                "pending_jobs": queue_status.get("total_pending_jobs", 0),
                "running_jobs": queue_status.get("total_running_jobs", 0),
                "active_workers": queue_status.get("active_workers", 0),
                "provider_available": queue_status.get("provider_available", False),
            }

        # Calculate overall system health
        healthy_count = sum(
            1 for h in health_results.values() if h.get("status") == "healthy"
        )
        total_count = len(health_results)

        overall_health = {
            "system_status": "healthy" if healthy_count == total_count else "degraded",
            "healthy_providers": healthy_count,
            "total_providers": total_count,
            "checked_at": datetime.now().isoformat(),
        }

        return jsonify(
            {
                "success": True,
                "data": {"overall": overall_health, "providers": health_results},
            }
        ), 200

    except Exception as e:
        logger.error(f"Failed to get all providers health: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to get providers health: {str(e)}"}
        ), 500


@provider_bp.route("/providers/<provider_name>/capabilities", methods=["GET"])
def get_provider_capabilities(provider_name: str):
    """
    Get detailed capabilities for a specific provider.

    Args:
        provider_name: Name of the provider

    Returns:
        JSON response with provider capabilities
    """
    try:
        # Check if provider exists
        available_providers = provider_factory.get_available_providers()
        if provider_name not in available_providers:
            return jsonify(
                {"success": False, "error": f"Provider '{provider_name}' not found"}
            ), 404

        # Get provider capabilities
        provider = provider_factory.create_provider(provider_name)
        capabilities = provider.supported_features

        # Add additional capability metadata
        enhanced_capabilities = {
            "provider": provider_name,
            "features": capabilities,
            "metadata": _get_provider_metadata(provider_name),
            "performance_characteristics": _get_provider_performance_info(
                provider_name
            ),
            "last_updated": datetime.now().isoformat(),
        }

        return jsonify({"success": True, "data": enhanced_capabilities}), 200

    except Exception as e:
        logger.error(f"Failed to get capabilities for provider {provider_name}: {e}")
        return jsonify(
            {
                "success": False,
                "error": f"Failed to get provider capabilities: {str(e)}",
            }
        ), 500


@provider_bp.route("/providers/queue-status", methods=["GET"])
def get_providers_queue_status():
    """
    Get queue status for all providers.

    Returns:
        JSON response with queue status for all providers
    """
    try:
        session_id = request.args.get("session_id", "system")

        # Get provider-specific queue statistics
        provider_stats = queue_manager.get_provider_queue_statistics()

        # Get load balancing metrics
        load_metrics = load_balancer.get_provider_load_metrics()

        # Combine data
        combined_data = {
            "session_id": session_id,
            "queue_statistics": provider_stats,
            "load_balancing": load_metrics,
            "timestamp": datetime.now().isoformat(),
        }

        return jsonify({"success": True, "data": combined_data}), 200

    except Exception as e:
        logger.error(f"Failed to get providers queue status: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to get queue status: {str(e)}"}
        ), 500


@provider_bp.route("/providers/optimal", methods=["POST"])
def get_optimal_provider():
    """
    Get optimal provider recommendation for a job.

    Request body should contain:
        - session_id: User session ID
        - generation_params: Video generation parameters

    Returns:
        JSON response with optimal provider recommendation
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Request body is required"}), 400

        session_id = data.get("session_id")
        generation_params = data.get("generation_params", {})

        if not session_id:
            return jsonify({"success": False, "error": "session_id is required"}), 400

        # Get optimal provider recommendation
        optimal_provider = provider_router.select_optimal_provider(
            session_id, generation_params
        )

        if not optimal_provider:
            return jsonify(
                {
                    "success": False,
                    "error": "No providers available for the requested parameters",
                }
            ), 503

        # Get routing recommendations for all providers
        routing_recommendations = provider_router.get_provider_routing_recommendations(
            session_id
        )

        # Validate the selected provider
        is_valid, validation_message = provider_router.validate_provider_for_job(
            optimal_provider, generation_params
        )

        response_data = {
            "optimal_provider": optimal_provider,
            "validation": {"is_valid": is_valid, "message": validation_message},
            "all_recommendations": routing_recommendations,
            "selection_timestamp": datetime.now().isoformat(),
        }

        return jsonify({"success": True, "data": response_data}), 200

    except Exception as e:
        logger.error(f"Failed to get optimal provider: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to get optimal provider: {str(e)}"}
        ), 500


@provider_bp.route("/providers/<provider_name>/validate", methods=["POST"])
def validate_provider_for_job(provider_name: str):
    """
    Validate if a provider can handle specific job requirements.

    Args:
        provider_name: Name of the provider to validate

    Request body should contain:
        - generation_params: Video generation parameters

    Returns:
        JSON response with validation result
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify(
                {
                    "success": False,
                    "error": "Request body with generation_params is required",
                }
            ), 400

        generation_params = data.get("generation_params", {})

        # Validate provider for job
        is_valid, validation_message = provider_router.validate_provider_for_job(
            provider_name, generation_params
        )

        # Get additional provider information
        available_providers = provider_factory.get_available_providers()
        provider_available = provider_name in available_providers

        validation_data = {
            "provider": provider_name,
            "is_valid": is_valid,
            "message": validation_message,
            "provider_available": provider_available,
            "validation_timestamp": datetime.now().isoformat(),
        }

        return jsonify({"success": True, "data": validation_data}), 200

    except Exception as e:
        logger.error(f"Failed to validate provider {provider_name}: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to validate provider: {str(e)}"}
        ), 500


@provider_bp.route("/providers/load-balance", methods=["POST"])
def get_load_balanced_distribution():
    """
    Get load-balanced provider distribution for multiple jobs.

    Request body should contain:
        - session_id: User session ID
        - job_count: Number of jobs to distribute (default: 1)

    Returns:
        JSON response with load-balanced provider distribution
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Request body is required"}), 400

        session_id = data.get("session_id")
        job_count = data.get("job_count", 1)

        if not session_id:
            return jsonify({"success": False, "error": "session_id is required"}), 400

        if job_count < 1 or job_count > 10:  # Reasonable limits
            return jsonify(
                {"success": False, "error": "job_count must be between 1 and 10"}
            ), 400

        # Get load-balanced distribution
        distribution = load_balancer.balance_load_across_providers(
            session_id, job_count
        )

        if not distribution:
            return jsonify(
                {"success": False, "error": "No providers available for load balancing"}
            ), 503

        # Get current load metrics
        load_metrics = load_balancer.get_provider_load_metrics()

        distribution_data = {
            "session_id": session_id,
            "job_count": job_count,
            "distribution": distribution,
            "load_metrics": load_metrics,
            "distribution_timestamp": datetime.now().isoformat(),
        }

        return jsonify({"success": True, "data": distribution_data}), 200

    except Exception as e:
        logger.error(f"Failed to get load balanced distribution: {e}")
        return jsonify(
            {
                "success": False,
                "error": f"Failed to get load balanced distribution: {str(e)}",
            }
        ), 500


# Helper functions


def _get_provider_description(provider_name: str) -> str:
    """Get human-readable description for provider."""
    descriptions = {
        "azure_sora": "Microsoft Azure Sora - High-quality video generation with enterprise features",
        "google_veo3": "Google Veo3 - Fast video generation with image-to-video capabilities",
    }
    return descriptions.get(
        provider_name, f"Video generation provider: {provider_name}"
    )


def _get_provider_metadata(provider_name: str) -> Dict:
    """Get additional metadata for provider."""
    metadata = {
        "azure_sora": {
            "vendor": "Microsoft",
            "model_type": "Sora",
            "specialties": ["high_quality", "enterprise", "scalable"],
            "typical_processing_time": "5-10 minutes",
            "max_concurrent_jobs": 3,
        },
        "google_veo3": {
            "vendor": "Google",
            "model_type": "Veo3",
            "specialties": ["fast_processing", "image_to_video", "mock_development"],
            "typical_processing_time": "2-5 minutes",
            "max_concurrent_jobs": 5,
        },
    }
    return metadata.get(
        provider_name,
        {
            "vendor": "Unknown",
            "model_type": provider_name,
            "specialties": [],
            "typical_processing_time": "Unknown",
            "max_concurrent_jobs": 2,
        },
    )


def _get_provider_performance_info(provider_name: str) -> Dict:
    """Get performance characteristics for provider."""
    performance = {
        "azure_sora": {
            "average_processing_time_seconds": 480,  # 8 minutes
            "success_rate_percentage": 95,
            "uptime_percentage": 99.5,
            "quality_score": 9.5,
        },
        "google_veo3": {
            "average_processing_time_seconds": 300,  # 5 minutes
            "success_rate_percentage": 98,
            "uptime_percentage": 99.9,
            "quality_score": 8.5,
        },
    }
    return performance.get(
        provider_name,
        {
            "average_processing_time_seconds": 360,  # 6 minutes
            "success_rate_percentage": 90,
            "uptime_percentage": 99.0,
            "quality_score": 8.0,
        },
    )


# C2 Provider Selection UI - Enhanced Status and Recommendation Routes


@provider_bp.route("/c2/status", methods=["GET"])
def get_c2_provider_status() -> dict:
    """
    Get comprehensive C2 provider status with real-time health, capabilities, and rate limiting.

    Integrates provider status service, configuration manager, and rate limiter for
    complete C2 Provider Selection UI data.
    """
    try:

        async def _get_c2_status():
            status_service = get_provider_status_service()
            config_manager = get_provider_configuration_manager()
            rate_limiter = get_provider_rate_limiter()

            # Get provider statuses
            provider_statuses = await status_service.get_all_provider_statuses()

            # Get environment configuration
            env_summary = config_manager.get_environment_configuration_summary()

            # Get rate limiting status
            rate_status = {}
            if rate_limiter:
                rate_status = rate_limiter.get_all_providers_rate_status()

            return {
                "provider_statuses": {
                    pid: {
                        "provider_id": status.provider_id,
                        "status": status.status.value,
                        "last_health_check": status.last_health_check.isoformat(),
                        "response_time_ms": status.response_time_ms,
                        "success_rate": status.success_rate,
                        "error_count_24h": status.error_count_24h,
                        "uptime_percentage": status.uptime_percentage,
                        "capabilities": status.capabilities,
                        "concurrent_jobs": status.concurrent_jobs,
                        "max_concurrent_jobs": status.max_concurrent_jobs,
                        "queue_length": status.queue_length,
                        "is_configured": status.is_configured,
                        "configuration_errors": status.configuration_errors,
                    }
                    for pid, status in provider_statuses.items()
                },
                "environment": env_summary["environment"],
                "available_providers": env_summary["providers"]["available_providers"],
                "default_provider": env_summary["providers"]["default_provider"],
                "rate_limiting": rate_status,
                "f4_system_info": env_summary["f4_system"],
            }

        result = _handle_async_request(_get_c2_status())

        response = APIResponse(
            success=True,
            message="C2 provider status retrieved successfully",
            data=result,
        )
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Error getting C2 provider status: {e}")
        response = APIResponse(
            success=False, message="Failed to retrieve C2 provider status", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@provider_bp.route("/c2/recommend", methods=["GET"])
def get_c2_provider_recommendation() -> dict:
    """Get C2 provider recommendation with session preferences and F4 environment integration."""
    try:
        session_id = _get_session_id()
        session_manager = get_session_manager()

        # Get session provider preferences
        preferences = session_manager.get_provider_preferences(session_id)

        # Get recommended provider
        recommended = session_manager.get_recommended_provider(session_id)

        async def _get_optimal_config():
            config_manager = get_provider_configuration_manager()
            return await config_manager.get_optimal_provider_config(preferences)

        optimal_config = _handle_async_request(_get_optimal_config())

        response = APIResponse(
            success=True,
            message="C2 provider recommendation retrieved successfully",
            data={
                "recommended_provider": recommended,
                "user_preferences": preferences,
                "optimal_configuration": optimal_config,
                "session_id": session_id,
            },
        )
        return jsonify(response.model_dump())

    except ValueError as e:
        response = APIResponse(
            success=False,
            message="Session required for C2 provider recommendation",
            error=str(e),
        )
        return jsonify(response.model_dump()), 400
    except Exception as e:
        logger.error(f"Error getting C2 provider recommendation: {e}")
        response = APIResponse(
            success=False,
            message="Failed to retrieve C2 provider recommendation",
            error=str(e),
        )
        return jsonify(response.model_dump()), 500


@provider_bp.route("/c2/preferences", methods=["GET"])
def get_c2_provider_preferences() -> dict:
    """Get C2 provider preferences for current session."""
    try:
        session_id = _get_session_id()
        session_manager = get_session_manager()

        preferences = session_manager.get_provider_preferences(session_id)
        usage_stats = session_manager.get_provider_usage_stats(session_id)

        if not preferences:
            response = APIResponse(
                success=False,
                message="Session preferences not found",
                error="Session not found or has no preferences",
            )
            return jsonify(response.model_dump()), 404

        response = APIResponse(
            success=True,
            message="C2 provider preferences retrieved successfully",
            data={
                "preferences": preferences,
                "usage_statistics": usage_stats,
                "session_id": session_id,
            },
        )
        return jsonify(response.model_dump())

    except ValueError as e:
        response = APIResponse(
            success=False, message="Session required for C2 preferences", error=str(e)
        )
        return jsonify(response.model_dump()), 400
    except Exception as e:
        logger.error(f"Error getting C2 provider preferences: {e}")
        response = APIResponse(
            success=False,
            message="Failed to retrieve C2 provider preferences",
            error=str(e),
        )
        return jsonify(response.model_dump()), 500


@provider_bp.route("/c2/preferences", methods=["POST"])
def set_c2_provider_preferences() -> dict:
    """Set C2 provider preferences for current session with WebSocket broadcasting."""
    try:
        session_id = _get_session_id()
        session_manager = get_session_manager()

        # Validate request data
        request_data = request.get_json()
        if not request_data:
            raise ValueError("Request body is required")

        pref_request = ProviderPreferenceRequest(**request_data)

        # Set preferred provider
        if pref_request.preferred_provider:
            success = session_manager.set_preferred_provider(
                session_id, pref_request.preferred_provider, validate_provider=True
            )
            if not success:
                raise ValueError(
                    f"Failed to set preferred provider: {pref_request.preferred_provider}"
                )

        # Set fallback providers
        if pref_request.fallback_providers:
            success = session_manager.set_fallback_providers(
                session_id, pref_request.fallback_providers, validate_providers=True
            )
            if not success:
                raise ValueError("Failed to set fallback providers")

        # Set performance priority
        success = session_manager.set_performance_priority(
            session_id, pref_request.performance_priority
        )
        if not success:
            raise ValueError(
                f"Failed to set performance priority: {pref_request.performance_priority}"
            )

        # Get updated preferences
        updated_preferences = session_manager.get_provider_preferences(session_id)

        # Broadcast preference change via WebSocket
        broadcast_provider_recommendation_change(
            session_id=session_id,
            preferred_provider=pref_request.preferred_provider,
            fallback_providers=pref_request.fallback_providers,
            performance_priority=pref_request.performance_priority,
        )

        response = APIResponse(
            success=True,
            message="C2 provider preferences updated successfully",
            data={"updated_preferences": updated_preferences, "session_id": session_id},
        )
        return jsonify(response.model_dump())

    except ValidationError as e:
        response = APIResponse(
            success=False, message="Invalid C2 preference data", error=str(e)
        )
        return jsonify(response.model_dump()), 400
    except ValueError as e:
        response = APIResponse(
            success=False, message="Failed to update C2 preferences", error=str(e)
        )
        return jsonify(response.model_dump()), 400
    except Exception as e:
        logger.error(f"Error setting C2 provider preferences: {e}")
        response = APIResponse(
            success=False,
            message="Failed to update C2 provider preferences",
            error=str(e),
        )
        return jsonify(response.model_dump()), 500


@provider_bp.route("/c2/throttle", methods=["POST"])
def c2_manual_throttle_provider() -> dict:
    """Manually throttle or unthrottle a provider with C2 integration."""
    try:
        rate_limiter = get_provider_rate_limiter()
        if not rate_limiter:
            raise ValueError("Provider rate limiter not available")

        # Validate request data
        request_data = request.get_json()
        if not request_data:
            raise ValueError("Request body is required")

        throttle_request = ProviderThrottleRequest(**request_data)

        # Apply throttling
        success = rate_limiter.manually_throttle_provider(
            throttle_request.provider_id,
            throttle_request.throttle,
            throttle_request.reason,
        )

        if not success:
            raise ValueError(
                f"Failed to throttle provider {throttle_request.provider_id}"
            )

        # Get updated rate status
        rate_status = rate_limiter.get_provider_rate_status(
            throttle_request.provider_id
        )

        # Broadcast provider status change via WebSocket
        action = "throttled" if throttle_request.throttle else "unthrottled"
        broadcast_provider_status_change(
            provider_id=throttle_request.provider_id,
            status="throttled" if throttle_request.throttle else "available",
            previous_status="available" if throttle_request.throttle else "throttled",
            response_time_ms=None,
            success_rate=None,
            error_count_24h=None,
            is_configured=True,
            configuration_errors=[],
        )

        response = APIResponse(
            success=True,
            message=f"Provider {throttle_request.provider_id} {action} successfully",
            data={
                "provider_id": throttle_request.provider_id,
                "action": action,
                "reason": throttle_request.reason,
                "rate_status": rate_status,
            },
        )
        return jsonify(response.model_dump())

    except ValidationError as e:
        response = APIResponse(
            success=False, message="Invalid C2 throttle request data", error=str(e)
        )
        return jsonify(response.model_dump()), 400
    except ValueError as e:
        response = APIResponse(
            success=False, message="Failed to throttle provider with C2", error=str(e)
        )
        return jsonify(response.model_dump()), 400
    except Exception as e:
        logger.error(f"Error throttling provider with C2: {e}")
        response = APIResponse(
            success=False, message="Failed to throttle provider with C2", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@provider_bp.route("/c2/environment", methods=["GET"])
def get_c2_environment_configuration() -> dict:
    """Get comprehensive C2 environment configuration with F4 system integration."""
    try:
        config_manager = get_provider_configuration_manager()
        environment_summary = config_manager.get_environment_configuration_summary()

        response = APIResponse(
            success=True,
            message="C2 environment configuration retrieved successfully",
            data=environment_summary,
        )
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Error getting C2 environment configuration: {e}")
        response = APIResponse(
            success=False,
            message="Failed to retrieve C2 environment configuration",
            error=str(e),
        )
        return jsonify(response.model_dump()), 500


@provider_bp.route("/c2/refresh", methods=["POST"])
def refresh_c2_provider_status() -> dict:
    """Force refresh of C2 provider status and availability with full integration."""
    try:

        async def _refresh_c2_all():
            # Refresh provider status service
            status_service = get_provider_status_service()

            # Get available providers and refresh each
            available_providers = await status_service.get_available_providers()

            refresh_results = {}
            for provider_id in available_providers:
                try:
                    updated_metrics = await status_service.force_provider_status_update(
                        provider_id
                    )
                    refresh_results[provider_id] = {
                        "success": True,
                        "status": updated_metrics.status.value,
                        "last_health_check": updated_metrics.last_health_check.isoformat(),
                        "response_time_ms": updated_metrics.response_time_ms,
                        "success_rate": updated_metrics.success_rate,
                    }
                except Exception as e:
                    refresh_results[provider_id] = {"success": False, "error": str(e)}

            # Refresh provider configuration manager
            config_manager = get_provider_configuration_manager()
            availability_matrix = await config_manager.refresh_provider_availability()

            return {
                "provider_status_refresh": refresh_results,
                "availability_matrix": availability_matrix.model_dump(),
                "available_providers": availability_matrix.available_providers,
                "default_provider": availability_matrix.default_provider,
            }

        result = _handle_async_request(_refresh_c2_all())

        response = APIResponse(
            success=True,
            message="C2 provider status refreshed successfully",
            data=result,
        )
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Error refreshing C2 provider status: {e}")
        response = APIResponse(
            success=False, message="Failed to refresh C2 provider status", error=str(e)
        )
        return jsonify(response.model_dump()), 500


# C2 Error handlers
@provider_bp.errorhandler(ValidationError)
def handle_c2_validation_error(error):
    """Handle C2 Pydantic validation errors."""
    response = APIResponse(
        success=False, message="C2 request validation failed", error=str(error)
    )
    return jsonify(response.model_dump()), 400


# Webhook endpoints for provider status updates


@provider_bp.route("/providers/<provider_name>/webhook/health", methods=["POST"])
def provider_health_webhook(provider_name: str):
    """
    Webhook endpoint for external provider health updates.

    Args:
        provider_name: Name of the provider

    Request body should contain health status data
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Request body is required"}), 400

        health_status = data.get("health_status", "unknown")
        health_details = data.get("health_details", {})

        # Broadcast health update
        broadcast_provider_health_update(
            provider=provider_name,
            health_status=health_status,
            health_details=health_details,
        )

        logger.info(
            f"Received health webhook for provider {provider_name}: {health_status}"
        )

        return jsonify(
            {
                "success": True,
                "message": f"Health update received for provider {provider_name}",
            }
        ), 200

    except Exception as e:
        logger.error(f"Failed to process health webhook for {provider_name}: {e}")
        return jsonify(
            {"success": False, "error": f"Failed to process health webhook: {str(e)}"}
        ), 500


@provider_bp.route("/providers/<provider_name>/webhook/capabilities", methods=["POST"])
def provider_capabilities_webhook(provider_name: str):
    """
    Webhook endpoint for external provider capability updates.

    Args:
        provider_name: Name of the provider

    Request body should contain capability data
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Request body is required"}), 400

        capabilities = data.get("capabilities", {})
        available = data.get("available", True)

        # Broadcast capability update
        broadcast_provider_capability_update(
            provider=provider_name, capabilities=capabilities, available=available
        )

        logger.info(f"Received capabilities webhook for provider {provider_name}")

        return jsonify(
            {
                "success": True,
                "message": f"Capabilities update received for provider {provider_name}",
            }
        ), 200

    except Exception as e:
        logger.error(f"Failed to process capabilities webhook for {provider_name}: {e}")
        return jsonify(
            {
                "success": False,
                "error": f"Failed to process capabilities webhook: {str(e)}",
            }
        ), 500
