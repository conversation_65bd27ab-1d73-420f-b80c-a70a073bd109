"""Database repository for video job operations."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional

from sqlalchemy.exc import SQLAlchemyError

from src.core.interfaces import JobRepositoryInterface
from src.core.models import VideoJob
from src.database.connection import get_db_session
from src.database.models import VideoJobDB


class JobRepository(JobRepositoryInterface):
    """
    Database repository for video job CRUD operations.

    Provides optimized database operations with proper error handling
    and transaction management for video generation workflows.
    """

    def __init__(self) -> None:
        """Initialize repository with logging."""
        self.logger = logging.getLogger(__name__)

    def create_job(self, job: VideoJob) -> Optional[VideoJob]:
        """
        Create new video job in database.

        Args:
            job (VideoJob): Video job to create

        Returns:
            Optional[VideoJob]: Created job or None if failed
        """
        try:
            with get_db_session() as session:
                job_db = VideoJobDB.from_pydantic(job)
                session.add(job_db)
                session.commit()

                self.logger.info(f"Created job in database: {job.id}")
                return job_db.to_pydantic()

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to create job {job.id}: {e}")
            return None

    def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
        """
        Retrieve video job by ID.

        Args:
            job_id (str): Unique job identifier

        Returns:
            Optional[VideoJob]: Job if found, None otherwise
        """
        try:
            with get_db_session() as session:
                job_db = session.query(VideoJobDB).filter_by(id=job_id).first()

                if job_db:
                    return job_db.to_pydantic()
                return None

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to retrieve job {job_id}: {e}")
            return None

    def update_job(self, job: VideoJob) -> Optional[VideoJob]:
        """
        Update existing video job.

        Args:
            job (VideoJob): Updated job data

        Returns:
            Optional[VideoJob]: Updated job or None if failed
        """
        try:
            with get_db_session() as session:
                job_db = session.query(VideoJobDB).filter_by(id=job.id).first()

                if job_db:
                    job_db.update_from_pydantic(job)
                    session.commit()

                    self.logger.info(f"Updated job in database: {job.id}")
                    return job_db.to_pydantic()
                else:
                    self.logger.warning(f"Job not found for update: {job.id}")
                    return None

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to update job {job.id}: {e}")
            return None

    def update_job_by_id(self, job_id: str, update_data: dict) -> Optional[VideoJob]:
        """
        Update job by ID with partial data (for Celery tasks).

        Args:
            job_id (str): Job identifier
            update_data (dict): Dictionary of fields to update

        Returns:
            Optional[VideoJob]: Updated job or None if failed
        """
        try:
            with get_db_session() as session:
                job_db = session.query(VideoJobDB).filter_by(id=job_id).first()

                if job_db:
                    # Update only specified fields
                    for field, value in update_data.items():
                        if hasattr(job_db, field):
                            setattr(job_db, field, value)

                    # Always update the updated_at timestamp
                    job_db.updated_at = datetime.utcnow()
                    session.commit()

                    self.logger.info(f"Updated job {job_id} with data: {update_data}")
                    return job_db.to_pydantic()
                else:
                    self.logger.warning(f"Job not found for update: {job_id}")
                    return None

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to update job {job_id}: {e}")
            return None

    def delete_job(self, job_id: str) -> bool:
        """
        Delete video job from database.

        Args:
            job_id (str): Unique job identifier

        Returns:
            bool: True if deleted successfully
        """
        try:
            with get_db_session() as session:
                job_db = session.query(VideoJobDB).filter_by(id=job_id).first()

                if job_db:
                    session.delete(job_db)
                    session.commit()

                    self.logger.info(f"Deleted job from database: {job_id}")
                    return True
                else:
                    self.logger.warning(f"Job not found for deletion: {job_id}")
                    return False

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to delete job {job_id}: {e}")
            return False

    def get_jobs_by_status(self, status: str, limit: int = 100) -> List[VideoJob]:
        """
        Retrieve jobs by status with limit.

        Args:
            status (str): Job status to filter by
            limit (int): Maximum number of jobs to return

        Returns:
            List[VideoJob]: List of jobs with specified status
        """
        try:
            with get_db_session() as session:
                jobs_db = (
                    session.query(VideoJobDB)
                    .filter_by(status=status)
                    .order_by(VideoJobDB.created_at.desc())
                    .limit(limit)
                    .all()
                )

                return [job_db.to_pydantic() for job_db in jobs_db]

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to retrieve jobs by status {status}: {e}")
            return []

    def get_recent_jobs(self, limit: int = 50) -> List[VideoJob]:
        """
        Retrieve most recent jobs.

        Args:
            limit (int): Maximum number of jobs to return

        Returns:
            List[VideoJob]: List of recent jobs
        """
        try:
            with get_db_session() as session:
                jobs_db = (
                    session.query(VideoJobDB)
                    .order_by(VideoJobDB.created_at.desc())
                    .limit(limit)
                    .all()
                )

                return [job_db.to_pydantic() for job_db in jobs_db]

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to retrieve recent jobs: {e}")
            return []

    def get_pending_jobs(self) -> List[VideoJob]:
        """
        Retrieve all pending jobs for processing.

        Returns:
            List[VideoJob]: List of pending jobs
        """
        return self.get_jobs_by_status("pending")

    def get_running_jobs(self) -> List[VideoJob]:
        """
        Retrieve all running jobs for monitoring.

        Returns:
            List[VideoJob]: List of running jobs
        """
        return self.get_jobs_by_status("running")

    def cleanup_old_jobs(self, days_old: int = 7) -> int:
        """
        Clean up old completed jobs.

        Args:
            days_old (int): Remove jobs older than this many days

        Returns:
            int: Number of jobs deleted
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            with get_db_session() as session:
                # Delete old completed or failed jobs
                deleted_count = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.created_at < cutoff_date)
                    .filter(VideoJobDB.status.in_(["succeeded", "failed"]))
                    .delete(synchronize_session=False)
                )

                session.commit()

                self.logger.info(f"Cleaned up {deleted_count} old jobs")
                return deleted_count

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to cleanup old jobs: {e}")
            return 0

    def get_job_stats(self) -> dict:
        """
        Get job statistics for monitoring.

        Returns:
            dict: Job statistics by status
        """
        try:
            with get_db_session() as session:
                # Count jobs by status
                stats = {}
                for status in ["pending", "running", "succeeded", "failed"]:
                    count = session.query(VideoJobDB).filter_by(status=status).count()
                    stats[status] = count

                # Total jobs
                stats["total"] = session.query(VideoJobDB).count()

                return stats

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to get job stats: {e}")
            return {}

    def bulk_update_status(self, job_ids: List[str], new_status: str) -> int:
        """
        Bulk update job status for multiple jobs.

        Args:
            job_ids (List[str]): List of job IDs to update
            new_status (str): New status to set

        Returns:
            int: Number of jobs updated
        """
        try:
            with get_db_session() as session:
                updated_count = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.id.in_(job_ids))
                    .update(
                        {"status": new_status, "completed_at": datetime.now()},
                        synchronize_session=False,
                    )
                )

                session.commit()

                self.logger.info(
                    f"Bulk updated {updated_count} jobs to status {new_status}"
                )
                return updated_count

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to bulk update jobs: {e}")
            return 0

    def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
        """
        Get all jobs for a specific session.

        Args:
            session_id (str): Session identifier

        Returns:
            List[VideoJob]: List of jobs for the session
        """
        try:
            with get_db_session() as session:
                job_dbs = (
                    session.query(VideoJobDB)
                    .filter_by(session_id=session_id)
                    .order_by(VideoJobDB.created_at.desc())
                    .all()
                )

                jobs = [job_db.to_pydantic() for job_db in job_dbs]

                self.logger.info(f"Retrieved {len(jobs)} jobs for session {session_id}")
                return jobs

        except SQLAlchemyError as e:
            self.logger.error(f"Failed to get jobs for session {session_id}: {e}")
            return []

    def get_jobs_by_session_and_status(
        self, session_id: str, status: str
    ) -> List[VideoJob]:
        """
        Get jobs for a session filtered by status.

        Args:
            session_id (str): Session identifier
            status (str): Job status to filter by

        Returns:
            List[VideoJob]: List of jobs matching criteria
        """
        try:
            with get_db_session() as session:
                job_dbs = (
                    session.query(VideoJobDB)
                    .filter_by(session_id=session_id, status=status)
                    .order_by(VideoJobDB.created_at.desc())
                    .all()
                )

                jobs = [job_db.to_pydantic() for job_db in job_dbs]

                self.logger.info(
                    f"Retrieved {len(jobs)} jobs for session {session_id} with status {status}"
                )
                return jobs

        except SQLAlchemyError as e:
            self.logger.error(
                f"Failed to get jobs for session {session_id} with status {status}: {e}"
            )
            return []

    def get_active_jobs_by_session(self, session_id: str) -> List[VideoJob]:
        """
        Get active jobs (pending or running) for a specific session.

        Args:
            session_id (str): Session identifier

        Returns:
            List[VideoJob]: List of active jobs for the session
        """
        try:
            with get_db_session() as session:
                job_dbs = (
                    session.query(VideoJobDB)
                    .filter_by(session_id=session_id)
                    .filter(VideoJobDB.status.in_(["pending", "running"]))
                    .order_by(VideoJobDB.created_at.desc())
                    .all()
                )

                jobs = [job_db.to_pydantic() for job_db in job_dbs]

                self.logger.info(
                    f"Retrieved {len(jobs)} active jobs for session {session_id}"
                )
                return jobs

        except SQLAlchemyError as e:
            self.logger.error(
                f"Failed to get active jobs for session {session_id}: {e}"
            )
            return []
