"""
F4 Configuration Health API Routes

Provides HTTP endpoints for F4 configuration health monitoring,
provider availability status, and operational readiness validation.

These endpoints enable:
- Real-time F4 configuration health checks
- Provider availability monitoring
- Configuration switching validation
- Performance metrics access
- Integration with monitoring systems

Routes:
- GET /health/config: Overall F4 configuration health
- GET /health/config/providers: Provider availability status
- GET /health/config/environment: Environment variables validation
- GET /health/config/performance: Performance metrics and trends
- GET /health/config/switching: Provider switching capabilities
"""

import asyncio
import logging
import time
from typing import Any, Dict

from flask import Blueprint, jsonify, request
from pydantic import BaseModel, Field

from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import get_cached_veo3_settings, validate_veo3_environment
from src.deployment.monitors.f4_config_monitor import F4ConfigurationMonitor

logger = logging.getLogger(__name__)

# Create blueprint for F4 health routes
f4_health_bp = Blueprint("f4_health", __name__, url_prefix="/health/config")


class F4HealthResponse(BaseModel):
    """Response model for F4 health check endpoints."""

    status: str = Field(description="Health status: healthy, warning, critical, error")
    timestamp: str = Field(description="ISO timestamp of health check")
    check_duration_ms: float = Field(
        description="Time taken for health check in milliseconds"
    )
    details: Dict[str, Any] = Field(description="Detailed health check results")


@f4_health_bp.route("/", methods=["GET"])
def get_f4_overall_health():
    """
    Get overall F4 configuration health status.

    Returns comprehensive health check including:
    - Configuration loading performance
    - Provider availability
    - Environment variables validation
    - Integration health

    Returns:
        JSON response with overall health status and detailed results
    """
    start_time = time.time()

    try:
        # Run quick health check
        monitor = F4ConfigurationMonitor()

        # Since we can't use async in Flask route directly, we'll run sync checks
        health_result = _run_sync_health_check(monitor)

        duration_ms = (time.time() - start_time) * 1000

        response = F4HealthResponse(
            status=health_result.get("overall_status", "unknown"),
            timestamp=health_result.get("timestamp", ""),
            check_duration_ms=duration_ms,
            details=health_result,
        )

        # Set appropriate HTTP status code based on health
        status_code = _get_http_status_code(
            health_result.get("overall_status", "unknown")
        )

        return jsonify(response.model_dump()), status_code

    except Exception as e:
        logger.error(f"F4 health check failed: {e}")

        error_response = F4HealthResponse(
            status="error",
            timestamp="",
            check_duration_ms=(time.time() - start_time) * 1000,
            details={"error": str(e)},
        )

        return jsonify(error_response.model_dump()), 500


@f4_health_bp.route("/providers", methods=["GET"])
def get_provider_availability():
    """
    Get provider availability status and configuration validation.

    Returns:
        JSON response with provider availability and validation results
    """
    start_time = time.time()

    try:
        # Get provider availability
        availability = ConfigurationFactory.get_provider_availability()

        # Validate each provider
        azure_validation = ConfigurationFactory.validate_provider_configuration(
            "azure_sora"
        )
        veo3_validation = ConfigurationFactory.validate_provider_configuration(
            "google_veo3"
        )

        # Get default provider
        default_provider = ConfigurationFactory.get_default_provider()

        # Determine overall status
        total_available = sum(availability.values())
        if total_available == 0:
            status = "critical"
        elif not availability.get(default_provider, False):
            status = "warning"
        else:
            status = "healthy"

        result = {
            "status": status,
            "default_provider": default_provider,
            "availability": availability,
            "total_available": total_available,
            "validations": {
                "azure_sora": azure_validation,
                "google_veo3": veo3_validation,
            },
            "timestamp": time.time(),
            "check_duration_ms": (time.time() - start_time) * 1000,
        }

        status_code = _get_http_status_code(status)
        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"Provider availability check failed: {e}")
        return jsonify(
            {"status": "error", "error": str(e), "timestamp": time.time()}
        ), 500


@f4_health_bp.route("/environment", methods=["GET"])
def get_environment_validation():
    """
    Get environment variables validation and configuration completeness.

    Returns:
        JSON response with environment validation results
    """
    start_time = time.time()

    try:
        # Run Veo3 environment validation
        veo3_validation = validate_veo3_environment()

        # Get Veo3 settings
        veo3_settings = get_cached_veo3_settings()

        # Check critical environment variables
        import os

        critical_vars = ["USE_MOCK_VEO", "DEFAULT_PROVIDER", "DEPLOYMENT_TYPE"]

        env_status = {}
        missing_vars = []

        for var in critical_vars:
            value = os.getenv(var)
            env_status[var] = {
                "present": value is not None,
                "value": value if value is not None else None,
            }
            if value is None:
                missing_vars.append(var)

        # Determine status
        if missing_vars:
            status = "critical"
        elif not veo3_validation.get("valid", False):
            status = "warning"
        else:
            status = "healthy"

        result = {
            "status": status,
            "environment_variables": env_status,
            "missing_critical": missing_vars,
            "veo3_validation": veo3_validation,
            "deployment_type": os.getenv("DEPLOYMENT_TYPE", "unknown"),
            "environment": os.getenv("F4_DEPLOYMENT_ENVIRONMENT", "unknown"),
            "timestamp": time.time(),
            "check_duration_ms": (time.time() - start_time) * 1000,
        }

        status_code = _get_http_status_code(status)
        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        return jsonify(
            {"status": "error", "error": str(e), "timestamp": time.time()}
        ), 500


@f4_health_bp.route("/performance", methods=["GET"])
def get_performance_metrics():
    """
    Get F4 configuration performance metrics and trends.

    Query Parameters:
        detailed (bool): Include detailed performance analysis

    Returns:
        JSON response with performance metrics
    """
    start_time = time.time()

    try:
        detailed = request.args.get("detailed", "false").lower() == "true"

        # Test configuration loading performance
        config_start = time.time()
        base_config = ConfigurationFactory.get_base_config()
        veo3_settings = get_cached_veo3_settings()
        config_load_time = (time.time() - config_start) * 1000

        # Test provider switching performance
        switch_start = time.time()
        try:
            azure_config = ConfigurationFactory.create_provider_config("azure_sora")
            veo3_config = ConfigurationFactory.create_provider_config("google_veo3")
            switch_time = (time.time() - switch_start) * 1000
            switch_success = True
        except Exception:
            switch_time = (time.time() - switch_start) * 1000
            switch_success = False

        # Performance thresholds
        config_threshold = 5000  # 5 seconds
        switch_threshold = 10000  # 10 seconds

        # Calculate performance status
        performance_issues = []
        if config_load_time > config_threshold:
            performance_issues.append(
                f"Config loading time ({config_load_time:.2f}ms) exceeds threshold ({config_threshold}ms)"
            )

        if switch_time > switch_threshold:
            performance_issues.append(
                f"Provider switching time ({switch_time:.2f}ms) exceeds threshold ({switch_threshold}ms)"
            )

        if not switch_success:
            performance_issues.append("Provider switching failed")

        status = (
            "critical"
            if not switch_success
            else ("warning" if performance_issues else "healthy")
        )

        result = {
            "status": status,
            "performance_metrics": {
                "configuration_loading": {
                    "time_ms": config_load_time,
                    "threshold_ms": config_threshold,
                    "exceeds_threshold": config_load_time > config_threshold,
                },
                "provider_switching": {
                    "time_ms": switch_time,
                    "threshold_ms": switch_threshold,
                    "exceeds_threshold": switch_time > switch_threshold,
                    "success": switch_success,
                },
            },
            "performance_issues": performance_issues,
            "timestamp": time.time(),
            "check_duration_ms": (time.time() - start_time) * 1000,
        }

        # Add detailed analysis if requested
        if detailed:
            result["detailed_analysis"] = {
                "configuration_factory": {
                    "base_config_loaded": bool(base_config),
                    "veo3_settings_loaded": bool(veo3_settings),
                },
                "provider_configs": {
                    "azure_available": "azure_config" in locals(),
                    "veo3_available": "veo3_config" in locals(),
                },
            }

        status_code = _get_http_status_code(status)
        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"Performance metrics check failed: {e}")
        return jsonify(
            {"status": "error", "error": str(e), "timestamp": time.time()}
        ), 500


@f4_health_bp.route("/switching", methods=["GET"])
def get_provider_switching_status():
    """
    Get provider switching capabilities and test switching functionality.

    Returns:
        JSON response with provider switching test results
    """
    start_time = time.time()

    try:
        # Test provider switching for each provider
        providers = ["azure_sora", "google_veo3"]
        switching_results = {}
        successful_switches = 0

        for provider in providers:
            provider_start = time.time()
            try:
                config = ConfigurationFactory.create_provider_config(provider)
                provider_time = (time.time() - provider_start) * 1000
                switching_results[provider] = {
                    "status": "success",
                    "switch_time_ms": provider_time,
                    "config_created": bool(config),
                }
                successful_switches += 1
            except Exception as e:
                provider_time = (time.time() - provider_start) * 1000
                switching_results[provider] = {
                    "status": "error",
                    "switch_time_ms": provider_time,
                    "error": str(e),
                }

        # Test optimal provider selection
        try:
            optimal_config = ConfigurationFactory.get_optimal_provider_config()
            optimal_status = "success"
        except Exception as e:
            optimal_config = None
            optimal_status = f"error: {str(e)}"

        # Determine overall switching status
        if successful_switches == 0:
            status = "critical"
        elif successful_switches < len(providers):
            status = "warning"
        else:
            status = "healthy"

        result = {
            "status": status,
            "provider_switching": switching_results,
            "successful_switches": successful_switches,
            "total_providers": len(providers),
            "optimal_provider_selection": {
                "status": optimal_status,
                "config_available": bool(optimal_config),
            },
            "timestamp": time.time(),
            "check_duration_ms": (time.time() - start_time) * 1000,
        }

        status_code = _get_http_status_code(status)
        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"Provider switching test failed: {e}")
        return jsonify(
            {"status": "error", "error": str(e), "timestamp": time.time()}
        ), 500


@f4_health_bp.route("/monitor", methods=["GET"])
def get_monitor_status():
    """
    Get F4 configuration monitor status and statistics.

    Returns:
        JSON response with monitor status and statistics
    """
    try:
        # This would typically connect to a running monitor instance
        # For now, we'll return basic status information

        import os

        result = {
            "status": "healthy",
            "monitor_configuration": {
                "interval_seconds": int(os.getenv("MONITOR_INTERVAL", "60")),
                "config_load_threshold_ms": int(
                    os.getenv("ALERT_THRESHOLD_CONFIG_LOAD_TIME", "5000")
                ),
                "provider_switch_threshold_ms": int(
                    os.getenv("ALERT_THRESHOLD_PROVIDER_SWITCH_TIME", "10000")
                ),
            },
            "deployment_info": {
                "deployment_type": os.getenv("DEPLOYMENT_TYPE", "unknown"),
                "environment": os.getenv("F4_DEPLOYMENT_ENVIRONMENT", "unknown"),
                "config_validation_enabled": os.getenv(
                    "CONFIG_VALIDATION_ENABLED", "true"
                ).lower()
                == "true",
            },
            "timestamp": time.time(),
        }

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Monitor status check failed: {e}")
        return jsonify(
            {"status": "error", "error": str(e), "timestamp": time.time()}
        ), 500


def _run_sync_health_check(monitor: F4ConfigurationMonitor) -> Dict[str, Any]:
    """
    Run a synchronous version of the health check for Flask compatibility.

    Args:
        monitor: F4ConfigurationMonitor instance

    Returns:
        Health check results dictionary
    """
    try:
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Run the async health check
            result = loop.run_until_complete(monitor.run_health_check())
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"Sync health check failed: {e}")
        return {"overall_status": "error", "error": str(e), "timestamp": time.time()}


def _get_http_status_code(health_status: str) -> int:
    """
    Convert health status to appropriate HTTP status code.

    Args:
        health_status: Health status string

    Returns:
        HTTP status code
    """
    status_map = {
        "healthy": 200,
        "warning": 200,  # Still OK but with warnings
        "critical": 503,  # Service unavailable
        "error": 500,  # Internal server error
        "unknown": 500,  # Internal server error
    }

    return status_map.get(health_status, 500)


# Register error handlers
@f4_health_bp.errorhandler(Exception)
def handle_f4_health_error(error):
    """Handle errors in F4 health endpoints."""
    logger.error(f"F4 health endpoint error: {error}")

    return jsonify(
        {"status": "error", "error": str(error), "timestamp": time.time()}
    ), 500
