"""
F4 Configuration Monitoring API Routes

Comprehensive monitoring endpoints for F4 configuration health,
metrics collection, alerting status, and operational dashboards.

These endpoints provide:
- Real-time F4 metrics and performance data
- Alert management and status monitoring
- Prometheus metrics export for external monitoring
- Configuration and provider performance insights
- Operational dashboards and reporting

Routes:
- GET /monitoring/f4/metrics: Current F4 metrics
- GET /monitoring/f4/metrics/prometheus: Prometheus metrics export
- GET /monitoring/f4/alerts: Alert status and management
- GET /monitoring/f4/dashboard: Monitoring dashboard data
- GET /monitoring/f4/performance: Performance analytics
- POST /monitoring/f4/alerts/resolve: Resolve alerts
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict

from flask import Blueprint, Response, jsonify, request
from pydantic import BaseModel, Field

from src.monitoring.f4_alerting import get_alert_manager
from src.monitoring.f4_metrics import get_metrics_collector

logger = logging.getLogger(__name__)

# Create blueprint for F4 monitoring routes
f4_monitoring_bp = Blueprint("f4_monitoring", __name__, url_prefix="/monitoring/f4")


class F4MonitoringResponse(BaseModel):
    """Response model for F4 monitoring endpoints."""

    status: str = Field(description="Response status")
    timestamp: str = Field(description="Response timestamp")
    data: Dict[str, Any] = Field(description="Response data")


@f4_monitoring_bp.route("/metrics", methods=["GET"])
def get_f4_metrics():
    """
    Get current F4 configuration metrics.

    Query Parameters:
        detailed (bool): Include detailed metrics breakdown
        history (bool): Include metrics history

    Returns:
        JSON response with current F4 metrics
    """
    try:
        detailed = request.args.get("detailed", "false").lower() == "true"
        include_history = request.args.get("history", "false").lower() == "true"

        collector = get_metrics_collector()
        current_metrics = collector.get_current_metrics()

        response_data = {
            "current_metrics": current_metrics,
            "collection_active": collector.collection_active,
            "start_time": datetime.fromtimestamp(
                collector.start_time, timezone.utc
            ).isoformat(),
        }

        # Add detailed breakdown if requested
        if detailed:
            response_data["thresholds"] = collector.thresholds.copy()
            response_data["alert_conditions"] = collector.get_alert_conditions()

        # Add metrics history if requested
        if include_history:
            response_data["metrics_history"] = {
                "config_load_times": list(
                    collector.metrics_history["config_load_times"]
                )[-50:],  # Last 50
                "provider_switches": list(
                    collector.metrics_history["provider_switches"]
                )[-50:],
                "health_checks": list(collector.metrics_history["health_checks"])[-20:],
                "errors": list(collector.metrics_history["errors"])[-100:],
            }

        response = F4MonitoringResponse(
            status="success",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data=response_data,
        )

        return jsonify(response.model_dump()), 200

    except Exception as e:
        logger.error(f"Failed to get F4 metrics: {e}")
        error_response = F4MonitoringResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data={"error": str(e)},
        )
        return jsonify(error_response.model_dump()), 500


@f4_monitoring_bp.route("/metrics/prometheus", methods=["GET"])
def get_prometheus_metrics():
    """
    Get F4 metrics in Prometheus exposition format.

    Returns:
        Prometheus metrics as plain text
    """
    try:
        collector = get_metrics_collector()
        metrics_data = collector.get_prometheus_metrics()

        return Response(
            metrics_data,
            mimetype="text/plain; version=0.0.4; charset=utf-8",
            headers={"Cache-Control": "no-cache"},
        )

    except Exception as e:
        logger.error(f"Failed to export Prometheus metrics: {e}")
        error_metrics = f"# Error exporting F4 metrics: {e}\n"
        return Response(error_metrics, mimetype="text/plain", status=500)


@f4_monitoring_bp.route("/alerts", methods=["GET"])
def get_f4_alerts():
    """
    Get F4 alert status and management information.

    Query Parameters:
        status (str): Filter by alert status (active/resolved)
        severity (str): Filter by alert severity
        type (str): Filter by alert type

    Returns:
        JSON response with alert information
    """
    try:
        alert_manager = get_alert_manager()

        # Get query parameters
        status_filter = request.args.get("status")
        severity_filter = request.args.get("severity")
        type_filter = request.args.get("type")

        # Get all alerts
        active_alerts = alert_manager.get_active_alerts()
        alert_summary = alert_manager.get_alert_summary()

        # Apply filters
        filtered_alerts = active_alerts
        if status_filter:
            filtered_alerts = [
                alert
                for alert in filtered_alerts
                if alert.get("status") == status_filter
            ]
        if severity_filter:
            filtered_alerts = [
                alert
                for alert in filtered_alerts
                if alert.get("severity") == severity_filter
            ]
        if type_filter:
            filtered_alerts = [
                alert
                for alert in filtered_alerts
                if alert.get("alert_type") == type_filter
            ]

        response_data = {
            "alerts": filtered_alerts,
            "summary": alert_summary,
            "filters_applied": {
                "status": status_filter,
                "severity": severity_filter,
                "type": type_filter,
            },
        }

        response = F4MonitoringResponse(
            status="success",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data=response_data,
        )

        return jsonify(response.model_dump()), 200

    except Exception as e:
        logger.error(f"Failed to get F4 alerts: {e}")
        error_response = F4MonitoringResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data={"error": str(e)},
        )
        return jsonify(error_response.model_dump()), 500


@f4_monitoring_bp.route("/alerts/resolve", methods=["POST"])
def resolve_f4_alert():
    """
    Resolve an F4 alert.

    Request Body:
        {
            "alert_id": "string",
            "resolution_note": "string" (optional)
        }

    Returns:
        JSON response confirming alert resolution
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "Request body required"}), 400

        alert_id = data.get("alert_id")
        if not alert_id:
            return jsonify({"status": "error", "message": "alert_id required"}), 400

        resolution_note = data.get("resolution_note", "")

        alert_manager = get_alert_manager()
        success = alert_manager.resolve_alert(alert_id)

        if success:
            response_data = {
                "alert_id": alert_id,
                "resolved": True,
                "resolution_note": resolution_note,
                "resolved_at": datetime.now(timezone.utc).isoformat(),
            }

            response = F4MonitoringResponse(
                status="success",
                timestamp=datetime.now(timezone.utc).isoformat(),
                data=response_data,
            )

            return jsonify(response.model_dump()), 200
        else:
            return jsonify(
                {"status": "error", "message": f"Alert {alert_id} not found"}
            ), 404

    except Exception as e:
        logger.error(f"Failed to resolve F4 alert: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500


@f4_monitoring_bp.route("/dashboard", methods=["GET"])
def get_f4_dashboard():
    """
    Get F4 monitoring dashboard data.

    Returns:
        JSON response with dashboard metrics and status
    """
    try:
        collector = get_metrics_collector()
        alert_manager = get_alert_manager()

        # Get current metrics
        current_metrics = collector.get_current_metrics()

        # Get alert summary
        alert_summary = alert_manager.get_alert_summary()

        # Get active alert conditions
        alert_conditions = collector.get_alert_conditions()

        # Calculate health score
        health_score = _calculate_health_score(current_metrics, alert_summary)

        # Get performance summary
        performance_summary = _get_performance_summary(current_metrics)

        # Get provider status
        provider_status = _get_provider_status(current_metrics)

        dashboard_data = {
            "health_score": health_score,
            "overall_status": _determine_overall_status(health_score, alert_summary),
            "performance_summary": performance_summary,
            "provider_status": provider_status,
            "alert_summary": alert_summary,
            "active_alert_conditions": alert_conditions,
            "uptime_seconds": current_metrics.get("uptime_seconds", 0),
            "last_updated": datetime.now(timezone.utc).isoformat(),
        }

        response = F4MonitoringResponse(
            status="success",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data=dashboard_data,
        )

        return jsonify(response.model_dump()), 200

    except Exception as e:
        logger.error(f"Failed to get F4 dashboard: {e}")
        error_response = F4MonitoringResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data={"error": str(e)},
        )
        return jsonify(error_response.model_dump()), 500


@f4_monitoring_bp.route("/performance", methods=["GET"])
def get_f4_performance():
    """
    Get F4 performance analytics and trends.

    Query Parameters:
        timeframe (str): Time frame for analysis (1h, 24h, 7d)
        metrics (str): Comma-separated list of metrics to include

    Returns:
        JSON response with performance analytics
    """
    try:
        timeframe = request.args.get("timeframe", "1h")
        metrics_filter = (
            request.args.get("metrics", "").split(",")
            if request.args.get("metrics")
            else []
        )

        collector = get_metrics_collector()
        current_metrics = collector.get_current_metrics()

        # Get performance analytics
        performance_data = {
            "timeframe": timeframe,
            "current_performance": current_metrics.get("recent_performance", {}),
            "thresholds": collector.thresholds.copy(),
            "performance_trends": _analyze_performance_trends(collector, timeframe),
            "configuration_metrics": _get_configuration_performance_metrics(collector),
            "provider_performance": _get_provider_performance_metrics(collector),
        }

        # Filter metrics if specified
        if metrics_filter and metrics_filter != [""]:
            filtered_data = {}
            for metric in metrics_filter:
                if metric in performance_data:
                    filtered_data[metric] = performance_data[metric]
            performance_data = filtered_data if filtered_data else performance_data

        response = F4MonitoringResponse(
            status="success",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data=performance_data,
        )

        return jsonify(response.model_dump()), 200

    except Exception as e:
        logger.error(f"Failed to get F4 performance analytics: {e}")
        error_response = F4MonitoringResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data={"error": str(e)},
        )
        return jsonify(error_response.model_dump()), 500


@f4_monitoring_bp.route("/status", methods=["GET"])
def get_f4_monitoring_status():
    """
    Get F4 monitoring system status.

    Returns:
        JSON response with monitoring system health
    """
    try:
        collector = get_metrics_collector()
        alert_manager = get_alert_manager()

        status_data = {
            "metrics_collector": {
                "active": collector.collection_active,
                "prometheus_enabled": collector.enable_prometheus,
                "start_time": datetime.fromtimestamp(
                    collector.start_time, timezone.utc
                ).isoformat(),
                "metrics_history_size": collector.metrics_history_size,
                "thresholds_configured": len(collector.thresholds),
            },
            "alert_manager": {
                "running": alert_manager.running,
                "configured_rules": len(alert_manager.alert_rules),
                "configured_channels": list(alert_manager.channels.keys()),
                "total_alerts": len(alert_manager.alerts),
                "active_alerts": len(alert_manager.get_active_alerts()),
            },
            "system_health": {
                "monitoring_operational": collector.collection_active
                and alert_manager.running,
                "last_check": datetime.now(timezone.utc).isoformat(),
            },
        }

        response = F4MonitoringResponse(
            status="success",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data=status_data,
        )

        return jsonify(response.model_dump()), 200

    except Exception as e:
        logger.error(f"Failed to get F4 monitoring status: {e}")
        error_response = F4MonitoringResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            data={"error": str(e)},
        )
        return jsonify(error_response.model_dump()), 500


def _calculate_health_score(
    metrics: Dict[str, Any], alert_summary: Dict[str, Any]
) -> int:
    """Calculate overall health score (0-100)."""
    score = 100

    # Deduct for active alerts
    active_alerts = alert_summary.get("active_alerts", 0)
    score -= min(active_alerts * 5, 30)  # Max 30 points for alerts

    # Deduct for critical alerts
    critical_alerts = alert_summary.get("severity_breakdown", {}).get("critical", 0)
    score -= critical_alerts * 20  # 20 points per critical alert

    # Deduct for high error rate
    error_rate = metrics.get("current_state", {}).get("error_rate", 0)
    if error_rate > 5:
        score -= min((error_rate - 5) * 2, 20)  # Max 20 points for error rate

    # Deduct for poor provider availability
    provider_availability = metrics.get("current_state", {}).get(
        "provider_availability", {}
    )
    if provider_availability:
        total_providers = len(provider_availability)
        available_providers = sum(provider_availability.values())
        availability_percent = (
            (available_providers / total_providers * 100)
            if total_providers > 0
            else 100
        )

        if availability_percent < 100:
            score -= (
                100 - availability_percent
            ) / 2  # Half point per percent unavailable

    # Deduct for performance issues
    recent_perf = metrics.get("recent_performance", {})
    config_load_time = recent_perf.get("avg_config_load_time_ms", 0)
    if config_load_time > 1000:  # 1 second threshold
        score -= min(
            (config_load_time - 1000) / 100, 10
        )  # Max 10 points for config load time

    return max(0, int(score))


def _determine_overall_status(health_score: int, alert_summary: Dict[str, Any]) -> str:
    """Determine overall system status."""
    critical_alerts = alert_summary.get("severity_breakdown", {}).get("critical", 0)

    if critical_alerts > 0:
        return "critical"
    elif health_score < 70:
        return "warning"
    elif health_score < 90:
        return "healthy"
    else:
        return "excellent"


def _get_performance_summary(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """Get performance summary from metrics."""
    recent_perf = metrics.get("recent_performance", {})

    return {
        "config_load_performance": {
            "avg_time_ms": recent_perf.get("avg_config_load_time_ms", 0),
            "recent_loads": recent_perf.get("recent_config_loads", 0),
            "status": "good"
            if recent_perf.get("avg_config_load_time_ms", 0) < 1000
            else "degraded",
        },
        "provider_switch_performance": {
            "avg_time_ms": recent_perf.get("avg_provider_switch_time_ms", 0),
            "recent_switches": recent_perf.get("recent_provider_switches", 0),
            "status": "good"
            if recent_perf.get("avg_provider_switch_time_ms", 0) < 5000
            else "degraded",
        },
        "error_rate": {
            "current_rate": metrics.get("current_state", {}).get("error_rate", 0),
            "status": "good"
            if metrics.get("current_state", {}).get("error_rate", 0) < 1
            else "elevated",
        },
    }


def _get_provider_status(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """Get provider status from metrics."""
    provider_availability = metrics.get("current_state", {}).get(
        "provider_availability", {}
    )

    provider_status = {}
    for provider, available in provider_availability.items():
        provider_status[provider] = {
            "available": available,
            "status": "healthy" if available else "unavailable",
        }

    total_providers = len(provider_availability)
    available_providers = sum(provider_availability.values())

    return {
        "providers": provider_status,
        "summary": {
            "total": total_providers,
            "available": available_providers,
            "availability_percent": (available_providers / total_providers * 100)
            if total_providers > 0
            else 0,
        },
    }


def _analyze_performance_trends(collector, timeframe: str) -> Dict[str, Any]:
    """Analyze performance trends over time."""
    # This is a simplified implementation
    # In a real system, you'd analyze historical data based on timeframe

    config_load_times = list(collector.metrics_history["config_load_times"])
    provider_switches = list(collector.metrics_history["provider_switches"])

    return {
        "config_load_trend": "stable",  # Would calculate actual trend
        "provider_switch_trend": "stable",
        "sample_size": {
            "config_loads": len(config_load_times),
            "provider_switches": len(provider_switches),
        },
    }


def _get_configuration_performance_metrics(collector) -> Dict[str, Any]:
    """Get configuration-specific performance metrics."""
    config_loads = list(collector.metrics_history["config_load_times"])

    if not config_loads:
        return {"no_data": True}

    recent_loads = config_loads[-10:]  # Last 10 loads
    load_times = [load["duration_ms"] for load in recent_loads]

    return {
        "recent_loads": len(recent_loads),
        "avg_load_time_ms": sum(load_times) / len(load_times) if load_times else 0,
        "min_load_time_ms": min(load_times) if load_times else 0,
        "max_load_time_ms": max(load_times) if load_times else 0,
        "success_rate": len(
            [load for load in recent_loads if load.get("status") == "success"]
        )
        / len(recent_loads)
        * 100
        if recent_loads
        else 100,
    }


def _get_provider_performance_metrics(collector) -> Dict[str, Any]:
    """Get provider-specific performance metrics."""
    provider_switches = list(collector.metrics_history["provider_switches"])

    if not provider_switches:
        return {"no_data": True}

    recent_switches = provider_switches[-20:]  # Last 20 switches

    provider_metrics = {}
    for switch in recent_switches:
        provider = switch["provider"]
        if provider not in provider_metrics:
            provider_metrics[provider] = {
                "switch_count": 0,
                "success_count": 0,
                "total_time_ms": 0,
                "switch_times": [],
            }

        provider_metrics[provider]["switch_count"] += 1
        if switch["success"]:
            provider_metrics[provider]["success_count"] += 1
        provider_metrics[provider]["total_time_ms"] += switch["duration_ms"]
        provider_metrics[provider]["switch_times"].append(switch["duration_ms"])

    # Calculate averages
    for provider, metrics in provider_metrics.items():
        if metrics["switch_count"] > 0:
            metrics["avg_switch_time_ms"] = (
                metrics["total_time_ms"] / metrics["switch_count"]
            )
            metrics["success_rate"] = (
                metrics["success_count"] / metrics["switch_count"]
            ) * 100
        else:
            metrics["avg_switch_time_ms"] = 0
            metrics["success_rate"] = 0

        # Remove raw times to reduce response size
        del metrics["switch_times"]
        del metrics["total_time_ms"]

    return provider_metrics


# Register error handlers
@f4_monitoring_bp.errorhandler(Exception)
def handle_f4_monitoring_error(error):
    """Handle errors in F4 monitoring endpoints."""
    logger.error(f"F4 monitoring endpoint error: {error}")

    return jsonify(
        {
            "status": "error",
            "error": str(error),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    ), 500
