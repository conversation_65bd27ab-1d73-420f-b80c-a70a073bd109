"""Health check API routes."""

import logging

from flask import Blueprint, Response, jsonify, request

from src.api.models import HealthCheckResponse
from src.core.models import APIResponse
from src.monitoring.health_check import HealthCheck
from src.monitoring.metrics import MetricsCollector

health_bp = Blueprint("health", __name__)
logger = logging.getLogger(__name__)

# Initialize components
health_check = HealthCheck()
metrics_collector = MetricsCollector()


@health_bp.route("/health")
def get_health() -> Response:
    """
    Get overall system health status.

    Returns:
        Response: JSON response with system health information
    """
    try:
        logger.info("🏥 Health check requested")

        # Get overall health status
        health_status = health_check.get_overall_health()

        # Create response model
        response_data = HealthCheckResponse(
            overall_status=health_status.get("overall_status", "unknown"),
            components=health_status.get("components", {}),
            timestamp=health_status.get("timestamp", ""),
            version=health_status.get("version", "unknown"),
        )

        # Determine HTTP status code based on health
        status_code = 200
        if health_status.get("overall_status") == "unhealthy":
            status_code = 503
        elif health_status.get("overall_status") == "degraded":
            status_code = 200  # Still operational

        logger.info(f"✅ Health check completed: {health_status.get('overall_status')}")
        return jsonify(response_data.model_dump()), status_code

    except Exception as e:
        logger.error(f"💥 Health check failed: {e}")

        response = APIResponse(
            success=False, message="Health check failed", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@health_bp.route("/health/database")
def get_database_health() -> Response:
    """
    Get database health status.

    Returns:
        Response: JSON response with database health information
    """
    try:
        logger.info("🗄️ Database health check requested")

        # Get database health
        db_health = health_check.check_database_health()

        response = APIResponse(
            success=True, message="Database health retrieved", data=db_health
        )

        # Determine status code
        status_code = 200
        if db_health.get("status") == "unhealthy":
            status_code = 503

        logger.info(f"✅ Database health: {db_health.get('status')}")
        return jsonify(response.model_dump()), status_code

    except Exception as e:
        logger.error(f"💥 Database health check failed: {e}")

        response = APIResponse(
            success=False, message="Database health check failed", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@health_bp.route("/health/azure")
def get_azure_health() -> Response:
    """
    Get Azure API health status.

    Returns:
        Response: JSON response with Azure API health information
    """
    try:
        logger.info("☁️ Azure API health check requested")

        # Get Azure API health
        azure_health = health_check.check_azure_api_health()

        response = APIResponse(
            success=True, message="Azure API health retrieved", data=azure_health
        )

        # Determine status code
        status_code = 200
        if azure_health.get("status") in [
            "unhealthy",
            "unreachable",
            "timeout",
            "connection_error",
            "error",
        ]:
            status_code = 503

        logger.info(f"✅ Azure API health: {azure_health.get('status')}")
        return jsonify(response.model_dump()), status_code

    except Exception as e:
        logger.error(f"💥 Azure API health check failed: {e}")

        response = APIResponse(
            success=False, message="Azure API health check failed", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@health_bp.route("/metrics")
def get_metrics() -> Response:
    """
    Get comprehensive system metrics.

    Returns:
        Response: JSON response with system metrics
    """
    try:
        logger.info("📊 System metrics requested")

        # Get comprehensive metrics
        comprehensive_metrics = metrics_collector.get_comprehensive_metrics()

        logger.info("✅ System metrics retrieved")
        return jsonify(comprehensive_metrics)

    except Exception as e:
        logger.error(f"💥 Failed to get system metrics: {e}")

        response = APIResponse(
            success=False, message="Failed to retrieve system metrics", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@health_bp.route("/metrics/jobs")
def get_job_metrics() -> Response:
    """
    Get job-specific metrics and queue health.

    Returns:
        Response: JSON response with job metrics
    """
    try:
        logger.info("📋 Job metrics requested")

        # Get job metrics from query parameters
        try:
            hours = int(request.args.get("hours", 24))
        except (ValueError, TypeError):
            hours = 24  # Default to 24 hours if invalid
        job_metrics = metrics_collector.get_job_completion_rate(hours)
        queue_health = health_check.check_job_queue_health()

        # Combine metrics
        combined_metrics = {**job_metrics, "queue_health": queue_health}

        response = APIResponse(
            success=True, message="Job metrics retrieved", data=combined_metrics
        )

        logger.info("✅ Job metrics retrieved")
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"💥 Failed to get job metrics: {e}")

        response = APIResponse(
            success=False, message="Failed to retrieve job metrics", error=str(e)
        )
        return jsonify(response.model_dump()), 500
