"""Job management API routes."""

import logging
from typing import Union

from flask import Blueprint, Response, g, jsonify

from src.api.job_repository import JobRepository
from src.api.models import JobStatusResponse, QueueStatusResponse, SessionInfoResponse
from src.core.interfaces import QueueManagerInterface
from src.core.models import APIResponse
from src.session.manager import get_session_manager

job_bp = Blueprint("job", __name__)
logger = logging.getLogger(__name__)

# Initialize components
job_repository = JobRepository()


def _get_queue_manager() -> QueueManagerInterface:
    """
    Get queue manager instance with dependency injection support.

    Returns:
        QueueManagerInterface: Queue manager instance
    """
    from src.job_queue.manager import QueueManager

    return QueueManager()


@job_bp.route("/status/<job_id>")
def get_job_status(job_id: str) -> Union[Response, tuple[Response, int]]:
    """
    Get job status and progress information.

    Args:
        job_id: Unique job identifier

    Returns:
        Response: JSON response with job status information
    """
    try:
        logger.info(f"📊 Job status requested for: {job_id}")

        # Get job from database
        job = job_repository.get_job_by_id(job_id)
        if not job:
            response = APIResponse(
                success=False,
                message="Job not found",
                error=f"No job found with ID: {job_id}",
            )
            return jsonify(response.model_dump()), 404

        # Create response model
        response_data = JobStatusResponse(
            job_id=job.id,
            status=job.status,
            video_url=f"/video/{job.id}" if job.status == "succeeded" else None,
            download_url=f"/download/{job.id}" if job.status == "succeeded" else None,
            error_message=job.error_message if hasattr(job, "error_message") else None,
            created_at=job.created_at,
            completed_at=job.completed_at if hasattr(job, "completed_at") else None,
            progress_percentage=_calculate_progress_percentage(job.status),
        )

        logger.info(f"✅ Job status: {job.status}")
        return jsonify(response_data.model_dump())

    except Exception as e:
        logger.error(f"💥 Failed to get job status: {e}")

        response = APIResponse(
            success=False, message="Failed to retrieve job status", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@job_bp.route("/queue/status")
def get_queue_status() -> Union[Response, tuple[Response, int]]:
    """
    Get queue status for current session.

    Returns:
        Response: JSON response with queue status
    """
    try:
        logger.info("📋 Queue status requested")

        # Get session ID from request context
        session_id = getattr(g, "session_id", None)
        if not session_id:
            response = APIResponse(
                success=False,
                message="Session required",
                error="Session not found in request context",
            )
            return jsonify(response.model_dump()), 400

        # Get queue status from queue manager
        try:
            queue_manager = _get_queue_manager()
            queue_info = queue_manager.get_session_queue_status(session_id)

            # Create response model
            response_data = QueueStatusResponse(
                total_jobs=queue_info.get("total_jobs", 0),
                position=queue_info.get("user_job_positions", [{}])[0].get(
                    "position", 0
                ),
                estimated_wait_minutes=queue_info.get("user_job_positions", [{}])[
                    0
                ].get("estimated_wait_minutes", 0),
                session_jobs=queue_info.get("user_jobs", 0),
                can_submit_more=queue_info.get("can_submit_more", True),
            )

            logger.info(f"✅ Queue status retrieved for session: {session_id}")
            return jsonify(response_data.model_dump())

        except Exception as e:
            logger.error(f"💥 Queue manager error: {e}")

            # Fallback response
            response_data = QueueStatusResponse(
                total_jobs=0,
                position=0,
                estimated_wait_minutes=0,
                session_jobs=0,
                can_submit_more=True,
            )
            return jsonify(response_data.model_dump())

    except Exception as e:
        logger.error(f"💥 Failed to get queue status: {e}")

        response = APIResponse(
            success=False, message="Failed to retrieve queue status", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@job_bp.route("/queue/stats")
def get_queue_stats() -> Response:
    """
    Get overall queue statistics for monitoring.

    Returns:
        dict: JSON response with queue statistics
    """
    try:
        logger.info("📊 Queue statistics requested")

        # Get queue statistics
        try:
            queue_manager = _get_queue_manager()
            stats = queue_manager.get_queue_statistics()

            response = APIResponse(
                success=True, message="Queue statistics retrieved", data=stats
            )

            logger.info("✅ Queue statistics retrieved")
            return jsonify(response.model_dump())

        except Exception as e:
            logger.error(f"💥 Queue manager error: {e}")

            # Fallback response
            response = APIResponse(
                success=True,
                message="Queue statistics retrieved (fallback)",
                data={
                    "queue_size": 0,
                    "active_processing": 0,
                    "available_workers": 0,
                    "average_wait_time_minutes": 0,
                    "error": "Queue manager unavailable",
                },
            )
            return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"💥 Failed to get queue statistics: {e}")

        response = APIResponse(
            success=False, message="Failed to retrieve queue statistics", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@job_bp.route("/session/info")
def get_session_info() -> Response:
    """
    Get current session information.

    Returns:
        Response: JSON response with session information
    """
    try:
        logger.info("🔑 Session info requested")

        # Get session ID from request context
        session_id = getattr(g, "session_id", None)
        if not session_id:
            response = APIResponse(
                success=False,
                message="Session required",
                error="Session not found in request context",
            )
            return jsonify(response.model_dump()), 400

        # Get session manager
        session_manager = get_session_manager()
        session_data = session_manager.get_session_data(session_id)

        if not session_data:
            response = APIResponse(
                success=False,
                message="Session not found",
                error=f"No session found with ID: {session_id}",
            )
            return jsonify(response.model_dump()), 404

        # Get job statistics for session
        session_jobs = job_repository.get_jobs_by_session(session_id)
        active_jobs = job_repository.get_active_jobs_by_session(session_id)

        # Create response model
        response_data = SessionInfoResponse(
            session_id=session_id,
            created_at=session_data.get("created_at"),
            jobs_submitted=len(session_jobs),
            max_concurrent_jobs=session_data.get("max_concurrent_jobs", 3),
            active_jobs=len(active_jobs),
        )

        logger.info(f"✅ Session info retrieved for: {session_id}")
        return jsonify(response_data.model_dump())

    except Exception as e:
        logger.error(f"💥 Failed to get session info: {e}")

        response = APIResponse(
            success=False,
            message="Failed to retrieve session information",
            error=str(e),
        )
        return jsonify(response.model_dump()), 500


def _calculate_progress_percentage(status: str) -> int:
    """
    Calculate progress percentage based on job status.

    Args:
        status: Job status

    Returns:
        int: Progress percentage (0-100)
    """
    status_progress = {
        "pending": 0,
        "running": 50,
        "completed": 100,
        "succeeded": 100,
        "failed": 0,
        "cancelled": 0,
    }

    return status_progress.get(status, 0)
