"""
C1 Image Upload API Routes.

Secure image upload endpoints with comprehensive security validation,
session isolation, and integration with F2 provider interface and F1 database.
"""

import logging
import time
from datetime import datetime
from typing import Any, Dict, Optional, Union

from flask import Blueprint, jsonify, request
from werkzeug.datastructures import FileStorage
from werkzeug.exceptions import RequestEntityTooLarge

from src.config.factory import ConfigurationFactory
from src.core.models import APIResponse
from src.database.connection import get_db
from src.database.models import ImageUploadRecord
from src.features.image_security import (
    ImageProcessingConfig,
    ImageSecurityPipeline,
    ImageUploadRequest,
    ImageValidationError,
)
from src.rate_limiting import rate_limiter
from src.realtime import websocket_manager
from src.session import session_manager

# Module-global pipeline instance for performance (following development standards)
_image_pipeline: Optional[ImageSecurityPipeline] = None


def get_image_pipeline() -> ImageSecurityPipeline:
    """Get module-global image processing pipeline."""
    global _image_pipeline
    if _image_pipeline is None:
        config = ImageProcessingConfig.from_environment()
        _image_pipeline = ImageSecurityPipeline(config)
    return _image_pipeline


image_upload_bp = Blueprint("image_upload", __name__, url_prefix="/api")
logger = logging.getLogger(__name__)


@image_upload_bp.before_request
def before_image_upload_request():
    """Security checks before processing image upload requests."""
    # Session validation
    session_id = session_manager.get_or_create_session_id()
    if not session_id:
        return jsonify(
            APIResponse(
                success=False,
                message="Session required",
                error="Invalid or missing session",
            ).model_dump()
        ), 401

    # Rate limiting
    if not rate_limiter.check_rate_limit(session_id, "image_upload"):
        return jsonify(
            APIResponse(
                success=False,
                message="Rate limit exceeded",
                error="Too many upload requests",
            ).model_dump()
        ), 429

    # Content-Type validation for upload endpoints
    if request.endpoint and "upload" in request.endpoint:
        if not request.content_type or not request.content_type.startswith(
            "multipart/form-data"
        ):
            return jsonify(
                APIResponse(
                    success=False,
                    message="Invalid content type",
                    error="Multipart form data required for image uploads",
                ).model_dump()
            ), 400


@image_upload_bp.route("/upload/image", methods=["POST"])
def upload_image() -> Union[tuple[Dict[str, Any], int], Dict[str, Any]]:
    """
    Upload and validate image with comprehensive security processing.

    Security Features:
    - Session-based isolation to prevent cross-user data exposure
    - Comprehensive input validation at API boundary
    - Secure error responses without information disclosure
    - Integration with monitoring infrastructure for security metrics

    Returns:
        JSON response with upload result and processing metadata
    """
    start_time = time.time()
    session_id = session_manager.get_or_create_session_id()

    try:
        logger.info(f"Image upload started for session: {session_id[:8]}")

        # 1. Validate request structure
        if "image" not in request.files:
            return jsonify(
                APIResponse(
                    success=False,
                    message="No image file provided",
                    error="Missing 'image' field in multipart form data",
                ).model_dump()
            ), 400

        image_file = request.files["image"]
        if not image_file or image_file.filename == "":
            return jsonify(
                APIResponse(
                    success=False,
                    message="No image file selected",
                    error="Empty file or missing filename",
                ).model_dump()
            ), 400

        # 2. Create upload request model for validation
        try:
            upload_request = _create_upload_request(image_file, request.form)
        except ValueError as e:
            return jsonify(
                APIResponse(
                    success=False, message="Invalid upload parameters", error=str(e)
                ).model_dump()
            ), 400

        # 3. Read image data with size validation
        try:
            image_data = image_file.read()
            if not image_data:
                return jsonify(
                    APIResponse(
                        success=False,
                        message="Empty image file",
                        error="No image data received",
                    ).model_dump()
                ), 400
        except RequestEntityTooLarge:
            return jsonify(
                APIResponse(
                    success=False,
                    message="File too large",
                    error="Image file exceeds size limit",
                ).model_dump()
            ), 413
        except Exception as e:
            logger.error(f"Error reading image data: {e}")
            return jsonify(
                APIResponse(
                    success=False,
                    message="File read error",
                    error="Could not read uploaded image",
                ).model_dump()
            ), 400

        # 4. Process image through security pipeline
        pipeline = get_image_pipeline()
        result = pipeline.process_uploaded_image(
            image_data=image_data,
            filename=upload_request.filename,
            session_id=session_id,
            upload_request=upload_request,
        )

        # 5. Handle processing result
        if not result.is_safe or not result.validation_passed:
            logger.warning(
                f"Image validation failed for session {session_id[:8]}: {result.error_message}"
            )

            # WebSocket notification for validation failure
            _send_websocket_notification(
                session_id,
                {
                    "type": "image_upload_failed",
                    "message": result.error_message,
                    "error_code": result.error_code,
                },
            )

            return jsonify(
                APIResponse(
                    success=False,
                    message="Image validation failed",
                    error=result.error_message,
                    data=result.to_api_response(),
                ).model_dump()
            ), 422

        # 6. Store to F1 database
        db_record = _store_image_record(session_id, result)

        # 7. Send WebSocket notification for success
        _send_websocket_notification(
            session_id,
            {
                "type": "image_upload_success",
                "message": "Image uploaded and validated successfully",
                "record_id": db_record.id if db_record else None,
                "processing_time_ms": result.total_processing_time_ms,
            },
        )

        # 8. Create success response
        processing_time = (time.time() - start_time) * 1000
        response_data = result.to_api_response()
        response_data.update(
            {
                "session_id": session_id,
                "total_request_time_ms": processing_time,
                "database_stored": db_record is not None,
            }
        )

        logger.info(
            f"Image upload completed successfully for session {session_id[:8]}: "
            f"{processing_time:.2f}ms total"
        )

        return jsonify(
            APIResponse(
                success=True,
                message="Image uploaded and validated successfully",
                data=response_data,
            ).model_dump()
        ), 200

    except ImageValidationError as e:
        logger.warning(
            f"Image validation error for session {session_id[:8]}: {e.message}"
        )

        _send_websocket_notification(
            session_id,
            {
                "type": "image_upload_failed",
                "message": e.message,
                "error_code": e.error_code,
            },
        )

        return jsonify(
            APIResponse(
                success=False, message="Image validation failed", error=e.message
            ).model_dump()
        ), 422

    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        logger.error(
            f"Unexpected error in image upload for session {session_id[:8]}: {e}"
        )

        _send_websocket_notification(
            session_id,
            {
                "type": "image_upload_error",
                "message": "Internal server error during image processing",
            },
        )

        return jsonify(
            APIResponse(
                success=False,
                message="Internal server error",
                error="An unexpected error occurred during image processing",
                data={"processing_time_ms": processing_time},
            ).model_dump()
        ), 500


@image_upload_bp.route("/upload/image/status", methods=["GET"])
def get_upload_status() -> Dict[str, Any]:
    """
    Get image upload status and statistics for current session.

    Returns:
        JSON response with upload statistics and pipeline health
    """
    session_id = session_manager.get_or_create_session_id()

    try:
        logger.info(f"Upload status requested for session: {session_id[:8]}")

        # Get pipeline statistics
        pipeline = get_image_pipeline()
        pipeline_stats = pipeline.get_processing_statistics()
        pipeline_health = pipeline.health_check()

        # Get session-specific statistics from database
        session_stats = _get_session_upload_stats(session_id)

        status_data = {
            "session_id": session_id,
            "pipeline_health": pipeline_health,
            "pipeline_statistics": pipeline_stats,
            "session_statistics": session_stats,
            "configuration": {
                "max_file_size_mb": pipeline.config.max_file_size_mb,
                "max_width": pipeline.config.max_width,
                "max_height": pipeline.config.max_height,
                "allowed_formats": [
                    "image/jpeg",
                    "image/png",
                    "image/webp",
                    "image/gif",
                ],
            },
        }

        return jsonify(
            APIResponse(
                success=True,
                message="Upload status retrieved successfully",
                data=status_data,
            ).model_dump()
        )

    except Exception as e:
        logger.error(f"Error getting upload status for session {session_id[:8]}: {e}")

        return jsonify(
            APIResponse(
                success=False,
                message="Error retrieving upload status",
                error="Could not get current upload status",
            ).model_dump()
        ), 500


@image_upload_bp.route("/upload/image/config", methods=["GET"])
def get_upload_config() -> Dict[str, Any]:
    """
    Get image upload configuration for client.

    Returns:
        JSON response with upload configuration and limits
    """
    try:
        pipeline = get_image_pipeline()
        config = pipeline.config

        client_config = {
            "upload_limits": {
                "max_file_size_mb": config.max_file_size_mb,
                "max_width": config.max_width,
                "max_height": config.max_height,
                "max_concurrent_uploads": config.max_concurrent_uploads,
            },
            "supported_formats": {
                "image/jpeg": [".jpg", ".jpeg"],
                "image/png": [".png"],
                "image/webp": [".webp"],
                "image/gif": [".gif"],
            },
            "processing_options": {
                "auto_resize": config.auto_resize,
                "jpeg_quality": config.jpeg_quality,
                "png_optimize": config.png_optimize,
            },
            "security_features": {
                "magic_number_check": config.enable_magic_number_check,
                "pil_validation": config.enable_pil_validation,
                "strict_mime_check": config.strict_mime_type_check,
            },
            "provider_support": {
                "default_provider": config.default_provider,
                "supported_providers": ["google_veo3", "azure_sora"],
            },
        }

        return jsonify(
            APIResponse(
                success=True,
                message="Upload configuration retrieved successfully",
                data=client_config,
            ).model_dump()
        )

    except Exception as e:
        logger.error(f"Error getting upload configuration: {e}")

        return jsonify(
            APIResponse(
                success=False,
                message="Error retrieving configuration",
                error="Could not get upload configuration",
            ).model_dump()
        ), 500


def _create_upload_request(
    image_file: FileStorage, form_data: Dict[str, Any]
) -> ImageUploadRequest:
    """
    Create and validate ImageUploadRequest from Flask request data.

    Args:
        image_file: Uploaded file from Flask request
        form_data: Form data from request

    Returns:
        ImageUploadRequest: Validated upload request

    Raises:
        ValueError: If validation fails
    """
    try:
        # Extract form parameters with defaults
        upload_request = ImageUploadRequest(
            filename=image_file.filename or "unknown.jpg",
            content_type=image_file.content_type or "image/jpeg",
            max_width=int(form_data.get("max_width", 1920)),
            max_height=int(form_data.get("max_height", 1080)),
            quality=int(form_data.get("quality", 85)),
            strict_validation=form_data.get("strict_validation", "true").lower()
            == "true",
            scan_for_malware=form_data.get("scan_for_malware", "true").lower()
            == "true",
            target_provider=form_data.get("target_provider", "google_veo3"),
            provider_format=form_data.get("provider_format", "base64"),
        )

        return upload_request

    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid upload parameters: {e}")


def _store_image_record(session_id: str, result) -> Optional[ImageUploadRecord]:
    """
    Store image upload record in F1 database.

    Args:
        session_id: User session ID
        result: ImageSecurityResult from pipeline

    Returns:
        ImageUploadRecord: Stored database record or None if failed
    """
    try:
        # Create database record
        db = get_db()

        record_data = result.to_database_record(session_id)

        # Add additional fields for database storage
        record_data.update(
            {
                "id": None,  # Auto-generated
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
        )

        # Create and save record
        record = ImageUploadRecord(**record_data)
        db.add(record)
        db.commit()

        logger.info(f"Image record stored in database: {record.id}")
        return record

    except Exception as e:
        logger.error(f"Error storing image record: {e}")
        return None


def _send_websocket_notification(session_id: str, data: Dict[str, Any]) -> None:
    """
    Send WebSocket notification for real-time updates.

    Args:
        session_id: User session ID
        data: Notification data
    """
    try:
        notification = {
            "timestamp": datetime.utcnow().isoformat(),
            "session_id": session_id,
            **data,
        }

        # Use threading for async websocket operations in Flask
        import threading

        def send_async():
            import asyncio

            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    websocket_manager.send_to_session(session_id, notification)
                )
            except Exception as e:
                logger.error(f"Error in async WebSocket send: {e}")
            finally:
                loop.close()

        thread = threading.Thread(target=send_async, daemon=True)
        thread.start()
        logger.debug(f"WebSocket notification sent to session {session_id[:8]}")

    except Exception as e:
        logger.error(f"Error sending WebSocket notification: {e}")


def _get_session_upload_stats(session_id: str) -> Dict[str, Any]:
    """
    Get upload statistics for specific session from F1 database.

    Args:
        session_id: User session ID

    Returns:
        Dict containing session upload statistics
    """
    try:
        db = get_db()

        # Query session statistics
        total_uploads = (
            db.query(ImageUploadRecord)
            .filter(ImageUploadRecord.session_id == session_id)
            .count()
        )

        successful_uploads = (
            db.query(ImageUploadRecord)
            .filter(
                ImageUploadRecord.session_id == session_id,
                ImageUploadRecord.validation_passed == True,
            )
            .count()
        )

        recent_uploads = (
            db.query(ImageUploadRecord)
            .filter(
                ImageUploadRecord.session_id == session_id,
                ImageUploadRecord.created_at
                >= datetime.utcnow().replace(hour=0, minute=0, second=0),
            )
            .count()
        )

        return {
            "total_uploads": total_uploads,
            "successful_uploads": successful_uploads,
            "failed_uploads": total_uploads - successful_uploads,
            "success_rate": successful_uploads / total_uploads
            if total_uploads > 0
            else 0.0,
            "recent_uploads_today": recent_uploads,
        }

    except Exception as e:
        logger.error(f"Error getting session upload stats: {e}")
        return {
            "total_uploads": 0,
            "successful_uploads": 0,
            "failed_uploads": 0,
            "success_rate": 0.0,
            "recent_uploads_today": 0,
        }


@image_upload_bp.errorhandler(413)
def handle_file_too_large(e):
    """Handle file too large error."""
    session_id = session_manager.get_or_create_session_id()
    logger.warning(f"File too large error for session {session_id[:8]}")

    return jsonify(
        APIResponse(
            success=False,
            message="File too large",
            error="Uploaded file exceeds the maximum allowed size",
        ).model_dump()
    ), 413


@image_upload_bp.errorhandler(400)
def handle_bad_request(e):
    """Handle bad request errors."""
    session_id = session_manager.get_or_create_session_id()
    logger.warning(f"Bad request error for session {session_id[:8]}: {e}")

    return jsonify(
        APIResponse(
            success=False,
            message="Bad request",
            error="Invalid request format or parameters",
        ).model_dump()
    ), 400


# Health check endpoint for monitoring
@image_upload_bp.route("/upload/health", methods=["GET"])
def upload_health_check() -> Dict[str, Any]:
    """
    Health check endpoint for image upload system.

    Returns:
        JSON response with system health status
    """
    try:
        # Check pipeline health
        pipeline = get_image_pipeline()
        pipeline_health = pipeline.health_check()

        # Check database connectivity
        try:
            db = get_db()
            db.execute("SELECT 1")
            database_healthy = True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            database_healthy = False

        # Check WebSocket manager
        websocket_healthy = (
            websocket_manager.is_healthy()
            if hasattr(websocket_manager, "is_healthy")
            else True
        )

        # Check image processing dependencies
        dependency_health = ConfigurationFactory.get_image_dependency_health()
        dependencies_healthy = dependency_health.get("healthy", False)

        # Overall health status
        overall_healthy = (
            pipeline_health["pipeline_healthy"]
            and database_healthy
            and websocket_healthy
            and dependencies_healthy
        )

        health_data = {
            "healthy": overall_healthy,
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "image_pipeline": pipeline_health,
                "database": {"healthy": database_healthy},
                "websocket": {"healthy": websocket_healthy},
                "dependencies": dependency_health,
            },
            "version": "1.0.0",
        }

        status_code = 200 if overall_healthy else 503

        return jsonify(health_data), status_code

    except Exception as e:
        logger.error(f"Health check error: {e}")

        return jsonify(
            {
                "healthy": False,
                "timestamp": datetime.utcnow().isoformat(),
                "error": "Health check failed",
                "version": "1.0.0",
            }
        ), 503


@image_upload_bp.route("/upload/dependencies", methods=["GET"])
def get_dependency_status() -> Dict[str, Any]:
    """
    Get detailed dependency validation status.

    Returns:
        JSON response with comprehensive dependency information
    """
    try:
        logger.info("Dependency status requested")

        # Get full dependency validation
        dependency_validation = ConfigurationFactory.validate_image_dependencies()

        # Get current security configuration
        security_config = ConfigurationFactory.create_environment_image_config()
        config_summary = {
            "environment": security_config.environment,
            "security_policy": security_config.get_security_policy(),
            "pil_security": security_config.get_pil_security_config(),
        }

        response_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "dependency_validation": dependency_validation,
            "security_configuration": config_summary,
            "environment_examples": {
                "development": "/src/config/examples/image_security_development.env",
                "staging": "/src/config/examples/image_security_staging.env",
                "production": "/src/config/examples/image_security_production.env",
            },
        }

        # Determine status code based on dependency health
        overall_status = dependency_validation.get("overall_status", "error")
        status_code = (
            200
            if overall_status == "ok"
            else (503 if overall_status == "error" else 200)
        )

        return jsonify(
            APIResponse(
                success=overall_status in ["ok", "warning"],
                message=f"Dependency status: {overall_status}",
                data=response_data,
            ).model_dump()
        ), status_code

    except Exception as e:
        logger.error(f"Error getting dependency status: {e}")

        return jsonify(
            APIResponse(
                success=False,
                message="Error retrieving dependency status",
                error=str(e),
            ).model_dump()
        ), 500
