"""Main API routes module - Blueprint registration."""

import logging
import os

from flask import Blueprint

from src.api.file_routes import file_bp
from src.api.health_routes import health_bp
from src.api.job_routes import job_bp
from src.api.provider_routes import provider_bp
from src.api.video_routes import video_bp

logger = logging.getLogger(__name__)

# Main API blueprint
api_bp = Blueprint("api", __name__)


def register_api_blueprints(app):
    """
    Register all API blueprints with the Flask application.

    Args:
        app: Flask application instance
    """
    logger.info("🔧 Registering API blueprints")

    # Register core API blueprints at root level (no prefix)
    # This maintains backward compatibility with existing tests and URLs
    app.register_blueprint(video_bp)
    app.register_blueprint(health_bp)
    app.register_blueprint(job_bp)
    app.register_blueprint(file_bp)
    app.register_blueprint(provider_bp)  # C3 Provider-aware API endpoints

    # ✅ SECURITY: Conditionally register debug blueprint based on environment
    # Only register in development/testing environments
    flask_env = os.getenv("FLASK_ENV", "production").lower()
    is_development = flask_env in ("development", "dev", "testing", "test")

    if is_development:
        # Import debug blueprint only when needed to avoid import in production
        from src.api.debug_routes import debug_bp

        app.register_blueprint(debug_bp)
        logger.warning("🚨 DEBUG: Debug blueprint registered (development mode)")
        logger.warning("🔧 DEBUG ENDPOINTS: /debug/azure-config available")
    else:
        logger.info("✅ SECURITY: Debug blueprint DISABLED in production mode")
        logger.info(f"✅ ENVIRONMENT: {flask_env} - Debug endpoints are NOT accessible")

    logger.info("✅ API blueprints registered successfully")


# Legacy compatibility - keep api_bp for existing imports
# This ensures backward compatibility with existing code that imports api_bp
__all__ = ["api_bp", "register_api_blueprints"]
