"""File serving API routes."""

import logging
import os
from typing import Union

from flask import Blueprint, Response, current_app, jsonify, request, send_file
from werkzeug.utils import secure_filename

from src.api.job_repository import JobRepository
from src.core.models import APIResponse

file_bp = Blueprint("file", __name__)
logger = logging.getLogger(__name__)

# Initialize components
job_repository = JobRepository()


def _validate_file_path_security(file_path: str, allowed_dir: str) -> bool:
    """
    Validate file path is within allowed directory to prevent path traversal attacks.

    Args:
        file_path: The file path to validate
        allowed_dir: The allowed base directory

    Returns:
        bool: True if path is safe, False otherwise
    """
    try:
        # Get absolute paths to prevent traversal attacks
        real_file = os.path.realpath(file_path)
        real_allowed = os.path.realpath(allowed_dir)

        # Check if file path starts with allowed directory
        return real_file.startswith(real_allowed + os.sep) or real_file == real_allowed
    except (<PERSON>Error, ValueError):
        # Return False for any path resolution errors
        return False


def _stream_video_with_range_support(video_path: str) -> Response:
    """
    Stream video file with HTTP Range request support for browser compatibility.
    
    Args:
        video_path: Path to the video file
        
    Returns:
        Response: Flask response with appropriate headers and content
    """
    # Get file size
    file_size = os.path.getsize(video_path)
    
    # Check for Range header
    range_header = request.headers.get('Range')
    
    if range_header:
        # Parse Range header (format: "bytes=start-end")
        try:
            byte_start = 0
            byte_end = file_size - 1
            
            # Extract range values
            range_match = range_header.replace('bytes=', '').split('-')
            if len(range_match) == 2:
                if range_match[0]:
                    byte_start = int(range_match[0])
                if range_match[1]:
                    byte_end = int(range_match[1])
            
            # Validate range
            byte_start = max(0, byte_start)
            byte_end = min(file_size - 1, byte_end)
            content_length = byte_end - byte_start + 1
            
            # Read partial content
            with open(video_path, 'rb') as video_file:
                video_file.seek(byte_start)
                chunk = video_file.read(content_length)
            
            # Create partial content response
            response = Response(
                chunk,
                status=206,  # Partial Content
                mimetype='video/mp4',
                headers={
                    'Content-Range': f'bytes {byte_start}-{byte_end}/{file_size}',
                    'Accept-Ranges': 'bytes',
                    'Content-Length': str(content_length),
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            )
            
            logger.info(f"📺 Serving partial content: bytes {byte_start}-{byte_end}/{file_size}")
            return response
            
        except (ValueError, IndexError) as e:
            logger.warning(f"🚨 Invalid Range header: {range_header}, error: {e}")
            # Fall through to serve full file
    
    # Serve full file if no Range header or invalid Range
    logger.info(f"📺 Serving full video file: {file_size} bytes")
    response = Response(
        headers={
            'Content-Type': 'video/mp4',
            'Accept-Ranges': 'bytes',
            'Content-Length': str(file_size),
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    )
    
    # Stream the full file
    def generate():
        with open(video_path, 'rb') as video_file:
            while True:
                chunk = video_file.read(8192)  # 8KB chunks
                if not chunk:
                    break
                yield chunk
    
    response.response = generate()
    return response


@file_bp.route("/video/<job_id>")
def serve_video(job_id: str) -> Union[Response, tuple[Response, int]]:
    """
    Stream video file for completed job with HTTP Range support.

    Args:
        job_id: Unique job identifier

    Returns:
        Response: Video file stream or error response
    """
    try:
        logger.info(f"🎥 Video stream requested for job: {job_id}")

        # Get job from database
        job = job_repository.get_job_by_id(job_id)
        if not job:
            response = APIResponse(
                success=False,
                message="Job not found",
                error=f"No job found with ID: {job_id}"
            )
            return jsonify(response.model_dump()), 404

        # Check if job is completed
        if job.status != "completed" and job.status != "succeeded":
            response = APIResponse(
                success=False,
                message="Video not ready",
                error=f"Job status is '{job.status}', expected 'completed'"
            )
            return jsonify(response.model_dump()), 400

        # Get video file path
        video_path = getattr(job, 'file_path', None)
        if not video_path:
            response = APIResponse(
                success=False,
                message="Video file not found",
                error="No file path associated with this job"
            )
            return jsonify(response.model_dump()), 404

        # Resolve file path to absolute path
        upload_folder = current_app.config.get("UPLOAD_FOLDER", "uploads")
        
        # Handle both absolute and relative paths
        if os.path.isabs(video_path):
            # Absolute path - use as is
            resolved_path = video_path
        else:
            # Relative path - resolve relative to current working directory
            resolved_path = os.path.abspath(video_path)
        
        # Validate file path security
        if not _validate_file_path_security(resolved_path, upload_folder):
            logger.warning(f"🚨 Path traversal attempt detected: {video_path}")
            response = APIResponse(
                success=False,
                message="Access denied",
                error="Invalid file path"
            )
            return jsonify(response.model_dump()), 403
        
        # Update video_path to resolved path for file existence check
        video_path = resolved_path

        # Check if file exists
        if not os.path.exists(video_path):
            response = APIResponse(
                success=False,
                message="Video file not found",
                error="Video file does not exist on disk"
            )
            return jsonify(response.model_dump()), 404

        # Stream the video file with Flask's native send_file (temporarily bypass custom Range)
        logger.info(f"✅ Streaming video file: {video_path}")
        return send_file(
            video_path,
            as_attachment=False,
            mimetype="video/mp4"
        )

    except FileNotFoundError:
        logger.error(f"💥 Video file not found for job: {job_id}")
        response = APIResponse(
            success=False,
            message="Video file not found",
            error="The video file could not be located"
        )
        return jsonify(response.model_dump()), 404
    except PermissionError:
        logger.error(f"💥 Permission denied accessing video for job: {job_id}")
        response = APIResponse(
            success=False,
            message="Access denied",
            error="Permission denied to access video file"
        )
        return jsonify(response.model_dump()), 403
    except OSError as e:
        logger.error(f"💥 OS error serving video for job {job_id}: {e}")
        response = APIResponse(
            success=False,
            message="File system error",
            error="Could not access video file"
        )
        return jsonify(response.model_dump()), 500
    except Exception as e:
        logger.error(f"💥 Unexpected error serving video for job {job_id}: {e}")
        response = APIResponse(
            success=False,
            message="Internal server error",
            error="An unexpected error occurred"
        )
        return jsonify(response.model_dump()), 500


@file_bp.route("/download/<job_id>")
def download_video(job_id: str) -> Union[Response, tuple[Response, int]]:
    """
    Download video file for completed job.

    Args:
        job_id: Unique job identifier

    Returns:
        Response: Video file download or error response
    """
    try:
        logger.info(f"📥 Video download requested for job: {job_id}")

        # Get job from database
        job = job_repository.get_job_by_id(job_id)
        if not job:
            response = APIResponse(
                success=False,
                message="Job not found",
                error=f"No job found with ID: {job_id}"
            )
            return jsonify(response.model_dump()), 404

        # Check if job is completed
        if job.status != "completed" and job.status != "succeeded":
            response = APIResponse(
                success=False,
                message="Video not ready",
                error=f"Job status is '{job.status}', expected 'completed'"
            )
            return jsonify(response.model_dump()), 400

        # Get video file path
        video_path = getattr(job, 'file_path', None)
        if not video_path:
            response = APIResponse(
                success=False,
                message="Video file not found",
                error="No file path associated with this job"
            )
            return jsonify(response.model_dump()), 404

        # Resolve file path to absolute path
        upload_folder = current_app.config.get("UPLOAD_FOLDER", "uploads")
        
        # Handle both absolute and relative paths
        if os.path.isabs(video_path):
            # Absolute path - use as is
            resolved_path = video_path
        else:
            # Relative path - resolve relative to current working directory
            resolved_path = os.path.abspath(video_path)
        
        # Validate file path security
        if not _validate_file_path_security(resolved_path, upload_folder):
            logger.warning(f"🚨 Path traversal attempt detected: {video_path}")
            response = APIResponse(
                success=False,
                message="Access denied",
                error="Invalid file path"
            )
            return jsonify(response.model_dump()), 403
        
        # Update video_path to resolved path for file existence check
        video_path = resolved_path

        # Check if file exists
        if not os.path.exists(video_path):
            response = APIResponse(
                success=False,
                message="Video file not found",
                error="Video file does not exist on disk"
            )
            return jsonify(response.model_dump()), 404

        # Generate secure filename for download
        safe_filename = secure_filename(f"video_{job_id}.mp4")

        # Download the video file
        logger.info(f"✅ Downloading video file: {video_path}")
        return send_file(
            video_path,
            as_attachment=True,
            download_name=safe_filename,
            mimetype="video/mp4"
        )

    except FileNotFoundError:
        logger.error(f"💥 Video file not found for job: {job_id}")
        response = APIResponse(
            success=False,
            message="Video file not found",
            error="The video file could not be located"
        )
        return jsonify(response.model_dump()), 404
    except PermissionError:
        logger.error(f"💥 Permission denied accessing video for job: {job_id}")
        response = APIResponse(
            success=False,
            message="Access denied",
            error="Permission denied to access video file"
        )
        return jsonify(response.model_dump()), 403
    except OSError as e:
        logger.error(f"💥 OS error downloading video for job {job_id}: {e}")
        response = APIResponse(
            success=False,
            message="File system error",
            error="Could not access video file"
        )
        return jsonify(response.model_dump()), 500
    except Exception as e:
        logger.error(f"💥 Unexpected error downloading video for job {job_id}: {e}")
        response = APIResponse(
            success=False,
            message="Internal server error",
            error="An unexpected error occurred"
        )
        return jsonify(response.model_dump()), 500
