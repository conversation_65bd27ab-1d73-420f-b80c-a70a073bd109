"""
Pydantic models for API request and response validation.

This module provides type-safe data models for all API endpoints,
ensuring proper validation and serialization of input/output data.
"""

from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class VideoGenerationRequest(BaseModel):
    """Request model for video generation endpoint."""

    prompt: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="Text prompt for video generation",
    )
    provider: Optional[str] = Field(
        "azure_sora",
        pattern="^(azure_sora|google_veo3)$",
        description="AI provider to use for video generation",
    )
    duration: Optional[int] = Field(
        None, ge=1, le=60, description="Video duration in seconds"
    )
    width: Optional[int] = Field(
        None, ge=480, le=3840, description="Video width in pixels"
    )
    height: Optional[int] = Field(
        None, ge=480, le=2160, description="Video height in pixels"
    )
    model: Optional[str] = Field(
        None, pattern="^(sora-v1|sora-v2)$", description="Model version to use"
    )

    class Config:
        """Pydantic configuration."""

        json_schema_extra = {
            "example": {
                "prompt": "A beautiful sunset over mountains",
                "provider": "azure_sora",
                "duration": 10,
                "width": 1920,
                "height": 1080,
                "model": "sora-v1",
            }
        }


class VideoGenerationResponse(BaseModel):
    """Response model for video generation endpoint."""

    success: bool = Field(..., description="Whether the request was successful")
    job_id: str = Field(
        ..., description="Unique identifier for the video generation job"
    )
    status: str = Field(..., description="Current status of the job")
    message: str = Field(..., description="Human-readable status message")
    queue_position: Optional[int] = Field(
        None, description="Position in queue (if applicable)"
    )
    estimated_wait_minutes: Optional[int] = Field(
        None, description="Estimated wait time in minutes"
    )

    class Config:
        """Pydantic configuration."""

        json_schema_extra = {
            "example": {
                "success": True,
                "job_id": "job-123e4567-e89b-12d3-a456-426614174000",
                "status": "pending",
                "message": "Job submitted successfully",
                "queue_position": 3,
                "estimated_wait_minutes": 5,
            }
        }


class HealthCheckResponse(BaseModel):
    """Response model for health check endpoints."""

    overall_status: str = Field(..., description="Overall system health status")
    components: Dict[str, Any] = Field(
        ..., description="Status of individual components"
    )
    timestamp: str = Field(..., description="Timestamp of health check")
    version: str = Field(..., description="Application version")

    class Config:
        """Pydantic configuration."""

        json_schema_extra = {
            "example": {
                "overall_status": "healthy",
                "components": {
                    "database": {"status": "healthy", "response_time_ms": 12},
                    "azure_api": {"status": "healthy", "response_time_ms": 245},
                    "redis": {"status": "healthy", "response_time_ms": 3},
                },
                "timestamp": "2025-01-09T10:30:00Z",
                "version": "1.0.0",
            }
        }


class JobStatusResponse(BaseModel):
    """Response model for job status endpoints."""

    job_id: str = Field(..., description="Unique identifier for the job")
    status: str = Field(..., description="Current job status")
    video_url: Optional[str] = Field(None, description="URL to stream completed video")
    download_url: Optional[str] = Field(
        None, description="URL to download completed video"
    )
    error_message: Optional[str] = Field(
        None, description="Error message if job failed"
    )
    created_at: datetime = Field(..., description="Job creation timestamp")
    completed_at: Optional[datetime] = Field(
        None, description="Job completion timestamp"
    )
    progress_percentage: Optional[int] = Field(
        None, ge=0, le=100, description="Job progress percentage"
    )

    class Config:
        """Pydantic configuration."""

        json_encoders = {datetime: lambda v: v.isoformat()}
        json_schema_extra = {
            "example": {
                "job_id": "job-123e4567-e89b-12d3-a456-426614174000",
                "status": "succeeded",
                "video_url": "/video/job-123e4567-e89b-12d3-a456-426614174000",
                "download_url": "/download/job-123e4567-e89b-12d3-a456-426614174000",
                "error_message": None,
                "created_at": "2025-01-09T10:25:00Z",
                "completed_at": "2025-01-09T10:30:00Z",
                "progress_percentage": 100,
            }
        }


class QueueStatusResponse(BaseModel):
    """Response model for queue status endpoints."""

    total_jobs: int = Field(..., ge=0, description="Total number of jobs in queue")
    position: int = Field(..., ge=0, description="Current position in queue")
    estimated_wait_minutes: int = Field(
        ..., ge=0, description="Estimated wait time in minutes"
    )
    session_jobs: int = Field(
        ..., ge=0, description="Number of jobs for current session"
    )
    can_submit_more: bool = Field(..., description="Whether user can submit more jobs")

    class Config:
        """Pydantic configuration."""

        json_schema_extra = {
            "example": {
                "total_jobs": 15,
                "position": 3,
                "estimated_wait_minutes": 8,
                "session_jobs": 2,
                "can_submit_more": True,
            }
        }


class SessionInfoResponse(BaseModel):
    """Response model for session information endpoints."""

    session_id: str = Field(..., description="Current session identifier")
    created_at: datetime = Field(..., description="Session creation timestamp")
    jobs_submitted: int = Field(
        ..., ge=0, description="Number of jobs submitted in session"
    )
    max_concurrent_jobs: int = Field(
        ..., ge=1, description="Maximum concurrent jobs allowed"
    )
    active_jobs: int = Field(..., ge=0, description="Number of currently active jobs")

    class Config:
        """Pydantic configuration."""

        json_encoders = {datetime: lambda v: v.isoformat()}
        json_schema_extra = {
            "example": {
                "session_id": "sess_abcd1234567890",
                "created_at": "2025-01-09T10:00:00Z",
                "jobs_submitted": 5,
                "max_concurrent_jobs": 3,
                "active_jobs": 2,
            }
        }


class ErrorResponse(BaseModel):
    """Response model for error cases."""

    success: bool = Field(False, description="Always false for error responses")
    error_code: str = Field(..., description="Machine-readable error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional error details"
    )
    timestamp: str = Field(..., description="Error timestamp")

    class Config:
        """Pydantic configuration."""

        json_schema_extra = {
            "example": {
                "success": False,
                "error_code": "VALIDATION_ERROR",
                "error_message": "Invalid prompt: exceeds maximum length",
                "details": {"field": "prompt", "max_length": 500},
                "timestamp": "2025-01-09T10:30:00Z",
            }
        }


class MetricsResponse(BaseModel):
    """Response model for metrics endpoints."""

    system_metrics: Dict[str, Any] = Field(..., description="System-level metrics")
    job_metrics: Dict[str, Any] = Field(..., description="Job-related metrics")
    performance_metrics: Dict[str, Any] = Field(..., description="Performance metrics")
    timestamp: str = Field(..., description="Metrics collection timestamp")

    class Config:
        """Pydantic configuration."""

        json_schema_extra = {
            "example": {
                "system_metrics": {
                    "cpu_usage": 45.2,
                    "memory_usage": 67.8,
                    "disk_usage": 23.1,
                },
                "job_metrics": {
                    "total_jobs": 1247,
                    "completed_jobs": 1189,
                    "failed_jobs": 58,
                    "success_rate": 95.3,
                },
                "performance_metrics": {
                    "avg_processing_time": 127.5,
                    "queue_wait_time": 45.2,
                    "api_response_time": 234.7,
                },
                "timestamp": "2025-01-09T10:30:00Z",
            }
        }


# Type aliases for commonly used types
JobId = str
SessionId = str
StatusType = str
