"""
Tests for C2 Provider Selection UI backward compatibility.

Ensures that existing functionality continues to work unchanged after
implementing the provider selection UI features.
"""

import json
from unittest.mock import MagicMock, patch

import pytest

from src.api.video_routes import video_bp


class TestBackwardCompatibility:
    """Test backward compatibility with existing video generation functionality."""

    def test_existing_config_endpoint_unchanged(self, client):
        """Test that existing /config endpoint functionality is preserved."""
        with patch("src.api.video_routes.ConfigurationFactory") as mock_factory:
            # Mock the configuration factory response
            mock_config = MagicMock()
            mock_config.get_defaults_dict.return_value = {
                "width": 1280,
                "height": 720,
                "duration": 5,
                "model": "sora",
            }
            mock_config.get_constraints_dict.return_value = {
                "min_width": 480,
                "max_width": 1920,
                "min_height": 480,
                "max_height": 1080,
                "min_duration": 1,
                "max_duration": 20,
                "max_prompt_length": 500,
            }
            mock_factory.get_video_config.return_value = mock_config

            response = client.get("/config")

            assert response.status_code == 200
            data = json.loads(response.data)

            assert data["success"] is True
            assert "defaults" in data["data"]
            assert "constraints" in data["data"]
            assert data["data"]["defaults"]["width"] == 1280
            assert data["data"]["constraints"]["max_duration"] == 20

    def test_existing_generate_endpoint_without_provider(self, client):
        """Test that /generate endpoint works without provider parameter (backward compatibility)."""
        form_data = {
            "prompt": "A beautiful sunset over mountains",
            "duration": "10",
            "width": "1280",
            "height": "720",
        }

        with patch(
            "src.api.video_routes._get_queue_manager"
        ) as mock_queue_getter, patch(
            "src.api.video_routes.job_repository"
        ) as mock_repo, patch(
            "src.api.video_routes.process_video_generation"
        ) as mock_task, patch("src.api.video_routes.g") as mock_g:
            # Mock queue manager
            mock_queue = MagicMock()
            mock_queue.assign_queue_position.return_value = 1
            mock_queue.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }
            mock_queue_getter.return_value = mock_queue

            # Mock repository save
            mock_repo.create_job = MagicMock(
                return_value=MagicMock(id="test-job-123", status="pending")
            )

            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "celery-task-123"
            mock_task.delay.return_value = mock_result

            # Mock session in Flask g object
            mock_g.session_id = "test-session-123"

            # Mock session
            with client.session_transaction() as sess:
                sess["session_id"] = "test-session-123"

            response = client.post("/generate", data=form_data)

            # Should work without provider parameter (defaults to existing behavior)
            assert response.status_code in [200, 201]

    def test_existing_form_parameters_still_processed(self, client):
        """Test that existing form parameters (duration, width, height, model) are still processed."""
        form_data = {
            "prompt": "Test video generation",
            "duration": "15",
            "width": "1920",
            "height": "1080",
            "model": "sora-v1",  # Use valid model value
        }

        with patch(
            "src.api.video_routes._get_queue_manager"
        ) as mock_queue_getter, patch(
            "src.api.video_routes.job_repository"
        ) as mock_repo, patch(
            "src.api.video_routes.process_video_generation"
        ) as mock_task, patch(
            "src.api.video_routes.GenerationParamsFactory"
        ) as mock_params_factory, patch("src.api.video_routes.g") as mock_g:
            # Mock queue manager
            mock_queue = MagicMock()
            mock_queue.assign_queue_position.return_value = 1
            mock_queue.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }
            mock_queue_getter.return_value = mock_queue

            # Mock repository save
            mock_repo.create_job = MagicMock(
                return_value=MagicMock(id="test-job-123", status="pending")
            )

            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "celery-task-123"
            mock_task.delay.return_value = mock_result

            # Mock params factory
            mock_params = MagicMock()
            mock_params_factory.create_from_ui_request.return_value = mock_params

            # Mock session in Flask g object
            mock_g.session_id = "test-session-123"

            # Mock session
            with client.session_transaction() as sess:
                sess["session_id"] = "test-session-123"

            response = client.post("/generate", data=form_data)

            assert response.status_code in [200, 201]

            # Verify that the GenerationParamsFactory was called with the parameters
            mock_params_factory.create_from_ui_request.assert_called()

    def test_html_template_maintains_existing_elements(self):
        """Test that HTML template still contains existing form elements."""
        from flask import Flask

        app = Flask(__name__)
        app.register_blueprint(video_bp)

        with app.test_request_context():
            # Read the actual template file
            with open("templates/index.html") as f:
                template_content = f.read()

            # Check that existing elements are still present
            assert 'id="prompt"' in template_content
            assert 'id="duration"' in template_content
            assert 'id="generateBtn"' in template_content
            assert 'id="videoContainer"' in template_content
            assert 'id="generatedVideo"' in template_content
            assert "preset-btn" in template_content  # Quick presets
            assert "advancedParams" in template_content  # Advanced parameters

            # Verify new elements are added but don't break existing structure
            assert "providerToggle" in template_content
            assert "imageUploadSection" in template_content

    def test_javascript_maintains_existing_methods(self):
        """Test that JavaScript VideoGenerator class maintains existing methods."""
        import os

        # Read the JavaScript file
        js_file_path = "static/js/app.js"
        assert os.path.exists(js_file_path), "JavaScript file should exist"

        with open(js_file_path) as f:
            js_content = f.read()

        # Check that existing methods are still present
        existing_methods = [
            "initializeElements",
            "bindEvents",
            "handleSubmit",
            "submitPrompt",
            "startPolling",
            "checkJobStatus",
            "stopPolling",
            "showVideo",
            "downloadVideo",
            "resetForm",
            "loadUIConfig",
            "updateUIWithConfig",
            "updateCharCount",
            "updateDurationDisplay",
            "handleResolutionChange",
            "validateDimensions",
            "applyPreset",
            "getCurrentParameters",
            "getDefaultValue",
        ]

        for method in existing_methods:
            assert f"{method}(" in js_content, f"Method {method} should still exist"

        # Check that VideoGenerator class is still properly instantiated
        assert "new VideoGenerator()" in js_content

    def test_existing_css_classes_preserved(self):
        """Test that existing CSS classes and Bootstrap elements are preserved."""
        with open("templates/index.html") as f:
            template_content = f.read()

        # Check that existing Bootstrap classes are still present
        existing_classes = [
            "form-control",
            "btn btn-primary",
            "card shadow",
            "accordion",
            "alert alert-info",
            "alert alert-danger",
            "spinner-border",
            "d-none",
            "fade-in",
        ]

        for css_class in existing_classes:
            assert css_class in template_content, (
                f"CSS class {css_class} should be preserved"
            )

    def test_existing_api_response_format_unchanged(self, client):
        """Test that API response formats remain unchanged for backward compatibility."""
        # Test /config endpoint response format
        with patch("src.api.video_routes.ConfigurationFactory") as mock_factory:
            mock_config = MagicMock()
            mock_config.get_defaults_dict.return_value = {"test": "value"}
            mock_config.get_constraints_dict.return_value = {"test": "constraint"}
            mock_factory.get_video_config.return_value = mock_config

            response = client.get("/config")
            data = json.loads(response.data)

            # Verify response structure hasn't changed
            assert "success" in data
            assert "message" in data
            assert "data" in data
            assert isinstance(data["data"], dict)
            assert "defaults" in data["data"]
            assert "constraints" in data["data"]

    def test_form_validation_backward_compatible(self):
        """Test that form validation rules remain backward compatible."""

        with open("static/js/app.js") as f:
            js_content = f.read()

        # Check that existing validation logic is preserved
        validation_checks = [
            "prompt.length > 500",  # Character limit validation
            "updateCharCount",  # Character count updates
            "validateDimensions",  # Dimension validation
            "is-invalid",  # Bootstrap validation classes
        ]

        for validation in validation_checks:
            assert validation in js_content, (
                f"Validation logic {validation} should be preserved"
            )


class TestProviderSelectionIntegration:
    """Test that provider selection integrates smoothly with existing functionality."""

    def test_provider_selection_enhances_existing_workflow(self, client):
        """Test that provider selection enhances rather than disrupts existing workflow."""
        # Test with provider selection
        form_data_with_provider = {
            "prompt": "Enhanced workflow test",
            "provider": "azure_sora",
            "duration": "10",
        }

        # Test without provider selection (backward compatibility)
        form_data_without_provider = {
            "prompt": "Legacy workflow test",
            "duration": "10",
        }

        with patch(
            "src.api.video_routes._get_queue_manager"
        ) as mock_queue_getter, patch(
            "src.api.video_routes.job_repository"
        ) as mock_repo, patch(
            "src.api.video_routes.process_video_generation"
        ) as mock_task, patch("src.api.video_routes.g") as mock_g:
            # Mock setup
            mock_queue = MagicMock()
            mock_queue.assign_queue_position.return_value = 1
            mock_queue.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }
            mock_queue_getter.return_value = mock_queue
            mock_repo.create_job = MagicMock(
                return_value=MagicMock(id="test-job-123", status="pending")
            )
            mock_result = MagicMock()
            mock_result.id = "celery-task-123"
            mock_task.delay.return_value = mock_result

            # Mock session in Flask g object
            mock_g.session_id = "test-session-123"

            # Test both workflows
            with client.session_transaction() as sess:
                sess["session_id"] = "test-session-123"

            response1 = client.post("/generate", data=form_data_with_provider)
            response2 = client.post("/generate", data=form_data_without_provider)

            # Both should work
            assert response1.status_code in [200, 201]
            assert response2.status_code in [200, 201]

    def test_new_features_dont_break_existing_ui(self):
        """Test that new provider selection features don't break existing UI functionality."""
        with open("templates/index.html") as f:
            template_content = f.read()

        # Ensure existing form structure is intact
        assert 'form id="videoForm"' in template_content
        assert 'textarea id="prompt"' in template_content
        assert 'button type="submit" id="generateBtn"' in template_content

        # Ensure accordion for advanced parameters still exists
        assert "accordion" in template_content
        assert "advancedParams" in template_content

        # Ensure preset buttons still exist
        assert "preset-btn" in template_content
        assert "data-preset" in template_content


@pytest.fixture
def client():
    """Create test client for backward compatibility testing."""
    from flask import Flask

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.secret_key = "test-secret-key"

    # Set template folder path
    import os

    template_dir = os.path.abspath("templates")
    app.template_folder = template_dir

    # Register the video blueprint
    app.register_blueprint(video_bp)

    with app.test_client() as client:
        with app.app_context():
            yield client
