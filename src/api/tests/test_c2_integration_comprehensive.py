"""
Comprehensive integration tests for C2 Provider Selection UI cross-module interactions.

Tests integration between F2 Provider Interface, F4 Configuration, WebSocket Infrastructure,
Session Management, and Rate Limiting components with C2 Provider Selection UI.
"""

import json
from unittest.mock import MagicMock, patch

import pytest

from src.api.provider_routes import provider_bp


class TestC2F2ProviderInterfaceIntegration:
    """Test C2 integration with F2 Provider Interface Factory."""

    @patch("src.api.provider_routes.get_provider_factory")
    def test_c2_provider_capabilities_integration(self, mock_factory_getter, c2_client):
        """Test C2 integration with F2 provider capability detection."""
        # Mock F2 provider factory
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = [
            "azure_sora",
            "google_veo3",
        ]

        # Mock provider instances with different capabilities
        mock_azure_provider = MagicMock()
        mock_azure_provider.supported_features = {
            "image_input": False,  # Azure doesn't support image input
            "audio_generation": True,  # Supports audio generation
            "custom_duration": True,  # Supports duration control
            "cancellation": False,  # No cancellation support
            "aspect_ratio_control": True,  # Supports resolution control
            "real_time_status": True,  # Supports status polling
        }

        mock_veo3_provider = MagicMock()
        mock_veo3_provider.supported_features = {
            "image_input": True,  # Veo3 supports image-to-video
            "audio_generation": True,  # Supports audio generation
            "custom_duration": True,  # Supports variable duration
            "cancellation": True,  # Supports generation cancellation
            "aspect_ratio_control": True,  # Supports aspect ratio control
            "real_time_status": True,  # Supports status polling
        }

        def create_provider_side_effect(provider_name):
            if provider_name == "azure_sora":
                return mock_azure_provider
            elif provider_name == "google_veo3":
                return mock_veo3_provider
            else:
                raise ValueError(f"Unknown provider: {provider_name}")

        mock_factory.create_provider.side_effect = create_provider_side_effect
        mock_factory_getter.return_value = mock_factory

        # Test provider capabilities endpoint
        response = c2_client.get("/providers")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert "providers" in data["data"]

        providers = data["data"]["providers"]
        assert "azure_sora" in providers
        assert "google_veo3" in providers

        # Verify Azure Sora capabilities
        azure_caps = providers["azure_sora"]["capabilities"]
        assert azure_caps["audio_generation"] is True
        assert azure_caps["image_input"] is False  # Azure doesn't support image input
        assert azure_caps["custom_duration"] is True
        assert azure_caps["cancellation"] is False

        # Verify Google Veo3 capabilities
        veo3_caps = providers["google_veo3"]["capabilities"]
        assert veo3_caps["audio_generation"] is True
        assert veo3_caps["image_input"] is True  # Veo3 supports image input
        assert veo3_caps["custom_duration"] is True
        assert veo3_caps["cancellation"] is True

    @patch("src.api.provider_routes.get_provider_factory")
    def test_c2_provider_factory_error_handling(self, mock_factory_getter, c2_client):
        """Test C2 handling of F2 provider factory errors."""
        # Mock factory to raise an exception
        mock_factory_getter.side_effect = Exception(
            "Provider factory initialization failed"
        )

        response = c2_client.get("/providers")

        assert response.status_code == 500
        data = json.loads(response.data)

        assert data["success"] is False
        assert "Failed to retrieve providers" in data["error"]

    @patch("src.api.provider_routes.get_provider_factory")
    def test_c2_provider_creation_error_handling(self, mock_factory_getter, c2_client):
        """Test C2 handling of F2 provider creation errors."""
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = ["azure_sora"]
        mock_factory.create_provider.side_effect = Exception("Provider creation failed")
        mock_factory_getter.return_value = mock_factory

        response = c2_client.get("/providers")

        assert response.status_code == 200
        data = json.loads(response.data)

        # Should still return successful response but mark provider as unavailable
        assert data["success"] is True
        providers = data["data"]["providers"]
        assert "azure_sora" in providers
        assert providers["azure_sora"]["available"] is False
        assert "error" in providers["azure_sora"]


class TestC2F4ConfigurationIntegration:
    """Test C2 integration with F4 Configuration Management."""

    @patch("src.api.provider_routes.get_provider_configuration_manager")
    def test_c2_environment_configuration_integration(
        self, mock_config_manager_getter, c2_client
    ):
        """Test C2 integration with F4 environment configuration."""
        # Mock F4 configuration manager
        mock_config_manager = MagicMock()
        mock_config_manager.get_environment_configuration_summary.return_value = {
            "environment": {"type": "local", "debug": True},
            "providers": {
                "available_providers": ["azure_sora", "google_veo3"],
                "default_provider": "azure_sora",
                "provider_configurations": {
                    "azure_sora": {"enabled": True, "max_concurrency": 3},
                    "google_veo3": {"enabled": True, "max_concurrency": 5},
                },
            },
            "f4_system": {
                "deployment_detection": "local",
                "configuration_source": "environment_variables",
                "last_updated": "2024-01-01T12:00:00Z",
            },
        }
        mock_config_manager_getter.return_value = mock_config_manager

        response = c2_client.get("/c2/environment")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["message"] == "C2 environment configuration retrieved successfully"

        env_data = data["data"]
        assert env_data["environment"]["type"] == "local"
        assert env_data["providers"]["default_provider"] == "azure_sora"
        assert len(env_data["providers"]["available_providers"]) == 2
        assert env_data["f4_system"]["deployment_detection"] == "local"

    @patch("src.api.provider_routes.get_provider_configuration_manager")
    @patch("src.api.provider_routes._handle_async_request")
    def test_c2_optimal_provider_config_integration(
        self, mock_async_handler, mock_config_manager_getter, c2_client
    ):
        """Test C2 integration with F4 optimal provider configuration."""
        mock_config_manager = MagicMock()
        mock_config_manager_getter.return_value = mock_config_manager

        # Mock async optimal config response
        mock_async_handler.return_value = {
            "selected_provider": "google_veo3",
            "configuration": {
                "max_concurrency": 5,
                "timeout_seconds": 300,
                "retry_attempts": 3,
            },
            "environment": "local",
            "selection_reason": "Best performance for current load",
        }

        with patch(
            "src.api.provider_routes._get_session_id", return_value="test-session"
        ):
            with patch(
                "src.api.provider_routes.get_session_manager"
            ) as mock_session_getter:
                mock_session_manager = MagicMock()
                mock_session_manager.get_provider_preferences.return_value = {
                    "performance_priority": "speed"
                }
                mock_session_manager.get_recommended_provider.return_value = (
                    "google_veo3"
                )
                mock_session_getter.return_value = mock_session_manager

                response = c2_client.get("/c2/recommend")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["data"]["recommended_provider"] == "google_veo3"
        assert "optimal_configuration" in data["data"]

    @patch("src.api.provider_routes.get_provider_configuration_manager")
    @patch("src.api.provider_routes._handle_async_request")
    def test_c2_provider_availability_refresh_integration(
        self, mock_async_handler, mock_config_manager_getter, c2_client
    ):
        """Test C2 integration with F4 provider availability refresh."""
        mock_config_manager = MagicMock()
        mock_config_manager_getter.return_value = mock_config_manager

        # Mock comprehensive refresh response
        mock_async_handler.return_value = {
            "provider_status_refresh": {
                "azure_sora": {
                    "success": True,
                    "status": "online",
                    "response_time_ms": 150.0,
                    "success_rate": 95.0,
                },
                "google_veo3": {
                    "success": True,
                    "status": "online",
                    "response_time_ms": 120.0,
                    "success_rate": 98.0,
                },
            },
            "availability_matrix": {
                "environment": "local",
                "available_providers": ["azure_sora", "google_veo3"],
                "default_provider": "azure_sora",
            },
            "available_providers": ["azure_sora", "google_veo3"],
            "default_provider": "azure_sora",
        }

        response = c2_client.post("/c2/refresh")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["message"] == "C2 provider status refreshed successfully"

        refresh_data = data["data"]
        assert "provider_status_refresh" in refresh_data
        assert "availability_matrix" in refresh_data
        assert len(refresh_data["available_providers"]) == 2


class TestC2WebSocketIntegration:
    """Test C2 integration with WebSocket Infrastructure."""

    @patch("src.api.provider_routes.broadcast_provider_status_change")
    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_c2_provider_throttle_websocket_broadcast(
        self, mock_rate_limiter_getter, mock_broadcast, c2_client
    ):
        """Test C2 provider throttling triggers WebSocket broadcast."""
        # Mock rate limiter
        mock_rate_limiter = MagicMock()
        mock_rate_limiter.manually_throttle_provider.return_value = True
        mock_rate_limiter.get_provider_rate_status.return_value = {
            "provider_id": "azure_sora",
            "current_rate_limit": 5,
            "throttle_reason": "manual_throttle",
            "throttled_until": "2024-01-01T13:00:00Z",
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        throttle_data = {
            "provider_id": "azure_sora",
            "throttle": True,
            "reason": "Manual throttle for testing",
        }

        response = c2_client.post(
            "/c2/throttle",
            data=json.dumps(throttle_data),
            content_type="application/json",
        )

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert "throttled successfully" in data["message"]

        # Verify WebSocket broadcast was called
        mock_broadcast.assert_called_once()
        broadcast_args = mock_broadcast.call_args[1]  # kwargs
        assert broadcast_args["provider_id"] == "azure_sora"
        assert broadcast_args["status"] == "throttled"
        assert broadcast_args["previous_status"] == "available"

    @patch("src.api.provider_routes.broadcast_provider_recommendation_change")
    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_c2_preference_update_websocket_broadcast(
        self,
        mock_session_manager_getter,
        mock_get_session_id,
        mock_broadcast,
        c2_client,
    ):
        """Test C2 preference updates trigger WebSocket broadcast."""
        mock_get_session_id.return_value = "test-session-123"

        # Mock session manager
        mock_session_manager = MagicMock()
        mock_session_manager.set_preferred_provider.return_value = True
        mock_session_manager.set_fallback_providers.return_value = True
        mock_session_manager.set_performance_priority.return_value = True
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3",
            "fallback_providers": ["azure_sora"],
            "performance_priority": "speed",
        }
        mock_session_manager_getter.return_value = mock_session_manager

        preference_data = {
            "preferred_provider": "google_veo3",
            "fallback_providers": ["azure_sora"],
            "performance_priority": "speed",
        }

        response = c2_client.post(
            "/c2/preferences",
            data=json.dumps(preference_data),
            content_type="application/json",
        )

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["message"] == "C2 provider preferences updated successfully"

        # Verify WebSocket broadcast was called
        mock_broadcast.assert_called_once()
        broadcast_args = mock_broadcast.call_args[1]  # kwargs
        assert broadcast_args["session_id"] == "test-session-123"
        assert broadcast_args["preferred_provider"] == "google_veo3"
        assert broadcast_args["fallback_providers"] == ["azure_sora"]
        assert broadcast_args["performance_priority"] == "speed"


class TestC2SessionManagementIntegration:
    """Test C2 integration with Session Management components."""

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_c2_session_preferences_integration(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test C2 integration with session-based provider preferences."""
        mock_get_session_id.return_value = "test-session-456"

        # Mock session manager with preferences
        mock_session_manager = MagicMock()
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora",
            "fallback_providers": ["google_veo3"],
            "performance_priority": "quality",
            "auto_failover": True,
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 15,
            "successful_requests": 14,
            "failed_requests": 1,
            "provider_usage": {"azure_sora": 10, "google_veo3": 5},
        }
        mock_session_manager_getter.return_value = mock_session_manager

        response = c2_client.get("/c2/preferences")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["message"] == "C2 provider preferences retrieved successfully"

        pref_data = data["data"]
        assert pref_data["session_id"] == "test-session-456"
        assert pref_data["preferences"]["preferred_provider"] == "azure_sora"
        assert pref_data["preferences"]["performance_priority"] == "quality"
        assert pref_data["usage_statistics"]["total_requests"] == 15
        assert pref_data["usage_statistics"]["successful_requests"] == 14

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_c2_session_isolation_integration(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test C2 session isolation between different users."""
        # Test user 1
        mock_get_session_id.return_value = "user1-session"

        mock_session_manager = MagicMock()
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora",
            "performance_priority": "quality",
        }
        mock_session_manager_getter.return_value = mock_session_manager

        response1 = c2_client.get("/c2/preferences")
        data1 = json.loads(response1.data)

        # Test user 2 with different session
        mock_get_session_id.return_value = "user2-session"
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3",
            "performance_priority": "speed",
        }

        response2 = c2_client.get("/c2/preferences")
        data2 = json.loads(response2.data)

        # Verify session isolation
        assert data1["data"]["session_id"] == "user1-session"
        assert data1["data"]["preferences"]["preferred_provider"] == "azure_sora"
        assert data1["data"]["preferences"]["performance_priority"] == "quality"

        assert data2["data"]["session_id"] == "user2-session"
        assert data2["data"]["preferences"]["preferred_provider"] == "google_veo3"
        assert data2["data"]["preferences"]["performance_priority"] == "speed"

    @patch("src.api.provider_routes._get_session_id")
    def test_c2_session_required_error_handling(self, mock_get_session_id, c2_client):
        """Test C2 error handling when session is required but missing."""
        mock_get_session_id.side_effect = ValueError(
            "Session ID not found in request context"
        )

        # Test routes that require session
        session_required_routes = [
            ("/c2/recommend", "GET"),
            ("/c2/preferences", "GET"),
            ("/c2/preferences", "POST"),
        ]

        for route, method in session_required_routes:
            if method == "GET":
                response = c2_client.get(route)
            else:
                response = c2_client.post(
                    route, data="{}", content_type="application/json"
                )

            assert response.status_code == 400
            data = json.loads(response.data)
            assert data["success"] is False
            assert "Session" in data["message"]


class TestC2RateLimitingIntegration:
    """Test C2 integration with Rate Limiting components."""

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_c2_rate_limiter_integration_available(
        self, mock_rate_limiter_getter, c2_client
    ):
        """Test C2 integration when rate limiter is available."""
        # Mock rate limiter
        mock_rate_limiter = MagicMock()
        mock_rate_limiter.get_all_providers_rate_status.return_value = {
            "azure_sora": {
                "provider_id": "azure_sora",
                "current_rate_limit": 10,
                "requests_in_window": 5,
                "rate_limit_exceeded": False,
                "throttled": False,
            },
            "google_veo3": {
                "provider_id": "google_veo3",
                "current_rate_limit": 15,
                "requests_in_window": 12,
                "rate_limit_exceeded": False,
                "throttled": False,
            },
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        # Mock other required services
        with patch(
            "src.api.provider_routes._handle_async_request"
        ) as mock_async_handler:
            mock_async_handler.return_value = {
                "provider_statuses": {},
                "environment": {"type": "local"},
                "available_providers": ["azure_sora", "google_veo3"],
                "default_provider": "azure_sora",
                "rate_limiting": mock_rate_limiter.get_all_providers_rate_status.return_value,
                "f4_system_info": {},
            }

            response = c2_client.get("/c2/status")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        rate_limiting = data["data"]["rate_limiting"]
        assert "azure_sora" in rate_limiting
        assert "google_veo3" in rate_limiting
        assert rate_limiting["azure_sora"]["current_rate_limit"] == 10
        assert rate_limiting["google_veo3"]["requests_in_window"] == 12

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_c2_rate_limiter_integration_unavailable(
        self, mock_rate_limiter_getter, c2_client
    ):
        """Test C2 integration when rate limiter is unavailable."""
        # Mock rate limiter as None (unavailable)
        mock_rate_limiter_getter.return_value = None

        throttle_data = {
            "provider_id": "azure_sora",
            "throttle": True,
            "reason": "Testing throttle without rate limiter",
        }

        response = c2_client.post(
            "/c2/throttle",
            data=json.dumps(throttle_data),
            content_type="application/json",
        )

        assert response.status_code == 400
        data = json.loads(response.data)

        assert data["success"] is False
        assert "Provider rate limiter not available" in data["error"]

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_c2_rate_limiter_throttle_integration(
        self, mock_rate_limiter_getter, c2_client
    ):
        """Test C2 integration with rate limiter throttling functionality."""
        mock_rate_limiter = MagicMock()
        mock_rate_limiter.manually_throttle_provider.return_value = True
        mock_rate_limiter.get_provider_rate_status.return_value = {
            "provider_id": "azure_sora",
            "current_rate_limit": 1,  # Reduced limit
            "throttled": True,
            "throttle_reason": "manual_throttle",
            "throttled_until": "2024-01-01T14:00:00Z",
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        with patch("src.api.provider_routes.broadcast_provider_status_change"):
            throttle_data = {
                "provider_id": "azure_sora",
                "throttle": True,
                "reason": "High error rate detected",
            }

            response = c2_client.post(
                "/c2/throttle",
                data=json.dumps(throttle_data),
                content_type="application/json",
            )

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["data"]["provider_id"] == "azure_sora"
        assert data["data"]["action"] == "throttled"
        assert data["data"]["reason"] == "High error rate detected"

        # Verify rate limiter was called correctly
        mock_rate_limiter.manually_throttle_provider.assert_called_once_with(
            "azure_sora", True, "High error rate detected"
        )


@pytest.fixture
def c2_client():
    """Create test client for C2 integration testing."""
    from flask import Flask

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.secret_key = "test-secret-key"

    # Register provider routes blueprint
    app.register_blueprint(provider_bp)

    with app.test_client() as client:
        with app.app_context():
            yield client
