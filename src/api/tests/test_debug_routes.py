"""Unit tests for debug API routes module.

Tests debug endpoints functionality, security validation, and environment
variable handling. These tests ensure debug endpoints work properly in
development while maintaining security awareness.
"""

import json
import os
import uuid
from unittest.mock import patch

from src.api.debug_routes import validate_production_security


class TestDebugAzureConfigEndpoint:
    """Test debug Azure configuration endpoint."""

    def test_get_azure_config_success(self, client):
        """Test successful Azure config retrieval with all environment variables set."""
        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test-endpoint.openai.azure.com/",
            "AZURE_OPENAI_API_VERSION": "preview",
            "AZURE_OPENAI_SORA_DEPLOYMENT": "sora-test",
            "AZURE_OPENAI_API_KEY": "test-api-key-12345",
            "FLASK_ENV": "development",  # Enable debug endpoints
        }

        with patch.dict(os.environ, test_env):
            with patch("src.api.debug_routes.logger") as mock_logger:
                response = client.get("/debug/azure-config")

                # Verify security warnings are logged
                mock_logger.warning.assert_any_call(
                    "🚨 SECURITY: Debug endpoint /debug/azure-config accessed"
                )
                mock_logger.warning.assert_any_call(
                    "🚨 SECURITY: This endpoint exposes sensitive Azure API keys"
                )
                mock_logger.warning.assert_any_call(
                    "🚨 SECURITY: MUST BE REMOVED before production deployment"
                )

        assert response.status_code == 200
        data = json.loads(response.data)

        # Verify response structure
        assert data["success"] is True
        assert "🚨 SECURITY WARNING" in data["message"]
        assert "data" in data

        # Verify config data
        config_data = data["data"]
        assert config_data["endpoint"] == "https://test-endpoint.openai.azure.com/"
        assert config_data["api_version"] == "preview"
        assert config_data["deployment_name"] == "sora-test"
        assert config_data["api_key"] == "test-api-key-12345"
        assert config_data["api_key_length"] == 18
        assert config_data["has_api_key"] is True

    def test_get_azure_config_missing_environment_variables(self, client):
        """Test Azure config endpoint with missing environment variables."""
        # Clear Azure environment variables
        test_env = {"FLASK_ENV": "development"}

        with patch.dict(os.environ, test_env, clear=True):
            response = client.get("/debug/azure-config")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        config_data = data["data"]

        # All values should be "NOT_SET" when environment variables are missing
        assert config_data["endpoint"] == "NOT_SET"
        assert config_data["api_version"] == "NOT_SET"
        assert config_data["deployment_name"] == "NOT_SET"
        assert config_data["api_key"] == "NOT_SET"
        assert config_data["api_key_length"] == 0
        assert config_data["has_api_key"] is False

    def test_get_azure_config_partial_environment_variables(self, client):
        """Test Azure config endpoint with only some environment variables set."""
        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://partial-config.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "partial-key",
            "FLASK_ENV": "development",
        }

        with patch.dict(os.environ, test_env, clear=True):
            response = client.get("/debug/azure-config")

        assert response.status_code == 200
        data = json.loads(response.data)

        config_data = data["data"]
        assert config_data["endpoint"] == "https://partial-config.openai.azure.com/"
        assert config_data["api_key"] == "partial-key"
        assert config_data["api_key_length"] == 11
        assert config_data["has_api_key"] is True

        # Missing variables should be "NOT_SET"
        assert config_data["api_version"] == "NOT_SET"
        assert config_data["deployment_name"] == "NOT_SET"

    def test_get_azure_config_empty_api_key(self, client):
        """Test Azure config endpoint with empty API key."""
        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test-endpoint.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "",  # Empty API key
            "FLASK_ENV": "development",
        }

        with patch.dict(os.environ, test_env):
            response = client.get("/debug/azure-config")

        assert response.status_code == 200
        data = json.loads(response.data)

        config_data = data["data"]
        assert config_data["api_key"] == ""
        assert config_data["api_key_length"] == 0
        assert config_data["has_api_key"] is False

    def test_get_azure_config_logging_behavior(self, client):
        """Test that Azure config endpoint logs security warnings and config exposure."""
        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "secret-key",
            "FLASK_ENV": "development",
        }

        with patch.dict(os.environ, test_env):
            with patch("src.api.debug_routes.logger") as mock_logger:
                response = client.get("/debug/azure-config")

                # Verify all expected security warnings are logged
                expected_warnings = [
                    "🚨 SECURITY: Debug endpoint /debug/azure-config accessed",
                    "🚨 SECURITY: This endpoint exposes sensitive Azure API keys",
                    "🚨 SECURITY: MUST BE REMOVED before production deployment",
                ]

                for warning in expected_warnings:
                    mock_logger.warning.assert_any_call(warning)

                # Verify config exposure is logged with warning
                assert mock_logger.warning.called

        assert response.status_code == 200

    @patch("src.api.debug_routes.os.getenv")
    def test_get_azure_config_exception_handling(self, mock_getenv, client):
        """Test Azure config endpoint exception handling."""
        # Mock environment variable access to raise exception
        mock_getenv.side_effect = Exception("Environment access failed")

        with patch.dict(os.environ, {"FLASK_ENV": "development"}):
            with patch("src.api.debug_routes.logger") as mock_logger:
                response = client.get("/debug/azure-config")

                # Verify error logging
                mock_logger.error.assert_called_once()
                error_call = mock_logger.error.call_args[0][0]
                assert "Debug endpoint error:" in error_call

        assert response.status_code == 500
        data = json.loads(response.data)

        assert data["success"] is False
        assert data["message"] == "Failed to retrieve Azure configuration"
        assert "Environment access failed" in data["error"]


class TestDebugEndpointSecurity:
    """Test security aspects of debug endpoints."""

    def test_debug_endpoint_security_awareness(self, client):
        """Test that debug endpoints include appropriate security warnings."""
        test_env = {"AZURE_OPENAI_API_KEY": "sensitive-key", "FLASK_ENV": "development"}

        with patch.dict(os.environ, test_env):
            response = client.get("/debug/azure-config")

        assert response.status_code == 200
        data = json.loads(response.data)

        # Security warning should be prominent in response
        assert "🚨 SECURITY WARNING" in data["message"]
        assert "REMOVE IN PRODUCTION" in data["message"]

    def test_debug_endpoint_api_key_exposure(self, client):
        """Test that debug endpoint actually exposes API key (security risk validation)."""
        sensitive_key = f"secret-key-{uuid.uuid4()}"
        test_env = {"AZURE_OPENAI_API_KEY": sensitive_key, "FLASK_ENV": "development"}

        with patch.dict(os.environ, test_env):
            response = client.get("/debug/azure-config")

        assert response.status_code == 200
        data = json.loads(response.data)

        # Verify the actual API key is exposed (demonstrating the security risk)
        assert data["data"]["api_key"] == sensitive_key
        assert data["data"]["has_api_key"] is True
        assert data["data"]["api_key_length"] == len(sensitive_key)

    def test_debug_endpoint_multiple_security_indicators(self, client):
        """Test that debug endpoint includes multiple security risk indicators."""
        test_env = {
            "AZURE_OPENAI_API_KEY": "sensitive-data",
            "FLASK_ENV": "development",
        }

        with patch.dict(os.environ, test_env):
            with patch("src.api.debug_routes.logger") as mock_logger:
                response = client.get("/debug/azure-config")

        assert response.status_code == 200
        data = json.loads(response.data)

        # Multiple security indicators should be present
        security_indicators = [
            "🚨 SECURITY WARNING" in data["message"],
            "REMOVE IN PRODUCTION" in data["message"],
            mock_logger.warning.called,
            data["data"]["api_key"] != "NOT_SET",  # Actual key exposure
        ]

        # All security indicators should be present
        assert all(security_indicators)


class TestProductionSecurityValidation:
    """Test production security validation function."""

    def test_validate_production_security_returns_false(self):
        """Test that production security validation correctly identifies security risk."""
        # This function should always return False since debug endpoints exist
        result = validate_production_security()
        assert result is False

    def test_validate_production_security_function_exists(self):
        """Test that production security validation function exists and is callable."""
        # Verify the function exists and can be called
        assert callable(validate_production_security)

        # Should consistently return False (indicating security risk)
        result1 = validate_production_security()
        result2 = validate_production_security()

        assert result1 is False
        assert result2 is False
        assert result1 == result2


class TestDebugBlueprintIntegration:
    """Test debug blueprint integration with main application."""

    def test_debug_endpoint_availability_in_development(self, client):
        """Test that debug endpoints are available in development environment."""
        with patch.dict(os.environ, {"FLASK_ENV": "development"}):
            response = client.get("/debug/azure-config")

        # Should be available (not 404)
        assert response.status_code != 404

    def test_debug_endpoint_response_format_consistency(self, client):
        """Test that debug endpoints return consistent JSON format."""
        test_env = {"AZURE_OPENAI_API_KEY": "test-key", "FLASK_ENV": "development"}

        with patch.dict(os.environ, test_env):
            response = client.get("/debug/azure-config")

        assert response.status_code == 200
        assert response.content_type == "application/json"

        data = json.loads(response.data)

        # Verify APIResponse format
        assert "success" in data
        assert "message" in data
        assert "data" in data
        assert isinstance(data["success"], bool)
        assert isinstance(data["message"], str)
        assert isinstance(data["data"], dict)

    def test_debug_endpoint_error_response_format(self, client):
        """Test that debug endpoint error responses follow consistent format."""
        with patch(
            "src.api.debug_routes.os.getenv", side_effect=Exception("Test error")
        ):
            with patch.dict(os.environ, {"FLASK_ENV": "development"}):
                response = client.get("/debug/azure-config")

        assert response.status_code == 500
        data = json.loads(response.data)

        # Verify error response format
        assert data["success"] is False
        assert "message" in data
        assert "error" in data
        assert data["message"] == "Failed to retrieve Azure configuration"
        assert "Test error" in data["error"]


class TestDebugEndpointConfiguration:
    """Test debug endpoint configuration handling."""

    def test_azure_config_with_various_key_lengths(self, client):
        """Test Azure config endpoint with API keys of various lengths."""
        test_cases = [
            ("", 0, False),  # Empty key
            ("a", 1, True),  # Single character
            ("short-key", 9, True),  # Short key
            ("a" * 32, 32, True),  # Standard length key
            ("a" * 64, 64, True),  # Long key
            ("very-long-api-key-" + "x" * 50, 68, True),  # Very long key
        ]

        for api_key, expected_length, expected_has_key in test_cases:
            test_env = {"AZURE_OPENAI_API_KEY": api_key, "FLASK_ENV": "development"}

            with patch.dict(os.environ, test_env, clear=True):
                response = client.get("/debug/azure-config")

            assert response.status_code == 200
            data = json.loads(response.data)

            config_data = data["data"]
            assert config_data["api_key_length"] == expected_length
            assert config_data["has_api_key"] == expected_has_key
            assert config_data["api_key"] == api_key

    def test_azure_config_endpoint_url_variations(self, client):
        """Test Azure config endpoint with various endpoint URL formats."""
        test_endpoints = [
            "https://test.openai.azure.com/",
            "https://another-test.openai.azure.com",  # No trailing slash
            "https://custom-endpoint.example.com/",
            "http://localhost:8080/",  # Local endpoint
            "",  # Empty endpoint
        ]

        for endpoint_url in test_endpoints:
            test_env = {
                "AZURE_OPENAI_ENDPOINT": endpoint_url,
                "FLASK_ENV": "development",
            }

            with patch.dict(os.environ, test_env, clear=True):
                response = client.get("/debug/azure-config")

            assert response.status_code == 200
            data = json.loads(response.data)

            expected_value = endpoint_url if endpoint_url else "NOT_SET"
            assert data["data"]["endpoint"] == expected_value

    def test_azure_config_deployment_name_variations(self, client):
        """Test Azure config endpoint with various deployment names."""
        test_deployments = [
            "sora",
            "sora-v1",
            "sora-production",
            "custom-deployment-name",
            "test123",
            "",  # Empty deployment
        ]

        for deployment_name in test_deployments:
            test_env = {
                "AZURE_OPENAI_SORA_DEPLOYMENT": deployment_name,
                "FLASK_ENV": "development",
            }

            with patch.dict(os.environ, test_env, clear=True):
                response = client.get("/debug/azure-config")

            assert response.status_code == 200
            data = json.loads(response.data)

            expected_value = deployment_name if deployment_name else "NOT_SET"
            assert data["data"]["deployment_name"] == expected_value
