"""Tests for video routes - Video generation API endpoints."""

import json
import uuid
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
from flask import Flask, g
from pydantic import ValidationError

from src.api.video_routes import video_bp
from src.core.models import VideoJob


@pytest.mark.integration
class TestVideoRoutes:
    """Test video generation routes."""

    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = Flask(__name__)
        app.register_blueprint(video_bp)
        app.config['TESTING'] = True
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()

    def test_index_route(self, client):
        """Test main index route."""
        with patch('src.api.video_routes.render_template') as mock_render:
            mock_render.return_value = "<html>Test Page</html>"
            
            response = client.get('/')
            
            assert response.status_code == 200
            mock_render.assert_called_once_with("index.html")

    @patch('src.api.video_routes.ConfigurationFactory.get_video_config')
    def test_get_ui_config_success(self, mock_get_config, client):
        """Test successful UI configuration retrieval."""
        # Mock video config
        mock_config = Mock()
        mock_config.get_defaults_dict.return_value = {
            "duration": 5,
            "width": 1280,
            "height": 720,
            "model": "sora-v1"
        }
        mock_config.get_constraints_dict.return_value = {
            "duration": {"min": 1, "max": 60},
            "width": {"min": 480, "max": 3840},
            "height": {"min": 480, "max": 2160}
        }
        mock_get_config.return_value = mock_config

        response = client.get('/config')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["success"] is True
        assert data["message"] == "UI configuration retrieved"
        assert "defaults" in data["data"]
        assert "constraints" in data["data"]
        assert data["data"]["defaults"]["duration"] == 5
        assert data["data"]["constraints"]["width"]["max"] == 3840

    @patch('src.api.video_routes.ConfigurationFactory.get_video_config')
    def test_get_ui_config_error(self, mock_get_config, client):
        """Test UI configuration retrieval error."""
        mock_get_config.side_effect = Exception("Config error")

        response = client.get('/config')
        
        assert response.status_code == 500
        data = json.loads(response.data)
        
        assert data["success"] is False
        assert data["message"] == "Failed to retrieve UI configuration"
        assert "Config error" in data["error"]


@pytest.mark.integration
class TestVideoGenerationRoute:
    """Test video generation endpoint."""

    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = Flask(__name__)
        app.register_blueprint(video_bp)
        app.config['TESTING'] = True
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()

    def test_generate_video_missing_session(self, client):
        """Test video generation without session."""
        response = client.post('/generate', data={
            'prompt': 'Test video prompt'
        })
        
        assert response.status_code == 400
        data = json.loads(response.data)
        
        assert data["success"] is False
        assert data["message"] == "Session required"
        assert "Session not found" in data["error"]

    @patch('src.api.video_routes.job_repository')
    @patch('src.api.video_routes.process_video_generation')
    @patch('src.api.video_routes._get_queue_manager')
    def test_generate_video_success(self, mock_queue_manager, mock_process, mock_job_repo, client):
        """Test successful video generation."""
        # Mock session
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            # Mock dependencies
            mock_job_repo.create_job.return_value = VideoJob(
                id="test-job-123",
                session_id="test-session-123", 
                prompt="Test video prompt",
                status="pending",
                created_at=datetime.now()
            )
            
            mock_queue_mgr_instance = Mock()
            mock_queue_mgr_instance.assign_queue_position.return_value = 2
            mock_queue_mgr_instance.get_queue_status.return_value = {
                'user_job_positions': [{'estimated_wait_minutes': 5}]
            }
            mock_queue_manager.return_value = mock_queue_mgr_instance
            
            # Mock Celery task
            mock_process.delay.return_value = Mock()
            
            response = client.post('/generate', data={
                'prompt': 'Test video prompt',
                'duration': '10',
                'width': '1920',
                'height': '1080',
                'model': 'sora-v1'
            })
            
            assert response.status_code == 200
            data = json.loads(response.data)
            
            assert data["success"] is True
            assert data["status"] == "pending"
            assert data["queue_position"] == 2
            assert data["estimated_wait_minutes"] == 5
            assert "job_id" in data
            
            # Verify job was created
            mock_job_repo.create_job.assert_called_once()
            
            # Verify task was queued
            mock_process.delay.assert_called_once()
            
            # Verify queue management
            mock_queue_mgr_instance.assign_queue_position.assert_called_once()

    @patch('src.api.video_routes.job_repository')
    @patch('src.api.video_routes.process_video_generation')
    @patch('src.api.video_routes._get_queue_manager')
    def test_generate_video_minimal_data(self, mock_queue_manager, mock_process, mock_job_repo, client):
        """Test video generation with minimal data."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            # Mock dependencies
            mock_job_repo.create_job.return_value = VideoJob(
                id="test-job-123",
                session_id="test-session-123",
                prompt="Simple prompt",
                status="pending",
                created_at=datetime.now()
            )
            
            mock_queue_mgr_instance = Mock()
            mock_queue_mgr_instance.assign_queue_position.return_value = 1
            mock_queue_mgr_instance.get_queue_status.return_value = {
                'user_job_positions': [{'estimated_wait_minutes': 3}]
            }
            mock_queue_manager.return_value = mock_queue_mgr_instance
            
            response = client.post('/generate', data={
                'prompt': 'Simple prompt'
            })
            
            assert response.status_code == 200
            data = json.loads(response.data)
            
            assert data["success"] is True
            assert data["status"] == "pending"
            assert data["queue_position"] == 1
            assert data["estimated_wait_minutes"] == 3

    def test_generate_video_validation_error_empty_prompt(self, client):
        """Test video generation with empty prompt."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            response = client.post('/generate', data={
                'prompt': ''  # Empty prompt should fail validation
            })
            
            assert response.status_code == 400
            data = json.loads(response.data)
            
            assert data["success"] is False
            assert data["message"] == "Request validation failed"
            assert "String should have at least 1 character" in data["error"]

    def test_generate_video_validation_error_invalid_duration(self, client):
        """Test video generation with invalid duration."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            response = client.post('/generate', data={
                'prompt': 'Test prompt',
                'duration': '100'  # Too long
            })
            
            assert response.status_code == 400
            data = json.loads(response.data)
            
            assert data["success"] is False
            assert data["message"] == "Request validation failed"
            assert "Input should be less than or equal to 60" in data["error"]

    def test_generate_video_validation_error_invalid_dimensions(self, client):
        """Test video generation with invalid dimensions."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            response = client.post('/generate', data={
                'prompt': 'Test prompt',
                'width': '100',  # Too small
                'height': '5000'  # Too large
            })
            
            assert response.status_code == 400
            data = json.loads(response.data)
            
            assert data["success"] is False
            assert data["message"] == "Request validation failed"
            assert "Input should be greater than or equal to 480" in data["error"]

    def test_generate_video_validation_error_invalid_model(self, client):
        """Test video generation with invalid model."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            response = client.post('/generate', data={
                'prompt': 'Test prompt',
                'model': 'invalid-model'
            })
            
            assert response.status_code == 400
            data = json.loads(response.data)
            
            assert data["success"] is False
            assert data["message"] == "Request validation failed"
            assert "String should match pattern" in data["error"]

    @patch('src.api.video_routes.job_repository')
    def test_generate_video_database_error(self, mock_job_repo, client):
        """Test video generation with database error."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            # Mock database failure
            mock_job_repo.create_job.return_value = None
            
            response = client.post('/generate', data={
                'prompt': 'Test prompt'
            })
            
            assert response.status_code == 500
            data = json.loads(response.data)
            
            assert data["success"] is False
            assert data["message"] == "Internal server error during video generation"
            assert data["error"] == "An unexpected error occurred"

    @patch('src.api.video_routes.job_repository')
    @patch('src.api.video_routes.process_video_generation')
    @patch('src.api.video_routes._get_queue_manager')
    def test_generate_video_queue_manager_error(self, mock_queue_manager, mock_process, mock_job_repo, client):
        """Test video generation with queue manager error."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            # Mock job creation success
            mock_job_repo.create_job.return_value = VideoJob(
                id="test-job-123",
                session_id="test-session-123",
                prompt="Test prompt",
                status="pending",
                created_at=datetime.now()
            )
            
            # Mock queue manager failure
            mock_queue_manager.side_effect = Exception("Queue error")
            
            response = client.post('/generate', data={
                'prompt': 'Test prompt'
            })
            
            assert response.status_code == 200
            data = json.loads(response.data)
            
            # Should still succeed with fallback values
            assert data["success"] is True
            assert data["status"] == "pending"
            assert data["queue_position"] == 1  # Default fallback
            assert data["estimated_wait_minutes"] == 3  # Default fallback

    @patch('src.api.video_routes.job_repository')
    @patch('src.api.video_routes.process_video_generation')
    @patch('src.api.video_routes._get_queue_manager')
    def test_generate_video_celery_integration(self, mock_queue_manager, mock_process, mock_job_repo, client):
        """Test video generation Celery task integration."""
        with client.application.app_context():
            g.session_id = "test-session-123"
            
            # Mock dependencies
            mock_job_repo.create_job.return_value = VideoJob(
                id="test-job-123",
                session_id="test-session-123",
                prompt="Test prompt",
                status="pending",
                created_at=datetime.now()
            )
            
            mock_queue_mgr_instance = Mock()
            mock_queue_mgr_instance.assign_queue_position.return_value = 1
            mock_queue_mgr_instance.get_queue_status.return_value = {
                'user_job_positions': [{'estimated_wait_minutes': 2}]
            }
            mock_queue_manager.return_value = mock_queue_mgr_instance
            
            response = client.post('/generate', data={
                'prompt': 'Test prompt',
                'duration': '5',
                'width': '1280',
                'height': '720'
            })
            
            assert response.status_code == 200
            
            # Verify Celery task was called with correct parameters
            mock_process.delay.assert_called_once()
            call_args = mock_process.delay.call_args
            
            session_id, job_id, job_data = call_args[0]
            assert session_id == "test-session-123"
            assert job_id == "test-job-123"
            assert job_data["prompt"] == "Test prompt"
            assert job_data["ui_parameters"]["duration"] == 5
            assert job_data["ui_parameters"]["width"] == 1280
            assert job_data["ui_parameters"]["height"] == 720


@pytest.mark.unit
class TestVideoRouteHelpers:
    """Test video route helper functions."""

    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = Flask(__name__)
        app.register_blueprint(video_bp)
        app.config['TESTING'] = True
        return app

    def test_extract_video_request_data_success(self, app):
        """Test successful request data extraction."""
        with app.test_request_context('/generate', method='POST', data={
            'prompt': 'Test prompt',
            'duration': '10',
            'width': '1920',
            'height': '1080',
            'model': 'sora-v1'
        }):
            from src.api.video_routes import _extract_video_request_data
            
            result = _extract_video_request_data()
            
            assert result["prompt"] == "Test prompt"
            assert result["duration"] == 10
            assert result["width"] == 1920
            assert result["height"] == 1080
            assert result["model"] == "sora-v1"

    def test_extract_video_request_data_minimal(self, app):
        """Test request data extraction with minimal data."""
        with app.test_request_context('/generate', method='POST', data={
            'prompt': 'Simple prompt'
        }):
            from src.api.video_routes import _extract_video_request_data
            
            result = _extract_video_request_data()
            
            assert result["prompt"] == "Simple prompt"
            assert result["duration"] is None
            assert result["width"] is None
            assert result["height"] is None
            assert result["model"] is None

    def test_extract_video_request_data_validation_error(self, app):
        """Test request data extraction with validation error."""
        with app.test_request_context('/generate', method='POST', data={
            'prompt': '',  # Empty prompt
            'duration': '100'  # Invalid duration
        }):
            from src.api.video_routes import _extract_video_request_data
            
            with pytest.raises(ValidationError):
                _extract_video_request_data()

    @patch('src.api.video_routes.job_repository')
    @patch('src.api.video_routes.process_video_generation')
    @patch('src.api.video_routes._get_queue_manager')
    def test_process_video_job_success(self, mock_queue_manager, mock_process, mock_job_repo, app):
        """Test successful video job processing."""
        with app.app_context():
            # Mock dependencies
            mock_job_repo.create_job.return_value = VideoJob(
                id="test-job-123",
                session_id="test-session-123",
                prompt="Test prompt",
                status="pending",
                created_at=datetime.now()
            )
            
            mock_queue_mgr_instance = Mock()
            mock_queue_mgr_instance.assign_queue_position.return_value = 3
            mock_queue_mgr_instance.get_queue_status.return_value = {
                'user_job_positions': [{'estimated_wait_minutes': 7}]
            }
            mock_queue_manager.return_value = mock_queue_mgr_instance
            
            from src.api.video_routes import _process_video_job
            
            request_data = {
                "prompt": "Test prompt",
                "duration": 10,
                "width": 1920,
                "height": 1080,
                "model": "sora-v1"
            }
            
            result = _process_video_job(request_data, "test-session-123")
            
            assert result["status"] == "pending"
            assert result["queue_position"] == 3
            assert result["estimated_wait"] == 7
            assert "job_id" in result
            
            # Verify job was created and queued
            mock_job_repo.create_job.assert_called_once()
            mock_process.delay.assert_called_once()

    def test_format_video_response(self, app):
        """Test video response formatting."""
        with app.app_context():
            from src.api.video_routes import _format_video_response
            
            job_result = {
                "job_id": "test-job-123",
                "status": "pending",
                "queue_position": 2,
                "estimated_wait": 5
            }
            
            response = _format_video_response(job_result)
            
            assert response.status_code == 200
            data = json.loads(response.data)
            
            assert data["success"] is True
            assert data["job_id"] == "test-job-123"
            assert data["status"] == "pending"
            assert data["queue_position"] == 2
            assert data["estimated_wait_minutes"] == 5
            assert "Queue position: 2" in data["message"]

    def test_handle_validation_error(self, app):
        """Test validation error handling."""
        with app.app_context():
            from src.api.video_routes import _handle_validation_error
            from pydantic import ValidationError
            
            # Create a validation error
            try:
                from src.api.models import VideoGenerationRequest
                VideoGenerationRequest(prompt="")  # Empty prompt
            except ValidationError as e:
                response, status_code = _handle_validation_error(e)
                
                assert status_code == 400
                data = json.loads(response.data)
                
                assert data["success"] is False
                assert data["message"] == "Request validation failed"
                assert "String should have at least 1 character" in data["error"]

    def test_handle_unexpected_error(self, app):
        """Test unexpected error handling."""
        with app.app_context():
            from src.api.video_routes import _handle_unexpected_error
            
            error = Exception("Database connection failed")
            response, status_code = _handle_unexpected_error(error)
            
            assert status_code == 500
            data = json.loads(response.data)
            
            assert data["success"] is False
            assert data["message"] == "Internal server error during video generation"
            assert data["error"] == "An unexpected error occurred"


@pytest.mark.unit
class TestVideoRouteIntegration:
    """Integration tests for video routes."""

    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = Flask(__name__)
        app.register_blueprint(video_bp)
        app.config['TESTING'] = True
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()

    def test_route_registration(self, app):
        """Test that all routes are properly registered."""
        with app.app_context():
            # Check that routes exist
            assert any(rule.rule == '/' for rule in app.url_map.iter_rules())
            assert any(rule.rule == '/config' for rule in app.url_map.iter_rules())
            assert any(rule.rule == '/generate' for rule in app.url_map.iter_rules())

    def test_blueprint_name(self, app):
        """Test blueprint name and prefix."""
        with app.app_context():
            blueprint = app.blueprints.get('video')
            assert blueprint is not None
            assert blueprint.name == 'video'

    @patch('src.api.video_routes.job_repository')
    @patch('src.api.video_routes.process_video_generation')
    @patch('src.api.video_routes._get_queue_manager')
    def test_complete_video_generation_workflow(self, mock_queue_manager, mock_process, mock_job_repo, client):
        """Test complete video generation workflow."""
        with client.application.app_context():
            g.session_id = "integration-test-session"
            
            # Mock all dependencies
            mock_job_repo.create_job.return_value = VideoJob(
                id="integration-job-123",
                session_id="integration-test-session",
                prompt="Integration test prompt",
                status="pending",
                created_at=datetime.now()
            )
            
            mock_queue_mgr_instance = Mock()
            mock_queue_mgr_instance.assign_queue_position.return_value = 1
            mock_queue_mgr_instance.get_queue_status.return_value = {
                'user_job_positions': [{'estimated_wait_minutes': 2}]
            }
            mock_queue_manager.return_value = mock_queue_mgr_instance
            
            # Simulate complete workflow
            response = client.post('/generate', data={
                'prompt': 'Integration test prompt',
                'duration': '8',
                'width': '1280',
                'height': '720',
                'model': 'sora-v1'
            })
            
            # Verify response
            assert response.status_code == 200
            data = json.loads(response.data)
            
            assert data["success"] is True
            assert data["job_id"] == "integration-job-123"
            assert data["status"] == "pending"
            assert data["queue_position"] == 1
            assert data["estimated_wait_minutes"] == 2
            
            # Verify all components were called
            mock_job_repo.create_job.assert_called_once()
            mock_process.delay.assert_called_once()
            mock_queue_mgr_instance.assign_queue_position.assert_called_once()
            mock_queue_mgr_instance.get_queue_status.assert_called_once()

    def test_error_handling_consistency(self, client):
        """Test consistent error handling across different scenarios."""
        # Test missing session
        response = client.post('/generate', data={'prompt': 'Test'})
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["success"] is False
        
        # Test validation error
        with client.application.app_context():
            g.session_id = "test-session"
            response = client.post('/generate', data={'prompt': ''})
            assert response.status_code == 400
            data = json.loads(response.data)
            assert data["success"] is False
            assert data["message"] == "Request validation failed"