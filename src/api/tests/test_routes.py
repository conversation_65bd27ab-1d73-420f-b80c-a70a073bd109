"""Unit and integration tests for API routes.

Tests Flask application routes including video generation, health monitoring,
metrics collection, and error handling scenarios. Covers both successful
operations and various failure conditions.
"""

import json
import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from src.core.models import VideoJob


@pytest.mark.integration
class TestAPIRoutes:
    """Test core API route functionality.

    Tests video generation endpoints including prompt validation,
    resolution presets, and queue management integration.
    """

    def test_index_route(self, client):
        """Test main page route."""
        response = client.get("/")
        assert response.status_code == 200
        assert b"Generate Video with Sora" in response.data

    @patch("src.session.manager.get_or_create_session")
    @patch("src.api.video_routes.process_video_generation")
    def test_generate_video_valid_prompt(self, mock_process_task, mock_session, client):
        """Test video generation with valid prompt."""
        # Mock session management
        mock_session.return_value = ("test-session-123", {"client_ip": "127.0.0.1"})

        # Mock the Celery task
        mock_process_task.delay.return_value = MagicMock()

        # Mock the queue manager
        with patch("src.api.video_routes._get_queue_manager") as mock_queue_manager:
            mock_queue_instance = MagicMock()
            mock_queue_manager.return_value = mock_queue_instance
            mock_queue_instance.assign_queue_position.return_value = 1
            mock_queue_instance.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }

            # Mock the job repository
            with patch("src.api.video_routes.job_repository") as mock_job_repo:
                job_id = str(uuid.uuid4())
                mock_job = VideoJob(
                    id=job_id,
                    prompt="A cat playing piano",
                    status="pending",
                    created_at=datetime.now(),
                    session_id="test-session-123",
                )
                mock_job_repo.create_job.return_value = mock_job

                response = client.post(
                    "/generate", data={"prompt": "A cat playing piano"}
                )

                assert response.status_code == 200
                data = json.loads(response.data)
                assert data["success"] is True
                assert "job_id" in data
                assert data["status"] == "pending"

    @patch("src.session.manager.get_or_create_session")
    def test_generate_video_empty_prompt(self, mock_session, client):
        """Test video generation with empty prompt."""
        # Mock session management
        mock_session.return_value = ("test-session-123", {"client_ip": "127.0.0.1"})

        response = client.post("/generate", data={"prompt": ""})

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["success"] is False
        assert "validation" in data["message"].lower()

    @patch("src.session.manager.get_or_create_session")
    def test_generate_video_long_prompt(self, mock_session, client):
        """Test video generation with overly long prompt."""
        # Mock session management
        mock_session.return_value = ("test-session-123", {"client_ip": "127.0.0.1"})

        long_prompt = "x" * 501
        response = client.post("/generate", data={"prompt": long_prompt})

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["success"] is False
        assert "validation" in data["message"].lower()

    @patch("src.api.video_routes.process_video_generation")
    @patch("src.session.manager.get_or_create_session")
    def test_generate_video_with_sd_resolution(
        self, mock_session, mock_process_task, client
    ):
        """Test video generation with SD resolution (848x480) - regression test for HTML5 step validation fix."""
        # Mock session management (this will be called by the middleware)
        mock_session.return_value = ("test-session-123", {"client_ip": "127.0.0.1"})

        # Mock the Celery task
        mock_process_task.delay.return_value = MagicMock()

        # Mock the queue manager and job repository like in previous test
        with patch("src.api.video_routes._get_queue_manager") as mock_queue_manager:
            mock_queue_instance = MagicMock()
            mock_queue_manager.return_value = mock_queue_instance
            mock_queue_instance.assign_queue_position.return_value = 1
            mock_queue_instance.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }

            with patch("src.api.video_routes.job_repository") as mock_job_repo:
                job_id = str(uuid.uuid4())
                mock_job = VideoJob(
                    id=job_id,
                    prompt="A cat playing piano in SD resolution",
                    status="pending",
                    created_at=datetime.now(),
                    session_id="test-session-123",
                )
                mock_job_repo.create_job.return_value = mock_job

                # Test the specific resolution that was causing HTML5 validation issues
                response = client.post(
                    "/generate",
                    data={
                        "prompt": "A cat playing piano in SD resolution",
                        "width": "848",  # The fixed width value (848 ÷ 16 = 53)
                        "height": "480",
                    },
                )

                assert response.status_code == 200
                data = json.loads(response.data)
                assert data["success"] is True
                assert "job_id" in data

    @patch("src.api.video_routes.process_video_generation")
    @patch("src.session.manager.get_or_create_session")
    def test_generate_video_with_hd_resolution(
        self, mock_session, mock_process_task, client
    ):
        """Test video generation with HD resolution (1280x720)."""
        # Mock session management (this will be called by the middleware)
        mock_session.return_value = ("test-session-123", {"client_ip": "127.0.0.1"})

        # Mock the Celery task
        mock_process_task.delay.return_value = MagicMock()

        # Mock the queue manager and job repository
        with patch("src.api.video_routes._get_queue_manager") as mock_queue_manager:
            mock_queue_instance = MagicMock()
            mock_queue_manager.return_value = mock_queue_instance
            mock_queue_instance.assign_queue_position.return_value = 1
            mock_queue_instance.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }

            with patch("src.api.video_routes.job_repository") as mock_job_repo:
                job_id = str(uuid.uuid4())
                mock_job = VideoJob(
                    id=job_id,
                    prompt="A cat playing piano in HD resolution",
                    status="pending",
                    created_at=datetime.now(),
                    session_id="test-session-123",
                )
                mock_job_repo.create_job.return_value = mock_job

                response = client.post(
                    "/generate",
                    data={
                        "prompt": "A cat playing piano in HD resolution",
                        "width": "1280",
                        "height": "720",
                    },
                )

                assert response.status_code == 200
                data = json.loads(response.data)
                assert data["success"] is True
                assert "job_id" in data

    @patch("src.api.video_routes.process_video_generation")
    @patch("src.session.manager.get_or_create_session")
    def test_generate_video_with_fullhd_resolution(
        self, mock_session, mock_process_task, client
    ):
        """Test video generation with Full HD resolution (1920x1080)."""
        # Mock session management (this will be called by the middleware)
        mock_session.return_value = ("test-session-123", {"client_ip": "127.0.0.1"})

        # Mock the Celery task
        mock_process_task.delay.return_value = MagicMock()

        # Mock the queue manager and job repository
        with patch("src.api.video_routes._get_queue_manager") as mock_queue_manager:
            mock_queue_instance = MagicMock()
            mock_queue_manager.return_value = mock_queue_instance
            mock_queue_instance.assign_queue_position.return_value = 1
            mock_queue_instance.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }

            with patch("src.api.video_routes.job_repository") as mock_job_repo:
                job_id = str(uuid.uuid4())
                mock_job = VideoJob(
                    id=job_id,
                    prompt="A cat playing piano in Full HD resolution",
                    status="pending",
                    created_at=datetime.now(),
                    session_id="test-session-123",
                )
                mock_job_repo.create_job.return_value = mock_job

                response = client.post(
                    "/generate",
                    data={
                        "prompt": "A cat playing piano in Full HD resolution",
                        "width": "1920",
                        "height": "1080",
                    },
                )

                assert response.status_code == 200
                data = json.loads(response.data)
                assert data["success"] is True
                assert "job_id" in data

    @patch("src.api.job_routes.job_repository")
    def test_get_job_status_valid_job(self, mock_job_repo, client):
        """Test getting status for valid job."""
        # Generate unique job ID to avoid database conflicts
        job_id = str(uuid.uuid4())

        # Mock job repository to return a job
        mock_job = VideoJob(
            id=job_id,
            prompt="A cat playing piano",
            status="succeeded",
            created_at=datetime.now(),
            completed_at=datetime.now(),
            session_id="test-session-123",
        )
        mock_job_repo.get_job_by_id.return_value = mock_job

        # Get status using the job ID
        response = client.get(f"/status/{job_id}")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["job_id"] == job_id
        assert data["status"] == "succeeded"

    def test_get_job_status_invalid_job(self, client):
        """Test getting status for non-existent job."""
        response = client.get("/status/nonexistent-job")

        assert response.status_code == 404
        data = json.loads(response.data)
        assert data["success"] is False
        assert "not found" in data["message"].lower()

    def test_serve_video_invalid_job(self, client):
        """Test serving video for non-existent job."""
        response = client.get("/video/nonexistent-job")

        assert response.status_code == 404
        data = json.loads(response.data)
        assert (
            "not found" in data["error"].lower()
            or "no job found" in data["error"].lower()
        )

    def test_download_video_invalid_job(self, client):
        """Test downloading video for non-existent job."""
        response = client.get("/download/nonexistent-job")

        assert response.status_code == 404
        data = json.loads(response.data)
        assert (
            "not found" in data["error"].lower()
            or "no job found" in data["error"].lower()
        )


@pytest.mark.integration
class TestHealthEndpoints:
    """Test health check API endpoints."""

    def test_health_overall_endpoint(self, client):
        """Test overall health check endpoint."""
        response = client.get("/health")

        # Should return some response (200 for healthy, 503 for unhealthy)
        assert response.status_code in [200, 500, 503]

        if response.status_code != 500:  # If not server error
            data = json.loads(response.data)
            assert "overall_status" in data
            assert "components" in data
            assert "timestamp" in data

    def test_health_database_endpoint(self, client):
        """Test database health check endpoint."""
        response = client.get("/health/database")

        # Should return some response
        assert response.status_code in [200, 500, 503]

        if response.status_code != 500:  # If not server error
            data = json.loads(response.data)
            assert "success" in data
            assert "data" in data
            # Check that the database health data is in the correct structure
            if data["success"]:
                assert "status" in data["data"]
                assert "timestamp" in data["data"]

    def test_health_azure_endpoint(self, client):
        """Test Azure API health check endpoint."""
        response = client.get("/health/azure")

        # Should return some response (might fail due to missing config in tests)
        assert response.status_code in [200, 500, 503]

        if response.status_code != 500:  # If not server error
            data = json.loads(response.data)
            if "data" in data:
                # Wrapped in APIResponse format
                assert "status" in data["data"]
                assert "timestamp" in data["data"]
            else:
                # Direct format
                assert "status" in data
                assert "timestamp" in data

    @patch("src.api.health_routes.health_check")
    def test_health_overall_success(self, mock_health_check, client):
        """Test overall health endpoint with successful health check."""
        mock_health_check.get_overall_health.return_value = {
            "overall_status": "healthy",
            "components": {
                "database": {"status": "healthy"},
                "azure_api": {"status": "reachable"},
                "disk_space": {"status": "healthy"},
                "job_queue": {"status": "healthy"},
            },
            "timestamp": "2023-01-01T12:00:00",
            "version": "1.0.0",
        }

        response = client.get("/health")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["overall_status"] == "healthy"
        assert "components" in data

    @patch("src.api.health_routes.health_check")
    def test_health_overall_unhealthy(self, mock_health_check, client):
        """Test overall health endpoint with unhealthy status."""
        mock_health_check.get_overall_health.return_value = {
            "overall_status": "unhealthy",
            "components": {
                "database": {"status": "unhealthy"},
                "azure_api": {"status": "unreachable"},
            },
            "timestamp": "2023-01-01T12:00:00",
        }

        response = client.get("/health")

        assert response.status_code == 503
        data = json.loads(response.data)
        assert data["overall_status"] == "unhealthy"

    @patch("src.api.health_routes.health_check")
    def test_health_overall_error(self, mock_health_check, client):
        """Test overall health endpoint with exception."""
        mock_health_check.get_overall_health.side_effect = Exception(
            "Health check failed"
        )

        response = client.get("/health")

        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
        assert "Health check failed" in data["error"]

    @patch("src.api.health_routes.health_check")
    def test_health_database_success(self, mock_health_check, client):
        """Test database health endpoint with successful check."""
        mock_health_check.check_database_health.return_value = {
            "status": "healthy",
            "response_time_ms": 25.5,
            "test_query_result": 1,
            "timestamp": "2023-01-01T12:00:00",
        }

        response = client.get("/health/database")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["status"] == "healthy"
        assert data["data"]["response_time_ms"] == 25.5

    @patch("src.api.health_routes.health_check")
    def test_health_database_unhealthy(self, mock_health_check, client):
        """Test database health endpoint with unhealthy status."""
        mock_health_check.check_database_health.return_value = {
            "status": "unhealthy",
            "error": "Connection timeout",
            "timestamp": "2023-01-01T12:00:00",
        }

        response = client.get("/health/database")

        assert response.status_code == 503
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["status"] == "unhealthy"
        assert "Connection timeout" in data["data"]["error"]

    @patch("src.api.health_routes.health_check")
    def test_health_azure_reachable(self, mock_health_check, client):
        """Test Azure health endpoint with reachable status."""
        mock_health_check.check_azure_api_health.return_value = {
            "status": "reachable",
            "response_time_ms": 150.0,
            "status_code": 200,
            "endpoint": "https://test.openai.azure.com/",
            "timestamp": "2023-01-01T12:00:00",
        }

        response = client.get("/health/azure")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["status"] == "reachable"
        assert data["data"]["response_time_ms"] == 150.0

    @patch("src.api.health_routes.health_check")
    def test_health_azure_unreachable(self, mock_health_check, client):
        """Test Azure health endpoint with unreachable status."""
        mock_health_check.check_azure_api_health.return_value = {
            "status": "unreachable",
            "error": "Connection failed",
            "endpoint": "https://test.openai.azure.com/",
            "timestamp": "2023-01-01T12:00:00",
        }

        response = client.get("/health/azure")

        assert response.status_code == 503
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["status"] == "unreachable"


@pytest.mark.integration
class TestMetricsEndpoints:
    """Test metrics API endpoints."""

    def test_metrics_system_endpoint(self, client):
        """Test system metrics endpoint."""
        response = client.get("/metrics")

        # Should return some response
        assert response.status_code in [200, 500]

        if response.status_code == 200:
            data = json.loads(response.data)
            # Should have main metric categories
            expected_keys = ["job_completion", "api_performance", "error_statistics"]
            for key in expected_keys:
                assert key in data or "error" in data  # Either metrics or error

    def test_metrics_jobs_endpoint(self, client):
        """Test job-specific metrics endpoint."""
        response = client.get("/metrics/jobs")

        # Should return some response
        assert response.status_code in [200, 500]

        if response.status_code == 200:
            data = json.loads(response.data)
            # Should have job-related metrics
            assert isinstance(data, dict)

    @patch("src.api.health_routes.metrics_collector")
    def test_metrics_system_success(self, mock_metrics_collector, client):
        """Test system metrics endpoint with successful data."""
        mock_metrics_collector.get_comprehensive_metrics.return_value = {
            "job_completion": {
                "period_hours": 24,
                "total_jobs": 10,
                "success_rate": 90.0,
                "average_duration_seconds": 45.5,
            },
            "api_performance": {
                "period_hours": 24,
                "total_requests": 50,
                "success_rate": 95.0,
                "average_response_time_ms": 125.0,
            },
            "error_statistics": {
                "period_hours": 24,
                "total_errors": 2,
                "error_rate_per_hour": 0.08,
            },
            "timestamp": "2023-01-01T12:00:00",
        }

        response = client.get("/metrics")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["job_completion"]["total_jobs"] == 10
        assert data["api_performance"]["success_rate"] == 95.0
        assert data["error_statistics"]["total_errors"] == 2

    @patch("src.api.health_routes.metrics_collector")
    def test_metrics_system_error(self, mock_metrics_collector, client):
        """Test system metrics endpoint with exception."""
        mock_metrics_collector.get_comprehensive_metrics.side_effect = Exception(
            "Metrics collection failed"
        )

        response = client.get("/metrics")

        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
        assert "Metrics collection failed" in data["error"]

    @patch("src.api.health_routes.health_check")
    @patch("src.api.health_routes.metrics_collector")
    def test_metrics_jobs_success(
        self, mock_metrics_collector, mock_health_check, client
    ):
        """Test job metrics endpoint with successful data."""
        mock_metrics_collector.get_job_completion_rate.return_value = {
            "period_hours": 24,
            "total_jobs": 15,
            "successful_jobs": 12,
            "failed_jobs": 3,
            "success_rate": 80.0,
            "error_rate": 20.0,
            "average_duration_seconds": 42.3,
        }
        mock_health_check.check_job_queue_health.return_value = {
            "status": "healthy",
            "statistics": {"total": 15},
        }

        response = client.get("/metrics/jobs")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["total_jobs"] == 15
        assert data["data"]["success_rate"] == 80.0
        assert data["data"]["failed_jobs"] == 3

    @patch("src.api.health_routes.metrics_collector")
    def test_metrics_jobs_error(self, mock_metrics_collector, client):
        """Test job metrics endpoint with exception."""
        mock_metrics_collector.get_job_completion_rate.side_effect = Exception(
            "Job metrics failed"
        )

        response = client.get("/metrics/jobs")

        assert response.status_code == 500
        data = json.loads(response.data)
        assert data["success"] is False
        assert "Job metrics failed" in data["error"]

    @patch("src.api.health_routes.health_check")
    @patch("src.api.health_routes.metrics_collector")
    def test_metrics_jobs_custom_hours(
        self, mock_metrics_collector, mock_health_check, client
    ):
        """Test job metrics endpoint with custom time period."""
        mock_metrics_collector.get_job_completion_rate.return_value = {
            "period_hours": 12,
            "total_jobs": 8,
            "success_rate": 87.5,
        }
        mock_health_check.check_job_queue_health.return_value = {
            "status": "healthy",
            "statistics": {"total": 8},
        }

        response = client.get("/metrics/jobs?hours=12")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["period_hours"] == 12
        assert data["data"]["total_jobs"] == 8

        # Verify the correct hours parameter was passed
        mock_metrics_collector.get_job_completion_rate.assert_called_with(12)

    def test_metrics_jobs_invalid_hours_parameter(self, client):
        """Test job metrics endpoint with invalid hours parameter."""
        response = client.get("/metrics/jobs?hours=invalid")

        # Should handle invalid parameter gracefully
        assert response.status_code in [200, 400, 500]


@pytest.mark.integration
class TestAPIErrorHandling:
    """Test API error handling scenarios."""

    def test_404_for_nonexistent_endpoints(self, client):
        """Test 404 responses for non-existent endpoints."""
        nonexistent_endpoints = [
            "/api/nonexistent",
            "/health/nonexistent",
            "/metrics/nonexistent",
            "/status/",  # Empty job ID
            "/video/",  # Empty job ID
            "/download/",  # Empty job ID
        ]

        for endpoint in nonexistent_endpoints:
            response = client.get(endpoint)
            assert response.status_code == 404

    def test_method_not_allowed(self, client):
        """Test method not allowed responses."""
        # Test wrong HTTP methods on endpoints
        test_cases = [
            ("POST", "/health"),
            ("PUT", "/metrics"),
            ("DELETE", "/status/test-job"),
            ("GET", "/generate"),  # Should be POST
        ]

        for method, endpoint in test_cases:
            if method == "POST":
                response = client.post(endpoint)
            elif method == "PUT":
                response = client.put(endpoint)
            elif method == "DELETE":
                response = client.delete(endpoint)
            else:
                response = client.get(endpoint)

            # Should be either 405 (Method Not Allowed) or handle gracefully
            assert response.status_code in [200, 400, 405, 500]

    @patch("src.api.health_routes.health_check")
    @patch("src.api.health_routes.metrics_collector")
    def test_concurrent_requests_handling(self, mock_metrics, mock_health, client):
        """Test API handles concurrent requests properly."""
        # Mock successful responses
        mock_health.get_overall_health.return_value = {"overall_status": "healthy"}
        mock_metrics.get_comprehensive_metrics.return_value = {"status": "ok"}

        # Make multiple concurrent-style requests
        endpoints = [
            "/health",
            "/metrics",
            "/health/database",
            "/health/azure",
            "/metrics/jobs",
        ]

        for endpoint in endpoints:
            response = client.get(endpoint)
            # Should handle all requests without issues
            assert response.status_code in [200, 500, 503]


@pytest.mark.integration
class TestAPIResponseFormat:
    """Test API response format consistency."""

    @patch("src.api.health_routes.health_check")
    def test_health_response_format_consistency(self, mock_health_check, client):
        """Test health endpoints return consistent format."""
        mock_health_check.get_overall_health.return_value = {
            "overall_status": "healthy",
            "timestamp": "2023-01-01T12:00:00",
        }
        mock_health_check.check_database_health.return_value = {
            "status": "healthy",
            "timestamp": "2023-01-01T12:00:00",
        }
        mock_health_check.check_azure_api_health.return_value = {
            "status": "reachable",
            "timestamp": "2023-01-01T12:00:00",
        }

        endpoints = ["/health", "/health/database", "/health/azure"]

        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 200

            # Check response is valid JSON
            data = json.loads(response.data)
            assert isinstance(data, dict)

            # Check has timestamp (either direct or in data)
            assert "timestamp" in data or (
                "data" in data and "timestamp" in data["data"]
            )

    @patch("src.api.health_routes.health_check")
    @patch("src.api.health_routes.metrics_collector")
    def test_metrics_response_format_consistency(
        self, mock_metrics, mock_health_check, client
    ):
        """Test metrics endpoints return consistent format."""
        mock_metrics.get_comprehensive_metrics.return_value = {
            "timestamp": "2023-01-01T12:00:00",
            "job_completion": {},
            "api_performance": {},
        }
        mock_metrics.get_job_completion_rate.return_value = {
            "period_hours": 24,
            "total_jobs": 0,
        }
        mock_health_check.check_job_queue_health.return_value = {
            "status": "healthy",
            "statistics": {"total": 0},
        }

        endpoints = ["/metrics", "/metrics/jobs"]

        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 200

            # Check response is valid JSON
            data = json.loads(response.data)
            assert isinstance(data, dict)

    def test_error_response_format_consistency(self, client):
        """Test error responses have consistent format."""
        # Test with endpoints that might generate errors
        response = client.get("/status/invalid-job-id-format")

        if response.status_code in [404, 500]:
            data = json.loads(response.data)

            # Error responses should have consistent structure
            if "error" in data:
                assert isinstance(data["error"], str)
            if "message" in data:
                assert isinstance(data["message"], str)


@pytest.mark.integration
class TestUIConfigEndpoint:
    """Test UI configuration endpoint for Phase 2 enhancements."""

    @patch("src.api.video_routes.ConfigurationFactory")
    def test_get_ui_config_success(self, mock_factory, client):
        """Test successful UI configuration retrieval."""
        # Mock the configuration factory
        mock_video_config = MagicMock()
        mock_video_config.get_defaults_dict.return_value = {
            "width": 1280,
            "height": 720,
            "duration": 2,
            "model": "sora",
        }
        mock_video_config.get_constraints_dict.return_value = {
            "min_width": 480,
            "max_width": 1920,
            "min_height": 480,
            "max_height": 1920,
            "min_duration": 1,
            "max_duration": 20,
            "max_prompt_length": 500,
        }
        mock_factory.get_video_config.return_value = mock_video_config

        response = client.get("/config")

        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["success"] is True
        assert data["message"] == "UI configuration retrieved"
        assert "defaults" in data["data"]
        assert "constraints" in data["data"]

        # Check defaults
        defaults = data["data"]["defaults"]
        assert defaults["width"] == 1280
        assert defaults["height"] == 720
        assert defaults["duration"] == 2
        assert defaults["model"] == "sora"

        # Check constraints
        constraints = data["data"]["constraints"]
        assert constraints["min_width"] == 480
        assert constraints["max_width"] == 1920
        assert constraints["min_duration"] == 1
        assert constraints["max_duration"] == 20

    @patch("src.api.video_routes.ConfigurationFactory")
    def test_get_ui_config_error(self, mock_factory, client):
        """Test UI configuration endpoint with error."""
        mock_factory.get_video_config.side_effect = Exception("Config error")

        response = client.get("/config")

        assert response.status_code == 500
        data = json.loads(response.data)

        assert data["success"] is False
        assert data["message"] == "Failed to retrieve UI configuration"
        assert "Config error" in data["error"]
