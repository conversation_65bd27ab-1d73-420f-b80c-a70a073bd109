"""Unit tests for API routes registration module.

Tests blueprint registration functionality, environment-based debug endpoint
registration, and production security validation.
"""

import os
from unittest.mock import patch

import pytest
from flask import Flask

from src.api.routes import register_api_blueprints


class TestBlueprintRegistration:
    """Test API blueprint registration functionality."""

    def test_register_api_blueprints_all_blueprints(self):
        """Test that all main API blueprints are registered."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Mock logger to verify logging calls
        with patch("src.api.routes.logger") as mock_logger:
            register_api_blueprints(app)

            # Verify logging calls
            mock_logger.info.assert_any_call("🔧 Registering API blueprints")
            mock_logger.info.assert_any_call(
                "✅ API blueprints registered successfully"
            )

        # Check that blueprints are registered by checking registered blueprint names
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]

        # These are the main blueprints that should always be registered
        expected_blueprints = ["video", "health", "job", "file"]
        for bp_name in expected_blueprints:
            assert bp_name in registered_blueprints

    def test_register_api_blueprints_production_environment(self):
        """Test blueprint registration in production environment (no debug endpoints)."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Set production environment
        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify security logging
                mock_logger.info.assert_any_call(
                    "✅ SECURITY: Debug blueprint DISABLED in production mode"
                )
                mock_logger.info.assert_any_call(
                    "✅ ENVIRONMENT: production - Debug endpoints are NOT accessible"
                )

        # Check that debug blueprint is NOT registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" not in registered_blueprints

    def test_register_api_blueprints_development_environment(self):
        """Test blueprint registration in development environment (with debug endpoints)."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Set development environment
        with patch.dict(os.environ, {"FLASK_ENV": "development"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify warning logging for debug blueprint registration
                mock_logger.warning.assert_any_call(
                    "🚨 DEBUG: Debug blueprint registered (development mode)"
                )
                mock_logger.warning.assert_any_call(
                    "🔧 DEBUG ENDPOINTS: /debug/azure-config available"
                )

        # Check that debug blueprint IS registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" in registered_blueprints

    def test_register_api_blueprints_testing_environment(self):
        """Test blueprint registration in testing environment (with debug endpoints)."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Set testing environment
        with patch.dict(os.environ, {"FLASK_ENV": "testing"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify debug blueprint is registered in testing
                mock_logger.warning.assert_any_call(
                    "🚨 DEBUG: Debug blueprint registered (development mode)"
                )

        # Check that debug blueprint IS registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" in registered_blueprints

    def test_register_api_blueprints_dev_environment_variant(self):
        """Test blueprint registration with 'dev' environment variant."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Set 'dev' environment (short form)
        with patch.dict(os.environ, {"FLASK_ENV": "dev"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify debug blueprint is registered
                mock_logger.warning.assert_any_call(
                    "🚨 DEBUG: Debug blueprint registered (development mode)"
                )

        # Check that debug blueprint IS registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" in registered_blueprints

    def test_register_api_blueprints_test_environment_variant(self):
        """Test blueprint registration with 'test' environment variant."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Set 'test' environment (short form)
        with patch.dict(os.environ, {"FLASK_ENV": "test"}):
            register_api_blueprints(app)

        # Check that debug blueprint IS registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" in registered_blueprints

    def test_register_api_blueprints_no_flask_env(self):
        """Test blueprint registration when FLASK_ENV is not set (defaults to production)."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Remove FLASK_ENV from environment if it exists
        with patch.dict(os.environ, {}, clear=False):
            if "FLASK_ENV" in os.environ:
                del os.environ["FLASK_ENV"]

            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Should default to production behavior
                mock_logger.info.assert_any_call(
                    "✅ SECURITY: Debug blueprint DISABLED in production mode"
                )

        # Check that debug blueprint is NOT registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" not in registered_blueprints

    def test_register_api_blueprints_unknown_environment(self):
        """Test blueprint registration with unknown environment (defaults to production)."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Set unknown environment
        with patch.dict(os.environ, {"FLASK_ENV": "unknown-env"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Should default to production behavior (secure by default)
                mock_logger.info.assert_any_call(
                    "✅ SECURITY: Debug blueprint DISABLED in production mode"
                )
                mock_logger.info.assert_any_call(
                    "✅ ENVIRONMENT: unknown-env - Debug endpoints are NOT accessible"
                )

        # Check that debug blueprint is NOT registered
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" not in registered_blueprints

    def test_register_api_blueprints_case_insensitive_environment(self):
        """Test that environment detection is case-insensitive."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Test uppercase development environment
        with patch.dict(os.environ, {"FLASK_ENV": "DEVELOPMENT"}):
            register_api_blueprints(app)

        # Check that debug blueprint IS registered (case insensitive)
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" in registered_blueprints

    def test_register_api_blueprints_mixed_case_environment(self):
        """Test environment detection with mixed case."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # Test mixed case testing environment
        with patch.dict(os.environ, {"FLASK_ENV": "Testing"}):
            register_api_blueprints(app)

        # Check that debug blueprint IS registered (case insensitive)
        registered_blueprints = [bp.name for bp in app.iter_blueprints()]
        assert "debug" in registered_blueprints


class TestEnvironmentDetection:
    """Test environment detection logic."""

    @pytest.mark.parametrize(
        "env_value,expected_debug",
        [
            ("development", True),
            ("dev", True),
            ("testing", True),
            ("test", True),
            ("DEVELOPMENT", True),  # Case insensitive
            ("DEV", True),
            ("TESTING", True),
            ("TEST", True),
            ("production", False),
            ("prod", False),
            ("staging", False),
            ("unknown", False),
            ("", False),  # Empty string defaults to production
        ],
    )
    def test_environment_debug_blueprint_logic(self, env_value, expected_debug):
        """Test environment-based debug blueprint registration with various values."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        with patch.dict(os.environ, {"FLASK_ENV": env_value}):
            register_api_blueprints(app)

        registered_blueprints = [bp.name for bp in app.iter_blueprints()]

        if expected_debug:
            assert "debug" in registered_blueprints
        else:
            assert "debug" not in registered_blueprints


class TestLoggingBehavior:
    """Test logging behavior during blueprint registration."""

    def test_logging_calls_sequence(self):
        """Test the correct sequence of logging calls."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify logging call sequence
                expected_calls = [
                    "🔧 Registering API blueprints",
                    "✅ SECURITY: Debug blueprint DISABLED in production mode",
                    "✅ ENVIRONMENT: production - Debug endpoints are NOT accessible",
                    "✅ API blueprints registered successfully",
                ]

                # Check that all expected log messages were called
                for expected_message in expected_calls:
                    mock_logger.info.assert_any_call(expected_message)

    def test_debug_logging_calls_sequence(self):
        """Test logging calls when debug blueprint is registered."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        with patch.dict(os.environ, {"FLASK_ENV": "development"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify warning logging for debug registration
                mock_logger.warning.assert_any_call(
                    "🚨 DEBUG: Debug blueprint registered (development mode)"
                )
                mock_logger.warning.assert_any_call(
                    "🔧 DEBUG ENDPOINTS: /debug/azure-config available"
                )

                # Verify info logging for start and completion
                mock_logger.info.assert_any_call("🔧 Registering API blueprints")
                mock_logger.info.assert_any_call(
                    "✅ API blueprints registered successfully"
                )


class TestBlueprintIntegration:
    """Test blueprint registration integration."""

    def test_blueprint_import_success(self):
        """Test that all required blueprints can be imported successfully."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # This should not raise any import errors
        try:
            register_api_blueprints(app)
            success = True
        except ImportError:
            success = False

        assert success, "Blueprint imports should succeed"

    def test_debug_blueprint_conditional_import(self):
        """Test that debug blueprint is only imported when needed."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        # In production, debug blueprint should not be imported
        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            register_api_blueprints(app)

            # The debug blueprint should not be registered in production
            registered_blueprints = [bp.name for bp in app.iter_blueprints()]
            assert "debug" not in registered_blueprints

    def test_blueprint_registration_idempotent(self):
        """Test that blueprint registration behavior is consistent."""
        # Create separate app instances to test registration
        app1 = Flask(__name__)
        app1.config["TESTING"] = True

        app2 = Flask(__name__)
        app2.config["TESTING"] = True

        # Register blueprints on both apps
        register_api_blueprints(app1)
        register_api_blueprints(app2)

        # Both should have the same blueprint count
        blueprint_count_1 = len(list(app1.iter_blueprints()))
        blueprint_count_2 = len(list(app2.iter_blueprints()))

        assert blueprint_count_1 == blueprint_count_2


class TestSecurityValidation:
    """Test security-related functionality."""

    def test_production_security_logging(self):
        """Test that security-related messages are logged in production."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        with patch.dict(os.environ, {"FLASK_ENV": "production"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify security logging
                security_messages = [
                    call.args[0]
                    for call in mock_logger.info.call_args_list
                    if "SECURITY" in call.args[0]
                ]

                assert len(security_messages) >= 1
                assert any(
                    "SECURITY: Debug blueprint DISABLED" in msg
                    for msg in security_messages
                )

    def test_debug_security_warnings(self):
        """Test that security warnings are logged when debug endpoints are enabled."""
        app = Flask(__name__)
        app.config["TESTING"] = True

        with patch.dict(os.environ, {"FLASK_ENV": "development"}):
            with patch("src.api.routes.logger") as mock_logger:
                register_api_blueprints(app)

                # Verify security warning logging
                warning_messages = [
                    call.args[0] for call in mock_logger.warning.call_args_list
                ]

                assert len(warning_messages) >= 2
                assert any(
                    "DEBUG: Debug blueprint registered" in msg
                    for msg in warning_messages
                )
                assert any("DEBUG ENDPOINTS:" in msg for msg in warning_messages)
