"""
Comprehensive performance tests for C2 Provider Selection UI.

Tests provider switching speed (<200ms), form responsiveness (<100ms),
concurrent user handling, load testing, and performance benchmarks.
"""

import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import MagicMock, patch

import pytest

from src.api.provider_routes import provider_bp


class TestC2ProviderSwitchingPerformance:
    """Test C2 provider switching performance requirements (<200ms)."""

    @patch("src.api.provider_routes.get_provider_factory")
    def test_provider_list_response_time(self, mock_factory_getter, c2_client):
        """Test provider list endpoint response time < 200ms."""
        # Mock fast provider factory
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = [
            "azure_sora",
            "google_veo3",
        ]

        mock_azure_provider = MagicMock()
        mock_azure_provider.supported_features = {"text_to_video": True}

        mock_veo3_provider = MagicMock()
        mock_veo3_provider.supported_features = {
            "text_to_video": True,
            "image_to_video": True,
        }

        mock_factory.create_provider.side_effect = lambda name: (
            mock_azure_provider if name == "azure_sora" else mock_veo3_provider
        )
        mock_factory_getter.return_value = mock_factory

        # Measure response time
        start_time = time.perf_counter()
        response = c2_client.get("/providers")
        end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 200, (
            f"Provider list response took {response_time_ms:.2f}ms (>200ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True
        assert len(data["data"]["providers"]) == 2

    @patch("src.api.provider_routes.get_provider_factory")
    def test_provider_capabilities_response_time(self, mock_factory_getter, c2_client):
        """Test individual provider capabilities endpoint response time < 200ms."""
        # Mock provider factory
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = ["azure_sora"]

        mock_provider = MagicMock()
        mock_provider.supported_features = {
            "text_to_video": True,
            "image_to_video": False,
            "audio_generation": True,
            "custom_duration": True,
        }
        mock_factory.create_provider.return_value = mock_provider
        mock_factory_getter.return_value = mock_factory

        # Measure response time
        start_time = time.perf_counter()
        response = c2_client.get("/providers/azure_sora/capabilities")
        end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 200, (
            f"Provider capabilities response took {response_time_ms:.2f}ms (>200ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True
        assert data["data"]["provider"] == "azure_sora"

    @patch("src.api.provider_routes._handle_async_request")
    def test_c2_status_response_time(self, mock_async_handler, c2_client):
        """Test C2 status endpoint response time < 200ms."""
        # Mock fast async response
        mock_async_handler.return_value = {
            "provider_statuses": {
                "azure_sora": {
                    "provider_id": "azure_sora",
                    "status": "online",
                    "response_time_ms": 150.0,
                }
            },
            "environment": {"type": "local"},
            "available_providers": ["azure_sora"],
            "default_provider": "azure_sora",
            "rate_limiting": {},
            "f4_system_info": {},
        }

        # Measure response time
        start_time = time.perf_counter()
        response = c2_client.get("/c2/status")
        end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 200, (
            f"C2 status response took {response_time_ms:.2f}ms (>200ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    @patch("src.api.provider_routes.broadcast_provider_recommendation_change")
    def test_c2_preference_update_response_time(
        self,
        mock_broadcast,
        mock_session_manager_getter,
        mock_get_session_id,
        c2_client,
    ):
        """Test C2 preference update response time < 200ms."""
        mock_get_session_id.return_value = "test-session"

        # Mock fast session manager
        mock_session_manager = MagicMock()
        mock_session_manager.set_preferred_provider.return_value = True
        mock_session_manager.set_fallback_providers.return_value = True
        mock_session_manager.set_performance_priority.return_value = True
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3"
        }
        mock_session_manager_getter.return_value = mock_session_manager

        preference_data = {
            "preferred_provider": "google_veo3",
            "performance_priority": "speed",
        }

        # Measure response time
        start_time = time.perf_counter()
        response = c2_client.post(
            "/c2/preferences",
            data=json.dumps(preference_data),
            content_type="application/json",
        )
        end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 200, (
            f"C2 preference update took {response_time_ms:.2f}ms (>200ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True


class TestC2FormResponsivenessPerformance:
    """Test C2 form responsiveness performance requirements (<100ms)."""

    @patch("src.api.provider_routes.get_provider_configuration_manager")
    def test_c2_environment_config_response_time(
        self, mock_config_manager_getter, c2_client
    ):
        """Test C2 environment configuration response time < 100ms."""
        # Mock fast configuration manager
        mock_config_manager = MagicMock()
        mock_config_manager.get_environment_configuration_summary.return_value = {
            "environment": {"type": "local"},
            "providers": {"available_providers": ["azure_sora"]},
            "f4_system": {"deployment_detection": "local"},
        }
        mock_config_manager_getter.return_value = mock_config_manager

        # Measure response time
        start_time = time.perf_counter()
        response = c2_client.get("/c2/environment")
        end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 100, (
            f"C2 environment config took {response_time_ms:.2f}ms (>100ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_c2_preferences_retrieval_response_time(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test C2 preferences retrieval response time < 100ms."""
        mock_get_session_id.return_value = "test-session"

        # Mock fast session manager
        mock_session_manager = MagicMock()
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora"
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 10
        }
        mock_session_manager_getter.return_value = mock_session_manager

        # Measure response time
        start_time = time.perf_counter()
        response = c2_client.get("/c2/preferences")
        end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 100, (
            f"C2 preferences retrieval took {response_time_ms:.2f}ms (>100ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_c2_throttle_response_time(self, mock_rate_limiter_getter, c2_client):
        """Test C2 provider throttle response time < 100ms."""
        # Mock fast rate limiter
        mock_rate_limiter = MagicMock()
        mock_rate_limiter.manually_throttle_provider.return_value = True
        mock_rate_limiter.get_provider_rate_status.return_value = {
            "provider_id": "azure_sora"
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        with patch("src.api.provider_routes.broadcast_provider_status_change"):
            throttle_data = {
                "provider_id": "azure_sora",
                "throttle": True,
                "reason": "Performance test",
            }

            # Measure response time
            start_time = time.perf_counter()
            response = c2_client.post(
                "/c2/throttle",
                data=json.dumps(throttle_data),
                content_type="application/json",
            )
            end_time = time.perf_counter()

        response_time_ms = (end_time - start_time) * 1000

        assert response.status_code == 200
        assert response_time_ms < 100, (
            f"C2 throttle response took {response_time_ms:.2f}ms (>100ms)"
        )

        data = json.loads(response.data)
        assert data["success"] is True


class TestC2ConcurrentUserPerformance:
    """Test C2 performance under concurrent user load."""

    @patch("src.api.provider_routes.get_provider_factory")
    def test_concurrent_provider_list_requests(self, mock_factory_getter, c2_client):
        """Test concurrent provider list requests performance."""
        # Mock provider factory
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = [
            "azure_sora",
            "google_veo3",
        ]

        mock_provider = MagicMock()
        mock_provider.supported_features = {"text_to_video": True}
        mock_factory.create_provider.return_value = mock_provider
        mock_factory_getter.return_value = mock_factory

        def make_request():
            start_time = time.perf_counter()
            response = c2_client.get("/providers")
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            return response.status_code, response_time_ms

        # Test with 10 concurrent requests
        num_concurrent_requests = 10

        with ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
            future_to_request = {
                executor.submit(make_request): i for i in range(num_concurrent_requests)
            }

            results = []
            for future in as_completed(future_to_request):
                status_code, response_time_ms = future.result()
                results.append((status_code, response_time_ms))

        # Verify all requests succeeded
        assert len(results) == num_concurrent_requests
        for status_code, response_time_ms in results:
            assert status_code == 200
            assert response_time_ms < 500, (
                f"Concurrent request took {response_time_ms:.2f}ms (>500ms)"
            )

        # Calculate average response time
        avg_response_time = sum(time for _, time in results) / len(results)
        assert avg_response_time < 300, (
            f"Average concurrent response time: {avg_response_time:.2f}ms (>300ms)"
        )

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_concurrent_session_isolation_performance(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test concurrent requests with different sessions maintain isolation."""
        mock_session_manager = MagicMock()
        mock_session_manager_getter.return_value = mock_session_manager

        def make_session_request(session_id):
            # Mock different session ID for each request
            with patch(
                "src.api.provider_routes._get_session_id", return_value=session_id
            ):
                mock_session_manager.get_provider_preferences.return_value = {
                    "preferred_provider": f"provider_for_{session_id}",
                    "session_id": session_id,
                }
                mock_session_manager.get_provider_usage_stats.return_value = {
                    "total_requests": 1
                }

                start_time = time.perf_counter()
                response = c2_client.get("/c2/preferences")
                end_time = time.perf_counter()

                response_time_ms = (end_time - start_time) * 1000
                data = (
                    json.loads(response.data) if response.status_code == 200 else None
                )
                return response.status_code, response_time_ms, data

        # Test with multiple concurrent sessions
        session_ids = [f"session_{i}" for i in range(5)]

        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_session = {
                executor.submit(make_session_request, session_id): session_id
                for session_id in session_ids
            }

            results = {}
            for future in as_completed(future_to_session):
                session_id = future_to_session[future]
                status_code, response_time_ms, data = future.result()
                results[session_id] = (status_code, response_time_ms, data)

        # Verify all sessions succeeded with proper isolation
        for session_id, (status_code, response_time_ms, data) in results.items():
            assert status_code == 200
            assert response_time_ms < 200, (
                f"Session {session_id} took {response_time_ms:.2f}ms"
            )
            assert data["success"] is True


class TestC2LoadTestingPerformance:
    """Test C2 performance under various load conditions."""

    @patch("src.api.provider_routes._handle_async_request")
    def test_sustained_load_c2_status_requests(self, mock_async_handler, c2_client):
        """Test sustained load on C2 status endpoint."""
        # Mock consistent response
        mock_async_handler.return_value = {
            "provider_statuses": {},
            "environment": {"type": "local"},
            "available_providers": ["azure_sora"],
            "default_provider": "azure_sora",
            "rate_limiting": {},
            "f4_system_info": {},
        }

        def make_sustained_requests(duration_seconds=2):
            start_time = time.perf_counter()
            end_time = start_time + duration_seconds

            request_count = 0
            response_times = []

            while time.perf_counter() < end_time:
                request_start = time.perf_counter()
                response = c2_client.get("/c2/status")
                request_end = time.perf_counter()

                request_time_ms = (request_end - request_start) * 1000
                response_times.append(request_time_ms)
                request_count += 1

                assert response.status_code == 200

                # Small delay to avoid overwhelming
                time.sleep(0.01)

            return request_count, response_times

        # Run sustained load test
        request_count, response_times = make_sustained_requests(duration_seconds=2)

        # Calculate performance metrics
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)

        # Performance assertions
        assert request_count > 50, (
            f"Only {request_count} requests in 2 seconds (expected >50)"
        )
        assert avg_response_time < 250, (
            f"Average response time: {avg_response_time:.2f}ms (>250ms)"
        )
        assert max_response_time < 500, (
            f"Max response time: {max_response_time:.2f}ms (>500ms)"
        )
        assert min_response_time < 100, (
            f"Min response time: {min_response_time:.2f}ms (>100ms)"
        )

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_throttle_operation_performance_under_load(
        self, mock_rate_limiter_getter, c2_client
    ):
        """Test throttle operations performance under load."""
        mock_rate_limiter = MagicMock()
        mock_rate_limiter.manually_throttle_provider.return_value = True
        mock_rate_limiter.get_provider_rate_status.return_value = {
            "provider_id": "azure_sora"
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        def make_throttle_request(throttle_state):
            with patch("src.api.provider_routes.broadcast_provider_status_change"):
                throttle_data = {
                    "provider_id": "azure_sora",
                    "throttle": throttle_state,
                    "reason": f"Load test throttle {throttle_state}",
                }

                start_time = time.perf_counter()
                response = c2_client.post(
                    "/c2/throttle",
                    data=json.dumps(throttle_data),
                    content_type="application/json",
                )
                end_time = time.perf_counter()

                response_time_ms = (end_time - start_time) * 1000
                return response.status_code, response_time_ms

        # Test alternating throttle/unthrottle operations
        operations = [True, False] * 10  # 20 operations total
        response_times = []

        for throttle_state in operations:
            status_code, response_time_ms = make_throttle_request(throttle_state)
            assert status_code == 200
            response_times.append(response_time_ms)

        # Performance validation
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)

        assert avg_response_time < 150, (
            f"Average throttle response time: {avg_response_time:.2f}ms (>150ms)"
        )
        assert max_response_time < 300, (
            f"Max throttle response time: {max_response_time:.2f}ms (>300ms)"
        )


class TestC2PerformanceBenchmarks:
    """Test C2 performance benchmarks and thresholds."""

    @patch("src.api.provider_routes.get_provider_factory")
    def test_provider_switching_benchmark(self, mock_factory_getter, c2_client):
        """Benchmark provider switching operations."""
        # Mock provider factory with multiple providers
        mock_factory = MagicMock()
        mock_factory.get_available_providers.return_value = [
            "azure_sora",
            "google_veo3",
        ]

        providers = {
            "azure_sora": MagicMock(supported_features={"text_to_video": True}),
            "google_veo3": MagicMock(
                supported_features={"text_to_video": True, "image_to_video": True}
            ),
        }

        mock_factory.create_provider.side_effect = lambda name: providers[name]
        mock_factory_getter.return_value = mock_factory

        # Benchmark multiple provider operations
        operations = [
            ("/providers", "GET"),
            ("/providers/azure_sora/capabilities", "GET"),
            ("/providers/google_veo3/capabilities", "GET"),
            ("/providers", "GET"),  # Repeat to test caching/optimization
        ]

        benchmark_results = {}

        for endpoint, method in operations:
            start_time = time.perf_counter()

            if method == "GET":
                response = c2_client.get(endpoint)

            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000

            assert response.status_code == 200
            benchmark_results[endpoint] = response_time_ms

        # Validate benchmark results
        assert benchmark_results["/providers"] < 200, "Provider list benchmark failed"
        assert benchmark_results["/providers/azure_sora/capabilities"] < 200, (
            "Azure capabilities benchmark failed"
        )
        assert benchmark_results["/providers/google_veo3/capabilities"] < 200, (
            "Veo3 capabilities benchmark failed"
        )

        # Verify performance consistency
        provider_list_times = [
            benchmark_results[k] for k in benchmark_results if k == "/providers"
        ]
        if len(provider_list_times) > 1:
            time_variance = max(provider_list_times) - min(provider_list_times)
            assert time_variance < 50, (
                f"Provider list performance variance too high: {time_variance:.2f}ms"
            )

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    @patch("src.api.provider_routes.broadcast_provider_recommendation_change")
    def test_preference_update_benchmark(
        self,
        mock_broadcast,
        mock_session_manager_getter,
        mock_get_session_id,
        c2_client,
    ):
        """Benchmark preference update operations."""
        mock_get_session_id.return_value = "benchmark-session"

        mock_session_manager = MagicMock()
        mock_session_manager.set_preferred_provider.return_value = True
        mock_session_manager.set_fallback_providers.return_value = True
        mock_session_manager.set_performance_priority.return_value = True
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3"
        }
        mock_session_manager_getter.return_value = mock_session_manager

        # Benchmark different preference update scenarios
        test_scenarios = [
            {"preferred_provider": "azure_sora"},
            {"preferred_provider": "google_veo3", "performance_priority": "speed"},
            {"fallback_providers": ["azure_sora", "google_veo3"]},
            {
                "preferred_provider": "azure_sora",
                "fallback_providers": ["google_veo3"],
                "performance_priority": "quality",
            },
        ]

        benchmark_results = []

        for i, preference_data in enumerate(test_scenarios):
            start_time = time.perf_counter()

            response = c2_client.post(
                "/c2/preferences",
                data=json.dumps(preference_data),
                content_type="application/json",
            )

            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000

            assert response.status_code == 200
            benchmark_results.append(response_time_ms)

        # Validate benchmark results
        avg_update_time = sum(benchmark_results) / len(benchmark_results)
        max_update_time = max(benchmark_results)

        assert avg_update_time < 150, (
            f"Average preference update time: {avg_update_time:.2f}ms (>150ms)"
        )
        assert max_update_time < 200, (
            f"Max preference update time: {max_update_time:.2f}ms (>200ms)"
        )

        # Verify all updates completed within acceptable range
        for i, response_time in enumerate(benchmark_results):
            assert response_time < 250, (
                f"Scenario {i} took {response_time:.2f}ms (>250ms)"
            )


@pytest.fixture
def c2_client():
    """Create test client for C2 performance testing."""
    from flask import Flask

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.secret_key = "test-secret-key"

    # Register provider routes blueprint
    app.register_blueprint(provider_bp)

    with app.test_client() as client:
        with app.app_context():
            yield client
