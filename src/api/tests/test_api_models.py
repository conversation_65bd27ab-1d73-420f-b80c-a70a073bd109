"""Tests for API Pydantic models - Request and response validation."""

import json
from datetime import datetime
from unittest.mock import patch

import pytest
from pydantic import ValidationError

from src.api.models import (
    VideoGenerationRequest,
    VideoGenerationResponse,
    HealthCheckResponse,
    JobStatusResponse,
    QueueStatusResponse,
    SessionInfoResponse,
    ErrorResponse,
    MetricsResponse,
)


@pytest.mark.unit
class TestVideoGenerationRequest:
    """Test VideoGenerationRequest model validation."""

    def test_valid_basic_request(self):
        """Test valid request with minimal required fields."""
        request = VideoGenerationRequest(prompt="A beautiful sunset")
        
        assert request.prompt == "A beautiful sunset"
        assert request.duration is None
        assert request.width is None
        assert request.height is None
        assert request.model is None

    def test_valid_full_request(self):
        """Test valid request with all fields."""
        request = VideoGenerationRequest(
            prompt="A beautiful sunset over mountains",
            duration=10,
            width=1920,
            height=1080,
            model="sora-v1"
        )
        
        assert request.prompt == "A beautiful sunset over mountains"
        assert request.duration == 10
        assert request.width == 1920
        assert request.height == 1080
        assert request.model == "sora-v1"

    def test_prompt_validation_empty(self):
        """Test prompt validation with empty string."""
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="")
        
        errors = exc_info.value.errors()
        assert len(errors) == 1
        assert errors[0]["type"] == "string_too_short"
        assert errors[0]["loc"] == ("prompt",)

    def test_prompt_validation_too_long(self):
        """Test prompt validation with too long string."""
        long_prompt = "A" * 501  # Max is 500
        
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt=long_prompt)
        
        errors = exc_info.value.errors()
        assert len(errors) == 1
        assert errors[0]["type"] == "string_too_long"
        assert errors[0]["loc"] == ("prompt",)

    def test_duration_validation_boundaries(self):
        """Test duration validation at boundaries."""
        # Valid boundaries
        VideoGenerationRequest(prompt="test", duration=1)  # Min
        VideoGenerationRequest(prompt="test", duration=60)  # Max
        
        # Invalid boundaries
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", duration=0)
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"
        
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", duration=61)
        assert exc_info.value.errors()[0]["type"] == "less_than_equal"

    def test_width_validation_boundaries(self):
        """Test width validation at boundaries."""
        # Valid boundaries
        VideoGenerationRequest(prompt="test", width=480)  # Min
        VideoGenerationRequest(prompt="test", width=3840)  # Max
        
        # Invalid boundaries
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", width=479)
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"
        
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", width=3841)
        assert exc_info.value.errors()[0]["type"] == "less_than_equal"

    def test_height_validation_boundaries(self):
        """Test height validation at boundaries."""
        # Valid boundaries
        VideoGenerationRequest(prompt="test", height=480)  # Min
        VideoGenerationRequest(prompt="test", height=2160)  # Max
        
        # Invalid boundaries
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", height=479)
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"
        
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", height=2161)
        assert exc_info.value.errors()[0]["type"] == "less_than_equal"

    def test_model_validation_pattern(self):
        """Test model validation with pattern matching."""
        # Valid models
        VideoGenerationRequest(prompt="test", model="sora-v1")
        VideoGenerationRequest(prompt="test", model="sora-v2")
        
        # Invalid models
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", model="invalid-model")
        assert exc_info.value.errors()[0]["type"] == "string_pattern_mismatch"
        
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationRequest(prompt="test", model="sora-v3")
        assert exc_info.value.errors()[0]["type"] == "string_pattern_mismatch"

    def test_serialization_to_dict(self):
        """Test serialization to dictionary."""
        request = VideoGenerationRequest(
            prompt="test prompt",
            duration=5,
            width=1280,
            height=720,
            model="sora-v1"
        )
        
        data = request.model_dump()
        
        assert data == {
            "prompt": "test prompt",
            "duration": 5,
            "width": 1280,
            "height": 720,
            "model": "sora-v1"
        }

    def test_serialization_exclude_none(self):
        """Test serialization excluding None values."""
        request = VideoGenerationRequest(prompt="test prompt", duration=5)
        
        data = request.model_dump(exclude_none=True)
        
        assert data == {
            "prompt": "test prompt",
            "duration": 5
        }

    def test_json_schema_example(self):
        """Test that JSON schema example is valid."""
        schema = VideoGenerationRequest.model_json_schema()
        example = schema["example"]
        
        # Example should be valid
        request = VideoGenerationRequest(**example)
        assert request.prompt == "A beautiful sunset over mountains"
        assert request.duration == 10
        assert request.width == 1920
        assert request.height == 1080
        assert request.model == "sora-v1"


@pytest.mark.unit
class TestVideoGenerationResponse:
    """Test VideoGenerationResponse model validation."""

    def test_valid_response(self):
        """Test valid response creation."""
        response = VideoGenerationResponse(
            success=True,
            job_id="job-123",
            status="pending",
            message="Job submitted successfully",
            queue_position=3,
            estimated_wait_minutes=5
        )
        
        assert response.success is True
        assert response.job_id == "job-123"
        assert response.status == "pending"
        assert response.message == "Job submitted successfully"
        assert response.queue_position == 3
        assert response.estimated_wait_minutes == 5

    def test_required_fields_validation(self):
        """Test validation of required fields."""
        with pytest.raises(ValidationError) as exc_info:
            VideoGenerationResponse()
        
        errors = exc_info.value.errors()
        required_fields = {"success", "job_id", "status", "message"}
        error_fields = {error["loc"][0] for error in errors}
        
        assert required_fields.issubset(error_fields)

    def test_optional_fields_none(self):
        """Test response with optional fields as None."""
        response = VideoGenerationResponse(
            success=True,
            job_id="job-123",
            status="running",
            message="Job is processing"
        )
        
        assert response.queue_position is None
        assert response.estimated_wait_minutes is None

    def test_serialization_to_json(self):
        """Test serialization to JSON."""
        response = VideoGenerationResponse(
            success=True,
            job_id="job-123",
            status="pending",
            message="Job submitted successfully",
            queue_position=3,
            estimated_wait_minutes=5
        )
        
        json_str = response.model_dump_json()
        data = json.loads(json_str)
        
        assert data["success"] is True
        assert data["job_id"] == "job-123"
        assert data["status"] == "pending"
        assert data["message"] == "Job submitted successfully"
        assert data["queue_position"] == 3
        assert data["estimated_wait_minutes"] == 5


@pytest.mark.unit
class TestHealthCheckResponse:
    """Test HealthCheckResponse model validation."""

    def test_valid_health_response(self):
        """Test valid health check response."""
        response = HealthCheckResponse(
            overall_status="healthy",
            components={
                "database": {"status": "healthy", "response_time_ms": 12},
                "azure_api": {"status": "healthy", "response_time_ms": 245}
            },
            timestamp="2025-01-09T10:30:00Z",
            version="1.0.0"
        )
        
        assert response.overall_status == "healthy"
        assert response.components["database"]["status"] == "healthy"
        assert response.components["azure_api"]["response_time_ms"] == 245
        assert response.timestamp == "2025-01-09T10:30:00Z"
        assert response.version == "1.0.0"

    def test_components_dict_validation(self):
        """Test components dictionary validation."""
        response = HealthCheckResponse(
            overall_status="degraded",
            components={
                "database": {"status": "healthy"},
                "azure_api": {"status": "unhealthy", "error": "Connection timeout"}
            },
            timestamp="2025-01-09T10:30:00Z",
            version="1.0.0"
        )
        
        assert len(response.components) == 2
        assert response.components["database"]["status"] == "healthy"
        assert response.components["azure_api"]["error"] == "Connection timeout"

    def test_required_fields_validation(self):
        """Test validation of required fields."""
        with pytest.raises(ValidationError) as exc_info:
            HealthCheckResponse()
        
        errors = exc_info.value.errors()
        required_fields = {"overall_status", "components", "timestamp", "version"}
        error_fields = {error["loc"][0] for error in errors}
        
        assert required_fields.issubset(error_fields)


@pytest.mark.unit
class TestJobStatusResponse:
    """Test JobStatusResponse model validation."""

    def test_valid_completed_job(self):
        """Test valid completed job response."""
        created_at = datetime.now()
        completed_at = datetime.now()
        
        response = JobStatusResponse(
            job_id="job-123",
            status="completed",
            download_url="/api/files/download/job-123",
            created_at=created_at,
            completed_at=completed_at,
            progress_percentage=100
        )
        
        assert response.job_id == "job-123"
        assert response.status == "completed"
        assert response.download_url == "/api/files/download/job-123"
        assert response.created_at == created_at
        assert response.completed_at == completed_at
        assert response.progress_percentage == 100

    def test_valid_failed_job(self):
        """Test valid failed job response."""
        created_at = datetime.now()
        
        response = JobStatusResponse(
            job_id="job-123",
            status="failed",
            error_message="Processing failed due to invalid input",
            created_at=created_at,
            progress_percentage=0
        )
        
        assert response.job_id == "job-123"
        assert response.status == "failed"
        assert response.error_message == "Processing failed due to invalid input"
        assert response.download_url is None
        assert response.completed_at is None
        assert response.progress_percentage == 0

    def test_progress_percentage_validation(self):
        """Test progress percentage validation."""
        created_at = datetime.now()
        
        # Valid boundaries
        JobStatusResponse(
            job_id="job-123",
            status="running",
            created_at=created_at,
            progress_percentage=0
        )
        
        JobStatusResponse(
            job_id="job-123",
            status="running",
            created_at=created_at,
            progress_percentage=100
        )
        
        # Invalid boundaries
        with pytest.raises(ValidationError) as exc_info:
            JobStatusResponse(
                job_id="job-123",
                status="running",
                created_at=created_at,
                progress_percentage=-1
            )
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"
        
        with pytest.raises(ValidationError) as exc_info:
            JobStatusResponse(
                job_id="job-123",
                status="running",
                created_at=created_at,
                progress_percentage=101
            )
        assert exc_info.value.errors()[0]["type"] == "less_than_equal"

    def test_datetime_serialization(self):
        """Test datetime serialization to ISO format."""
        created_at = datetime(2025, 1, 9, 10, 30, 0)
        completed_at = datetime(2025, 1, 9, 10, 35, 0)
        
        response = JobStatusResponse(
            job_id="job-123",
            status="completed",
            created_at=created_at,
            completed_at=completed_at
        )
        
        json_str = response.model_dump_json()
        data = json.loads(json_str)
        
        assert data["created_at"] == "2025-01-09T10:30:00"
        assert data["completed_at"] == "2025-01-09T10:35:00"


@pytest.mark.unit
class TestQueueStatusResponse:
    """Test QueueStatusResponse model validation."""

    def test_valid_queue_status(self):
        """Test valid queue status response."""
        response = QueueStatusResponse(
            total_jobs=15,
            position=3,
            estimated_wait_minutes=8,
            session_jobs=2,
            can_submit_more=True
        )
        
        assert response.total_jobs == 15
        assert response.position == 3
        assert response.estimated_wait_minutes == 8
        assert response.session_jobs == 2
        assert response.can_submit_more is True

    def test_field_validation_non_negative(self):
        """Test validation of non-negative integer fields."""
        # Valid values
        QueueStatusResponse(
            total_jobs=0,
            position=0,
            estimated_wait_minutes=0,
            session_jobs=0,
            can_submit_more=True
        )
        
        # Invalid negative values
        with pytest.raises(ValidationError) as exc_info:
            QueueStatusResponse(
                total_jobs=-1,
                position=0,
                estimated_wait_minutes=0,
                session_jobs=0,
                can_submit_more=True
            )
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"

    def test_boolean_field_validation(self):
        """Test boolean field validation."""
        response = QueueStatusResponse(
            total_jobs=5,
            position=1,
            estimated_wait_minutes=2,
            session_jobs=1,
            can_submit_more=False
        )
        
        assert response.can_submit_more is False


@pytest.mark.unit
class TestSessionInfoResponse:
    """Test SessionInfoResponse model validation."""

    def test_valid_session_info(self):
        """Test valid session info response."""
        created_at = datetime.now()
        
        response = SessionInfoResponse(
            session_id="sess_abcd1234567890",
            created_at=created_at,
            jobs_submitted=5,
            max_concurrent_jobs=3,
            active_jobs=2
        )
        
        assert response.session_id == "sess_abcd1234567890"
        assert response.created_at == created_at
        assert response.jobs_submitted == 5
        assert response.max_concurrent_jobs == 3
        assert response.active_jobs == 2

    def test_job_count_validation(self):
        """Test job count validation."""
        created_at = datetime.now()
        
        # Valid boundaries
        SessionInfoResponse(
            session_id="sess_123",
            created_at=created_at,
            jobs_submitted=0,
            max_concurrent_jobs=1,
            active_jobs=0
        )
        
        # Invalid values
        with pytest.raises(ValidationError) as exc_info:
            SessionInfoResponse(
                session_id="sess_123",
                created_at=created_at,
                jobs_submitted=-1,
                max_concurrent_jobs=1,
                active_jobs=0
            )
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"
        
        with pytest.raises(ValidationError) as exc_info:
            SessionInfoResponse(
                session_id="sess_123",
                created_at=created_at,
                jobs_submitted=1,
                max_concurrent_jobs=0,
                active_jobs=0
            )
        assert exc_info.value.errors()[0]["type"] == "greater_than_equal"

    def test_datetime_serialization(self):
        """Test datetime serialization."""
        created_at = datetime(2025, 1, 9, 10, 0, 0)
        
        response = SessionInfoResponse(
            session_id="sess_123",
            created_at=created_at,
            jobs_submitted=1,
            max_concurrent_jobs=3,
            active_jobs=1
        )
        
        json_str = response.model_dump_json()
        data = json.loads(json_str)
        
        assert data["created_at"] == "2025-01-09T10:00:00"


@pytest.mark.unit
class TestErrorResponse:
    """Test ErrorResponse model validation."""

    def test_valid_error_response(self):
        """Test valid error response."""
        response = ErrorResponse(
            error_code="VALIDATION_ERROR",
            error_message="Invalid prompt: exceeds maximum length",
            details={"field": "prompt", "max_length": 500},
            timestamp="2025-01-09T10:30:00Z"
        )
        
        assert response.success is False  # Should always be False
        assert response.error_code == "VALIDATION_ERROR"
        assert response.error_message == "Invalid prompt: exceeds maximum length"
        assert response.details["field"] == "prompt"
        assert response.details["max_length"] == 500
        assert response.timestamp == "2025-01-09T10:30:00Z"

    def test_success_field_always_false(self):
        """Test that success field is always False."""
        response = ErrorResponse(
            error_code="TEST_ERROR",
            error_message="Test error message",
            timestamp="2025-01-09T10:30:00Z"
        )
        
        assert response.success is False

    def test_optional_details_field(self):
        """Test error response with optional details."""
        response = ErrorResponse(
            error_code="GENERIC_ERROR",
            error_message="Something went wrong",
            timestamp="2025-01-09T10:30:00Z"
        )
        
        assert response.details is None


@pytest.mark.unit
class TestMetricsResponse:
    """Test MetricsResponse model validation."""

    def test_valid_metrics_response(self):
        """Test valid metrics response."""
        response = MetricsResponse(
            system_metrics={
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 23.1
            },
            job_metrics={
                "total_jobs": 1247,
                "completed_jobs": 1189,
                "failed_jobs": 58,
                "success_rate": 95.3
            },
            performance_metrics={
                "avg_processing_time": 127.5,
                "queue_wait_time": 45.2,
                "api_response_time": 234.7
            },
            timestamp="2025-01-09T10:30:00Z"
        )
        
        assert response.system_metrics["cpu_usage"] == 45.2
        assert response.job_metrics["total_jobs"] == 1247
        assert response.performance_metrics["avg_processing_time"] == 127.5
        assert response.timestamp == "2025-01-09T10:30:00Z"

    def test_nested_dict_validation(self):
        """Test nested dictionary validation."""
        response = MetricsResponse(
            system_metrics={"nested": {"deep": {"value": 42}}},
            job_metrics={"array": [1, 2, 3]},
            performance_metrics={"mixed": {"string": "value", "number": 123}},
            timestamp="2025-01-09T10:30:00Z"
        )
        
        assert response.system_metrics["nested"]["deep"]["value"] == 42
        assert response.job_metrics["array"] == [1, 2, 3]
        assert response.performance_metrics["mixed"]["string"] == "value"
        assert response.performance_metrics["mixed"]["number"] == 123

    def test_required_fields_validation(self):
        """Test validation of required fields."""
        with pytest.raises(ValidationError) as exc_info:
            MetricsResponse()
        
        errors = exc_info.value.errors()
        required_fields = {"system_metrics", "job_metrics", "performance_metrics", "timestamp"}
        error_fields = {error["loc"][0] for error in errors}
        
        assert required_fields.issubset(error_fields)


@pytest.mark.unit
class TestModelIntegration:
    """Integration tests for API models."""

    def test_request_response_workflow(self):
        """Test complete request-response workflow."""
        # Create request
        request = VideoGenerationRequest(
            prompt="A beautiful sunset",
            duration=10,
            width=1920,
            height=1080,
            model="sora-v1"
        )
        
        # Simulate processing and create response
        response = VideoGenerationResponse(
            success=True,
            job_id="job-123e4567-e89b-12d3-a456-426614174000",
            status="pending",
            message="Job submitted successfully",
            queue_position=3,
            estimated_wait_minutes=5
        )
        
        # Verify workflow
        assert request.prompt == "A beautiful sunset"
        assert response.job_id.startswith("job-")
        assert response.success is True

    def test_error_handling_workflow(self):
        """Test error handling workflow."""
        # Invalid request
        try:
            VideoGenerationRequest(prompt="")
        except ValidationError as e:
            # Create error response
            error_response = ErrorResponse(
                error_code="VALIDATION_ERROR",
                error_message="Invalid prompt: too short",
                details={"field": "prompt", "min_length": 1},
                timestamp="2025-01-09T10:30:00Z"
            )
            
            assert error_response.success is False
            assert error_response.error_code == "VALIDATION_ERROR"
            assert error_response.details["field"] == "prompt"

    def test_model_serialization_consistency(self):
        """Test serialization consistency across models."""
        models = [
            VideoGenerationRequest(prompt="test"),
            VideoGenerationResponse(
                success=True,
                job_id="job-123",
                status="pending",
                message="test"
            ),
            HealthCheckResponse(
                overall_status="healthy",
                components={"db": {"status": "healthy"}},
                timestamp="2025-01-09T10:30:00Z",
                version="1.0.0"
            )
        ]
        
        for model in models:
            # Should be able to serialize to dict
            data = model.model_dump()
            assert isinstance(data, dict)
            
            # Should be able to serialize to JSON
            json_str = model.model_dump_json()
            assert isinstance(json_str, str)
            
            # Should be able to parse back
            parsed_data = json.loads(json_str)
            assert isinstance(parsed_data, dict)

    def test_model_validation_edge_cases(self):
        """Test edge cases in model validation."""
        # Test with minimal valid data
        minimal_request = VideoGenerationRequest(prompt="a")
        assert minimal_request.prompt == "a"
        
        # Test with maximum valid data
        max_request = VideoGenerationRequest(
            prompt="a" * 500,
            duration=60,
            width=3840,
            height=2160,
            model="sora-v2"
        )
        assert len(max_request.prompt) == 500
        assert max_request.duration == 60
        assert max_request.width == 3840
        assert max_request.height == 2160
        assert max_request.model == "sora-v2"

    def test_json_schema_generation(self):
        """Test JSON schema generation for all models."""
        models = [
            VideoGenerationRequest,
            VideoGenerationResponse,
            HealthCheckResponse,
            JobStatusResponse,
            QueueStatusResponse,
            SessionInfoResponse,
            ErrorResponse,
            MetricsResponse
        ]
        
        for model in models:
            schema = model.model_json_schema()
            assert isinstance(schema, dict)
            assert "properties" in schema
            assert "title" in schema
            assert schema["title"] == model.__name__
            
            # Check if example exists and is valid
            if "example" in schema:
                example = schema["example"]
                # Example should be valid for the model
                instance = model(**example)
                assert isinstance(instance, model)