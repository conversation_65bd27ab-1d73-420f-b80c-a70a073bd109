"""Unit tests for JobRepository database operations.

Tests all CRUD operations, error handling, and bulk operations
for video job persistence using SQLAlchemy ORM.
"""

from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy.exc import SQLAlchemyError

from src.api.job_repository import JobRepository
from src.core.models import VideoJob
from src.database.models import VideoJobDB


@pytest.mark.unit
class TestJobRepository:
    """Test JobRepository database operations.
    
    Tests CRUD operations, error handling, statistics collection,
    and cleanup operations for video job data persistence.
    """

    def setup_method(self):
        """Set up test environment."""
        self.repo = JobRepository()
        self.sample_job = VideoJob(
            id="test-job-123",
            prompt="A cat playing piano",
            status="pending",
            created_at=datetime.now(),
        )

    def test_job_repository_initialization(self):
        """Test JobRepository initialization."""
        repo = JobRepository()
        assert hasattr(repo, "logger")

    @patch("src.api.job_repository.get_db_session")
    def test_create_job_success(self, mock_get_session):
        """Test successful job creation."""
        # Mock database session
        mock_session = MagicMock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        # Mock VideoJobDB methods
        mock_job_db = MagicMock()
        mock_job_db.to_pydantic.return_value = self.sample_job

        with patch.object(
            VideoJobDB, "from_pydantic", return_value=mock_job_db
        ) as mock_from_pydantic:
            with patch.object(self.repo, "logger") as mock_logger:
                result = self.repo.create_job(self.sample_job)

                # Verify database operations
                mock_from_pydantic.assert_called_once_with(self.sample_job)
                mock_session.add.assert_called_once_with(mock_job_db)
                mock_session.commit.assert_called_once()

                # Verify logging
                mock_logger.info.assert_called_once()
                log_message = mock_logger.info.call_args[0][0]
                assert self.sample_job.id in log_message

                # Verify return value
                assert result == self.sample_job

    @patch("src.api.job_repository.get_db_session")
    def test_create_job_failure(self, mock_get_session):
        """Test job creation with database error."""
        # Mock session to raise SQLAlchemy error
        mock_session = MagicMock()
        mock_session.add.side_effect = SQLAlchemyError("Database error")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.create_job(self.sample_job)

            # Verify error logging
            mock_logger.error.assert_called_once()
            log_message = mock_logger.error.call_args[0][0]
            assert self.sample_job.id in log_message
            assert "Database error" in log_message

            # Should return None on failure
            assert result is None

    @patch("src.api.job_repository.get_db_session")
    def test_get_job_by_id_found(self, mock_get_session):
        """Test retrieving existing job by ID."""
        # Mock database session and query result
        mock_session = MagicMock()
        mock_job_db = MagicMock()
        mock_job_db.to_pydantic.return_value = self.sample_job
        mock_session.query.return_value.filter_by.return_value.first.return_value = (
            mock_job_db
        )
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.get_job_by_id(self.sample_job.id)

        # Verify query operations
        mock_session.query.assert_called_once_with(VideoJobDB)
        mock_session.query.return_value.filter_by.assert_called_once_with(
            id=self.sample_job.id
        )

        # Verify return value
        assert result == self.sample_job

    @patch("src.api.job_repository.get_db_session")
    def test_get_job_by_id_not_found(self, mock_get_session):
        """Test retrieving non-existent job by ID."""
        # Mock session to return None (job not found)
        mock_session = MagicMock()
        mock_session.query.return_value.filter_by.return_value.first.return_value = None
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.get_job_by_id("nonexistent-job")

        assert result is None

    @patch("src.api.job_repository.get_db_session")
    def test_get_job_by_id_error(self, mock_get_session):
        """Test get job by ID with database error."""
        # Mock session to raise SQLAlchemy error
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError("Query failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.get_job_by_id(self.sample_job.id)

            # Verify error logging
            mock_logger.error.assert_called_once()

            assert result is None

    @patch("src.api.job_repository.get_db_session")
    def test_update_job_success(self, mock_get_session):
        """Test successful job update."""
        # Create updated job
        updated_job = VideoJob(
            id=self.sample_job.id,
            prompt=self.sample_job.prompt,
            status="running",
            created_at=self.sample_job.created_at,
            started_at=datetime.now(),
        )

        # Mock database session and existing job
        mock_session = MagicMock()
        mock_job_db = MagicMock()
        mock_job_db.to_pydantic.return_value = updated_job
        mock_session.query.return_value.filter_by.return_value.first.return_value = (
            mock_job_db
        )
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.update_job(updated_job)

            # Verify update operations
            mock_job_db.update_from_pydantic.assert_called_once_with(updated_job)
            mock_session.commit.assert_called_once()

            # Verify logging
            mock_logger.info.assert_called_once()

            assert result == updated_job

    @patch("src.api.job_repository.get_db_session")
    def test_update_job_not_found(self, mock_get_session):
        """Test updating non-existent job."""
        # Mock session to return None (job not found)
        mock_session = MagicMock()
        mock_session.query.return_value.filter_by.return_value.first.return_value = None
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.update_job(self.sample_job)

            # Verify warning logged
            mock_logger.warning.assert_called_once()
            log_message = mock_logger.warning.call_args[0][0]
            assert self.sample_job.id in log_message

            assert result is None

    @patch("src.api.job_repository.get_db_session")
    def test_update_job_error(self, mock_get_session):
        """Test job update with database error."""
        # Mock session to raise error during commit
        mock_session = MagicMock()
        mock_job_db = MagicMock()
        mock_session.query.return_value.filter_by.return_value.first.return_value = (
            mock_job_db
        )
        mock_session.commit.side_effect = SQLAlchemyError("Update failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.update_job(self.sample_job)

            # Verify error logging
            mock_logger.error.assert_called_once()

            assert result is None

    @patch("src.api.job_repository.get_db_session")
    def test_delete_job_success(self, mock_get_session):
        """Test successful job deletion."""
        # Mock database session and existing job
        mock_session = MagicMock()
        mock_job_db = MagicMock()
        mock_session.query.return_value.filter_by.return_value.first.return_value = (
            mock_job_db
        )
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.delete_job(self.sample_job.id)

            # Verify delete operations
            mock_session.delete.assert_called_once_with(mock_job_db)
            mock_session.commit.assert_called_once()

            # Verify logging
            mock_logger.info.assert_called_once()

            assert result is True

    @patch("src.api.job_repository.get_db_session")
    def test_delete_job_not_found(self, mock_get_session):
        """Test deleting non-existent job."""
        # Mock session to return None (job not found)
        mock_session = MagicMock()
        mock_session.query.return_value.filter_by.return_value.first.return_value = None
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.delete_job("nonexistent-job")

            # Verify warning logged
            mock_logger.warning.assert_called_once()

            assert result is False

    @patch("src.api.job_repository.get_db_session")
    def test_delete_job_error(self, mock_get_session):
        """Test job deletion with database error."""
        # Mock session to raise error during delete
        mock_session = MagicMock()
        mock_job_db = MagicMock()
        mock_session.query.return_value.filter_by.return_value.first.return_value = (
            mock_job_db
        )
        mock_session.delete.side_effect = SQLAlchemyError("Delete failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.delete_job(self.sample_job.id)

            # Verify error logging
            mock_logger.error.assert_called_once()

            assert result is False

    @patch("src.api.job_repository.get_db_session")
    def test_get_jobs_by_status_success(self, mock_get_session):
        """Test retrieving jobs by status."""
        # Mock multiple jobs
        mock_jobs_db = []
        mock_jobs = []
        for i in range(3):
            mock_job_db = MagicMock()
            mock_job = VideoJob(
                id=f"job-{i}",
                prompt=f"Test prompt {i}",
                status="pending",
                created_at=datetime.now() - timedelta(minutes=i),
            )
            mock_job_db.to_pydantic.return_value = mock_job
            mock_jobs_db.append(mock_job_db)
            mock_jobs.append(mock_job)

        # Mock database session
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.order_by.return_value.limit.return_value.all.return_value = mock_jobs_db
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.get_jobs_by_status("pending", limit=10)

        # Verify query construction
        mock_session.query.assert_called_once_with(VideoJobDB)
        mock_query.filter_by.assert_called_once_with(status="pending")
        mock_query.filter_by.return_value.order_by.assert_called_once()
        mock_query.filter_by.return_value.order_by.return_value.limit.assert_called_once_with(
            10
        )

        # Verify results
        assert len(result) == 3
        assert all(isinstance(job, VideoJob) for job in result)
        assert result == mock_jobs

    @patch("src.api.job_repository.get_db_session")
    def test_get_jobs_by_status_error(self, mock_get_session):
        """Test get jobs by status with database error."""
        # Mock session to raise error
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError("Query failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.get_jobs_by_status("pending")

            # Verify error logging
            mock_logger.error.assert_called_once()

            # Should return empty list on error
            assert result == []

    @patch("src.api.job_repository.get_db_session")
    def test_get_recent_jobs_success(self, mock_get_session):
        """Test retrieving recent jobs."""
        # Mock multiple jobs
        mock_jobs_db = []
        mock_jobs = []
        for i in range(2):
            mock_job_db = MagicMock()
            mock_job = VideoJob(
                id=f"job-{i}",
                prompt=f"Test prompt {i}",
                status="succeeded",
                created_at=datetime.now() - timedelta(hours=i),
            )
            mock_job_db.to_pydantic.return_value = mock_job
            mock_jobs_db.append(mock_job_db)
            mock_jobs.append(mock_job)

        # Mock database session
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.order_by.return_value.limit.return_value.all.return_value = (
            mock_jobs_db
        )
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.get_recent_jobs(limit=25)

        # Verify query construction
        mock_session.query.assert_called_once_with(VideoJobDB)
        mock_query.order_by.assert_called_once()
        mock_query.order_by.return_value.limit.assert_called_once_with(25)

        assert len(result) == 2
        assert result == mock_jobs

    @patch("src.api.job_repository.get_db_session")
    def test_get_recent_jobs_error(self, mock_get_session):
        """Test get recent jobs with database error."""
        # Mock session to raise error
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError("Query failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.get_recent_jobs()

            mock_logger.error.assert_called_once()
            assert result == []

    def test_get_pending_jobs(self):
        """Test get pending jobs delegates to get_jobs_by_status."""
        with patch.object(self.repo, "get_jobs_by_status") as mock_get_by_status:
            mock_jobs = [self.sample_job]
            mock_get_by_status.return_value = mock_jobs

            result = self.repo.get_pending_jobs()

            mock_get_by_status.assert_called_once_with("pending")
            assert result == mock_jobs

    def test_get_running_jobs(self):
        """Test get running jobs delegates to get_jobs_by_status."""
        with patch.object(self.repo, "get_jobs_by_status") as mock_get_by_status:
            mock_jobs = [self.sample_job]
            mock_get_by_status.return_value = mock_jobs

            result = self.repo.get_running_jobs()

            mock_get_by_status.assert_called_once_with("running")
            assert result == mock_jobs

    @patch("src.api.job_repository.get_db_session")
    def test_cleanup_old_jobs_success(self, mock_get_session):
        """Test successful cleanup of old jobs."""
        # Mock database session
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter.return_value.filter.return_value.delete.return_value = 5
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.cleanup_old_jobs(days_old=7)

            # Verify query construction
            mock_session.query.assert_called_once_with(VideoJobDB)
            # Two filter calls are made (chained), but we only need to verify the delete call
            mock_query.filter.return_value.filter.return_value.delete.assert_called_once_with(
                synchronize_session=False
            )
            mock_session.commit.assert_called_once()

            # Verify logging
            mock_logger.info.assert_called_once()
            log_message = mock_logger.info.call_args[0][0]
            assert "5" in log_message

            assert result == 5

    @patch("src.api.job_repository.get_db_session")
    def test_cleanup_old_jobs_error(self, mock_get_session):
        """Test cleanup old jobs with database error."""
        # Mock session to raise error
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError("Delete failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.cleanup_old_jobs(days_old=7)

            mock_logger.error.assert_called_once()
            assert result == 0

    @patch("src.api.job_repository.get_db_session")
    def test_get_job_stats_success(self, mock_get_session):
        """Test successful job statistics retrieval."""
        # Mock database session
        mock_session = MagicMock()

        # Mock count queries for different statuses
        count_results = {"pending": 5, "running": 3, "succeeded": 20, "failed": 2}

        def mock_count_side_effect(*args, **kwargs):
            # This will be called multiple times with filter_by
            filter_by_call = mock_session.query.return_value.filter_by
            if filter_by_call.called:
                last_call_args = filter_by_call.call_args[1]
                status = last_call_args.get("status")
                return count_results.get(status, 0)
            return 30  # Total count (all jobs)

        mock_session.query.return_value.filter_by.return_value.count.side_effect = (
            mock_count_side_effect
        )
        mock_session.query.return_value.count.return_value = 30  # Total jobs

        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.get_job_stats()

        # Verify all status counts were queried
        assert mock_session.query.call_count >= 5  # 4 status queries + 1 total

        # Verify results
        assert result["pending"] == 5
        assert result["running"] == 3
        assert result["succeeded"] == 20
        assert result["failed"] == 2
        assert result["total"] == 30

    @patch("src.api.job_repository.get_db_session")
    def test_get_job_stats_error(self, mock_get_session):
        """Test get job stats with database error."""
        # Mock session to raise error
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError("Stats query failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.get_job_stats()

            mock_logger.error.assert_called_once()
            assert result == {}

    @patch("src.api.job_repository.get_db_session")
    def test_bulk_update_status_success(self, mock_get_session):
        """Test successful bulk status update."""
        job_ids = ["job-1", "job-2", "job-3"]
        new_status = "failed"

        # Mock database session
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter.return_value.update.return_value = 3
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.bulk_update_status(job_ids, new_status)

            # Verify query construction
            mock_session.query.assert_called_once_with(VideoJobDB)
            mock_query.filter.assert_called_once()

            # Verify update parameters
            update_call = mock_query.filter.return_value.update
            update_call.assert_called_once()
            update_args = update_call.call_args[0][0]
            assert update_args["status"] == new_status
            assert "completed_at" in update_args
            assert isinstance(update_args["completed_at"], datetime)

            # Verify synchronize_session parameter
            update_kwargs = update_call.call_args[1]
            assert update_kwargs["synchronize_session"] is False

            mock_session.commit.assert_called_once()

            # Verify logging
            mock_logger.info.assert_called_once()
            log_message = mock_logger.info.call_args[0][0]
            assert "3" in log_message
            assert new_status in log_message

            assert result == 3

    @patch("src.api.job_repository.get_db_session")
    def test_bulk_update_status_error(self, mock_get_session):
        """Test bulk update status with database error."""
        job_ids = ["job-1", "job-2"]
        new_status = "failed"

        # Mock session to raise error
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError("Bulk update failed")
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        with patch.object(self.repo, "logger") as mock_logger:
            result = self.repo.bulk_update_status(job_ids, new_status)

            mock_logger.error.assert_called_once()
            assert result == 0

    @patch("src.api.job_repository.get_db_session")
    def test_bulk_update_status_empty_list(self, mock_get_session):
        """Test bulk update status with empty job list."""
        job_ids = []
        new_status = "failed"

        # Mock database session
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter.return_value.update.return_value = 0
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.bulk_update_status(job_ids, new_status)

        # Should still work, just update 0 jobs
        assert result == 0

    def test_get_jobs_by_status_default_limit(self):
        """Test get jobs by status uses default limit."""
        # Test the actual method with default parameter by patching the database session
        with patch("src.api.job_repository.get_db_session") as mock_get_session:
            mock_session = MagicMock()
            mock_get_session.return_value.__enter__.return_value = mock_session
            mock_get_session.return_value.__exit__.return_value = None
            mock_session.query.return_value.filter_by.return_value.order_by.return_value.limit.return_value.all.return_value = []

            self.repo.get_jobs_by_status("pending")  # No limit specified

            # Verify default limit of 100 was used
            mock_session.query.return_value.filter_by.return_value.order_by.return_value.limit.assert_called_once_with(
                100
            )

    def test_get_recent_jobs_default_limit(self):
        """Test get recent jobs uses default limit."""
        with patch("src.api.job_repository.get_db_session") as mock_get_session:
            mock_session = MagicMock()
            mock_get_session.return_value.__enter__.return_value = mock_session
            mock_get_session.return_value.__exit__.return_value = None
            mock_session.query.return_value.order_by.return_value.limit.return_value.all.return_value = []

            self.repo.get_recent_jobs()  # No limit specified

            # Verify default limit of 50 was used
            mock_session.query.return_value.order_by.return_value.limit.assert_called_once_with(
                50
            )

    def test_cleanup_old_jobs_default_days(self):
        """Test cleanup old jobs uses default days."""
        with patch("src.api.job_repository.get_db_session") as mock_get_session:
            mock_session = MagicMock()
            mock_get_session.return_value.__enter__.return_value = mock_session
            mock_get_session.return_value.__exit__.return_value = None
            mock_session.query.return_value.filter.return_value.filter.return_value.delete.return_value = 0

            # Test that datetime calculation uses default 7 days
            with patch("src.api.job_repository.datetime") as mock_datetime:
                mock_now = datetime(2023, 1, 15, 12, 0, 0)
                mock_datetime.now.return_value = mock_now

                self.repo.cleanup_old_jobs()  # No days specified

                # Verify datetime.now() was called for calculation
                mock_datetime.now.assert_called_once()

    @patch("src.api.job_repository.get_db_session")
    def test_get_job_stats_structure(self, mock_get_session):
        """Test get job stats returns correct structure."""
        # Mock database session with specific counts
        mock_session = MagicMock()
        status_counts = {"pending": 1, "running": 2, "succeeded": 3, "failed": 4}

        def count_side_effect():
            # Get the filter_by call to determine which status
            filter_call = mock_session.query.return_value.filter_by.call_args
            if filter_call and filter_call[1]:
                status = filter_call[1].get("status")
                return status_counts.get(status, 0)
            return sum(status_counts.values())  # Total count

        mock_session.query.return_value.filter_by.return_value.count.side_effect = (
            count_side_effect
        )
        mock_session.query.return_value.count.return_value = sum(status_counts.values())

        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_get_session.return_value.__exit__.return_value = None

        result = self.repo.get_job_stats()

        # Verify all expected keys are present
        expected_keys = ["pending", "running", "succeeded", "failed", "total"]
        for key in expected_keys:
            assert key in result

        # Verify total is sum of individual counts
        assert result["total"] == sum(status_counts.values())
