"""
Tests for provider-related API routes.

Tests the provider capabilities endpoint, provider selection functionality,
and C2 Provider Selection UI integration routes in the video generation API.
"""

import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.api.provider_routes import provider_bp
from src.api.video_routes import video_bp


class TestProviderCapabilitiesRoute:
    """Test provider capabilities API endpoint."""

    def test_get_provider_capabilities_success(self, client):
        """Test successful provider capabilities retrieval."""
        mock_capabilities = {
            "azure_sora": {
                "image_input": False,
                "audio_generation": True,
                "custom_duration": True,
                "cancellation": False,
            },
            "google_veo3": {
                "image_input": True,
                "audio_generation": True,
                "custom_duration": True,
                "cancellation": True,
            },
        }

        with patch("src.api.video_routes.get_provider_factory") as mock_factory_getter:
            mock_factory = MagicMock()
            mock_factory.get_provider_capabilities = AsyncMock(
                return_value=mock_capabilities
            )
            mock_factory_getter.return_value = mock_factory

            response = client.get("/provider-capabilities")

            assert response.status_code == 200
            data = json.loads(response.data)

            assert data["success"] is True
            assert data["message"] == "Provider capabilities retrieved"
            assert data["data"] == mock_capabilities

    def test_get_provider_capabilities_factory_error(self, client):
        """Test provider capabilities with factory error."""
        with patch("src.api.video_routes.get_provider_factory") as mock_factory_getter:
            mock_factory_getter.side_effect = Exception("Factory creation failed")

            response = client.get("/provider-capabilities")

            assert response.status_code == 500
            data = json.loads(response.data)

            assert data["success"] is False
            assert "Failed to retrieve provider capabilities" in data["message"]
            assert data["data"] == {}

    def test_get_provider_capabilities_async_error(self, client):
        """Test provider capabilities with async method error."""
        with patch("src.api.video_routes.get_provider_factory") as mock_factory_getter:
            mock_factory = MagicMock()
            mock_factory.get_provider_capabilities = AsyncMock(
                side_effect=Exception("Provider query failed")
            )
            mock_factory_getter.return_value = mock_factory

            response = client.get("/provider-capabilities")

            assert response.status_code == 500
            data = json.loads(response.data)

            assert data["success"] is False
            assert "Failed to retrieve provider capabilities" in data["message"]

    def test_get_provider_capabilities_empty_result(self, client):
        """Test provider capabilities with empty result."""
        with patch("src.api.video_routes.get_provider_factory") as mock_factory_getter:
            mock_factory = MagicMock()
            mock_factory.get_provider_capabilities = AsyncMock(return_value={})
            mock_factory_getter.return_value = mock_factory

            response = client.get("/provider-capabilities")

            assert response.status_code == 200
            data = json.loads(response.data)

            assert data["success"] is True
            assert data["data"] == {}


class TestProviderIntegration:
    """Test provider integration with video generation."""

    def test_provider_selection_in_form_data(self, client):
        """Test that provider selection is included in form data."""
        form_data = {
            "prompt": "A beautiful sunset over mountains",
            "provider": "google_veo3",
            "duration": "10",
        }

        # Mock the queue manager and job creation
        with patch(
            "src.api.video_routes._get_queue_manager"
        ) as mock_queue_getter, patch(
            "src.api.video_routes.job_repository"
        ) as mock_repo, patch(
            "src.api.video_routes.process_video_generation"
        ) as mock_task, patch("src.api.video_routes.g") as mock_g:
            # Mock queue manager
            mock_queue = MagicMock()
            mock_queue.assign_queue_position.return_value = 1
            mock_queue.assign_provider_queue_position.return_value = (
                1  # Fix for comparison issue
            )
            mock_queue.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }
            mock_queue_getter.return_value = mock_queue

            # Mock repository save
            mock_repo.create_job = MagicMock(
                return_value=MagicMock(id="test-job-123", status="pending")
            )

            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "celery-task-123"
            mock_task.delay.return_value = mock_result

            # Mock session in Flask g object
            mock_g.session_id = "test-session-123"

            # Mock session
            with client.session_transaction() as sess:
                sess["session_id"] = "test-session-123"

            response = client.post("/generate", data=form_data)

            # Debug: Print response details if test fails
            if response.status_code not in [200, 201]:
                print(f"Response Status: {response.status_code}")
                print(f"Response Data: {response.data.decode()}")

            # Verify the request was processed
            assert response.status_code in [200, 201]

            # Verify repository create_job was called
            mock_repo.create_job.assert_called_once()

            # Verify Celery task was called with correct parameters
            mock_task.delay.assert_called_once()
            call_args = mock_task.delay.call_args[0]

            # Verify provider was included in the job data
            # call_args should be (session_id, job_id, job_data)
            assert len(call_args) == 3
            job_data = call_args[2]  # Third argument is job_data
            assert "ui_parameters" in job_data
            assert job_data["ui_parameters"]["provider"] == "google_veo3"

    def test_image_upload_with_veo3_provider(self, client):
        """Test image upload with Veo3 provider."""
        from io import BytesIO

        # Create a simple test image
        test_image = BytesIO(b"fake image data")
        test_image.name = "test.jpg"

        form_data = {
            "prompt": "Transform this image into a video",
            "provider": "google_veo3",
            "image": (test_image, "test.jpg", "image/jpeg"),
        }

        with patch(
            "src.api.video_routes._get_queue_manager"
        ) as mock_queue_getter, patch(
            "src.api.video_routes.job_repository"
        ) as mock_repo, patch(
            "src.api.video_routes.process_video_generation"
        ) as mock_task, patch("src.api.video_routes.g") as mock_g:
            # Mock queue manager
            mock_queue = MagicMock()
            mock_queue.assign_queue_position.return_value = 1
            mock_queue.assign_provider_queue_position.return_value = (
                1  # Fix for comparison issue
            )
            mock_queue.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }
            mock_queue_getter.return_value = mock_queue

            # Mock repository save
            mock_repo.create_job = MagicMock(
                return_value=MagicMock(id="test-job-123", status="pending")
            )

            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "celery-task-123"
            mock_task.delay.return_value = mock_result

            # Mock session in Flask g object
            mock_g.session_id = "test-session-123"

            # Mock session
            with client.session_transaction() as sess:
                sess["session_id"] = "test-session-123"

            response = client.post(
                "/generate", data=form_data, content_type="multipart/form-data"
            )

            # Verify the request was processed
            assert response.status_code in [200, 201]

    def test_provider_validation_azure_sora_no_image(self, client):
        """Test that Azure Sora doesn't accept image uploads."""
        from io import BytesIO

        # Create a test image
        test_image = BytesIO(b"fake image data")
        test_image.name = "test.jpg"

        form_data = {
            "prompt": "A beautiful video",
            "provider": "azure_sora",
            "image": (test_image, "test.jpg", "image/jpeg"),
        }

        # Mock the queue manager and job creation like other integration tests
        with patch(
            "src.api.video_routes._get_queue_manager"
        ) as mock_queue_getter, patch(
            "src.api.video_routes.job_repository"
        ) as mock_repo, patch(
            "src.api.video_routes.process_video_generation"
        ) as mock_task, patch("src.api.video_routes.g") as mock_g:
            # Mock queue manager
            mock_queue = MagicMock()
            mock_queue.assign_queue_position.return_value = 1
            mock_queue.assign_provider_queue_position.return_value = (
                1  # Fix for comparison issue
            )
            mock_queue.get_queue_status.return_value = {
                "user_job_positions": [{"estimated_wait_minutes": 3}]
            }
            mock_queue_getter.return_value = mock_queue

            # Mock repository save
            mock_repo.create_job = MagicMock(
                return_value=MagicMock(id="test-job-123", status="pending")
            )

            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "celery-task-123"
            mock_task.delay.return_value = mock_result

            # Mock session in Flask g object
            mock_g.session_id = "test-session-123"

            with client.session_transaction() as sess:
                sess["session_id"] = "test-session-123"

            # Since Azure Sora shouldn't support image uploads,
            # the backend should either ignore the image or return validation error
            response = client.post(
                "/generate", data=form_data, content_type="multipart/form-data"
            )

        # Response should either succeed (ignoring image) or return validation error
        assert response.status_code in [200, 201, 400]


# C2 Provider Selection UI Tests


class TestC2ProviderStatusRoutes:
    """Test C2 provider status and health monitoring routes."""

    @patch("src.api.provider_routes._handle_async_request")
    @patch("src.api.provider_routes.get_provider_status_service")
    @patch("src.api.provider_routes.get_provider_configuration_manager")
    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_get_c2_provider_status_success(
        self,
        mock_rate_limiter,
        mock_config_manager,
        mock_status_service,
        mock_async_handler,
        c2_client,
    ):
        """Test successful C2 provider status retrieval."""
        # Return fully serializable data structure (no complex objects)
        mock_async_handler.return_value = {
            "provider_statuses": {
                "azure_sora": {
                    "provider_id": "azure_sora",
                    "status": "online",
                    "last_health_check": "2024-01-01T12:00:00",
                    "response_time_ms": 150.0,
                    "success_rate": 95.0,
                    "error_count_24h": 2,
                    "uptime_percentage": 99.5,
                    "capabilities": {"text_to_video": True},
                    "concurrent_jobs": 1,
                    "max_concurrent_jobs": 5,
                    "queue_length": 3,
                    "is_configured": True,
                    "configuration_errors": [],
                }
            },
            "environment": {"type": "local"},
            "available_providers": ["azure_sora"],
            "default_provider": "azure_sora",
            "rate_limiting": {},
            "f4_system_info": {},
        }

        response = c2_client.get("/c2/status")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "C2 provider status retrieved successfully"
        assert "data" in data

    @patch("src.api.provider_routes._handle_async_request")
    def test_get_c2_provider_status_error(self, mock_async_handler, c2_client):
        """Test C2 provider status error handling."""
        mock_async_handler.side_effect = Exception("Service unavailable")

        response = c2_client.get("/c2/status")

        assert response.status_code == 500
        data = json.loads(response.data)
        assert data["success"] is False
        assert "Failed to retrieve C2 provider status" in data["message"]


class TestC2ProviderRecommendationRoutes:
    """Test C2 provider recommendation and preference routes."""

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    @patch("src.api.provider_routes._handle_async_request")
    def test_get_c2_provider_recommendation_success(
        self,
        mock_async_handler,
        mock_session_manager_getter,
        mock_get_session_id,
        c2_client,
    ):
        """Test successful C2 provider recommendation."""
        mock_get_session_id.return_value = "test-session-123"

        mock_session_manager = MagicMock()
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora",
            "fallback_providers": ["google_veo3"],
        }
        mock_session_manager.get_recommended_provider.return_value = "azure_sora"
        mock_session_manager_getter.return_value = mock_session_manager

        mock_async_handler.return_value = {
            "selected_provider": "azure_sora",
            "environment": "local",
        }

        response = c2_client.get("/c2/recommend")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "C2 provider recommendation retrieved successfully"
        assert data["data"]["recommended_provider"] == "azure_sora"

    @patch("src.api.provider_routes._get_session_id")
    def test_get_c2_provider_recommendation_no_session(
        self, mock_get_session_id, c2_client
    ):
        """Test C2 provider recommendation without session."""
        mock_get_session_id.side_effect = ValueError("Session ID not found")

        response = c2_client.get("/c2/recommend")

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["success"] is False
        assert "Session required" in data["message"]


class TestC2ProviderPreferenceRoutes:
    """Test C2 provider preference management routes."""

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    def test_get_c2_provider_preferences_success(
        self, mock_session_manager_getter, mock_get_session_id, c2_client
    ):
        """Test successful C2 provider preferences retrieval."""
        mock_get_session_id.return_value = "test-session-123"

        mock_session_manager = MagicMock()
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "azure_sora",
            "fallback_providers": ["google_veo3"],
            "performance_priority": "balanced",
        }
        mock_session_manager.get_provider_usage_stats.return_value = {
            "total_requests": 10,
            "successful_requests": 9,
        }
        mock_session_manager_getter.return_value = mock_session_manager

        response = c2_client.get("/c2/preferences")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "C2 provider preferences retrieved successfully"
        assert data["data"]["preferences"]["preferred_provider"] == "azure_sora"

    @patch("src.api.provider_routes._get_session_id")
    @patch("src.api.provider_routes.get_session_manager")
    @patch("src.api.provider_routes.broadcast_provider_recommendation_change")
    def test_set_c2_provider_preferences_success(
        self,
        mock_broadcast,
        mock_session_manager_getter,
        mock_get_session_id,
        c2_client,
    ):
        """Test successful C2 provider preferences setting."""
        mock_get_session_id.return_value = "test-session-123"

        mock_session_manager = MagicMock()
        mock_session_manager.set_preferred_provider.return_value = True
        mock_session_manager.set_fallback_providers.return_value = True
        mock_session_manager.set_performance_priority.return_value = True
        mock_session_manager.get_provider_preferences.return_value = {
            "preferred_provider": "google_veo3",
            "fallback_providers": ["azure_sora"],
            "performance_priority": "speed",
        }
        mock_session_manager_getter.return_value = mock_session_manager

        preference_data = {
            "preferred_provider": "google_veo3",
            "fallback_providers": ["azure_sora"],
            "performance_priority": "speed",
        }

        response = c2_client.post(
            "/c2/preferences",
            data=json.dumps(preference_data),
            content_type="application/json",
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "C2 provider preferences updated successfully"

        # Verify WebSocket broadcast was called
        mock_broadcast.assert_called_once()

    @patch("src.api.provider_routes._get_session_id")
    def test_set_c2_provider_preferences_validation_error(
        self, mock_get_session_id, c2_client
    ):
        """Test C2 provider preferences validation error."""
        mock_get_session_id.return_value = "test-session-123"

        # Invalid preference data (invalid performance_priority)
        invalid_data = {
            "preferred_provider": "google_veo3",
            "performance_priority": "invalid_priority",
        }

        response = c2_client.post(
            "/c2/preferences",
            data=json.dumps(invalid_data),
            content_type="application/json",
        )

        # Should accept the data but may fail during session manager validation
        # The actual validation happens in the session manager, not Pydantic
        assert response.status_code in [200, 400]


class TestC2ProviderThrottleRoutes:
    """Test C2 provider throttling and administration routes."""

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    @patch("src.api.provider_routes.broadcast_provider_status_change")
    def test_c2_manual_throttle_provider_success(
        self, mock_broadcast, mock_rate_limiter_getter, c2_client
    ):
        """Test successful C2 provider throttling."""
        mock_rate_limiter = MagicMock()
        mock_rate_limiter.manually_throttle_provider.return_value = True
        mock_rate_limiter.get_provider_rate_status.return_value = {
            "provider_id": "azure_sora",
            "current_rate_limit": 5,
            "throttle_reason": "manual_throttle",
        }
        mock_rate_limiter_getter.return_value = mock_rate_limiter

        throttle_data = {
            "provider_id": "azure_sora",
            "throttle": True,
            "reason": "Testing throttle functionality",
        }

        response = c2_client.post(
            "/c2/throttle",
            data=json.dumps(throttle_data),
            content_type="application/json",
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert "throttled successfully" in data["message"]

        # Verify WebSocket broadcast was called
        mock_broadcast.assert_called_once()

    @patch("src.api.provider_routes.get_provider_rate_limiter")
    def test_c2_manual_throttle_provider_no_limiter(
        self, mock_rate_limiter_getter, c2_client
    ):
        """Test C2 provider throttling when rate limiter unavailable."""
        mock_rate_limiter_getter.return_value = None

        throttle_data = {
            "provider_id": "azure_sora",
            "throttle": True,
            "reason": "Testing",
        }

        response = c2_client.post(
            "/c2/throttle",
            data=json.dumps(throttle_data),
            content_type="application/json",
        )

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["success"] is False
        assert "Provider rate limiter not available" in data["error"]


class TestC2ProviderEnvironmentRoutes:
    """Test C2 provider environment and configuration routes."""

    @patch("src.api.provider_routes.get_provider_configuration_manager")
    def test_get_c2_environment_configuration_success(
        self, mock_config_manager_getter, c2_client
    ):
        """Test successful C2 environment configuration retrieval."""
        mock_config_manager = MagicMock()
        mock_config_manager.get_environment_configuration_summary.return_value = {
            "environment": {"type": "local", "debug": True},
            "providers": {"available_providers": ["azure_sora", "google_veo3"]},
            "f4_system": {"deployment_detection": "local"},
        }
        mock_config_manager_getter.return_value = mock_config_manager

        response = c2_client.get("/c2/environment")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "C2 environment configuration retrieved successfully"
        assert data["data"]["environment"]["type"] == "local"

    @patch("src.api.provider_routes._handle_async_request")
    def test_refresh_c2_provider_status_success(self, mock_async_handler, c2_client):
        """Test successful C2 provider status refresh."""
        mock_async_handler.return_value = {
            "provider_status_refresh": {
                "azure_sora": {"success": True, "status": "online"},
                "google_veo3": {"success": True, "status": "online"},
            },
            "availability_matrix": {},
            "available_providers": ["azure_sora", "google_veo3"],
            "default_provider": "azure_sora",
        }

        response = c2_client.post("/c2/refresh")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "C2 provider status refreshed successfully"
        assert len(data["data"]["available_providers"]) == 2


class TestC2ErrorHandling:
    """Test C2 error handling and validation."""

    def test_c2_validation_error_handler(self, c2_client):
        """Test C2 validation error handling."""
        # Send invalid JSON data to trigger validation error
        invalid_data = "invalid json"

        response = c2_client.post(
            "/c2/preferences", data=invalid_data, content_type="application/json"
        )

        # Should return 400 for invalid JSON
        assert response.status_code == 400

    @patch("src.api.provider_routes._get_session_id")
    def test_c2_session_required_routes(self, mock_get_session_id, c2_client):
        """Test C2 routes that require session context."""
        mock_get_session_id.side_effect = ValueError("Session ID not found")

        # Test routes that should require session
        session_required_routes = ["/c2/recommend", "/c2/preferences"]

        for route in session_required_routes:
            response = c2_client.get(route)
            assert response.status_code == 400
            data = json.loads(response.data)
            assert data["success"] is False
            assert "Session" in data["message"]


@pytest.fixture
def client():
    """Create test client for API testing."""
    from flask import Flask

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.secret_key = "test-secret-key"

    # Register the video blueprint
    app.register_blueprint(video_bp)

    with app.test_client() as client:
        with app.app_context():
            yield client


@pytest.fixture
def c2_client():
    """Create test client for C2 Provider Selection UI API testing."""
    from flask import Flask

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.secret_key = "test-secret-key"

    # Register both provider routes and video routes without prefix (matches main app)
    app.register_blueprint(provider_bp)
    app.register_blueprint(video_bp)

    with app.test_client() as client:
        with app.app_context():
            yield client
