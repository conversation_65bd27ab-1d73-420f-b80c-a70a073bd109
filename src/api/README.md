# API Module

## Purpose
Production-ready Flask API with Blueprint organization supporting multi-user video generation workflows with comprehensive request validation and standardized responses.

## Key Components
- `routes.py`: Main blueprint registration and request handling
- `models.py`: Pydantic I/O models for request/response validation
- `video_routes.py`: Video generation and streaming endpoints
- `health_routes.py`: System health and monitoring endpoints
- `job_routes.py`: Job management and queue status endpoints

## Core Endpoints

### Video Generation
```bash
# Generate video
POST /generate
Content-Type: application/json
{
  "prompt": "A cat playing with a ball",
  "duration": 5,
  "provider": "azure_sora"
}

# Check status
GET /status/<job_id>

# Stream/download video
GET /video/<job_id>
GET /download/<job_id>
```

### Provider Management
```bash
# Get available providers and capabilities
GET /providers

# Example response:
{
  "providers": [
    {
      "name": "azure_sora",
      "status": "available",
      "capabilities": ["text_to_video", "audio_generation"]
    }
  ]
}
```

### Health Monitoring
```bash
# System health
GET /health

# Provider-specific health
GET /health/providers

# System metrics
GET /metrics
```

## Request/Response Models
```python
from src.api.models import VideoGenerationRequest, JobStatusResponse

# Input validation
request = VideoGenerationRequest(
    prompt="A serene mountain landscape",
    duration=10,
    provider="google_veo3"
)

# Response structure
response = JobStatusResponse(
    job_id="123e4567-e89b-12d3-a456-426614174000",
    status="completed",
    video_url="/video/123e4567-e89b-12d3-a456-426614174000"
)
```

## Testing
```bash
# Run API tests
uv run pytest src/api/tests/ -v

# Test video generation endpoints
uv run pytest src/api/tests/test_video_routes.py

# Test health endpoints
uv run pytest src/api/tests/test_health_routes.py

# Integration tests
uv run pytest src/api/tests/test_integration.py
```

## Configuration
- Blueprint registration: Automatic via Flask application factory
- Request validation: Pydantic models with automatic error handling
- Response formatting: Standardized JSON responses with proper HTTP status codes
- CORS support: Configurable for cross-origin requests

## Architecture
- **Blueprint Organization**: Modular route organization by functionality
- **Request Validation**: Pydantic models for type-safe input validation
- **Error Handling**: Centralized error responses with proper HTTP status codes
- **Multi-Provider Support**: Unified interface for Azure Sora and Google Veo3

## See Also
- [Video Generation](../features/video_generation/)
- [Job Queue](../job_queue/)
- [Health Monitoring](../monitoring/)