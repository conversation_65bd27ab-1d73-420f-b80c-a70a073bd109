# API Module Documentation

## Overview

Production-ready Flask API with Blueprint organization supporting multi-user video generation workflows. Features comprehensive request validation, standardized responses, complete test coverage, and **✅ WORKING video display integration**.

**Latest Update (2025-07-11)**: Frontend video display issues completely resolved - videos now appear correctly in web interface with proper streaming support.

## Module Structure

```
src/api/
├── routes.py           # Main blueprint registration
├── models.py           # Pydantic I/O models
├── job_repository.py   # Database CRUD operations
├── video_routes.py     # Video generation endpoints
├── health_routes.py    # Health check endpoints  
├── job_routes.py       # Job management endpoints
├── file_routes.py      # File serving endpoints
├── debug_routes.py     # Debug endpoints (REMOVE IN PRODUCTION)
└── tests/              # Co-located API tests
```

## API Endpoints

### Core Video Generation
- `GET /` - Main web interface with advanced parameter controls
- `POST /generate` - Create video generation job (supports optional parameters: duration, width, height, model)
- `GET /status/<job_id>` - Poll job status and progress
- `GET /video/<job_id>` - Stream video file
- `GET /download/<job_id>` - Download video file
- `GET /config` - Get UI configuration constraints and defaults for parameter validation

### Health Monitoring & Metrics
- `GET /health` - Overall system health status (database, Azure API, disk space, job queue)
- `GET /health/database` - Database connectivity and performance
- `GET /health/azure` - Azure OpenAI API connectivity
- `GET /metrics` - Comprehensive system metrics (job completion rates, API performance, errors)
- `GET /metrics/jobs` - Job-specific metrics and queue health

### Multi-User Management
- `GET /queue/status` - Current queue status for user's session
- `GET /queue/stats` - Overall queue statistics and performance
- `GET /session/info` - Current session information and limits

### Debug Endpoints (Development Only)
- `GET /debug/azure-config` - **⚠️ SECURITY CRITICAL**: Exposes Azure OpenAI configuration including API keys

**🚨 PRODUCTION SECURITY WARNING 🚨**
- **Risk**: Debug endpoint exposes Azure OpenAI API keys and sensitive configuration
- **Production**: **MUST BE REMOVED** before any production deployment
- **Detection**: Check `src/api/routes.py` for `/debug/` routes before deployment

## Pydantic I/O Models

### Request Models
```python
class VideoGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=500)
    duration: Optional[int] = Field(None, ge=1, le=60)
    width: Optional[int] = Field(None, ge=480, le=3840)
    height: Optional[int] = Field(None, ge=480, le=2160)
    model: Optional[str] = Field(None, regex="^(sora-v1|sora-v2)$")

class JobStatusRequest(BaseModel):
    job_id: str = Field(..., min_length=1, max_length=100)
```

### Response Models
```python
class VideoGenerationResponse(BaseModel):
    success: bool
    job_id: str
    status: str
    message: str
    queue_position: Optional[int] = None
    estimated_wait_minutes: Optional[int] = None

class HealthCheckResponse(BaseModel):
    overall_status: str
    components: dict
    timestamp: str
    version: str

class JobStatusResponse(BaseModel):
    job_id: str
    status: str
    video_url: Optional[str] = None      # ✅ NEW: For video streaming in UI
    download_url: Optional[str] = None   # For download button
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    progress_percentage: Optional[int] = None
```

## ✅ **Recent API Fixes (2025-07-11)**

### **Frontend Video Display Integration**
The API now properly supports video display in the web interface:

#### **Enhanced Job Status Response**
```python
# BEFORE (broken):
JobStatusResponse(
    job_id="job-123",
    status="succeeded",
    download_url=None,  # ❌ Was null due to status mismatch
)

# AFTER (working):
JobStatusResponse(
    job_id="job-123", 
    status="succeeded",
    video_url="/video/job-123",      # ✅ For <video> element streaming
    download_url="/download/job-123" # ✅ For download button
)
```

#### **Status Handling Fixed**
```python
# src/api/job_routes.py - Fixed status check
video_url=f"/video/{job.id}" if job.status == "succeeded" else None,
download_url=f"/download/{job.id}" if job.status == "succeeded" else None,
```

#### **Video Streaming Support**
- ✅ **HTTP Range Requests**: Proper 206 Partial Content support
- ✅ **MIME Type**: Correct `video/mp4` content type
- ✅ **Security**: Path traversal protection maintained
- ✅ **Performance**: Efficient streaming with accept-ranges headers

## Flask Blueprint Architecture

### Blueprint Registration Pattern
```python
from flask import Flask
from src.api.video_routes import video_bp
from src.api.health_routes import health_bp
from src.api.job_routes import job_bp
from src.api.file_routes import file_bp

def register_blueprints(app: Flask) -> None:
    """Register all API blueprints with proper URL prefixes."""
    app.register_blueprint(video_bp, url_prefix="/api/video")
    app.register_blueprint(health_bp, url_prefix="/api/health")
    app.register_blueprint(job_bp, url_prefix="/api/jobs")
    app.register_blueprint(file_bp, url_prefix="/api/files")
```

### Route Function Pattern
```python
from typing import Union
from flask import Response, request, jsonify

def example_route() -> Union[Response, tuple[Response, int]]:
    """Example API route with proper type hints and error handling."""
    try:
        # Extract and validate request data
        request_data = _extract_request_data()
        
        # Process business logic
        result = _process_request(request_data)
        
        # Format standardized response
        return _format_response(result)
        
    except ValidationError as e:
        return _handle_validation_error(e)
    except Exception as e:
        return _handle_unexpected_error(e)
```

## Request Validation Patterns

### Input Validation with Pydantic
```python
from pydantic import ValidationError
from src.api.models import VideoGenerationRequest

def validate_video_request(request_data: dict) -> VideoGenerationRequest:
    """Validate video generation request with Pydantic."""
    try:
        return VideoGenerationRequest(**request_data)
    except ValidationError as e:
        raise ValueError(f"Invalid request data: {e.errors()}")
```

### Security Validation
```python
def _validate_file_path_security(file_path: str) -> bool:
    """Validate file path to prevent directory traversal attacks."""
    # Implementation in src/api/routes.py:33-53
    return not any(dangerous in file_path for dangerous in ['..', '~', '//', '\\'])
```

## Database Integration Patterns

### Repository Pattern Usage
```python
from src.api.job_repository import JobRepository

class APIController:
    def __init__(self, job_repository: JobRepository):
        self.job_repository = job_repository
    
    def get_job_status(self, job_id: str) -> dict:
        """Get job status using repository pattern."""
        job = self.job_repository.get_job_by_id(job_id)
        return job.to_dict() if job else None
```

### Database Session Management
```python
from src.database.connection import get_db_session

def database_operation():
    """Proper database session handling."""
    with get_db_session() as session:
        # Perform database operations
        result = session.query(VideoJobDB).filter_by(job_id=job_id).first()
        session.commit()
        return result
```

## Testing Patterns

### API Route Testing
```python
import pytest
from flask.testing import FlaskClient

class TestVideoRoutes:
    """Test video generation API endpoints."""
    
    def test_generate_video_success(self, client: FlaskClient):
        """Test successful video generation request."""
        response = client.post('/generate', json={
            'prompt': 'A cat playing in a garden',
            'duration': 5
        })
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert 'job_id' in data
    
    def test_generate_video_validation_error(self, client: FlaskClient):
        """Test validation error handling."""
        response = client.post('/generate', json={
            'prompt': '',  # Invalid empty prompt
        })
        assert response.status_code == 400
```

### Pydantic Model Testing
```python
from src.api.models import VideoGenerationRequest

class TestAPIModels:
    """Test Pydantic I/O models."""
    
    def test_video_request_validation(self):
        """Test video request model validation."""
        # Valid request
        request = VideoGenerationRequest(
            prompt="Test prompt",
            duration=5,
            width=1280,
            height=720
        )
        assert request.prompt == "Test prompt"
        
        # Invalid request
        with pytest.raises(ValidationError):
            VideoGenerationRequest(prompt="")  # Empty prompt
```

## Error Handling Patterns

### Standardized Error Responses
```python
def _handle_validation_error(error: ValidationError) -> tuple[Response, int]:
    """Handle Pydantic validation errors."""
    return jsonify({
        'success': False,
        'error': 'Validation failed',
        'details': error.errors()
    }), 400

def _handle_unexpected_error(error: Exception) -> tuple[Response, int]:
    """Handle unexpected errors with logging."""
    logger.error(f"Unexpected error: {error}", exc_info=True)
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500
```

### Security Error Handling
```python
def _handle_security_error(message: str) -> tuple[Response, int]:
    """Handle security violations."""
    logger.warning(f"Security violation: {message}")
    return jsonify({
        'success': False,
        'error': 'Access denied'
    }), 403
```

## Security Considerations

### Production Security Features
- **Input Validation**: Comprehensive validation via Pydantic models
- **Path Traversal Protection**: File path validation in download endpoints
- **Rate Limiting**: API endpoint protection against abuse
- **SQL Injection Protection**: SQLAlchemy ORM with parameterized queries
- **Error Information**: Sanitized error messages without sensitive data

### Security Headers
```python
from flask import Flask

def configure_security_headers(app: Flask) -> None:
    """Configure security headers for API responses."""
    @app.after_request
    def set_security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response
```

## Performance Optimization

### Response Caching
```python
from flask import g
import time

def cache_response(cache_key: str, ttl: int = 300):
    """Cache API responses for performance."""
    def decorator(f):
        def wrapper(*args, **kwargs):
            # Check cache first
            cached = get_cache(cache_key)
            if cached:
                return cached
            
            # Generate response and cache
            result = f(*args, **kwargs)
            set_cache(cache_key, result, ttl)
            return result
        return wrapper
    return decorator
```

### Database Query Optimization
```python
from sqlalchemy.orm import joinedload

def get_job_with_details(job_id: str):
    """Optimized database query with eager loading."""
    return session.query(VideoJobDB)\
        .options(joinedload(VideoJobDB.session_data))\
        .filter_by(job_id=job_id)\
        .first()
```

## Development Workflow

### API Development Commands
```bash
# Test API endpoints
uv run pytest src/api/tests/ -v

# Test specific route
uv run pytest src/api/tests/test_video_routes.py -v

# Test with coverage
uv run pytest src/api/tests/ --cov=src/api

# API model validation
uv run python -c "from src.api.models import *; print('Models loaded successfully')"
```

### API Testing Tools
```bash
# Health check
curl http://localhost:5001/health

# Generate video
curl -X POST http://localhost:5001/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt":"A cat playing","duration":5}'

# Check job status
curl http://localhost:5001/status/<job_id>
```

## Code Quality Standards

### Type Hints
- **Required**: All route functions must have complete return type hints
- **Pattern**: `Union[Response, tuple[Response, int]]` for Flask routes
- **Validation**: Use mypy for type checking

### Documentation
- **Google-style docstrings** for all route functions
- **API endpoint documentation** with request/response examples
- **Error condition documentation** with status codes

### Testing Requirements
- **Unit tests** for all Pydantic models
- **Integration tests** for all API endpoints
- **Error handling tests** for validation and security
- **Performance tests** for high-traffic endpoints

## Best Practices

1. **Always validate input** with Pydantic models
2. **Use repository pattern** for database operations
3. **Implement proper error handling** with standardized responses
4. **Add security headers** to all responses
5. **Test all endpoints** with comprehensive test coverage
6. **Document API contracts** with clear examples
7. **Monitor performance** with metrics and logging
8. **Follow RESTful principles** for endpoint design