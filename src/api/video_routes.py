"""Video generation API routes."""

import asyncio
import logging
from typing import Union

from flask import Blueprint, Response, g, jsonify, render_template, request
from pydantic import ValidationError

from src.api.job_repository import JobRepository
from src.api.models import VideoGenerationRequest, VideoGenerationResponse
from src.config.factory import ConfigurationFactory
from src.core.interfaces import QueueManagerInterface
from src.core.models import APIResponse, GenerationParamsFactory, VideoJob
from src.features.video_generation import get_provider_factory
from src.job_queue.tasks import process_video_generation_with_provider
from src.rate_limiting.provider_rate_limiter import get_provider_rate_limiter

video_bp = Blueprint("video", __name__)
logger = logging.getLogger(__name__)

# Initialize components
job_repository = JobRepository()


def _get_queue_manager() -> QueueManagerInterface:
    """
    Get queue manager instance with dependency injection support.

    Returns:
        QueueManagerInterface: Queue manager instance
    """
    from src.job_queue.manager import QueueManager

    return QueueManager()


@video_bp.route("/")
def index() -> str:
    """
    Render main interface.

    Returns:
        str: Rendered HTML template
    """
    return render_template("index.html")


@video_bp.route("/config")
def get_ui_config() -> Response:
    """
    Get UI configuration constraints for frontend parameter controls.

    Returns:
        dict: JSON response with video generation constraints and defaults
    """
    try:
        video_config = ConfigurationFactory.get_video_config()
        response = APIResponse(
            success=True,
            message="UI configuration retrieved",
            data={
                "defaults": video_config.get_defaults_dict(),
                "constraints": video_config.get_constraints_dict(),
            },
        )
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Failed to get UI config: {e}")
        response = APIResponse(
            success=False, message="Failed to retrieve UI configuration", error=str(e)
        )
        return jsonify(response.model_dump()), 500


@video_bp.route("/provider-capabilities")
def get_provider_capabilities() -> Response:
    """
    Get available video providers and their capabilities for UI adaptation.

    Returns:
        dict: JSON response with provider capabilities mapping
    """
    try:
        factory = get_provider_factory()
        # Run async method in event loop
        capabilities = asyncio.run(factory.get_provider_capabilities())

        response = APIResponse(
            success=True, message="Provider capabilities retrieved", data=capabilities
        )
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Failed to get provider capabilities: {e}")
        response = APIResponse(
            success=False,
            message="Failed to retrieve provider capabilities",
            error=str(e),
            data={},  # Return empty data for graceful fallback
        )
        return jsonify(response.model_dump()), 500


@video_bp.route("/generate", methods=["POST"])
def generate_video() -> Union[Response, tuple[Response, int]]:
    """
    Generate video from prompt with queue management.

    Expected form data:
        - prompt: Text prompt for video generation
        - provider: AI provider to use (optional, defaults to azure_sora)
        - image: Image file for Veo3 image-to-video (optional)
        - duration: Video duration in seconds (optional)
        - width: Video width in pixels (optional)
        - height: Video height in pixels (optional)
        - model: Model version to use (optional)

    Returns:
        Response: JSON response with job ID and queue information
    """
    try:
        logger.info("🎬 Video generation request received")

        # Get session ID from request context
        session_id = getattr(g, "session_id", None)
        if not session_id:
            response = APIResponse(
                success=False,
                message="Session required",
                error="Session not found in request context",
            )
            return jsonify(response.model_dump()), 400

        # Extract and validate request data
        request_data = _extract_video_request_data()

        # Process video job
        job_result = _process_video_job(request_data, session_id)

        # Format response
        return _format_video_response(job_result)

    except ValidationError as e:
        return _handle_validation_error(e)
    except Exception as e:
        return _handle_unexpected_error(e)


def _extract_video_request_data() -> dict:
    """
    Extract and validate video generation request data.

    Returns:
        dict: Validated request data

    Raises:
        ValidationError: If validation fails
    """
    # Handle both JSON and form data
    if request.is_json:
        # Get JSON data
        json_data = request.get_json() or {}
        prompt = (
            json_data.get("prompt", "").strip()
            if isinstance(json_data.get("prompt"), str)
            else ""
        )
        provider = (
            json_data.get("provider", "azure_sora").strip()
            if isinstance(json_data.get("provider"), str)
            else "azure_sora"
        )
        duration = json_data.get("duration")
        width = json_data.get("width")
        height = json_data.get("height")
        model = (
            json_data.get("model", "").strip()
            if isinstance(json_data.get("model"), str)
            else None
        )
        image_file = None  # JSON requests don't support file uploads
    else:
        # Get form data (for web form submissions)
        prompt = request.form.get("prompt", "").strip()
        provider = request.form.get("provider", "azure_sora").strip()
        duration = request.form.get("duration", type=int)
        width = request.form.get("width", type=int)
        height = request.form.get("height", type=int)
        model = request.form.get("model", "").strip()

        # Handle image upload for Veo3
        image_file = None
        if provider == "google_veo3" and "image" in request.files:
            uploaded_file = request.files["image"]
            if uploaded_file and uploaded_file.filename:
                image_file = uploaded_file

    # Create request model for validation
    request_data = {
        "prompt": prompt,
        "provider": provider,
        "duration": duration,
        "width": width,
        "height": height,
        "model": model if model else None,
        "image_file": image_file,  # Store file object for processing
    }

    # Validate with Pydantic model (excluding image_file which isn't in the model)
    validation_data = {k: v for k, v in request_data.items() if k != "image_file"}
    video_request = VideoGenerationRequest(**validation_data)

    # Add back the image file for processing
    validated_data = video_request.model_dump()
    validated_data["image_file"] = image_file

    logger.info(f"📝 Video request validated: {validated_data}")
    return validated_data


def _process_video_job(request_data: dict, session_id: str) -> dict:
    """
    Process video job through queue or direct processing.

    Args:
        request_data: Validated request data
        session_id: User session identifier

    Returns:
        dict: Job result with ID and queue information
    """
    import uuid

    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Create base UI parameters for generation
    ui_parameters = {
        "duration": request_data.get("duration"),
        "width": request_data.get("width"),
        "height": request_data.get("height"),
        "model": request_data.get("model"),
    }

    # Handle image file if present
    image_file = request_data.get("image_file")
    if image_file:
        # For now, we'll store the image filename
        # In a full implementation, you'd save the file and store the path
        ui_parameters["image_filename"] = image_file.filename
        ui_parameters["image_path"] = image_file.filename  # For provider routing
        logger.info(f"📷 Image upload detected: {image_file.filename}")

    # Provider selection with intelligent routing
    requested_provider = request_data.get("provider")
    if requested_provider and requested_provider != "auto":
        # User explicitly requested a provider
        selected_provider = requested_provider
        logger.info(f"🎯 User requested provider: {selected_provider}")
    else:
        # Use provider router for optimal selection
        from src.job_queue.manager import QueueManager
        from src.job_queue.provider_integration import get_provider_router

        queue_manager = QueueManager()
        provider_router = get_provider_router(queue_manager)

        # Create generation params for provider routing
        generation_params_for_routing = {
            "ui_parameters": ui_parameters,
            "prompt": request_data["prompt"],
        }

        selected_provider = provider_router.select_optimal_provider(
            session_id, generation_params_for_routing
        )

        if not selected_provider:
            # Fallback to default provider if routing fails
            selected_provider = "azure_sora"
            logger.warning("⚠️ Provider routing failed, falling back to azure_sora")
        else:
            logger.info(f"🎯 Router selected optimal provider: {selected_provider}")

    # Add selected provider to UI parameters
    ui_parameters["provider"] = selected_provider

    # C2 Provider Rate Limiting Check
    provider_rate_limiter = get_provider_rate_limiter()
    if provider_rate_limiter:
        try:
            # Handle async call within synchronous function using proper pattern
            import asyncio

            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If loop is already running, create a new task
                    import concurrent.futures

                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(
                            asyncio.run,
                            provider_rate_limiter.check_provider_rate_limit(
                                selected_provider, session_id
                            ),
                        )
                        allowed, throttle_reason, retry_after = future.result()
                else:
                    allowed, throttle_reason, retry_after = loop.run_until_complete(
                        provider_rate_limiter.check_provider_rate_limit(
                            selected_provider, session_id
                        )
                    )
            except RuntimeError:
                # No event loop, create one
                allowed, throttle_reason, retry_after = asyncio.run(
                    provider_rate_limiter.check_provider_rate_limit(
                        selected_provider, session_id
                    )
                )

            if not allowed:
                # Provider is rate limited or throttled
                throttle_message = {
                    "rate_limit": "Provider rate limit exceeded",
                    "health_degraded": "Provider experiencing health issues",
                    "error_rate_high": "Provider has high error rate",
                    "response_time_high": "Provider response time too high",
                    "manual_throttle": "Provider manually throttled",
                    "maintenance": "Provider in maintenance mode",
                }.get(
                    throttle_reason.value if throttle_reason else "rate_limit",
                    "Provider temporarily unavailable",
                )

                raise Exception(
                    f"Provider {selected_provider} throttled: {throttle_message}. "
                    f"Retry after {retry_after:.1f} seconds"
                )

            logger.info(f"🎯 Provider rate limit check passed for {selected_provider}")

        except Exception as e:
            logger.warning(f"⚠️ Provider rate limit check failed: {e}")
            # Continue with request but log the issue - fail-open approach

    # Create generation parameters with selected provider
    generation_params = GenerationParamsFactory.create_from_ui_request(
        prompt=request_data["prompt"], ui_parameters=ui_parameters
    )

    # Create job record with provider information
    from datetime import datetime

    job = VideoJob(
        id=job_id,
        session_id=session_id,
        prompt=request_data["prompt"],
        status="pending",
        created_at=datetime.utcnow(),
        api_provider=selected_provider,  # Include selected provider
        input_image_path=ui_parameters.get(
            "image_filename"
        ),  # Include image path if present
        # Provider-aware extensions
        provider_health_status="healthy",  # Assume healthy when submitting
    )

    # Save job to database
    created_job = job_repository.create_job(job)
    if not created_job:
        raise Exception("Failed to create job in database")

    # Assign provider-aware queue position
    queue_manager = _get_queue_manager()
    provider_queue_position = queue_manager.assign_provider_queue_position(
        job_id=job_id,
        provider=selected_provider,
        session_id=session_id,
        priority=0,  # Default priority, can be enhanced later
    )

    # Update job with queue position information
    if provider_queue_position > 0:
        job.provider_queue_position = provider_queue_position
        job_repository.update_job_by_id(
            job_id, {"provider_queue_position": provider_queue_position}
        )
        logger.info(
            f"📍 Assigned provider queue position: {provider_queue_position} (provider: {selected_provider})"
        )

    # Prepare job data for Celery task (match task signature exactly)
    job_data = {
        "prompt": request_data["prompt"],
        "ui_parameters": ui_parameters,
    }

    logger.info(f"🎯 Using provider: {selected_provider}")
    if image_file:
        logger.info(
            f"📷 Image attached: {image_file.filename} ({image_file.content_type})"
        )

    # Queue the provider-aware task AFTER creating database record (with correct parameters)
    logger.info(
        "🚀 CALLING provider-aware task: process_video_generation_with_provider"
    )
    logger.info(
        f"🚀 TASK ARGS: session_id={session_id}, job_id={job_id}, provider={selected_provider}"
    )
    logger.info(f"🚀 TASK DATA KEYS: {list(job_data.keys()) if job_data else 'None'}")

    task_result = process_video_generation_with_provider.delay(
        session_id, job_id, selected_provider, job_data
    )

    logger.info(
        f"🚀 TASK QUEUED: task_id={task_result.id}, task_name={task_result.name}"
    )
    logger.info(f"🚀 TASK STATE: {task_result.state}")
    logger.info(
        f"🚀 CONFIRMED: Provider-aware task successfully queued with provider={selected_provider}"
    )

    # Get queue position from queue manager
    try:
        queue_manager = _get_queue_manager()
        queue_position = queue_manager.assign_queue_position(job_id, session_id)
        queue_status = queue_manager.get_queue_status(session_id)
        estimated_wait = queue_status.get("user_job_positions", [{}])[0].get(
            "estimated_wait_minutes", 3
        )
    except (ImportError, Exception) as e:
        logger.warning(f"Queue manager error: {e}")
        queue_position = 1
        estimated_wait = 3  # Default 3 minutes

    provider_name = ui_parameters.get("provider", "azure_sora")
    logger.info(
        f"📝 Job queued with ID: {job_id}, provider: {provider_name}, position: {queue_position}"
    )

    return {
        "job_id": job_id,
        "queue_position": queue_position,
        "estimated_wait": estimated_wait,
        "status": "pending",
        "provider": provider_name,
    }


def _format_video_response(job_result: dict) -> Response:
    """
    Format video generation response.

    Args:
        job_result: Job result data

    Returns:
        Response: JSON response
    """
    response_data = VideoGenerationResponse(
        success=True,
        job_id=job_result["job_id"],
        status=job_result["status"],
        message=f"Video generation job created successfully using {job_result.get('provider', 'default provider')}. Queue position: {job_result['queue_position']}",
        queue_position=job_result["queue_position"],
        estimated_wait_minutes=job_result["estimated_wait"],
    )

    logger.info(f"✅ Video generation response: {response_data.model_dump()}")
    return jsonify(response_data.model_dump())


def _handle_validation_error(error: ValidationError) -> tuple[Response, int]:
    """
    Handle validation errors.

    Args:
        error: Pydantic validation error

    Returns:
        tuple[Response, int]: Error response and status code
    """
    logger.warning(f"🚨 Validation error: {error}")

    response = APIResponse(
        success=False, message="Request validation failed", error=str(error)
    )
    return jsonify(response.model_dump()), 400


def _handle_unexpected_error(error: Exception) -> tuple[Response, int]:
    """
    Handle unexpected errors.

    Args:
        error: Exception that occurred

    Returns:
        tuple[Response, int]: Error response and status code
    """
    logger.error(f"💥 Unexpected error in video generation: {error}")

    response = APIResponse(
        success=False,
        message="Internal server error during video generation",
        error="An unexpected error occurred",
    )
    return jsonify(response.model_dump()), 500
