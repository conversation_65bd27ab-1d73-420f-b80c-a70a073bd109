"""Debug API routes - REMOVE BEFORE PRODUCTION DEPLOYMENT."""

import logging
import os
from typing import Union

from flask import Blueprint, Response, jsonify

from src.core.models import APIResponse

debug_bp = Blueprint("debug", __name__)
logger = logging.getLogger(__name__)

# 🚨 PRODUCTION SECURITY WARNING 🚨
# This module contains debug endpoints that expose sensitive configuration
# including Azure OpenAI API keys. These endpoints MUST BE REMOVED before
# any production deployment to prevent security vulnerabilities.


@debug_bp.route("/debug/azure-config")
def get_azure_config() -> Union[Response, tuple[Response, int]]:
    """
    ⚠️ SECURITY CRITICAL: Debug endpoint that exposes Azure OpenAI configuration.

    🚨 PRODUCTION SECURITY WARNING 🚨
    This endpoint exposes Azure OpenAI API keys and sensitive configuration.

    - Development: Safe to use locally for debugging API configuration issues
    - Production: MUST BE REMOVED before any production deployment
    - Detection: Check src/api/routes.py for /debug/ routes before deployment
    - Impact: Complete compromise of Azure OpenAI resources if exposed in production

    Returns:
        Response: JSON response with Azure configuration (INCLUDING API KEYS)
    """
    logger.warning("🚨 SECURITY: Debug endpoint /debug/azure-config accessed")
    logger.warning("🚨 SECURITY: This endpoint exposes sensitive Azure API keys")
    logger.warning("🚨 SECURITY: MUST BE REMOVED before production deployment")

    try:
        # Get Azure OpenAI configuration from environment
        # Treat empty strings as NOT_SET for consistency
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT", "")
        api_version = os.getenv("AZURE_OPENAI_API_VERSION", "")
        deployment_name = os.getenv("AZURE_OPENAI_SORA_DEPLOYMENT", "")
        api_key = os.getenv("AZURE_OPENAI_API_KEY", "")

        azure_config = {
            "endpoint": endpoint if endpoint else "NOT_SET",
            "api_version": api_version if api_version else "NOT_SET",
            "deployment_name": deployment_name if deployment_name else "NOT_SET",
            # 🚨 SECURITY RISK: API key exposure
            "api_key": api_key if api_key else "NOT_SET",
            "api_key_length": len(api_key) if api_key else 0,
            "has_api_key": bool(api_key),
        }

        response = APIResponse(
            success=True,
            message="🚨 SECURITY WARNING: Azure config exposed (REMOVE IN PRODUCTION)",
            data=azure_config,
        )

        logger.warning(f"🚨 SECURITY: Azure config exposed: {azure_config}")
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"💥 Debug endpoint error: {e}")

        response = APIResponse(
            success=False,
            message="Failed to retrieve Azure configuration",
            error=str(e),
        )
        return jsonify(response.model_dump()), 500


# Production deployment validation function
def validate_production_security() -> bool:
    """
    Validate that debug endpoints are not accessible in production.

    Returns:
        bool: True if secure for production, False if debug endpoints exist
    """
    # This function should be called during deployment validation
    # to ensure debug endpoints are not accessible in production
    return False  # Always return False since debug endpoints exist
