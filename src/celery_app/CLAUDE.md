# Celery Application Configuration

Comprehensive Celery configuration and task management for distributed background video generation processing with Redis broker, queue management, and production-ready scalability.

## Architecture Overview

**Distributed Task Processing**: Celery-based background job processing for video generation with Redis broker, fair queue management, and multi-user session isolation.

**Production-Ready Configuration**: Factory pattern configuration with comprehensive error handling, retry logic, monitoring, and horizontal scaling support for enterprise deployment.

**Current Implementation**: Primary configuration located in `src/job_queue/celery_app.py` following vertical slice architecture, with this directory prepared for future Celery application reorganization.

```
Celery Architecture:
├── Core Configuration (src/job_queue/)
│   ├── celery_app.py                 # Celery application factory
│   ├── tasks.py                      # Video generation tasks
│   ├── worker.py                     # Worker management
│   └── manager.py                    # Queue management
├── Redis Broker Integration
│   ├── Connection pooling            # Efficient broker connectivity
│   ├── Rate limiting integration     # Global API coordination
│   └── Session storage               # Multi-user isolation
├── Monitoring and Management
│   ├── Flower web UI                 # Real-time monitoring
│   ├── Health checks                 # System health validation
│   └── Metrics collection            # Performance monitoring
└── Production Features
    ├── Horizontal scaling            # Multi-worker deployment
    ├── Graceful shutdown             # Signal handling
    └── Error recovery                # Comprehensive retry logic
```

## Celery Application Configuration

### Core Configuration (`src/job_queue/celery_app.py`)

**Factory Pattern Configuration**: Environment-driven Celery application creation with production-ready defaults.

```python
from celery import Celery
from src.config.factory import ConfigurationFactory

def create_celery_app(config_name: str = "development") -> Celery:
    """
    Create Celery application with environment-specific configuration.
    
    Args:
        config_name: Configuration environment (development, testing, production)
        
    Returns:
        Configured Celery application instance
    """
    
    # Get environment configuration
    config = ConfigurationFactory.get_app_config()
    
    # Create Celery application
    celery = Celery("sora_multiuser")
    
    # Core broker configuration
    celery.conf.update(
        broker_url=config.CELERY_BROKER_URL,
        result_backend=config.CELERY_RESULT_BACKEND,
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        
        # Worker configuration
        worker_prefetch_multiplier=1,          # Fair distribution
        task_acks_late=True,                   # Reliability
        task_reject_on_worker_lost=True,       # Failure handling
        worker_max_tasks_per_child=50,         # Periodic restart
        
        # Task execution limits
        task_time_limit=1800,                  # 30 minutes hard limit
        task_soft_time_limit=1500,             # 25 minutes soft limit
        
        # Queue routing
        task_routes={
            "src.job_queue.tasks.process_video_generation": {"queue": "video_generation"},
            "src.job_queue.tasks.update_queue_positions": {"queue": "maintenance"}
        },
        
        # Error handling
        task_annotations={
            "*": {"rate_limit": "10/s"},        # Global rate limiting
        }
    )
    
    return celery

# Create default application instance
celery = create_celery_app()
```

**Configuration Settings**:
- **Broker URL**: `redis://localhost:6379/0` (configurable)
- **Result Backend**: Redis for result storage and retrieval
- **Serialization**: JSON for cross-platform compatibility
- **Worker Settings**: Fair distribution and reliability configuration
- **Time Limits**: 30-minute hard limit, 25-minute soft limit
- **Queue Routing**: Separate queues for video generation and maintenance

### Environment Configuration

```python
# Environment variables for Celery configuration
CELERY_BROKER_URL = "redis://localhost:6379/0"
CELERY_RESULT_BACKEND = "redis://localhost:6379/0"

# Redis connection details
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 0

# Queue configuration
MAX_CONCURRENT_JOBS_PER_SESSION = 3
QUEUE_PRIORITY_ENABLED = True

# Worker configuration
CELERY_WORKER_CONCURRENCY = 4
CELERY_WORKER_LOGLEVEL = "INFO"
```

## Task Definitions (`src/job_queue/tasks.py`)

### Video Generation Task

**Primary Background Task**: Complete video generation workflow with Azure API integration, polling, and error handling.

```python
from celery import Task
from celery.exceptions import Retry

class VideoGenerationTask(Task):
    """Custom task class with enhanced error handling and retry logic."""
    
    autoretry_for = (ConnectionError, TimeoutError)
    retry_kwargs = {"max_retries": 3, "countdown": 60}
    retry_backoff = True
    retry_jitter = True

@celery.task(base=VideoGenerationTask, bind=True, name="process_video_generation")
def process_video_generation(self, session_id: str, job_id: str, job_data: dict) -> dict:
    """
    Process video generation with complete Azure API workflow.
    
    Args:
        session_id: User session identifier for multi-user isolation
        job_id: Unique job identifier for tracking
        job_data: Video generation parameters and configuration
        
    Returns:
        Job completion result with status and file information
        
    Raises:
        Retry: On recoverable errors (connection issues, temporary failures)
        Exception: On permanent failures (invalid parameters, quota exceeded)
    """
    
    logger.info(f"Starting video generation for job {job_id} in session {session_id}")
    
    try:
        # 1. Initialize Azure client
        azure_client = SoraClient()
        
        # 2. Submit job to Azure API
        initial_job = azure_client.create_video_job(
            prompt=job_data["prompt"],
            duration=job_data.get("duration", 5),
            width=job_data.get("width", 1280),
            height=job_data.get("height", 720)
        )
        
        # 3. Update job status to running
        update_job_status(job_id, "running", {
            "generation_id": initial_job.generation_id,
            "worker_id": self.request.hostname
        })
        
        # 4. Poll Azure API until completion
        poll_interval = 5.0  # Start with 5 seconds
        max_poll_interval = 30.0
        max_total_time = 1500  # 25 minutes (within soft time limit)
        start_time = time.time()
        
        while time.time() - start_time < max_total_time:
            current_job = azure_client.poll_job_status(job_id, initial_job.generation_id)
            
            if current_job.status == "succeeded":
                # 5. Download video file
                file_path = azure_client.download_video(current_job.download_url, job_id)
                
                # 6. Update job completion
                completion_data = {
                    "file_path": file_path,
                    "download_url": current_job.download_url,
                    "completed_at": datetime.utcnow(),
                    "processing_time": time.time() - start_time
                }
                update_job_status(job_id, "succeeded", completion_data)
                
                logger.info(f"Video generation completed for job {job_id}")
                return {"status": "succeeded", "file_path": file_path}
                
            elif current_job.status == "failed":
                error_message = f"Azure API job failed: {current_job.error_message}"
                update_job_status(job_id, "failed", {"error_message": error_message})
                raise Exception(error_message)
            
            # Exponential backoff with jitter
            time.sleep(poll_interval)
            poll_interval = min(poll_interval * 1.5, max_poll_interval)
        
        # Timeout handling
        error_message = "Video generation timed out"
        update_job_status(job_id, "failed", {"error_message": error_message})
        raise Exception(error_message)
        
    except (ConnectionError, TimeoutError) as e:
        # Retryable errors
        logger.warning(f"Retryable error in job {job_id}: {e}")
        update_job_status(job_id, "pending", {"retry_count": self.request.retries + 1})
        raise self.retry(exc=e)
        
    except Exception as e:
        # Permanent failures
        logger.error(f"Permanent failure in job {job_id}: {e}")
        update_job_status(job_id, "failed", {"error_message": str(e)})
        raise
```

### Queue Management Task

**Periodic Maintenance Task**: Queue position updates and status maintenance.

```python
@celery.task(name="update_queue_positions")
def update_queue_positions() -> dict:
    """
    Update queue positions for all pending jobs and provide real-time feedback.
    
    Returns:
        Statistics about queue updates performed
    """
    
    try:
        from src.job_queue.manager import QueueManager
        
        queue_manager = QueueManager()
        updated_jobs = queue_manager.update_all_positions()
        
        logger.info(f"Updated queue positions for {len(updated_jobs)} jobs")
        
        return {
            "updated_jobs": len(updated_jobs),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error updating queue positions: {e}")
        raise
```

## Worker Management (`src/job_queue/worker.py`)

### WorkerManager Class

**Comprehensive Worker Management**: Graceful shutdown, health monitoring, and statistics collection.

```python
class WorkerManager:
    """
    Manages Celery worker lifecycle with graceful shutdown and health monitoring.
    
    Features:
    - Signal handling for graceful shutdown
    - Health monitoring and statistics
    - Worker scaling interface
    - Comprehensive logging
    """
    
    def __init__(self, concurrency: int = 2, queue: str = "video_generation"):
        self.concurrency = concurrency
        self.queue = queue
        self.worker = None
        self.is_shutting_down = False
        
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self._handle_shutdown)
        signal.signal(signal.SIGINT, self._handle_shutdown)
    
    def start_worker(self) -> None:
        """Start Celery worker with configuration."""
        
        logger.info(f"Starting Celery worker with concurrency {self.concurrency}")
        
        # Worker configuration
        argv = [
            "worker",
            f"--concurrency={self.concurrency}",
            f"--queues={self.queue}",
            "--loglevel=INFO",
            "--prefetch-multiplier=1",
            "--max-tasks-per-child=50"
        ]
        
        # Start worker
        celery.worker_main(argv)
    
    def get_worker_health(self) -> Dict[str, Any]:
        """Get comprehensive worker health status."""
        
        try:
            # Ping active workers
            inspector = celery.control.inspect()
            active_workers = inspector.ping()
            
            if not active_workers:
                return {
                    "status": "unhealthy",
                    "error": "No active workers found"
                }
            
            # Get worker statistics
            stats = inspector.stats()
            active_tasks = inspector.active()
            scheduled_tasks = inspector.scheduled()
            
            return {
                "status": "healthy",
                "active_workers": len(active_workers),
                "worker_stats": stats,
                "active_tasks": len(active_tasks) if active_tasks else 0,
                "scheduled_tasks": len(scheduled_tasks) if scheduled_tasks else 0,
                "last_check": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def _handle_shutdown(self, signum: int, frame) -> None:
        """Handle graceful shutdown signals."""
        
        logger.info(f"Received signal {signum}, initiating graceful shutdown")
        self.is_shutting_down = True
        
        if self.worker:
            self.worker.should_stop = True
```

## Queue Management Integration

### Fair Resource Allocation (`src/job_queue/manager.py`)

**Multi-User Queue Management**: Session isolation, priority handling, and position tracking.

```python
class QueueManager:
    """
    Manages video generation queue with multi-user support and fair allocation.
    
    Features:
    - Session-based isolation
    - Priority queue handling
    - Position tracking and estimation
    - Concurrent job limits
    """
    
    def __init__(self, redis_client=None, max_concurrent_jobs: int = 5):
        self.redis_client = redis_client or get_redis_client()
        self.max_concurrent_jobs = max_concurrent_jobs
        self.max_jobs_per_session = 3
    
    def submit_job(self, session_id: str, job_data: dict) -> QueuedVideoJob:
        """Submit job to queue with session validation and position assignment."""
        
        # Validate session limits
        session_jobs = self.get_session_job_count(session_id)
        if session_jobs >= self.max_jobs_per_session:
            raise ValueError(f"Session {session_id} has reached job limit ({self.max_jobs_per_session})")
        
        # Create queued job
        job = QueuedVideoJob(
            session_id=session_id,
            queue_position=self._get_next_position(),
            priority=job_data.get("priority", 0),
            **job_data
        )
        
        # Submit to Celery
        task = process_video_generation.delay(session_id, job.id, job_data)
        job.worker_id = task.id
        
        # Store in queue
        self._store_queued_job(job)
        
        logger.info(f"Submitted job {job.id} to queue for session {session_id}")
        return job
    
    def get_queue_status(self, session_id: str) -> QueueStatus:
        """Get current queue status for user session."""
        
        total_jobs = self._get_total_pending_jobs()
        session_jobs = self.get_session_job_count(session_id)
        position = self._get_session_position(session_id)
        active_workers = self._get_active_worker_count()
        
        # Calculate estimated wait time
        avg_processing_time = 300  # 5 minutes average
        estimated_wait = self._calculate_wait_time(position, active_workers, avg_processing_time)
        
        return QueueStatus(
            total_jobs=total_jobs,
            position=position,
            estimated_wait_minutes=estimated_wait,
            active_workers=active_workers,
            session_jobs=session_jobs,
            can_submit_more=session_jobs < self.max_jobs_per_session,
            max_jobs_per_session=self.max_jobs_per_session
        )
```

## Redis Broker Configuration

### Connection Management

**Redis Integration**: Broker configuration with connection pooling and failover handling.

```python
import redis
from redis.connection import ConnectionPool

def get_redis_client() -> redis.Redis:
    """Create Redis client with connection pooling."""
    
    config = ConfigurationFactory.get_app_config()
    
    # Connection pool for efficiency
    pool = ConnectionPool(
        host=config.REDIS_HOST,
        port=config.REDIS_PORT,
        db=config.REDIS_DB,
        max_connections=20,
        retry_on_timeout=True,
        socket_connect_timeout=5,
        socket_timeout=5
    )
    
    return redis.Redis(connection_pool=pool)

# Celery broker configuration
def configure_celery_broker(celery_app: Celery) -> None:
    """Configure Celery broker with Redis settings."""
    
    config = ConfigurationFactory.get_app_config()
    
    # Broker configuration
    broker_url = f"redis://{config.REDIS_HOST}:{config.REDIS_PORT}/{config.REDIS_DB}"
    
    celery_app.conf.update(
        broker_url=broker_url,
        result_backend=broker_url,
        
        # Connection settings
        broker_connection_retry_on_startup=True,
        broker_connection_retry=True,
        broker_connection_max_retries=10,
        
        # Result backend settings
        result_backend_transport_options={
            "master_name": "mymaster"  # For Redis Sentinel
        }
    )
```

### Rate Limiting Integration

**Global API Coordination**: Distributed rate limiting using shared Redis state.

```python
from src.rate_limiting.limiter import GlobalRateLimiter

class RateLimitedTask(Task):
    """Task class with integrated rate limiting for Azure API calls."""
    
    def __init__(self):
        self.rate_limiter = GlobalRateLimiter(
            redis_client=get_redis_client(),
            requests_per_second=10  # Azure API limit
        )
    
    def apply_async(self, *args, **kwargs):
        """Apply task with rate limiting."""
        
        # Acquire rate limit token
        if not self.rate_limiter.acquire():
            # Retry with delay if rate limited
            kwargs.setdefault("countdown", 1)
            return self.retry(*args, **kwargs)
        
        return super().apply_async(*args, **kwargs)
```

## Monitoring and Management

### Flower Web UI

**Real-Time Monitoring**: Web-based Celery monitoring and management interface.

```bash
# Start Flower monitoring
uv run celery -A src.job_queue.celery_app flower

# Access Flower web UI
# URL: http://localhost:5555
# Features:
# - Real-time task monitoring
# - Worker status and statistics
# - Queue depth and processing rates
# - Task result inspection
# - Worker scaling controls
```

**Flower Configuration**:
```python
# flower_config.py
broker_api = "http://localhost:15672/api/"  # RabbitMQ API (if used)
flower_basic_auth = ["admin:password"]      # Basic authentication
flower_db = "flower.db"                     # Task history database
flower_persistent = True                    # Persistent task history
```

### Health Check Integration

**System Health Monitoring**: Comprehensive health checks for Celery components.

```python
def check_celery_health() -> Dict[str, Any]:
    """Check Celery system health."""
    
    health_status = {
        "status": "healthy",
        "broker": {},
        "workers": {},
        "queues": {},
        "errors": []
    }
    
    try:
        # Check broker connectivity
        broker_health = check_redis_broker()
        health_status["broker"] = broker_health
        
        if broker_health["status"] != "healthy":
            health_status["status"] = "degraded"
        
        # Check worker status
        worker_manager = WorkerManager()
        worker_health = worker_manager.get_worker_health()
        health_status["workers"] = worker_health
        
        if worker_health["status"] != "healthy":
            health_status["status"] = "unhealthy"
        
        # Check queue status
        queue_manager = QueueManager()
        queue_stats = queue_manager.get_queue_statistics()
        health_status["queues"] = queue_stats
        
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["errors"].append(str(e))
    
    return health_status

def check_redis_broker() -> Dict[str, Any]:
    """Check Redis broker health."""
    
    try:
        redis_client = get_redis_client()
        
        # Test connectivity
        pong = redis_client.ping()
        if not pong:
            return {"status": "unhealthy", "error": "Redis ping failed"}
        
        # Check memory usage
        info = redis_client.info("memory")
        memory_usage = info.get("used_memory_human", "unknown")
        
        return {
            "status": "healthy",
            "memory_usage": memory_usage,
            "connected_clients": info.get("connected_clients", 0),
            "last_check": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
```

## Production Deployment

### Multi-Service Startup

**Production Service Orchestration**: Complete multi-service startup for production deployment.

```bash
# Production startup sequence

# 1. Start Redis server (required for broker and rate limiting)
redis-server /etc/redis/redis.conf

# 2. Start Celery workers (multiple instances for redundancy)
uv run celery -A src.job_queue.celery_app worker \
    --loglevel=info \
    --concurrency=4 \
    --queues=video_generation \
    --hostname=worker1@%h

uv run celery -A src.job_queue.celery_app worker \
    --loglevel=info \
    --concurrency=4 \
    --queues=video_generation \
    --hostname=worker2@%h

# 3. Start Celery Beat (for periodic tasks)
uv run celery -A src.job_queue.celery_app beat \
    --loglevel=info \
    --schedule=/opt/sora/data/celerybeat-schedule

# 4. Start Flower monitoring
uv run celery -A src.job_queue.celery_app flower \
    --port=5555 \
    --broker=redis://localhost:6379/0

# 5. Start Flask application
uv run python src/main.py
```

### Docker Compose Integration

**Container Orchestration**: Celery services in Docker Compose for production deployment.

```yaml
# docker-compose.production.yml excerpt
services:
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    command: redis-server --requirepass ${REDIS_PASSWORD}
  
  celery-worker-1:
    build: .
    command: celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4
    environment:
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - redis
    volumes:
      - ./uploads:/app/uploads
  
  celery-worker-2:
    build: .
    command: celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4
    environment:
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - redis
    volumes:
      - ./uploads:/app/uploads
  
  celery-beat:
    build: .
    command: celery -A src.job_queue.celery_app beat --loglevel=info
    environment:
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - redis
    volumes:
      - celery_beat_data:/app/celerybeat
  
  flower:
    build: .
    command: celery -A src.job_queue.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - redis
```

### Scaling and Performance

**Horizontal Scaling**: Multi-worker deployment with load balancing and resource management.

```python
# Worker scaling configuration
WORKER_SCALING_CONFIG = {
    "min_workers": 2,                   # Minimum worker instances
    "max_workers": 8,                   # Maximum worker instances
    "scale_up_threshold": 10,           # Queue depth trigger for scaling up
    "scale_down_threshold": 2,          # Queue depth trigger for scaling down
    "scale_cooldown": 300,              # Cooldown period between scaling operations
}

def auto_scale_workers() -> Dict[str, Any]:
    """Automatic worker scaling based on queue depth."""
    
    inspector = celery.control.inspect()
    
    # Get current queue depth
    active_queues = inspector.active_queues()
    queue_depth = sum(len(queue) for queue in active_queues.values())
    
    # Get current worker count
    active_workers = len(inspector.ping())
    
    scaling_action = None
    
    if queue_depth > WORKER_SCALING_CONFIG["scale_up_threshold"]:
        if active_workers < WORKER_SCALING_CONFIG["max_workers"]:
            scaling_action = "scale_up"
            # Trigger worker scaling up
            
    elif queue_depth < WORKER_SCALING_CONFIG["scale_down_threshold"]:
        if active_workers > WORKER_SCALING_CONFIG["min_workers"]:
            scaling_action = "scale_down"
            # Trigger worker scaling down
    
    return {
        "queue_depth": queue_depth,
        "active_workers": active_workers,
        "scaling_action": scaling_action,
        "timestamp": datetime.utcnow().isoformat()
    }
```

## Integration Patterns

### Flask Application Integration

**Seamless Integration**: Celery integration with Flask application factory pattern.

```python
# src/main.py
def create_app(config_name: str = "development") -> Flask:
    """Create Flask application with Celery integration."""
    
    app = Flask(__name__)
    app.config.from_object(get_config(config_name))
    
    # Initialize database
    db.init_app(app)
    
    # Initialize Celery
    try:
        from src.job_queue.celery_app import create_celery_app
        app.celery = create_celery_app(config_name)
        app.celery.conf.update(app.config)
        
        # Register Celery task context
        class ContextTask(app.celery.Task):
            def __call__(self, *args, **kwargs):
                with app.app_context():
                    return self.run(*args, **kwargs)
        
        app.celery.Task = ContextTask
        
    except ImportError as e:
        logger.warning(f"Celery not available: {e}")
        app.celery = None
    
    return app
```

### API Endpoint Integration

**Task Submission**: RESTful API integration with Celery task submission.

```python
# src/api/routes.py
@api_bp.route("/generate", methods=["POST"])
def generate_video() -> Tuple[Response, int]:
    """Submit video generation job to Celery queue."""
    
    try:
        # Validate request
        request_data = request.get_json()
        params = GenerationParamsFactory.create_from_ui_request(
            prompt=request_data["prompt"],
            ui_parameters=request_data
        )
        
        # Get or create session
        session_id, session_data = get_or_create_session(request.remote_addr)
        
        # Submit to queue
        queue_manager = QueueManager()
        queued_job = queue_manager.submit_job(session_id, {
            "prompt": params.prompt,
            "duration": params.duration,
            "width": params.width,
            "height": params.height
        })
        
        # Return response
        response = APIResponse(
            success=True,
            message="Video generation job submitted",
            data={
                "job_id": queued_job.id,
                "queue_position": queued_job.queue_position,
                "estimated_wait_minutes": queue_manager.estimate_wait_time(session_id)
            }
        )
        
        return jsonify(response.model_dump()), 200
        
    except Exception as e:
        logger.error(f"Error submitting video generation job: {e}")
        error_response = APIResponse(
            success=False,
            message="Failed to submit video generation job",
            error=str(e)
        )
        return jsonify(error_response.model_dump()), 500
```

## Future Reorganization Considerations

### Potential Directory Structure

**Future Organization**: Potential reorganization to centralize Celery configuration in `src/celery_app/`.

```
src/celery_app/                           # Future centralized Celery module
├── __init__.py                           # Module initialization
├── app.py                                # Core Celery application
├── config.py                             # Celery-specific configuration
├── workers.py                            # Worker management utilities
├── monitoring.py                         # Health checks and monitoring
├── tasks/                                # Task definitions
│   ├── __init__.py
│   ├── video_generation.py              # Video generation tasks
│   ├── maintenance.py                    # Periodic maintenance tasks
│   └── monitoring.py                     # Monitoring tasks
└── tests/                                # Celery-specific tests
    ├── test_app.py
    ├── test_workers.py
    └── test_tasks.py
```

### Migration Strategy

**Gradual Migration**: Approach for moving Celery configuration to dedicated module.

```python
# Phase 1: Create celery_app module with core application
# src/celery_app/app.py
from src.job_queue.celery_app import create_celery_app

# Re-export for backward compatibility
__all__ = ["create_celery_app", "celery"]

# Phase 2: Move task definitions
# src/celery_app/tasks/video_generation.py
from src.job_queue.tasks import process_video_generation

# Phase 3: Centralize configuration
# src/celery_app/config.py
# Centralized Celery configuration management

# Phase 4: Update imports throughout codebase
# Update all imports to use src.celery_app instead of src.job_queue.celery_app
```

## Dependencies

### Core Dependencies
- **celery**: Distributed task queue framework
- **redis**: Redis client for broker connectivity
- **redis-py**: Redis client library
- **kombu**: Celery messaging library

### Monitoring Dependencies
- **flower**: Web-based Celery monitoring
- **psutil**: System resource monitoring
- **prometheus-client**: Metrics collection (optional)

### Production Dependencies
- **gevent**: Async worker pool (optional)
- **eventlet**: Alternative async worker pool (optional)
- **librabbitmq**: High-performance RabbitMQ client (optional)

## Development Guidelines

### Task Design Principles
- **Idempotent Tasks**: Ensure tasks can be safely retried
- **Session Isolation**: Use session IDs for multi-user isolation
- **Comprehensive Logging**: Include context and timing information
- **Error Handling**: Distinguish between retryable and permanent errors

### Configuration Management
- **Environment-Based**: Use environment variables for all configuration
- **Factory Pattern**: Follow established configuration factory pattern
- **Testable Configuration**: Support different configurations for testing
- **Documentation**: Document all configuration options and defaults

### Monitoring and Alerting
- **Health Checks**: Implement comprehensive health monitoring
- **Metrics Collection**: Track key performance indicators
- **Error Tracking**: Log errors with sufficient context for debugging
- **Performance Monitoring**: Monitor task execution times and resource usage

### Production Deployment
- **Graceful Shutdown**: Handle signals properly for graceful worker shutdown
- **Horizontal Scaling**: Design for multi-worker deployment
- **Resource Management**: Configure appropriate resource limits
- **Monitoring Integration**: Integrate with production monitoring systems