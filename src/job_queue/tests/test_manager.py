"""Tests for queue manager functionality."""

from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest

from src.job_queue.manager import QueueManager


@pytest.mark.integration
class TestQueueManager:
    """Test cases for QueueManager functionality."""

    @pytest.fixture
    def mock_config(self):
        """Create mock configuration for testing."""
        config = Mock()
        config.MAX_CONCURRENT_JOBS = 5
        config.MAX_JOBS_PER_SESSION = 3
        config.AVERAGE_PROCESSING_TIME_MINUTES = 5
        return config

    @pytest.fixture
    def mock_repository(self):
        """Create mock job repository for testing."""
        return Mock()

    @pytest.fixture
    def queue_manager(self, mock_config, mock_repository):
        """Create QueueManager instance with mocked dependencies."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config",
            return_value=mock_config,
        ):
            with patch(
                "src.api.job_repository.JobRepository", return_value=mock_repository
            ):
                manager = QueueManager("testing")
                manager.repository = mock_repository  # Ensure mock is used
                return manager

    def test_queue_manager_initialization(self, queue_manager, mock_config):
        """Test QueueManager initialization with configuration."""
        assert queue_manager.max_concurrent_jobs == 5
        assert queue_manager.max_jobs_per_session == 3
        assert queue_manager.average_processing_time_minutes == 5

    def test_get_queue_status_basic(self, queue_manager, mock_repository):
        """Test basic queue status retrieval."""
        # Setup mock data
        now = datetime.utcnow()
        pending_jobs = [
            Mock(id="job1", session_id="session1", created_at=now, priority=1),
            Mock(id="job2", session_id="session1", created_at=now, priority=1),
            Mock(id="job3", session_id="session2", created_at=now, priority=1),
        ]
        running_jobs = [
            Mock(id="job4", session_id="session3"),
        ]

        mock_repository.get_jobs_by_status.side_effect = [pending_jobs, running_jobs]

        # Get queue status for session1
        status = queue_manager.get_queue_status("session1")

        # Verify status information
        assert status["total_pending_jobs"] == 3
        assert status["total_running_jobs"] == 1
        assert status["user_pending_jobs"] == 2  # 2 jobs for session1
        assert status["active_workers"] == 1
        assert status["max_concurrent_jobs"] == 5
        assert status["max_jobs_per_session"] == 3
        assert len(status["user_job_positions"]) == 2  # Position info for user's jobs

    def test_get_queue_status_empty_queue(self, queue_manager, mock_repository):
        """Test queue status when queue is empty."""
        mock_repository.get_jobs_by_status.return_value = []

        status = queue_manager.get_queue_status("session1")

        assert status["total_pending_jobs"] == 0
        assert status["total_running_jobs"] == 0
        assert status["user_pending_jobs"] == 0
        assert status["user_job_positions"] == []
        assert status["can_submit_more"] is True

    def test_can_submit_job_within_limits(self, queue_manager, mock_repository):
        """Test job submission check when within limits."""
        # User has 1 active job (below limit of 3)
        mock_repository.get_active_jobs_by_session.return_value = [Mock()]
        mock_repository.get_jobs_by_status.side_effect = [
            [],
            [],
        ]  # No pending/running jobs

        can_submit, reason = queue_manager.can_submit_job("session1")

        assert can_submit is True
        assert "allowed" in reason

    def test_can_submit_job_session_limit_exceeded(
        self, queue_manager, mock_repository
    ):
        """Test job submission check when session limit exceeded."""
        # User has 3 active jobs (at limit)
        mock_repository.get_active_jobs_by_session.return_value = [
            Mock(),
            Mock(),
            Mock(),
        ]

        can_submit, reason = queue_manager.can_submit_job("session1")

        assert can_submit is False
        assert "Maximum 3 jobs per session exceeded" in reason

    def test_can_submit_job_system_capacity_exceeded(
        self, queue_manager, mock_repository
    ):
        """Test job submission check when system capacity exceeded."""
        # User within session limit but system at capacity
        mock_repository.get_active_jobs_by_session.return_value = [Mock()]

        # System has too many total jobs (50 jobs = 10x concurrent limit)
        mock_repository.get_jobs_by_status.side_effect = [
            [Mock() for _ in range(40)],  # 40 pending
            [Mock() for _ in range(10)],  # 10 running = 50 total (at capacity)
        ]

        can_submit, reason = queue_manager.can_submit_job("session1")

        assert can_submit is False
        assert "System at capacity" in reason

    def test_assign_queue_position_normal_priority(
        self, queue_manager, mock_repository
    ):
        """Test queue position assignment with normal priority."""
        # 3 existing pending jobs
        existing_jobs = [Mock(id=f"job{i}", queue_position=i+1, priority=0) for i in range(3)]
        mock_repository.get_jobs_by_status.return_value = existing_jobs

        position = queue_manager.assign_queue_position(
            "new-job", "session1", priority=0
        )

        # Should be assigned position 4 (end of queue)
        assert position == 4

        # Verify job was updated with position info
        mock_repository.update_job_by_id.assert_called_with(
            "new-job",
            {
                "queue_position": 4,
                "priority": 0,
                "session_id": "session1",
            },
        )

    def test_assign_queue_position_high_priority(self, queue_manager, mock_repository):
        """Test queue position assignment with high priority."""
        # 3 existing jobs with lower priority
        existing_jobs = [
            Mock(id="job1", priority=0, queue_position=2),
            Mock(id="job2", priority=0, queue_position=3),
            Mock(id="job3", priority=1, queue_position=1),
        ]
        mock_repository.get_jobs_by_status.return_value = existing_jobs

        position = queue_manager.assign_queue_position(
            "new-job", "session1", priority=5
        )

        # Should be assigned position 1 (front of queue due to high priority)
        assert position == 1

    def test_update_all_queue_positions(self, queue_manager, mock_repository):
        """Test updating all queue positions for fairness."""
        # Mock jobs with different priorities and creation times
        job1 = Mock(
            id="job1",
            priority=0,
            created_at=datetime.utcnow() - timedelta(minutes=10),
            queue_position=3,
        )
        job2 = Mock(
            id="job2",
            priority=5,
            created_at=datetime.utcnow() - timedelta(minutes=5),
            queue_position=1,
        )
        job3 = Mock(
            id="job3",
            priority=0,
            created_at=datetime.utcnow() - timedelta(minutes=15),
            queue_position=2,
        )

        mock_repository.get_jobs_by_status.return_value = [job1, job2, job3]

        updated_count = queue_manager.update_all_queue_positions()

        # Should update jobs that have incorrect positions
        assert updated_count >= 0  # Some positions may need updating

        # Verify repository calls for position updates
        assert mock_repository.update_job_by_id.call_count >= 0

    def test_get_next_job_to_process(self, queue_manager, mock_repository):
        """Test getting next job for worker processing."""
        # Mock pending jobs with different priorities
        job1 = Mock(
            id="job1",
            priority=0,
            queue_position=2,
            created_at=datetime.utcnow(),
            prompt="low priority",
        )
        job2 = Mock(
            id="job2",
            priority=5,
            queue_position=1,
            created_at=datetime.utcnow(),
            prompt="high priority",
        )
        job1.session_id = "session1"
        job2.session_id = "session2"

        mock_repository.get_jobs_by_status.return_value = [job1, job2]

        next_job = queue_manager.get_next_job_to_process()

        # Should return highest priority job
        assert next_job is not None
        assert next_job["job_id"] == "job2"  # High priority job
        assert next_job["session_id"] == "session2"
        assert next_job["priority"] == 5
        assert next_job["prompt"] == "high priority"

    def test_get_next_job_empty_queue(self, queue_manager, mock_repository):
        """Test getting next job when queue is empty."""
        mock_repository.get_jobs_by_status.return_value = []

        next_job = queue_manager.get_next_job_to_process()

        assert next_job is None

    def test_calculate_queue_position(self, queue_manager):
        """Test queue position calculation logic."""
        # Mock pending jobs
        job1 = Mock(
            id="job1", priority=0, created_at=datetime.utcnow() - timedelta(minutes=10)
        )
        job2 = Mock(
            id="job2", priority=5, created_at=datetime.utcnow() - timedelta(minutes=5)
        )
        job3 = Mock(
            id="job3", priority=0, created_at=datetime.utcnow() - timedelta(minutes=15)
        )
        pending_jobs = [job1, job2, job3]

        # Test position calculation for each job
        position1 = queue_manager._calculate_queue_position("job1", pending_jobs)
        position2 = queue_manager._calculate_queue_position("job2", pending_jobs)
        position3 = queue_manager._calculate_queue_position("job3", pending_jobs)

        # job2 should be first (highest priority), job3 second (oldest), job1 third
        assert position2 == 1  # Highest priority
        assert position3 == 2  # Oldest among same priority
        assert position1 == 3  # Newest among same priority

    def test_estimate_wait_time(self, queue_manager):
        """Test wait time estimation logic."""
        # Test various queue positions
        assert queue_manager._estimate_wait_time(1) == 0  # First in queue
        assert queue_manager._estimate_wait_time(2) == 1  # Second position
        assert queue_manager._estimate_wait_time(6) == 5  # (6-1) * 5 / 5 = 5 minutes

        # Test with zero position (should return 0)
        assert queue_manager._estimate_wait_time(0) == 0

        # Test minimum wait time
        assert queue_manager._estimate_wait_time(2) >= 1  # At least 1 minute

    def test_queue_manager_error_handling(self, queue_manager, mock_repository):
        """Test error handling in queue manager operations."""
        # Setup repository to raise exception
        mock_repository.get_jobs_by_status.side_effect = Exception("Database error")

        # Operations should handle errors gracefully
        status = queue_manager.get_queue_status("session1")
        assert "error" in status
        assert status["total_pending_jobs"] == 0

        can_submit, reason = queue_manager.can_submit_job("session1")
        assert can_submit is False
        assert "Error checking submission eligibility" in reason

        next_job = queue_manager.get_next_job_to_process()
        assert next_job is None


@pytest.mark.unit
@pytest.mark.integration
class TestQueueManagerIntegration:
    """Integration tests for QueueManager with realistic scenarios."""

    def test_multi_user_queue_fairness(self):
        """Test queue fairness across multiple users."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config_factory:
            with patch("src.api.job_repository.JobRepository") as mock_repo_class:
                # Setup configuration
                mock_config = Mock()
                mock_config.MAX_CONCURRENT_JOBS = 3
                mock_config.MAX_JOBS_PER_SESSION = 2
                mock_config.AVERAGE_PROCESSING_TIME_MINUTES = 3
                mock_config_factory.return_value = mock_config

                # Setup repository with multiple user jobs
                mock_repo = Mock()

                # User1: 2 jobs (at limit), User2: 1 job, User3: 1 job
                pending_jobs = [
                    Mock(
                        id="job1",
                        session_id="user1",
                        priority=0,
                        created_at=datetime.utcnow() - timedelta(minutes=10),
                    ),
                    Mock(
                        id="job2",
                        session_id="user1",
                        priority=0,
                        created_at=datetime.utcnow() - timedelta(minutes=8),
                    ),
                    Mock(
                        id="job3",
                        session_id="user2",
                        priority=0,
                        created_at=datetime.utcnow() - timedelta(minutes=5),
                    ),
                    Mock(
                        id="job4",
                        session_id="user3",
                        priority=0,
                        created_at=datetime.utcnow() - timedelta(minutes=2),
                    ),
                ]

                mock_repo.get_jobs_by_status.return_value = pending_jobs
                # Setup active jobs for specific sessions
                def get_active_jobs_side_effect(session_id):
                    if session_id == "user1":
                        return [pending_jobs[0], pending_jobs[1]]  # 2 jobs
                    elif session_id == "user2":
                        return [pending_jobs[2]]  # 1 job
                    elif session_id == "user3":
                        return [pending_jobs[3]]  # 1 job
                    else:
                        return []
                
                mock_repo.get_active_jobs_by_session.side_effect = get_active_jobs_side_effect
                mock_repo_class.return_value = mock_repo

                manager = QueueManager("testing")

                # Test that user1 cannot submit more jobs (at limit)
                can_submit, reason = manager.can_submit_job("user1")
                assert can_submit is False
                assert "Maximum 2 jobs per session exceeded" in reason

                # Test that user2 and user3 can submit more jobs
                can_submit, reason = manager.can_submit_job("user2")
                assert can_submit is True

                can_submit, reason = manager.can_submit_job("user3")
                assert can_submit is True

    def test_priority_queue_processing(self):
        """Test priority-based queue processing."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config_factory:
            with patch("src.api.job_repository.JobRepository") as mock_repo_class:
                # Setup
                mock_config = Mock()
                mock_config_factory.return_value = mock_config

                mock_repo = Mock()

                # Jobs with different priorities
                pending_jobs = [
                    Mock(
                        id="job1",
                        priority=0,
                        queue_position=3,
                        created_at=datetime.utcnow(),
                        session_id="user1",
                        prompt="normal",
                    ),
                    Mock(
                        id="job2",
                        priority=10,
                        queue_position=1,
                        created_at=datetime.utcnow(),
                        session_id="user2",
                        prompt="urgent",
                    ),
                    Mock(
                        id="job3",
                        priority=5,
                        queue_position=2,
                        created_at=datetime.utcnow(),
                        session_id="user3",
                        prompt="medium",
                    ),
                ]

                mock_repo.get_jobs_by_status.return_value = pending_jobs
                mock_repo_class.return_value = mock_repo

                manager = QueueManager("testing")

                # Next job should be highest priority
                next_job = manager.get_next_job_to_process()
                assert next_job["job_id"] == "job2"  # Highest priority
                assert next_job["priority"] == 10
                assert next_job["prompt"] == "urgent"
