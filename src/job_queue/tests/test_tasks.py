"""Tests for Celery video generation tasks."""

import uuid
from unittest.mock import Mock, patch

import pytest

from src.core.models import GenerationParams
from src.job_queue.tasks import (
    _should_retry_error,
    process_video_generation,
    update_queue_positions,
)


@pytest.fixture
def sample_generation_params():
    """Create sample generation parameters."""
    return {
        "prompt": "test video prompt",
        "ui_parameters": {
            "width": 1280,
            "height": 720,
            "duration": 5,
            "model": "sora-turbo",
        }
    }


@pytest.mark.integration
class TestVideoGenerationTask:
    """Test cases for video generation Celery task."""

    @pytest.fixture
    def mock_task_instance(self):
        """Create mock task instance for testing."""
        task = Mock()
        task.request.id = "test-worker-123"
        task.retry = Mock(side_effect=Exception("Retry called"))
        return task


    @pytest.fixture
    def sample_job_data(self):
        """Create sample job data."""
        return {
            "session_id": "test-session-456",
            "job_id": str(uuid.uuid4()),
            "retry_count": 0,
        }

    @patch("src.job_queue.tasks._update_job_status")
    @patch("src.job_queue.tasks._broadcast_job_completion")
    @patch("src.job_queue.tasks.SoraClient")
    def test_process_video_generation_success(
        self,
        mock_sora_client,
        mock_broadcast_completion,
        mock_update_status,
        mock_task_instance,
        sample_generation_params,
        sample_job_data,
    ):
        """Test successful video generation task execution."""
        # Setup mock SoraClient with proper VideoJob structure
        mock_client_instance = Mock()
        
        # Mock create_video_job to return a proper VideoJob-like object
        mock_initial_job = Mock()
        mock_initial_job.status = "pending"
        mock_initial_job.generation_id = "test-generation-123"
        mock_initial_job.error_message = None
        mock_client_instance.create_video_job.return_value = mock_initial_job
        
        # Mock poll_job_status to return successful completion
        mock_completed_job = Mock()
        mock_completed_job.status = "succeeded"
        mock_completed_job.file_path = "/tmp/test_video.mp4"
        mock_completed_job.download_url = "https://example.com/video.mp4"
        mock_completed_job.error_message = None
        mock_client_instance.poll_job_status.return_value = mock_completed_job
        
        mock_sora_client.return_value = mock_client_instance

        # Execute task (self, session_id, job_id, generation_params, retry_count)
        result = process_video_generation(
            sample_job_data["session_id"],
            sample_job_data["job_id"],
            sample_generation_params,
            sample_job_data["retry_count"],
        )

        # Verify success result
        assert result["success"] is True
        assert result["job_id"] == sample_job_data["job_id"]
        assert result["session_id"] == sample_job_data["session_id"]
        assert result["file_path"] == "/tmp/test_video.mp4"
        assert result["download_url"] == "https://example.com/video.mp4"
        assert "Video generation completed successfully" in result["message"]

        # Verify SoraClient was called correctly
        mock_client_instance.create_video_job.assert_called_once_with(
            prompt="test video prompt",
            ui_parameters={'width': 1280, 'height': 720, 'duration': 5, 'model': 'sora-turbo'}
        )
        
        # Verify polling was called
        mock_client_instance.poll_job_status.assert_called_once_with(
            sample_job_data["job_id"], "test-generation-123"
        )

        # Verify status updates (running + running with generation_id + succeeded)
        assert mock_update_status.call_count == 3
        
        # First call should set status to running
        first_call = mock_update_status.call_args_list[0]
        assert first_call[0][0] == sample_job_data["job_id"]
        assert first_call[0][1] == "running"
        assert "worker_id" in first_call[0][2]
        
        # Second call should update with generation_id
        second_call = mock_update_status.call_args_list[1]
        assert second_call[0][0] == sample_job_data["job_id"]
        assert second_call[0][1] == "running"
        assert "generation_id" in second_call[0][2]
        assert second_call[0][2]["generation_id"] == "test-generation-123"
        
        # Third call should set status to succeeded with completion data
        third_call = mock_update_status.call_args_list[2]
        assert third_call[0][0] == sample_job_data["job_id"]
        assert third_call[0][1] == "succeeded"
        completion_data = third_call[0][2]
        assert completion_data["file_path"] == "/tmp/test_video.mp4"
        assert completion_data["download_url"] == "https://example.com/video.mp4"

        # Verify completion broadcast
        mock_broadcast_completion.assert_called_once_with(
            sample_job_data["session_id"],
            sample_job_data["job_id"],
            completion_data
        )

    @patch("src.job_queue.tasks._update_job_status")
    @patch("src.job_queue.tasks._broadcast_job_failure")
    @patch("src.job_queue.tasks.SoraClient")
    def test_process_video_generation_failure_no_retry(
        self,
        mock_sora_client,
        mock_broadcast_failure,
        mock_update_status,
        mock_task_instance,
        sample_generation_params,
        sample_job_data,
    ):
        """Test video generation task failure without retry."""
        # Setup mock SoraClient to raise an exception during job creation
        mock_client_instance = Mock()
        mock_client_instance.create_video_job.side_effect = ValueError("Invalid prompt")
        mock_sora_client.return_value = mock_client_instance

        # Execute task
        result = process_video_generation(
            sample_job_data["session_id"],
            sample_job_data["job_id"],
            sample_generation_params,
            3,  # Max retries reached
        )

        # Verify failure result
        assert result["success"] is False
        assert result["job_id"] == sample_job_data["job_id"]
        assert result["error"] == "Invalid prompt"
        assert "Video generation failed" in result["message"]

        # Verify status updates (running + failed)
        assert mock_update_status.call_count == 2
        
        # First call should set status to running
        first_call = mock_update_status.call_args_list[0]
        assert first_call[0][0] == sample_job_data["job_id"]
        assert first_call[0][1] == "running"
        assert "worker_id" in first_call[0][2]
        
        # Second call should set status to failed with error data
        second_call = mock_update_status.call_args_list[1]
        assert second_call[0][0] == sample_job_data["job_id"]
        assert second_call[0][1] == "failed"
        error_data = second_call[0][2]
        assert error_data["error_message"] == "Invalid prompt"
        assert error_data["error_type"] == "ValueError"
        assert error_data["retry_count"] == 3

        # Verify failure broadcast
        mock_broadcast_failure.assert_called_once()

    @patch("src.job_queue.tasks._handle_job_failure")
    @patch("src.job_queue.tasks.SoraClient")
    def test_process_video_generation_retry_logic(
        self,
        mock_sora_client,
        mock_handle_failure,
        mock_task_instance,
        sample_generation_params,
        sample_job_data,
    ):
        """Test video generation task retry logic."""
        # Setup mock SoraClient to raise a non-auto-retryable exception
        mock_client_instance = Mock()
        mock_client_instance.create_video_job.side_effect = ValueError("Test error")
        mock_sora_client.return_value = mock_client_instance

        # Setup failure handler to raise the retry exception
        mock_handle_failure.side_effect = Exception("Retry called")

        # Execute task and expect retry exception
        with pytest.raises(Exception, match="Retry called"):
            process_video_generation(
                sample_job_data["session_id"],
                sample_job_data["job_id"],
                sample_generation_params,
                1,  # First retry
            )

        # Verify failure handler was called with correct parameters
        mock_handle_failure.assert_called_once()
        call_args = mock_handle_failure.call_args[0]
        assert call_args[1] == sample_job_data["session_id"]  # session_id
        assert call_args[2] == sample_job_data["job_id"]  # job_id
        assert isinstance(call_args[3], ValueError)  # exception
        assert call_args[4] == 1  # retry_count

    def test_generation_params_validation(self, sample_generation_params):
        """Test that GenerationParams validation works in task context."""
        # Valid parameters should work (flatten the fixture for GenerationParams)
        flat_params = {
            "prompt": sample_generation_params["prompt"],
            **sample_generation_params["ui_parameters"]
        }
        params = GenerationParams(**flat_params)
        assert params.prompt == "test video prompt"
        assert params.width == 1280
        assert params.height == 720
        assert params.duration == 5
        assert params.model == "sora-turbo"

        # Invalid parameters should raise validation error
        invalid_params = flat_params.copy()
        invalid_params["width"] = -100  # Invalid width

        with pytest.raises(ValueError):
            GenerationParams(**invalid_params)


@pytest.mark.integration
class TestQueuePositionUpdateTask:
    """Test cases for queue position update task."""

    @patch("src.job_queue.manager.QueueManager")
    def test_update_queue_positions_success(self, mock_queue_manager_class):
        """Test successful queue position update."""
        # Setup mock queue manager
        mock_manager = Mock()
        mock_manager.update_all_queue_positions.return_value = 5
        mock_queue_manager_class.return_value = mock_manager

        # Execute task
        result = update_queue_positions()

        # Verify success result
        assert result["success"] is True
        assert result["updated_jobs"] == 5
        assert "Queue positions updated successfully" in result["message"]

        # Verify queue manager was called
        mock_manager.update_all_queue_positions.assert_called_once()

    @patch("src.job_queue.manager.QueueManager")
    def test_update_queue_positions_failure(self, mock_queue_manager_class):
        """Test queue position update failure."""
        # Setup mock queue manager to raise exception
        mock_manager = Mock()
        mock_manager.update_all_queue_positions.side_effect = Exception(
            "Database error"
        )
        mock_queue_manager_class.return_value = mock_manager

        # Execute task
        result = update_queue_positions()

        # Verify failure result
        assert result["success"] is False
        assert result["error"] == "Database error"
        assert "Queue position update failed" in result["message"]


@pytest.mark.unit
class TestTaskHelperFunctions:
    """Test cases for task helper functions."""

    def test_should_retry_error_retryable_exceptions(self):
        """Test that retryable exceptions return True."""
        # These should be retried
        assert _should_retry_error(ConnectionError("Network error")) is True
        assert _should_retry_error(TimeoutError("Request timeout")) is True
        assert _should_retry_error(OSError("System error")) is True

    def test_should_retry_error_non_retryable_exceptions(self):
        """Test that non-retryable exceptions return False."""
        # These should not be retried
        assert _should_retry_error(ValueError("Invalid input")) is False
        assert _should_retry_error(TypeError("Type error")) is False
        assert _should_retry_error(RuntimeError("Runtime error")) is False

    @patch("src.api.job_repository.JobRepository")
    def test_update_job_status_success(self, mock_repository_class):
        """Test successful job status update."""
        from src.job_queue.tasks import _update_job_status

        # Setup mock repository
        mock_repository = Mock()
        mock_repository_class.return_value = mock_repository

        # Test update
        _update_job_status("test-job-123", "running", {"worker_id": "worker-456"})

        # Verify repository was called correctly
        mock_repository.update_job_by_id.assert_called_once_with(
            "test-job-123", {"status": "running", "worker_id": "worker-456"}
        )

    @patch("src.api.job_repository.JobRepository")
    def test_update_job_status_failure_handling(self, mock_repository_class):
        """Test job status update error handling."""
        from src.job_queue.tasks import _update_job_status

        # Setup mock repository to raise exception
        mock_repository = Mock()
        mock_repository.update_job_by_id.side_effect = Exception("Database error")
        mock_repository_class.return_value = mock_repository

        # Should not raise exception - errors are logged and handled
        _update_job_status("test-job-123", "failed", {"error": "test error"})

        # Verify repository was called
        mock_repository.update_job_by_id.assert_called_once()


@pytest.mark.unit
@pytest.mark.integration
class TestTaskIntegration:
    """Integration tests for task system components."""

    def test_task_data_flow(self, sample_generation_params):
        """Test that data flows correctly through task pipeline."""
        # Test generation params serialization/deserialization
        flat_params = {
            "prompt": sample_generation_params["prompt"],
            **sample_generation_params["ui_parameters"]
        }
        params_obj = GenerationParams(**flat_params)
        api_dict = params_obj.to_api_dict()

        # Verify API format matches expected Azure API structure
        assert api_dict["prompt"] == "test video prompt"
        assert api_dict["width"] == 1280
        assert api_dict["height"] == 720
        assert api_dict["n_seconds"] == 5  # Note: converts duration to n_seconds
        assert api_dict["model"] == "sora-turbo"

    def test_task_error_handling_pipeline(self):
        """Test error handling throughout task pipeline."""
        # Test various error scenarios
        error_scenarios = [
            (ConnectionError("Network issue"), True),  # Should retry
            (ValueError("Bad input"), False),  # Should not retry
            (TimeoutError("Timeout"), True),  # Should retry
            (RuntimeError("Runtime issue"), False),  # Should not retry
        ]

        for error, should_retry in error_scenarios:
            result = _should_retry_error(error)
            assert result == should_retry, (
                f"Error {type(error).__name__} retry logic incorrect"
            )
