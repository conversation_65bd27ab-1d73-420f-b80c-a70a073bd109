"""Tests for Celery application configuration."""

from unittest.mock import Mock, patch

import pytest

from src.job_queue.celery_app import create_celery_app


@pytest.mark.integration
class TestCeleryApp:
    """Test cases for Celery application configuration."""

    def test_create_celery_app_default_config(self):
        """Test creating Celery app with default configuration."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            # Mock configuration
            mock_base_config = Mock()
            mock_base_config.CELERY_BROKER_URL = "redis://localhost:6379/0"
            mock_base_config.CELERY_RESULT_BACKEND = "redis://localhost:6379/0"
            mock_config.return_value = mock_base_config

            # Create Celery app
            celery_app = create_celery_app()

            # Verify configuration
            assert celery_app.main == "sora_multiuser"
            assert celery_app.conf.broker_url == "redis://localhost:6379/0"
            assert celery_app.conf.result_backend == "redis://localhost:6379/0"
            assert celery_app.conf.task_serializer == "json"
            assert celery_app.conf.accept_content == ["json"]
            assert celery_app.conf.result_serializer == "json"
            assert celery_app.conf.timezone == "UTC"
            assert celery_app.conf.enable_utc is True
            assert celery_app.conf.worker_prefetch_multiplier == 1
            assert celery_app.conf.task_acks_late is True

    def test_create_celery_app_custom_config(self):
        """Test creating Celery app with custom configuration."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            # Mock custom configuration
            mock_base_config = Mock()
            mock_base_config.CELERY_BROKER_URL = "redis://custom-host:6380/1"
            mock_base_config.CELERY_RESULT_BACKEND = "redis://custom-host:6380/2"
            mock_config.return_value = mock_base_config

            # Create Celery app with custom config
            celery_app = create_celery_app("production")

            # Verify custom configuration is used
            assert celery_app.conf.broker_url == "redis://custom-host:6380/1"
            assert celery_app.conf.result_backend == "redis://custom-host:6380/2"

            # Verify factory was called with correct config name
            mock_config.assert_called_once_with("production")

    def test_create_celery_app_fallback_config(self):
        """Test Celery app creation with missing config attributes (uses defaults)."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            # Mock configuration without Celery-specific attributes
            mock_base_config = Mock()
            # Remove attributes so getattr returns defaults
            del mock_base_config.CELERY_BROKER_URL
            del mock_base_config.CELERY_RESULT_BACKEND
            mock_config.return_value = mock_base_config

            # Create Celery app
            celery_app = create_celery_app()

            # Verify fallback configuration
            assert celery_app.conf.broker_url == "redis://localhost:6379/0"
            assert celery_app.conf.result_backend == "redis://localhost:6379/0"

    def test_celery_task_routing_configuration(self):
        """Test that task routing is properly configured."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            mock_base_config = Mock()
            mock_config.return_value = mock_base_config

            celery_app = create_celery_app()

            # Check task routing configuration
            task_routes = celery_app.conf.task_routes
            assert "src.job_queue.tasks.process_video_generation" in task_routes
            assert (
                task_routes["src.job_queue.tasks.process_video_generation"]["queue"]
                == "video_generation"
            )

    def test_celery_worker_settings(self):
        """Test worker-specific settings are properly configured."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            mock_base_config = Mock()
            mock_config.return_value = mock_base_config

            celery_app = create_celery_app()

            # Verify worker settings for reliability and performance
            assert celery_app.conf.task_time_limit == 1800  # 30 minutes
            assert celery_app.conf.task_soft_time_limit == 1500  # 25 minutes
            assert celery_app.conf.worker_max_tasks_per_child == 50
            assert celery_app.conf.task_reject_on_worker_lost is True

    @pytest.mark.unit
    def test_celery_configuration_logging(self):
        """Test that configuration logging works properly."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            with patch("src.job_queue.celery_app.logging.getLogger") as mock_logger:
                mock_base_config = Mock()
                mock_base_config.CELERY_BROKER_URL = "redis://test:6379/0"
                mock_config.return_value = mock_base_config

                mock_logger_instance = Mock()
                mock_logger.return_value = mock_logger_instance

                # Create Celery app
                create_celery_app()

                # Verify logging calls
                assert mock_logger_instance.info.call_count >= 3  # Config logging calls

                # Check that broker URL is logged
                broker_logged = any(
                    "redis://test:6379/0" in str(call)
                    for call in mock_logger_instance.info.call_args_list
                )
                assert broker_logged
