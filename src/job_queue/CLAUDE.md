# Job Queue Module Documentation

## Overview

Production-ready background job processing system using Celery with Redis backend. Handles video generation tasks, queue management, and worker coordination for multi-user video generation workflows.

## Module Structure

```
src/job_queue/
├── celery_app.py        # Celery application configuration
├── tasks.py             # Video generation tasks and workflow
├── manager.py           # Queue management and coordination
├── worker.py            # Worker process management
└── tests/               # Co-located queue tests
    ├── test_celery_app.py
    ├── test_tasks.py
    ├── test_manager.py
    └── test_worker.py
```

## Celery Configuration

### Application Setup
```python
# src/job_queue/celery_app.py
from celery import Celery
from src.config.environments import get_environment_config

def create_celery_app() -> Celery:
    """Create and configure Celery application."""
    config = get_environment_config()
    
    app = Celery('sora_poc')
    
    # Redis configuration
    app.conf.update(
        broker_url=config.celery_broker_url,  # redis://localhost:6379/0
        result_backend=config.celery_result_backend,
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        
        # Task routing
        task_routes={
            'src.job_queue.tasks.process_video_generation': {'queue': 'video_generation'},
            'src.job_queue.tasks.cleanup_old_files': {'queue': 'maintenance'},
        },
        
        # Worker configuration
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        worker_max_tasks_per_child=1000,
        
        # Result backend settings
        result_expires=3600,  # 1 hour
        result_backend_transport_options={
            'master_name': 'mymaster'
        }
    )
    
    return app

# Global Celery instance
celery_app = create_celery_app()
```

### Task Configuration
```python
# Task execution settings
CELERY_TASK_CONFIG = {
    'max_retries': 3,
    'default_retry_delay': 60,  # seconds
    'retry_backoff': True,
    'retry_backoff_max': 300,   # 5 minutes
    'retry_jitter': True,
    'task_time_limit': 1800,    # 30 minutes
    'task_soft_time_limit': 1500, # 25 minutes
}
```

## Task Implementation

### Video Generation Task
```python
# src/job_queue/tasks.py
from celery import current_task
from src.job_queue.celery_app import celery_app
from src.features.sora_integration.client import SoraClient
from src.api.job_repository import JobRepository

@celery_app.task(bind=True, **CELERY_TASK_CONFIG)
def process_video_generation(self, session_id: str, job_id: str, generation_params: dict) -> dict:
    """
    Process video generation with complete workflow.
    
    Args:
        session_id: User session identifier
        job_id: Unique job identifier
        generation_params: Video generation parameters
        
    Returns:
        dict: Job completion data with status and file paths
        
    Raises:
        Exception: Various exceptions for different failure modes
    """
    try:
        # Step 1: Initialize components
        sora_client = SoraClient.from_env()
        job_repository = JobRepository()
        
        # Step 2: Update job status to running
        _update_job_status(job_repository, job_id, "running")
        
        # Step 3: Submit job to Azure API
        initial_job = sora_client.create_video_job(
            prompt=generation_params['prompt'],
            ui_parameters=generation_params.get('ui_parameters', {})
        )
        generation_id = initial_job.generation_id
        
        # Step 4: Update with generation ID
        _update_job_status(job_repository, job_id, "running", {
            "generation_id": generation_id,
            "azure_job_id": initial_job.job_id
        })
        
        # Step 5: Poll for completion with exponential backoff
        result = _poll_for_completion(sora_client, job_id, generation_id)
        
        # Step 6: Handle completion
        return _handle_job_completion(job_repository, job_id, result)
        
    except Exception as exc:
        return _handle_job_failure(job_repository, job_id, exc, self.retry)

def _poll_for_completion(sora_client: SoraClient, job_id: str, generation_id: str) -> dict:
    """Poll Azure API until job completion with exponential backoff."""
    poll_interval = 5.0  # Start with 5 seconds
    max_poll_interval = 30.0
    max_poll_time = 600  # 10 minutes total
    start_time = time.time()
    
    while time.time() - start_time < max_poll_time:
        current_job = sora_client.poll_job_status(job_id, generation_id)
        
        if current_job.status == "succeeded":
            return {
                "status": "succeeded",
                "file_path": current_job.file_path,
                "download_url": current_job.download_url,
                "completed_at": datetime.utcnow(),
            }
        elif current_job.status == "failed":
            raise Exception(f"Azure API job failed: {current_job.error_message}")
        
        # Exponential backoff: 5s -> 7.5s -> 11.25s -> ... max 30s
        time.sleep(poll_interval)
        poll_interval = min(poll_interval * 1.5, max_poll_interval)
    
    raise TimeoutError(f"Job {job_id} polling timed out after {max_poll_time} seconds")
```

### Task Helper Functions
```python
def _update_job_status(job_repository: JobRepository, job_id: str, status: str, data: dict = None) -> None:
    """Update job status in database."""
    update_data = {"status": status}
    if data:
        update_data.update(data)
    
    job_repository.update_job_by_id(job_id, update_data)

def _handle_job_completion(job_repository: JobRepository, job_id: str, result: dict) -> dict:
    """Handle successful job completion."""
    _update_job_status(job_repository, job_id, "succeeded", result)
    return {
        "status": "completed",
        "job_id": job_id,
        "file_path": result.get("file_path"),
        "completed_at": result.get("completed_at")
    }

def _handle_job_failure(job_repository: JobRepository, job_id: str, exc: Exception, retry_func) -> dict:
    """Handle job failure with retry logic."""
    error_message = str(exc)
    
    # Determine if error is retryable
    if isinstance(exc, (ConnectionError, TimeoutError)):
        # Retry for transient errors
        try:
            retry_func(countdown=60, max_retries=3)
        except Exception:
            # Max retries exceeded
            _update_job_status(job_repository, job_id, "failed", {
                "error_message": f"Failed after retries: {error_message}",
                "failed_at": datetime.utcnow()
            })
    else:
        # Don't retry for permanent errors
        _update_job_status(job_repository, job_id, "failed", {
            "error_message": error_message,
            "failed_at": datetime.utcnow()
        })
    
    return {
        "status": "failed",
        "job_id": job_id,
        "error": error_message
    }
```

## Queue Management

### QueueManager Class
```python
# src/job_queue/manager.py
from typing import Dict, List, Optional
from celery.result import AsyncResult
from src.job_queue.celery_app import celery_app

class QueueManager:
    """Manages job queue operations and statistics."""
    
    def __init__(self):
        self.celery_app = celery_app
    
    def submit_job(self, session_id: str, job_id: str, generation_params: dict) -> str:
        """Submit video generation job to queue."""
        task = celery_app.send_task(
            'src.job_queue.tasks.process_video_generation',
            args=[session_id, job_id, generation_params],
            queue='video_generation',
            routing_key='video_generation'
        )
        return task.id
    
    def get_job_status(self, task_id: str) -> dict:
        """Get Celery task status."""
        result = AsyncResult(task_id, app=self.celery_app)
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "traceback": result.traceback,
            "started_at": getattr(result, 'date_started', None),
            "completed_at": getattr(result, 'date_done', None)
        }
    
    def get_queue_stats(self) -> dict:
        """Get queue statistics."""
        inspect = self.celery_app.control.inspect()
        
        # Get active tasks
        active_tasks = inspect.active()
        active_count = sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0
        
        # Get reserved tasks
        reserved_tasks = inspect.reserved()
        reserved_count = sum(len(tasks) for tasks in reserved_tasks.values()) if reserved_tasks else 0
        
        # Get scheduled tasks
        scheduled_tasks = inspect.scheduled()
        scheduled_count = sum(len(tasks) for tasks in scheduled_tasks.values()) if scheduled_tasks else 0
        
        return {
            "active_jobs": active_count,
            "pending_jobs": reserved_count + scheduled_count,
            "total_jobs": active_count + reserved_count + scheduled_count,
            "workers_online": len(active_tasks) if active_tasks else 0
        }
    
    def get_session_queue_status(self, session_id: str) -> dict:
        """Get queue status for specific session."""
        # Query database for session jobs
        from src.api.job_repository import JobRepository
        job_repository = JobRepository()
        
        session_jobs = job_repository.get_jobs_by_session(session_id)
        active_jobs = [job for job in session_jobs if job.status in ["pending", "running"]]
        
        return {
            "session_id": session_id,
            "active_jobs": len(active_jobs),
            "can_submit_more": len(active_jobs) < 3,  # MAX_CONCURRENT_JOBS_PER_SESSION
            "queue_position": self._calculate_queue_position(active_jobs),
            "estimated_wait_minutes": self._estimate_wait_time(len(active_jobs))
        }
    
    def _calculate_queue_position(self, active_jobs: List) -> int:
        """Calculate position in queue for session."""
        # Simple queue position calculation
        return len([job for job in active_jobs if job.status == "pending"])
    
    def _estimate_wait_time(self, active_jobs_count: int) -> int:
        """Estimate wait time based on queue depth."""
        # Simple estimation: 2 minutes per job
        return active_jobs_count * 2
```

## Worker Management

### Worker Configuration
```python
# src/job_queue/worker.py
import signal
import sys
from celery.signals import worker_ready, worker_shutting_down
from src.job_queue.celery_app import celery_app

@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Handle worker ready signal."""
    print(f"Worker {sender.hostname} is ready to process tasks")

@worker_shutting_down.connect
def worker_shutting_down_handler(sender=None, **kwargs):
    """Handle worker shutdown signal."""
    print(f"Worker {sender.hostname} is shutting down")

def graceful_shutdown(signum, frame):
    """Handle graceful shutdown signals."""
    print("Received shutdown signal, stopping worker gracefully...")
    celery_app.control.shutdown()
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, graceful_shutdown)
signal.signal(signal.SIGTERM, graceful_shutdown)

if __name__ == "__main__":
    # Start worker with configuration
    celery_app.worker_main([
        'worker',
        '--loglevel=info',
        '--concurrency=4',
        '--queues=video_generation,maintenance',
        '--hostname=worker@%h'
    ])
```

## Multi-Service Startup

### Development Setup
```bash
# Terminal 1: Start Redis server (required for queue and rate limiting)
redis-server
# Or with Docker: docker run -p 6379:6379 redis:alpine

# Terminal 2: Start Celery worker (required for background processing)
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4

# Terminal 3: Start Flask application (with WebSocket support)
uv run python src/main.py

# Optional: Start Celery monitoring (Flower)
uv run celery -A src.job_queue.celery_app flower
# Web UI available at http://localhost:5555
```

### Production Setup
```bash
# Use process manager (systemd, supervisor, etc.)
# Example supervisor configuration:
[program:celery_worker]
command=uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4
directory=/path/to/sora-poc
user=celery
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/celery/worker.log

[program:celery_beat]
command=uv run celery -A src.job_queue.celery_app beat --loglevel=info
directory=/path/to/sora-poc
user=celery
autostart=true
autorestart=true
```

## Configuration

### Environment Variables
```bash
# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Queue Management
MAX_CONCURRENT_JOBS_PER_SESSION=3
QUEUE_PRIORITY_ENABLED=true

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Worker Settings
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_TASK_TIME_LIMIT=1800
```

### Task Routing Configuration
```python
CELERY_TASK_ROUTES = {
    'src.job_queue.tasks.process_video_generation': {
        'queue': 'video_generation',
        'routing_key': 'video_generation'
    },
    'src.job_queue.tasks.cleanup_old_files': {
        'queue': 'maintenance',
        'routing_key': 'maintenance'
    },
    'src.job_queue.tasks.health_check': {
        'queue': 'monitoring',
        'routing_key': 'monitoring'
    }
}
```

## Monitoring and Health Checks

### Queue Health Monitoring
```python
def check_queue_health() -> dict:
    """Check queue system health."""
    try:
        inspect = celery_app.control.inspect()
        
        # Check if workers are responding
        stats = inspect.stats()
        if not stats:
            return {"status": "unhealthy", "reason": "No workers responding"}
        
        # Check Redis connection
        celery_app.broker_connection().ensure_connection(max_retries=3)
        
        # Check queue depth
        queue_stats = QueueManager().get_queue_stats()
        if queue_stats["pending_jobs"] > 100:  # Threshold
            return {"status": "degraded", "reason": "High queue depth"}
        
        return {
            "status": "healthy",
            "workers": len(stats),
            "active_jobs": queue_stats["active_jobs"],
            "pending_jobs": queue_stats["pending_jobs"]
        }
    except Exception as e:
        return {"status": "unhealthy", "reason": str(e)}
```

### Task Progress Tracking
```python
@celery_app.task(bind=True)
def long_running_task(self, data):
    """Example task with progress tracking."""
    total_steps = 100
    
    for i in range(total_steps):
        # Do work
        process_step(data, i)
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'current': i + 1, 'total': total_steps}
        )
        
        time.sleep(0.1)
    
    return {'status': 'completed', 'result': 'Task finished'}
```

## Testing Patterns

### Task Testing
```python
import pytest
from unittest.mock import Mock, patch
from src.job_queue.tasks import process_video_generation

class TestVideoGenerationTasks:
    """Test video generation tasks."""
    
    @patch('src.job_queue.tasks.SoraClient')
    @patch('src.job_queue.tasks.JobRepository')
    def test_process_video_generation_success(self, mock_repo, mock_client):
        """Test successful video generation task."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client.from_env.return_value = mock_client_instance
        
        mock_job = Mock()
        mock_job.generation_id = "gen-123"
        mock_job.job_id = "job-456"
        mock_client_instance.create_video_job.return_value = mock_job
        
        completed_job = Mock()
        completed_job.status = "succeeded"
        completed_job.file_path = "/path/to/video.mp4"
        completed_job.download_url = "https://example.com/video.mp4"
        mock_client_instance.poll_job_status.return_value = completed_job
        
        # Execute task
        result = process_video_generation(
            "session-123",
            "job-789",
            {"prompt": "Test prompt"}
        )
        
        # Verify result
        assert result["status"] == "completed"
        assert result["job_id"] == "job-789"
        assert result["file_path"] == "/path/to/video.mp4"
```

### Queue Manager Testing
```python
class TestQueueManager:
    """Test queue management functionality."""
    
    @pytest.fixture
    def queue_manager(self):
        """Create queue manager for testing."""
        return QueueManager()
    
    def test_submit_job(self, queue_manager):
        """Test job submission to queue."""
        with patch.object(queue_manager.celery_app, 'send_task') as mock_send:
            mock_task = Mock()
            mock_task.id = "task-123"
            mock_send.return_value = mock_task
            
            task_id = queue_manager.submit_job(
                "session-123",
                "job-456",
                {"prompt": "Test"}
            )
            
            assert task_id == "task-123"
            mock_send.assert_called_once()
```

## Performance Optimization

### Task Batching
```python
@celery_app.task
def batch_process_videos(job_data_list: List[dict]):
    """Process multiple video jobs in batch for efficiency."""
    results = []
    
    # Initialize shared resources once
    sora_client = SoraClient.from_env()
    job_repository = JobRepository()
    
    for job_data in job_data_list:
        try:
            result = _process_single_video(sora_client, job_repository, job_data)
            results.append(result)
        except Exception as e:
            results.append({"error": str(e), "job_id": job_data.get("job_id")})
    
    return results
```

### Connection Pooling
```python
# Redis connection pooling for Celery
import redis
from celery import Celery

redis_pool = redis.ConnectionPool(
    host='localhost',
    port=6379,
    db=0,
    max_connections=20,
    retry_on_timeout=True
)

celery_app = Celery('sora_poc')
celery_app.conf.broker_connection_retry_on_startup = True
celery_app.conf.broker_pool_limit = 10
```

## Best Practices

1. **Task Idempotency**: Ensure tasks can be safely retried
2. **Error Handling**: Implement comprehensive error handling with appropriate retry logic
3. **Resource Management**: Use connection pooling and resource cleanup
4. **Monitoring**: Track task progress and queue health
5. **Graceful Shutdown**: Handle shutdown signals properly
6. **Testing**: Mock external dependencies and test task logic
7. **Configuration**: Use environment-based configuration
8. **Logging**: Log task execution with request tracking
9. **Security**: Validate all task inputs and sanitize outputs
10. **Performance**: Use batching and connection pooling for optimization

## Troubleshooting

### Common Issues

**Worker Not Starting**
```bash
# Check Redis connectivity
redis-cli ping

# Check Celery configuration
uv run celery -A src.job_queue.celery_app inspect stats

# Check worker logs
tail -f /var/log/celery/worker.log
```

**Tasks Failing**
```bash
# Check task status
uv run celery -A src.job_queue.celery_app inspect active

# Monitor task execution
uv run celery -A src.job_queue.celery_app events

# Check failed tasks
uv run python -c "
from src.job_queue.celery_app import celery_app
from celery.result import AsyncResult
result = AsyncResult('task-id', app=celery_app)
print(f'Status: {result.status}')
print(f'Result: {result.result}')
print(f'Traceback: {result.traceback}')
"
```

**High Queue Depth**
```bash
# Check queue length
uv run celery -A src.job_queue.celery_app inspect reserved

# Purge queue (development only)
uv run celery -A src.job_queue.celery_app purge

# Scale workers
uv run celery -A src.job_queue.celery_app worker --concurrency=8
```

### Development Commands
```bash
# Start development worker
uv run celery -A src.job_queue.celery_app worker --loglevel=debug --concurrency=1

# Monitor with Flower
uv run celery -A src.job_queue.celery_app flower --port=5555

# Test task execution
uv run python -c "
from src.job_queue.tasks import process_video_generation
result = process_video_generation.delay('session-123', 'job-456', {'prompt': 'Test'})
print(f'Task ID: {result.id}')
"
```