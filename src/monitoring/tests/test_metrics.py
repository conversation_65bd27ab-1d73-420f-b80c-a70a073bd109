"""Tests for metrics collection functionality."""

import threading
from collections import defaultdict, deque
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from src.monitoring.metrics import MetricsCollector


@pytest.mark.unit
class TestMetricsCollector:
    """Test MetricsCollector functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.metrics = MetricsCollector(max_samples=100)

    def test_metrics_collector_initialization(self):
        """Test MetricsCollector initialization."""
        # Test default initialization
        metrics = MetricsCollector()
        assert metrics.max_samples == 1000
        assert hasattr(metrics, "logger")
        assert hasattr(metrics, "job_repository")
        assert hasattr(metrics, "_lock")

        # Test custom max_samples
        metrics = MetricsCollector(max_samples=500)
        assert metrics.max_samples == 500

        # Check internal storage structures
        assert isinstance(metrics._job_completion_times, deque)
        assert isinstance(metrics._api_response_times, deque)
        assert isinstance(metrics._error_counts, defaultdict)
        assert isinstance(metrics._job_status_changes, deque)

    def test_record_job_completion(self):
        """Test recording job completion metrics."""
        job_id = "test-job-123"
        duration = 45.5
        status = "succeeded"

        # Mock logger to verify logging
        with patch.object(self.metrics, "logger") as mock_logger:
            self.metrics.record_job_completion(job_id, duration, status)

            # Verify logging was called
            mock_logger.info.assert_called_once()
            log_call = mock_logger.info.call_args[0][0]
            assert job_id in log_call
            assert status in log_call
            assert str(duration) in log_call

        # Check that data was stored
        assert len(self.metrics._job_completion_times) == 1
        assert len(self.metrics._job_status_changes) == 1

        # Check job completion data
        completion_data = list(self.metrics._job_completion_times)[0]
        assert completion_data["job_id"] == job_id
        assert completion_data["duration"] == duration
        assert completion_data["status"] == status
        assert isinstance(completion_data["timestamp"], datetime)

        # Check status change data
        status_data = list(self.metrics._job_status_changes)[0]
        assert status_data["job_id"] == job_id
        assert status_data["status"] == status
        assert isinstance(status_data["timestamp"], datetime)

    def test_record_job_completion_multiple_jobs(self):
        """Test recording multiple job completions."""
        jobs = [
            ("job-1", 30.0, "succeeded"),
            ("job-2", 25.5, "succeeded"),
            ("job-3", 60.0, "failed"),
            ("job-4", 15.2, "succeeded"),
        ]

        for job_id, duration, status in jobs:
            self.metrics.record_job_completion(job_id, duration, status)

        assert len(self.metrics._job_completion_times) == 4
        assert len(self.metrics._job_status_changes) == 4

        # Verify data is stored in correct order
        completion_list = list(self.metrics._job_completion_times)
        for i, (job_id, duration, status) in enumerate(jobs):
            assert completion_list[i]["job_id"] == job_id
            assert completion_list[i]["duration"] == duration
            assert completion_list[i]["status"] == status

    def test_record_api_response_time(self):
        """Test recording API response time metrics."""
        endpoint = "/api/generate"
        response_time = 250.5
        status_code = 200

        # Mock logger to verify logging
        with patch.object(self.metrics, "logger") as mock_logger:
            self.metrics.record_api_response_time(endpoint, response_time, status_code)

            # Verify debug logging was called
            mock_logger.debug.assert_called_once()
            log_call = mock_logger.debug.call_args[0][0]
            assert endpoint in log_call
            assert str(status_code) in log_call
            assert str(response_time) in log_call

        # Check that data was stored
        assert len(self.metrics._api_response_times) == 1

        response_data = list(self.metrics._api_response_times)[0]
        assert response_data["endpoint"] == endpoint
        assert response_data["response_time"] == response_time
        assert response_data["status_code"] == status_code
        assert isinstance(response_data["timestamp"], datetime)

    def test_record_api_response_time_multiple_endpoints(self):
        """Test recording multiple API response times."""
        responses = [
            ("/api/generate", 200.0, 200),
            ("/api/status/job-1", 50.0, 200),
            ("/api/generate", 300.0, 400),
            ("/health", 25.0, 200),
            ("/metrics", 75.0, 200),
        ]

        for endpoint, response_time, status_code in responses:
            self.metrics.record_api_response_time(endpoint, response_time, status_code)

        assert len(self.metrics._api_response_times) == 5

        # Verify all data is stored correctly
        response_list = list(self.metrics._api_response_times)
        for i, (endpoint, response_time, status_code) in enumerate(responses):
            assert response_list[i]["endpoint"] == endpoint
            assert response_list[i]["response_time"] == response_time
            assert response_list[i]["status_code"] == status_code

    def test_record_error(self):
        """Test recording error metrics."""
        error_type = "ValidationError"
        error_message = "Invalid prompt format"
        context = {"prompt_length": 600, "user_id": "test-user"}

        # Mock logger to verify logging
        with patch.object(self.metrics, "logger") as mock_logger:
            self.metrics.record_error(error_type, error_message, context)

            # Verify error logging was called
            mock_logger.error.assert_called_once()
            log_call = mock_logger.error.call_args[0][0]
            assert error_type in log_call
            assert error_message in log_call

            # Check extra context was passed
            extra = mock_logger.error.call_args[1].get("extra", {})
            assert extra.get("context") == context

        # Check that error count was incremented
        assert self.metrics._error_counts[error_type] == 1

    def test_record_error_multiple_same_type(self):
        """Test recording multiple errors of the same type."""
        error_type = "ConnectionError"

        for i in range(5):
            self.metrics.record_error(error_type, f"Connection failed #{i}")

        assert self.metrics._error_counts[error_type] == 5

    def test_record_error_multiple_different_types(self):
        """Test recording errors of different types."""
        errors = [
            ("ValidationError", "Invalid input"),
            ("ConnectionError", "Connection timeout"),
            ("ValidationError", "Missing field"),
            ("AuthenticationError", "Invalid API key"),
            ("ValidationError", "Too long"),
        ]

        for error_type, error_message in errors:
            self.metrics.record_error(error_type, error_message)

        assert self.metrics._error_counts["ValidationError"] == 3
        assert self.metrics._error_counts["ConnectionError"] == 1
        assert self.metrics._error_counts["AuthenticationError"] == 1

    def test_record_error_no_context(self):
        """Test recording error without context."""
        error_type = "GeneralError"
        error_message = "Something went wrong"

        with patch.object(self.metrics, "logger") as mock_logger:
            self.metrics.record_error(error_type, error_message)

            # Should still log without context
            mock_logger.error.assert_called_once()
            extra = mock_logger.error.call_args[1].get("extra", {})
            assert extra.get("context") is None

    def test_get_job_completion_rate_no_data(self):
        """Test job completion rate with no data."""
        result = self.metrics.get_job_completion_rate(hours=24)

        assert result["period_hours"] == 24
        assert result["total_jobs"] == 0
        assert result["success_rate"] == 0.0
        assert result["average_duration_seconds"] == 0.0
        assert result["error_rate"] == 0.0

    def test_get_job_completion_rate_with_data(self):
        """Test job completion rate with sample data."""
        # Add recent completions (within 24 hours)
        recent_jobs = [
            ("job-1", 30.0, "succeeded"),
            ("job-2", 25.5, "succeeded"),
            ("job-3", 60.0, "failed"),
            ("job-4", 15.2, "succeeded"),
            ("job-5", 45.0, "succeeded"),
        ]

        for job_id, duration, status in recent_jobs:
            self.metrics.record_job_completion(job_id, duration, status)

        result = self.metrics.get_job_completion_rate(hours=24)

        assert result["period_hours"] == 24
        assert result["total_jobs"] == 5
        assert result["successful_jobs"] == 4
        assert result["failed_jobs"] == 1
        assert result["success_rate"] == 80.0  # 4/5 * 100
        assert result["error_rate"] == 20.0  # 1/5 * 100

        # Average duration for successful jobs: (30 + 25.5 + 15.2 + 45) / 4 = 28.925
        expected_avg = (30.0 + 25.5 + 15.2 + 45.0) / 4
        assert result["average_duration_seconds"] == round(expected_avg, 2)

    def test_get_job_completion_rate_time_filtering(self):
        """Test job completion rate time filtering."""
        # Mock timestamps for testing time filtering
        now = datetime.now()
        old_time = now - timedelta(hours=25)  # Outside 24-hour window
        recent_time = now - timedelta(hours=1)  # Within 24-hour window

        # Manually add data with specific timestamps
        with self.metrics._lock:
            # Old completion (should be filtered out)
            self.metrics._job_completion_times.append(
                {
                    "job_id": "old-job",
                    "duration": 30.0,
                    "status": "succeeded",
                    "timestamp": old_time,
                }
            )

            # Recent completion (should be included)
            self.metrics._job_completion_times.append(
                {
                    "job_id": "recent-job",
                    "duration": 20.0,
                    "status": "succeeded",
                    "timestamp": recent_time,
                }
            )

        result = self.metrics.get_job_completion_rate(hours=24)

        # Should only include the recent job
        assert result["total_jobs"] == 1
        assert result["successful_jobs"] == 1
        assert result["success_rate"] == 100.0

    def test_get_api_performance_metrics_no_data(self):
        """Test API performance metrics with no data."""
        result = self.metrics.get_api_performance_metrics(hours=24)

        assert result["period_hours"] == 24
        assert result["total_requests"] == 0
        assert result["average_response_time_ms"] == 0.0
        assert result["success_rate"] == 0.0

    def test_get_api_performance_metrics_with_data(self):
        """Test API performance metrics with sample data."""
        responses = [
            ("/api/generate", 200.0, 200),
            ("/api/generate", 250.0, 200),
            ("/api/status", 50.0, 200),
            ("/api/generate", 300.0, 400),  # Error response
            ("/health", 25.0, 200),
        ]

        for endpoint, response_time, status_code in responses:
            self.metrics.record_api_response_time(endpoint, response_time, status_code)

        result = self.metrics.get_api_performance_metrics(hours=24)

        assert result["period_hours"] == 24
        assert result["total_requests"] == 5
        assert result["successful_requests"] == 4  # Status codes 200
        assert result["success_rate"] == 80.0  # 4/5 * 100

        # Average response time: (200 + 250 + 50 + 300 + 25) / 5 = 165
        expected_avg = (200.0 + 250.0 + 50.0 + 300.0 + 25.0) / 5
        assert result["average_response_time_ms"] == round(expected_avg, 2)

        # Check endpoint breakdown
        endpoint_breakdown = result["endpoint_breakdown"]
        assert "/api/generate" in endpoint_breakdown
        assert "/api/status" in endpoint_breakdown
        assert "/health" in endpoint_breakdown

        # Check specific endpoint stats
        generate_stats = endpoint_breakdown["/api/generate"]
        assert generate_stats["request_count"] == 3
        # Average for /api/generate: (200 + 250 + 300) / 3 = 250
        assert generate_stats["average_response_time_ms"] == 250.0
        assert generate_stats["min_response_time_ms"] == 200.0
        assert generate_stats["max_response_time_ms"] == 300.0

    def test_get_error_statistics_no_errors(self):
        """Test error statistics with no errors."""
        result = self.metrics.get_error_statistics(hours=24)

        assert result["period_hours"] == 24
        assert result["total_errors"] == 0
        assert result["error_types"] == {}
        assert result["most_common_errors"] == []
        assert result["error_rate_per_hour"] == 0.0

    def test_get_error_statistics_with_errors(self):
        """Test error statistics with sample errors."""
        errors = [
            ("ValidationError", "Invalid input 1"),
            ("ValidationError", "Invalid input 2"),
            ("ValidationError", "Invalid input 3"),
            ("ConnectionError", "Connection timeout"),
            ("AuthError", "Auth failed"),
            ("ValidationError", "Invalid input 4"),
        ]

        for error_type, error_message in errors:
            self.metrics.record_error(error_type, error_message)

        result = self.metrics.get_error_statistics(hours=24)

        assert result["period_hours"] == 24
        assert result["total_errors"] == 6
        assert result["error_types"]["ValidationError"] == 4
        assert result["error_types"]["ConnectionError"] == 1
        assert result["error_types"]["AuthError"] == 1

        # Check most common errors (sorted by count, descending)
        most_common = result["most_common_errors"]
        assert len(most_common) == 3  # All 3 error types
        assert most_common[0] == ("ValidationError", 4)  # Most common
        assert most_common[1][1] == 1  # ConnectionError or AuthError
        assert most_common[2][1] == 1  # ConnectionError or AuthError

        # Error rate per hour: 6 errors / 24 hours = 0.25
        assert result["error_rate_per_hour"] == 0.25

    def test_get_error_statistics_top_five_limit(self):
        """Test error statistics limits to top 5 error types."""
        # Create 10 different error types
        for i in range(10):
            error_type = f"ErrorType{i}"
            # Give each type a different number of occurrences
            for j in range(i + 1):
                self.metrics.record_error(error_type, f"Error message {j}")

        result = self.metrics.get_error_statistics(hours=24)

        # Should only return top 5 most common
        assert len(result["most_common_errors"]) == 5

        # Verify they're sorted by count (descending)
        counts = [count for _, count in result["most_common_errors"]]
        assert counts == sorted(counts, reverse=True)

    @patch("src.monitoring.metrics.JobRepository")
    def test_get_database_metrics_success(self, mock_job_repo_class):
        """Test successful database metrics collection."""
        # Mock job repository
        mock_repo = MagicMock()
        mock_repo.get_job_stats.return_value = {
            "total": 100,
            "pending": 5,
            "running": 3,
            "succeeded": 85,
            "failed": 7,
        }

        # Mock recent jobs
        mock_jobs = []
        now = datetime.now()
        for _i in range(5):
            job = Mock()
            job.created_at = now - timedelta(minutes=30)  # 30 minutes ago
            mock_jobs.append(job)

        # Add some older jobs
        for _i in range(3):
            job = Mock()
            job.created_at = now - timedelta(hours=2)  # 2 hours ago
            mock_jobs.append(job)

        mock_repo.get_recent_jobs.return_value = mock_jobs
        mock_job_repo_class.return_value = mock_repo

        # Create fresh metrics instance to use mocked repo
        metrics = MetricsCollector()
        metrics.job_repository = mock_repo

        result = metrics.get_database_metrics()

        assert "job_statistics" in result
        assert result["job_statistics"]["total"] == 100

        assert "recent_activity" in result
        assert result["recent_activity"]["jobs_last_hour"] == 5  # Only recent jobs
        assert result["recent_activity"]["total_recent_jobs"] == 8  # All jobs

        assert "queue_health" in result
        queue_health = result["queue_health"]
        assert queue_health["pending_jobs"] == 5
        assert queue_health["running_jobs"] == 3
        assert queue_health["completed_jobs"] == 85
        assert queue_health["failed_jobs"] == 7

    @patch("src.monitoring.metrics.JobRepository")
    def test_get_database_metrics_error(self, mock_job_repo_class):
        """Test database metrics with repository error."""
        # Mock repository to raise exception
        mock_repo = MagicMock()
        mock_repo.get_job_stats.side_effect = Exception("Database connection failed")
        mock_job_repo_class.return_value = mock_repo

        metrics = MetricsCollector()
        metrics.job_repository = mock_repo

        with patch.object(metrics, "logger") as mock_logger:
            result = metrics.get_database_metrics()

            # Should log the error
            mock_logger.error.assert_called_once()

        assert "error" in result
        assert "Database connection failed" in result["error"]
        assert "timestamp" in result

    def test_get_comprehensive_metrics(self):
        """Test comprehensive metrics report."""
        # Add some sample data
        self.metrics.record_job_completion("job-1", 30.0, "succeeded")
        self.metrics.record_api_response_time("/api/test", 100.0, 200)
        self.metrics.record_error("TestError", "Test error message")

        # Mock database metrics to avoid external dependencies
        with patch.object(self.metrics, "get_database_metrics") as mock_db_metrics:
            mock_db_metrics.return_value = {"mocked": "database_metrics"}

            result = self.metrics.get_comprehensive_metrics()

        # Check all major sections are present
        assert "job_completion" in result
        assert "api_performance" in result
        assert "error_statistics" in result
        assert "database_metrics" in result
        assert "collection_info" in result
        assert "timestamp" in result

        # Check collection info
        collection_info = result["collection_info"]
        assert collection_info["max_samples"] == 100
        assert "current_samples" in collection_info

        current_samples = collection_info["current_samples"]
        assert current_samples["job_completions"] == 1
        assert current_samples["api_responses"] == 1
        assert current_samples["status_changes"] == 1

    def test_reset_metrics(self):
        """Test resetting all metrics."""
        # Add some data first
        self.metrics.record_job_completion("job-1", 30.0, "succeeded")
        self.metrics.record_api_response_time("/api/test", 100.0, 200)
        self.metrics.record_error("TestError", "Test error")

        # Verify data exists
        assert len(self.metrics._job_completion_times) > 0
        assert len(self.metrics._api_response_times) > 0
        assert len(self.metrics._error_counts) > 0
        assert len(self.metrics._job_status_changes) > 0

        # Reset metrics
        with patch.object(self.metrics, "logger") as mock_logger:
            self.metrics.reset_metrics()

            # Verify logging
            mock_logger.info.assert_called_once_with("All metrics have been reset")

        # Verify all data is cleared
        assert len(self.metrics._job_completion_times) == 0
        assert len(self.metrics._api_response_times) == 0
        assert len(self.metrics._error_counts) == 0
        assert len(self.metrics._job_status_changes) == 0

    def test_max_samples_deque_limit(self):
        """Test that deque respects max_samples limit."""
        metrics = MetricsCollector(max_samples=5)

        # Add more job completions than max_samples
        for i in range(10):
            metrics.record_job_completion(f"job-{i}", float(i), "succeeded")

        # Should only keep the last 5
        assert len(metrics._job_completion_times) == 5
        assert len(metrics._job_status_changes) == 5

        # Verify it kept the most recent ones
        job_ids = [job["job_id"] for job in metrics._job_completion_times]
        expected_ids = [f"job-{i}" for i in range(5, 10)]  # job-5 through job-9
        assert job_ids == expected_ids

    def test_thread_safety(self):
        """Test thread safety of metrics collection."""

        def record_metrics():
            for i in range(50):
                self.metrics.record_job_completion(f"job-{i}", float(i), "succeeded")
                self.metrics.record_api_response_time("/api/test", float(i), 200)
                self.metrics.record_error("TestError", f"Error {i}")

        # Run multiple threads simultaneously
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=record_metrics)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify all data was recorded (should have 150 of each)
        # Note: deque may have trimmed to max_samples (100)
        assert len(self.metrics._job_completion_times) == 100  # Limited by max_samples
        assert len(self.metrics._api_response_times) == 100  # Limited by max_samples
        assert (
            self.metrics._error_counts["TestError"] == 150
        )  # Error counts not limited

    def test_get_job_completion_rate_all_failed(self):
        """Test job completion rate with all failed jobs."""
        failed_jobs = [
            ("job-1", 30.0, "failed"),
            ("job-2", 25.5, "failed"),
            ("job-3", 60.0, "failed"),
        ]

        for job_id, duration, status in failed_jobs:
            self.metrics.record_job_completion(job_id, duration, status)

        result = self.metrics.get_job_completion_rate(hours=24)

        assert result["total_jobs"] == 3
        assert result["successful_jobs"] == 0
        assert result["failed_jobs"] == 3
        assert result["success_rate"] == 0.0
        assert result["error_rate"] == 100.0
        assert result["average_duration_seconds"] == 0.0  # No successful jobs

    def test_api_performance_metrics_different_status_codes(self):
        """Test API performance metrics with various status codes."""
        responses = [
            ("/api/test", 100.0, 200),  # Success
            ("/api/test", 150.0, 201),  # Success
            ("/api/test", 200.0, 300),  # Redirect (not success for our definition)
            ("/api/test", 250.0, 400),  # Client error
            ("/api/test", 300.0, 500),  # Server error
        ]

        for endpoint, response_time, status_code in responses:
            self.metrics.record_api_response_time(endpoint, response_time, status_code)

        result = self.metrics.get_api_performance_metrics(hours=24)

        assert result["total_requests"] == 5
        assert result["successful_requests"] == 2  # Only 200-399 are considered success
        assert result["success_rate"] == 40.0  # 2/5 * 100

    def test_job_completion_rate_edge_cases(self):
        """Test job completion rate with edge cases."""
        # Test with very short time period
        self.metrics.record_job_completion("job-1", 30.0, "succeeded")

        # Test with 0 hours (should handle gracefully)
        result = self.metrics.get_job_completion_rate(hours=0)
        assert result["total_jobs"] == 0  # No jobs in 0-hour window

        # Test with very large time period
        result = self.metrics.get_job_completion_rate(hours=8760)  # 1 year
        assert result["total_jobs"] == 1  # Should include our job

    def test_error_statistics_zero_hours(self):
        """Test error statistics with zero hours period."""
        self.metrics.record_error("TestError", "Test message")

        result = self.metrics.get_error_statistics(hours=0)

        assert result["period_hours"] == 0
        assert result["error_rate_per_hour"] == 0  # Should handle division by zero
