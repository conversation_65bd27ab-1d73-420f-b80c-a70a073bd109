"""Tests for health check monitoring functionality."""

import os
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
import requests
from sqlalchemy.exc import SQLAlchemyError

from src.monitoring.health_check import HealthCheck


@pytest.mark.integration
class TestHealthCheck:
    """Test HealthCheck functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.health_check = HealthCheck()

    @patch("src.monitoring.health_check.get_db_manager")
    @patch("src.monitoring.health_check.JobRepository")
    def test_check_database_health_success(
        self, mock_job_repo_class, mock_get_db_manager
    ):
        """Test successful database health check."""
        # Mock database manager and session
        mock_session = MagicMock()
        mock_session.execute.return_value.scalar.return_value = 1

        mock_db_manager = MagicMock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        mock_db_manager.get_session.return_value.__exit__.return_value = None
        mock_get_db_manager.return_value = mock_db_manager

        # Mock job repository
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.return_value = {
            "total_jobs": 10,
            "completed_jobs": 8,
            "failed_jobs": 1,
            "pending_jobs": 1,
        }
        mock_job_repo_class.return_value = mock_job_repo

        # Create fresh instance to pick up mocked dependencies
        health_check = HealthCheck()
        health_check.job_repository = mock_job_repo

        result = health_check.check_database_health()

        assert result["status"] == "healthy"
        assert "response_time_ms" in result
        assert result["test_query_result"] == 1
        assert "job_statistics" in result
        assert "timestamp" in result
        assert isinstance(result["response_time_ms"], float)

    @patch("src.monitoring.health_check.get_db_manager")
    def test_check_database_health_sqlalchemy_error(self, mock_get_db_manager):
        """Test database health check with SQLAlchemy error."""
        # Mock database manager to raise SQLAlchemy error
        mock_db_manager = MagicMock()
        mock_db_manager.get_session.side_effect = SQLAlchemyError("Connection failed")
        mock_get_db_manager.return_value = mock_db_manager

        health_check = HealthCheck()
        result = health_check.check_database_health()

        assert result["status"] == "unhealthy"
        assert "error" in result
        assert "Connection failed" in result["error"]
        assert "timestamp" in result

    @patch("src.monitoring.health_check.get_db_manager")
    def test_check_database_health_unexpected_error(self, mock_get_db_manager):
        """Test database health check with unexpected error."""
        # Mock database manager to raise unexpected error
        mock_db_manager = MagicMock()
        mock_db_manager.get_session.side_effect = Exception("Unexpected error")
        mock_get_db_manager.return_value = mock_db_manager

        health_check = HealthCheck()
        result = health_check.check_database_health()

        assert result["status"] == "error"
        assert "error" in result
        assert "Unexpected error" in result["error"]
        assert "timestamp" in result

    def test_check_azure_api_health_missing_endpoint(self):
        """Test Azure API health check with missing endpoint."""
        with patch.dict(os.environ, {}, clear=True):
            result = self.health_check.check_azure_api_health()

            assert result["status"] == "configuration_error"
            assert "AZURE_OPENAI_ENDPOINT not configured" in result["error"]
            assert "timestamp" in result

    def test_check_azure_api_health_missing_auth(self):
        """Test Azure API health check with missing authentication."""
        with patch.dict(
            os.environ,
            {"AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/"},
            clear=True,
        ):
            result = self.health_check.check_azure_api_health()

            assert result["status"] == "configuration_error"
            assert "No authentication method configured" in result["error"]
            assert "timestamp" in result

    @patch("src.monitoring.health_check.requests.head")
    def test_check_azure_api_health_reachable(self, mock_head):
        """Test Azure API health check with reachable endpoint."""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_head.return_value = mock_response

        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
        }

        with patch.dict(os.environ, test_env, clear=True):
            result = self.health_check.check_azure_api_health()

            assert result["status"] == "reachable"
            assert result["status_code"] == 200
            assert "response_time_ms" in result
            assert result["endpoint"] == "https://test.openai.azure.com/"
            assert "timestamp" in result

    @patch("src.monitoring.health_check.requests.head")
    def test_check_azure_api_health_various_status_codes(self, mock_head):
        """Test Azure API health check with various acceptable status codes."""
        acceptable_codes = [200, 400, 401, 403, 404, 405]

        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
        }

        for status_code in acceptable_codes:
            mock_response = MagicMock()
            mock_response.status_code = status_code
            mock_head.return_value = mock_response

            with patch.dict(os.environ, test_env, clear=True):
                result = self.health_check.check_azure_api_health()

                assert result["status"] == "reachable"
                assert result["status_code"] == status_code

    @patch("src.monitoring.health_check.requests.head")
    def test_check_azure_api_health_unreachable(self, mock_head):
        """Test Azure API health check with unreachable endpoint."""
        # Mock error response
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_head.return_value = mock_response

        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
        }

        with patch.dict(os.environ, test_env, clear=True):
            result = self.health_check.check_azure_api_health()

            assert result["status"] == "unreachable"
            assert result["status_code"] == 500
            assert "response_time_ms" in result

    @patch("src.monitoring.health_check.requests.head")
    def test_check_azure_api_health_timeout(self, mock_head):
        """Test Azure API health check with timeout."""
        mock_head.side_effect = requests.exceptions.Timeout("Request timed out")

        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
        }

        with patch.dict(os.environ, test_env, clear=True):
            result = self.health_check.check_azure_api_health()

            assert result["status"] == "timeout"
            assert "Azure API request timed out" in result["error"]
            assert result["endpoint"] == "https://test.openai.azure.com/"

    @patch("src.monitoring.health_check.requests.head")
    def test_check_azure_api_health_connection_error(self, mock_head):
        """Test Azure API health check with connection error."""
        mock_head.side_effect = requests.exceptions.ConnectionError(
            "Connection refused"
        )

        test_env = {
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_API_KEY": "test-key",
        }

        with patch.dict(os.environ, test_env, clear=True):
            result = self.health_check.check_azure_api_health()

            assert result["status"] == "connection_error"
            assert "Connection refused" in result["error"]

    def test_check_disk_space_health_healthy(self):
        """Test disk space health check with healthy disk usage."""
        mock_file_handler = MagicMock()
        mock_file_handler.get_disk_usage.return_value = {
            "total_files": 5,
            "total_size_bytes": 1000000,
            "disk_free_gb": 50.0,
            "disk_used_percent": 70.0,
        }

        self.health_check.file_handler = mock_file_handler
        result = self.health_check.check_disk_space_health()

        assert result["status"] == "healthy"
        assert result["disk_usage"]["disk_used_percent"] == 70.0
        assert result["thresholds"]["warning"] == 80
        assert result["thresholds"]["critical"] == 90

    def test_check_disk_space_health_warning(self):
        """Test disk space health check with warning level usage."""
        mock_file_handler = MagicMock()
        mock_file_handler.get_disk_usage.return_value = {"disk_used_percent": 85.0}

        self.health_check.file_handler = mock_file_handler
        result = self.health_check.check_disk_space_health()

        assert result["status"] == "warning"

    def test_check_disk_space_health_critical(self):
        """Test disk space health check with critical level usage."""
        mock_file_handler = MagicMock()
        mock_file_handler.get_disk_usage.return_value = {"disk_used_percent": 95.0}

        self.health_check.file_handler = mock_file_handler
        result = self.health_check.check_disk_space_health()

        assert result["status"] == "critical"

    def test_check_disk_space_health_error(self):
        """Test disk space health check with error."""
        mock_file_handler = MagicMock()
        mock_file_handler.get_disk_usage.side_effect = Exception("Disk access error")

        self.health_check.file_handler = mock_file_handler
        result = self.health_check.check_disk_space_health()

        assert result["status"] == "error"
        assert "Disk access error" in result["error"]

    def test_check_job_queue_health_healthy(self):
        """Test job queue health check with healthy queue."""
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.return_value = {
            "total_jobs": 100,
            "completed_jobs": 90,
            "failed_jobs": 5,
            "pending_jobs": 5,
        }

        # Mock pending and running jobs (small numbers)
        pending_jobs = [Mock(id=f"pending-{i}") for i in range(5)]
        running_jobs = [Mock(id=f"running-{i}") for i in range(2)]

        mock_job_repo.get_pending_jobs.return_value = pending_jobs
        mock_job_repo.get_running_jobs.return_value = running_jobs

        self.health_check.job_repository = mock_job_repo
        result = self.health_check.check_job_queue_health()

        assert result["status"] == "healthy"
        assert result["queue_info"]["pending_jobs"] == 5
        assert result["queue_info"]["running_jobs"] == 2
        assert len(result["recent_pending_jobs"]) == 5
        assert len(result["recent_running_jobs"]) == 2

    def test_check_job_queue_health_busy(self):
        """Test job queue health check with busy queue."""
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.return_value = {"total_jobs": 100}

        # Mock many pending jobs (more than half of max threshold)
        pending_jobs = [Mock(id=f"pending-{i}") for i in range(30)]  # >25 (half of 50)
        running_jobs = [Mock(id=f"running-{i}") for i in range(3)]

        mock_job_repo.get_pending_jobs.return_value = pending_jobs
        mock_job_repo.get_running_jobs.return_value = running_jobs

        self.health_check.job_repository = mock_job_repo
        result = self.health_check.check_job_queue_health()

        assert result["status"] == "busy"
        assert result["queue_info"]["pending_jobs"] == 30

    def test_check_job_queue_health_overloaded_pending(self):
        """Test job queue health check with too many pending jobs."""
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.return_value = {"total_jobs": 100}

        # Mock too many pending jobs
        pending_jobs = [Mock(id=f"pending-{i}") for i in range(60)]  # >50 threshold
        running_jobs = [Mock(id=f"running-{i}") for i in range(3)]

        mock_job_repo.get_pending_jobs.return_value = pending_jobs
        mock_job_repo.get_running_jobs.return_value = running_jobs

        self.health_check.job_repository = mock_job_repo
        result = self.health_check.check_job_queue_health()

        assert result["status"] == "overloaded"
        assert result["queue_info"]["pending_jobs"] == 60

    def test_check_job_queue_health_overloaded_running(self):
        """Test job queue health check with too many running jobs."""
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.return_value = {"total_jobs": 100}

        # Mock too many running jobs
        pending_jobs = [Mock(id=f"pending-{i}") for i in range(5)]
        running_jobs = [Mock(id=f"running-{i}") for i in range(15)]  # >10 threshold

        mock_job_repo.get_pending_jobs.return_value = pending_jobs
        mock_job_repo.get_running_jobs.return_value = running_jobs

        self.health_check.job_repository = mock_job_repo
        result = self.health_check.check_job_queue_health()

        assert result["status"] == "overloaded"
        assert result["queue_info"]["running_jobs"] == 15

    def test_check_job_queue_health_recent_jobs_limit(self):
        """Test job queue health check limits recent job lists."""
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.return_value = {"total_jobs": 100}

        # Mock many jobs (more than 5)
        pending_jobs = [Mock(id=f"pending-{i}") for i in range(10)]
        running_jobs = [Mock(id=f"running-{i}") for i in range(8)]

        mock_job_repo.get_pending_jobs.return_value = pending_jobs
        mock_job_repo.get_running_jobs.return_value = running_jobs

        self.health_check.job_repository = mock_job_repo
        result = self.health_check.check_job_queue_health()

        # Should only return first 5 job IDs
        assert len(result["recent_pending_jobs"]) == 5
        assert len(result["recent_running_jobs"]) == 5

    def test_check_job_queue_health_error(self):
        """Test job queue health check with error."""
        mock_job_repo = MagicMock()
        mock_job_repo.get_job_stats.side_effect = Exception("Repository error")

        self.health_check.job_repository = mock_job_repo
        result = self.health_check.check_job_queue_health()

        assert result["status"] == "error"
        assert "Repository error" in result["error"]

    def test_get_overall_health_all_healthy(self):
        """Test overall health check with all components healthy."""
        with patch.object(
            self.health_check, "check_database_health"
        ) as mock_db, patch.object(
            self.health_check, "check_azure_api_health"
        ) as mock_azure, patch.object(
            self.health_check, "check_disk_space_health"
        ) as mock_disk, patch.object(
            self.health_check, "check_job_queue_health"
        ) as mock_queue:
            # Mock all components as healthy
            mock_db.return_value = {"status": "healthy"}
            mock_azure.return_value = {"status": "reachable"}
            mock_disk.return_value = {"status": "healthy"}
            mock_queue.return_value = {"status": "healthy"}

            result = self.health_check.get_overall_health()

            assert result["overall_status"] == "healthy"
            assert "components" in result
            assert "timestamp" in result
            assert result["version"] == "1.0.0"

            # Check that all component checks were called
            mock_db.assert_called_once()
            mock_azure.assert_called_once()
            mock_disk.assert_called_once()
            mock_queue.assert_called_once()

    def test_get_overall_health_warning_status(self):
        """Test overall health check with warning status."""
        with patch.object(
            self.health_check, "check_database_health"
        ) as mock_db, patch.object(
            self.health_check, "check_azure_api_health"
        ) as mock_azure, patch.object(
            self.health_check, "check_disk_space_health"
        ) as mock_disk, patch.object(
            self.health_check, "check_job_queue_health"
        ) as mock_queue:
            # One component with warning status
            mock_db.return_value = {"status": "healthy"}
            mock_azure.return_value = {"status": "reachable"}
            mock_disk.return_value = {"status": "warning"}  # Warning status
            mock_queue.return_value = {"status": "healthy"}

            result = self.health_check.get_overall_health()

            assert result["overall_status"] == "warning"

    def test_get_overall_health_unhealthy_status(self):
        """Test overall health check with unhealthy status."""
        with patch.object(
            self.health_check, "check_database_health"
        ) as mock_db, patch.object(
            self.health_check, "check_azure_api_health"
        ) as mock_azure, patch.object(
            self.health_check, "check_disk_space_health"
        ) as mock_disk, patch.object(
            self.health_check, "check_job_queue_health"
        ) as mock_queue:
            # One component unhealthy
            mock_db.return_value = {"status": "unhealthy"}  # Unhealthy status
            mock_azure.return_value = {"status": "reachable"}
            mock_disk.return_value = {"status": "healthy"}
            mock_queue.return_value = {"status": "healthy"}

            result = self.health_check.get_overall_health()

            assert result["overall_status"] == "unhealthy"

    def test_get_overall_health_critical_status(self):
        """Test overall health check with critical status."""
        with patch.object(
            self.health_check, "check_database_health"
        ) as mock_db, patch.object(
            self.health_check, "check_azure_api_health"
        ) as mock_azure, patch.object(
            self.health_check, "check_disk_space_health"
        ) as mock_disk, patch.object(
            self.health_check, "check_job_queue_health"
        ) as mock_queue:
            # One component critical
            mock_db.return_value = {"status": "healthy"}
            mock_azure.return_value = {"status": "reachable"}
            mock_disk.return_value = {"status": "critical"}  # Critical status
            mock_queue.return_value = {"status": "healthy"}

            result = self.health_check.get_overall_health()

            assert result["overall_status"] == "critical"

    def test_get_overall_health_error_status(self):
        """Test overall health check with error status."""
        with patch.object(
            self.health_check, "check_database_health"
        ) as mock_db, patch.object(
            self.health_check, "check_azure_api_health"
        ) as mock_azure, patch.object(
            self.health_check, "check_disk_space_health"
        ) as mock_disk, patch.object(
            self.health_check, "check_job_queue_health"
        ) as mock_queue:
            # One component with error
            mock_db.return_value = {"status": "error"}  # Error status
            mock_azure.return_value = {"status": "reachable"}
            mock_disk.return_value = {"status": "healthy"}
            mock_queue.return_value = {"status": "healthy"}

            result = self.health_check.get_overall_health()

            assert result["overall_status"] == "critical"

    def test_get_overall_health_mixed_statuses(self):
        """Test overall health check with mixed component statuses."""
        test_cases = [
            # (db, azure, disk, queue, expected_overall)
            ("healthy", "reachable", "busy", "warning", "warning"),
            ("unhealthy", "reachable", "healthy", "healthy", "unhealthy"),
            ("healthy", "unreachable", "warning", "healthy", "unhealthy"),
            ("healthy", "reachable", "critical", "warning", "critical"),
            ("error", "reachable", "warning", "healthy", "critical"),
            ("overloaded", "timeout", "critical", "busy", "critical"),
        ]

        for db_status, azure_status, disk_status, queue_status, expected in test_cases:
            with patch.object(
                self.health_check, "check_database_health"
            ) as mock_db, patch.object(
                self.health_check, "check_azure_api_health"
            ) as mock_azure, patch.object(
                self.health_check, "check_disk_space_health"
            ) as mock_disk, patch.object(
                self.health_check, "check_job_queue_health"
            ) as mock_queue:
                mock_db.return_value = {"status": db_status}
                mock_azure.return_value = {"status": azure_status}
                mock_disk.return_value = {"status": disk_status}
                mock_queue.return_value = {"status": queue_status}

                result = self.health_check.get_overall_health()

                assert result["overall_status"] == expected, (
                    f"Expected {expected} for statuses: db={db_status}, azure={azure_status}, disk={disk_status}, queue={queue_status}"
                )

    def test_health_check_initialization(self):
        """Test HealthCheck initialization."""
        health_check = HealthCheck()

        assert hasattr(health_check, "logger")
        assert hasattr(health_check, "job_repository")
        assert hasattr(health_check, "file_handler")

    def test_health_check_logging(self):
        """Test that health check includes logging calls."""
        with patch.object(self.health_check, "logger") as mock_logger, patch.object(
            self.health_check, "check_database_health"
        ) as mock_db, patch.object(
            self.health_check, "check_azure_api_health"
        ) as mock_azure, patch.object(
            self.health_check, "check_disk_space_health"
        ) as mock_disk, patch.object(
            self.health_check, "check_job_queue_health"
        ) as mock_queue:
            # Mock all health checks
            mock_db.return_value = {"status": "healthy"}
            mock_azure.return_value = {"status": "reachable"}
            mock_disk.return_value = {"status": "healthy"}
            mock_queue.return_value = {"status": "healthy"}

            self.health_check.get_overall_health()

            # Verify logging was called
            mock_logger.info.assert_called_with("Starting comprehensive health check")
