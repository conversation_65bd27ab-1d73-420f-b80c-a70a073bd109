# Monitoring Module Documentation

## Overview

Production-ready monitoring system with comprehensive health checks, performance metrics collection, and system observability. Provides real-time system status monitoring, performance baselines, and alerting capabilities for the multi-user video generation platform.

## Performance Guidelines

**Efficiency**: Choose appropriate algorithms and data structures for video processing
**Resource Management**: Proper cleanup of video files and memory management
**Scalability**: Design with 15+ concurrent users and future growth in mind
**Monitoring**: Comprehensive logging and metrics for production video generation systems

## Module Structure

```
src/monitoring/
├── health_check.py     # System health monitoring with queue checks
├── metrics.py          # Performance metrics collection
└── tests/              # Co-located monitoring tests
    ├── test_health_check.py
    └── test_metrics.py
```

## Health Check System

### HealthCheck Class
```python
# src/monitoring/health_check.py
from typing import Dict, Any, Optional, List
from enum import Enum
import time
import logging
from dataclasses import dataclass
from sqlalchemy import text

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """Health check status levels."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class ComponentHealth:
    """Health status for individual component."""
    name: str
    status: HealthStatus
    message: str
    response_time_ms: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class HealthCheck:
    """Comprehensive system health monitoring."""
    
    def __init__(self, db_session_factory, redis_client, celery_app=None):
        self.db_session_factory = db_session_factory
        self.redis_client = redis_client
        self.celery_app = celery_app
        self.component_checks = {
            'database': self._check_database_health,
            'redis': self._check_redis_health,
            'azure_api': self._check_azure_api_health,
            'disk_space': self._check_disk_space,
            'job_queue': self._check_job_queue_health,
            'memory': self._check_memory_usage,
            'rate_limiting': self._check_rate_limiting
        }
    
    def get_overall_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health status.
        
        Returns:
            dict: Overall health status with component details
        """
        start_time = time.time()
        component_results = {}
        
        # Run all health checks
        for component_name, check_func in self.component_checks.items():
            try:
                component_results[component_name] = check_func()
            except Exception as e:
                logger.error(f"Health check failed for {component_name}: {e}")
                component_results[component_name] = ComponentHealth(
                    name=component_name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(e)}"
                )
        
        # Determine overall status
        overall_status = self._calculate_overall_status(component_results)
        
        # Calculate total response time
        total_response_time = (time.time() - start_time) * 1000
        
        return {
            'overall_status': overall_status.value,
            'timestamp': time.time(),
            'response_time_ms': total_response_time,
            'components': {
                name: {
                    'status': health.status.value,
                    'message': health.message,
                    'response_time_ms': health.response_time_ms,
                    'metadata': health.metadata or {}
                }
                for name, health in component_results.items()
            },
            'version': self._get_application_version(),
            'uptime': self._get_uptime()
        }
    
    def _check_database_health(self) -> ComponentHealth:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            with self.db_session_factory() as session:
                # Test basic connectivity
                result = session.execute(text("SELECT 1")).scalar()
                
                if result != 1:
                    return ComponentHealth(
                        name="database",
                        status=HealthStatus.UNHEALTHY,
                        message="Database query returned unexpected result"
                    )
                
                # Test job table access
                job_count = session.execute(
                    text("SELECT COUNT(*) FROM video_jobs WHERE status = 'running'")
                ).scalar()
                
                response_time = (time.time() - start_time) * 1000
                
                # Check for performance issues
                if response_time > 1000:  # 1 second
                    status = HealthStatus.DEGRADED
                    message = f"Database responding slowly ({response_time:.0f}ms)"
                else:
                    status = HealthStatus.HEALTHY
                    message = "Database connection healthy"
                
                return ComponentHealth(
                    name="database",
                    status=status,
                    message=message,
                    response_time_ms=response_time,
                    metadata={
                        'running_jobs': job_count,
                        'connection_pool_size': self._get_db_pool_info()
                    }
                )
                
        except Exception as e:
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}"
            )
    
    def _check_redis_health(self) -> ComponentHealth:
        """Check Redis connectivity and performance."""
        start_time = time.time()
        
        try:
            # Test basic connectivity
            pong = self.redis_client.ping()
            if not pong:
                return ComponentHealth(
                    name="redis",
                    status=HealthStatus.UNHEALTHY,
                    message="Redis ping failed"
                )
            
            # Test read/write operations
            test_key = "health_check_test"
            test_value = str(time.time())
            
            self.redis_client.set(test_key, test_value, ex=60)  # 1 minute expiry
            retrieved_value = self.redis_client.get(test_key)
            
            if retrieved_value.decode() != test_value:
                return ComponentHealth(
                    name="redis",
                    status=HealthStatus.DEGRADED,
                    message="Redis read/write test failed"
                )
            
            # Clean up test key
            self.redis_client.delete(test_key)
            
            response_time = (time.time() - start_time) * 1000
            
            # Get Redis info
            redis_info = self.redis_client.info()
            memory_usage = redis_info.get('used_memory_human', 'unknown')
            connected_clients = redis_info.get('connected_clients', 0)
            
            # Check for performance issues
            if response_time > 500:  # 500ms
                status = HealthStatus.DEGRADED
                message = f"Redis responding slowly ({response_time:.0f}ms)"
            elif connected_clients > 100:  # High client count
                status = HealthStatus.DEGRADED
                message = f"High Redis client count ({connected_clients})"
            else:
                status = HealthStatus.HEALTHY
                message = "Redis connection healthy"
            
            return ComponentHealth(
                name="redis",
                status=status,
                message=message,
                response_time_ms=response_time,
                metadata={
                    'memory_usage': memory_usage,
                    'connected_clients': connected_clients,
                    'version': redis_info.get('redis_version', 'unknown')
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="redis",
                status=HealthStatus.UNHEALTHY,
                message=f"Redis connection failed: {str(e)}"
            )
    
    def _check_azure_api_health(self) -> ComponentHealth:
        """Check Azure OpenAI API connectivity."""
        start_time = time.time()
        
        try:
            # Import Azure client (lazy import to avoid circular dependencies)
            from src.features.sora_integration.client import SoraClient
            
            # Create client instance
            client = SoraClient.from_env()
            
            # Test API connectivity (this would be a lightweight health check)
            # In practice, you might have a dedicated health check endpoint
            # For now, we'll check if the client can be instantiated properly
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="azure_api",
                status=HealthStatus.HEALTHY,
                message="Azure API client initialized successfully",
                response_time_ms=response_time,
                metadata={
                    'endpoint': client.http_client.endpoint if hasattr(client, 'http_client') else 'configured',
                    'rate_limit_status': 'active'
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="azure_api",
                status=HealthStatus.UNHEALTHY,
                message=f"Azure API client failed: {str(e)}"
            )
    
    def _check_disk_space(self) -> ComponentHealth:
        """Check available disk space."""
        try:
            import shutil
            
            # Check disk space for current directory
            total, used, free = shutil.disk_usage('.')
            
            # Convert to GB
            total_gb = total / (1024**3)
            used_gb = used / (1024**3)
            free_gb = free / (1024**3)
            usage_percent = (used / total) * 100
            
            # Determine status based on available space
            if free_gb < 1:  # Less than 1GB free
                status = HealthStatus.UNHEALTHY
                message = f"Critical disk space: {free_gb:.1f}GB free"
            elif usage_percent > 90:  # More than 90% used
                status = HealthStatus.DEGRADED
                message = f"Low disk space: {usage_percent:.1f}% used"
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk space healthy: {free_gb:.1f}GB free"
            
            return ComponentHealth(
                name="disk_space",
                status=status,
                message=message,
                metadata={
                    'total_gb': round(total_gb, 1),
                    'used_gb': round(used_gb, 1),
                    'free_gb': round(free_gb, 1),
                    'usage_percent': round(usage_percent, 1)
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="disk_space",
                status=HealthStatus.UNKNOWN,
                message=f"Could not check disk space: {str(e)}"
            )
    
    def _check_job_queue_health(self) -> ComponentHealth:
        """Check Celery job queue health."""
        try:
            if not self.celery_app:
                return ComponentHealth(
                    name="job_queue",
                    status=HealthStatus.UNKNOWN,
                    message="Celery app not configured"
                )
            
            # Check if workers are responding
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()
            
            if not stats:
                return ComponentHealth(
                    name="job_queue",
                    status=HealthStatus.UNHEALTHY,
                    message="No Celery workers responding"
                )
            
            # Get active and reserved tasks
            active_tasks = inspect.active()
            reserved_tasks = inspect.reserved()
            
            # Calculate totals
            total_workers = len(stats)
            total_active = sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0
            total_reserved = sum(len(tasks) for tasks in reserved_tasks.values()) if reserved_tasks else 0
            
            # Determine status
            if total_active > 50:  # High queue depth
                status = HealthStatus.DEGRADED
                message = f"High queue depth: {total_active} active tasks"
            else:
                status = HealthStatus.HEALTHY
                message = f"Queue healthy: {total_workers} workers, {total_active} active tasks"
            
            return ComponentHealth(
                name="job_queue",
                status=status,
                message=message,
                metadata={
                    'workers_online': total_workers,
                    'active_tasks': total_active,
                    'reserved_tasks': total_reserved,
                    'queue_depth': total_active + total_reserved
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="job_queue",
                status=HealthStatus.UNHEALTHY,
                message=f"Queue check failed: {str(e)}"
            )
    
    def _check_memory_usage(self) -> ComponentHealth:
        """Check system memory usage."""
        try:
            import psutil
            
            # Get memory statistics
            memory = psutil.virtual_memory()
            
            # Convert to GB
            total_gb = memory.total / (1024**3)
            available_gb = memory.available / (1024**3)
            used_percent = memory.percent
            
            # Determine status
            if used_percent > 90:
                status = HealthStatus.UNHEALTHY
                message = f"Critical memory usage: {used_percent:.1f}% used"
            elif used_percent > 80:
                status = HealthStatus.DEGRADED
                message = f"High memory usage: {used_percent:.1f}% used"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage healthy: {used_percent:.1f}% used"
            
            return ComponentHealth(
                name="memory",
                status=status,
                message=message,
                metadata={
                    'total_gb': round(total_gb, 1),
                    'available_gb': round(available_gb, 1),
                    'used_percent': round(used_percent, 1)
                }
            )
            
        except ImportError:
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNKNOWN,
                message="psutil not available for memory monitoring"
            )
        except Exception as e:
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNKNOWN,
                message=f"Memory check failed: {str(e)}"
            )
    
    def _check_rate_limiting(self) -> ComponentHealth:
        """Check rate limiting system health."""
        try:
            # Test rate limiting connectivity
            from src.rate_limiting.limiter import GlobalRateLimiter
            
            rate_limiter = GlobalRateLimiter(
                redis_client=self.redis_client,
                default_limit=1,
                default_window=60
            )
            
            # Test rate limiting functionality
            test_key = "health_check_rate_limit"
            is_allowed = rate_limiter.is_allowed(test_key)
            
            return ComponentHealth(
                name="rate_limiting",
                status=HealthStatus.HEALTHY,
                message="Rate limiting system operational",
                metadata={
                    'test_result': 'allowed' if is_allowed else 'rate_limited',
                    'strategies_available': len(rate_limiter.strategies)
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="rate_limiting",
                status=HealthStatus.DEGRADED,
                message=f"Rate limiting check failed: {str(e)}"
            )
    
    def _calculate_overall_status(self, component_results: Dict[str, ComponentHealth]) -> HealthStatus:
        """Calculate overall system status from component results."""
        statuses = [health.status for health in component_results.values()]
        
        # If any component is unhealthy, system is unhealthy
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY
        
        # If any component is degraded, system is degraded
        if HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED
        
        # If any component is unknown, system status is unknown
        if HealthStatus.UNKNOWN in statuses:
            return HealthStatus.UNKNOWN
        
        # All components are healthy
        return HealthStatus.HEALTHY
    
    def _get_application_version(self) -> str:
        """Get application version."""
        try:
            # This could read from a VERSION file or package metadata
            return "1.0.0"  # Placeholder
        except Exception:
            return "unknown"
    
    def _get_uptime(self) -> float:
        """Get application uptime in seconds."""
        try:
            import psutil
            import os
            
            # Get current process uptime
            process = psutil.Process(os.getpid())
            return time.time() - process.create_time()
        except Exception:
            return 0.0
    
    def _get_db_pool_info(self) -> Dict[str, Any]:
        """Get database connection pool information."""
        try:
            # This would depend on your specific database setup
            return {
                'pool_size': 'unknown',
                'checked_out': 'unknown',
                'overflow': 'unknown'
            }
        except Exception:
            return {}
```

## Metrics Collection

### MetricsCollector Class
```python
# src/monitoring/metrics.py
from typing import Dict, Any, Optional, List
import time
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum

class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"

@dataclass
class MetricData:
    """Container for metric data."""
    name: str
    type: MetricType
    value: float
    timestamp: float = field(default_factory=time.time)
    tags: Dict[str, str] = field(default_factory=dict)

class MetricsCollector:
    """Production-ready metrics collection system."""
    
    def __init__(self, max_metrics_history: int = 1000):
        self.max_metrics_history = max_metrics_history
        self._metrics = defaultdict(lambda: deque(maxlen=max_metrics_history))
        self._counters = defaultdict(float)
        self._gauges = defaultdict(float)
        self._timers = defaultdict(list)
        self._lock = threading.RLock()
    
    def increment(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric."""
        with self._lock:
            self._counters[name] += value
            self._record_metric(name, MetricType.COUNTER, self._counters[name], tags or {})
    
    def decrement(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None) -> None:
        """Decrement a counter metric."""
        self.increment(name, -value, tags)
    
    def gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set a gauge metric."""
        with self._lock:
            self._gauges[name] = value
            self._record_metric(name, MetricType.GAUGE, value, tags or {})
    
    def timer(self, name: str, tags: Optional[Dict[str, str]] = None):
        """Context manager for timing operations."""
        return TimerContext(self, name, tags or {})
    
    def histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a histogram value."""
        with self._lock:
            self._timers[name].append(value)
            # Keep only recent values
            if len(self._timers[name]) > 100:
                self._timers[name] = self._timers[name][-100:]
            
            self._record_metric(name, MetricType.HISTOGRAM, value, tags or {})
    
    def get_counter(self, name: str) -> float:
        """Get current counter value."""
        with self._lock:
            return self._counters.get(name, 0.0)
    
    def get_gauge(self, name: str) -> float:
        """Get current gauge value."""
        with self._lock:
            return self._gauges.get(name, 0.0)
    
    def get_histogram_stats(self, name: str) -> Dict[str, float]:
        """Get histogram statistics."""
        with self._lock:
            values = self._timers.get(name, [])
            if not values:
                return {'count': 0, 'min': 0, 'max': 0, 'avg': 0, 'p95': 0}
            
            sorted_values = sorted(values)
            count = len(sorted_values)
            
            return {
                'count': count,
                'min': sorted_values[0],
                'max': sorted_values[-1],
                'avg': sum(sorted_values) / count,
                'p95': sorted_values[int(0.95 * count)] if count > 0 else 0,
                'p99': sorted_values[int(0.99 * count)] if count > 0 else 0
            }
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all current metric values."""
        with self._lock:
            metrics = {
                'counters': dict(self._counters),
                'gauges': dict(self._gauges),
                'histograms': {}
            }
            
            # Add histogram statistics
            for name in self._timers:
                metrics['histograms'][name] = self.get_histogram_stats(name)
            
            return metrics
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of system metrics."""
        all_metrics = self.get_all_metrics()
        
        return {
            'timestamp': time.time(),
            'total_counters': len(all_metrics['counters']),
            'total_gauges': len(all_metrics['gauges']),
            'total_histograms': len(all_metrics['histograms']),
            'metrics': all_metrics
        }
    
    def reset_metrics(self) -> None:
        """Reset all metrics (useful for testing)."""
        with self._lock:
            self._metrics.clear()
            self._counters.clear()
            self._gauges.clear()
            self._timers.clear()
    
    def _record_metric(self, name: str, metric_type: MetricType, value: float, tags: Dict[str, str]) -> None:
        """Record metric data with timestamp."""
        metric = MetricData(
            name=name,
            type=metric_type,
            value=value,
            tags=tags
        )
        self._metrics[name].append(metric)

class TimerContext:
    """Context manager for timing operations."""
    
    def __init__(self, collector: MetricsCollector, name: str, tags: Dict[str, str]):
        self.collector = collector
        self.name = name
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (time.time() - self.start_time) * 1000  # Convert to milliseconds
            self.collector.histogram(self.name, duration, self.tags)
    
    def record_metric(self, name: str, value: float) -> None:
        """Record additional metric during timing."""
        self.collector.gauge(name, value, self.tags)
```

## Performance Monitoring

### Application Performance Monitoring
```python
class ApplicationMonitor:
    """Monitor application performance and health."""
    
    def __init__(self, metrics_collector: MetricsCollector, health_check: HealthCheck):
        self.metrics = metrics_collector
        self.health_check = health_check
        self.monitoring_interval = 60  # 1 minute
        self._monitoring_thread = None
        self._stop_monitoring = False
    
    def start_monitoring(self):
        """Start background monitoring."""
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            return
        
        self._stop_monitoring = False
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        logger.info("Application monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring."""
        self._stop_monitoring = True
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5)
        logger.info("Application monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while not self._stop_monitoring:
            try:
                self._collect_system_metrics()
                self._collect_application_metrics()
                time.sleep(self.monitoring_interval)
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                time.sleep(10)  # Brief pause before retrying
    
    def _collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics.gauge('system_cpu_percent', cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.metrics.gauge('system_memory_percent', memory.percent)
            self.metrics.gauge('system_memory_available_gb', memory.available / (1024**3))
            
            # Disk usage
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            self.metrics.gauge('system_disk_percent', disk_percent)
            self.metrics.gauge('system_disk_free_gb', disk.free / (1024**3))
            
        except ImportError:
            pass  # psutil not available
        except Exception as e:
            logger.error(f"System metrics collection failed: {e}")
    
    def _collect_application_metrics(self):
        """Collect application-specific metrics."""
        try:
            # Health check metrics
            health = self.health_check.get_overall_health()
            
            # Convert health status to numeric
            status_mapping = {
                'healthy': 1,
                'degraded': 0.5,
                'unhealthy': 0,
                'unknown': -1
            }
            
            overall_health_score = status_mapping.get(health['overall_status'], -1)
            self.metrics.gauge('app_health_score', overall_health_score)
            
            # Component health scores
            for component, details in health['components'].items():
                component_score = status_mapping.get(details['status'], -1)
                self.metrics.gauge(f'app_component_health_{component}', component_score)
                
                # Response times
                if details.get('response_time_ms'):
                    self.metrics.histogram(f'app_component_response_time_{component}', 
                                        details['response_time_ms'])
            
        except Exception as e:
            logger.error(f"Application metrics collection failed: {e}")

# Usage example
def create_monitoring_system():
    """Create and configure monitoring system."""
    from src.database.connection import get_db_session
    from redis import Redis
    from src.job_queue.celery_app import celery_app
    
    # Create components
    metrics_collector = MetricsCollector()
    health_check = HealthCheck(get_db_session, Redis(), celery_app)
    
    # Create application monitor
    app_monitor = ApplicationMonitor(metrics_collector, health_check)
    
    return {
        'metrics': metrics_collector,
        'health_check': health_check,
        'monitor': app_monitor
    }
```

## API Integration

### Health Check Endpoints
```python
# Integration with Flask routes
from flask import Blueprint, jsonify
from src.monitoring.health_check import HealthCheck
from src.monitoring.metrics import MetricsCollector

monitoring_bp = Blueprint('monitoring', __name__)

@monitoring_bp.route('/health')
def get_health():
    """Get overall system health."""
    health_check = current_app.health_check
    health_data = health_check.get_overall_health()
    
    # Determine HTTP status code based on health
    status_code = 200
    if health_data['overall_status'] == 'unhealthy':
        status_code = 503  # Service Unavailable
    elif health_data['overall_status'] == 'degraded':
        status_code = 200  # OK but with warnings
    
    return jsonify(health_data), status_code

@monitoring_bp.route('/health/<component>')
def get_component_health(component):
    """Get health for specific component."""
    health_check = current_app.health_check
    
    if component not in health_check.component_checks:
        return jsonify({'error': 'Component not found'}), 404
    
    try:
        component_health = health_check.component_checks[component]()
        
        return jsonify({
            'component': component,
            'status': component_health.status.value,
            'message': component_health.message,
            'response_time_ms': component_health.response_time_ms,
            'metadata': component_health.metadata or {},
            'timestamp': time.time()
        })
    
    except Exception as e:
        return jsonify({
            'component': component,
            'status': 'unhealthy',
            'message': f'Health check failed: {str(e)}',
            'timestamp': time.time()
        }), 503

@monitoring_bp.route('/metrics')
def get_metrics():
    """Get system metrics."""
    metrics_collector = current_app.metrics_collector
    metrics_data = metrics_collector.get_metrics_summary()
    
    return jsonify(metrics_data)

@monitoring_bp.route('/metrics/<metric_name>')
def get_specific_metric(metric_name):
    """Get specific metric data."""
    metrics_collector = current_app.metrics_collector
    
    # Check different metric types
    counter_value = metrics_collector.get_counter(metric_name)
    gauge_value = metrics_collector.get_gauge(metric_name)
    histogram_stats = metrics_collector.get_histogram_stats(metric_name)
    
    return jsonify({
        'name': metric_name,
        'counter': counter_value,
        'gauge': gauge_value,
        'histogram': histogram_stats,
        'timestamp': time.time()
    })
```

## Testing Patterns

### Health Check Testing
```python
import pytest
from unittest.mock import Mock, patch
from src.monitoring.health_check import HealthCheck, HealthStatus

class TestHealthCheck:
    """Test health check functionality."""
    
    @pytest.fixture
    def db_session_mock(self):
        """Mock database session."""
        session = Mock()
        session.execute.return_value.scalar.return_value = 1
        return session
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        redis = Mock()
        redis.ping.return_value = True
        redis.set.return_value = True
        redis.get.return_value = b'test_value'
        redis.delete.return_value = True
        redis.info.return_value = {
            'used_memory_human': '10M',
            'connected_clients': 5,
            'redis_version': '6.2.0'
        }
        return redis
    
    @pytest.fixture
    def health_check(self, db_session_mock, redis_mock):
        """Create health check with mocked dependencies."""
        db_factory = Mock(return_value=db_session_mock)
        db_factory.__enter__ = Mock(return_value=db_session_mock)
        db_factory.__exit__ = Mock(return_value=None)
        
        return HealthCheck(db_factory, redis_mock)
    
    def test_database_health_check_success(self, health_check):
        """Test successful database health check."""
        result = health_check._check_database_health()
        
        assert result.status == HealthStatus.HEALTHY
        assert "Database connection healthy" in result.message
        assert result.response_time_ms is not None
    
    def test_redis_health_check_success(self, health_check):
        """Test successful Redis health check."""
        result = health_check._check_redis_health()
        
        assert result.status == HealthStatus.HEALTHY
        assert "Redis connection healthy" in result.message
        assert result.metadata['connected_clients'] == 5
    
    def test_overall_health_aggregation(self, health_check):
        """Test overall health status calculation."""
        with patch.object(health_check, '_check_database_health') as db_check, \
             patch.object(health_check, '_check_redis_health') as redis_check:
            
            # Mock healthy responses
            db_check.return_value = Mock(status=HealthStatus.HEALTHY)
            redis_check.return_value = Mock(status=HealthStatus.HEALTHY)
            
            health = health_check.get_overall_health()
            
            assert health['overall_status'] == 'healthy'
            assert 'components' in health
            assert 'timestamp' in health
```

### Metrics Testing
```python
class TestMetricsCollector:
    """Test metrics collection functionality."""
    
    @pytest.fixture
    def metrics(self):
        """Create metrics collector."""
        return MetricsCollector()
    
    def test_counter_operations(self, metrics):
        """Test counter metric operations."""
        # Increment counter
        metrics.increment('test_counter', 5)
        assert metrics.get_counter('test_counter') == 5
        
        # Increment again
        metrics.increment('test_counter', 3)
        assert metrics.get_counter('test_counter') == 8
        
        # Decrement
        metrics.decrement('test_counter', 2)
        assert metrics.get_counter('test_counter') == 6
    
    def test_gauge_operations(self, metrics):
        """Test gauge metric operations."""
        metrics.gauge('test_gauge', 42.5)
        assert metrics.get_gauge('test_gauge') == 42.5
        
        # Gauge should replace previous value
        metrics.gauge('test_gauge', 100)
        assert metrics.get_gauge('test_gauge') == 100
    
    def test_timer_context_manager(self, metrics):
        """Test timer context manager."""
        import time
        
        with metrics.timer('test_timer'):
            time.sleep(0.01)  # 10ms
        
        stats = metrics.get_histogram_stats('test_timer')
        assert stats['count'] == 1
        assert stats['min'] >= 10  # At least 10ms
        assert stats['max'] >= 10
    
    def test_histogram_statistics(self, metrics):
        """Test histogram statistics calculation."""
        # Add test values
        for value in [10, 20, 30, 40, 50]:
            metrics.histogram('test_histogram', value)
        
        stats = metrics.get_histogram_stats('test_histogram')
        assert stats['count'] == 5
        assert stats['min'] == 10
        assert stats['max'] == 50
        assert stats['avg'] == 30
        assert stats['p95'] == 50
```

## Best Practices

1. **Comprehensive Coverage**: Monitor all critical system components
2. **Response Time Tracking**: Include timing information for all health checks
3. **Graceful Degradation**: Handle monitoring failures without affecting application
4. **Metric Naming**: Use consistent, descriptive metric names
5. **Performance**: Keep monitoring overhead minimal
6. **Alerting**: Set up appropriate thresholds for alerting
7. **Testing**: Test all monitoring components thoroughly
8. **Documentation**: Document all metrics and health check meanings
9. **Retention**: Configure appropriate data retention policies
10. **Security**: Ensure monitoring doesn't expose sensitive information

## Troubleshooting

### Common Issues

**Health Checks Failing**
```bash
# Test individual components
python -c "
from src.monitoring.health_check import HealthCheck
from src.database.connection import get_db_session
from redis import Redis

health = HealthCheck(get_db_session, Redis())
print('Database:', health._check_database_health())
print('Redis:', health._check_redis_health())
"

# Check overall health
curl http://localhost:5001/health
curl http://localhost:5001/health/database
curl http://localhost:5001/health/redis
```

**Metrics Not Collecting**
```bash
# Test metrics collection
python -c "
from src.monitoring.metrics import MetricsCollector

metrics = MetricsCollector()
metrics.increment('test_counter')
print('Counter:', metrics.get_counter('test_counter'))
print('All metrics:', metrics.get_all_metrics())
"

# Check metrics endpoint
curl http://localhost:5001/metrics
```

**High Resource Usage**
```bash
# Monitor monitoring overhead
top -p $(pgrep -f python)

# Check metric history size
python -c "
from src.monitoring.metrics import MetricsCollector
metrics = MetricsCollector()
# Check memory usage of metrics storage
"
```

### Development Commands
```bash
# Test monitoring system
uv run pytest src/monitoring/tests/ -v

# Test health checks
uv run pytest src/monitoring/tests/test_health_check.py -v

# Test metrics collection
uv run pytest src/monitoring/tests/test_metrics.py -v

# Start monitoring with debugging
uv run python -c "
from src.monitoring.health_check import HealthCheck
from src.monitoring.metrics import MetricsCollector
# Create and test monitoring components
"
```