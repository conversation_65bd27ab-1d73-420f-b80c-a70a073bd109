# F4 Configuration Operations Runbook

## Overview

This runbook provides comprehensive operational procedures for F4 Environment Configuration management, Google Veo3 integration, and provider switching operations.

**Target Audience:** Operations teams, DevOps engineers, SREs
**Scope:** F4 configuration deployment, monitoring, troubleshooting, and maintenance
**Last Updated:** 2025-07-27

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Deployment Procedures](#deployment-procedures)
3. [Monitoring and Alerting](#monitoring-and-alerting)
4. [Troubleshooting Guide](#troubleshooting-guide)
5. [Emergency Procedures](#emergency-procedures)
6. [Maintenance Tasks](#maintenance-tasks)
7. [Provider Management](#provider-management)
8. [Performance Optimization](#performance-optimization)

---

## System Architecture

### F4 Configuration Components

```
┌─────────────────────────────────────────────────────────────┐
│                     F4 Configuration System                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Configuration  │  │    Provider     │  │   Monitoring    │ │
│  │     Factory     │  │   Management    │  │   & Alerting    │ │
│  │                 │  │                 │  │                 │ │
│  │ • Base Config   │  │ • Azure Sora    │  │ • Metrics       │ │
│  │ • Veo3 Settings │  │ • Google Veo3   │  │ • Health Checks │ │
│  │ • Validation    │  │ • Switching     │  │ • Alerting      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Flask App      │  │  Celery Workers │  │  Health Checks  │ │
│  │                 │  │                 │  │                 │ │
│  │ • API Routes    │  │ • Video Gen     │  │ • /health/config│ │
│  │ • F4 Health     │  │ • Provider Jobs │  │ • /monitoring/f4│ │
│  │ • Monitoring    │  │ • Queue Mgmt    │  │ • Prometheus    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │    PostgreSQL   │  │      Redis      │  │     Docker      │ │
│  │                 │  │                 │  │                 │ │
│  │ • Configuration │  │ • Job Queue     │  │ • Containers    │ │
│  │ • Job Metadata  │  │ • Cache         │  │ • Networking    │ │
│  │ • Persistence   │  │ • Sessions      │  │ • Volumes       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Key Components

- **Configuration Factory**: Central configuration management
- **Provider Management**: Azure Sora and Google Veo3 integration
- **Monitoring System**: Metrics, health checks, and alerting
- **Deployment Automation**: Scripts and validation tools

---

## Deployment Procedures

### Standard F4 Deployment

#### Prerequisites

1. **Environment Validation**
   ```bash
   # Validate environment
   python src/deployment/scripts/f4_validation.py --environment production
   
   # Check current system health
   curl http://localhost/health/config
   ```

2. **Backup Creation**
   ```bash
   # Create configuration backup
   cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
   
   # Export current provider state
   curl http://localhost/health/config/providers > provider_state.backup.json
   ```

#### Deployment Steps

1. **Deploy F4 Configuration**
   ```bash
   # Production deployment
   src/deployment/scripts/f4_deployment.sh --type production --environment production
   
   # Staging deployment
   src/deployment/scripts/f4_deployment.sh --type staging --environment staging
   
   # Local development
   src/deployment/scripts/f4_deployment.sh --type local --environment development
   ```

2. **Validate Deployment**
   ```bash
   # Validate configuration
   python src/deployment/scripts/f4_validation.py --environment production --output-format html
   
   # Check health endpoints
   curl http://localhost/health/config
   curl http://localhost/health/config/providers
   curl http://localhost/monitoring/f4/metrics
   ```

3. **Monitor Deployment**
   ```bash
   # Watch deployment progress
   watch -n 5 'curl -s http://localhost/health/config | jq .status'
   
   # Check logs
   docker logs sora-app-simple
   docker logs sora-worker-simple
   ```

#### Rollback Procedure

```bash
# Emergency rollback
src/deployment/scripts/f4_deployment.sh rollback

# Manual rollback
cp .env.backup.YYYYMMDD_HHMMSS .env
docker-compose -f src/deployment/docker/docker-compose.simple.yml restart app worker
```

### Environment-Specific Procedures

#### Local Development Deployment

```bash
# Quick local setup
cd /path/to/project
cp src/deployment/config/f4-environment-local.env .env
src/deployment/scripts/f4_deployment.sh --type local

# Verify local setup
curl http://localhost:5001/health/config
```

#### Staging Deployment

```bash
# Staging deployment with testing
src/deployment/scripts/f4_deployment.sh --type staging --environment staging
python src/deployment/scripts/f4_validation.py --environment staging
```

#### Production Deployment

```bash
# Production deployment with validation
src/deployment/scripts/f4_deployment.sh --type production --environment production --force-provider-reset
python src/deployment/scripts/f4_validation.py --environment production --include-credentials
```

---

## Monitoring and Alerting

### Health Check Endpoints

| Endpoint | Purpose | Expected Response |
|----------|---------|-------------------|
| `/health/config` | Overall F4 health | `200 OK` with status |
| `/health/config/providers` | Provider availability | `200 OK` with provider status |
| `/health/config/environment` | Environment validation | `200 OK` with env status |
| `/health/config/performance` | Performance metrics | `200 OK` with timing data |
| `/monitoring/f4/metrics` | Current metrics | `200 OK` with metrics |
| `/monitoring/f4/alerts` | Alert status | `200 OK` with alerts |

### Monitoring Dashboard

Access the F4 monitoring dashboard:
```bash
# Get dashboard data
curl http://localhost/monitoring/f4/dashboard

# Get specific metrics
curl http://localhost/monitoring/f4/metrics?detailed=true&history=true

# Export Prometheus metrics
curl http://localhost/monitoring/f4/metrics/prometheus
```

### Alert Thresholds

| Metric | Warning Threshold | Critical Threshold | Action Required |
|--------|-------------------|-------------------|-----------------|
| Config Load Time | > 5 seconds | > 10 seconds | Performance tuning |
| Provider Switch Time | > 10 seconds | > 30 seconds | Provider investigation |
| Error Rate | > 5% | > 10% | Immediate investigation |
| Provider Availability | < 95% | < 50% | Provider failover |

### Setting Up Alerts

#### Slack Alerts
```bash
# Configure Slack webhook
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/WEBHOOK/URL"
export SLACK_ALERT_RATE_LIMIT_HOUR=10
export SLACK_ALERT_RATE_LIMIT_DAY=50
```

#### Email Alerts
```bash
# Configure email alerts
export ALERT_EMAIL="<EMAIL>"
export SMTP_HOST="smtp.yourcompany.com"
export SMTP_PORT=587
export SMTP_USERNAME="<EMAIL>"
export SMTP_PASSWORD="your_smtp_password"
```

---

## Troubleshooting Guide

### Common Issues

#### 1. Configuration Loading Failures

**Symptoms:**
- HTTP 500 errors from `/health/config`
- "Configuration loading failed" in logs
- High config load times

**Diagnosis:**
```bash
# Check configuration validation
python src/deployment/scripts/f4_validation.py --verbose

# Test configuration loading
python -c "
from src.config.factory import ConfigurationFactory
try:
    config = ConfigurationFactory.get_base_config()
    print('✓ Base config loaded successfully')
except Exception as e:
    print(f'✗ Config loading failed: {e}')
"
```

**Resolution:**
1. Check environment variables: `env | grep -E "(USE_MOCK_VEO|DEFAULT_PROVIDER|GOOGLE_)"`
2. Validate configuration file: `python src/deployment/scripts/f4_validation.py`
3. Restart services: `docker-compose restart app worker`

#### 2. Provider Switching Issues

**Symptoms:**
- Provider switching timeouts
- "No providers available" errors
- Intermittent video generation failures

**Diagnosis:**
```bash
# Check provider availability
curl http://localhost/health/config/providers

# Test provider switching
curl -X POST http://localhost/health/config/switching

# Check provider-specific logs
docker logs sora-worker-simple | grep -i "provider\|veo3\|azure"
```

**Resolution:**
1. Verify provider credentials:
   ```bash
   # Check Azure credentials
   env | grep AZURE_OPENAI
   
   # Check Google credentials
   env | grep GOOGLE_
   ```

2. Test provider connectivity:
   ```bash
   # Test Azure endpoint
   curl -H "api-key: $AZURE_OPENAI_API_KEY" "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"
   
   # Test Google Veo3 (if not using mock)
   python -c "
   from src.config.veo3_settings import validate_veo3_environment
   result = validate_veo3_environment()
   print(f'Veo3 validation: {result}')
   "
   ```

3. Reset provider configuration:
   ```bash
   src/deployment/scripts/f4_deployment.sh --force-provider-reset
   ```

#### 3. High Error Rates

**Symptoms:**
- Error rate > 5% in monitoring dashboard
- Frequent alerts
- Degraded system performance

**Diagnosis:**
```bash
# Check error metrics
curl http://localhost/monitoring/f4/metrics | jq '.data.current_state.error_rate'

# Get recent errors
curl http://localhost/monitoring/f4/metrics?history=true | jq '.data.metrics_history.errors[-10:]'

# Check application logs
docker logs sora-app-simple --tail 100 | grep ERROR
```

**Resolution:**
1. Identify error patterns in logs
2. Check resource usage: `docker stats`
3. Restart problematic services
4. Escalate if error rate remains high

#### 4. Performance Degradation

**Symptoms:**
- Slow response times
- High configuration load times
- Timeout errors

**Diagnosis:**
```bash
# Check performance metrics
curl http://localhost/monitoring/f4/performance

# Monitor resource usage
docker stats --no-stream

# Check database performance
docker exec sora-postgres-simple psql -U sora_user -d sora_production -c "
SELECT query, state, query_start 
FROM pg_stat_activity 
WHERE state = 'active' AND now() - query_start > interval '5 minutes';
"
```

**Resolution:**
1. Restart services to clear memory issues
2. Check disk space: `df -h`
3. Optimize database queries if needed
4. Scale resources if consistently high load

### Diagnostic Commands

#### System Health Check
```bash
#!/bin/bash
echo "=== F4 System Health Check ==="

echo "1. Service Status:"
docker ps | grep sora

echo "2. Configuration Health:"
curl -s http://localhost/health/config | jq '.status'

echo "3. Provider Status:"
curl -s http://localhost/health/config/providers | jq '.availability'

echo "4. Error Rate:"
curl -s http://localhost/monitoring/f4/metrics | jq '.data.current_state.error_rate'

echo "5. Recent Alerts:"
curl -s http://localhost/monitoring/f4/alerts | jq '.data.summary'

echo "6. Resource Usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

#### Configuration Validation
```bash
#!/bin/bash
echo "=== F4 Configuration Validation ==="

echo "1. Environment Variables:"
env | grep -E "(USE_MOCK_VEO|DEFAULT_PROVIDER|GOOGLE_|AZURE_)" | sort

echo "2. Configuration Loading Test:"
python -c "
from src.config.factory import ConfigurationFactory
try:
    config = ConfigurationFactory.get_base_config()
    veo3_config = ConfigurationFactory.create_veo3_config()
    print('✓ Configuration loading successful')
except Exception as e:
    print(f'✗ Configuration loading failed: {e}')
"

echo "3. Provider Validation:"
python src/deployment/scripts/f4_validation.py --environment production
```

---

## Emergency Procedures

### Critical System Down

**Immediate Actions (0-5 minutes):**
1. Check system status: `docker ps`
2. Restart all services: `docker-compose restart`
3. Verify health endpoints: `curl http://localhost/health`
4. Check logs for critical errors: `docker logs sora-app-simple`

**Short-term Resolution (5-15 minutes):**
1. Roll back to last known good configuration
2. Verify provider connectivity
3. Check resource availability
4. Escalate if not resolved

### Provider Outage

**When Azure Sora is Down:**
1. Switch default provider to Google Veo3:
   ```bash
   export DEFAULT_PROVIDER=google_veo3
   src/deployment/scripts/f4_deployment.sh --type production
   ```

2. Verify Veo3 availability:
   ```bash
   curl http://localhost/health/config/providers
   ```

**When Google Veo3 is Down:**
1. Switch to mock mode temporarily:
   ```bash
   export USE_MOCK_VEO=true
   src/deployment/scripts/f4_deployment.sh --type production
   ```

2. Switch default to Azure:
   ```bash
   export DEFAULT_PROVIDER=azure_sora
   ```

### Configuration Corruption

**Symptoms:**
- Services won't start
- Configuration validation fails
- Database connection errors

**Recovery Steps:**
1. Stop all services: `docker-compose down`
2. Restore from backup: `cp .env.backup.LATEST .env`
3. Validate configuration: `python src/deployment/scripts/f4_validation.py`
4. Start services: `docker-compose up -d`
5. Verify functionality: `curl http://localhost/health/config`

---

## Maintenance Tasks

### Daily Tasks

1. **Health Check Review**
   ```bash
   # Check system health
   curl http://localhost/health/config
   curl http://localhost/monitoring/f4/dashboard
   
   # Review alerts
   curl http://localhost/monitoring/f4/alerts
   ```

2. **Log Review**
   ```bash
   # Check for errors in the last 24 hours
   docker logs sora-app-simple --since 24h | grep ERROR
   docker logs sora-worker-simple --since 24h | grep ERROR
   ```

### Weekly Tasks

1. **Performance Review**
   ```bash
   # Generate performance report
   curl http://localhost/monitoring/f4/performance > weekly_performance_$(date +%Y%m%d).json
   
   # Check trends
   python src/deployment/scripts/f4_validation.py --output-format html --output-file weekly_health_$(date +%Y%m%d).html
   ```

2. **Configuration Backup**
   ```bash
   # Backup current configuration
   tar -czf config_backup_$(date +%Y%m%d).tar.gz .env src/deployment/config/
   
   # Store in backup location
   mv config_backup_*.tar.gz /backup/location/
   ```

### Monthly Tasks

1. **Security Review**
   ```bash
   # Check for security issues
   python src/deployment/scripts/f4_validation.py --environment production --include-credentials
   
   # Review access logs
   grep -i "security\|auth\|credential" /var/log/application.log
   ```

2. **Capacity Planning**
   ```bash
   # Generate capacity report
   curl http://localhost/monitoring/f4/metrics?detailed=true > monthly_metrics_$(date +%Y%m%d).json
   
   # Analyze resource usage trends
   docker stats --no-stream > monthly_resources_$(date +%Y%m%d).txt
   ```

3. **Dependency Updates**
   ```bash
   # Check for security updates
   uv show --outdated
   
   # Review and plan updates
   # Update in staging first, then production
   ```

---

## Provider Management

### Azure Sora Management

#### Configuration
```bash
# Required environment variables
export AZURE_OPENAI_API_KEY="your-api-key"
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com"
export AZURE_OPENAI_API_VERSION="2024-02-15-preview"
export AZURE_OPENAI_SORA_DEPLOYMENT="sora"
```

#### Health Checks
```bash
# Test Azure connectivity
curl -H "api-key: $AZURE_OPENAI_API_KEY" \
     "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"

# Test Sora deployment
curl -H "api-key: $AZURE_OPENAI_API_KEY" \
     "$AZURE_OPENAI_ENDPOINT/openai/deployments/$AZURE_OPENAI_SORA_DEPLOYMENT?api-version=$AZURE_OPENAI_API_VERSION"
```

#### Troubleshooting
- **401 Unauthorized**: Check API key
- **404 Not Found**: Verify endpoint URL and deployment name
- **429 Rate Limit**: Check quota and rate limits
- **503 Service Unavailable**: Azure service issues

### Google Veo3 Management

#### Configuration (Production)
```bash
# Required environment variables
export GOOGLE_PROJECT_ID="your-project-12345"
export GOOGLE_CLIENT_ID="your-client-id.apps.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="your-client-secret"
export USE_MOCK_VEO=false
```

#### Configuration (Development/Mock)
```bash
# Mock mode for development
export USE_MOCK_VEO=true
export GOOGLE_PROJECT_ID="mock-project-12345"
```

#### Health Checks
```bash
# Validate Veo3 configuration
python -c "
from src.config.veo3_settings import validate_veo3_environment
result = validate_veo3_environment()
print(f'Valid: {result[\"valid\"]}')
if result['errors']:
    print(f'Errors: {result[\"errors\"]}')
"

# Test provider switching to Veo3
curl -X POST http://localhost/health/config/switching
```

#### Service Account Setup
```bash
# Download service account key (production)
gcloud iam service-accounts keys create google-service-account.json \
    --iam-account=<EMAIL>

# Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/google-service-account.json"
```

### Provider Switching

#### Manual Provider Switch
```bash
# Switch to Azure Sora
export DEFAULT_PROVIDER=azure_sora
src/deployment/scripts/f4_deployment.sh --type production

# Switch to Google Veo3
export DEFAULT_PROVIDER=google_veo3
src/deployment/scripts/f4_deployment.sh --type production

# Enable mock mode
export USE_MOCK_VEO=true
src/deployment/scripts/f4_deployment.sh --type production
```

#### Automated Failover
The system automatically fails over between providers based on availability. Monitor failover events:

```bash
# Check provider availability
curl http://localhost/health/config/providers

# Monitor switching events
docker logs sora-worker-simple | grep "provider.*switch"
```

---

## Performance Optimization

### Configuration Performance

#### Optimize Configuration Loading
```bash
# Enable configuration caching
export CONFIG_CACHE_TTL=300  # 5 minutes

# Disable auto-reload in production
export CONFIG_AUTO_RELOAD=false

# Enable validation caching
export CONFIG_VALIDATION_ENABLED=true
```

#### Monitor Configuration Performance
```bash
# Check configuration load times
curl http://localhost/health/config/performance

# Get detailed performance metrics
curl http://localhost/monitoring/f4/performance?timeframe=24h
```

### Provider Performance

#### Optimize Provider Switching
```bash
# Tune provider timeouts
export VEO3_TIMEOUT=300
export VEO3_MAX_RETRIES=3
export VEO3_RETRY_DELAY=2

# Optimize rate limiting
export VEO3_RATE_LIMIT_RPM=30
```

#### Monitor Provider Performance
```bash
# Provider-specific performance
curl http://localhost/monitoring/f4/performance | jq '.data.provider_performance'

# Provider switching metrics
curl http://localhost/health/config/switching
```

### System Performance

#### Resource Optimization
```bash
# Optimize worker concurrency
export WORKER_CONCURRENCY=4
export WORKER_MAX_TASKS_PER_CHILD=100

# Tune memory settings
export CELERY_WORKER_MEMORY_LIMIT=1024  # MB
```

#### Database Performance
```bash
# Monitor database connections
docker exec sora-postgres-simple psql -U sora_user -d sora_production -c "
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE state = 'active';
"

# Check slow queries
docker exec sora-postgres-simple psql -U sora_user -d sora_production -c "
SELECT query, query_start, state 
FROM pg_stat_activity 
WHERE state = 'active' AND now() - query_start > interval '30 seconds';
"
```

---

## Contact Information

### Escalation Path

1. **Level 1**: Operations Team
   - Email: <EMAIL>
   - Slack: #ops-alerts
   - Phone: +1-xxx-xxx-xxxx

2. **Level 2**: Engineering Team
   - Email: <EMAIL>
   - Slack: #engineering-urgent
   - On-call: PagerDuty

3. **Level 3**: Architecture Team
   - Email: <EMAIL>
   - Slack: #architecture

### Emergency Contacts

- **On-Call Engineer**: Available 24/7 via PagerDuty
- **System Architect**: Available during business hours
- **Product Owner**: Available during business hours

---

## Appendix

### Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `USE_MOCK_VEO` | Use mock Veo3 API | `true` | Yes |
| `DEFAULT_PROVIDER` | Default provider | `azure_sora` | Yes |
| `GOOGLE_PROJECT_ID` | Google Cloud project ID | - | For real Veo3 |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | - | For real Veo3 |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | - | For real Veo3 |
| `VEO3_TIMEOUT` | Veo3 API timeout (seconds) | `300` | No |
| `CONFIG_VALIDATION_ENABLED` | Enable config validation | `true` | No |
| `DEPLOYMENT_TYPE` | Deployment type | `docker` | Yes |

### Useful Commands Reference

```bash
# Health checks
curl http://localhost/health/config
curl http://localhost/health/config/providers
curl http://localhost/monitoring/f4/metrics

# Deployment
src/deployment/scripts/f4_deployment.sh --type production
src/deployment/scripts/f4_deployment.sh rollback

# Validation
python src/deployment/scripts/f4_validation.py --environment production

# Docker management
docker-compose -f src/deployment/docker/docker-compose.simple.yml restart app worker
docker logs sora-app-simple --tail 100
docker stats --no-stream

# Configuration testing
python -c "from src.config.factory import ConfigurationFactory; print(ConfigurationFactory.get_provider_availability())"
```

### Log Locations

- **Application Logs**: `docker logs sora-app-simple`
- **Worker Logs**: `docker logs sora-worker-simple`
- **Database Logs**: `docker logs sora-postgres-simple`
- **F4 Monitor Logs**: `/tmp/f4_config_monitor.log`
- **Deployment Logs**: `/tmp/f4-deployment-*.log`

---

**Document Version**: 1.0
**Last Updated**: 2025-07-27
**Next Review**: 2025-08-27