# F4 Configuration Troubleshooting Guide

## Quick Reference

**Most Common Issues:**
1. [Provider switching failures](#provider-switching-failures)
2. [Configuration loading errors](#configuration-loading-errors)
3. [Google Veo3 authentication issues](#google-veo3-authentication-issues)
4. [Performance degradation](#performance-degradation)
5. [Environment variable issues](#environment-variable-issues)

---

## Problem Categories

### Provider Switching Failures

#### Symptom: "No providers available" Error

**Error Messages:**
- `No video generation providers are available`
- `Provider switching failed`
- `Default provider not available`

**Quick Diagnosis:**
```bash
# Check provider status
curl http://localhost/health/config/providers

# Check configuration
curl http://localhost/health/config/environment
```

**Common Causes & Solutions:**

**Cause 1: Missing Azure Credentials**
```bash
# Check Azure variables
env | grep AZURE_OPENAI

# Expected output:
# AZURE_OPENAI_API_KEY=sk-...
# AZURE_OPENAI_ENDPOINT=https://....openai.azure.com
# AZURE_OPENAI_API_VERSION=2024-02-15-preview
# AZURE_OPENAI_SORA_DEPLOYMENT=sora
```

*Solution:*
```bash
# Set missing variables
export AZURE_OPENAI_API_KEY="your-api-key"
export AZURE_OPENAI_ENDPOINT="your-endpoint"
export AZURE_OPENAI_API_VERSION="2024-02-15-preview"
export AZURE_OPENAI_SORA_DEPLOYMENT="sora"

# Restart services
docker-compose restart app worker
```

**Cause 2: Google Veo3 Configuration Issues**
```bash
# Check Google variables
env | grep GOOGLE_

# For production (real API):
# GOOGLE_PROJECT_ID=your-project-12345
# GOOGLE_CLIENT_ID=...apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=...
# USE_MOCK_VEO=false

# For development (mock):
# USE_MOCK_VEO=true
```

*Solution for Production:*
```bash
# Set real Google credentials
export GOOGLE_PROJECT_ID="your-project-12345"
export GOOGLE_CLIENT_ID="your-client-id.apps.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="your-client-secret"
export USE_MOCK_VEO=false
```

*Solution for Development:*
```bash
# Use mock mode
export USE_MOCK_VEO=true
export GOOGLE_PROJECT_ID="mock-project-12345"
```

**Cause 3: Invalid Default Provider**
```bash
# Check default provider setting
echo $DEFAULT_PROVIDER

# Valid values: azure_sora, google_veo3
```

*Solution:*
```bash
# Set valid default provider
export DEFAULT_PROVIDER=azure_sora  # or google_veo3
```

#### Symptom: Provider Switch Timeout

**Error Messages:**
- `Provider switch timeout`
- `Configuration creation failed`
- `Provider switch time exceeds threshold`

**Diagnosis:**
```bash
# Test provider switching performance
time curl http://localhost/health/config/switching

# Check performance metrics
curl http://localhost/monitoring/f4/performance
```

**Solutions:**

1. **Increase Timeout Values:**
```bash
export VEO3_TIMEOUT=600  # Increase from 300
export VEO3_MAX_RETRIES=5  # Increase from 3
```

2. **Check Network Connectivity:**
```bash
# Test Azure connectivity
curl -H "api-key: $AZURE_OPENAI_API_KEY" "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"

# Test Google connectivity (if using real API)
ping googleapis.com
```

3. **Restart Services:**
```bash
docker-compose restart app worker
```

### Configuration Loading Errors

#### Symptom: "Configuration factory failed"

**Error Messages:**
- `Configuration factory creation failed`
- `BaseConfig loading failed`
- `Veo3Settings validation failed`

**Diagnosis:**
```bash
# Test configuration loading
python -c "
from src.config.factory import ConfigurationFactory
try:
    config = ConfigurationFactory.get_base_config()
    print('✓ Base config loaded')
    veo3_config = ConfigurationFactory.create_veo3_config()
    print('✓ Veo3 config created')
except Exception as e:
    print(f'✗ Error: {e}')
"
```

**Common Solutions:**

1. **Check Environment File:**
```bash
# Verify .env file exists and is readable
ls -la .env
cat .env | head -10

# Check file permissions
chmod 600 .env
```

2. **Validate Required Variables:**
```bash
# Run full validation
python src/deployment/scripts/f4_validation.py --environment production --verbose
```

3. **Reset Configuration Cache:**
```bash
# Clear any cached config
export CONFIG_AUTO_RELOAD=true
docker-compose restart app worker
```

#### Symptom: "Environment variable missing"

**Error Messages:**
- `USE_MOCK_VEO not set`
- `DEFAULT_PROVIDER not set`
- `DEPLOYMENT_TYPE not set`

**Quick Fix:**
```bash
# Set missing critical variables
export USE_MOCK_VEO=true
export DEFAULT_PROVIDER=azure_sora
export DEPLOYMENT_TYPE=docker

# Verify all critical variables are set
env | grep -E "(USE_MOCK_VEO|DEFAULT_PROVIDER|DEPLOYMENT_TYPE)"
```

### Google Veo3 Authentication Issues

#### Symptom: "Google Cloud authentication failed"

**Error Messages:**
- `GOOGLE_PROJECT_ID required`
- `Google Cloud credentials invalid`
- `Service account authentication failed`

**Diagnosis:**
```bash
# Check Veo3 environment validation
python -c "
from src.config.veo3_settings import validate_veo3_environment
result = validate_veo3_environment()
print(f'Valid: {result[\"valid\"]}')
print(f'Errors: {result.get(\"errors\", [])}')
"
```

**Solutions by Authentication Method:**

**OAuth Client Credentials:**
```bash
# Required variables
export GOOGLE_PROJECT_ID="your-project-12345"
export GOOGLE_CLIENT_ID="your-client.apps.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="your-client-secret"
export USE_MOCK_VEO=false

# Test OAuth setup
gcloud auth application-default login
```

**Service Account:**
```bash
# Download service account key
gcloud iam service-accounts keys create google-service-account.json \
    --iam-account=<EMAIL>

# Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/google-service-account.json"

# Verify service account
gcloud auth list
```

**Docker Service Account Mount:**
```bash
# For Docker deployment, mount service account
# In docker-compose.yml:
secrets:
  google_service_account:
    file: /opt/sora/secrets/google-service-account.json

# In container environment:
export GOOGLE_SERVICE_ACCOUNT_FILE=/run/secrets/google_service_account
```

**Mock Mode (Development):**
```bash
# Use mock for development/testing
export USE_MOCK_VEO=true
export GOOGLE_PROJECT_ID="mock-project-12345"
# Other Google variables can be unset in mock mode
```

### Performance Degradation

#### Symptom: "Configuration load time exceeds threshold"

**Error Messages:**
- `Config load time (5234ms) exceeds threshold (5000ms)`
- `Provider switch time exceeds threshold`
- `Health check timeout`

**Diagnosis:**
```bash
# Check current performance
curl http://localhost/monitoring/f4/performance

# Monitor real-time performance
watch -n 5 'curl -s http://localhost/health/config/performance | jq .performance_metrics'
```

**Solutions:**

1. **Optimize Configuration Loading:**
```bash
# Enable caching
export CONFIG_CACHE_TTL=300  # 5 minutes
export CONFIG_AUTO_RELOAD=false

# Disable validation in production
export CONFIG_VALIDATION_ENABLED=false  # Only if performance critical
```

2. **Check System Resources:**
```bash
# Check Docker resource usage
docker stats --no-stream

# Check disk space
df -h

# Check memory usage
free -h
```

3. **Database Performance:**
```bash
# Check active connections
docker exec sora-postgres-simple psql -U sora_user -d sora_production -c "
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"

# Check for long-running queries
docker exec sora-postgres-simple psql -U sora_user -d sora_production -c "
SELECT query, state, query_start FROM pg_stat_activity 
WHERE state = 'active' AND now() - query_start > interval '30 seconds';"
```

4. **Restart Services:**
```bash
# Restart containers to clear memory issues
docker-compose restart app worker

# Or restart specific container
docker restart sora-app-simple
```

### Environment Variable Issues

#### Symptom: "Environment variable validation failed"

**Common Variable Issues:**

**USE_MOCK_VEO:**
```bash
# Must be exactly "true" or "false"
export USE_MOCK_VEO=true   # ✓ Correct
export USE_MOCK_VEO=True   # ✗ Incorrect (case sensitive)
export USE_MOCK_VEO=1      # ✗ Incorrect (not boolean string)
```

**DEFAULT_PROVIDER:**
```bash
# Must be exactly one of these values
export DEFAULT_PROVIDER=azure_sora   # ✓ Correct
export DEFAULT_PROVIDER=google_veo3  # ✓ Correct
export DEFAULT_PROVIDER=Azure_Sora   # ✗ Incorrect (case sensitive)
export DEFAULT_PROVIDER=veo3         # ✗ Incorrect (wrong value)
```

**DEPLOYMENT_TYPE:**
```bash
# Must be exactly one of these values
export DEPLOYMENT_TYPE=local      # ✓ Correct
export DEPLOYMENT_TYPE=docker     # ✓ Correct
export DEPLOYMENT_TYPE=production # ✓ Correct
export DEPLOYMENT_TYPE=staging    # ✗ Use production for staging deploy
```

**Google Project ID Format:**
```bash
# Must be valid Google Cloud project ID format
export GOOGLE_PROJECT_ID=my-project-12345    # ✓ Correct
export GOOGLE_PROJECT_ID=MyProject12345      # ✗ No uppercase
export GOOGLE_PROJECT_ID=my_project_12345    # ✗ No underscores
export GOOGLE_PROJECT_ID=my-project          # ✓ Correct (no numbers required)
```

**Validation Script:**
```bash
# Validate all environment variables
python src/deployment/scripts/f4_validation.py --environment production --verbose

# Quick variable check
python -c "
import os
required = ['USE_MOCK_VEO', 'DEFAULT_PROVIDER', 'DEPLOYMENT_TYPE']
for var in required:
    value = os.getenv(var)
    status = '✓' if value else '✗'
    print(f'{status} {var}: {value}')
"
```

---

## Advanced Troubleshooting

### Debug Mode

#### Enable Debug Logging
```bash
# Set debug environment variables
export DEBUG=true
export LOG_LEVEL=DEBUG
export CONFIG_DEBUG_MODE=true

# Restart services
docker-compose restart app worker

# Check debug logs
docker logs sora-app-simple | tail -50
```

#### Debug Configuration Loading
```bash
# Test step by step
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)

from src.config.service import ConfigurationService
print('1. ConfigurationService loaded')

from src.config.environments import get_config
config = get_config()
print('2. Base config loaded')

from src.config.veo3_settings import get_cached_veo3_settings
veo3_settings = get_cached_veo3_settings()
print('3. Veo3 settings loaded')

from src.config.factory import ConfigurationFactory
availability = ConfigurationFactory.get_provider_availability()
print(f'4. Provider availability: {availability}')
"
```

### Health Check Deep Dive

#### Detailed Health Analysis
```bash
# Get comprehensive health data
curl http://localhost/health/config | jq '.'

# Get provider-specific details
curl http://localhost/health/config/providers | jq '.validations'

# Get performance breakdown
curl http://localhost/health/config/performance?detailed=true | jq '.'
```

#### Monitor Health Changes
```bash
# Watch health status changes
watch -n 10 'curl -s http://localhost/health/config | jq ".status, .timestamp"'

# Log health changes
while true; do
    status=$(curl -s http://localhost/health/config | jq -r .status)
    echo "$(date): $status"
    sleep 30
done > health_monitoring.log
```

### Network Connectivity Issues

#### Test External API Connectivity
```bash
# Test Azure OpenAI connectivity
curl -v -H "api-key: $AZURE_OPENAI_API_KEY" \
     "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"

# Test Google APIs connectivity
curl -v https://googleapis.com

# Test DNS resolution
nslookup $AZURE_OPENAI_ENDPOINT
nslookup googleapis.com
```

#### Container Network Issues
```bash
# Check container network
docker network ls
docker network inspect sora-network

# Test container-to-container connectivity
docker exec sora-app-simple ping sora-postgres-simple
docker exec sora-app-simple ping sora-redis-simple

# Check port bindings
docker port sora-app-simple
docker port sora-worker-simple
```

---

## Emergency Recovery Procedures

### Complete System Recovery

**When everything is broken:**

1. **Stop all services:**
```bash
docker-compose down
```

2. **Restore from backup:**
```bash
# Restore environment file
cp .env.backup.LATEST .env

# Restore Docker configuration if changed
git checkout src/deployment/docker/docker-compose.simple.yml
```

3. **Reset to known good state:**
```bash
# Use local development config as baseline
cp src/deployment/config/f4-environment-local.env .env

# Modify for your environment
export USE_MOCK_VEO=true
export DEFAULT_PROVIDER=azure_sora
export DEPLOYMENT_TYPE=docker
```

4. **Start services:**
```bash
docker-compose up -d
```

5. **Verify recovery:**
```bash
# Wait for services to start
sleep 30

# Check health
curl http://localhost/health
curl http://localhost/health/config
```

### Provider Failover

**When primary provider fails:**

1. **Switch to backup provider:**
```bash
# If Azure fails, switch to Veo3 mock
export DEFAULT_PROVIDER=google_veo3
export USE_MOCK_VEO=true

# If Veo3 fails, switch to Azure
export DEFAULT_PROVIDER=azure_sora
```

2. **Deploy switch:**
```bash
src/deployment/scripts/f4_deployment.sh --type production
```

3. **Verify switch:**
```bash
curl http://localhost/health/config/providers
```

---

## Getting Help

### Self-Service Resources

1. **Validation Script**: `python src/deployment/scripts/f4_validation.py`
2. **Health Endpoints**: `http://localhost/health/config/*`
3. **Monitoring Dashboard**: `http://localhost/monitoring/f4/dashboard`
4. **Operations Runbook**: `src/deployment/runbooks/f4_operations_runbook.md`

### Log Analysis

**Key Log Patterns to Look For:**

```bash
# Configuration errors
docker logs sora-app-simple 2>&1 | grep -i "config.*error\|factory.*failed"

# Provider issues
docker logs sora-worker-simple 2>&1 | grep -i "provider.*error\|veo3.*failed\|azure.*error"

# Performance issues
docker logs sora-app-simple 2>&1 | grep -i "timeout\|slow\|performance"

# Authentication issues
docker logs sora-app-simple 2>&1 | grep -i "auth.*failed\|credential.*error\|permission"
```

### Escalation Information

**Before escalating, gather:**

1. **Error messages** from logs
2. **Health check outputs**: 
   - `curl http://localhost/health/config`
   - `curl http://localhost/health/config/providers`
3. **Environment validation**:
   - `python src/deployment/scripts/f4_validation.py`
4. **System status**:
   - `docker ps`
   - `docker stats --no-stream`

**Contact Information:**
- Level 1 Support: <EMAIL>
- Engineering Team: <EMAIL>
- On-call: PagerDuty escalation

---

## Common Error Codes

| Error Code | Meaning | Common Cause | Quick Fix |
|------------|---------|--------------|-----------|
| HTTP 500 | Internal Server Error | Configuration loading failed | Check environment variables |
| HTTP 503 | Service Unavailable | No providers available | Check provider credentials |
| HTTP 404 | Not Found | Endpoint not registered | Restart app container |
| HTTP 400 | Bad Request | Invalid configuration | Validate configuration |

---

**Last Updated**: 2025-07-27
**Version**: 1.0