# C3 Provider-Aware Job Queue Operational Runbook

## Overview

This runbook provides comprehensive operational guidance for the C3 provider-aware job queue system, covering deployment, monitoring, troubleshooting, and maintenance procedures for the dual-provider video generation infrastructure.

## System Architecture

### Provider-Aware Components
- **Deployment Manager**: `src/deployment/c3_deployment_manager.py`
- **Provider Monitor**: `src/monitoring/c3_provider_monitor.py`
- **Operations Automation**: `src/deployment/c3_operations_automation.py`
- **Production Readiness Validator**: `src/deployment/c3_production_readiness.py`

### Dual-Provider Support
- **Azure Sora**: Production video generation provider
- **Google Veo3**: Mock/development provider with image-to-video specialty

## Pre-Deployment Checklist

### Environment Preparation
```bash
# 1. Verify environment variables
export AZURE_OPENAI_API_KEY="your_azure_key"
export AZURE_OPENAI_ENDPOINT="https://your-endpoint.openai.azure.com/"
export DATABASE_URL="********************************/db"
export REDIS_URL="redis://localhost:6379/0"
export CELERY_BROKER_URL="redis://localhost:6379/0"

# 2. Validate provider configurations
python -c "
from src.features.video_generation.provider_factory import get_provider_factory
factory = get_provider_factory()
print('Available providers:', factory.get_available_providers())
"

# 3. Check database connectivity
python -c "
from src.database.connection import get_db_session
with get_db_session() as session:
    result = session.execute('SELECT 1').scalar()
    print('Database connection:', 'OK' if result == 1 else 'FAILED')
"
```

### Production Readiness Validation
```bash
# Run comprehensive production readiness validation
python src/deployment/c3_production_readiness.py \
    --level comprehensive \
    --output markdown \
    --output-file validation_report.md

# Quick validation for critical components only
python src/deployment/c3_production_readiness.py \
    --quick \
    --output json
```

## Deployment Procedures

### Automated Deployment
```bash
# Standard production deployment
python src/deployment/c3_deployment_manager.py \
    --environment production \
    --strategy rolling \
    --providers azure_sora google_veo3

# Development deployment
python src/deployment/c3_deployment_manager.py \
    --environment development \
    --strategy rolling \
    --no-rollback \
    --skip-backup
```

### Manual Deployment Steps
```bash
# 1. Pre-deployment backup
python -c "
import asyncio
from src.features.video_generation.orchestration.core import ProviderOperationsManager
from src.features.video_generation.orchestration.core.operations_manager import OperationsConfig
automation_config = OperationsConfig(enable_automated_backup=True)
automation = ProviderOperationsManager(automation_config)
asyncio.run(automation.manual_backup())
"

# 2. Database migrations
flask db upgrade

# 3. Provider configuration validation
python -c "
import asyncio
from src.job_queue.provider_integration import get_health_monitor
monitor = get_health_monitor()
health = asyncio.run(monitor.check_all_providers_health())
for provider, status in health.items():
    print(f'{provider}: {status[\"status\"]}')
"

# 4. Worker deployment
docker-compose up -d celery-worker-azure celery-worker-veo3

# 5. Service deployment
docker-compose up -d sora-app nginx-lb
```

### Rollback Procedures
```bash
# Automatic rollback (if deployment fails)
# Rollback is triggered automatically by deployment manager

# Manual rollback
python -c "
import asyncio
from src.features.video_generation.orchestration.core import ProviderOrchestrationManager
from src.features.video_generation.orchestration.core.deployment_manager import DeploymentConfig
deployment_config = DeploymentConfig(deployment_id='production_deploy')
manager = ProviderOrchestrationManager(deployment_config)
# Restore from backup timestamp
"

# Emergency rollback
docker-compose down
docker-compose -f docker-compose.production.yml up -d
```

## Monitoring and Alerting

### Start Provider Monitoring
```bash
# Start comprehensive provider monitoring
python src/monitoring/c3_provider_monitor.py \
    --providers azure_sora google_veo3 \
    --interval 30

# Monitor specific provider
python -c "
from src.features.video_generation.orchestration.core import ProviderMonitoringService
from src.features.video_generation.orchestration.core.provider_monitor import MonitoringConfig
monitor_config = MonitoringConfig(service_id='production_monitor', providers=['azure_sora'])
monitor = ProviderMonitoringService(monitor_config)
asyncio.run(monitor.start_monitoring())
"
```

### Key Metrics to Monitor

#### Provider Health Metrics
- **Response Time**: `response_time_ms` (threshold: <2000ms)
- **Success Rate**: `success_rate_percent` (threshold: >95%)
- **Queue Depth**: `queue_depth` (threshold: <50 jobs)
- **Error Rate**: `error_rate_percent` (threshold: <5%)

#### System Metrics
- **Active Workers**: Monitor worker count per provider
- **Memory Usage**: System and application memory
- **Database Connections**: Connection pool utilization
- **Redis Memory**: Cache memory usage

### Alert Thresholds
```python
# Alert configuration (from provider_monitor.py)
alert_thresholds = {
    "response_time_ms": {
        "warning": 2000,    # 2 seconds
        "critical": 5000,   # 5 seconds  
        "emergency": 10000  # 10 seconds
    },
    "queue_depth": {
        "warning": 50,
        "critical": 100,
        "emergency": 200
    },
    "error_rate_percent": {
        "warning": 5.0,
        "critical": 10.0,
        "emergency": 25.0
    },
    "success_rate_percent": {
        "warning": 95.0,
        "critical": 90.0,
        "emergency": 80.0
    }
}
```

### Monitoring Dashboard Access
- **Grafana**: http://localhost:3000 (admin/admin)
- **Flower (Celery)**: http://localhost:5555
- **Provider Monitor Status**: `curl http://localhost:5001/c3/monitor/status`

## Operations Automation

### Start Automated Operations
```bash
# Start full operations automation
python src/deployment/c3_operations_automation.py --start

# Check automation status
python src/deployment/c3_operations_automation.py --status

# Manual operations
python src/deployment/c3_operations_automation.py \
    --scale azure_sora 5 "increased_load"

python src/deployment/c3_operations_automation.py \
    --failover google_veo3 "provider_unhealthy"

python src/deployment/c3_operations_automation.py \
    --backup "scheduled_backup"
```

### Automatic Scaling Rules
- **Scale Up**: Queue depth > 50 jobs
- **Scale Down**: Queue depth < 12 jobs (25% of threshold)
- **Cooldown**: 5 minutes between scaling actions
- **Limits**: 2-10 workers per provider

### Failover Triggers
- **Consecutive Failures**: 5 failed health checks
- **Response Time**: >10 seconds consistently
- **Error Rate**: >25% error rate
- **Manual Trigger**: Operator intervention

## Troubleshooting Guide

### Common Issues

#### Provider Unavailable
```bash
# 1. Check provider health
python -c "
import asyncio
from src.job_queue.provider_integration import get_health_monitor
monitor = get_health_monitor()
health = asyncio.run(monitor._check_single_provider_health('azure_sora'))
print(f'Status: {health.status.value}')
print(f'Checks: {health.checks}')
"

# 2. Verify configuration
python -c "
from src.features.video_generation.provider_factory import get_provider_factory
factory = get_provider_factory()
try:
    provider = factory.create_provider('azure_sora')
    print('Provider created successfully')
except Exception as e:
    print(f'Provider creation failed: {e}')
"

# 3. Test connectivity
curl -H "Authorization: Bearer $AZURE_OPENAI_API_KEY" \
    "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=2024-02-01"
```

#### High Queue Depth
```bash
# 1. Check queue status
python -c "
from src.job_queue.manager import QueueManager
qm = QueueManager()
stats = qm.get_provider_queue_statistics()
for provider, data in stats['providers'].items():
    print(f'{provider}: {data[\"queue_size\"]} pending')
"

# 2. Scale workers manually
python src/deployment/c3_operations_automation.py \
    --scale azure_sora 8 "high_queue_depth"

# 3. Check worker status
docker-compose ps | grep celery
celery -A src.job_queue.celery_app inspect active
```

#### Failed Jobs
```bash
# 1. Check failed job details
python -c "
from src.job_queue.provider_status_tracker import get_status_tracker
tracker = get_status_tracker()
stats = tracker.get_provider_job_statistics('azure_sora')
print(f'Failed jobs: {stats[\"failed_jobs\"]}')
print(f'Success rate: {stats[\"success_rate\"]}%')
"

# 2. Check Celery logs
docker-compose logs celery-worker-azure

# 3. Inspect specific job
python -c "
from src.api.job_repository import JobRepository
repo = JobRepository()
jobs = repo.get_jobs_by_status('failed')
for job in jobs[-5:]:  # Last 5 failed jobs
    print(f'Job {job.id}: {job.error_message}')
"
```

#### Database Issues
```bash
# 1. Check database connectivity
python -c "
from src.monitoring.health_check import HealthCheck
from src.database.connection import get_db_session
from redis import Redis
health = HealthCheck(get_db_session, Redis())
db_health = health._check_database_health()
print(f'Database: {db_health.status.value} - {db_health.message}')
"

# 2. Check connection pool
python -c "
from src.database.connection import get_db_session
with get_db_session() as session:
    result = session.execute('SELECT COUNT(*) FROM pg_stat_activity')
    print(f'Active connections: {result.scalar()}')
"

# 3. Run database maintenance
python -c "
import asyncio
from src.features.video_generation.orchestration.core import ProviderOperationsManager
from src.features.video_generation.orchestration.core.operations_manager import OperationsConfig
automation_config = OperationsConfig(enable_automated_maintenance=True)
automation = ProviderOperationsManager(automation_config)
# Note: Database maintenance is handled through maintenance manager module
"
```

### Performance Issues

#### Slow Response Times
```bash
# 1. Check system resources
python -c "
import psutil
print(f'CPU: {psutil.cpu_percent()}%')
print(f'Memory: {psutil.virtual_memory().percent}%')
print(f'Disk: {psutil.disk_usage(\"/\").percent}%')
"

# 2. Check provider response times
python -c "
from src.monitoring.c3_provider_monitor import get_c3_monitor
monitor = get_c3_monitor()
status = monitor.get_all_provider_status()
for provider, health in status.items():
    print(f'{provider}: {health.response_time_ms}ms')
"

# 3. Scale resources
docker-compose up -d --scale celery-worker-azure=4 --scale celery-worker-veo3=6
```

#### Memory Issues
```bash
# 1. Check Redis memory
redis-cli info memory

# 2. Check application memory
docker stats --no-stream

# 3. Clear caches
python -c "
from src.monitoring.c3_provider_monitor import get_c3_monitor
monitor = get_c3_monitor()
monitor._cleanup_old_metrics()
"
```

## Maintenance Procedures

### Scheduled Maintenance
```bash
# Maintenance window: 02:00-04:00 UTC daily (automatic)
# Manual maintenance trigger:
python -c "
import asyncio
from src.features.video_generation.orchestration.core import ProviderOperationsManager
from src.features.video_generation.orchestration.core.operations_manager import OperationsConfig
automation_config = OperationsConfig(enable_automated_maintenance=True)
automation = ProviderOperationsManager(automation_config)
# Maintenance is handled through automated cycle
"
```

### Backup Procedures
```bash
# Automatic backup every 6 hours
# Manual backup:
python src/deployment/c3_operations_automation.py \
    --backup "manual_backup_$(date +%Y%m%d_%H%M%S)"

# Verify backup
ls -la /opt/sora/backups/
```

### Update Procedures
```bash
# 1. Run production readiness validation
python src/deployment/c3_production_readiness.py --level standard

# 2. Create pre-update backup
python src/deployment/c3_operations_automation.py --backup "pre_update"

# 3. Deploy with rolling strategy
python src/deployment/c3_deployment_manager.py \
    --environment production \
    --strategy rolling

# 4. Validate deployment
python src/deployment/c3_production_readiness.py --quick
```

### Security Maintenance
```bash
# 1. Run security scan
python -c "
import asyncio
from src.features.video_generation.orchestration.core import ProviderOperationsManager
from src.features.video_generation.orchestration.core.operations_manager import OperationsConfig
automation_config = OperationsConfig()
automation = ProviderOperationsManager(automation_config)
# Security scans are integrated into health checks and monitoring
"

# 2. Update dependencies
uv update
uv run pip-audit

# 3. Check for exposed secrets
python -c "
import os
sensitive = ['AZURE_OPENAI_API_KEY', 'DATABASE_URL']
for var in sensitive:
    value = os.environ.get(var, '')
    if value and value in ['test', 'example', 'your_key_here']:
        print(f'WARNING: {var} appears to have default value')
"
```

## Emergency Procedures

### System Down
1. **Check overall health**:
   ```bash
   curl -f http://localhost:8090/health || echo "System down"
   ```

2. **Restart services**:
   ```bash
   docker-compose restart sora-app nginx-lb
   ```

3. **Check logs**:
   ```bash
   docker-compose logs --tail=100 sora-app
   ```

### Provider Failure
1. **Trigger manual failover**:
   ```bash
   python src/deployment/c3_operations_automation.py \
       --failover azure_sora "emergency_failover"
   ```

2. **Check fallback provider**:
   ```bash
   python -c "
   from src.monitoring.c3_provider_monitor import get_c3_monitor
   monitor = get_c3_monitor()
   status = monitor.get_provider_status('google_veo3')
   print(f'Fallback status: {status.status.value}')
   "
   ```

### Database Emergency
1. **Switch to read-only mode** (if supported)
2. **Restore from backup**:
   ```bash
   # Find latest backup
   ls -la /opt/sora/backups/ | tail -5
   
   # Restore (placeholder - implement based on backup format)
   # pg_restore -d database_name backup_file.sql
   ```

### High Load Emergency
1. **Scale immediately**:
   ```bash
   docker-compose up -d --scale celery-worker-azure=8 --scale celery-worker-veo3=10
   ```

2. **Enable rate limiting**:
   ```bash
   # Update rate limits in Redis
   redis-cli SET rate_limit:global 1000
   ```

3. **Monitor queue depth**:
   ```bash
   watch -n 5 'python -c "
   from src.job_queue.manager import QueueManager
   qm = QueueManager()
   stats = qm.get_queue_stats()
   print(f\"Queue: {stats[\"pending_jobs\"]} pending, {stats[\"active_jobs\"]} active\")
   "'
   ```

## Performance Tuning

### Worker Optimization
```bash
# Optimal worker configuration per provider:
# Azure Sora: 3-5 workers (API rate limited)
# Google Veo3: 5-8 workers (mock provider, higher throughput)

# Update worker scaling limits
python -c "
from src.features.video_generation.orchestration.core import ProviderOperationsManager
from src.features.video_generation.orchestration.core.operations_manager import OperationsConfig
config = OperationsConfig(
    max_concurrent_operations=8,
    enable_automated_scaling=True
)
automation = ProviderOperationsManager(config)
"
```

### Database Optimization
```bash
# Connection pool tuning
export DATABASE_POOL_SIZE=20
export DATABASE_MAX_OVERFLOW=30

# Query optimization
EXPLAIN ANALYZE SELECT * FROM video_jobs WHERE status = 'running';
```

### Redis Optimization
```bash
# Memory optimization
redis-cli CONFIG SET maxmemory 4gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# Persistence tuning
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

## Monitoring Alerts Configuration

### Critical Alerts (Immediate Response)
- System health status = "unhealthy"
- Provider response time > 10 seconds
- Queue depth > 200 jobs
- Error rate > 25%
- Database connectivity failure

### Warning Alerts (Response within 1 hour)
- Provider response time > 2 seconds
- Queue depth > 50 jobs
- Error rate > 5%
- Success rate < 95%
- High memory usage (>80%)

### Info Alerts (Daily review)
- Auto-scaling events
- Maintenance completions
- Backup completions
- Performance trend changes

## Contact Information

### Escalation Path
1. **Level 1**: DevOps Engineer
2. **Level 2**: Senior DevOps Engineer
3. **Level 3**: Platform Engineering Team
4. **Level 4**: CTO

### Key Contacts
- **Primary On-Call**: DevOps rotation
- **Backup On-Call**: Senior DevOps Engineer
- **Product Owner**: Platform Product Manager
- **Technical Lead**: Senior Software Engineer

## Documentation Links

### Technical Documentation
- [C3 Architecture Overview](../../../CLAUDE.md)
- [Provider Integration Guide](../../../src/job_queue/C3_PROVIDER_AWARE_DOCUMENTATION.md)
- [Deployment Guide](../../../src/deployment/CLAUDE.md)
- [Monitoring Setup](../../../src/monitoring/CLAUDE.md)

### Operational Resources
- [Load Testing Guide](../../../docs/LOAD_TESTING_GUIDE.md)
- [Security Checklist](../security/production-readiness-checklist.md)
- [Troubleshooting Guide](../../../docs/TROUBLESHOOTING_GUIDE.md)

---

**Last Updated**: $(date)
**Version**: 1.0.0
**Owner**: DevOps Team
**Review Schedule**: Monthly