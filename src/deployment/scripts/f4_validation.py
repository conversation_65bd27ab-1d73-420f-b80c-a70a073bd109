#!/usr/bin/env python3
"""
F4 Configuration Validation Script

Comprehensive validation script for F4 environment configuration,
provider setup, and deployment readiness verification.

This script provides:
- Deep F4 configuration validation
- Provider availability and credential testing
- Environment variable completeness checking
- Integration validation with dependent services
- Performance and security validation
- Deployment readiness assessment

Usage:
    python f4_validation.py [options]
    ./f4_validation.py --environment production

Options:
    --environment ENV: Environment to validate (development/staging/production)
    --config-file FILE: F4 configuration file to validate
    --provider PROVIDER: Test specific provider (azure_sora/google_veo3)
    --include-credentials: Test actual API credentials (security risk)
    --output-format FORMAT: Output format (text/json/html)
    --verbose: Enable verbose output
    --fix-issues: Attempt to fix common configuration issues
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import get_cached_veo3_settings, validate_veo3_environment

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class F4ValidationResult:
    """Container for F4 validation results."""

    def __init__(self):
        self.overall_status = "unknown"
        self.validation_timestamp = datetime.now(timezone.utc)
        self.checks = {}
        self.errors = []
        self.warnings = []
        self.recommendations = []
        self.performance_metrics = {}
        self.security_issues = []

    def add_check(self, check_name: str, status: str, details: Dict[str, Any]) -> None:
        """Add a validation check result."""
        self.checks[check_name] = {
            "status": status,
            "details": details,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    def add_error(self, error: str) -> None:
        """Add an error to the validation result."""
        self.errors.append(error)

    def add_warning(self, warning: str) -> None:
        """Add a warning to the validation result."""
        self.warnings.append(warning)

    def add_recommendation(self, recommendation: str) -> None:
        """Add a recommendation to the validation result."""
        self.recommendations.append(recommendation)

    def add_security_issue(self, issue: str, severity: str = "medium") -> None:
        """Add a security issue to the validation result."""
        self.security_issues.append(
            {
                "issue": issue,
                "severity": severity,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    def determine_overall_status(self) -> str:
        """Determine overall validation status from individual checks."""
        statuses = [check["status"] for check in self.checks.values()]

        if "critical" in statuses or "error" in statuses:
            return "failed"
        elif "warning" in statuses or self.warnings or self.security_issues:
            return "warning"
        elif all(status == "passed" for status in statuses):
            return "passed"
        else:
            return "unknown"

    def to_dict(self) -> Dict[str, Any]:
        """Convert validation result to dictionary."""
        return {
            "overall_status": self.overall_status,
            "validation_timestamp": self.validation_timestamp.isoformat(),
            "summary": {
                "total_checks": len(self.checks),
                "passed_checks": len(
                    [c for c in self.checks.values() if c["status"] == "passed"]
                ),
                "failed_checks": len(
                    [
                        c
                        for c in self.checks.values()
                        if c["status"] in ["error", "critical", "failed"]
                    ]
                ),
                "warning_checks": len(
                    [c for c in self.checks.values() if c["status"] == "warning"]
                ),
                "total_errors": len(self.errors),
                "total_warnings": len(self.warnings),
                "security_issues": len(self.security_issues),
            },
            "checks": self.checks,
            "errors": self.errors,
            "warnings": self.warnings,
            "recommendations": self.recommendations,
            "security_issues": self.security_issues,
            "performance_metrics": self.performance_metrics,
        }


class F4ConfigurationValidator:
    """Comprehensive F4 configuration validator."""

    def __init__(
        self,
        environment: str = "production",
        config_file: Optional[str] = None,
        include_credentials: bool = False,
        verbose: bool = False,
    ):
        """
        Initialize F4 configuration validator.

        Args:
            environment: Environment to validate
            config_file: F4 configuration file path
            include_credentials: Test actual API credentials
            verbose: Enable verbose output
        """
        self.environment = environment
        self.config_file = config_file
        self.include_credentials = include_credentials
        self.verbose = verbose
        self.result = F4ValidationResult()

        # Load configuration if file specified
        if self.config_file and os.path.exists(self.config_file):
            self._load_config_file()

    def _load_config_file(self) -> None:
        """Load F4 configuration file."""
        try:
            with open(self.config_file) as f:
                # Simple .env file parsing
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#") and "=" in line:
                        key, value = line.split("=", 1)
                        os.environ[key.strip()] = value.strip()

            logger.info(f"Loaded configuration from {self.config_file}")

        except Exception as e:
            self.result.add_error(f"Failed to load config file {self.config_file}: {e}")

    def validate_all(self) -> F4ValidationResult:
        """Run comprehensive F4 validation."""
        logger.info(
            f"Starting comprehensive F4 validation for {self.environment} environment"
        )

        start_time = time.time()

        try:
            # Core validation checks
            self._validate_environment_variables()
            self._validate_provider_configuration()
            self._validate_veo3_environment()
            self._validate_deployment_settings()
            self._validate_security_settings()

            # Performance validation
            self._validate_performance_settings()
            self._validate_configuration_loading_performance()

            # Integration validation
            self._validate_provider_integration()
            self._validate_service_dependencies()

            # Environment-specific validation
            self._validate_environment_specific_settings()

            # Security validation
            self._validate_security_configuration()

            # Generate recommendations
            self._generate_recommendations()

        except Exception as e:
            self.result.add_error(f"Validation failed with exception: {e}")
            logger.error(f"Validation exception: {e}")

        # Record performance metrics
        total_time = (time.time() - start_time) * 1000
        self.result.performance_metrics["total_validation_time_ms"] = total_time

        # Determine overall status
        self.result.overall_status = self.result.determine_overall_status()

        logger.info(
            f"F4 validation completed - Status: {self.result.overall_status} ({total_time:.2f}ms)"
        )

        return self.result

    def _validate_environment_variables(self) -> None:
        """Validate F4 environment variables."""
        logger.info("Validating F4 environment variables...")

        # Critical F4 variables
        critical_vars = {
            "USE_MOCK_VEO": ["true", "false"],
            "DEFAULT_PROVIDER": ["azure_sora", "google_veo3"],
            "DEPLOYMENT_TYPE": ["local", "docker", "production"],
        }

        # Optional F4 variables
        optional_vars = [
            "CONFIG_VALIDATION_ENABLED",
            "F4_DEPLOYMENT_ENVIRONMENT",
            "VEO3_MODEL_VERSION",
            "VEO3_TIMEOUT",
            "CONFIG_CACHE_TTL",
        ]

        missing_critical = []
        invalid_values = []
        missing_optional = []

        # Check critical variables
        for var, valid_values in critical_vars.items():
            value = os.getenv(var)
            if not value:
                missing_critical.append(var)
            elif valid_values and value not in valid_values:
                invalid_values.append(
                    f"{var}={value} (valid: {', '.join(valid_values)})"
                )

        # Check optional variables
        for var in optional_vars:
            if not os.getenv(var):
                missing_optional.append(var)

        # Determine status
        if missing_critical or invalid_values:
            status = "critical"
            if missing_critical:
                self.result.add_error(
                    f"Missing critical environment variables: {', '.join(missing_critical)}"
                )
            if invalid_values:
                self.result.add_error(
                    f"Invalid environment variable values: {', '.join(invalid_values)}"
                )
        elif missing_optional:
            status = "warning"
            self.result.add_warning(
                f"Missing optional environment variables: {', '.join(missing_optional)}"
            )
        else:
            status = "passed"

        self.result.add_check(
            "environment_variables",
            status,
            {
                "critical_variables": {
                    "required": list(critical_vars.keys()),
                    "missing": missing_critical,
                    "invalid": invalid_values,
                },
                "optional_variables": {
                    "available": optional_vars,
                    "missing": missing_optional,
                },
            },
        )

    def _validate_provider_configuration(self) -> None:
        """Validate provider configuration."""
        logger.info("Validating provider configuration...")

        try:
            # Test provider availability
            availability = ConfigurationFactory.get_provider_availability()
            total_available = sum(availability.values())

            # Test default provider
            default_provider = ConfigurationFactory.get_default_provider()
            default_available = availability.get(default_provider, False)

            # Validate each provider
            provider_validations = {}
            for provider in ["azure_sora", "google_veo3"]:
                try:
                    validation = ConfigurationFactory.validate_provider_configuration(
                        provider
                    )
                    provider_validations[provider] = validation
                except Exception as e:
                    provider_validations[provider] = {"error": str(e)}

            # Determine status
            if total_available == 0:
                status = "critical"
                self.result.add_error("No video generation providers are available")
            elif not default_available:
                status = "warning"
                self.result.add_warning(
                    f"Default provider ({default_provider}) is not available"
                )
            else:
                status = "passed"

            self.result.add_check(
                "provider_configuration",
                status,
                {
                    "availability": availability,
                    "total_available": total_available,
                    "default_provider": default_provider,
                    "default_available": default_available,
                    "validations": provider_validations,
                },
            )

        except Exception as e:
            self.result.add_check("provider_configuration", "error", {"error": str(e)})
            self.result.add_error(f"Provider configuration validation failed: {e}")

    def _validate_veo3_environment(self) -> None:
        """Validate Google Veo3 environment configuration."""
        logger.info("Validating Google Veo3 environment...")

        try:
            # Run Veo3 environment validation
            veo3_validation = validate_veo3_environment()

            # Get Veo3 settings
            veo3_settings = get_cached_veo3_settings()

            # Determine status
            if not veo3_validation.get("valid", False):
                status = "warning"
                errors = veo3_validation.get("errors", [])
                for error in errors:
                    self.result.add_warning(f"Veo3 validation: {error}")
            else:
                status = "passed"

            self.result.add_check(
                "veo3_environment",
                status,
                {
                    "validation_result": veo3_validation,
                    "settings_loaded": bool(veo3_settings),
                    "use_mock_veo": veo3_settings.USE_MOCK_VEO
                    if veo3_settings
                    else None,
                    "project_id": veo3_settings.GOOGLE_PROJECT_ID
                    if veo3_settings
                    else None,
                },
            )

        except Exception as e:
            self.result.add_check("veo3_environment", "error", {"error": str(e)})
            self.result.add_error(f"Veo3 environment validation failed: {e}")

    def _validate_deployment_settings(self) -> None:
        """Validate deployment-specific settings."""
        logger.info("Validating deployment settings...")

        deployment_type = os.getenv("DEPLOYMENT_TYPE", "unknown")
        environment = os.getenv("F4_DEPLOYMENT_ENVIRONMENT", self.environment)

        issues = []

        # Validate deployment type consistency
        if deployment_type == "unknown":
            issues.append("DEPLOYMENT_TYPE not set")

        # Environment-specific validations
        if self.environment == "production":
            if os.getenv("DEBUG", "false").lower() == "true":
                issues.append("DEBUG enabled in production environment")

            if os.getenv("FLASK_DEBUG", "false").lower() == "true":
                issues.append("FLASK_DEBUG enabled in production environment")

            if os.getenv("USE_MOCK_VEO", "false").lower() == "true":
                self.result.add_warning("Using mock Veo3 in production environment")

        status = "critical" if issues else "passed"

        if issues:
            for issue in issues:
                self.result.add_error(f"Deployment setting issue: {issue}")

        self.result.add_check(
            "deployment_settings",
            status,
            {
                "deployment_type": deployment_type,
                "environment": environment,
                "issues": issues,
            },
        )

    def _validate_security_settings(self) -> None:
        """Validate security-related settings."""
        logger.info("Validating security settings...")

        security_issues = []

        # Check for weak secrets
        secret_key = os.getenv("SECRET_KEY", "")
        if not secret_key:
            security_issues.append("SECRET_KEY not set")
        elif len(secret_key) < 32:
            security_issues.append("SECRET_KEY too short (should be 32+ characters)")
        elif secret_key in ["dev", "development", "test", "changeme"]:
            security_issues.append("SECRET_KEY appears to be a default/weak value")

        # Check credential exposure
        google_client_secret = os.getenv("GOOGLE_CLIENT_SECRET", "")
        if google_client_secret and self.verbose:
            # Don't log the secret, just check if it's set
            logger.info("GOOGLE_CLIENT_SECRET is configured")

        # Check HTTPS settings for production
        if self.environment == "production":
            if os.getenv("FORCE_HTTPS", "false").lower() != "true":
                security_issues.append("FORCE_HTTPS not enabled for production")

            if os.getenv("SECURE_HEADERS_ENABLED", "false").lower() != "true":
                security_issues.append(
                    "SECURE_HEADERS_ENABLED not enabled for production"
                )

        # Add security issues to result
        for issue in security_issues:
            self.result.add_security_issue(
                issue, "high" if "SECRET_KEY" in issue else "medium"
            )

        status = (
            "critical"
            if any("SECRET_KEY" in issue for issue in security_issues)
            else ("warning" if security_issues else "passed")
        )

        self.result.add_check(
            "security_settings",
            status,
            {
                "issues": security_issues,
                "secret_key_set": bool(secret_key),
                "secret_key_length": len(secret_key) if secret_key else 0,
            },
        )

    def _validate_performance_settings(self) -> None:
        """Validate performance-related settings."""
        logger.info("Validating performance settings...")

        performance_issues = []

        # Check timeout settings
        veo3_timeout = int(os.getenv("VEO3_TIMEOUT", "300"))
        if veo3_timeout < 60:
            performance_issues.append(
                "VEO3_TIMEOUT too low (may cause premature timeouts)"
            )
        elif veo3_timeout > 1800:
            performance_issues.append(
                "VEO3_TIMEOUT very high (may affect user experience)"
            )

        # Check worker settings
        worker_concurrency = int(os.getenv("WORKER_CONCURRENCY", "2"))
        if worker_concurrency < 1:
            performance_issues.append("WORKER_CONCURRENCY too low")
        elif worker_concurrency > 8:
            performance_issues.append(
                "WORKER_CONCURRENCY very high (may cause resource issues)"
            )

        # Check cache settings
        config_cache_ttl = int(os.getenv("CONFIG_CACHE_TTL", "300"))
        if config_cache_ttl < 60:
            performance_issues.append(
                "CONFIG_CACHE_TTL too low (may impact performance)"
            )

        status = "warning" if performance_issues else "passed"

        for issue in performance_issues:
            self.result.add_warning(f"Performance setting: {issue}")

        self.result.add_check(
            "performance_settings",
            status,
            {
                "issues": performance_issues,
                "veo3_timeout": veo3_timeout,
                "worker_concurrency": worker_concurrency,
                "config_cache_ttl": config_cache_ttl,
            },
        )

    def _validate_configuration_loading_performance(self) -> None:
        """Validate configuration loading performance."""
        logger.info("Validating configuration loading performance...")

        start_time = time.time()

        try:
            # Test configuration loading
            base_config = ConfigurationFactory.get_base_config()
            veo3_settings = get_cached_veo3_settings()

            load_time_ms = (time.time() - start_time) * 1000
            self.result.performance_metrics["config_load_time_ms"] = load_time_ms

            # Performance thresholds
            threshold_ms = 5000  # 5 seconds

            if load_time_ms > threshold_ms:
                status = "warning"
                self.result.add_warning(
                    f"Configuration loading time ({load_time_ms:.2f}ms) exceeds threshold ({threshold_ms}ms)"
                )
            else:
                status = "passed"

            self.result.add_check(
                "configuration_loading_performance",
                status,
                {
                    "load_time_ms": load_time_ms,
                    "threshold_ms": threshold_ms,
                    "base_config_loaded": bool(base_config),
                    "veo3_settings_loaded": bool(veo3_settings),
                },
            )

        except Exception as e:
            self.result.add_check(
                "configuration_loading_performance", "error", {"error": str(e)}
            )

    def _validate_provider_integration(self) -> None:
        """Validate provider integration and switching."""
        logger.info("Validating provider integration...")

        start_time = time.time()

        try:
            # Test provider switching
            providers = ["azure_sora", "google_veo3"]
            switch_results = {}

            for provider in providers:
                provider_start = time.time()
                try:
                    config = ConfigurationFactory.create_provider_config(provider)
                    switch_time = (time.time() - provider_start) * 1000
                    switch_results[provider] = {
                        "status": "success",
                        "switch_time_ms": switch_time,
                        "config_created": bool(config),
                    }
                except Exception as e:
                    switch_time = (time.time() - provider_start) * 1000
                    switch_results[provider] = {
                        "status": "error",
                        "switch_time_ms": switch_time,
                        "error": str(e),
                    }

            total_switch_time = (time.time() - start_time) * 1000
            self.result.performance_metrics["provider_switch_time_ms"] = (
                total_switch_time
            )

            # Determine status
            successful_switches = sum(
                1 for r in switch_results.values() if r["status"] == "success"
            )
            if successful_switches == 0:
                status = "critical"
                self.result.add_error("No providers can be configured successfully")
            elif successful_switches < len(providers):
                status = "warning"
                self.result.add_warning("Some providers cannot be configured")
            else:
                status = "passed"

            self.result.add_check(
                "provider_integration",
                status,
                {
                    "switch_results": switch_results,
                    "successful_switches": successful_switches,
                    "total_providers": len(providers),
                    "total_switch_time_ms": total_switch_time,
                },
            )

        except Exception as e:
            self.result.add_check("provider_integration", "error", {"error": str(e)})

    def _validate_service_dependencies(self) -> None:
        """Validate service dependencies and integration points."""
        logger.info("Validating service dependencies...")

        dependencies = {}

        # Check database connection (if configured)
        database_url = os.getenv("DATABASE_URL")
        if database_url:
            try:
                # This is a basic check - in a real environment you'd test actual connectivity
                dependencies["database"] = {
                    "configured": True,
                    "url_format": "valid" if "://" in database_url else "invalid",
                }
            except Exception as e:
                dependencies["database"] = {"error": str(e)}
        else:
            dependencies["database"] = {"configured": False}

        # Check Redis connection (if configured)
        redis_url = os.getenv("CELERY_BROKER_URL")
        if redis_url:
            dependencies["redis"] = {
                "configured": True,
                "url_format": "valid" if "://" in redis_url else "invalid",
            }
        else:
            dependencies["redis"] = {"configured": False}

        # Determine status based on required dependencies
        required_dependencies = (
            ["database", "redis"] if self.environment == "production" else []
        )
        missing_required = [
            dep
            for dep in required_dependencies
            if not dependencies.get(dep, {}).get("configured", False)
        ]

        if missing_required:
            status = "warning"
            self.result.add_warning(
                f"Missing required dependencies for {self.environment}: {', '.join(missing_required)}"
            )
        else:
            status = "passed"

        self.result.add_check(
            "service_dependencies",
            status,
            {
                "dependencies": dependencies,
                "required_dependencies": required_dependencies,
                "missing_required": missing_required,
            },
        )

    def _validate_environment_specific_settings(self) -> None:
        """Validate environment-specific configuration requirements."""
        logger.info(f"Validating {self.environment}-specific settings...")

        issues = []

        if self.environment == "production":
            # Production-specific validations
            required_prod_vars = ["SECRET_KEY", "DB_PASSWORD"]
            for var in required_prod_vars:
                if not os.getenv(var):
                    issues.append(f"Production requires {var} to be set")

            # Check SSL settings
            if os.getenv("SSL_ENABLED", "false").lower() != "true":
                self.result.add_warning("SSL not enabled for production")

        elif self.environment == "development":
            # Development-specific validations
            if os.getenv("USE_MOCK_VEO", "true").lower() != "true":
                self.result.add_warning("Consider using mock Veo3 in development")

        status = "critical" if issues else "passed"

        for issue in issues:
            self.result.add_error(issue)

        self.result.add_check(
            "environment_specific_settings",
            status,
            {"environment": self.environment, "issues": issues},
        )

    def _validate_security_configuration(self) -> None:
        """Validate security configuration comprehensively."""
        logger.info("Validating security configuration...")

        security_score = 100
        security_findings = []

        # Check environment variable security
        sensitive_vars = [
            "SECRET_KEY",
            "GOOGLE_CLIENT_SECRET",
            "DB_PASSWORD",
            "AZURE_OPENAI_API_KEY",
        ]
        for var in sensitive_vars:
            value = os.getenv(var, "")
            if value:
                if len(value) < 16:
                    security_findings.append(f"{var} appears to be too short")
                    security_score -= 10

                # Check for common weak values
                weak_values = ["password", "secret", "key", "admin", "test", "dev"]
                if any(weak in value.lower() for weak in weak_values):
                    security_findings.append(
                        f"{var} may contain weak/predictable content"
                    )
                    security_score -= 15

        # Check file permissions (if config file provided)
        if self.config_file and os.path.exists(self.config_file):

            file_mode = oct(os.stat(self.config_file).st_mode)[-3:]
            if file_mode != "600":
                security_findings.append(
                    f"Config file {self.config_file} has permissive permissions ({file_mode})"
                )
                security_score -= 20

        # Determine status based on security score
        if security_score >= 80:
            status = "passed"
        elif security_score >= 60:
            status = "warning"
        else:
            status = "critical"

        for finding in security_findings:
            self.result.add_security_issue(finding)

        self.result.add_check(
            "security_configuration",
            status,
            {
                "security_score": security_score,
                "findings": security_findings,
                "sensitive_vars_configured": len(
                    [var for var in sensitive_vars if os.getenv(var)]
                ),
            },
        )

    def _generate_recommendations(self) -> None:
        """Generate recommendations based on validation results."""
        logger.info("Generating recommendations...")

        # Performance recommendations
        if self.result.performance_metrics.get("config_load_time_ms", 0) > 1000:
            self.result.add_recommendation(
                "Consider optimizing configuration loading for better performance"
            )

        # Security recommendations
        if (
            self.environment == "production"
            and os.getenv("USE_MOCK_VEO", "false").lower() == "true"
        ):
            self.result.add_recommendation(
                "Switch to real Veo3 API for production deployment"
            )

        # Provider recommendations
        availability = {}
        try:
            availability = ConfigurationFactory.get_provider_availability()
        except:
            pass

        if sum(availability.values()) == 1:
            self.result.add_recommendation(
                "Consider configuring multiple providers for redundancy"
            )

        # Environment recommendations
        if self.environment == "development" and not os.getenv("DEBUG"):
            self.result.add_recommendation(
                "Enable DEBUG mode for development environment"
            )


def create_html_report(validation_result: F4ValidationResult, output_file: str) -> None:
    """Create HTML validation report."""

    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>F4 Configuration Validation Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
            .status-passed { color: green; }
            .status-warning { color: orange; }
            .status-critical, .status-error, .status-failed { color: red; }
            .check { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
            .check-passed { border-left-color: green; }
            .check-warning { border-left-color: orange; }
            .check-critical, .check-error, .check-failed { border-left-color: red; }
            .details { margin-left: 20px; font-size: 0.9em; color: #666; }
            .recommendations { background: #e7f3ff; padding: 15px; border-radius: 5px; }
            .security-issues { background: #ffe7e7; padding: 15px; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>F4 Configuration Validation Report</h1>
            <p><strong>Overall Status:</strong> <span class="status-{overall_status}">{overall_status}</span></p>
            <p><strong>Validation Time:</strong> {timestamp}</p>
            <p><strong>Total Checks:</strong> {total_checks} | 
               <strong>Passed:</strong> {passed_checks} | 
               <strong>Warnings:</strong> {warning_checks} | 
               <strong>Failed:</strong> {failed_checks}</p>
        </div>
        
        <h2>Validation Checks</h2>
        {checks_html}
        
        {recommendations_html}
        
        {security_html}
        
        <h2>Performance Metrics</h2>
        <div class="details">
            {performance_html}
        </div>
    </body>
    </html>
    """

    # Generate checks HTML
    checks_html = ""
    for check_name, check_data in validation_result.checks.items():
        status = check_data["status"]
        checks_html += f"""
        <div class="check check-{status}">
            <h3>{check_name.replace("_", " ").title()}: <span class="status-{status}">{status}</span></h3>
            <div class="details">
                {json.dumps(check_data["details"], indent=2)}
            </div>
        </div>
        """

    # Generate recommendations HTML
    recommendations_html = ""
    if validation_result.recommendations:
        recommendations_html = (
            "<h2>Recommendations</h2><div class='recommendations'><ul>"
        )
        for rec in validation_result.recommendations:
            recommendations_html += f"<li>{rec}</li>"
        recommendations_html += "</ul></div>"

    # Generate security issues HTML
    security_html = ""
    if validation_result.security_issues:
        security_html = "<h2>Security Issues</h2><div class='security-issues'><ul>"
        for issue in validation_result.security_issues:
            security_html += f"<li><strong>{issue['severity'].upper()}:</strong> {issue['issue']}</li>"
        security_html += "</ul></div>"

    # Generate performance HTML
    performance_html = "<ul>"
    for metric, value in validation_result.performance_metrics.items():
        performance_html += f"<li><strong>{metric}:</strong> {value}</li>"
    performance_html += "</ul>"

    # Fill template
    html_content = html_template.format(
        overall_status=validation_result.overall_status,
        timestamp=validation_result.validation_timestamp.isoformat(),
        total_checks=len(validation_result.checks),
        passed_checks=len(
            [c for c in validation_result.checks.values() if c["status"] == "passed"]
        ),
        warning_checks=len(
            [c for c in validation_result.checks.values() if c["status"] == "warning"]
        ),
        failed_checks=len(
            [
                c
                for c in validation_result.checks.values()
                if c["status"] in ["error", "critical", "failed"]
            ]
        ),
        checks_html=checks_html,
        recommendations_html=recommendations_html,
        security_html=security_html,
        performance_html=performance_html,
    )

    with open(output_file, "w") as f:
        f.write(html_content)

    print(f"HTML report saved to: {output_file}")


def main():
    """Main entry point for F4 validation script."""
    parser = argparse.ArgumentParser(description="F4 Configuration Validation Script")

    parser.add_argument(
        "--environment",
        "-e",
        default="production",
        choices=["development", "staging", "production"],
        help="Environment to validate",
    )

    parser.add_argument("--config-file", "-c", help="F4 configuration file to validate")

    parser.add_argument(
        "--provider",
        "-p",
        choices=["azure_sora", "google_veo3"],
        help="Test specific provider",
    )

    parser.add_argument(
        "--include-credentials",
        action="store_true",
        help="Test actual API credentials (security risk)",
    )

    parser.add_argument(
        "--output-format",
        "-f",
        default="text",
        choices=["text", "json", "html"],
        help="Output format",
    )

    parser.add_argument("--output-file", "-o", help="Output file path")

    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )

    parser.add_argument(
        "--fix-issues",
        action="store_true",
        help="Attempt to fix common configuration issues",
    )

    args = parser.parse_args()

    # Set up logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create validator
    validator = F4ConfigurationValidator(
        environment=args.environment,
        config_file=args.config_file,
        include_credentials=args.include_credentials,
        verbose=args.verbose,
    )

    # Run validation
    result = validator.validate_all()

    # Output results
    if args.output_format == "json":
        output = json.dumps(result.to_dict(), indent=2)
        if args.output_file:
            with open(args.output_file, "w") as f:
                f.write(output)
            print(f"JSON report saved to: {args.output_file}")
        else:
            print(output)

    elif args.output_format == "html":
        output_file = (
            args.output_file
            or f"f4_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        )
        create_html_report(result, output_file)

    else:  # text format
        print("\nF4 Configuration Validation Report")
        print("=" * 50)
        print(f"Overall Status: {result.overall_status}")
        print(f"Validation Time: {result.validation_timestamp}")
        print(f"Environment: {args.environment}")

        print("\nSummary:")
        print(f"  Total Checks: {len(result.checks)}")
        print(
            f"  Passed: {len([c for c in result.checks.values() if c['status'] == 'passed'])}"
        )
        print(
            f"  Warnings: {len([c for c in result.checks.values() if c['status'] == 'warning'])}"
        )
        print(
            f"  Failed: {len([c for c in result.checks.values() if c['status'] in ['error', 'critical', 'failed']])}"
        )

        print("\nValidation Checks:")
        for check_name, check_data in result.checks.items():
            status_symbol = (
                "✓"
                if check_data["status"] == "passed"
                else ("⚠" if check_data["status"] == "warning" else "✗")
            )
            print(
                f"  {status_symbol} {check_name.replace('_', ' ').title()}: {check_data['status']}"
            )

        if result.errors:
            print("\nErrors:")
            for error in result.errors:
                print(f"  ✗ {error}")

        if result.warnings:
            print("\nWarnings:")
            for warning in result.warnings:
                print(f"  ⚠ {warning}")

        if result.recommendations:
            print("\nRecommendations:")
            for rec in result.recommendations:
                print(f"  • {rec}")

        if result.security_issues:
            print("\nSecurity Issues:")
            for issue in result.security_issues:
                print(f"  🔒 {issue['severity'].upper()}: {issue['issue']}")

        if result.performance_metrics:
            print("\nPerformance Metrics:")
            for metric, value in result.performance_metrics.items():
                print(f"  • {metric}: {value}")

        if args.output_file:
            with open(args.output_file, "w") as f:
                f.write(json.dumps(result.to_dict(), indent=2))
            print(f"\nDetailed report saved to: {args.output_file}")

    # Exit with appropriate code
    exit_codes = {
        "passed": 0,
        "warning": 1,
        "failed": 2,
        "critical": 2,
        "error": 2,
        "unknown": 3,
    }

    sys.exit(exit_codes.get(result.overall_status, 3))


if __name__ == "__main__":
    main()
