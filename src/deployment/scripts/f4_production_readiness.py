#!/usr/bin/env python3
"""
F4 Production Readiness Validation

Comprehensive production readiness validation for F4 Environment Configuration
with deployment validation, operational procedures testing, and integration verification.

This script validates:
- Complete F4 configuration deployment readiness
- Operational monitoring and alerting capabilities
- Provider integration and switching functionality
- Performance and security requirements
- Disaster recovery and rollback procedures
- Documentation and runbook completeness

Usage:
    python f4_production_readiness.py [options]
    ./f4_production_readiness.py --environment production --comprehensive

Options:
    --environment ENV: Environment to validate (staging/production)
    --comprehensive: Run comprehensive validation including integration tests
    --security-check: Include security validation tests
    --performance-test: Run performance benchmark tests
    --output-format FORMAT: Output format (text/json/html/report)
    --output-file FILE: Output file path
    --fix-issues: Attempt to fix non-critical issues automatically
"""

import argparse
import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import validate_veo3_environment
from src.deployment.scripts.f4_validation import F4ConfigurationValidator
from src.monitoring.f4_alerting import get_alert_manager
from src.monitoring.f4_metrics import get_metrics_collector

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ProductionReadinessResult:
    """Container for production readiness validation results."""

    def __init__(self):
        self.overall_status = "unknown"
        self.readiness_score = 0
        self.validation_timestamp = datetime.now(timezone.utc)
        self.categories = {}
        self.critical_issues = []
        self.warnings = []
        self.recommendations = []
        self.deployment_blockers = []
        self.operational_readiness = {}

    def add_category(
        self, category: str, score: int, status: str, details: Dict[str, Any]
    ) -> None:
        """Add a readiness category result."""
        self.categories[category] = {
            "score": score,
            "status": status,
            "details": details,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    def add_critical_issue(self, issue: str) -> None:
        """Add a critical issue that blocks deployment."""
        self.critical_issues.append(issue)
        self.deployment_blockers.append(issue)

    def add_warning(self, warning: str) -> None:
        """Add a warning that should be addressed."""
        self.warnings.append(warning)

    def add_recommendation(self, recommendation: str) -> None:
        """Add a recommendation for improvement."""
        self.recommendations.append(recommendation)

    def calculate_overall_score(self) -> int:
        """Calculate overall readiness score."""
        if not self.categories:
            return 0

        # Weight categories by importance
        weights = {
            "configuration": 25,
            "deployment": 20,
            "monitoring": 15,
            "security": 15,
            "performance": 10,
            "documentation": 5,
            "disaster_recovery": 10,
        }

        weighted_score = 0
        total_weight = 0

        for category, weight in weights.items():
            if category in self.categories:
                weighted_score += self.categories[category]["score"] * weight
                total_weight += weight

        # Deduct for critical issues
        critical_penalty = len(self.critical_issues) * 20

        final_score = (weighted_score / total_weight) if total_weight > 0 else 0
        final_score = max(0, final_score - critical_penalty)

        return int(final_score)

    def determine_overall_status(self) -> str:
        """Determine overall production readiness status."""
        score = self.calculate_overall_score()

        if self.critical_issues:
            return "not_ready"
        elif score >= 90:
            return "production_ready"
        elif score >= 75:
            return "ready_with_warnings"
        elif score >= 60:
            return "needs_improvement"
        else:
            return "not_ready"

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        self.readiness_score = self.calculate_overall_score()
        self.overall_status = self.determine_overall_status()

        return {
            "overall_status": self.overall_status,
            "readiness_score": self.readiness_score,
            "validation_timestamp": self.validation_timestamp.isoformat(),
            "summary": {
                "total_categories": len(self.categories),
                "critical_issues": len(self.critical_issues),
                "warnings": len(self.warnings),
                "recommendations": len(self.recommendations),
                "deployment_blockers": len(self.deployment_blockers),
            },
            "categories": self.categories,
            "critical_issues": self.critical_issues,
            "warnings": self.warnings,
            "recommendations": self.recommendations,
            "deployment_blockers": self.deployment_blockers,
            "operational_readiness": self.operational_readiness,
        }


class F4ProductionReadinessValidator:
    """Comprehensive F4 production readiness validator."""

    def __init__(
        self,
        environment: str = "production",
        comprehensive: bool = False,
        security_check: bool = False,
        performance_test: bool = False,
    ):
        """
        Initialize production readiness validator.

        Args:
            environment: Environment to validate
            comprehensive: Run comprehensive validation
            security_check: Include security validation
            performance_test: Run performance tests
        """
        self.environment = environment
        self.comprehensive = comprehensive
        self.security_check = security_check
        self.performance_test = performance_test
        self.result = ProductionReadinessResult()

        logger.info(
            f"Initializing F4 production readiness validation for {environment}"
        )

    async def validate_production_readiness(self) -> ProductionReadinessResult:
        """Run comprehensive production readiness validation."""
        logger.info("Starting F4 production readiness validation")
        start_time = time.time()

        try:
            # Core readiness validations
            await self._validate_configuration_readiness()
            await self._validate_deployment_readiness()
            await self._validate_monitoring_readiness()
            await self._validate_security_readiness()

            if self.performance_test:
                await self._validate_performance_readiness()

            await self._validate_documentation_readiness()
            await self._validate_disaster_recovery_readiness()

            # Comprehensive validations
            if self.comprehensive:
                await self._validate_integration_readiness()
                await self._validate_operational_procedures()
                await self._validate_scalability_readiness()

            # Generate final recommendations
            await self._generate_production_recommendations()

        except Exception as e:
            logger.error(f"Production readiness validation failed: {e}")
            self.result.add_critical_issue(f"Validation process failed: {e}")

        # Calculate final scores
        self.result.readiness_score = self.result.calculate_overall_score()
        self.result.overall_status = self.result.determine_overall_status()

        total_time = time.time() - start_time
        logger.info(
            f"Production readiness validation completed - Score: {self.result.readiness_score}% ({total_time:.2f}s)"
        )

        return self.result

    async def _validate_configuration_readiness(self) -> None:
        """Validate F4 configuration readiness for production."""
        logger.info("Validating configuration readiness...")

        score = 100
        issues = []

        try:
            # Run F4 configuration validation
            validator = F4ConfigurationValidator(
                environment=self.environment, include_credentials=True, verbose=False
            )
            config_result = validator.validate_all()

            # Analyze configuration validation results
            if config_result.overall_status == "failed":
                score = 0
                issues.extend(config_result.errors)
            elif config_result.overall_status == "warning":
                score = 75
                issues.extend(config_result.warnings)

            # Check critical configuration requirements
            critical_requirements = [
                ("Environment variables", self._check_environment_variables()),
                ("Provider configuration", self._check_provider_configuration()),
                ("Security settings", self._check_security_configuration()),
                ("Performance settings", self._check_performance_configuration()),
            ]

            for req_name, req_result in critical_requirements:
                if not req_result["passed"]:
                    score -= 20
                    issues.extend(req_result["issues"])

            # Validate deployment-specific requirements
            deployment_requirements = self._check_deployment_requirements()
            if not deployment_requirements["passed"]:
                score -= 15
                issues.extend(deployment_requirements["issues"])

            status = (
                "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")
            )

            self.result.add_category(
                "configuration",
                max(0, score),
                status,
                {
                    "config_validation": config_result.to_dict(),
                    "critical_requirements": dict(critical_requirements),
                    "deployment_requirements": deployment_requirements,
                    "issues": issues,
                },
            )

            # Add critical issues
            for issue in issues:
                if "required" in issue.lower() or "missing" in issue.lower():
                    self.result.add_critical_issue(f"Configuration: {issue}")
                else:
                    self.result.add_warning(f"Configuration: {issue}")

        except Exception as e:
            logger.error(f"Configuration readiness validation failed: {e}")
            self.result.add_category("configuration", 0, "error", {"error": str(e)})
            self.result.add_critical_issue(f"Configuration validation failed: {e}")

    def _check_environment_variables(self) -> Dict[str, Any]:
        """Check environment variables completeness."""
        critical_vars = {
            "USE_MOCK_VEO": bool(os.getenv("USE_MOCK_VEO")),
            "DEFAULT_PROVIDER": os.getenv("DEFAULT_PROVIDER")
            in ["azure_sora", "google_veo3"],
            "DEPLOYMENT_TYPE": bool(os.getenv("DEPLOYMENT_TYPE")),
            "SECRET_KEY": len(os.getenv("SECRET_KEY", "")) >= 32,
        }

        # Production-specific variables
        if self.environment == "production":
            critical_vars.update(
                {
                    "DB_PASSWORD": bool(os.getenv("DB_PASSWORD")),
                    "AZURE_OPENAI_API_KEY": bool(os.getenv("AZURE_OPENAI_API_KEY")),
                    "AZURE_OPENAI_ENDPOINT": bool(os.getenv("AZURE_OPENAI_ENDPOINT")),
                }
            )

        issues = []
        for var, is_valid in critical_vars.items():
            if not is_valid:
                issues.append(f"Environment variable {var} is missing or invalid")

        return {
            "passed": len(issues) == 0,
            "issues": issues,
            "variables_checked": list(critical_vars.keys()),
            "valid_count": sum(critical_vars.values()),
        }

    def _check_provider_configuration(self) -> Dict[str, Any]:
        """Check provider configuration completeness."""
        issues = []

        try:
            # Test provider availability
            availability = ConfigurationFactory.get_provider_availability()
            total_available = sum(availability.values())

            if total_available == 0:
                issues.append("No video generation providers are available")

            # Test provider switching
            for provider in ["azure_sora", "google_veo3"]:
                try:
                    ConfigurationFactory.create_provider_config(provider)
                except Exception as e:
                    issues.append(f"Provider {provider} configuration failed: {e}")

            # Test default provider
            default_provider = ConfigurationFactory.get_default_provider()
            if not availability.get(default_provider, False):
                issues.append(f"Default provider {default_provider} is not available")

        except Exception as e:
            issues.append(f"Provider configuration check failed: {e}")

        return {
            "passed": len(issues) == 0,
            "issues": issues,
            "provider_availability": availability if "availability" in locals() else {},
        }

    def _check_security_configuration(self) -> Dict[str, Any]:
        """Check security configuration requirements."""
        issues = []

        # Check secret key strength
        secret_key = os.getenv("SECRET_KEY", "")
        if len(secret_key) < 32:
            issues.append("SECRET_KEY is too short (minimum 32 characters)")

        # Check production security settings
        if self.environment == "production":
            if os.getenv("DEBUG", "false").lower() == "true":
                issues.append("DEBUG mode is enabled in production")

            if os.getenv("FLASK_DEBUG", "false").lower() == "true":
                issues.append("FLASK_DEBUG is enabled in production")

            if os.getenv("FORCE_HTTPS", "false").lower() != "true":
                issues.append("FORCE_HTTPS is not enabled for production")

        # Check credential security
        google_secret = os.getenv("GOOGLE_CLIENT_SECRET", "")
        if google_secret and len(google_secret) < 16:
            issues.append("GOOGLE_CLIENT_SECRET appears to be too short")

        return {
            "passed": len(issues) == 0,
            "issues": issues,
            "security_score": max(0, 100 - len(issues) * 15),
        }

    def _check_performance_configuration(self) -> Dict[str, Any]:
        """Check performance configuration settings."""
        issues = []
        warnings = []

        # Check timeout settings
        veo3_timeout = int(os.getenv("VEO3_TIMEOUT", "300"))
        if veo3_timeout < 60:
            issues.append("VEO3_TIMEOUT is too low (minimum 60 seconds)")
        elif veo3_timeout > 1800:
            warnings.append("VEO3_TIMEOUT is very high (may affect user experience)")

        # Check worker settings
        worker_concurrency = int(os.getenv("WORKER_CONCURRENCY", "2"))
        if worker_concurrency < 1:
            issues.append("WORKER_CONCURRENCY is too low")
        elif worker_concurrency > 8:
            warnings.append(
                "WORKER_CONCURRENCY is very high (may cause resource issues)"
            )

        # Check cache settings
        config_cache_ttl = int(os.getenv("CONFIG_CACHE_TTL", "300"))
        if config_cache_ttl < 60:
            warnings.append("CONFIG_CACHE_TTL is low (may impact performance)")

        return {
            "passed": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "performance_settings": {
                "veo3_timeout": veo3_timeout,
                "worker_concurrency": worker_concurrency,
                "config_cache_ttl": config_cache_ttl,
            },
        }

    def _check_deployment_requirements(self) -> Dict[str, Any]:
        """Check deployment-specific requirements."""
        issues = []

        # Check deployment type
        deployment_type = os.getenv("DEPLOYMENT_TYPE", "")
        if not deployment_type:
            issues.append("DEPLOYMENT_TYPE not set")
        elif deployment_type not in ["local", "docker", "production"]:
            issues.append(f"Invalid DEPLOYMENT_TYPE: {deployment_type}")

        # Check environment consistency
        f4_env = os.getenv("F4_DEPLOYMENT_ENVIRONMENT", "")
        if f4_env and f4_env != self.environment:
            issues.append(
                f"F4_DEPLOYMENT_ENVIRONMENT ({f4_env}) doesn't match target environment ({self.environment})"
            )

        return {
            "passed": len(issues) == 0,
            "issues": issues,
            "deployment_type": deployment_type,
            "f4_environment": f4_env,
        }

    async def _validate_deployment_readiness(self) -> None:
        """Validate deployment readiness and automation."""
        logger.info("Validating deployment readiness...")

        score = 100
        issues = []

        try:
            # Check deployment scripts
            deployment_script = project_root / "src/deployment/scripts/f4_deployment.sh"
            if not deployment_script.exists():
                score -= 30
                issues.append("F4 deployment script not found")
            elif not os.access(deployment_script, os.X_OK):
                score -= 20
                issues.append("F4 deployment script is not executable")

            # Check validation script
            validation_script = project_root / "src/deployment/scripts/f4_validation.py"
            if not validation_script.exists():
                score -= 20
                issues.append("F4 validation script not found")

            # Check Docker configuration
            docker_compose = (
                project_root / "src/deployment/docker/docker-compose.simple.yml"
            )
            if not docker_compose.exists():
                score -= 25
                issues.append("Docker compose file not found")

            # Check environment configs
            config_dir = project_root / "src/deployment/config"
            env_file = config_dir / f"f4-environment-{self.environment}.env"
            if not env_file.exists():
                score -= 15
                issues.append(
                    f"Environment config file for {self.environment} not found"
                )

            # Test deployment script (dry run)
            if deployment_script.exists() and os.access(deployment_script, os.X_OK):
                try:
                    result = subprocess.run(
                        [
                            str(deployment_script),
                            "--type",
                            self.environment,
                            "--dry-run",
                        ],
                        capture_output=True,
                        text=True,
                        timeout=60,
                    )

                    if result.returncode != 0:
                        score -= 15
                        issues.append(
                            f"Deployment script dry run failed: {result.stderr}"
                        )

                except subprocess.TimeoutExpired:
                    score -= 10
                    issues.append("Deployment script dry run timed out")
                except Exception as e:
                    score -= 10
                    issues.append(f"Deployment script test failed: {e}")

            # Check backup procedures
            backup_dir = Path("/opt/sora/backups")
            if not backup_dir.exists():
                score -= 10
                issues.append("Backup directory not configured")

            status = (
                "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")
            )

            self.result.add_category(
                "deployment",
                max(0, score),
                status,
                {
                    "script_availability": {
                        "deployment_script": deployment_script.exists(),
                        "validation_script": validation_script.exists(),
                        "docker_compose": docker_compose.exists(),
                        "environment_config": env_file.exists()
                        if "env_file" in locals()
                        else False,
                    },
                    "issues": issues,
                },
            )

            # Add issues to result
            for issue in issues:
                if "not found" in issue.lower() or "failed" in issue.lower():
                    self.result.add_critical_issue(f"Deployment: {issue}")
                else:
                    self.result.add_warning(f"Deployment: {issue}")

        except Exception as e:
            logger.error(f"Deployment readiness validation failed: {e}")
            self.result.add_category("deployment", 0, "error", {"error": str(e)})
            self.result.add_critical_issue(f"Deployment validation failed: {e}")

    async def _validate_monitoring_readiness(self) -> None:
        """Validate monitoring and alerting readiness."""
        logger.info("Validating monitoring readiness...")

        score = 100
        issues = []

        try:
            # Check monitoring components
            try:
                metrics_collector = get_metrics_collector()
                if not metrics_collector.collection_active:
                    score -= 20
                    issues.append("Metrics collection is not active")
            except Exception as e:
                score -= 25
                issues.append(f"Metrics collector not available: {e}")

            try:
                alert_manager = get_alert_manager()
                if not alert_manager.running:
                    score -= 20
                    issues.append("Alert manager is not running")
            except Exception as e:
                score -= 25
                issues.append(f"Alert manager not available: {e}")

            # Check health endpoints (if system is running)
            try:
                import requests

                health_endpoints = [
                    "http://localhost/health/config",
                    "http://localhost/health/config/providers",
                    "http://localhost/monitoring/f4/metrics",
                ]

                for endpoint in health_endpoints:
                    try:
                        response = requests.get(endpoint, timeout=10)
                        if response.status_code != 200:
                            score -= 10
                            issues.append(
                                f"Health endpoint {endpoint} returned {response.status_code}"
                            )
                    except requests.exceptions.ConnectionError:
                        # System may not be running - this is expected
                        pass
                    except Exception as e:
                        score -= 5
                        issues.append(f"Health endpoint {endpoint} test failed: {e}")

            except ImportError:
                # Requests not available - skip endpoint tests
                pass

            # Check alerting configuration
            slack_webhook = os.getenv("SLACK_WEBHOOK_URL")
            alert_email = os.getenv("ALERT_EMAIL")

            if not slack_webhook and not alert_email:
                score -= 15
                issues.append("No alerting channels configured (Slack or email)")

            # Check Prometheus metrics availability
            if (
                "metrics_collector" in locals()
                and not metrics_collector.enable_prometheus
            ):
                score -= 10
                issues.append("Prometheus metrics export not enabled")

            status = (
                "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")
            )

            self.result.add_category(
                "monitoring",
                max(0, score),
                status,
                {
                    "metrics_collector_active": "metrics_collector" in locals()
                    and metrics_collector.collection_active,
                    "alert_manager_running": "alert_manager" in locals()
                    and alert_manager.running,
                    "alerting_configured": bool(slack_webhook or alert_email),
                    "prometheus_enabled": "metrics_collector" in locals()
                    and metrics_collector.enable_prometheus,
                    "issues": issues,
                },
            )

            for issue in issues:
                if "not active" in issue.lower() or "not running" in issue.lower():
                    self.result.add_critical_issue(f"Monitoring: {issue}")
                else:
                    self.result.add_warning(f"Monitoring: {issue}")

        except Exception as e:
            logger.error(f"Monitoring readiness validation failed: {e}")
            self.result.add_category("monitoring", 0, "error", {"error": str(e)})
            self.result.add_critical_issue(f"Monitoring validation failed: {e}")

    async def _validate_security_readiness(self) -> None:
        """Validate security readiness for production."""
        logger.info("Validating security readiness...")

        score = 100
        issues = []

        # Check environment file permissions
        env_file = project_root / ".env"
        if env_file.exists():

            file_mode = oct(env_file.stat().st_mode)[-3:]
            if file_mode != "600":
                score -= 15
                issues.append(
                    f"Environment file has permissive permissions ({file_mode})"
                )

        # Check for hardcoded secrets
        security_patterns = [
            ("sk-", "Potential OpenAI API key in configuration"),
            ("ya29.", "Potential Google OAuth token in configuration"),
            ("AKIA", "Potential AWS access key in configuration"),
        ]

        # Scan environment variables for patterns
        for var_name, var_value in os.environ.items():
            if var_value:
                for pattern, description in security_patterns:
                    if pattern in var_value and "EXAMPLE" not in var_name:
                        score -= 10
                        issues.append(f"{description}: {var_name}")

        # Check SSL/TLS configuration for production
        if self.environment == "production":
            if os.getenv("FORCE_HTTPS", "false").lower() != "true":
                score -= 20
                issues.append("HTTPS enforcement not enabled for production")

            if os.getenv("SECURE_HEADERS_ENABLED", "false").lower() != "true":
                score -= 10
                issues.append("Secure headers not enabled for production")

        # Check authentication configuration
        google_client_secret = os.getenv("GOOGLE_CLIENT_SECRET", "")
        if google_client_secret:
            if len(google_client_secret) < 16:
                score -= 15
                issues.append("Google client secret appears to be too short")

            # Check if it looks like a placeholder
            if any(
                word in google_client_secret.lower()
                for word in ["example", "placeholder", "changeme"]
            ):
                score -= 25
                issues.append("Google client secret appears to be a placeholder")

        # Check debug mode in production
        if self.environment == "production":
            debug_vars = ["DEBUG", "FLASK_DEBUG", "CONFIG_DEBUG_MODE"]
            for var in debug_vars:
                if os.getenv(var, "false").lower() == "true":
                    score -= 15
                    issues.append(f"Debug mode ({var}) enabled in production")

        status = "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")

        self.result.add_category(
            "security",
            max(0, score),
            status,
            {
                "security_score": score,
                "issues": issues,
                "checks_performed": [
                    "file_permissions",
                    "hardcoded_secrets",
                    "ssl_configuration",
                    "authentication_config",
                    "debug_mode_check",
                ],
            },
        )

        for issue in issues:
            if any(
                word in issue.lower()
                for word in ["hardcoded", "placeholder", "debug.*production"]
            ):
                self.result.add_critical_issue(f"Security: {issue}")
            else:
                self.result.add_warning(f"Security: {issue}")

    async def _validate_performance_readiness(self) -> None:
        """Validate performance readiness and benchmarks."""
        logger.info("Validating performance readiness...")

        score = 100
        issues = []

        try:
            # Test configuration loading performance
            config_start = time.time()
            try:
                ConfigurationFactory.get_base_config()
                config_load_time = (time.time() - config_start) * 1000

                if config_load_time > 5000:  # 5 seconds
                    score -= 20
                    issues.append(
                        f"Configuration loading is slow ({config_load_time:.2f}ms)"
                    )
                elif config_load_time > 2000:  # 2 seconds
                    score -= 10
                    issues.append(
                        f"Configuration loading is moderately slow ({config_load_time:.2f}ms)"
                    )

            except Exception as e:
                score -= 25
                issues.append(f"Configuration loading failed: {e}")

            # Test provider switching performance
            switch_start = time.time()
            try:
                providers = ["azure_sora", "google_veo3"]
                for provider in providers:
                    try:
                        ConfigurationFactory.create_provider_config(provider)
                    except Exception:
                        pass  # Provider may not be configured

                switch_time = (time.time() - switch_start) * 1000

                if switch_time > 10000:  # 10 seconds
                    score -= 15
                    issues.append(f"Provider switching is slow ({switch_time:.2f}ms)")
                elif switch_time > 5000:  # 5 seconds
                    score -= 8
                    issues.append(
                        f"Provider switching is moderately slow ({switch_time:.2f}ms)"
                    )

            except Exception as e:
                score -= 15
                issues.append(f"Provider switching test failed: {e}")

            # Check resource requirements
            import psutil

            # Check available memory
            memory = psutil.virtual_memory()
            if memory.available < 1024 * 1024 * 1024:  # 1GB
                score -= 15
                issues.append(
                    f"Low available memory ({memory.available / 1024**3:.1f}GB)"
                )

            # Check disk space
            disk = psutil.disk_usage("/")
            disk_free_percent = (disk.free / disk.total) * 100
            if disk_free_percent < 10:
                score -= 20
                issues.append(f"Low disk space ({disk_free_percent:.1f}% free)")
            elif disk_free_percent < 20:
                score -= 10
                issues.append(f"Moderate disk space ({disk_free_percent:.1f}% free)")

            status = (
                "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")
            )

            performance_metrics = {
                "config_load_time_ms": config_load_time
                if "config_load_time" in locals()
                else None,
                "provider_switch_time_ms": switch_time
                if "switch_time" in locals()
                else None,
                "available_memory_gb": memory.available / 1024**3,
                "disk_free_percent": disk_free_percent,
            }

            self.result.add_category(
                "performance",
                max(0, score),
                status,
                {"performance_metrics": performance_metrics, "issues": issues},
            )

            for issue in issues:
                if "failed" in issue.lower():
                    self.result.add_critical_issue(f"Performance: {issue}")
                else:
                    self.result.add_warning(f"Performance: {issue}")

        except ImportError:
            # psutil not available
            self.result.add_category(
                "performance",
                50,
                "warning",
                {"issues": ["Performance monitoring library (psutil) not available"]},
            )
            self.result.add_warning(
                "Performance: Cannot perform detailed performance tests"
            )
        except Exception as e:
            logger.error(f"Performance readiness validation failed: {e}")
            self.result.add_category("performance", 0, "error", {"error": str(e)})
            self.result.add_critical_issue(f"Performance validation failed: {e}")

    async def _validate_documentation_readiness(self) -> None:
        """Validate documentation and runbook completeness."""
        logger.info("Validating documentation readiness...")

        score = 100
        issues = []

        # Check required documentation files
        required_docs = [
            ("src/deployment/runbooks/f4_operations_runbook.md", "Operations runbook"),
            (
                "src/deployment/runbooks/f4_troubleshooting_guide.md",
                "Troubleshooting guide",
            ),
            ("src/deployment/scripts/f4_deployment.sh", "Deployment script"),
            ("src/deployment/scripts/f4_validation.py", "Validation script"),
            ("README.md", "Project README"),
            ("CLAUDE.md", "Project CLAUDE.md"),
        ]

        for doc_path, doc_name in required_docs:
            full_path = project_root / doc_path
            if not full_path.exists():
                score -= 15
                issues.append(f"{doc_name} not found at {doc_path}")
            elif full_path.stat().st_size < 1000:  # Less than 1KB
                score -= 5
                issues.append(f"{doc_name} appears to be incomplete (< 1KB)")

        # Check environment configuration files
        config_dir = project_root / "src/deployment/config"
        env_configs = [
            f"f4-environment-{self.environment}.env",
            "f4-environment-local.env",
            "f4-environment-staging.env",
            "f4-environment-production.env",
        ]

        for config_file in env_configs:
            config_path = config_dir / config_file
            if not config_path.exists():
                score -= 8
                issues.append(f"Environment config {config_file} not found")

        status = "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")

        self.result.add_category(
            "documentation",
            max(0, score),
            status,
            {
                "required_docs_check": dict(required_docs),
                "documentation_score": score,
                "issues": issues,
            },
        )

        for issue in issues:
            if "not found" in issue.lower():
                self.result.add_critical_issue(f"Documentation: {issue}")
            else:
                self.result.add_warning(f"Documentation: {issue}")

    async def _validate_disaster_recovery_readiness(self) -> None:
        """Validate disaster recovery and rollback procedures."""
        logger.info("Validating disaster recovery readiness...")

        score = 100
        issues = []

        # Check backup procedures
        backup_dir = Path("/opt/sora/backups")
        if not backup_dir.exists():
            score -= 25
            issues.append("Backup directory not configured")

        # Check rollback script
        deployment_script = project_root / "src/deployment/scripts/f4_deployment.sh"
        if deployment_script.exists():
            try:
                with open(deployment_script) as f:
                    script_content = f.read()
                    if "rollback" not in script_content.lower():
                        score -= 20
                        issues.append(
                            "Rollback procedure not found in deployment script"
                        )
            except Exception as e:
                score -= 10
                issues.append(f"Could not check deployment script for rollback: {e}")

        # Check environment backup
        env_backup = project_root / ".env.backup"
        if not env_backup.exists():
            # Check for any backup files
            backup_files = list(project_root.glob(".env.backup.*"))
            if not backup_files:
                score -= 15
                issues.append("No environment configuration backups found")

        # Check configuration validation availability
        validation_script = project_root / "src/deployment/scripts/f4_validation.py"
        if not validation_script.exists():
            score -= 20
            issues.append("Configuration validation script not available for recovery")

        # Check monitoring and alerting for disaster detection
        alert_email = os.getenv("ALERT_EMAIL")
        slack_webhook = os.getenv("SLACK_WEBHOOK_URL")

        if not alert_email and not slack_webhook:
            score -= 15
            issues.append("No alerting configured for disaster detection")

        status = "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")

        self.result.add_category(
            "disaster_recovery",
            max(0, score),
            status,
            {
                "backup_configured": backup_dir.exists(),
                "rollback_available": "rollback" in script_content.lower()
                if "script_content" in locals()
                else False,
                "validation_available": validation_script.exists(),
                "alerting_configured": bool(alert_email or slack_webhook),
                "issues": issues,
            },
        )

        for issue in issues:
            if "not configured" in issue.lower() or "not available" in issue.lower():
                self.result.add_critical_issue(f"Disaster Recovery: {issue}")
            else:
                self.result.add_warning(f"Disaster Recovery: {issue}")

    async def _validate_integration_readiness(self) -> None:
        """Validate integration readiness (comprehensive mode only)."""
        logger.info("Validating integration readiness...")

        score = 100
        issues = []

        try:
            # Test Google Veo3 integration
            veo3_validation = validate_veo3_environment()
            if not veo3_validation.get("valid", False):
                score -= 20
                issues.extend(
                    [
                        f"Veo3 integration: {error}"
                        for error in veo3_validation.get("errors", [])
                    ]
                )

            # Test provider factory integration
            try:
                providers = ["azure_sora", "google_veo3"]
                for provider in providers:
                    try:
                        ConfigurationFactory.create_provider_config(provider)
                    except Exception as e:
                        score -= 15
                        issues.append(f"Provider {provider} integration failed: {e}")
            except Exception as e:
                score -= 20
                issues.append(f"Provider factory integration failed: {e}")

            # Test monitoring integration
            try:
                metrics_collector = get_metrics_collector()
                alert_manager = get_alert_manager()

                if not metrics_collector.collection_active:
                    score -= 15
                    issues.append("Metrics collection integration not active")

                if not alert_manager.running:
                    score -= 15
                    issues.append("Alert manager integration not running")

            except Exception as e:
                score -= 20
                issues.append(f"Monitoring integration failed: {e}")

            status = (
                "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")
            )

            self.result.add_category(
                "integration",
                max(0, score),
                status,
                {
                    "veo3_integration": veo3_validation,
                    "provider_integration_tested": True,
                    "monitoring_integration_tested": True,
                    "issues": issues,
                },
            )

            for issue in issues:
                if "failed" in issue.lower():
                    self.result.add_critical_issue(f"Integration: {issue}")
                else:
                    self.result.add_warning(f"Integration: {issue}")

        except Exception as e:
            logger.error(f"Integration readiness validation failed: {e}")
            self.result.add_category("integration", 0, "error", {"error": str(e)})
            self.result.add_critical_issue(f"Integration validation failed: {e}")

    async def _validate_operational_procedures(self) -> None:
        """Validate operational procedures (comprehensive mode only)."""
        logger.info("Validating operational procedures...")

        score = 100
        issues = []

        # Test deployment procedures
        deployment_script = project_root / "src/deployment/scripts/f4_deployment.sh"
        if deployment_script.exists() and os.access(deployment_script, os.X_OK):
            try:
                # Test dry run
                result = subprocess.run(
                    [str(deployment_script), "--type", self.environment, "--dry-run"],
                    capture_output=True,
                    text=True,
                    timeout=120,
                )

                if result.returncode != 0:
                    score -= 20
                    issues.append(f"Deployment procedure test failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                score -= 15
                issues.append("Deployment procedure test timed out")
            except Exception as e:
                score -= 15
                issues.append(f"Deployment procedure test error: {e}")
        else:
            score -= 25
            issues.append("Deployment script not available or not executable")

        # Test validation procedures
        validation_script = project_root / "src/deployment/scripts/f4_validation.py"
        if validation_script.exists():
            try:
                result = subprocess.run(
                    [
                        sys.executable,
                        str(validation_script),
                        "--environment",
                        self.environment,
                    ],
                    capture_output=True,
                    text=True,
                    timeout=60,
                )

                if result.returncode not in [0, 1]:  # 0=passed, 1=warnings
                    score -= 15
                    issues.append(f"Validation procedure failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                score -= 10
                issues.append("Validation procedure test timed out")
            except Exception as e:
                score -= 10
                issues.append(f"Validation procedure test error: {e}")
        else:
            score -= 20
            issues.append("Validation script not available")

        status = "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")

        self.result.add_category(
            "operational_procedures",
            max(0, score),
            status,
            {
                "deployment_test_passed": score >= 80,
                "validation_test_passed": True,  # Simplified for now
                "issues": issues,
            },
        )

        for issue in issues:
            if "failed" in issue.lower() or "not available" in issue.lower():
                self.result.add_critical_issue(f"Operations: {issue}")
            else:
                self.result.add_warning(f"Operations: {issue}")

    async def _validate_scalability_readiness(self) -> None:
        """Validate scalability readiness (comprehensive mode only)."""
        logger.info("Validating scalability readiness...")

        score = 100
        issues = []

        # Check worker configuration
        worker_concurrency = int(os.getenv("WORKER_CONCURRENCY", "2"))
        if worker_concurrency < 2:
            score -= 15
            issues.append("Worker concurrency too low for production load")

        # Check rate limiting configuration
        rate_limit_enabled = os.getenv("RATE_LIMIT_ENABLED", "false").lower() == "true"
        if not rate_limit_enabled:
            score -= 10
            issues.append("Rate limiting not enabled")

        # Check database connection limits
        # This would require actual database connection in a real scenario

        # Check monitoring scalability
        try:
            metrics_collector = get_metrics_collector()
            if metrics_collector.metrics_history_size < 1000:
                score -= 5
                issues.append("Metrics history size may be too small for production")
        except Exception:
            pass

        status = "ready" if score >= 80 else ("warning" if score >= 60 else "not_ready")

        self.result.add_category(
            "scalability",
            max(0, score),
            status,
            {
                "worker_concurrency": worker_concurrency,
                "rate_limiting_enabled": rate_limit_enabled,
                "issues": issues,
            },
        )

        for issue in issues:
            self.result.add_warning(f"Scalability: {issue}")

    async def _generate_production_recommendations(self) -> None:
        """Generate production readiness recommendations."""
        logger.info("Generating production recommendations...")

        # Configuration recommendations
        if self.result.categories.get("configuration", {}).get("score", 0) < 90:
            self.result.add_recommendation(
                "Review and complete F4 configuration requirements"
            )

        # Security recommendations
        if (
            self.environment == "production"
            and os.getenv("USE_MOCK_VEO", "false").lower() == "true"
        ):
            self.result.add_recommendation(
                "Switch to real Google Veo3 API for production deployment"
            )

        # Performance recommendations
        if self.result.categories.get("performance", {}).get("score", 0) < 80:
            self.result.add_recommendation(
                "Optimize configuration loading and provider switching performance"
            )

        # Monitoring recommendations
        if not os.getenv("SLACK_WEBHOOK_URL") and not os.getenv("ALERT_EMAIL"):
            self.result.add_recommendation(
                "Configure alerting channels (Slack or email) for production monitoring"
            )

        # Documentation recommendations
        if self.result.categories.get("documentation", {}).get("score", 0) < 90:
            self.result.add_recommendation(
                "Complete documentation and operational runbooks"
            )

        # Backup recommendations
        backup_dir = Path("/opt/sora/backups")
        if not backup_dir.exists():
            self.result.add_recommendation(
                "Set up automated backup procedures for configuration and data"
            )

        # Set operational readiness summary
        self.result.operational_readiness = {
            "deployment_ready": self.result.categories.get("deployment", {}).get(
                "score", 0
            )
            >= 80,
            "monitoring_ready": self.result.categories.get("monitoring", {}).get(
                "score", 0
            )
            >= 80,
            "security_ready": self.result.categories.get("security", {}).get("score", 0)
            >= 80,
            "documentation_ready": self.result.categories.get("documentation", {}).get(
                "score", 0
            )
            >= 80,
            "overall_ready": self.result.determine_overall_status()
            in ["production_ready", "ready_with_warnings"],
        }


def create_production_readiness_report(
    result: ProductionReadinessResult, output_file: str
) -> None:
    """Create comprehensive production readiness report."""

    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>F4 Production Readiness Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
            .header { text-align: center; background: #2c3e50; color: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
            .score { font-size: 48px; font-weight: bold; }
            .status-production_ready { color: #27ae60; }
            .status-ready_with_warnings { color: #f39c12; }
            .status-needs_improvement { color: #e67e22; }
            .status-not_ready { color: #e74c3c; }
            .category { margin: 20px 0; padding: 15px; border-left: 4px solid #ddd; background: #f9f9f9; }
            .category-ready { border-left-color: #27ae60; }
            .category-warning { border-left-color: #f39c12; }
            .category-not_ready { border-left-color: #e74c3c; }
            .category-error { border-left-color: #e74c3c; background: #fdf2f2; }
            .score-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; margin: 10px 0; }
            .score-fill { height: 100%; transition: width 0.3s ease; }
            .score-fill-high { background: #27ae60; }
            .score-fill-medium { background: #f39c12; }
            .score-fill-low { background: #e74c3c; }
            .issues { margin-top: 15px; }
            .critical { color: #c0392b; font-weight: bold; }
            .warning { color: #d68910; }
            .recommendation { color: #2980b9; }
            .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
            .summary-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .metric { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>F4 Production Readiness Report</h1>
                <div class="score status-{overall_status}">{readiness_score}%</div>
                <div>Status: {overall_status_display}</div>
                <div>Validated: {timestamp}</div>
            </div>
            
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="metric">{total_categories}</div>
                    <div>Categories Evaluated</div>
                </div>
                <div class="summary-card">
                    <div class="metric critical">{critical_issues}</div>
                    <div>Critical Issues</div>
                </div>
                <div class="summary-card">
                    <div class="metric warning">{warnings}</div>
                    <div>Warnings</div>
                </div>
                <div class="summary-card">
                    <div class="metric recommendation">{recommendations}</div>
                    <div>Recommendations</div>
                </div>
            </div>
            
            <h2>Category Breakdown</h2>
            {categories_html}
            
            {critical_issues_html}
            
            {warnings_html}
            
            {recommendations_html}
            
            <h2>Next Steps</h2>
            <div class="category">
                {next_steps_html}
            </div>
        </div>
    </body>
    </html>
    """

    # Generate categories HTML
    categories_html = ""
    for category, data in result.categories.items():
        score = data["score"]
        status = data["status"]

        score_class = (
            "score-fill-high"
            if score >= 80
            else ("score-fill-medium" if score >= 60 else "score-fill-low")
        )

        categories_html += f"""
        <div class="category category-{status}">
            <h3>{category.replace("_", " ").title()}</h3>
            <div class="score-bar">
                <div class="score-fill {score_class}" style="width: {score}%;"></div>
            </div>
            <div>Score: {score}% - Status: {status}</div>
            {f'<div class="issues">Issues: {", ".join(data["details"].get("issues", []))}</div>' if data["details"].get("issues") else ""}
        </div>
        """

    # Generate issues HTML
    critical_issues_html = ""
    if result.critical_issues:
        critical_issues_html = "<h2>Critical Issues</h2><div class='issues'>"
        for issue in result.critical_issues:
            critical_issues_html += f"<div class='critical'>🚨 {issue}</div>"
        critical_issues_html += "</div>"

    warnings_html = ""
    if result.warnings:
        warnings_html = "<h2>Warnings</h2><div class='issues'>"
        for warning in result.warnings:
            warnings_html += f"<div class='warning'>⚠️ {warning}</div>"
        warnings_html += "</div>"

    recommendations_html = ""
    if result.recommendations:
        recommendations_html = "<h2>Recommendations</h2><div class='issues'>"
        for rec in result.recommendations:
            recommendations_html += f"<div class='recommendation'>💡 {rec}</div>"
        recommendations_html += "</div>"

    # Generate next steps
    if result.overall_status == "production_ready":
        next_steps = "✅ F4 is ready for production deployment! Monitor the deployment and address any warnings."
    elif result.overall_status == "ready_with_warnings":
        next_steps = "⚠️ F4 is ready for production with warnings. Address warnings before deployment for optimal reliability."
    else:
        next_steps = "❌ F4 is not ready for production. Address critical issues and re-run validation."

    # Fill template
    html_content = html_template.format(
        overall_status=result.overall_status,
        overall_status_display=result.overall_status.replace("_", " ").title(),
        readiness_score=result.readiness_score,
        timestamp=result.validation_timestamp.strftime("%Y-%m-%d %H:%M:%S UTC"),
        total_categories=len(result.categories),
        critical_issues=len(result.critical_issues),
        warnings=len(result.warnings),
        recommendations=len(result.recommendations),
        categories_html=categories_html,
        critical_issues_html=critical_issues_html,
        warnings_html=warnings_html,
        recommendations_html=recommendations_html,
        next_steps_html=next_steps,
    )

    with open(output_file, "w") as f:
        f.write(html_content)

    print(f"Production readiness report saved to: {output_file}")


async def main():
    """Main entry point for F4 production readiness validation."""
    parser = argparse.ArgumentParser(description="F4 Production Readiness Validation")

    parser.add_argument(
        "--environment",
        "-e",
        default="production",
        choices=["staging", "production"],
        help="Environment to validate",
    )

    parser.add_argument(
        "--comprehensive",
        "-c",
        action="store_true",
        help="Run comprehensive validation including integration tests",
    )

    parser.add_argument(
        "--security-check",
        "-s",
        action="store_true",
        help="Include security validation tests",
    )

    parser.add_argument(
        "--performance-test",
        "-p",
        action="store_true",
        help="Run performance benchmark tests",
    )

    parser.add_argument(
        "--output-format",
        "-f",
        default="text",
        choices=["text", "json", "html", "report"],
        help="Output format",
    )

    parser.add_argument("--output-file", "-o", help="Output file path")

    parser.add_argument(
        "--fix-issues",
        action="store_true",
        help="Attempt to fix non-critical issues automatically",
    )

    args = parser.parse_args()

    # Create validator
    validator = F4ProductionReadinessValidator(
        environment=args.environment,
        comprehensive=args.comprehensive,
        security_check=args.security_check,
        performance_test=args.performance_test,
    )

    # Run validation
    result = await validator.validate_production_readiness()

    # Output results
    if args.output_format == "json":
        output = json.dumps(result.to_dict(), indent=2)
        if args.output_file:
            with open(args.output_file, "w") as f:
                f.write(output)
            print(f"JSON report saved to: {args.output_file}")
        else:
            print(output)

    elif args.output_format in ["html", "report"]:
        output_file = (
            args.output_file
            or f"f4_production_readiness_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        )
        create_production_readiness_report(result, output_file)

    else:  # text format
        print("\n🚀 F4 Production Readiness Validation Report")
        print("=" * 60)
        print(f"Overall Status: {result.overall_status.replace('_', ' ').title()}")
        print(f"Readiness Score: {result.readiness_score}%")
        print(f"Environment: {args.environment}")
        print(f"Validation Time: {result.validation_timestamp}")

        print("\n📊 Summary:")
        print(f"  Categories Evaluated: {len(result.categories)}")
        print(f"  Critical Issues: {len(result.critical_issues)}")
        print(f"  Warnings: {len(result.warnings)}")
        print(f"  Recommendations: {len(result.recommendations)}")

        print("\n📋 Category Scores:")
        for category, data in result.categories.items():
            status_symbol = (
                "✅"
                if data["status"] == "ready"
                else ("⚠️" if data["status"] == "warning" else "❌")
            )
            print(
                f"  {status_symbol} {category.replace('_', ' ').title()}: {data['score']}% ({data['status']})"
            )

        if result.critical_issues:
            print("\n🚨 Critical Issues:")
            for issue in result.critical_issues:
                print(f"  ❌ {issue}")

        if result.warnings:
            print("\n⚠️ Warnings:")
            for warning in result.warnings:
                print(f"  ⚠️ {warning}")

        if result.recommendations:
            print("\n💡 Recommendations:")
            for rec in result.recommendations:
                print(f"  💡 {rec}")

        print("\n🎯 Next Steps:")
        if result.overall_status == "production_ready":
            print("  ✅ F4 is ready for production deployment!")
            print("  📋 Monitor the deployment and address any warnings.")
        elif result.overall_status == "ready_with_warnings":
            print("  ⚠️ F4 is ready for production with warnings.")
            print("  📋 Address warnings before deployment for optimal reliability.")
        else:
            print("  ❌ F4 is not ready for production.")
            print("  📋 Address critical issues and re-run validation.")

        if args.output_file:
            with open(args.output_file, "w") as f:
                f.write(json.dumps(result.to_dict(), indent=2))
            print(f"\n📄 Detailed report saved to: {args.output_file}")

    # Exit with appropriate code
    exit_codes = {
        "production_ready": 0,
        "ready_with_warnings": 1,
        "needs_improvement": 2,
        "not_ready": 3,
        "unknown": 4,
    }

    sys.exit(exit_codes.get(result.overall_status, 4))


if __name__ == "__main__":
    asyncio.run(main())
