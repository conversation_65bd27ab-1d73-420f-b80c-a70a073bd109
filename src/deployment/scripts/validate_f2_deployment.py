#!/usr/bin/env python3
"""
F2 Provider Interface & Factory Deployment Validation Script

Validates deployment configuration for both Azure Sora and Google Veo3 providers,
ensures configuration factory integration, and validates provider availability.
"""

import asyncio
import os
import sys
import time
from typing import Dict

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from src.config.factory import ConfigurationFactory
from src.features.video_generation import (
    get_provider_factory,
)

console = Console()


class F2DeploymentValidator:
    """Comprehensive F2 Provider Interface deployment validator."""

    def __init__(self):
        self.console = console
        self.validation_results = {}

    def validate_environment_variables(self) -> Dict[str, Dict[str, bool]]:
        """Validate environment variables for both providers."""
        console.print("\n[bold blue]🔧 Validating Environment Variables[/bold blue]")

        # Azure Sora Required Variables
        azure_vars = {
            "AZURE_OPENAI_ENDPOINT": os.getenv("AZURE_OPENAI_ENDPOINT"),
            "AZURE_OPENAI_API_KEY": os.getenv("AZURE_OPENAI_API_KEY"),
            "AZURE_OPENAI_API_VERSION": os.getenv("AZURE_OPENAI_API_VERSION"),
            "AZURE_OPENAI_DEPLOYMENT_NAME": os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
        }

        # Google Veo3 Variables (optional for mock mode)
        veo3_vars = {
            "GOOGLE_PROJECT_ID": os.getenv("GOOGLE_PROJECT_ID"),
            "GOOGLE_LOCATION": os.getenv("GOOGLE_LOCATION", "us-central1"),
            "USE_MOCK_VEO": os.getenv("USE_MOCK_VEO", "true"),
            "GOOGLE_APPLICATION_CREDENTIALS": os.getenv(
                "GOOGLE_APPLICATION_CREDENTIALS"
            ),
        }

        # Create validation table
        table = Table(title="Environment Variable Validation")
        table.add_column("Provider", style="cyan")
        table.add_column("Variable", style="magenta")
        table.add_column("Status", style="green")
        table.add_column("Value", style="yellow")

        azure_status = {}
        for var, value in azure_vars.items():
            status = "✅ SET" if value else "❌ MISSING"
            display_value = (
                "***SET***" if value and "key" in var.lower() else (value or "Not Set")
            )
            table.add_row("Azure Sora", var, status, display_value)
            azure_status[var] = bool(value)

        veo3_status = {}
        for var, value in veo3_vars.items():
            # For Veo3, some vars are optional if using mock
            is_mock = veo3_vars.get("USE_MOCK_VEO", "true").lower() == "true"
            required = not is_mock and var in [
                "GOOGLE_PROJECT_ID",
                "GOOGLE_APPLICATION_CREDENTIALS",
            ]

            if value:
                status = "✅ SET"
            elif required:
                status = "❌ MISSING (Required)"
            else:
                status = "🟡 OPTIONAL"

            display_value = (
                "***SET***"
                if value and "credentials" in var.lower()
                else (value or "Not Set")
            )
            table.add_row("Google Veo3", var, status, display_value)
            veo3_status[var] = bool(value) or not required

        console.print(table)

        return {"azure_sora": azure_status, "google_veo3": veo3_status}

    def validate_configuration_factories(self) -> Dict[str, bool]:
        """Validate configuration factory integration."""
        console.print("\n[bold blue]🏭 Validating Configuration Factories[/bold blue]")

        results = {}

        # Test Azure configuration factory
        try:
            azure_config = ConfigurationFactory.get_azure_config()
            results["azure_config_factory"] = True
            console.print("✅ Azure configuration factory: [green]Working[/green]")
            console.print(
                f"   Endpoint configured: {'✅' if azure_config.get('endpoint') else '❌'}"
            )
            console.print(
                f"   API key configured: {'✅' if azure_config.get('api_key') else '❌'}"
            )
        except Exception as e:
            results["azure_config_factory"] = False
            console.print(f"❌ Azure configuration factory: [red]Failed[/red] - {e}")

        # Test Veo3 configuration factory
        try:
            veo3_config = ConfigurationFactory.create_veo3_config()
            results["veo3_config_factory"] = True
            console.print("✅ Veo3 configuration factory: [green]Working[/green]")
            console.print(f"   Project ID: {veo3_config.project_id or 'Not set'}")
            console.print(f"   Use Mock: {veo3_config.use_mock}")
        except Exception as e:
            results["veo3_config_factory"] = False
            console.print(f"❌ Veo3 configuration factory: [red]Failed[/red] - {e}")

        return results

    def validate_provider_availability(self) -> Dict[str, bool]:
        """Validate provider availability and creation."""
        console.print("\n[bold blue]🔌 Validating Provider Availability[/bold blue]")

        results = {}

        try:
            factory = get_provider_factory()
            available_providers = factory.get_available_providers()

            table = Table(title="Provider Availability")
            table.add_column("Provider", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Details", style="yellow")

            # Check Azure Sora
            azure_available = "azure_sora" in available_providers
            results["azure_sora_available"] = azure_available

            if azure_available:
                try:
                    azure_provider = factory.create_provider("azure_sora")
                    results["azure_sora_creatable"] = True
                    table.add_row(
                        "Azure Sora",
                        "✅ Available",
                        f"Provider: {azure_provider.provider_name}",
                    )
                except Exception as e:
                    results["azure_sora_creatable"] = False
                    table.add_row(
                        "Azure Sora", "❌ Error", f"Creation failed: {str(e)[:50]}"
                    )
            else:
                results["azure_sora_creatable"] = False
                table.add_row("Azure Sora", "❌ Unavailable", "Configuration missing")

            # Check Google Veo3
            veo3_available = "google_veo3" in available_providers
            results["google_veo3_available"] = veo3_available

            if veo3_available:
                try:
                    veo3_provider = factory.create_provider("google_veo3")
                    results["google_veo3_creatable"] = True
                    table.add_row(
                        "Google Veo3",
                        "✅ Available",
                        f"Provider: {veo3_provider.provider_name}",
                    )
                except Exception as e:
                    results["google_veo3_creatable"] = False
                    table.add_row(
                        "Google Veo3", "❌ Error", f"Creation failed: {str(e)[:50]}"
                    )
            else:
                results["google_veo3_creatable"] = False
                table.add_row(
                    "Google Veo3", "❌ Unavailable", "Configuration missing or disabled"
                )

            console.print(table)
            console.print(
                f"\n[bold]Available Providers:[/bold] {', '.join(available_providers) or 'None'}"
            )

        except Exception as e:
            console.print(f"❌ Provider factory error: [red]{e}[/red]")
            results["factory_error"] = str(e)

        return results

    async def validate_provider_health(self) -> Dict[str, Dict[str, bool]]:
        """Validate provider health checks."""
        console.print("\n[bold blue]🏥 Validating Provider Health Checks[/bold blue]")

        try:
            factory = get_provider_factory()
            health_status = await factory.health_check_all_providers()

            table = Table(title="Provider Health Status")
            table.add_column("Provider", style="cyan")
            table.add_column("Health Check", style="magenta")
            table.add_column("Status", style="green")

            for provider_name, health_checks in health_status.items():
                for check_name, check_result in health_checks.items():
                    if check_name != "error":
                        status = "✅ Pass" if check_result else "❌ Fail"
                        table.add_row(provider_name, check_name, status)

                if "error" in health_checks:
                    table.add_row(
                        provider_name, "error", f"❌ {health_checks['error']}"
                    )

            console.print(table)
            return health_status

        except Exception as e:
            console.print(f"❌ Health check error: [red]{e}[/red]")
            return {"error": str(e)}

    async def validate_performance_metrics(self) -> Dict[str, float]:
        """Validate provider performance metrics."""
        console.print("\n[bold blue]⚡ Validating Performance Metrics[/bold blue]")

        results = {}

        try:
            factory = get_provider_factory()

            # Test provider creation performance
            start_time = time.time()
            if "azure_sora" in factory.get_available_providers():
                provider = factory.create_provider("azure_sora")
                creation_time = (time.time() - start_time) * 1000
                results["azure_creation_time_ms"] = creation_time

                # Test cached creation
                start_time = time.time()
                cached_provider = factory.create_provider("azure_sora")
                cached_time = (time.time() - start_time) * 1000
                results["azure_cached_time_ms"] = cached_time

                console.print(
                    f"Azure Sora - First creation: [yellow]{creation_time:.2f}ms[/yellow]"
                )
                console.print(
                    f"Azure Sora - Cached creation: [yellow]{cached_time:.2f}ms[/yellow]"
                )
                console.print(
                    f"Caching benefit: [green]{creation_time - cached_time:.2f}ms[/green]"
                )

                # Validate performance targets
                if creation_time < 50:
                    console.print(
                        "✅ Provider creation performance: [green]Meets <50ms target[/green]"
                    )
                else:
                    console.print(
                        "⚠️ Provider creation performance: [yellow]Exceeds 50ms target[/yellow]"
                    )

            if "google_veo3" in factory.get_available_providers():
                start_time = time.time()
                veo3_provider = factory.create_provider("google_veo3")
                veo3_creation_time = (time.time() - start_time) * 1000
                results["veo3_creation_time_ms"] = veo3_creation_time

                console.print(
                    f"Google Veo3 - Creation: [yellow]{veo3_creation_time:.2f}ms[/yellow]"
                )

        except Exception as e:
            console.print(f"❌ Performance validation error: [red]{e}[/red]")
            results["error"] = str(e)

        return results

    async def validate_docker_integration(self) -> Dict[str, bool]:
        """Validate Docker deployment compatibility."""
        console.print("\n[bold blue]🐳 Validating Docker Integration[/bold blue]")

        results = {}

        # Check if running in Docker environment
        in_docker = (
            os.path.exists("/.dockerenv") or os.getenv("DOCKER_CONTAINER") == "true"
        )
        results["running_in_docker"] = in_docker

        if in_docker:
            console.print("✅ Running in Docker environment")

            # Check Docker-specific environment variables
            docker_vars = ["DATABASE_URL", "CELERY_BROKER_URL", "CELERY_RESULT_BACKEND"]

            docker_config_valid = True
            for var in docker_vars:
                value = os.getenv(var)
                if value:
                    console.print(f"✅ {var}: Configured")
                else:
                    console.print(f"❌ {var}: Missing")
                    docker_config_valid = False

            results["docker_config_valid"] = docker_config_valid

        else:
            console.print("ℹ️ Not running in Docker environment")
            results["docker_config_valid"] = True  # Not applicable

        # Test provider factory in current environment
        try:
            factory = get_provider_factory()
            available = factory.get_available_providers()
            results["providers_available_in_env"] = len(available) > 0
            console.print(
                f"✅ Providers available in environment: {', '.join(available)}"
            )
        except Exception as e:
            results["providers_available_in_env"] = False
            console.print(f"❌ Provider factory error in environment: {e}")

        return results

    async def validate_concurrent_load(self) -> Dict[str, bool]:
        """Validate provider factory under concurrent load."""
        console.print("\n[bold blue]🔄 Validating Concurrent Load Handling[/bold blue]")

        results = {}

        try:
            factory = get_provider_factory()
            available_providers = factory.get_available_providers()

            if not available_providers:
                console.print("⚠️ No providers available for load testing")
                return {"no_providers": True}

            provider_name = available_providers[0]  # Use first available provider

            # Test concurrent provider creation
            async def create_provider_test():
                try:
                    provider = factory.create_provider(provider_name)
                    return True
                except Exception:
                    return False

            console.print(
                f"Testing concurrent provider creation with {provider_name}..."
            )

            # Create 10 concurrent provider instances
            tasks = [create_provider_test() for _ in range(10)]
            results_list = await asyncio.gather(*tasks, return_exceptions=True)

            successful_creations = sum(1 for r in results_list if r is True)
            total_attempts = len(results_list)

            results["concurrent_creation_success_rate"] = (
                successful_creations / total_attempts
            )
            results["concurrent_creations_successful"] = (
                successful_creations == total_attempts
            )

            console.print(
                f"Concurrent creation results: {successful_creations}/{total_attempts} successful"
            )

            if successful_creations == total_attempts:
                console.print(
                    "✅ Concurrent load handling: [green]All creations successful[/green]"
                )
            else:
                console.print(
                    f"⚠️ Concurrent load handling: [yellow]{successful_creations}/{total_attempts} successful[/yellow]"
                )

        except Exception as e:
            console.print(f"❌ Concurrent load validation error: [red]{e}[/red]")
            results["error"] = str(e)

        return results

    def generate_deployment_report(self, all_results: Dict) -> None:
        """Generate comprehensive deployment validation report."""
        console.print("\n" + "=" * 80)
        console.print(
            "[bold blue]F2 Provider Interface & Factory Deployment Report[/bold blue]"
        )
        console.print("=" * 80)

        # Overall status calculation
        critical_checks = [
            all_results.get("environment_vars", {})
            .get("azure_sora", {})
            .get("AZURE_OPENAI_API_KEY", False),
            all_results.get("configuration_factories", {}).get(
                "azure_config_factory", False
            ),
            all_results.get("provider_availability", {}).get(
                "azure_sora_available", False
            ),
            all_results.get("provider_availability", {}).get("factory_error") is None,
        ]

        warning_checks = [
            all_results.get("performance_metrics", {}).get(
                "azure_creation_time_ms", 100
            )
            < 50,
            all_results.get("concurrent_load", {}).get(
                "concurrent_creations_successful", False
            ),
        ]

        critical_passed = all(critical_checks)
        warnings_passed = all(warning_checks)

        if critical_passed and warnings_passed:
            status = "[bold green]✅ PRODUCTION READY[/bold green]"
            status_color = "green"
        elif critical_passed:
            status = "[bold yellow]⚠️ READY WITH WARNINGS[/bold yellow]"
            status_color = "yellow"
        else:
            status = "[bold red]❌ NOT READY[/bold red]"
            status_color = "red"

        console.print(f"\n[bold]Overall Status:[/bold] {status}")

        # Detailed results
        if "environment_vars" in all_results:
            env_vars = all_results["environment_vars"]
            azure_configured = all(env_vars.get("azure_sora", {}).values())
            veo3_configured = all(env_vars.get("google_veo3", {}).values())

            console.print("\n[bold]Environment Configuration:[/bold]")
            console.print(
                f"  Azure Sora: {'✅ Fully configured' if azure_configured else '❌ Missing configuration'}"
            )
            console.print(
                f"  Google Veo3: {'✅ Fully configured' if veo3_configured else '🟡 Partial/Mock configuration'}"
            )

        if "provider_availability" in all_results:
            avail = all_results["provider_availability"]
            console.print("\n[bold]Provider Availability:[/bold]")
            console.print(
                f"  Azure Sora: {'✅ Available' if avail.get('azure_sora_available') else '❌ Unavailable'}"
            )
            console.print(
                f"  Google Veo3: {'✅ Available' if avail.get('google_veo3_available') else '❌ Unavailable'}"
            )

        if "performance_metrics" in all_results:
            perf = all_results["performance_metrics"]
            console.print("\n[bold]Performance Metrics:[/bold]")
            if "azure_creation_time_ms" in perf:
                creation_time = perf["azure_creation_time_ms"]
                status_icon = "✅" if creation_time < 50 else "⚠️"
                console.print(
                    f"  Provider creation: {status_icon} {creation_time:.2f}ms"
                )

            if "azure_cached_time_ms" in perf:
                cached_time = perf["azure_cached_time_ms"]
                console.print(f"  Cached creation: ✅ {cached_time:.2f}ms")

        # Recommendations
        console.print("\n[bold]Recommendations:[/bold]")

        if not critical_passed:
            console.print(
                "  🔧 [red]CRITICAL:[/red] Fix missing Azure Sora configuration"
            )
            console.print(
                "     - Set AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_KEY, etc."
            )
            console.print("     - Verify configuration factory integration")

        if critical_passed and not warnings_passed:
            console.print(
                "  ⚡ [yellow]PERFORMANCE:[/yellow] Consider optimizing provider creation"
            )
            console.print("     - Review provider caching implementation")
            console.print("     - Check concurrent load handling")

        if critical_passed and warnings_passed:
            console.print(
                "  🚀 [green]READY:[/green] All systems optimal for production deployment"
            )
            console.print("     - Provider factory is production-ready")
            console.print("     - Performance targets met")
            console.print("     - Concurrent handling validated")

        # Next steps
        console.print("\n[bold]Next Steps:[/bold]")
        console.print("  1. Review deployment checklist in F2 module documentation")
        console.print("  2. Test provider switching and fallback scenarios")
        console.print("  3. Validate monitoring integration with existing systems")
        console.print("  4. Deploy to staging environment for final validation")

        console.print("\n" + "=" * 80)


@click.command()
@click.option(
    "--environment",
    "-e",
    default="auto",
    help="Environment type: auto, local, docker, production",
)
@click.option(
    "--detailed",
    "-d",
    is_flag=True,
    help="Include detailed performance and load testing",
)
@click.option(
    "--fix-config",
    "-f",
    is_flag=True,
    help="Attempt to fix common configuration issues",
)
async def main(environment: str, detailed: bool, fix_config: bool):
    """F2 Provider Interface & Factory deployment validation."""

    console.print(
        Panel.fit(
            "[bold blue]F2 Provider Interface & Factory[/bold blue]\n"
            "[white]Deployment Configuration Validator[/white]\n\n"
            "Validates Azure Sora and Google Veo3 provider configuration,\n"
            "performance metrics, and production readiness.",
            title="🚀 Deployment Validator",
            border_style="blue",
        )
    )

    validator = F2DeploymentValidator()
    all_results = {}

    # Environment variable validation
    all_results["environment_vars"] = validator.validate_environment_variables()

    # Configuration factory validation
    all_results["configuration_factories"] = (
        validator.validate_configuration_factories()
    )

    # Provider availability validation
    all_results["provider_availability"] = validator.validate_provider_availability()

    # Health check validation
    all_results["health_checks"] = await validator.validate_provider_health()

    # Performance validation
    all_results["performance_metrics"] = await validator.validate_performance_metrics()

    # Docker integration validation
    all_results["docker_integration"] = await validator.validate_docker_integration()

    if detailed:
        # Concurrent load validation
        all_results["concurrent_load"] = await validator.validate_concurrent_load()

    # Generate comprehensive report
    validator.generate_deployment_report(all_results)

    # Fix configuration if requested
    if fix_config:
        console.print("\n[bold blue]🔧 Configuration Fix Suggestions[/bold blue]")
        console.print("Consider creating .env.local with missing variables:")
        console.print("  AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/")
        console.print("  AZURE_OPENAI_API_KEY=your-api-key")
        console.print("  USE_MOCK_VEO=true  # For Google Veo3 mock provider")


if __name__ == "__main__":
    asyncio.run(main())
