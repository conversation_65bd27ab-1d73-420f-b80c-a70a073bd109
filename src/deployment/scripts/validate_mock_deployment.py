#!/usr/bin/env python3
"""
F3 Mock Veo3 Provider Deployment Validation

Comprehensive validation of mock provider deployment readiness
including Docker environment testing and operational validation.
"""

import asyncio
import os
import sys
import time
from typing import Any, Dict

import click
import requests
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

console = Console()


class MockVeo3DeploymentValidator:
    """
    Deployment validation for F3 Mock Veo3 Provider.

    Validates deployed services, tests operational functionality,
    and verifies end-to-end mock provider workflows.
    """

    def __init__(self, base_url: str = "http://localhost:5001") -> None:
        """
        Initialize deployment validator.

        Args:
            base_url: Base URL for the deployed application
        """
        self.base_url = base_url.rstrip("/")
        self.console = console

        # Validation test scenarios
        self.test_scenarios = [
            {
                "name": "mock_text_generation",
                "prompt": "A peaceful sunset over mountains",
                "duration": 5,
                "expected_provider": "google_veo3",
            },
            {
                "name": "mock_timing_validation",
                "prompt": "Fast test video for timing validation",
                "duration": 3,
                "expected_latency_ms": 2000,
            },
        ]

    async def validate_service_health(self) -> Dict[str, Any]:
        """
        Validate deployed service health endpoints.

        Returns:
            Dict[str, Any]: Service health validation results
        """
        console.print("\n[bold blue]🏥 Validating Service Health[/bold blue]")

        results = {}

        # Test main health endpoint
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            health_accessible = response.status_code == 200

            if health_accessible:
                health_data = response.json()
                results["main_health"] = {
                    "accessible": True,
                    "status": health_data.get("overall_status", "unknown"),
                    "response_time_ms": response.elapsed.total_seconds() * 1000,
                    "components": health_data.get("components", {}),
                }
                console.print(
                    f"✅ Main health endpoint: {health_data.get('overall_status', 'unknown')}"
                )
            else:
                results["main_health"] = {
                    "accessible": False,
                    "status_code": response.status_code,
                }
                console.print(f"❌ Main health endpoint failed: {response.status_code}")

        except Exception as e:
            results["main_health"] = {
                "accessible": False,
                "error": str(e),
            }
            console.print(f"❌ Health endpoint error: {e}")

        # Test mock provider specific health
        try:
            response = requests.get(
                f"{self.base_url}/health?check=mock_veo3", timeout=10
            )
            mock_health_accessible = response.status_code == 200

            if mock_health_accessible:
                mock_health_data = response.json()
                results["mock_veo3_health"] = {
                    "accessible": True,
                    "mock_provider_status": mock_health_data.get("components", {}).get(
                        "mock_veo3_provider", {}
                    ),
                    "response_time_ms": response.elapsed.total_seconds() * 1000,
                }
                console.print("✅ Mock Veo3 health endpoint accessible")
            else:
                results["mock_veo3_health"] = {
                    "accessible": False,
                    "status_code": response.status_code,
                }
                console.print(f"❌ Mock Veo3 health failed: {response.status_code}")

        except Exception as e:
            results["mock_veo3_health"] = {
                "accessible": False,
                "error": str(e),
            }
            console.print(f"❌ Mock health endpoint error: {e}")

        return results

    async def validate_docker_services(self) -> Dict[str, Any]:
        """
        Validate Docker service deployment status.

        Returns:
            Dict[str, Any]: Docker services validation results
        """
        console.print("\n[bold blue]🐳 Validating Docker Services[/bold blue]")

        results = {}

        # Check if Docker Compose is available
        try:
            docker_compose_result = os.system(
                "docker-compose --version > /dev/null 2>&1"
            )
            docker_compose_available = docker_compose_result == 0

            results["docker_compose_available"] = docker_compose_available

            if not docker_compose_available:
                console.print("❌ Docker Compose not available")
                return results
            else:
                console.print("✅ Docker Compose available")

        except Exception as e:
            results["docker_compose_available"] = False
            console.print(f"❌ Docker Compose check failed: {e}")
            return results

        # Check running services
        expected_services = [
            "sora-postgres-simple",
            "sora-redis-simple",
            "sora-app-simple",
            "sora-worker-simple",
            "sora-nginx-simple",
        ]

        service_statuses = {}

        for service in expected_services:
            try:
                # Check if container is running
                check_cmd = f"docker ps --filter name={service} --format '{{.Status}}' | head -1"
                result = os.popen(check_cmd).read().strip()

                is_running = bool(result and "Up" in result)
                service_statuses[service] = {
                    "running": is_running,
                    "status": result if result else "Not found",
                }

                status_icon = "✅" if is_running else "❌"
                console.print(
                    f"{status_icon} {service}: {result if result else 'Not running'}"
                )

            except Exception as e:
                service_statuses[service] = {
                    "running": False,
                    "error": str(e),
                }
                console.print(f"❌ {service}: Error - {e}")

        results["service_statuses"] = service_statuses

        # Overall service health
        running_services = sum(
            1 for status in service_statuses.values() if status.get("running", False)
        )
        results["services_running"] = running_services
        results["services_total"] = len(expected_services)
        results["all_services_healthy"] = running_services == len(expected_services)

        return results

    async def test_mock_video_generation(self) -> Dict[str, Any]:
        """
        Test end-to-end mock video generation workflow.

        Returns:
            Dict[str, Any]: Video generation test results
        """
        console.print("\n[bold blue]🎬 Testing Mock Video Generation[/bold blue]")

        results = {}
        test_results = []

        for scenario in self.test_scenarios:
            console.print(f"Testing scenario: {scenario['name']}")

            scenario_result = {
                "scenario_name": scenario["name"],
                "success": False,
                "generation_id": None,
                "timing_ms": None,
                "status_progression": [],
                "error": None,
            }

            try:
                # Step 1: Submit video generation request
                start_time = time.time()

                generation_data = {
                    "prompt": scenario["prompt"],
                    "duration_seconds": scenario["duration"],
                    "aspect_ratio": "16:9",
                }

                response = requests.post(
                    f"{self.base_url}/api/video/generate",
                    json=generation_data,
                    timeout=30,
                )

                if response.status_code == 200:
                    response_data = response.json()
                    generation_id = response_data.get("generation_id")
                    scenario_result["generation_id"] = generation_id

                    console.print(f"✅ Generation started: {generation_id}")

                    # Step 2: Poll for completion
                    max_wait_time = 60  # 1 minute max wait
                    poll_interval = 2  # Check every 2 seconds
                    elapsed_time = 0

                    while elapsed_time < max_wait_time:
                        await asyncio.sleep(poll_interval)
                        elapsed_time += poll_interval

                        try:
                            status_response = requests.get(
                                f"{self.base_url}/api/video/status/{generation_id}",
                                timeout=10,
                            )

                            if status_response.status_code == 200:
                                status_data = status_response.json()
                                current_status = status_data.get("status", "unknown")

                                scenario_result["status_progression"].append(
                                    {
                                        "timestamp": time.time(),
                                        "status": current_status,
                                        "elapsed_seconds": elapsed_time,
                                    }
                                )

                                console.print(
                                    f"Status: {current_status} (after {elapsed_time}s)"
                                )

                                if current_status in ["succeeded", "completed"]:
                                    # Generation completed successfully
                                    total_time = (time.time() - start_time) * 1000
                                    scenario_result["timing_ms"] = total_time
                                    scenario_result["success"] = True

                                    # Test video URL accessibility
                                    video_url = status_data.get("video_url")
                                    if video_url:
                                        try:
                                            video_response = requests.head(
                                                f"{self.base_url}{video_url}",
                                                timeout=10,
                                            )
                                            scenario_result["video_accessible"] = (
                                                video_response.status_code == 200
                                            )
                                        except:
                                            scenario_result["video_accessible"] = False

                                    console.print(
                                        f"✅ Generation completed in {total_time:.0f}ms"
                                    )
                                    break

                                elif current_status in ["failed", "error"]:
                                    scenario_result["error"] = status_data.get(
                                        "error_message", "Generation failed"
                                    )
                                    console.print(
                                        f"❌ Generation failed: {scenario_result['error']}"
                                    )
                                    break

                            else:
                                console.print(
                                    f"❌ Status check failed: {status_response.status_code}"
                                )
                                break

                        except Exception as e:
                            console.print(f"❌ Status polling error: {e}")
                            break

                    if not scenario_result["success"]:
                        scenario_result["error"] = (
                            scenario_result["error"] or "Generation timed out"
                        )

                else:
                    scenario_result["error"] = f"Request failed: {response.status_code}"
                    console.print(
                        f"❌ Generation request failed: {response.status_code}"
                    )

            except Exception as e:
                scenario_result["error"] = str(e)
                console.print(f"❌ Scenario failed: {e}")

            test_results.append(scenario_result)

        # Analyze test results
        successful_tests = sum(1 for test in test_results if test["success"])
        results["test_scenarios"] = test_results
        results["successful_tests"] = successful_tests
        results["total_tests"] = len(test_results)
        results["success_rate"] = (
            successful_tests / len(test_results) if test_results else 0
        )
        results["all_tests_passed"] = successful_tests == len(test_results)

        return results

    async def validate_environment_switching(self) -> Dict[str, Any]:
        """
        Validate mock/real provider environment switching.

        Returns:
            Dict[str, Any]: Environment switching validation results
        """
        console.print("\n[bold blue]🔄 Validating Environment Switching[/bold blue]")

        results = {}

        try:
            # Test provider info endpoint
            response = requests.get(f"{self.base_url}/api/providers/info", timeout=10)

            if response.status_code == 200:
                provider_info = response.json()

                results["provider_info_accessible"] = True
                results["available_providers"] = provider_info.get(
                    "available_providers", []
                )
                results["current_providers"] = provider_info.get(
                    "current_providers", {}
                )

                # Check if Veo3 provider is configured as mock
                veo3_info = provider_info.get("current_providers", {}).get(
                    "google_veo3", {}
                )
                results["veo3_is_mock"] = veo3_info.get("is_mock", False)
                results["veo3_features"] = veo3_info.get("supported_features", {})

                console.print(
                    f"✅ Available providers: {results['available_providers']}"
                )
                console.print(f"✅ Veo3 is mock: {results['veo3_is_mock']}")

                # Validate mock-specific features
                mock_features = results["veo3_features"]
                expected_mock_features = ["mock_provider", "realistic_timing"]

                mock_features_present = all(
                    mock_features.get(feature, False)
                    for feature in expected_mock_features
                )
                results["mock_features_valid"] = mock_features_present

                if mock_features_present:
                    console.print("✅ Mock provider features validated")
                else:
                    console.print("❌ Mock provider features missing")

            else:
                results["provider_info_accessible"] = False
                results["status_code"] = response.status_code
                console.print(
                    f"❌ Provider info endpoint failed: {response.status_code}"
                )

        except Exception as e:
            results["provider_info_accessible"] = False
            results["error"] = str(e)
            console.print(f"❌ Environment switching validation failed: {e}")

        return results

    async def validate_monitoring_integration(self) -> Dict[str, Any]:
        """
        Validate monitoring and metrics collection.

        Returns:
            Dict[str, Any]: Monitoring validation results
        """
        console.print("\n[bold blue]📊 Validating Monitoring Integration[/bold blue]")

        results = {}

        try:
            # Test metrics endpoint
            response = requests.get(
                f"{self.base_url}/api/monitoring/mock-veo3/metrics", timeout=10
            )

            if response.status_code == 200:
                metrics_data = response.json()

                results["metrics_accessible"] = True
                results["metrics_data"] = metrics_data

                # Validate expected metrics structure
                expected_metrics = [
                    "total_requests",
                    "successful_generations",
                    "average_latency_ms",
                    "success_rate",
                ]

                metrics_valid = all(
                    metric in metrics_data for metric in expected_metrics
                )
                results["metrics_structure_valid"] = metrics_valid

                console.print("✅ Metrics endpoint accessible")

                if metrics_valid:
                    console.print("✅ Metrics structure validated")
                    console.print(
                        f"  Total requests: {metrics_data.get('total_requests', 0)}"
                    )
                    console.print(
                        f"  Success rate: {metrics_data.get('success_rate', 0):.2%}"
                    )
                else:
                    console.print("❌ Metrics structure invalid")

            else:
                results["metrics_accessible"] = False
                results["status_code"] = response.status_code
                console.print(f"❌ Metrics endpoint failed: {response.status_code}")

        except Exception as e:
            results["metrics_accessible"] = False
            results["error"] = str(e)
            console.print(f"❌ Monitoring validation failed: {e}")

        return results

    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """
        Run comprehensive deployment validation.

        Returns:
            Dict[str, Any]: Complete validation results
        """
        console.print(
            Panel.fit(
                "[bold blue]F3 Mock Veo3 Deployment Validation[/bold blue]\n"
                "[white]Comprehensive operational testing[/white]\n"
                f"[yellow]Target: {self.base_url}[/yellow]",
                title="🔍 Deployment Validator",
                border_style="blue",
            )
        )

        results = {}

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            transient=True,
        ) as progress:
            # Step 1: Service health validation
            task1 = progress.add_task("Validating service health...", total=1)
            results["service_health"] = await self.validate_service_health()
            progress.update(task1, completed=1)

            # Step 2: Docker services validation
            task2 = progress.add_task("Validating Docker services...", total=1)
            results["docker_services"] = await self.validate_docker_services()
            progress.update(task2, completed=1)

            # Step 3: Video generation testing
            task3 = progress.add_task("Testing video generation...", total=1)
            results["video_generation"] = await self.test_mock_video_generation()
            progress.update(task3, completed=1)

            # Step 4: Environment switching validation
            task4 = progress.add_task("Validating environment switching...", total=1)
            results[
                "environment_switching"
            ] = await self.validate_environment_switching()
            progress.update(task4, completed=1)

            # Step 5: Monitoring integration validation
            task5 = progress.add_task("Validating monitoring integration...", total=1)
            results[
                "monitoring_integration"
            ] = await self.validate_monitoring_integration()
            progress.update(task5, completed=1)

        return results

    def generate_validation_report(self, results: Dict[str, Any]) -> None:
        """
        Generate comprehensive validation report.

        Args:
            results: Validation results from run_comprehensive_validation
        """
        console.print("\n" + "=" * 80)
        console.print(
            "[bold blue]F3 Mock Veo3 Deployment Validation Report[/bold blue]"
        )
        console.print("=" * 80)

        # Overall deployment status
        critical_validations = [
            results.get("service_health", {})
            .get("main_health", {})
            .get("accessible", False),
            results.get("docker_services", {}).get("all_services_healthy", False),
            results.get("video_generation", {}).get("all_tests_passed", False),
            results.get("environment_switching", {}).get("veo3_is_mock", False),
        ]

        deployment_ready = all(critical_validations)

        if deployment_ready:
            status = "[bold green]✅ DEPLOYMENT VALIDATED[/bold green]"
        else:
            status = "[bold red]❌ VALIDATION ISSUES[/bold red]"

        console.print(f"\n[bold]Deployment Status:[/bold] {status}")

        # Service Health Summary
        health_results = results.get("service_health", {})
        console.print("\n[bold]Service Health:[/bold]")

        main_health = health_results.get("main_health", {})
        if main_health.get("accessible"):
            console.print(f"  ✅ Main health: {main_health.get('status', 'unknown')}")
        else:
            console.print("  ❌ Main health: Not accessible")

        mock_health = health_results.get("mock_veo3_health", {})
        if mock_health.get("accessible"):
            console.print("  ✅ Mock Veo3 health: Accessible")
        else:
            console.print("  ❌ Mock Veo3 health: Not accessible")

        # Docker Services Summary
        docker_results = results.get("docker_services", {})
        console.print("\n[bold]Docker Services:[/bold]")

        running_services = docker_results.get("services_running", 0)
        total_services = docker_results.get("services_total", 0)
        console.print(f"  Services: {running_services}/{total_services} running")

        # Video Generation Summary
        generation_results = results.get("video_generation", {})
        console.print("\n[bold]Video Generation Testing:[/bold]")

        success_rate = generation_results.get("success_rate", 0)
        successful_tests = generation_results.get("successful_tests", 0)
        total_tests = generation_results.get("total_tests", 0)

        console.print(
            f"  Success Rate: {success_rate:.1%} ({successful_tests}/{total_tests})"
        )

        # Test scenario details
        for scenario in generation_results.get("test_scenarios", []):
            scenario_name = scenario["scenario_name"]
            scenario_success = scenario["success"]
            status_icon = "✅" if scenario_success else "❌"

            console.print(f"    {status_icon} {scenario_name}")

            if not scenario_success and scenario.get("error"):
                console.print(f"      Error: {scenario['error']}")

        # Environment Switching Summary
        env_results = results.get("environment_switching", {})
        console.print("\n[bold]Environment Configuration:[/bold]")

        if env_results.get("provider_info_accessible"):
            console.print("  ✅ Provider info accessible")
            console.print(
                f"  Available providers: {env_results.get('available_providers', [])}"
            )
            console.print(
                f"  Veo3 is mock: {'✅' if env_results.get('veo3_is_mock') else '❌'}"
            )
        else:
            console.print("  ❌ Provider info not accessible")

        # Monitoring Summary
        monitoring_results = results.get("monitoring_integration", {})
        console.print("\n[bold]Monitoring Integration:[/bold]")

        if monitoring_results.get("metrics_accessible"):
            console.print("  ✅ Metrics endpoint accessible")

            metrics_valid = monitoring_results.get("metrics_structure_valid", False)
            console.print(
                f"  {'✅' if metrics_valid else '❌'} Metrics structure valid"
            )
        else:
            console.print("  ❌ Metrics endpoint not accessible")

        # Deployment Readiness Assessment
        console.print("\n[bold]Deployment Readiness:[/bold]")

        if deployment_ready:
            console.print("  🚀 [green]READY FOR PRODUCTION USE[/green]")
            console.print("  - All critical services validated")
            console.print("  - Mock video generation working")
            console.print("  - Environment switching functional")
            console.print("  - Monitoring integration active")
        else:
            console.print("  🔧 [red]REQUIRES ATTENTION[/red]")

            issues = []

            if not critical_validations[0]:
                issues.append("Service health endpoints not accessible")

            if not critical_validations[1]:
                issues.append("Docker services not fully operational")

            if not critical_validations[2]:
                issues.append("Video generation tests failing")

            if not critical_validations[3]:
                issues.append("Mock provider not properly configured")

            for issue in issues:
                console.print(f"    • {issue}")

        console.print("\n" + "=" * 80)


@click.command()
@click.option(
    "--url",
    "-u",
    default="http://localhost:5001",
    help="Base URL of deployed application",
)
@click.option(
    "--quick",
    "-q",
    is_flag=True,
    help="Run quick validation (skip video generation tests)",
)
async def main(url: str, quick: bool):
    """F3 Mock Veo3 Provider deployment validation."""

    validator = MockVeo3DeploymentValidator(url)

    if quick:
        # Run quick validation without video generation tests
        console.print("Running quick deployment validation...")

        results = {}
        results["service_health"] = await validator.validate_service_health()
        results["docker_services"] = await validator.validate_docker_services()
        results[
            "environment_switching"
        ] = await validator.validate_environment_switching()
        results[
            "monitoring_integration"
        ] = await validator.validate_monitoring_integration()

        validator.generate_validation_report(results)

    else:
        # Run comprehensive validation
        results = await validator.run_comprehensive_validation()
        validator.generate_validation_report(results)


if __name__ == "__main__":
    asyncio.run(main())
