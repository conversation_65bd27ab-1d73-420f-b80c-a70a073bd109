#!/bin/bash

# F4 Environment Configuration Deployment Script
# Specialized deployment script for F4 configuration management,
# provider switching, and Google Veo3 integration deployment

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
DEPLOYMENT_DIR="$PROJECT_ROOT/src/deployment"
CONFIG_DIR="$DEPLOYMENT_DIR/config"
LOG_FILE="/tmp/f4-deployment-$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
DEPLOYMENT_TYPE="production"
ENVIRONMENT="production"
SKIP_VALIDATION=false
SKIP_BACKUP=false
FORCE_PROVIDER_RESET=false
DRY_RUN=false
F4_CONFIG_FILE=""

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[F4-INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[F4-WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[F4-ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[F4-DEBUG]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Error handling
error_exit() {
    log ERROR "$1"
    log ERROR "F4 deployment failed. Check log file: $LOG_FILE"
    exit 1
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy F4 Environment Configuration with Google Veo3 Integration

Options:
    -t, --type TYPE              Deployment type: local, staging, production (default: production)
    -e, --environment ENV        Environment: development, staging, production (default: production)
    -c, --config FILE           F4 configuration file path
    -s, --skip-validation       Skip F4 configuration validation
    -b, --skip-backup           Skip configuration backup
    -f, --force-provider-reset  Force provider configuration reset
    -d, --dry-run               Dry run - show what would be done
    -h, --help                  Show this help message

F4 Configuration Files:
    Local:      $CONFIG_DIR/f4-environment-local.env
    Staging:    $CONFIG_DIR/f4-environment-staging.env
    Production: $CONFIG_DIR/f4-environment-production.env

Examples:
    $0                                          # Production deployment
    $0 -t staging -e staging                    # Staging deployment
    $0 -t local -c custom-f4.env               # Local with custom config
    $0 -d                                       # Dry run to see what would happen
    $0 --force-provider-reset                  # Reset all provider configurations

Google Veo3 Setup:
    For real API usage, ensure these are set:
    - GOOGLE_PROJECT_ID
    - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET (or service account)
    - Valid Google Cloud credentials

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                DEPLOYMENT_TYPE="$2"
                shift 2
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -c|--config)
                F4_CONFIG_FILE="$2"
                shift 2
                ;;
            -s|--skip-validation)
                SKIP_VALIDATION=true
                shift
                ;;
            -b|--skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            -f|--force-provider-reset)
                FORCE_PROVIDER_RESET=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error_exit "Unknown option: $1"
                ;;
        esac
    done
}

# Validate deployment parameters
validate_deployment_params() {
    log INFO "Validating F4 deployment parameters..."
    
    case $DEPLOYMENT_TYPE in
        local|staging|production)
            log INFO "Deployment type: $DEPLOYMENT_TYPE"
            ;;
        *)
            error_exit "Invalid deployment type: $DEPLOYMENT_TYPE. Use 'local', 'staging', or 'production'"
            ;;
    esac
    
    case $ENVIRONMENT in
        development|staging|production)
            log INFO "Environment: $ENVIRONMENT"
            ;;
        *)
            error_exit "Invalid environment: $ENVIRONMENT. Use 'development', 'staging', or 'production'"
            ;;
    esac
    
    # Determine F4 config file if not specified
    if [[ -z "$F4_CONFIG_FILE" ]]; then
        case $DEPLOYMENT_TYPE in
            local)
                F4_CONFIG_FILE="$CONFIG_DIR/f4-environment-local.env"
                ;;
            staging)
                F4_CONFIG_FILE="$CONFIG_DIR/f4-environment-staging.env"
                ;;
            production)
                F4_CONFIG_FILE="$CONFIG_DIR/f4-environment-production.env"
                ;;
        esac
    fi
    
    if [[ ! -f "$F4_CONFIG_FILE" ]]; then
        error_exit "F4 configuration file not found: $F4_CONFIG_FILE"
    fi
    
    log INFO "F4 configuration file: $F4_CONFIG_FILE"
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN MODE: No actual changes will be made"
    fi
}

# Backup existing F4 configuration
backup_f4_configuration() {
    if [[ "$SKIP_BACKUP" == true ]]; then
        log INFO "Skipping F4 configuration backup (--skip-backup flag)"
        return
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN: Would backup F4 configuration"
        return
    fi
    
    log INFO "Creating backup of existing F4 configuration..."
    
    local backup_dir="/opt/sora/backups/f4/$(date +%Y%m%d_%H%M%S)"
    sudo mkdir -p "$backup_dir"
    
    # Backup current environment files
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        sudo cp "$PROJECT_ROOT/.env" "$backup_dir/original.env"
    fi
    
    # Backup F4 configuration files
    if [[ -d "$CONFIG_DIR" ]]; then
        sudo cp -r "$CONFIG_DIR" "$backup_dir/config"
    fi
    
    # Backup Docker configuration
    if [[ -f "$DEPLOYMENT_DIR/docker/docker-compose.simple.yml" ]]; then
        sudo cp "$DEPLOYMENT_DIR/docker/docker-compose.simple.yml" "$backup_dir/docker-compose.simple.yml.backup"
    fi
    
    # Export current provider settings for rollback
    if command -v python3 &> /dev/null; then
        python3 -c "
import os, json, sys
sys.path.append('$PROJECT_ROOT')
try:
    from src.config.factory import ConfigurationFactory
    availability = ConfigurationFactory.get_provider_availability()
    with open('$backup_dir/provider_state.json', 'w') as f:
        json.dump(availability, f, indent=2)
    print('Provider state backed up')
except Exception as e:
    print(f'Provider state backup failed: {e}')
" 2>/dev/null || log WARN "Could not backup provider state"
    fi
    
    log INFO "F4 configuration backup completed: $backup_dir"
    echo "$backup_dir" > /tmp/f4_last_backup_dir
}

# Validate F4 configuration
validate_f4_configuration() {
    if [[ "$SKIP_VALIDATION" == true ]]; then
        log INFO "Skipping F4 configuration validation (--skip-validation flag)"
        return
    fi
    
    log INFO "Validating F4 configuration..."
    
    # Source the F4 configuration file
    set -a
    source "$F4_CONFIG_FILE"
    set +a
    
    # Validate required F4 variables
    local required_vars=(
        "USE_MOCK_VEO"
        "DEFAULT_PROVIDER"
        "DEPLOYMENT_TYPE"
    )
    
    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        error_exit "Missing required F4 variables: ${missing_vars[*]}"
    fi
    
    # Validate provider configuration
    log INFO "Validating provider configurations..."
    
    # Check Azure configuration if it's the default or available
    if [[ "$DEFAULT_PROVIDER" == "azure_sora" ]] || [[ -n "$AZURE_OPENAI_API_KEY" ]]; then
        local azure_vars=("AZURE_OPENAI_API_KEY" "AZURE_OPENAI_ENDPOINT" "AZURE_OPENAI_API_VERSION")
        for var in "${azure_vars[@]}"; do
            if [[ -z "${!var}" ]]; then
                log WARN "Azure configuration incomplete: $var not set"
            fi
        done
    fi
    
    # Check Google Veo3 configuration if not using mock
    if [[ "$USE_MOCK_VEO" != "true" ]]; then
        if [[ -z "$GOOGLE_PROJECT_ID" ]]; then
            error_exit "GOOGLE_PROJECT_ID required when USE_MOCK_VEO=false"
        fi
        
        if [[ -z "$GOOGLE_CLIENT_ID" && -z "$GOOGLE_APPLICATION_CREDENTIALS" ]]; then
            error_exit "Either GOOGLE_CLIENT_ID or GOOGLE_APPLICATION_CREDENTIALS required for real Veo3 API"
        fi
        
        if [[ -n "$GOOGLE_CLIENT_ID" && -z "$GOOGLE_CLIENT_SECRET" ]]; then
            error_exit "GOOGLE_CLIENT_SECRET required when using GOOGLE_CLIENT_ID"
        fi
    fi
    
    # Validate environment-specific settings
    case $ENVIRONMENT in
        production)
            if [[ "$USE_MOCK_VEO" == "true" ]]; then
                log WARN "WARNING: Using mock Veo3 in production environment"
            fi
            if [[ "$DEBUG" == "true" ]]; then
                log WARN "WARNING: Debug mode enabled in production"
            fi
            ;;
        development)
            if [[ "$USE_MOCK_VEO" != "true" ]]; then
                log INFO "Using real Veo3 API in development (ensure credentials are test credentials)"
            fi
            ;;
    esac
    
    log INFO "F4 configuration validation passed"
}

# Test F4 configuration with Python validation
test_f4_configuration() {
    log INFO "Testing F4 configuration with Python validation..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN: Would test F4 configuration"
        return
    fi
    
    # Source configuration
    set -a
    source "$F4_CONFIG_FILE"
    set +a
    
    # Run Python configuration test
    python3 -c "
import os, sys
sys.path.append('$PROJECT_ROOT')

try:
    from src.config.factory import ConfigurationFactory
    from src.config.veo3_settings import validate_veo3_environment
    
    print('Testing F4 configuration...')
    
    # Test basic configuration loading
    try:
        base_config = ConfigurationFactory.get_base_config()
        print('✓ Base configuration loaded')
    except Exception as e:
        print(f'✗ Base configuration failed: {e}')
        sys.exit(1)
    
    # Test Veo3 settings
    try:
        veo3_validation = validate_veo3_environment()
        if veo3_validation.get('valid', False):
            print('✓ Veo3 environment validation passed')
        else:
            print(f'⚠ Veo3 environment validation warnings: {veo3_validation.get(\"errors\", [])}')
    except Exception as e:
        print(f'✗ Veo3 environment validation failed: {e}')
        sys.exit(1)
    
    # Test provider availability
    try:
        availability = ConfigurationFactory.get_provider_availability()
        total_available = sum(availability.values())
        print(f'✓ Provider availability: {total_available} providers available')
        for provider, available in availability.items():
            status = '✓' if available else '✗'
            print(f'  {status} {provider}: {\"Available\" if available else \"Not available\"}')
        
        if total_available == 0:
            print('⚠ WARNING: No providers are available')
            sys.exit(1)
    except Exception as e:
        print(f'✗ Provider availability check failed: {e}')
        sys.exit(1)
    
    # Test provider configuration creation
    try:
        default_provider = ConfigurationFactory.get_default_provider()
        config = ConfigurationFactory.create_provider_config(default_provider)
        print(f'✓ Default provider ({default_provider}) configuration created')
    except Exception as e:
        print(f'✗ Default provider configuration failed: {e}')
        sys.exit(1)
    
    print('✅ F4 configuration test completed successfully')
    
except ImportError as e:
    print(f'✗ Import error: {e}')
    print('Make sure all dependencies are installed')
    sys.exit(1)
except Exception as e:
    print(f'✗ Configuration test failed: {e}')
    sys.exit(1)
" || error_exit "F4 configuration test failed"
    
    log INFO "F4 configuration test completed successfully"
}

# Deploy F4 configuration
deploy_f4_configuration() {
    log INFO "Deploying F4 configuration..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN: Would deploy F4 configuration:"
        log INFO "  - Copy $F4_CONFIG_FILE to $PROJECT_ROOT/.env"
        log INFO "  - Update Docker environment variables"
        log INFO "  - Restart affected services"
        return
    fi
    
    # Copy F4 configuration to project root
    cp "$F4_CONFIG_FILE" "$PROJECT_ROOT/.env"
    log INFO "F4 configuration deployed to $PROJECT_ROOT/.env"
    
    # Update Docker environment if using Docker deployment
    if [[ "$DEPLOYMENT_TYPE" != "local" ]]; then
        local docker_env_file="$DEPLOYMENT_DIR/docker/.env"
        cp "$F4_CONFIG_FILE" "$docker_env_file"
        log INFO "F4 configuration deployed to Docker environment: $docker_env_file"
    fi
    
    # Set appropriate permissions
    chmod 600 "$PROJECT_ROOT/.env"
    if [[ -f "$DEPLOYMENT_DIR/docker/.env" ]]; then
        chmod 600 "$DEPLOYMENT_DIR/docker/.env"
    fi
    
    log INFO "F4 configuration deployment completed"
}

# Handle provider configuration reset
reset_provider_configuration() {
    if [[ "$FORCE_PROVIDER_RESET" != true ]]; then
        return
    fi
    
    log INFO "Resetting provider configurations (--force-provider-reset)..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN: Would reset provider configurations"
        return
    fi
    
    # Clear any cached configurations
    python3 -c "
import os, sys
sys.path.append('$PROJECT_ROOT')

try:
    from src.config.factory import ConfigurationFactory
    
    # Clear configuration cache if available
    try:
        ConfigurationFactory.clear_cache()
        print('Provider configuration cache cleared')
    except Exception as e:
        print(f'Cache clear failed (may not be implemented): {e}')
    
    print('Provider configuration reset completed')
    
except Exception as e:
    print(f'Provider reset failed: {e}')
    sys.exit(1)
" || log WARN "Provider configuration reset had issues"
    
    log INFO "Provider configuration reset completed"
}

# Restart affected services
restart_services() {
    log INFO "Restarting affected services..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN: Would restart affected services"
        return
    fi
    
    case $DEPLOYMENT_TYPE in
        local)
            log INFO "Local deployment - services restart manually required"
            log INFO "Restart commands:"
            log INFO "  Flask app: Restart your development server"
            log INFO "  Celery workers: pkill -f celery && celery worker &"
            ;;
        staging|production)
            if command -v docker-compose &> /dev/null; then
                cd "$DEPLOYMENT_DIR/docker"
                
                # Restart app and worker containers to pick up new environment
                log INFO "Restarting Docker containers..."
                docker-compose -f docker-compose.simple.yml restart app worker
                
                # Wait for services to be healthy
                log INFO "Waiting for services to be healthy..."
                sleep 30
                
                # Check service health
                if docker ps | grep -q "sora-app-simple"; then
                    log INFO "✓ App container restarted successfully"
                else
                    log WARN "⚠ App container may not be running properly"
                fi
                
                if docker ps | grep -q "sora-worker-simple"; then
                    log INFO "✓ Worker container restarted successfully"
                else
                    log WARN "⚠ Worker container may not be running properly"
                fi
            else
                log WARN "Docker Compose not available - manual service restart required"
            fi
            ;;
    esac
}

# Validate deployment success
validate_deployment_success() {
    log INFO "Validating F4 deployment success..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "DRY RUN: Would validate deployment success"
        return
    fi
    
    # Test configuration endpoints
    local base_url="http://localhost:5001"
    if [[ "$DEPLOYMENT_TYPE" != "local" ]]; then
        base_url="http://localhost"
    fi
    
    local endpoints=(
        "$base_url/health"
        "$base_url/health/config"
        "$base_url/health/config/providers"
    )
    
    local failed_endpoints=()
    
    for endpoint in "${endpoints[@]}"; do
        log INFO "Testing endpoint: $endpoint"
        
        local response=$(curl -s -o /dev/null -w "%{http_code}" --max-time 30 "$endpoint" || echo "000")
        
        if [[ "$response" == "200" ]]; then
            log INFO "✓ $endpoint - OK"
        else
            log WARN "⚠ $endpoint - Failed (HTTP $response)"
            failed_endpoints+=("$endpoint")
        fi
    done
    
    # Test F4 configuration with Python
    log INFO "Testing F4 configuration after deployment..."
    python3 -c "
import os, sys
sys.path.append('$PROJECT_ROOT')

try:
    from src.config.factory import ConfigurationFactory
    
    # Test provider availability
    availability = ConfigurationFactory.get_provider_availability()
    total_available = sum(availability.values())
    
    if total_available > 0:
        print(f'✓ {total_available} providers available after deployment')
        default_provider = ConfigurationFactory.get_default_provider()
        print(f'✓ Default provider: {default_provider}')
    else:
        print('✗ No providers available after deployment')
        sys.exit(1)
    
except Exception as e:
    print(f'✗ Post-deployment configuration test failed: {e}')
    sys.exit(1)
" || error_exit "Post-deployment configuration validation failed"
    
    if [[ ${#failed_endpoints[@]} -eq 0 ]]; then
        log INFO "✅ F4 deployment validation successful"
    else
        log WARN "⚠ Some endpoints failed: ${failed_endpoints[*]}"
        log WARN "Deployment may need manual verification"
    fi
}

# Rollback F4 configuration
rollback_f4_configuration() {
    local backup_dir_file="/tmp/f4_last_backup_dir"
    
    if [[ ! -f "$backup_dir_file" ]]; then
        error_exit "No backup directory found for rollback"
    fi
    
    local backup_dir=$(cat "$backup_dir_file")
    
    if [[ ! -d "$backup_dir" ]]; then
        error_exit "Backup directory not found: $backup_dir"
    fi
    
    log INFO "Rolling back F4 configuration from: $backup_dir"
    
    # Restore environment file
    if [[ -f "$backup_dir/original.env" ]]; then
        cp "$backup_dir/original.env" "$PROJECT_ROOT/.env"
        log INFO "Environment file restored"
    fi
    
    # Restore Docker compose if exists
    if [[ -f "$backup_dir/docker-compose.simple.yml.backup" ]]; then
        cp "$backup_dir/docker-compose.simple.yml.backup" "$DEPLOYMENT_DIR/docker/docker-compose.simple.yml"
        log INFO "Docker compose file restored"
    fi
    
    # Restart services
    restart_services
    
    log INFO "F4 configuration rollback completed"
}

# Generate deployment report
generate_deployment_report() {
    local report_file="/tmp/f4-deployment-report-$(date +%Y%m%d_%H%M%S).json"
    
    log INFO "Generating F4 deployment report..."
    
    # Generate JSON report
    python3 -c "
import json, os, sys
sys.path.append('$PROJECT_ROOT')

report = {
    'deployment_info': {
        'timestamp': '$(date -u +%Y-%m-%dT%H:%M:%SZ)',
        'deployment_type': '$DEPLOYMENT_TYPE',
        'environment': '$ENVIRONMENT',
        'config_file': '$F4_CONFIG_FILE',
        'dry_run': $([[ '$DRY_RUN' == 'true' ]] && echo 'true' || echo 'false')
    },
    'configuration': {},
    'validation': {},
    'deployment_status': 'unknown'
}

try:
    from src.config.factory import ConfigurationFactory
    from src.config.veo3_settings import validate_veo3_environment
    
    # Get provider availability
    availability = ConfigurationFactory.get_provider_availability()
    report['configuration']['provider_availability'] = availability
    report['configuration']['default_provider'] = ConfigurationFactory.get_default_provider()
    
    # Get validation results
    veo3_validation = validate_veo3_environment()
    report['validation']['veo3_environment'] = veo3_validation
    
    # Determine deployment status
    total_available = sum(availability.values())
    if total_available > 0 and veo3_validation.get('valid', False):
        report['deployment_status'] = 'success'
    elif total_available > 0:
        report['deployment_status'] = 'warning'
    else:
        report['deployment_status'] = 'failed'
    
except Exception as e:
    report['deployment_status'] = 'error'
    report['error'] = str(e)

with open('$report_file', 'w') as f:
    json.dump(report, f, indent=2)

print(f'Deployment report saved: $report_file')
" || log WARN "Could not generate deployment report"
    
    log INFO "F4 deployment report generated: $report_file"
}

# Display deployment summary
show_deployment_summary() {
    log INFO "F4 Deployment Summary"
    log INFO "===================="
    log INFO "Deployment Type: $DEPLOYMENT_TYPE"
    log INFO "Environment: $ENVIRONMENT"
    log INFO "Configuration File: $F4_CONFIG_FILE"
    log INFO "Log File: $LOG_FILE"
    
    if [[ "$DRY_RUN" == true ]]; then
        log INFO "Mode: DRY RUN (no changes made)"
    else
        log INFO "Mode: DEPLOYMENT EXECUTED"
    fi
    
    log INFO ""
    log INFO "Next Steps:"
    
    case $DEPLOYMENT_TYPE in
        local)
            log INFO "  1. Restart your development server"
            log INFO "  2. Test F4 endpoints: curl http://localhost:5001/health/config"
            log INFO "  3. Verify provider switching works as expected"
            ;;
        staging|production)
            log INFO "  1. Verify all containers are healthy: docker ps"
            log INFO "  2. Test health endpoints: curl http://localhost/health/config"
            log INFO "  3. Monitor F4 configuration: curl http://localhost/health/config/providers"
            log INFO "  4. Check logs for any configuration issues"
            ;;
    esac
    
    log INFO ""
    
    if [[ "$DRY_RUN" != true ]]; then
        log INFO "✅ F4 deployment completed successfully!"
    else
        log INFO "✅ F4 deployment dry run completed!"
    fi
}

# Main deployment function
main() {
    log INFO "Starting F4 Environment Configuration deployment..."
    log INFO "Log file: $LOG_FILE"
    
    # Parse arguments
    parse_args "$@"
    
    # Validate parameters
    validate_deployment_params
    
    # Backup existing configuration
    backup_f4_configuration
    
    # Validate F4 configuration
    validate_f4_configuration
    
    # Test configuration
    test_f4_configuration
    
    # Reset provider configuration if requested
    reset_provider_configuration
    
    # Deploy F4 configuration
    deploy_f4_configuration
    
    # Restart services
    restart_services
    
    # Validate deployment
    validate_deployment_success
    
    # Generate report
    generate_deployment_report
    
    # Show summary
    show_deployment_summary
    
    log INFO "F4 deployment completed successfully!"
}

# Handle script interruption
trap 'log ERROR "F4 deployment interrupted"; exit 1' INT TERM

# Handle rollback command
if [[ "${1:-}" == "rollback" ]]; then
    log INFO "F4 Configuration Rollback initiated..."
    rollback_f4_configuration
    exit 0
fi

# Run main function
main "$@"