#!/usr/bin/env python3
"""
F2 Provider Interface Operations Automation

Automated provider configuration validation, switching, and operations management
for Azure Sora and Google Veo3 providers.
"""

import asyncio
import os
import sys
import time
from typing import Dict

import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import BarColumn, Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from src.config.factory import ConfigurationFactory
from src.features.video_generation import (
    get_provider_factory,
)

console = Console()


class ProviderAutomationManager:
    """Automated operations for F2 Provider Interface management."""

    def __init__(self):
        self.console = console
        self.factory = None

    async def initialize_factory(self) -> bool:
        """Initialize provider factory with error handling."""
        try:
            self.factory = get_provider_factory()
            return True
        except Exception as e:
            console.print(f"❌ Failed to initialize provider factory: {e}")
            return False

    async def validate_all_configurations(self) -> Dict[str, Dict[str, bool]]:
        """Validate all provider configurations."""
        console.print(
            "\n[bold blue]🔧 Validating All Provider Configurations[/bold blue]"
        )

        results = {}

        # Validate Azure Sora configuration
        try:
            azure_config = ConfigurationFactory.get_azure_config()
            azure_valid = all(
                [
                    azure_config.get("endpoint"),
                    azure_config.get("api_key"),
                    azure_config.get("api_version"),
                    azure_config.get("deployment_name"),
                ]
            )
            results["azure_sora"] = {
                "configuration_valid": azure_valid,
                "endpoint_configured": bool(azure_config.get("endpoint")),
                "api_key_configured": bool(azure_config.get("api_key")),
                "version_configured": bool(azure_config.get("api_version")),
                "deployment_configured": bool(azure_config.get("deployment_name")),
            }
            console.print(f"Azure Sora: {'✅ Valid' if azure_valid else '❌ Invalid'}")
        except Exception as e:
            results["azure_sora"] = {"configuration_valid": False, "error": str(e)}
            console.print(f"Azure Sora: ❌ Error - {e}")

        # Validate Google Veo3 configuration
        try:
            veo3_config = ConfigurationFactory.create_veo3_config()
            # For Veo3, mock mode is acceptable
            veo3_valid = veo3_config.use_mock or bool(veo3_config.project_id)
            results["google_veo3"] = {
                "configuration_valid": veo3_valid,
                "project_id_configured": bool(veo3_config.project_id),
                "use_mock": veo3_config.use_mock,
                "location_configured": bool(veo3_config.location),
            }
            console.print(f"Google Veo3: {'✅ Valid' if veo3_valid else '❌ Invalid'}")
        except Exception as e:
            results["google_veo3"] = {"configuration_valid": False, "error": str(e)}
            console.print(f"Google Veo3: ❌ Error - {e}")

        return results

    async def test_provider_switching(self) -> Dict[str, bool]:
        """Test automated provider switching capabilities."""
        console.print("\n[bold blue]🔄 Testing Provider Switching[/bold blue]")

        if not self.factory:
            console.print("❌ Factory not initialized")
            return {"initialization_failed": True}

        results = {}
        available_providers = self.factory.get_available_providers()

        if len(available_providers) < 2:
            console.print(
                f"⚠️ Only {len(available_providers)} provider(s) available for switching test"
            )
            if len(available_providers) == 1:
                console.print(f"Available: {available_providers[0]}")
            results["insufficient_providers"] = True
            return results

        console.print(f"Testing switching between: {', '.join(available_providers)}")

        # Test switching between available providers
        switching_results = []
        for i, provider_name in enumerate(available_providers):
            try:
                start_time = time.time()
                provider = self.factory.create_provider(provider_name)
                creation_time = (time.time() - start_time) * 1000

                # Test basic provider functionality
                features = provider.supported_features
                health = await provider.health_check()

                switching_results.append(
                    {
                        "provider": provider_name,
                        "creation_time_ms": creation_time,
                        "creation_successful": True,
                        "features_accessible": bool(features),
                        "health_check_successful": isinstance(health, dict)
                        and "error" not in health,
                    }
                )

                console.print(
                    f"✅ {provider_name}: Switched successfully ({creation_time:.2f}ms)"
                )

            except Exception as e:
                switching_results.append(
                    {
                        "provider": provider_name,
                        "creation_successful": False,
                        "error": str(e),
                    }
                )
                console.print(f"❌ {provider_name}: Switch failed - {e}")

        # Analyze results
        successful_switches = sum(
            1 for r in switching_results if r.get("creation_successful", False)
        )
        results["total_providers_tested"] = len(available_providers)
        results["successful_switches"] = successful_switches
        results["switch_success_rate"] = successful_switches / len(available_providers)
        results["all_switches_successful"] = successful_switches == len(
            available_providers
        )
        results["switching_details"] = switching_results

        return results

    async def test_failover_scenario(self) -> Dict[str, bool]:
        """Test provider failover automation."""
        console.print("\n[bold blue]🚨 Testing Failover Scenarios[/bold blue]")

        if not self.factory:
            return {"initialization_failed": True}

        available_providers = self.factory.get_available_providers()

        if len(available_providers) < 2:
            console.print("⚠️ Failover testing requires multiple providers")
            return {"insufficient_providers_for_failover": True}

        results = {}
        primary_provider = available_providers[0]
        fallback_provider = available_providers[1]

        console.print(f"Primary: {primary_provider}, Fallback: {fallback_provider}")

        # Test failover logic
        try:
            # 1. Test primary provider
            primary = self.factory.create_provider(primary_provider)
            primary_health = await primary.health_check()
            primary_healthy = isinstance(primary_health, dict) and all(
                v for k, v in primary_health.items() if k != "error"
            )

            console.print(
                f"Primary provider health: {'✅ Healthy' if primary_healthy else '❌ Unhealthy'}"
            )

            # 2. Test fallback provider
            fallback = self.factory.create_provider(fallback_provider)
            fallback_health = await fallback.health_check()
            fallback_healthy = isinstance(fallback_health, dict) and all(
                v for k, v in fallback_health.items() if k != "error"
            )

            console.print(
                f"Fallback provider health: {'✅ Healthy' if fallback_healthy else '❌ Unhealthy'}"
            )

            # 3. Test feature compatibility
            primary_features = primary.supported_features
            fallback_features = fallback.supported_features

            common_features = set(primary_features.keys()) & set(
                fallback_features.keys()
            )
            compatible_features = {
                feature
                for feature in common_features
                if primary_features[feature] == fallback_features[feature]
            }

            feature_compatibility = (
                len(compatible_features) / len(common_features)
                if common_features
                else 0
            )

            console.print(
                f"Feature compatibility: {len(compatible_features)}/{len(common_features)} features compatible"
            )

            results.update(
                {
                    "primary_healthy": primary_healthy,
                    "fallback_healthy": fallback_healthy,
                    "both_providers_healthy": primary_healthy and fallback_healthy,
                    "feature_compatibility_rate": feature_compatibility,
                    "failover_ready": primary_healthy
                    and fallback_healthy
                    and feature_compatibility > 0.5,
                }
            )

        except Exception as e:
            console.print(f"❌ Failover testing failed: {e}")
            results["failover_test_error"] = str(e)

        return results

    async def generate_backup_configuration(self) -> Dict[str, str]:
        """Generate backup configuration for deployment rollback."""
        console.print("\n[bold blue]💾 Generating Backup Configuration[/bold blue]")

        backup_config = {}

        # Backup current environment variables
        azure_vars = [
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_API_VERSION",
            "AZURE_OPENAI_DEPLOYMENT_NAME",
        ]

        veo3_vars = [
            "GOOGLE_PROJECT_ID",
            "GOOGLE_LOCATION",
            "USE_MOCK_VEO",
            "GOOGLE_APPLICATION_CREDENTIALS",
        ]

        console.print("Backing up environment configuration...")

        for var in azure_vars + veo3_vars:
            value = os.getenv(var)
            if value:
                # Sanitize sensitive values for backup
                if "key" in var.lower() or "credentials" in var.lower():
                    backup_config[var] = f"***REDACTED*** (length: {len(value)})"
                else:
                    backup_config[var] = value
                console.print(f"✅ Backed up {var}")
            else:
                console.print(f"⚠️ {var} not set")

        # Generate restoration script
        restoration_script = self._generate_restoration_script(backup_config)
        backup_config["restoration_script"] = restoration_script

        return backup_config

    def _generate_restoration_script(self, config: Dict[str, str]) -> str:
        """Generate shell script for configuration restoration."""
        script_lines = [
            "#!/bin/bash",
            "# F2 Provider Configuration Restoration Script",
            "# Generated by Provider Automation Manager",
            "",
            "echo 'Restoring F2 Provider Configuration...'",
            "",
        ]

        for var, value in config.items():
            if var != "restoration_script" and not value.startswith("***REDACTED***"):
                script_lines.append(f"export {var}='{value}'")

        script_lines.extend(
            [
                "",
                "echo 'Configuration restored. Verify with:'",
                "echo 'python -c \"from src.features.video_generation import get_provider_factory; print(get_provider_factory().get_available_providers())\"'",
                "",
            ]
        )

        return "\n".join(script_lines)

    async def optimize_provider_performance(self) -> Dict[str, float]:
        """Analyze and optimize provider performance."""
        console.print("\n[bold blue]⚡ Optimizing Provider Performance[/bold blue]")

        if not self.factory:
            return {"initialization_failed": True}

        performance_results = {}
        available_providers = self.factory.get_available_providers()

        for provider_name in available_providers:
            console.print(f"Analyzing {provider_name}...")

            # Test creation performance
            creation_times = []
            for i in range(5):  # Test 5 iterations
                start_time = time.time()
                try:
                    provider = self.factory.create_provider(provider_name)
                    creation_time = (time.time() - start_time) * 1000
                    creation_times.append(creation_time)
                except Exception as e:
                    console.print(f"❌ {provider_name} creation failed: {e}")
                    break

            if creation_times:
                avg_creation_time = sum(creation_times) / len(creation_times)
                min_creation_time = min(creation_times)
                max_creation_time = max(creation_times)

                performance_results[provider_name] = {
                    "average_creation_time_ms": avg_creation_time,
                    "min_creation_time_ms": min_creation_time,
                    "max_creation_time_ms": max_creation_time,
                    "creation_consistency": max_creation_time - min_creation_time,
                    "meets_50ms_target": avg_creation_time < 50,
                }

                status = (
                    "✅ Optimal" if avg_creation_time < 50 else "⚠️ Needs optimization"
                )
                console.print(
                    f"{provider_name}: {status} (avg: {avg_creation_time:.2f}ms)"
                )

        return performance_results

    async def run_comprehensive_validation(self) -> Dict[str, any]:
        """Run comprehensive provider automation validation."""
        console.print(
            Panel.fit(
                "[bold blue]F2 Provider Interface Automation[/bold blue]\n"
                "[white]Comprehensive Validation & Operations Testing[/white]",
                title="🤖 Automation Manager",
                border_style="blue",
            )
        )

        results = {}

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            transient=True,
        ) as progress:
            # Step 1: Initialize
            task1 = progress.add_task("Initializing provider factory...", total=1)
            if await self.initialize_factory():
                results["initialization"] = {"successful": True}
                progress.update(task1, completed=1)
            else:
                results["initialization"] = {"successful": False}
                return results

            # Step 2: Configuration validation
            task2 = progress.add_task("Validating configurations...", total=1)
            results[
                "configuration_validation"
            ] = await self.validate_all_configurations()
            progress.update(task2, completed=1)

            # Step 3: Provider switching
            task3 = progress.add_task("Testing provider switching...", total=1)
            results["provider_switching"] = await self.test_provider_switching()
            progress.update(task3, completed=1)

            # Step 4: Failover testing
            task4 = progress.add_task("Testing failover scenarios...", total=1)
            results["failover_testing"] = await self.test_failover_scenario()
            progress.update(task4, completed=1)

            # Step 5: Performance optimization
            task5 = progress.add_task("Optimizing performance...", total=1)
            results[
                "performance_optimization"
            ] = await self.optimize_provider_performance()
            progress.update(task5, completed=1)

            # Step 6: Backup generation
            task6 = progress.add_task("Generating backup configuration...", total=1)
            results["backup_configuration"] = await self.generate_backup_configuration()
            progress.update(task6, completed=1)

        return results

    def generate_automation_report(self, results: Dict) -> None:
        """Generate comprehensive automation validation report."""
        console.print("\n" + "=" * 80)
        console.print("[bold blue]F2 Provider Automation Validation Report[/bold blue]")
        console.print("=" * 80)

        # Overall status
        critical_checks = [
            results.get("initialization", {}).get("successful", False),
            results.get("configuration_validation", {})
            .get("azure_sora", {})
            .get("configuration_valid", False),
            results.get("provider_switching", {}).get("all_switches_successful", False),
        ]

        automation_ready = all(critical_checks)

        if automation_ready:
            status = "[bold green]✅ AUTOMATION READY[/bold green]"
        else:
            status = "[bold red]❌ AUTOMATION ISSUES[/bold red]"

        console.print(f"\n[bold]Automation Status:[/bold] {status}")

        # Configuration Summary
        config_results = results.get("configuration_validation", {})
        console.print("\n[bold]Configuration Status:[/bold]")
        for provider, config in config_results.items():
            if isinstance(config, dict) and "configuration_valid" in config:
                status_icon = "✅" if config["configuration_valid"] else "❌"
                console.print(
                    f"  {status_icon} {provider}: {'Valid' if config['configuration_valid'] else 'Invalid'}"
                )

        # Provider Switching Results
        switching_results = results.get("provider_switching", {})
        if "switching_details" in switching_results:
            console.print("\n[bold]Provider Switching:[/bold]")
            success_rate = switching_results.get("switch_success_rate", 0) * 100
            console.print(f"  Success Rate: {success_rate:.1f}%")

            table = Table(title="Provider Switch Performance")
            table.add_column("Provider", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Creation Time", style="yellow")

            for switch in switching_results["switching_details"]:
                provider = switch["provider"]
                if switch.get("creation_successful", False):
                    status = "✅ Success"
                    time_str = f"{switch.get('creation_time_ms', 0):.2f}ms"
                else:
                    status = "❌ Failed"
                    time_str = "N/A"
                table.add_row(provider, status, time_str)

            console.print(table)

        # Performance Results
        perf_results = results.get("performance_optimization", {})
        if perf_results and not perf_results.get("initialization_failed"):
            console.print("\n[bold]Performance Analysis:[/bold]")
            for provider, metrics in perf_results.items():
                if isinstance(metrics, dict):
                    avg_time = metrics.get("average_creation_time_ms", 0)
                    meets_target = metrics.get("meets_50ms_target", False)
                    status_icon = "✅" if meets_target else "⚠️"
                    console.print(
                        f"  {status_icon} {provider}: {avg_time:.2f}ms average"
                    )

        # Recommendations
        console.print("\n[bold]Automation Recommendations:[/bold]")

        if automation_ready:
            console.print(
                "  🚀 [green]READY:[/green] All automation systems operational"
            )
            console.print("     - Provider switching validated")
            console.print("     - Performance targets met")
            console.print("     - Backup procedures ready")
        else:
            if not results.get("initialization", {}).get("successful", False):
                console.print(
                    "  🔧 [red]CRITICAL:[/red] Provider factory initialization failed"
                )

            config_issues = []
            for provider, config in config_results.items():
                if isinstance(config, dict) and not config.get(
                    "configuration_valid", False
                ):
                    config_issues.append(provider)

            if config_issues:
                console.print(
                    f"  🔧 [red]CONFIG:[/red] Fix configuration for: {', '.join(config_issues)}"
                )

            if not switching_results.get("all_switches_successful", False):
                console.print(
                    "  🔄 [yellow]SWITCHING:[/yellow] Provider switching needs attention"
                )

        console.print("\n" + "=" * 80)


@click.command()
@click.option("--validate", "-v", is_flag=True, help="Run comprehensive validation")
@click.option("--switch-test", "-s", is_flag=True, help="Test provider switching only")
@click.option("--backup", "-b", is_flag=True, help="Generate backup configuration")
@click.option("--optimize", "-o", is_flag=True, help="Run performance optimization")
async def main(validate: bool, switch_test: bool, backup: bool, optimize: bool):
    """F2 Provider Interface automation and operations management."""

    manager = ProviderAutomationManager()

    if validate:
        # Run comprehensive validation
        results = await manager.run_comprehensive_validation()
        manager.generate_automation_report(results)

    elif switch_test:
        # Test provider switching only
        await manager.initialize_factory()
        results = await manager.test_provider_switching()
        console.print(f"\nProvider switching results: {results}")

    elif backup:
        # Generate backup configuration
        backup_config = await manager.generate_backup_configuration()
        console.print(
            f"\nBackup configuration generated with {len(backup_config)} entries"
        )

    elif optimize:
        # Run performance optimization
        await manager.initialize_factory()
        perf_results = await manager.optimize_provider_performance()
        console.print(f"\nPerformance analysis complete: {perf_results}")

    else:
        # Default: run quick validation
        console.print("Running quick provider automation check...")
        await manager.initialize_factory()
        config_results = await manager.validate_all_configurations()
        switch_results = await manager.test_provider_switching()

        console.print("\nQuick Results:")
        console.print(
            f"  Configuration: {sum(1 for c in config_results.values() if isinstance(c, dict) and c.get('configuration_valid', False))}/{len(config_results)} valid"
        )
        console.print(
            f"  Switching: {switch_results.get('successful_switches', 0)}/{switch_results.get('total_providers_tested', 0)} successful"
        )


if __name__ == "__main__":
    asyncio.run(main())
