#!/bin/bash
# Environment-Specific Security Deployment Script for C1-Image Upload Security Pipeline
# Implements comprehensive security deployment automation with environment detection

set -euo pipefail
IFS=$'\n\t'

readonly SCRIPT_NAME="$(basename "$0")"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Configuration
readonly DEFAULT_ENVIRONMENT="development"
readonly DEFAULT_SECURITY_LEVEL="standard"
readonly LOG_FILE="/tmp/security-deployment-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Logging functions
log() {
    local level="$1"
    local message="$2"
    local timestamp
    timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    echo -e "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$1${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$1${NC}"
}

log_error() {
    log "ERROR" "${RED}$1${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$1${NC}"
}

# Usage information
usage() {
    cat << EOF
Usage: $SCRIPT_NAME [OPTIONS]

Environment-Specific Security Deployment for C1-Image Upload Security Pipeline

OPTIONS:
    -e, --environment    Target environment (development|testing|staging|production)
    -s, --security-level Security level (basic|standard|strict|paranoid)
    -v, --validate-only  Validate configuration without deployment
    -f, --force         Force deployment without confirmation
    -d, --dry-run       Show what would be deployed without executing
    -h, --help          Show this help message

EXAMPLES:
    $SCRIPT_NAME -e production -s strict
    $SCRIPT_NAME -e development -s standard --validate-only
    $SCRIPT_NAME -e staging -s strict --dry-run

ENVIRONMENTS:
    development  - Development environment with relaxed security
    testing      - Testing environment with moderate security
    staging      - Staging environment with production-like security
    production   - Production environment with maximum security

SECURITY LEVELS:
    basic        - Basic security configuration
    standard     - Standard security with monitoring
    strict       - Strict security with comprehensive validation
    paranoid     - Maximum security with extensive hardening

EOF
}

# Environment detection
detect_environment() {
    local detected_env="$DEFAULT_ENVIRONMENT"
    
    # Check environment variables
    if [[ -n "${ENVIRONMENT:-}" ]]; then
        detected_env="$ENVIRONMENT"
    elif [[ -n "${FLASK_ENV:-}" ]]; then
        case "$FLASK_ENV" in
            production) detected_env="production" ;;
            development) detected_env="development" ;;
            testing) detected_env="testing" ;;
        esac
    elif [[ -n "${NODE_ENV:-}" ]]; then
        case "$NODE_ENV" in
            production) detected_env="production" ;;
            development) detected_env="development" ;;
            test) detected_env="testing" ;;
        esac
    fi
    
    # Check for production indicators
    if [[ -f "/opt/sora/production.marker" ]] || [[ "$HOSTNAME" == *"prod"* ]]; then
        detected_env="production"
    elif [[ -f "/opt/sora/staging.marker" ]] || [[ "$HOSTNAME" == *"staging"* ]]; then
        detected_env="staging"
    fi
    
    echo "$detected_env"
}

# Security level detection based on environment
detect_security_level() {
    local environment="$1"
    local detected_level="$DEFAULT_SECURITY_LEVEL"
    
    case "$environment" in
        production)
            detected_level="strict"
            ;;
        staging)
            detected_level="standard"
            ;;
        testing)
            detected_level="standard"
            ;;
        development)
            detected_level="basic"
            ;;
    esac
    
    echo "$detected_level"
}

# Configuration validation
validate_configuration() {
    local environment="$1"
    local security_level="$2"
    
    log_info "Validating configuration for environment: $environment, security level: $security_level"
    
    # Validate environment
    case "$environment" in
        development|testing|staging|production)
            log_success "Environment '$environment' is valid"
            ;;
        *)
            log_error "Invalid environment: $environment"
            return 1
            ;;
    esac
    
    # Validate security level
    case "$security_level" in
        basic|standard|strict|paranoid)
            log_success "Security level '$security_level' is valid"
            ;;
        *)
            log_error "Invalid security level: $security_level"
            return 1
            ;;
    esac
    
    # Environment-specific validation
    case "$environment" in
        production)
            if [[ "$security_level" == "basic" ]]; then
                log_error "Production environment requires at least 'standard' security level"
                return 1
            fi
            
            # Check for required production secrets
            local required_secrets=(
                "AZURE_OPENAI_API_KEY"
                "SECRET_KEY"
                "DB_PASSWORD"
            )
            
            for secret in "${required_secrets[@]}"; do
                if [[ -z "${!secret:-}" ]]; then
                    log_error "Required production secret '$secret' is not set"
                    return 1
                fi
            done
            ;;
        
        staging)
            if [[ "$security_level" == "basic" ]]; then
                log_warn "Staging environment should use at least 'standard' security level"
            fi
            ;;
    esac
    
    log_success "Configuration validation passed"
    return 0
}

# Generate environment-specific configuration
generate_environment_config() {
    local environment="$1"
    local security_level="$2"
    local output_file="$3"
    
    log_info "Generating configuration for $environment environment with $security_level security"
    
    cat > "$output_file" << EOF
# Environment-Specific Security Configuration
# Generated on $(date -u +"%Y-%m-%dT%H:%M:%SZ")
# Environment: $environment
# Security Level: $security_level

# Base Configuration
ENVIRONMENT=$environment
SECURITY_LEVEL=$security_level
FLASK_ENV=production
DEBUG=false

# Security Headers
SECURITY_HEADERS_ENABLED=true
RATE_LIMITING_ENABLED=true
INPUT_VALIDATION_STRICT=true
AUDIT_LOGGING_ENABLED=true

# Image Security Pipeline
IMAGE_SECURITY_ENABLED=true
MALWARE_SCANNING_ENABLED=true
CONTENT_FILTERING_ENABLED=true
THREAT_DETECTION_ENABLED=true

EOF

    # Environment-specific settings
    case "$environment" in
        development)
            cat >> "$output_file" << EOF
# Development Environment Settings
LOG_LEVEL=DEBUG
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
MAX_CONTENT_LENGTH=104857600
WORKER_CONCURRENCY=1
ENABLE_SWAGGER=true

# Development Database
DATABASE_URL=postgresql://sora_user:\${DB_PASSWORD}@postgres:5432/sora_development

# Development Security (Relaxed)
MALWARE_SCANNING_TIMEOUT=30
VALIDATION_TIMEOUT=10
ALLOW_HTTP=true

EOF
            ;;
        
        testing)
            cat >> "$output_file" << EOF
# Testing Environment Settings
LOG_LEVEL=INFO
RATE_LIMIT_REQUESTS_PER_MINUTE=500
MAX_CONTENT_LENGTH=52428800
WORKER_CONCURRENCY=2
ENABLE_SWAGGER=false

# Testing Database
DATABASE_URL=postgresql://sora_user:\${DB_PASSWORD}@postgres:5432/sora_testing

# Testing Security (Moderate)
MALWARE_SCANNING_TIMEOUT=45
VALIDATION_TIMEOUT=15
ALLOW_HTTP=false

EOF
            ;;
        
        staging)
            cat >> "$output_file" << EOF
# Staging Environment Settings
LOG_LEVEL=INFO
RATE_LIMIT_REQUESTS_PER_MINUTE=100
MAX_CONTENT_LENGTH=26214400
WORKER_CONCURRENCY=3
ENABLE_SWAGGER=false

# Staging Database
DATABASE_URL=postgresql://sora_user:\${DB_PASSWORD}@postgres:5432/sora_staging

# Staging Security (Production-like)
MALWARE_SCANNING_TIMEOUT=60
VALIDATION_TIMEOUT=20
ALLOW_HTTP=false
SSL_REQUIRED=true

EOF
            ;;
        
        production)
            cat >> "$output_file" << EOF
# Production Environment Settings
LOG_LEVEL=WARNING
RATE_LIMIT_REQUESTS_PER_MINUTE=60
MAX_CONTENT_LENGTH=26214400
WORKER_CONCURRENCY=4
ENABLE_SWAGGER=false

# Production Database
DATABASE_URL=postgresql://sora_user:\${DB_PASSWORD}@postgres:5432/sora_production

# Production Security (Maximum)
MALWARE_SCANNING_TIMEOUT=90
VALIDATION_TIMEOUT=30
ALLOW_HTTP=false
SSL_REQUIRED=true
HSTS_ENABLED=true

EOF
            ;;
    esac
    
    # Security level-specific settings
    case "$security_level" in
        basic)
            cat >> "$output_file" << EOF
# Basic Security Settings
SECURITY_SCAN_FREQUENCY=3600
BACKUP_FREQUENCY=86400
VULNERABILITY_SCAN_ENABLED=false
PENETRATION_TESTING=false

EOF
            ;;
        
        standard)
            cat >> "$output_file" << EOF
# Standard Security Settings
SECURITY_SCAN_FREQUENCY=1800
BACKUP_FREQUENCY=21600
VULNERABILITY_SCAN_ENABLED=true
PENETRATION_TESTING=false
INCIDENT_RESPONSE_ENABLED=true

EOF
            ;;
        
        strict)
            cat >> "$output_file" << EOF
# Strict Security Settings
SECURITY_SCAN_FREQUENCY=900
BACKUP_FREQUENCY=3600
VULNERABILITY_SCAN_ENABLED=true
PENETRATION_TESTING=true
INCIDENT_RESPONSE_ENABLED=true
AUTO_THREAT_MITIGATION=true
COMPLIANCE_MONITORING=true

EOF
            ;;
        
        paranoid)
            cat >> "$output_file" << EOF
# Paranoid Security Settings
SECURITY_SCAN_FREQUENCY=300
BACKUP_FREQUENCY=1800
VULNERABILITY_SCAN_ENABLED=true
PENETRATION_TESTING=true
INCIDENT_RESPONSE_ENABLED=true
AUTO_THREAT_MITIGATION=true
COMPLIANCE_MONITORING=true
BEHAVIORAL_ANALYSIS=true
ZERO_TRUST_MODE=true

EOF
            ;;
    esac
    
    log_success "Configuration generated: $output_file"
}

# Deploy security configuration
deploy_security_configuration() {
    local environment="$1"
    local security_level="$2"
    local dry_run="$3"
    
    log_info "Deploying security configuration for $environment environment"
    
    # Generate Docker Compose override for environment
    local compose_override="$PROJECT_ROOT/docker-compose.$environment.override.yml"
    
    if [[ "$dry_run" == "true" ]]; then
        log_info "[DRY RUN] Would generate Docker Compose override: $compose_override"
    else
        cat > "$compose_override" << EOF
# Environment-Specific Docker Compose Override
# Environment: $environment
# Security Level: $security_level

version: '3.8'

services:
  app:
    build:
      dockerfile: src/deployment/docker/Dockerfile.hardened
      args:
        SECURITY_LEVEL: $security_level
    environment:
      ENVIRONMENT: $environment
      SECURITY_LEVEL: $security_level
    
EOF
        
        # Add environment-specific service configurations
        case "$environment" in
            production)
                cat >> "$compose_override" << EOF
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        
  nginx:
    deploy:
      replicas: 2
      
  worker:
    deploy:
      replicas: 4

EOF
                ;;
            
            staging)
                cat >> "$compose_override" << EOF
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 768M
          cpus: '0.75'
        reservations:
          memory: 384M
          cpus: '0.25'
          
  worker:
    deploy:
      replicas: 2

EOF
                ;;
        esac
        
        log_success "Docker Compose override generated: $compose_override"
    fi
    
    # Generate environment-specific configuration
    local env_config="$PROJECT_ROOT/.env.$environment"
    if [[ "$dry_run" == "true" ]]; then
        log_info "[DRY RUN] Would generate environment configuration: $env_config"
    else
        generate_environment_config "$environment" "$security_level" "$env_config"
    fi
    
    # Copy security configurations
    local security_config_dir="$PROJECT_ROOT/src/deployment/security/configs"
    if [[ "$dry_run" == "true" ]]; then
        log_info "[DRY RUN] Would copy security configurations to: $security_config_dir"
    else
        # Create environment-specific security directory
        mkdir -p "$security_config_dir/$environment"
        
        # Copy hardened Docker configuration
        cp "$security_config_dir/docker-security-hardened.yml" "$security_config_dir/$environment/"
        
        log_success "Security configurations copied for $environment"
    fi
    
    # Generate deployment commands
    generate_deployment_commands "$environment" "$security_level" "$dry_run"
}

# Generate deployment commands
generate_deployment_commands() {
    local environment="$1"
    local security_level="$2"
    local dry_run="$3"
    
    local commands_file="$PROJECT_ROOT/deploy-$environment.sh"
    
    if [[ "$dry_run" == "true" ]]; then
        log_info "[DRY RUN] Would generate deployment script: $commands_file"
        return
    fi
    
    cat > "$commands_file" << EOF
#!/bin/bash
# Auto-generated deployment script for $environment environment
# Generated on $(date -u +"%Y-%m-%dT%H:%M:%SZ")

set -euo pipefail

readonly ENVIRONMENT="$environment"
readonly SECURITY_LEVEL="$security_level"

# Load environment configuration
source .env.$environment

# Pre-deployment validation
echo "Starting deployment validation..."

# Validate Docker and Docker Compose
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "ERROR: Docker Compose is not installed"
    exit 1
fi

# Security validation
echo "Performing security validation..."

# Check for required secrets
required_secrets=(
    "SECRET_KEY"
    "DB_PASSWORD"
    "AZURE_OPENAI_API_KEY"
)

for secret in "\${required_secrets[@]}"; do
    if [[ -z "\${!secret:-}" ]]; then
        echo "ERROR: Required secret '\$secret' is not set"
        exit 1
    fi
done

# Deploy with security configuration
echo "Deploying $environment environment with $security_level security..."

# Use environment-specific compose files
docker-compose \\
    -f docker-compose.yml \\
    -f docker-compose.$environment.override.yml \\
    -f src/deployment/security/configs/docker-security-hardened.yml \\
    up -d --build

# Post-deployment validation
echo "Performing post-deployment validation..."

# Wait for services to be healthy
timeout=300
counter=0

while [[ \$counter -lt \$timeout ]]; do
    if docker-compose ps | grep -q "healthy"; then
        echo "Services are healthy"
        break
    fi
    
    sleep 5
    counter=\$((counter + 5))
done

if [[ \$counter -eq \$timeout ]]; then
    echo "ERROR: Services failed to become healthy within \$timeout seconds"
    exit 1
fi

# Security validation endpoint
if curl -f http://localhost:5001/health/security &> /dev/null; then
    echo "Security validation passed"
else
    echo "ERROR: Security validation failed"
    exit 1
fi

echo "Deployment completed successfully for $environment environment"

EOF
    
    chmod +x "$commands_file"
    log_success "Deployment script generated: $commands_file"
}

# Main deployment function
main() {
    local environment="$DEFAULT_ENVIRONMENT"
    local security_level=""
    local validate_only="false"
    local force="false"
    local dry_run="false"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                environment="$2"
                shift 2
                ;;
            -s|--security-level)
                security_level="$2"
                shift 2
                ;;
            -v|--validate-only)
                validate_only="true"
                shift
                ;;
            -f|--force)
                force="true"
                shift
                ;;
            -d|--dry-run)
                dry_run="true"
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Auto-detect environment if not specified
    if [[ "$environment" == "$DEFAULT_ENVIRONMENT" ]]; then
        detected_env=$(detect_environment)
        if [[ "$detected_env" != "$DEFAULT_ENVIRONMENT" ]]; then
            log_info "Auto-detected environment: $detected_env"
            environment="$detected_env"
        fi
    fi
    
    # Auto-detect security level if not specified
    if [[ -z "$security_level" ]]; then
        security_level=$(detect_security_level "$environment")
        log_info "Auto-detected security level: $security_level"
    fi
    
    log_info "Starting security deployment for $environment environment with $security_level security level"
    
    # Validation
    if ! validate_configuration "$environment" "$security_level"; then
        log_error "Configuration validation failed"
        exit 1
    fi
    
    if [[ "$validate_only" == "true" ]]; then
        log_success "Configuration validation completed successfully"
        exit 0
    fi
    
    # Confirmation for production deployments
    if [[ "$environment" == "production" ]] && [[ "$force" != "true" ]] && [[ "$dry_run" != "true" ]]; then
        echo -n "Are you sure you want to deploy to PRODUCTION environment? (yes/no): "
        read -r confirmation
        
        if [[ "$confirmation" != "yes" ]]; then
            log_info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Deploy security configuration
    deploy_security_configuration "$environment" "$security_level" "$dry_run"
    
    if [[ "$dry_run" == "true" ]]; then
        log_success "Dry run completed - no changes were made"
    else
        log_success "Security deployment completed successfully"
        log_info "Log file: $LOG_FILE"
    fi
}

# Execute main function
main "$@"