#!/usr/bin/env python3
"""
F3 Mock Veo3 Provider - Integration Test Suite

Comprehensive integration testing for mock provider deployment
including environment switching, health monitoring, and operational validation.
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

console = Console()


class MockVeo3IntegrationTest:
    """
    Comprehensive integration test suite for F3 Mock Veo3 Provider.

    Tests deployment readiness, environment switching, monitoring integration,
    and end-to-end operational workflows across different deployment scenarios.
    """

    def __init__(self) -> None:
        """Initialize integration test suite."""
        self.console = console
        self.test_results = {}

        # Test configuration
        self.test_environments = ["development", "staging"]
        self.test_scenarios = [
            {
                "name": "basic_mock_functionality",
                "description": "Test basic mock provider functionality",
                "critical": True,
            },
            {
                "name": "environment_switching",
                "description": "Test mock/real provider switching",
                "critical": True,
            },
            {
                "name": "health_monitoring",
                "description": "Test health checks and monitoring",
                "critical": True,
            },
            {
                "name": "performance_validation",
                "description": "Test performance and timing accuracy",
                "critical": False,
            },
            {
                "name": "storage_management",
                "description": "Test storage and cleanup functionality",
                "critical": False,
            },
        ]

    async def test_basic_mock_functionality(self) -> Dict[str, Any]:
        """
        Test basic mock provider functionality.

        Returns:
            Dict[str, Any]: Test results
        """
        console.print("\n[bold blue]🧪 Testing Basic Mock Functionality[/bold blue]")

        test_result = {
            "test_name": "basic_mock_functionality",
            "success": False,
            "sub_tests": {},
            "error": None,
        }

        try:
            # Test 1: Provider factory integration
            console.print("Testing provider factory integration...")
            try:
                from src.features.video_generation import get_provider_factory

                factory = get_provider_factory()
                available_providers = factory.get_available_providers()

                veo3_available = "google_veo3" in available_providers
                test_result["sub_tests"]["provider_factory"] = {
                    "success": veo3_available,
                    "available_providers": available_providers,
                }

                if veo3_available:
                    console.print("✅ Provider factory integration successful")
                else:
                    console.print("❌ Google Veo3 provider not available in factory")
                    test_result["error"] = "Provider not available in factory"
                    return test_result

            except Exception as e:
                test_result["sub_tests"]["provider_factory"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Provider factory test failed: {e}")
                test_result["error"] = f"Provider factory error: {e}"
                return test_result

            # Test 2: Mock provider creation
            console.print("Testing mock provider creation...")
            try:
                provider = factory.create_provider("google_veo3")
                is_mock = getattr(provider, "is_mock", False)
                provider_name = getattr(provider, "provider_name", "unknown")
                supported_features = getattr(provider, "supported_features", {})

                test_result["sub_tests"]["provider_creation"] = {
                    "success": True,
                    "is_mock": is_mock,
                    "provider_name": provider_name,
                    "supported_features": supported_features,
                }

                console.print(f"✅ Mock provider created (is_mock: {is_mock})")

            except Exception as e:
                test_result["sub_tests"]["provider_creation"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Provider creation failed: {e}")
                test_result["error"] = f"Provider creation error: {e}"
                return test_result

            # Test 3: Health check functionality
            console.print("Testing health check functionality...")
            try:
                health_result = await provider.health_check()
                health_success = isinstance(
                    health_result, dict
                ) and not health_result.get("error")

                test_result["sub_tests"]["health_check"] = {
                    "success": health_success,
                    "health_result": health_result,
                }

                if health_success:
                    console.print("✅ Health check passed")
                else:
                    console.print(f"❌ Health check failed: {health_result}")

            except Exception as e:
                test_result["sub_tests"]["health_check"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Health check test failed: {e}")

            # Test 4: Mock provider capabilities
            console.print("Testing mock provider capabilities...")
            try:
                features = provider.supported_features
                expected_features = ["mock_provider", "image_input", "custom_duration"]

                capabilities_valid = all(
                    features.get(feature, False) for feature in expected_features
                )

                test_result["sub_tests"]["capabilities"] = {
                    "success": capabilities_valid,
                    "features": features,
                    "expected_features": expected_features,
                }

                if capabilities_valid:
                    console.print("✅ Mock provider capabilities validated")
                else:
                    console.print("❌ Some expected capabilities missing")

            except Exception as e:
                test_result["sub_tests"]["capabilities"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Capabilities test failed: {e}")

            # Overall success determination
            sub_test_success = all(
                test.get("success", False) for test in test_result["sub_tests"].values()
            )
            test_result["success"] = sub_test_success

            if sub_test_success:
                console.print("✅ Basic mock functionality test passed")
            else:
                console.print("❌ Basic mock functionality test failed")

        except Exception as e:
            test_result["error"] = str(e)
            console.print(f"❌ Basic functionality test failed: {e}")

        return test_result

    async def test_environment_switching(self) -> Dict[str, Any]:
        """
        Test environment switching functionality.

        Returns:
            Dict[str, Any]: Test results
        """
        console.print("\n[bold blue]🔄 Testing Environment Switching[/bold blue]")

        test_result = {
            "test_name": "environment_switching",
            "success": False,
            "sub_tests": {},
            "error": None,
        }

        try:
            from src.features.video_generation import get_provider_factory

            # Test 1: Current environment detection
            console.print("Testing current environment detection...")
            try:
                current_mock_setting = (
                    os.getenv("USE_MOCK_VEO3", "true").lower() == "true"
                )
                mock_enabled = os.getenv("MOCK_VEO3_ENABLED", "true").lower() == "true"
                deployment_env = os.getenv("DEPLOYMENT_ENVIRONMENT", "development")

                test_result["sub_tests"]["environment_detection"] = {
                    "success": True,
                    "use_mock_veo3": current_mock_setting,
                    "mock_veo3_enabled": mock_enabled,
                    "deployment_environment": deployment_env,
                    "settings_consistent": current_mock_setting == mock_enabled,
                }

                console.print(
                    f"✅ Environment: {deployment_env} (mock: {current_mock_setting})"
                )

            except Exception as e:
                test_result["sub_tests"]["environment_detection"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Environment detection failed: {e}")

            # Test 2: Provider switching capability
            console.print("Testing provider switching capability...")
            try:
                factory = get_provider_factory()
                available_providers = factory.get_available_providers()

                # Test creating different provider instances
                switching_results = {}

                for provider_name in ["google_veo3", "google_veo3_mock"]:
                    if provider_name in available_providers:
                        try:
                            provider = factory.create_provider(provider_name)
                            is_mock = getattr(provider, "is_mock", False)

                            switching_results[provider_name] = {
                                "creation_success": True,
                                "is_mock": is_mock,
                            }

                            console.print(
                                f"✅ {provider_name}: Created (mock: {is_mock})"
                            )

                        except Exception as e:
                            switching_results[provider_name] = {
                                "creation_success": False,
                                "error": str(e),
                            }
                            console.print(f"❌ {provider_name}: Creation failed - {e}")

                test_result["sub_tests"]["provider_switching"] = {
                    "success": len(switching_results) > 0,
                    "available_providers": available_providers,
                    "switching_results": switching_results,
                }

            except Exception as e:
                test_result["sub_tests"]["provider_switching"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Provider switching test failed: {e}")

            # Test 3: Configuration validation
            console.print("Testing configuration validation...")
            try:
                from src.config.factory import ConfigurationFactory

                # Test Azure configuration (should always be available)
                azure_config = ConfigurationFactory.get_azure_config()
                azure_valid = bool(azure_config.get("endpoint"))

                # Test Veo3 configuration (mock or real)
                try:
                    veo3_config = ConfigurationFactory.create_veo3_config()
                    veo3_valid = veo3_config.use_mock or bool(veo3_config.project_id)
                except:
                    veo3_valid = False

                test_result["sub_tests"]["configuration_validation"] = {
                    "success": azure_valid and veo3_valid,
                    "azure_config_valid": azure_valid,
                    "veo3_config_valid": veo3_valid,
                }

                if azure_valid and veo3_valid:
                    console.print("✅ Configuration validation passed")
                else:
                    console.print("❌ Configuration validation failed")

            except Exception as e:
                test_result["sub_tests"]["configuration_validation"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Configuration validation failed: {e}")

            # Overall success determination
            sub_test_success = all(
                test.get("success", False) for test in test_result["sub_tests"].values()
            )
            test_result["success"] = sub_test_success

        except Exception as e:
            test_result["error"] = str(e)
            console.print(f"❌ Environment switching test failed: {e}")

        return test_result

    async def test_health_monitoring(self) -> Dict[str, Any]:
        """
        Test health monitoring functionality.

        Returns:
            Dict[str, Any]: Test results
        """
        console.print("\n[bold blue]🏥 Testing Health Monitoring[/bold blue]")

        test_result = {
            "test_name": "health_monitoring",
            "success": False,
            "sub_tests": {},
            "error": None,
        }

        try:
            # Test 1: Mock provider health check
            console.print("Testing mock provider health check...")
            try:
                from src.monitoring.mock_veo3_health import MockVeo3HealthCheck

                health_checker = MockVeo3HealthCheck()

                # Test individual health components
                provider_health = await health_checker.check_provider_health()
                timing_health = await health_checker.check_timing_accuracy()
                environment_health = await health_checker.check_environment_switching()

                test_result["sub_tests"]["health_components"] = {
                    "success": all(
                        [
                            provider_health.status in ["healthy", "degraded"],
                            timing_health.status in ["healthy", "degraded"],
                            environment_health.status in ["healthy", "degraded"],
                        ]
                    ),
                    "provider_health": provider_health.status,
                    "timing_health": timing_health.status,
                    "environment_health": environment_health.status,
                }

                console.print(
                    f"✅ Health components: Provider={provider_health.status}, Timing={timing_health.status}, Environment={environment_health.status}"
                )

            except Exception as e:
                test_result["sub_tests"]["health_components"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Health components test failed: {e}")

            # Test 2: Metrics collection
            console.print("Testing metrics collection...")
            try:
                from src.monitoring.mock_veo3_metrics import get_mock_veo3_metrics

                metrics = get_mock_veo3_metrics()

                # Record some test metrics
                metrics.record_request_latency(1500, "test_operation")
                metrics.record_video_generation_success("test_gen_123", 5)
                metrics.record_timing_accuracy(2000, 1950, "test_timing")

                current_metrics = metrics.get_current_metrics()
                performance_summary = metrics.get_performance_summary()

                test_result["sub_tests"]["metrics_collection"] = {
                    "success": True,
                    "total_requests": current_metrics.get("total_requests", 0),
                    "successful_generations": current_metrics.get(
                        "successful_generations", 0
                    ),
                    "performance_status": performance_summary.get("performance_status"),
                }

                console.print(
                    f"✅ Metrics collection: {current_metrics.get('total_requests', 0)} requests, {performance_summary.get('performance_status')} status"
                )

            except Exception as e:
                test_result["sub_tests"]["metrics_collection"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Metrics collection test failed: {e}")

            # Test 3: Storage monitoring
            console.print("Testing storage monitoring...")
            try:
                from src.monitoring.mock_veo3_health import MockVeo3HealthCheck

                health_checker = MockVeo3HealthCheck()
                storage_usage = health_checker._get_storage_usage()

                test_result["sub_tests"]["storage_monitoring"] = {
                    "success": isinstance(storage_usage, dict),
                    "storage_info": storage_usage,
                }

                console.print(
                    f"✅ Storage monitoring: {storage_usage.get('used_mb', 0):.2f}MB used"
                )

            except Exception as e:
                test_result["sub_tests"]["storage_monitoring"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Storage monitoring test failed: {e}")

            # Overall success determination
            sub_test_success = all(
                test.get("success", False) for test in test_result["sub_tests"].values()
            )
            test_result["success"] = sub_test_success

        except Exception as e:
            test_result["error"] = str(e)
            console.print(f"❌ Health monitoring test failed: {e}")

        return test_result

    async def test_performance_validation(self) -> Dict[str, Any]:
        """
        Test performance validation functionality.

        Returns:
            Dict[str, Any]: Test results
        """
        console.print("\n[bold blue]⚡ Testing Performance Validation[/bold blue]")

        test_result = {
            "test_name": "performance_validation",
            "success": False,
            "sub_tests": {},
            "error": None,
        }

        try:
            # Test 1: Provider creation performance
            console.print("Testing provider creation performance...")
            try:
                from src.features.video_generation import get_provider_factory

                factory = get_provider_factory()
                creation_times = []

                # Test provider creation speed
                for i in range(5):
                    start_time = time.time()
                    provider = factory.create_provider("google_veo3")
                    creation_time = (time.time() - start_time) * 1000
                    creation_times.append(creation_time)

                avg_creation_time = sum(creation_times) / len(creation_times)
                max_creation_time = max(creation_times)

                # Performance threshold: should create provider in <100ms
                performance_acceptable = avg_creation_time < 100

                test_result["sub_tests"]["provider_creation_performance"] = {
                    "success": performance_acceptable,
                    "average_creation_time_ms": avg_creation_time,
                    "max_creation_time_ms": max_creation_time,
                    "creation_times": creation_times,
                }

                console.print(
                    f"✅ Provider creation: {avg_creation_time:.2f}ms average ({'✅' if performance_acceptable else '❌'} target)"
                )

            except Exception as e:
                test_result["sub_tests"]["provider_creation_performance"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Provider creation performance test failed: {e}")

            # Test 2: Mock timing accuracy
            console.print("Testing mock timing accuracy...")
            try:
                # Simulate timing tests with current environment settings
                expected_latency = int(os.getenv("MOCK_VEO3_LATENCY_MS", "2000"))
                simulate_timing = (
                    os.getenv("MOCK_VEO3_SIMULATE_REAL_TIMING", "true").lower()
                    == "true"
                )

                timing_tests = []
                for i in range(3):
                    test_start = time.time()

                    if simulate_timing:
                        await asyncio.sleep(expected_latency / 1000)

                    actual_time = (time.time() - test_start) * 1000

                    if simulate_timing:
                        accuracy = (
                            1 - abs(actual_time - expected_latency) / expected_latency
                        )
                        timing_acceptable = accuracy > 0.9  # Within 10%
                    else:
                        accuracy = 1.0 if actual_time < 100 else 0.0
                        timing_acceptable = actual_time < 100

                    timing_tests.append(
                        {
                            "expected_ms": expected_latency if simulate_timing else 0,
                            "actual_ms": actual_time,
                            "accuracy": accuracy,
                            "acceptable": timing_acceptable,
                        }
                    )

                overall_timing_success = all(
                    test["acceptable"] for test in timing_tests
                )

                test_result["sub_tests"]["timing_accuracy"] = {
                    "success": overall_timing_success,
                    "simulate_timing": simulate_timing,
                    "expected_latency_ms": expected_latency,
                    "timing_tests": timing_tests,
                }

                console.print(
                    f"✅ Timing accuracy: {'✅' if overall_timing_success else '❌'} (simulate: {simulate_timing})"
                )

            except Exception as e:
                test_result["sub_tests"]["timing_accuracy"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Timing accuracy test failed: {e}")

            # Overall success determination
            sub_test_success = all(
                test.get("success", False) for test in test_result["sub_tests"].values()
            )
            test_result["success"] = sub_test_success

        except Exception as e:
            test_result["error"] = str(e)
            console.print(f"❌ Performance validation test failed: {e}")

        return test_result

    async def test_storage_management(self) -> Dict[str, Any]:
        """
        Test storage management functionality.

        Returns:
            Dict[str, Any]: Test results
        """
        console.print("\n[bold blue]💾 Testing Storage Management[/bold blue]")

        test_result = {
            "test_name": "storage_management",
            "success": False,
            "sub_tests": {},
            "error": None,
        }

        try:
            # Test 1: Storage directory creation
            console.print("Testing storage directory creation...")
            try:
                mock_storage_path = Path(
                    os.getenv("MOCK_VEO3_VIDEO_STORAGE", "/tmp/mock_videos")
                )

                # Ensure directory exists
                mock_storage_path.mkdir(parents=True, exist_ok=True)

                directory_exists = mock_storage_path.exists()
                directory_writable = (
                    os.access(mock_storage_path, os.W_OK) if directory_exists else False
                )

                test_result["sub_tests"]["storage_directory"] = {
                    "success": directory_exists and directory_writable,
                    "path": str(mock_storage_path),
                    "exists": directory_exists,
                    "writable": directory_writable,
                }

                console.print(
                    f"✅ Storage directory: {mock_storage_path} ({'✅' if directory_exists and directory_writable else '❌'})"
                )

            except Exception as e:
                test_result["sub_tests"]["storage_directory"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Storage directory test failed: {e}")

            # Test 2: Storage usage monitoring
            console.print("Testing storage usage monitoring...")
            try:
                from src.monitoring.mock_veo3_health import MockVeo3HealthCheck

                health_checker = MockVeo3HealthCheck()
                storage_usage = health_checker._get_storage_usage()

                usage_valid = (
                    isinstance(storage_usage, dict) and "used_mb" in storage_usage
                )

                test_result["sub_tests"]["storage_usage"] = {
                    "success": usage_valid,
                    "storage_data": storage_usage,
                }

                if usage_valid:
                    console.print(
                        f"✅ Storage usage: {storage_usage.get('used_mb', 0):.2f}MB"
                    )
                else:
                    console.print("❌ Storage usage monitoring failed")

            except Exception as e:
                test_result["sub_tests"]["storage_usage"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Storage usage test failed: {e}")

            # Test 3: Cleanup configuration
            console.print("Testing cleanup configuration...")
            try:
                cleanup_enabled = (
                    os.getenv("MOCK_VEO3_CLEANUP_ENABLED", "true").lower() == "true"
                )
                cleanup_interval = int(
                    os.getenv("MOCK_VEO3_CLEANUP_INTERVAL_HOURS", "24")
                )
                max_storage_mb = int(os.getenv("MOCK_VEO3_MAX_STORAGE_MB", "1000"))

                config_valid = cleanup_interval > 0 and max_storage_mb > 0

                test_result["sub_tests"]["cleanup_configuration"] = {
                    "success": config_valid,
                    "cleanup_enabled": cleanup_enabled,
                    "cleanup_interval_hours": cleanup_interval,
                    "max_storage_mb": max_storage_mb,
                }

                console.print(
                    f"✅ Cleanup config: enabled={cleanup_enabled}, interval={cleanup_interval}h, limit={max_storage_mb}MB"
                )

            except Exception as e:
                test_result["sub_tests"]["cleanup_configuration"] = {
                    "success": False,
                    "error": str(e),
                }
                console.print(f"❌ Cleanup configuration test failed: {e}")

            # Overall success determination
            sub_test_success = all(
                test.get("success", False) for test in test_result["sub_tests"].values()
            )
            test_result["success"] = sub_test_success

        except Exception as e:
            test_result["error"] = str(e)
            console.print(f"❌ Storage management test failed: {e}")

        return test_result

    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """
        Run comprehensive integration test suite.

        Returns:
            Dict[str, Any]: Complete test results
        """
        console.print(
            Panel.fit(
                "[bold blue]F3 Mock Veo3 Provider Integration Test Suite[/bold blue]\n"
                "[white]Comprehensive deployment and operational validation[/white]\n"
                "[yellow]Testing all critical functionality[/yellow]",
                title="🧪 Integration Tests",
                border_style="blue",
            )
        )

        test_results = {}

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            transient=True,
        ) as progress:
            # Run all test scenarios
            for scenario in self.test_scenarios:
                test_name = scenario["name"]
                test_description = scenario["description"]

                task = progress.add_task(f"Running {test_description}...", total=1)

                if test_name == "basic_mock_functionality":
                    result = await self.test_basic_mock_functionality()
                elif test_name == "environment_switching":
                    result = await self.test_environment_switching()
                elif test_name == "health_monitoring":
                    result = await self.test_health_monitoring()
                elif test_name == "performance_validation":
                    result = await self.test_performance_validation()
                elif test_name == "storage_management":
                    result = await self.test_storage_management()
                else:
                    result = {
                        "test_name": test_name,
                        "success": False,
                        "error": "Test not implemented",
                    }

                test_results[test_name] = result
                progress.update(task, completed=1)

        return test_results

    def generate_test_report(self, test_results: Dict[str, Any]) -> None:
        """
        Generate comprehensive test report.

        Args:
            test_results: Test results from run_comprehensive_tests
        """
        console.print("\n" + "=" * 80)
        console.print("[bold blue]F3 Mock Veo3 Integration Test Report[/bold blue]")
        console.print("=" * 80)

        # Overall test status
        total_tests = len(test_results)
        successful_tests = sum(
            1 for result in test_results.values() if result.get("success", False)
        )
        critical_tests = [
            test_name
            for test_name in test_results.keys()
            if any(
                scenario["name"] == test_name and scenario["critical"]
                for scenario in self.test_scenarios
            )
        ]
        critical_failures = [
            test_name
            for test_name in critical_tests
            if not test_results.get(test_name, {}).get("success", False)
        ]

        # Determine overall status
        if critical_failures:
            overall_status = "[bold red]❌ CRITICAL FAILURES[/bold red]"
        elif successful_tests == total_tests:
            overall_status = "[bold green]✅ ALL TESTS PASSED[/bold green]"
        else:
            overall_status = "[bold yellow]⚠️ SOME TESTS FAILED[/bold yellow]"

        console.print(f"\n[bold]Overall Status:[/bold] {overall_status}")
        console.print(
            f"[bold]Test Summary:[/bold] {successful_tests}/{total_tests} tests passed"
        )

        # Individual test results
        console.print("\n[bold]Test Results:[/bold]")

        table = Table(title="Integration Test Results")
        table.add_column("Test Name", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Critical", style="yellow")
        table.add_column("Details", style="white")

        for scenario in self.test_scenarios:
            test_name = scenario["name"]
            test_result = test_results.get(test_name, {})

            success = test_result.get("success", False)
            critical = scenario["critical"]

            status_icon = "✅" if success else "❌"
            critical_icon = "🔥" if critical else "📋"

            # Get details
            if success:
                details = "All sub-tests passed"
            else:
                error = test_result.get("error")
                if error:
                    details = f"Error: {error[:50]}..."
                else:
                    sub_tests = test_result.get("sub_tests", {})
                    failed_subtests = [
                        name
                        for name, result in sub_tests.items()
                        if not result.get("success", False)
                    ]
                    details = (
                        f"Failed: {', '.join(failed_subtests[:3])}"
                        if failed_subtests
                        else "Unknown failure"
                    )

            table.add_row(
                test_name.replace("_", " ").title(), status_icon, critical_icon, details
            )

        console.print(table)

        # Critical failures details
        if critical_failures:
            console.print(
                "\n[bold red]Critical Failures Requiring Attention:[/bold red]"
            )
            for test_name in critical_failures:
                test_result = test_results[test_name]
                console.print(
                    f"  • {test_name}: {test_result.get('error', 'See sub-test failures')}"
                )

        # Recommendations
        console.print("\n[bold]Recommendations:[/bold]")

        if not critical_failures and successful_tests == total_tests:
            console.print(
                "  🚀 [green]Integration tests passed - deployment ready[/green]"
            )
            console.print("  - Mock provider functionality validated")
            console.print("  - Environment switching operational")
            console.print("  - Health monitoring active")
            console.print("  - Performance within acceptable limits")

        else:
            console.print("  🔧 [red]Address failures before deployment[/red]")

            if not test_results.get("basic_mock_functionality", {}).get(
                "success", False
            ):
                console.print("    • Fix basic mock provider functionality")

            if not test_results.get("environment_switching", {}).get("success", False):
                console.print("    • Resolve environment switching issues")

            if not test_results.get("health_monitoring", {}).get("success", False):
                console.print("    • Fix health monitoring integration")

            if successful_tests < total_tests and not critical_failures:
                console.print(
                    "    • Address non-critical test failures when convenient"
                )

        console.print("\n" + "=" * 80)


@click.command()
@click.option(
    "--environment",
    "-e",
    default="development",
    help="Environment to test (affects configuration)",
)
@click.option("--critical-only", "-c", is_flag=True, help="Run only critical tests")
@click.option("--output-json", "-j", help="Output results to JSON file")
async def main(environment: str, critical_only: bool, output_json: Optional[str]):
    """F3 Mock Veo3 Provider comprehensive integration testing."""

    # Set environment for testing
    os.environ["DEPLOYMENT_ENVIRONMENT"] = environment
    os.environ["USE_MOCK_VEO3"] = "true"
    os.environ["MOCK_VEO3_ENABLED"] = "true"

    test_suite = MockVeo3IntegrationTest()

    if critical_only:
        # Filter to critical tests only
        test_suite.test_scenarios = [
            scenario for scenario in test_suite.test_scenarios if scenario["critical"]
        ]
        console.print("Running critical tests only...")

    # Run comprehensive tests
    results = await test_suite.run_comprehensive_tests()

    # Generate report
    test_suite.generate_test_report(results)

    # Output JSON if requested
    if output_json:
        with open(output_json, "w") as f:
            json.dump(results, f, indent=2, default=str)
        console.print(f"\n📄 Results saved to: {output_json}")


if __name__ == "__main__":
    asyncio.run(main())
