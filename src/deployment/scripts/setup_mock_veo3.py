#!/usr/bin/env python3
"""
F3 Mock Veo3 Provider Deployment Setup Script

Automated setup and validation for mock provider deployment across
development, staging, and production environments.
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Dict

import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

console = Console()


class MockVeo3DeploymentSetup:
    """
    Automated deployment setup for F3 Mock Veo3 Provider.

    Handles environment-specific configuration, directory creation,
    validation, and operational readiness checks.
    """

    def __init__(self, environment: str = "development") -> None:
        """
        Initialize deployment setup.

        Args:
            environment: Target deployment environment
        """
        self.environment = environment
        self.console = console
        self.logger = self._setup_logging()

        # Environment-specific configuration
        self.configs = {
            "development": {
                "latency_ms": 500,
                "processing_time": 5,
                "failure_rate": 0.1,
                "storage_mb": 500,
                "cleanup_hours": 1,
                "worker_concurrency": 1,
            },
            "staging": {
                "latency_ms": 1500,
                "processing_time": 12,
                "failure_rate": 0.05,
                "storage_mb": 2000,
                "cleanup_hours": 6,
                "worker_concurrency": 2,
            },
            "production": {
                "latency_ms": 2000,
                "processing_time": 20,
                "failure_rate": 0.01,
                "storage_mb": 5000,
                "cleanup_hours": 24,
                "worker_concurrency": 4,
            },
        }

    def _setup_logging(self):
        """Setup logging for deployment operations."""
        import logging

        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
        return logging.getLogger(__name__)

    def create_mock_directories(self) -> Dict[str, bool]:
        """
        Create required directories for mock provider operation.

        Returns:
            Dict[str, bool]: Directory creation results
        """
        console.print("\n[bold blue]📁 Creating Mock Provider Directories[/bold blue]")

        results = {}

        # Base directories
        base_dir = Path.cwd()
        directories = [
            base_dir / "data" / "mock_videos",
            base_dir / "data" / "mock_images",
            base_dir / "logs" / "mock_veo3",
            base_dir / "tmp" / "mock_processing",
        ]

        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)

                # Set appropriate permissions
                if os.name != "nt":  # Unix-like systems
                    os.chmod(directory, 0o755)

                results[str(directory)] = True
                console.print(f"✅ Created: {directory}")

            except Exception as e:
                results[str(directory)] = False
                console.print(f"❌ Failed to create {directory}: {e}")

        return results

    def validate_ffmpeg_availability(self) -> Dict[str, bool]:
        """
        Validate FFmpeg availability for video generation.

        Returns:
            Dict[str, bool]: FFmpeg validation results
        """
        console.print("\n[bold blue]🎬 Validating FFmpeg Availability[/bold blue]")

        results = {}

        try:
            # Check if FFmpeg is available
            result = os.system("ffmpeg -version > /dev/null 2>&1")
            ffmpeg_available = result == 0

            results["ffmpeg_available"] = ffmpeg_available

            if ffmpeg_available:
                console.print("✅ FFmpeg is available")

                # Test basic FFmpeg functionality
                try:
                    test_cmd = (
                        "ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 "
                        "-f webm -c:v libvpx-vp9 -crf 30 -b:v 200k "
                        "/tmp/ffmpeg_test.webm -y > /dev/null 2>&1"
                    )
                    test_result = os.system(test_cmd)

                    webm_encoding = test_result == 0
                    results["webm_encoding"] = webm_encoding

                    if webm_encoding:
                        console.print("✅ WebM/VP9 encoding supported")
                        # Clean up test file
                        try:
                            os.remove("/tmp/ffmpeg_test.webm")
                        except:
                            pass
                    else:
                        console.print("❌ WebM/VP9 encoding failed")

                except Exception as e:
                    results["webm_encoding"] = False
                    console.print(f"❌ WebM encoding test failed: {e}")
            else:
                console.print("❌ FFmpeg not available - mock videos will use fallback")
                results["webm_encoding"] = False

        except Exception as e:
            results["ffmpeg_available"] = False
            results["webm_encoding"] = False
            console.print(f"❌ FFmpeg validation failed: {e}")

        return results

    def setup_environment_configuration(self) -> Dict[str, str]:
        """
        Setup environment-specific configuration.

        Returns:
            Dict[str, str]: Generated configuration
        """
        console.print(
            f"\n[bold blue]⚙️ Setting up {self.environment.title()} Configuration[/bold blue]"
        )

        config = self.configs.get(self.environment, self.configs["development"])

        env_vars = {
            "USE_MOCK_VEO3": "true",
            "MOCK_VEO3_ENABLED": "true",
            "DEPLOYMENT_ENVIRONMENT": self.environment,
            "MOCK_VEO3_LATENCY_MS": str(config["latency_ms"]),
            "MOCK_VEO3_PROCESSING_TIME": str(config["processing_time"]),
            "MOCK_VEO3_FAILURE_RATE": str(config["failure_rate"]),
            "MOCK_VEO3_SIMULATE_REAL_TIMING": "true",
            "MOCK_VEO3_GENERATE_UNIQUE_VIDEOS": "true",
            "MOCK_VEO3_VALIDATE_INPUTS": "true",
            "MOCK_VEO3_VIDEO_STORAGE": "/app/mock_videos",
            "MOCK_VEO3_CLEANUP_ENABLED": "true",
            "MOCK_VEO3_CLEANUP_INTERVAL_HOURS": str(config["cleanup_hours"]),
            "MOCK_VEO3_MAX_STORAGE_MB": str(config["storage_mb"]),
            "WORKER_CONCURRENCY": str(config["worker_concurrency"]),
        }

        # Write environment file
        env_file_path = Path.cwd() / f".env.mock_veo3_{self.environment}"

        try:
            with open(env_file_path, "w") as f:
                f.write(
                    f"# F3 Mock Veo3 Provider - {self.environment.title()} Environment\n"
                )
                f.write("# Generated by setup_mock_veo3.py\n\n")

                for key, value in env_vars.items():
                    f.write(f"{key}={value}\n")

            console.print(f"✅ Configuration written to: {env_file_path}")

            # Display configuration summary
            table = Table(title=f"Mock Veo3 Configuration - {self.environment.title()}")
            table.add_column("Setting", style="cyan")
            table.add_column("Value", style="green")

            table.add_row("Latency Simulation", f"{config['latency_ms']}ms")
            table.add_row("Processing Time", f"{config['processing_time']}s")
            table.add_row("Failure Rate", f"{config['failure_rate']:.1%}")
            table.add_row("Storage Limit", f"{config['storage_mb']}MB")
            table.add_row("Cleanup Interval", f"{config['cleanup_hours']}h")
            table.add_row("Worker Concurrency", str(config["worker_concurrency"]))

            console.print(table)

        except Exception as e:
            console.print(f"❌ Failed to write configuration: {e}")
            env_vars["config_file_error"] = str(e)

        return env_vars

    async def test_mock_provider_integration(self) -> Dict[str, bool]:
        """
        Test mock provider integration with existing systems.

        Returns:
            Dict[str, bool]: Integration test results
        """
        console.print("\n[bold blue]🔧 Testing Mock Provider Integration[/bold blue]")

        results = {}

        try:
            # Test 1: Import mock provider modules
            try:
                from src.features.veo3_integration.provider_adapter import (
                    MockVeo3Client,
                )
                from src.features.video_generation import get_provider_factory

                results["module_imports"] = True
                console.print("✅ Mock provider modules imported successfully")
            except ImportError as e:
                results["module_imports"] = False
                console.print(f"❌ Module import failed: {e}")
                return results

            # Test 2: Provider factory integration
            try:
                factory = get_provider_factory()
                available_providers = factory.get_available_providers()

                veo3_available = "google_veo3" in available_providers
                results["provider_factory_integration"] = veo3_available

                if veo3_available:
                    console.print("✅ Veo3 provider available in factory")
                else:
                    console.print("❌ Veo3 provider not found in factory")

            except Exception as e:
                results["provider_factory_integration"] = False
                console.print(f"❌ Provider factory test failed: {e}")

            # Test 3: Mock provider creation
            if results.get("provider_factory_integration", False):
                try:
                    provider = factory.create_provider("google_veo3")
                    is_mock = getattr(provider, "is_mock", False)

                    results["mock_provider_creation"] = True
                    results["provider_is_mock"] = is_mock

                    console.print(f"✅ Mock provider created (is_mock: {is_mock})")

                except Exception as e:
                    results["mock_provider_creation"] = False
                    console.print(f"❌ Mock provider creation failed: {e}")

            # Test 4: Health check functionality
            if results.get("mock_provider_creation", False):
                try:
                    health_status = await provider.health_check()
                    health_ok = isinstance(
                        health_status, dict
                    ) and not health_status.get("error")

                    results["health_check"] = health_ok

                    if health_ok:
                        console.print("✅ Provider health check passed")
                    else:
                        console.print(
                            f"❌ Provider health check failed: {health_status}"
                        )

                except Exception as e:
                    results["health_check"] = False
                    console.print(f"❌ Health check test failed: {e}")

        except Exception as e:
            console.print(f"❌ Integration testing failed: {e}")
            results["integration_test_error"] = str(e)

        return results

    def validate_docker_integration(self) -> Dict[str, bool]:
        """
        Validate Docker integration and compose file compatibility.

        Returns:
            Dict[str, bool]: Docker validation results
        """
        console.print("\n[bold blue]🐳 Validating Docker Integration[/bold blue]")

        results = {}

        # Check if Docker is available
        try:
            docker_result = os.system("docker --version > /dev/null 2>&1")
            docker_available = docker_result == 0

            results["docker_available"] = docker_available

            if docker_available:
                console.print("✅ Docker is available")
            else:
                console.print("❌ Docker not available")
                return results

        except Exception as e:
            results["docker_available"] = False
            console.print(f"❌ Docker check failed: {e}")
            return results

        # Check Docker Compose files
        compose_files = [
            "src/deployment/docker/docker-compose.simple.yml",
            "src/deployment/docker/mock-veo3-override.yml",
        ]

        for compose_file in compose_files:
            file_path = Path.cwd() / compose_file
            file_exists = file_path.exists()

            results[f"compose_file_{Path(compose_file).name}"] = file_exists

            if file_exists:
                console.print(f"✅ Found: {compose_file}")
            else:
                console.print(f"❌ Missing: {compose_file}")

        # Test Docker Compose validation
        try:
            main_compose = (
                Path.cwd() / "src/deployment/docker/docker-compose.simple.yml"
            )
            override_compose = (
                Path.cwd() / "src/deployment/docker/mock-veo3-override.yml"
            )

            if main_compose.exists() and override_compose.exists():
                # Test compose file syntax
                compose_cmd = (
                    f"docker-compose -f {main_compose} -f {override_compose} "
                    "config > /dev/null 2>&1"
                )

                compose_result = os.system(compose_cmd)
                compose_valid = compose_result == 0

                results["compose_syntax_valid"] = compose_valid

                if compose_valid:
                    console.print("✅ Docker Compose configuration valid")
                else:
                    console.print("❌ Docker Compose configuration invalid")
            else:
                results["compose_syntax_valid"] = False

        except Exception as e:
            results["compose_syntax_valid"] = False
            console.print(f"❌ Compose validation failed: {e}")

        return results

    async def run_comprehensive_setup(self) -> Dict[str, Dict[str, any]]:
        """
        Run comprehensive mock provider deployment setup.

        Returns:
            Dict[str, Dict[str, any]]: Complete setup results
        """
        console.print(
            Panel.fit(
                f"[bold blue]F3 Mock Veo3 Provider Deployment Setup[/bold blue]\n"
                f"[white]Environment: {self.environment.title()}[/white]\n"
                f"[yellow]Automated setup and validation[/yellow]",
                title="🚀 Deployment Setup",
                border_style="blue",
            )
        )

        results = {}

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            transient=True,
        ) as progress:
            # Step 1: Create directories
            task1 = progress.add_task("Creating mock provider directories...", total=1)
            results["directory_creation"] = self.create_mock_directories()
            progress.update(task1, completed=1)

            # Step 2: Validate FFmpeg
            task2 = progress.add_task("Validating FFmpeg availability...", total=1)
            results["ffmpeg_validation"] = self.validate_ffmpeg_availability()
            progress.update(task2, completed=1)

            # Step 3: Setup configuration
            task3 = progress.add_task(
                "Setting up environment configuration...", total=1
            )
            results["environment_configuration"] = (
                self.setup_environment_configuration()
            )
            progress.update(task3, completed=1)

            # Step 4: Test integration
            task4 = progress.add_task("Testing mock provider integration...", total=1)
            results["integration_testing"] = await self.test_mock_provider_integration()
            progress.update(task4, completed=1)

            # Step 5: Validate Docker
            task5 = progress.add_task("Validating Docker integration...", total=1)
            results["docker_validation"] = self.validate_docker_integration()
            progress.update(task5, completed=1)

        return results

    def generate_setup_report(self, results: Dict[str, Dict[str, any]]) -> None:
        """
        Generate comprehensive setup validation report.

        Args:
            results: Setup results from run_comprehensive_setup
        """
        console.print("\n" + "=" * 80)
        console.print(
            f"[bold blue]F3 Mock Veo3 Deployment Setup Report - {self.environment.title()}[/bold blue]"
        )
        console.print("=" * 80)

        # Overall status assessment
        critical_checks = [
            any(results.get("directory_creation", {}).values()),
            results.get("integration_testing", {}).get(
                "provider_factory_integration", False
            ),
            results.get("integration_testing", {}).get("mock_provider_creation", False),
        ]

        setup_successful = all(critical_checks)

        if setup_successful:
            status = "[bold green]✅ SETUP SUCCESSFUL[/bold green]"
        else:
            status = "[bold red]❌ SETUP ISSUES[/bold red]"

        console.print(f"\n[bold]Overall Status:[/bold] {status}")

        # Directory Creation Summary
        dir_results = results.get("directory_creation", {})
        successful_dirs = sum(1 for success in dir_results.values() if success)
        console.print(
            f"\n[bold]Directory Creation:[/bold] {successful_dirs}/{len(dir_results)} successful"
        )

        # FFmpeg Validation Summary
        ffmpeg_results = results.get("ffmpeg_validation", {})
        console.print("\n[bold]FFmpeg Validation:[/bold]")
        console.print(
            f"  Available: {'✅' if ffmpeg_results.get('ffmpeg_available') else '❌'}"
        )
        console.print(
            f"  WebM Support: {'✅' if ffmpeg_results.get('webm_encoding') else '❌'}"
        )

        # Integration Testing Summary
        integration_results = results.get("integration_testing", {})
        console.print("\n[bold]Integration Testing:[/bold]")
        for test_name, result in integration_results.items():
            if isinstance(result, bool):
                status_icon = "✅" if result else "❌"
                console.print(f"  {test_name}: {status_icon}")

        # Docker Validation Summary
        docker_results = results.get("docker_validation", {})
        console.print("\n[bold]Docker Integration:[/bold]")
        for check_name, result in docker_results.items():
            if isinstance(result, bool):
                status_icon = "✅" if result else "❌"
                console.print(f"  {check_name}: {status_icon}")

        # Deployment Commands
        console.print(
            f"\n[bold]Deployment Commands for {self.environment.title()}:[/bold]"
        )

        if setup_successful:
            console.print("\n[green]Ready for deployment![/green]")

            if self.environment == "development":
                console.print("  # Development deployment:")
                console.print("  export $(cat .env.mock_veo3_development | xargs)")
                console.print(
                    "  docker-compose -f src/deployment/docker/docker-compose.simple.yml \\"
                )
                console.print(
                    "    -f src/deployment/docker/mock-veo3-override.yml up -d"
                )

            elif self.environment == "staging":
                console.print("  # Staging deployment:")
                console.print("  export $(cat .env.mock_veo3_staging | xargs)")
                console.print(
                    "  docker-compose -f src/deployment/docker/docker-compose.simple.yml \\"
                )
                console.print(
                    "    -f src/deployment/docker/mock-veo3-override.yml up -d"
                )

            else:  # production
                console.print("  # Production deployment (mock disabled by default):")
                console.print("  export $(cat .env.mock_veo3_production | xargs)")
                console.print("  # Note: USE_MOCK_VEO3=false in production config")

        else:
            console.print(
                "\n[red]Setup issues detected - resolve before deployment[/red]"
            )

            recommendations = []

            if not integration_results.get("module_imports", False):
                recommendations.append("Install missing Python dependencies")

            if not integration_results.get("provider_factory_integration", False):
                recommendations.append("Check provider factory configuration")

            if not ffmpeg_results.get("ffmpeg_available", False):
                recommendations.append("Install FFmpeg for video generation")

            if not docker_results.get("docker_available", False):
                recommendations.append("Install Docker for containerized deployment")

            for recommendation in recommendations:
                console.print(f"  • {recommendation}")

        console.print("\n" + "=" * 80)


@click.command()
@click.option(
    "--environment",
    "-e",
    type=click.Choice(["development", "staging", "production"]),
    default="development",
    help="Target deployment environment",
)
@click.option(
    "--validate-only",
    "-v",
    is_flag=True,
    help="Only run validation, don't create files or directories",
)
async def main(environment: str, validate_only: bool):
    """F3 Mock Veo3 Provider deployment setup and validation."""

    setup_manager = MockVeo3DeploymentSetup(environment)

    if validate_only:
        # Run validation-only tests
        console.print(f"Running validation for {environment} environment...")

        ffmpeg_results = setup_manager.validate_ffmpeg_availability()
        integration_results = await setup_manager.test_mock_provider_integration()
        docker_results = setup_manager.validate_docker_integration()

        validation_results = {
            "ffmpeg_validation": ffmpeg_results,
            "integration_testing": integration_results,
            "docker_validation": docker_results,
        }

        setup_manager.generate_setup_report(validation_results)

    else:
        # Run comprehensive setup
        results = await setup_manager.run_comprehensive_setup()
        setup_manager.generate_setup_report(results)


if __name__ == "__main__":
    asyncio.run(main())
