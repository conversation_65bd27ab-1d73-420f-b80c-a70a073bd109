"""
Security Monitoring and Observability for C1-Image Upload Security Pipeline.

Comprehensive security event logging, malicious file detection alerts, performance monitoring,
and real-time security observability for the image upload security pipeline.
"""

import hashlib
import json
import logging
import os
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from src.monitoring.metrics import MetricsCollector

logger = logging.getLogger(__name__)


class SecurityEventType(Enum):
    """Types of security events."""

    MALICIOUS_FILE_DETECTED = "malicious_file_detected"
    SUSPICIOUS_UPLOAD_PATTERN = "suspicious_upload_pattern"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    VALIDATION_FAILURE = "validation_failure"
    AUTHENTICATION_FAILURE = "authentication_failure"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    SYSTEM_SECURITY_ALERT = "system_security_alert"
    PERFORMANCE_DEGRADATION = "performance_degradation"


class AlertSeverity(Enum):
    """Alert severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class ThreatLevel(Enum):
    """Threat classification levels."""

    BENIGN = "benign"
    SUSPICIOUS = "suspicious"
    MALICIOUS = "malicious"
    CRITICAL_THREAT = "critical_threat"


@dataclass
class SecurityEvent:
    """Security event data structure."""

    event_type: SecurityEventType
    severity: AlertSeverity
    threat_level: ThreatLevel
    timestamp: float
    source_ip: Optional[str] = None
    user_session: Optional[str] = None
    file_hash: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    validation_details: Dict[str, Any] = field(default_factory=dict)
    threat_indicators: List[str] = field(default_factory=list)
    mitigation_actions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class SecurityAlert(BaseModel):
    """Security alert model for notifications."""

    alert_id: str
    event_type: str
    severity: str
    threat_level: str
    timestamp: datetime
    message: str
    details: Dict[str, Any]
    source_info: Dict[str, Any] = Field(default_factory=dict)
    recommended_actions: List[str] = Field(default_factory=list)
    auto_mitigation: bool = False


class SecurityMetrics:
    """Security-specific metrics tracking."""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self._threat_counters = defaultdict(int)
        self._validation_times = deque(maxlen=1000)
        self._file_sizes = deque(maxlen=1000)

    def record_security_event(self, event: SecurityEvent) -> None:
        """Record security event metrics."""
        # Event type metrics
        self.metrics.increment(f"security_events_{event.event_type.value}")
        self.metrics.increment(f"security_severity_{event.severity.value}")
        self.metrics.increment(f"threat_level_{event.threat_level.value}")

        # Threat classification metrics
        self._threat_counters[event.threat_level.value] += 1

        # File metrics if available
        if event.file_size:
            self._file_sizes.append(event.file_size)
            self.metrics.histogram("upload_file_size_bytes", event.file_size)

        # Validation performance
        if "validation_time_ms" in event.metadata:
            validation_time = event.metadata["validation_time_ms"]
            self._validation_times.append(validation_time)
            self.metrics.histogram("security_validation_time_ms", validation_time)

    def record_malicious_file_detection(
        self, file_hash: str, threat_indicators: List[str], validation_time: float
    ) -> None:
        """Record malicious file detection event."""
        self.metrics.increment("malicious_files_detected")
        self.metrics.histogram(
            "malicious_file_validation_time_ms", validation_time * 1000
        )

        # Track specific threat types
        for indicator in threat_indicators:
            self.metrics.increment(f"threat_indicator_{indicator}")

    def record_performance_metrics(
        self, concurrent_uploads: int, processing_time: float, memory_usage_mb: float
    ) -> None:
        """Record security pipeline performance metrics."""
        self.metrics.gauge("concurrent_uploads", concurrent_uploads)
        self.metrics.histogram("upload_processing_time_ms", processing_time * 1000)
        self.metrics.gauge("security_pipeline_memory_mb", memory_usage_mb)

    def get_security_summary(self) -> Dict[str, Any]:
        """Get security metrics summary."""
        return {
            "threat_counts": dict(self._threat_counters),
            "total_events": self.metrics.get_counter("security_events_total"),
            "malicious_files": self.metrics.get_counter("malicious_files_detected"),
            "validation_stats": self._get_validation_stats(),
            "performance_stats": self._get_performance_stats(),
        }

    def _get_validation_stats(self) -> Dict[str, float]:
        """Get validation performance statistics."""
        if not self._validation_times:
            return {"count": 0, "avg": 0, "p95": 0, "p99": 0}

        times = sorted(self._validation_times)
        count = len(times)

        return {
            "count": count,
            "avg": sum(times) / count,
            "p95": times[int(0.95 * count)] if count > 0 else 0,
            "p99": times[int(0.99 * count)] if count > 0 else 0,
            "max": max(times),
        }

    def _get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            "avg_file_size_mb": sum(self._file_sizes)
            / len(self._file_sizes)
            / 1024
            / 1024
            if self._file_sizes
            else 0,
            "max_file_size_mb": max(self._file_sizes) / 1024 / 1024
            if self._file_sizes
            else 0,
            "concurrent_uploads": self.metrics.get_gauge("concurrent_uploads"),
            "memory_usage_mb": self.metrics.get_gauge("security_pipeline_memory_mb"),
        }


class SecurityEventLogger:
    """Comprehensive security event logging with structured output."""

    def __init__(self, log_level: str = "INFO", log_file: Optional[str] = None):
        self.log_level = log_level
        self.log_file = log_file
        self._setup_logging()

        # Event storage for analysis
        self._event_history = deque(maxlen=10000)  # Keep last 10k events
        self._threat_patterns = defaultdict(list)

    def _setup_logging(self) -> None:
        """Setup structured security logging."""
        # Create security-specific logger
        self.security_logger = logging.getLogger("security_events")
        self.security_logger.setLevel(getattr(logging, self.log_level.upper()))

        # Remove existing handlers
        for handler in self.security_logger.handlers[:]:
            self.security_logger.removeHandler(handler)

        # Create formatter for structured logging
        formatter = logging.Formatter(
            "%(asctime)s - SECURITY - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S UTC",
        )

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.security_logger.addHandler(console_handler)

        # File handler if specified
        if self.log_file:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setFormatter(formatter)
            self.security_logger.addHandler(file_handler)

    def log_security_event(self, event: SecurityEvent) -> None:
        """Log security event with structured data."""
        # Store event for analysis
        self._event_history.append(event)

        # Track threat patterns
        if event.threat_level != ThreatLevel.BENIGN:
            self._threat_patterns[event.source_ip].append(
                {
                    "timestamp": event.timestamp,
                    "threat_level": event.threat_level.value,
                    "event_type": event.event_type.value,
                }
            )

        # Create structured log entry
        log_data = {
            "event_type": event.event_type.value,
            "severity": event.severity.value,
            "threat_level": event.threat_level.value,
            "timestamp": datetime.fromtimestamp(event.timestamp).isoformat(),
            "source_ip": event.source_ip,
            "user_session": event.user_session,
            "file_info": {
                "hash": event.file_hash,
                "name": self._sanitize_filename(event.file_name),
                "size": event.file_size,
            },
            "validation_details": event.validation_details,
            "threat_indicators": event.threat_indicators,
            "mitigation_actions": event.mitigation_actions,
            "metadata": event.metadata,
        }

        # Log at appropriate level
        log_message = f"Security Event: {json.dumps(log_data, default=str)}"

        if event.severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]:
            self.security_logger.critical(log_message)
        elif event.severity == AlertSeverity.HIGH:
            self.security_logger.error(log_message)
        elif event.severity == AlertSeverity.MEDIUM:
            self.security_logger.warning(log_message)
        else:
            self.security_logger.info(log_message)

    def analyze_threat_patterns(self, time_window_minutes: int = 60) -> Dict[str, Any]:
        """Analyze threat patterns for suspicious activity."""
        cutoff_time = time.time() - (time_window_minutes * 60)

        pattern_analysis = {
            "suspicious_ips": [],
            "threat_escalation": [],
            "repeated_threats": [],
            "analysis_timestamp": datetime.utcnow().isoformat(),
        }

        # Analyze IP-based patterns
        for ip, events in self._threat_patterns.items():
            recent_events = [e for e in events if e["timestamp"] > cutoff_time]

            if len(recent_events) >= 3:  # Multiple threats from same IP
                pattern_analysis["suspicious_ips"].append(
                    {
                        "ip": ip,
                        "event_count": len(recent_events),
                        "threat_levels": [e["threat_level"] for e in recent_events],
                        "event_types": list(
                            set(e["event_type"] for e in recent_events)
                        ),
                    }
                )

        # Analyze recent events for escalation patterns
        recent_events = [e for e in self._event_history if e.timestamp > cutoff_time]

        # Group by source to detect escalation
        source_events = defaultdict(list)
        for event in recent_events:
            if event.source_ip:
                source_events[event.source_ip].append(event)

        for source, events in source_events.items():
            if len(events) >= 2:
                # Check for threat level escalation
                threat_levels = [
                    e.threat_level for e in sorted(events, key=lambda x: x.timestamp)
                ]
                if self._is_escalating_threat(threat_levels):
                    pattern_analysis["threat_escalation"].append(
                        {
                            "source": source,
                            "events": len(events),
                            "escalation_pattern": [t.value for t in threat_levels],
                        }
                    )

        return pattern_analysis

    def _sanitize_filename(self, filename: Optional[str]) -> Optional[str]:
        """Sanitize filename for safe logging."""
        if not filename:
            return None

        # Remove path information and keep only basename
        safe_name = os.path.basename(filename)

        # Limit length and remove potentially dangerous characters
        safe_name = "".join(c for c in safe_name if c.isalnum() or c in ".-_")[:50]

        return safe_name if safe_name else "unknown_file"

    def _is_escalating_threat(self, threat_levels: List[ThreatLevel]) -> bool:
        """Check if threat levels show escalation pattern."""
        threat_values = {
            "benign": 0,
            "suspicious": 1,
            "malicious": 2,
            "critical_threat": 3,
        }

        for i in range(1, len(threat_levels)):
            current = threat_values.get(threat_levels[i].value, 0)
            previous = threat_values.get(threat_levels[i - 1].value, 0)

            if current > previous:
                return True

        return False


class SecurityAlertManager:
    """Manages security alerts and notifications."""

    def __init__(
        self, webhook_url: Optional[str] = None, email_config: Optional[Dict] = None
    ):
        self.webhook_url = webhook_url
        self.email_config = email_config
        self._alert_history = deque(maxlen=1000)
        self._rate_limits = defaultdict(lambda: {"count": 0, "reset_time": 0})

        # Alert thresholds to prevent spam
        self.rate_limit_window = 300  # 5 minutes
        self.max_alerts_per_window = 10

    def create_alert(
        self, event: SecurityEvent, auto_mitigation: bool = False
    ) -> SecurityAlert:
        """Create security alert from event."""
        alert_id = self._generate_alert_id(event)

        # Determine message and recommended actions
        message, actions = self._get_alert_content(event)

        alert = SecurityAlert(
            alert_id=alert_id,
            event_type=event.event_type.value,
            severity=event.severity.value,
            threat_level=event.threat_level.value,
            timestamp=datetime.fromtimestamp(event.timestamp),
            message=message,
            details={
                "file_info": {
                    "hash": event.file_hash,
                    "name": event.file_name,
                    "size": event.file_size,
                },
                "validation_details": event.validation_details,
                "threat_indicators": event.threat_indicators,
                "metadata": event.metadata,
            },
            source_info={
                "ip": event.source_ip,
                "session": event.user_session,
                "timestamp": event.timestamp,
            },
            recommended_actions=actions,
            auto_mitigation=auto_mitigation,
        )

        self._alert_history.append(alert)
        return alert

    def send_alert(self, alert: SecurityAlert) -> bool:
        """Send alert through configured channels."""
        # Check rate limiting
        if not self._should_send_alert(alert):
            logger.debug(f"Alert rate limited: {alert.alert_id}")
            return False

        sent_successfully = False

        # Send webhook notification
        if self.webhook_url:
            try:
                self._send_webhook_alert(alert)
                sent_successfully = True
            except Exception as e:
                logger.error(f"Failed to send webhook alert: {e}")

        # Send email notification
        if self.email_config:
            try:
                self._send_email_alert(alert)
                sent_successfully = True
            except Exception as e:
                logger.error(f"Failed to send email alert: {e}")

        # Update rate limiting
        if sent_successfully:
            self._update_rate_limit(alert)

        return sent_successfully

    def _generate_alert_id(self, event: SecurityEvent) -> str:
        """Generate unique alert ID."""
        content = f"{event.event_type.value}_{event.timestamp}_{event.source_ip}_{event.file_hash}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

    def _get_alert_content(self, event: SecurityEvent) -> tuple[str, List[str]]:
        """Get alert message and recommended actions."""
        messages = {
            SecurityEventType.MALICIOUS_FILE_DETECTED: (
                f"Malicious file detected: {len(event.threat_indicators)} threat indicators found",
                [
                    "Block source IP temporarily",
                    "Review file content manually",
                    "Update threat detection patterns",
                    "Monitor for similar uploads",
                ],
            ),
            SecurityEventType.SUSPICIOUS_UPLOAD_PATTERN: (
                f"Suspicious upload pattern detected from {event.source_ip}",
                [
                    "Monitor source IP closely",
                    "Review recent uploads from this source",
                    "Consider rate limiting",
                    "Check for coordinated attacks",
                ],
            ),
            SecurityEventType.RATE_LIMIT_EXCEEDED: (
                f"Rate limit exceeded: {event.source_ip} exceeded upload limits",
                [
                    "Enforce stricter rate limiting",
                    "Monitor for abuse patterns",
                    "Consider temporary IP blocking",
                    "Review rate limit configuration",
                ],
            ),
            SecurityEventType.VALIDATION_FAILURE: (
                f"Security validation failed: {event.validation_details.get('reason', 'Unknown')}",
                [
                    "Review validation logic",
                    "Check for false positives",
                    "Update validation rules if needed",
                    "Monitor for bypass attempts",
                ],
            ),
        }

        return messages.get(
            event.event_type,
            (
                f"Security event: {event.event_type.value}",
                ["Review event details", "Take appropriate action"],
            ),
        )

    def _should_send_alert(self, alert: SecurityAlert) -> bool:
        """Check if alert should be sent based on rate limiting."""
        alert_key = f"{alert.event_type}_{alert.severity}"
        current_time = time.time()

        rate_limit = self._rate_limits[alert_key]

        # Reset rate limit window if expired
        if current_time > rate_limit["reset_time"]:
            rate_limit["count"] = 0
            rate_limit["reset_time"] = current_time + self.rate_limit_window

        # Check if under rate limit
        if rate_limit["count"] >= self.max_alerts_per_window:
            return False

        # Always send critical and emergency alerts
        if alert.severity in ["critical", "emergency"]:
            return True

        return True

    def _update_rate_limit(self, alert: SecurityAlert) -> None:
        """Update rate limiting counters."""
        alert_key = f"{alert.event_type}_{alert.severity}"
        self._rate_limits[alert_key]["count"] += 1

    def _send_webhook_alert(self, alert: SecurityAlert) -> None:
        """Send alert via webhook."""
        import requests

        payload = {
            "alert_id": alert.alert_id,
            "event_type": alert.event_type,
            "severity": alert.severity,
            "threat_level": alert.threat_level,
            "timestamp": alert.timestamp.isoformat(),
            "message": alert.message,
            "recommended_actions": alert.recommended_actions,
            "details": alert.details,
        }

        response = requests.post(
            self.webhook_url,
            json=payload,
            timeout=10,
            headers={"Content-Type": "application/json"},
        )
        response.raise_for_status()

    def _send_email_alert(self, alert: SecurityAlert) -> None:
        """Send alert via email."""
        # Email implementation would go here
        # This is a placeholder for email notification
        logger.info(f"Email alert would be sent: {alert.alert_id}")


class SecurityMonitor:
    """
    Main security monitoring coordinator for C1-Image Upload Security Pipeline.

    Integrates event logging, metrics collection, alerting, and real-time monitoring
    for comprehensive security observability.
    """

    def __init__(
        self,
        metrics_collector: MetricsCollector,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        webhook_url: Optional[str] = None,
        email_config: Optional[Dict] = None,
    ):
        self.metrics = SecurityMetrics(metrics_collector)
        self.event_logger = SecurityEventLogger(log_level, log_file)
        self.alert_manager = SecurityAlertManager(webhook_url, email_config)

        # Real-time monitoring
        self._monitoring_active = False
        self._monitoring_thread: Optional[threading.Thread] = None
        self._performance_baseline = {
            "avg_validation_time": 2000,  # 2 seconds
            "max_concurrent_uploads": 15,
            "memory_limit_mb": 512,
        }

        logger.info("SecurityMonitor initialized")

    def record_security_event(
        self,
        event_type: SecurityEventType,
        severity: AlertSeverity,
        threat_level: ThreatLevel,
        source_ip: Optional[str] = None,
        user_session: Optional[str] = None,
        file_hash: Optional[str] = None,
        file_name: Optional[str] = None,
        file_size: Optional[int] = None,
        validation_details: Optional[Dict[str, Any]] = None,
        threat_indicators: Optional[List[str]] = None,
        mitigation_actions: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> SecurityEvent:
        """Record comprehensive security event."""

        event = SecurityEvent(
            event_type=event_type,
            severity=severity,
            threat_level=threat_level,
            timestamp=time.time(),
            source_ip=source_ip,
            user_session=user_session,
            file_hash=file_hash,
            file_name=file_name,
            file_size=file_size,
            validation_details=validation_details or {},
            threat_indicators=threat_indicators or [],
            mitigation_actions=mitigation_actions or [],
            metadata=metadata or {},
        )

        # Record metrics
        self.metrics.record_security_event(event)

        # Log event
        self.event_logger.log_security_event(event)

        # Create and send alert if appropriate
        if severity in [
            AlertSeverity.HIGH,
            AlertSeverity.CRITICAL,
            AlertSeverity.EMERGENCY,
        ]:
            alert = self.alert_manager.create_alert(event)
            self.alert_manager.send_alert(alert)

        return event

    def record_malicious_file_detection(
        self,
        file_hash: str,
        file_name: str,
        file_size: int,
        threat_indicators: List[str],
        validation_time: float,
        source_ip: Optional[str] = None,
        user_session: Optional[str] = None,
        validation_details: Optional[Dict[str, Any]] = None,
    ) -> SecurityEvent:
        """Record malicious file detection with comprehensive details."""

        # Determine severity based on threat indicators
        severity = AlertSeverity.HIGH
        threat_level = ThreatLevel.MALICIOUS

        # Critical threats (multiple indicators or specific high-risk patterns)
        critical_indicators = [
            "script_injection",
            "executable_disguise",
            "polyglot_file",
        ]
        if any(indicator in threat_indicators for indicator in critical_indicators):
            severity = AlertSeverity.CRITICAL
            threat_level = ThreatLevel.CRITICAL_THREAT

        # Record specific malicious file metrics
        self.metrics.record_malicious_file_detection(
            file_hash, threat_indicators, validation_time
        )

        # Create comprehensive security event
        return self.record_security_event(
            event_type=SecurityEventType.MALICIOUS_FILE_DETECTED,
            severity=severity,
            threat_level=threat_level,
            source_ip=source_ip,
            user_session=user_session,
            file_hash=file_hash,
            file_name=file_name,
            file_size=file_size,
            validation_details=validation_details,
            threat_indicators=threat_indicators,
            mitigation_actions=[
                "File upload blocked",
                "Source IP flagged for monitoring",
                "Threat signature updated",
            ],
            metadata={
                "validation_time_ms": validation_time * 1000,
                "detection_timestamp": time.time(),
                "threat_score": len(threat_indicators) * 10,
            },
        )

    def record_performance_event(
        self,
        concurrent_uploads: int,
        processing_time: float,
        memory_usage_mb: float,
        validation_queue_depth: int = 0,
    ) -> None:
        """Record performance metrics and detect degradation."""

        # Record metrics
        self.metrics.record_performance_metrics(
            concurrent_uploads, processing_time, memory_usage_mb
        )

        # Check for performance degradation
        if (
            processing_time
            > self._performance_baseline["avg_validation_time"] / 1000 * 2
        ):  # 2x baseline
            self.record_security_event(
                event_type=SecurityEventType.PERFORMANCE_DEGRADATION,
                severity=AlertSeverity.MEDIUM,
                threat_level=ThreatLevel.SUSPICIOUS,
                validation_details={
                    "processing_time": processing_time,
                    "baseline": self._performance_baseline["avg_validation_time"]
                    / 1000,
                    "degradation_factor": processing_time
                    / (self._performance_baseline["avg_validation_time"] / 1000),
                },
                metadata={
                    "concurrent_uploads": concurrent_uploads,
                    "memory_usage_mb": memory_usage_mb,
                    "queue_depth": validation_queue_depth,
                },
            )

        # Check for resource exhaustion
        if (
            memory_usage_mb > self._performance_baseline["memory_limit_mb"] * 0.9
        ):  # 90% of limit
            self.record_security_event(
                event_type=SecurityEventType.SYSTEM_SECURITY_ALERT,
                severity=AlertSeverity.HIGH,
                threat_level=ThreatLevel.SUSPICIOUS,
                validation_details={
                    "memory_usage_mb": memory_usage_mb,
                    "memory_limit_mb": self._performance_baseline["memory_limit_mb"],
                    "usage_percent": (
                        memory_usage_mb / self._performance_baseline["memory_limit_mb"]
                    )
                    * 100,
                },
                mitigation_actions=[
                    "Monitor for memory exhaustion attacks",
                    "Consider reducing concurrent upload limit",
                    "Review file size limits",
                ],
            )

    def get_security_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive security monitoring dashboard data."""
        threat_patterns = self.event_logger.analyze_threat_patterns()
        security_summary = self.metrics.get_security_summary()

        return {
            "timestamp": datetime.utcnow().isoformat(),
            "security_summary": security_summary,
            "threat_analysis": threat_patterns,
            "performance_status": {
                "validation_stats": security_summary.get("validation_stats", {}),
                "performance_stats": security_summary.get("performance_stats", {}),
                "baseline_comparison": self._compare_to_baseline(),
            },
            "alert_summary": {
                "recent_alerts": len(self.alert_manager._alert_history),
                "rate_limit_status": dict(self.alert_manager._rate_limits),
            },
            "monitoring_status": {
                "active": self._monitoring_active,
                "uptime": self._get_monitor_uptime(),
            },
        }

    def start_real_time_monitoring(self, interval_seconds: int = 60) -> None:
        """Start real-time security monitoring."""
        if self._monitoring_active:
            logger.warning("Real-time monitoring already active")
            return

        self._monitoring_active = True
        self._monitoring_thread = threading.Thread(
            target=self._monitoring_loop, args=(interval_seconds,), daemon=True
        )
        self._monitoring_thread.start()

        logger.info(
            f"Real-time security monitoring started (interval: {interval_seconds}s)"
        )

    def stop_real_time_monitoring(self) -> None:
        """Stop real-time security monitoring."""
        if not self._monitoring_active:
            return

        self._monitoring_active = False
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=10)

        logger.info("Real-time security monitoring stopped")

    def _monitoring_loop(self, interval_seconds: int) -> None:
        """Real-time monitoring loop."""
        while self._monitoring_active:
            try:
                # Analyze threat patterns
                threat_patterns = self.event_logger.analyze_threat_patterns(
                    time_window_minutes=15
                )

                # Check for suspicious activity
                if threat_patterns["suspicious_ips"]:
                    for suspicious_ip in threat_patterns["suspicious_ips"]:
                        if suspicious_ip["event_count"] >= 5:  # High activity threshold
                            self.record_security_event(
                                event_type=SecurityEventType.SUSPICIOUS_UPLOAD_PATTERN,
                                severity=AlertSeverity.MEDIUM,
                                threat_level=ThreatLevel.SUSPICIOUS,
                                source_ip=suspicious_ip["ip"],
                                validation_details={
                                    "event_count": suspicious_ip["event_count"],
                                    "threat_levels": suspicious_ip["threat_levels"],
                                    "event_types": suspicious_ip["event_types"],
                                },
                                mitigation_actions=[
                                    "Increase monitoring of source IP",
                                    "Consider rate limiting",
                                    "Review upload patterns",
                                ],
                            )

                # Check for threat escalation
                if threat_patterns["threat_escalation"]:
                    for escalation in threat_patterns["threat_escalation"]:
                        self.record_security_event(
                            event_type=SecurityEventType.SYSTEM_SECURITY_ALERT,
                            severity=AlertSeverity.HIGH,
                            threat_level=ThreatLevel.MALICIOUS,
                            source_ip=escalation["source"],
                            validation_details={
                                "escalation_pattern": escalation["escalation_pattern"],
                                "event_count": escalation["events"],
                            },
                            mitigation_actions=[
                                "Block source IP immediately",
                                "Escalate to security team",
                                "Review all uploads from source",
                            ],
                        )

                time.sleep(interval_seconds)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(min(interval_seconds, 60))  # Don't sleep too long on error

    def _compare_to_baseline(self) -> Dict[str, Any]:
        """Compare current performance to baseline."""
        validation_stats = self.metrics._get_validation_stats()
        performance_stats = self.metrics._get_performance_stats()

        comparison = {
            "validation_time_vs_baseline": "normal",
            "memory_usage_vs_baseline": "normal",
            "concurrent_uploads_vs_baseline": "normal",
        }

        # Compare validation time
        if (
            validation_stats["avg"]
            > self._performance_baseline["avg_validation_time"] * 1.5
        ):
            comparison["validation_time_vs_baseline"] = "degraded"
        elif (
            validation_stats["avg"]
            > self._performance_baseline["avg_validation_time"] * 2
        ):
            comparison["validation_time_vs_baseline"] = "severely_degraded"

        # Compare memory usage
        current_memory = performance_stats["memory_usage_mb"]
        if current_memory > self._performance_baseline["memory_limit_mb"] * 0.8:
            comparison["memory_usage_vs_baseline"] = "high"
        elif current_memory > self._performance_baseline["memory_limit_mb"] * 0.9:
            comparison["memory_usage_vs_baseline"] = "critical"

        return comparison

    def _get_monitor_uptime(self) -> float:
        """Get monitoring uptime in seconds."""
        # This would track actual uptime in a real implementation
        return 0.0 if not self._monitoring_active else time.time()


# Factory function for creating security monitor
def create_security_monitor(
    metrics_collector: MetricsCollector,
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    webhook_url: Optional[str] = None,
    email_config: Optional[Dict] = None,
) -> SecurityMonitor:
    """
    Factory function to create SecurityMonitor with configuration.

    Args:
        metrics_collector: MetricsCollector instance for metrics tracking
        log_level: Logging level for security events
        log_file: Optional file path for security event logging
        webhook_url: Optional webhook URL for alert notifications
        email_config: Optional email configuration for notifications

    Returns:
        Configured SecurityMonitor instance
    """

    # Get configuration from environment if not provided
    if webhook_url is None:
        webhook_url = os.getenv("SECURITY_WEBHOOK_URL")

    if email_config is None and os.getenv("SECURITY_EMAIL_ENABLED"):
        email_config = {
            "smtp_server": os.getenv("SMTP_SERVER"),
            "smtp_port": int(os.getenv("SMTP_PORT", "587")),
            "username": os.getenv("SMTP_USERNAME"),
            "password": os.getenv("SMTP_PASSWORD"),
            "recipients": os.getenv("SECURITY_EMAIL_RECIPIENTS", "").split(","),
        }

    return SecurityMonitor(
        metrics_collector=metrics_collector,
        log_level=log_level,
        log_file=log_file,
        webhook_url=webhook_url,
        email_config=email_config,
    )
