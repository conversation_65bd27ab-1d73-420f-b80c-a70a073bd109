# Security Operational Monitoring Configuration for C1-Image Upload Security Pipeline
# Comprehensive threat detection, alerting, and real-time security observability

monitoring:
  # Global monitoring settings
  enabled: true
  log_level: "INFO"
  monitoring_interval: 30  # seconds
  alert_cooldown: 300     # seconds
  
  # Threat detection configuration
  threat_detection:
    enabled: true
    real_time_scanning: true
    behavioral_analysis: true
    threat_intelligence_integration: true
    
    # Malicious file detection
    malicious_file_detection:
      enabled: true
      scan_timeout: 30
      threat_threshold: 0.7
      quarantine_enabled: true
      
      # Detection engines
      engines:
        - name: "magic_number_validator"
          enabled: true
          weight: 0.3
        - name: "pil_validator"
          enabled: true
          weight: 0.3
        - name: "content_analyzer"
          enabled: true
          weight: 0.4
      
      # Threat indicators
      threat_indicators:
        - name: "script_injection"
          severity: "critical"
          auto_block: true
        - name: "executable_disguise"
          severity: "critical"
          auto_block: true
        - name: "polyglot_file"
          severity: "high"
          auto_block: true
        - name: "suspicious_metadata"
          severity: "medium"
          auto_block: false
        - name: "unusual_file_structure"
          severity: "low"
          auto_block: false
    
    # Upload pattern analysis
    upload_pattern_analysis:
      enabled: true
      time_window: 300  # seconds
      max_uploads_per_ip: 10
      max_failed_uploads: 5
      suspicious_user_agents:
        - "curl"
        - "wget"
        - "python-requests"
        - "bot"
      
      # Pattern detection rules
      patterns:
        - name: "rapid_uploads"
          condition: "uploads_per_minute > 20"
          severity: "high"
          action: "rate_limit"
        - name: "repeated_failures"
          condition: "failed_uploads > 10 in 5 minutes"
          severity: "medium"
          action: "temporary_block"
        - name: "size_anomaly"
          condition: "average_file_size > 50MB"
          severity: "medium"
          action: "enhanced_scanning"
    
    # Performance anomaly detection
    performance_monitoring:
      enabled: true
      baseline_window: 3600  # seconds
      
      thresholds:
        response_time:
          warning: 2000    # milliseconds
          critical: 5000
        memory_usage:
          warning: 80      # percentage
          critical: 95
        cpu_usage:
          warning: 70      # percentage
          critical: 90
        queue_depth:
          warning: 100     # jobs
          critical: 500
      
      # Performance degradation alerts
      degradation_detection:
        enabled: true
        degradation_threshold: 0.5  # 50% performance drop
        sustained_duration: 300     # seconds

# Alerting configuration
alerting:
  enabled: true
  
  # Alert channels
  channels:
    webhook:
      enabled: true
      url: "${SECURITY_WEBHOOK_URL}"
      timeout: 10
      retry_attempts: 3
      retry_delay: 5
    
    email:
      enabled: false
      smtp_server: "${SMTP_SERVER}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    slack:
      enabled: false
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#security-alerts"
      username: "SecurityBot"
    
    pagerduty:
      enabled: false
      integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
      severity_mapping:
        critical: "critical"
        high: "error"
        medium: "warning"
        low: "info"
  
  # Alert rules
  rules:
    - name: "Critical Threat Detected"
      condition: "threat_level == 'critical'"
      severity: "critical"
      channels: ["webhook", "email", "pagerduty"]
      throttle: false
      
    - name: "High Threat Activity"
      condition: "threat_level == 'high' and count > 5 in 10 minutes"
      severity: "high"
      channels: ["webhook", "slack"]
      throttle: true
      throttle_duration: 300
      
    - name: "Suspicious Upload Pattern"
      condition: "pattern_detected == 'rapid_uploads' or pattern_detected == 'repeated_failures'"
      severity: "medium"
      channels: ["webhook"]
      throttle: true
      throttle_duration: 600
      
    - name: "Performance Degradation"
      condition: "performance_degradation == true"
      severity: "medium"
      channels: ["webhook", "slack"]
      throttle: true
      throttle_duration: 900
      
    - name: "System Security Alert"
      condition: "security_event_type == 'system_security_alert'"
      severity: "high"
      channels: ["webhook", "email"]
      throttle: false

# Security event logging
logging:
  enabled: true
  
  # Log destinations
  destinations:
    file:
      enabled: true
      file_path: "/app/logs/security/security-events.log"
      rotation:
        max_size: "10MB"
        max_files: 10
        compress: true
      format: "json"
      
    syslog:
      enabled: false
      server: "localhost"
      port: 514
      facility: "LOG_LOCAL0"
      format: "rfc3164"
      
    elasticsearch:
      enabled: false
      hosts:
        - "elasticsearch:9200"
      index_prefix: "sora-security"
      doc_type: "security_event"
      
    splunk:
      enabled: false
      host: "splunk-server"
      port: 8088
      token: "${SPLUNK_HEC_TOKEN}"
      index: "sora_security"
  
  # Log levels
  log_levels:
    threat_detection: "INFO"
    performance_monitoring: "WARNING"
    pattern_analysis: "INFO"
    system_events: "WARNING"
  
  # Structured logging fields
  fields:
    - timestamp
    - event_type
    - severity
    - source_ip
    - user_session
    - file_hash
    - file_name
    - file_size
    - threat_indicators
    - validation_results
    - processing_time
    - metadata

# Metrics collection
metrics:
  enabled: true
  collection_interval: 15  # seconds
  
  # Metrics endpoints
  endpoints:
    prometheus:
      enabled: true
      port: 9090
      path: "/metrics"
      
    statsd:
      enabled: false
      host: "statsd-server"
      port: 8125
      prefix: "sora.security"
  
  # Custom metrics
  custom_metrics:
    - name: "threat_detection_rate"
      type: "counter"
      description: "Number of threats detected per minute"
      labels: ["threat_type", "severity"]
      
    - name: "file_processing_duration"
      type: "histogram"
      description: "Time taken to process uploaded files"
      labels: ["file_type", "processing_stage"]
      buckets: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
      
    - name: "security_validation_success_rate"
      type: "gauge"
      description: "Percentage of files passing security validation"
      labels: ["validation_type"]
      
    - name: "active_threat_count"
      type: "gauge"
      description: "Number of active threats being monitored"
      labels: ["threat_level"]
      
    - name: "security_queue_depth"
      type: "gauge"
      description: "Number of files waiting for security processing"
      labels: ["queue_type"]

# Health monitoring
health:
  enabled: true
  check_interval: 60  # seconds
  
  # Health checks
  checks:
    - name: "threat_detection_engine"
      type: "component"
      timeout: 30
      critical: true
      
    - name: "file_validator"
      type: "component"
      timeout: 10
      critical: true
      
    - name: "database_connection"
      type: "external"
      timeout: 5
      critical: true
      endpoint: "postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production"
      
    - name: "redis_connection"
      type: "external"
      timeout: 5
      critical: false
      endpoint: "redis://redis:6379/0"
      
    - name: "disk_space"
      type: "system"
      timeout: 5
      critical: true
      threshold: 90  # percentage
      path: "/app"
      
    - name: "memory_usage"
      type: "system"
      timeout: 5
      critical: false
      threshold: 85  # percentage

# Incident response automation
incident_response:
  enabled: true
  
  # Automated response actions
  actions:
    ip_blocking:
      enabled: true
      duration: 3600  # seconds
      whitelist:
        - "127.0.0.1"
        - "::1"
      
    rate_limiting:
      enabled: true
      strict_mode: false
      limits:
        per_ip: 10
        per_session: 20
        global: 1000
      
    quarantine:
      enabled: true
      quarantine_path: "/app/quarantine"
      retention_period: 604800  # 7 days
      
    notification:
      enabled: true
      escalation_levels:
        - level: 1
          threshold: "high"
          delay: 0
          channels: ["webhook"]
        - level: 2
          threshold: "critical"
          delay: 300
          channels: ["webhook", "email"]
        - level: 3
          threshold: "critical"
          delay: 900
          channels: ["webhook", "email", "pagerduty"]
  
  # Playbooks
  playbooks:
    malicious_file_detected:
      steps:
        - action: "quarantine_file"
          timeout: 30
        - action: "block_source_ip"
          timeout: 10
        - action: "send_alert"
          timeout: 5
        - action: "update_threat_intelligence"
          timeout: 60
    
    suspicious_pattern_detected:
      steps:
        - action: "apply_rate_limiting"
          timeout: 10
        - action: "enhance_monitoring"
          timeout: 5
        - action: "send_alert"
          timeout: 5
    
    performance_degradation:
      steps:
        - action: "collect_diagnostics"
          timeout: 30
        - action: "send_alert"
          timeout: 5
        - action: "scale_resources"
          timeout: 120

# Dashboard configuration
dashboard:
  enabled: true
  
  # Dashboard panels
  panels:
    - name: "Threat Detection Overview"
      type: "summary"
      metrics:
        - "total_threats_detected"
        - "threats_by_severity"
        - "threat_detection_rate"
      
    - name: "File Processing Metrics"
      type: "time_series"
      metrics:
        - "files_processed_per_minute"
        - "processing_duration_p95"
        - "validation_success_rate"
      
    - name: "Security Alerts"
      type: "alert_list"
      filters:
        - "severity >= medium"
        - "timestamp >= now-1h"
      
    - name: "System Health"
      type: "status"
      components:
        - "threat_detection_engine"
        - "file_validator"
        - "database_connection"
        - "redis_connection"
      
    - name: "Performance Monitoring"
      type: "gauge"
      metrics:
        - "cpu_usage"
        - "memory_usage"
        - "disk_usage"
        - "queue_depth"
  
  # Auto-refresh settings
  refresh:
    enabled: true
    interval: 30  # seconds
    
  # Export settings
  export:
    enabled: true
    formats: ["json", "csv", "pdf"]
    retention: 30  # days

# Environment-specific overrides
environments:
  development:
    monitoring:
      monitoring_interval: 60
    alerting:
      channels:
        webhook:
          enabled: false
        email:
          enabled: false
    logging:
      log_levels:
        threat_detection: "DEBUG"
        performance_monitoring: "INFO"
  
  testing:
    monitoring:
      monitoring_interval: 30
    alerting:
      rules:
        - name: "Test Alert Suppression"
          condition: "environment == 'testing'"
          severity: "info"
          channels: []
  
  staging:
    monitoring:
      monitoring_interval: 15
    alerting:
      channels:
        email:
          enabled: true
    incident_response:
      actions:
        ip_blocking:
          duration: 1800
  
  production:
    monitoring:
      monitoring_interval: 10
    alerting:
      channels:
        webhook:
          enabled: true
        email:
          enabled: true
        pagerduty:
          enabled: true
    incident_response:
      actions:
        ip_blocking:
          enabled: true
        notification:
          enabled: true