# Enhanced Docker Security Configuration for C1-Image Upload Security Pipeline
# Optimized security hardening with minimal attack surface

version: '3.8'

# Security-hardened configuration template
x-security-base: &security-base
  # User Security
  user: "sora:sora"
  
  # Capabilities - Drop all, add only essential
  cap_drop:
    - ALL
  cap_add:
    - <PERSON><PERSON><PERSON>        # Required for file ownership changes
    - <PERSON><PERSON><PERSON>       # Required for user switching
    - SETGID       # Required for group switching
    - DAC_OVERRIDE # Required for file permission override
  
  # Security Options
  security_opt:
    - no-new-privileges:true
    - seccomp:unconfined  # Would use custom profile in production
    - apparmor:docker-default
  
  # Read-only root filesystem with writable volumes
  read_only: true
  tmpfs:
    - /tmp:noexec,nosuid,size=100m
    - /var/tmp:noexec,nosuid,size=50m
    - /run:noexec,nosuid,size=50m
  
  # Resource Limits
  deploy:
    resources:
      limits:
        memory: 512M
        cpus: '1.0'
        pids: 100
      reservations:
        memory: 256M
        cpus: '0.5'
  
  # Logging Configuration
  logging:
    driver: json-file
    options:
      max-size: "10m"
      max-file: "3"
      labels: "service,environment,security"
  
  # Health Checks
  healthcheck:
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s
  
  # Network Security
  networks:
    - sora-security-network

# Production Security-Enhanced Services
services:
  # Application Service with Enhanced Security
  sora-app-secure:
    <<: *security-base
    build:
      context: ../../..
      dockerfile: src/deployment/docker/Dockerfile.hardened
      args:
        SECURITY_LEVEL: "strict"
    environment:
      # Security Environment Variables
      SECURITY_HEADERS_ENABLED: "true"
      RATE_LIMITING_ENABLED: "true"
      INPUT_VALIDATION_STRICT: "true"
      AUDIT_LOGGING_ENABLED: "true"
      DEBUG: "false"
      FLASK_ENV: "production"
      
      # Image Security Pipeline Configuration
      IMAGE_SECURITY_ENABLED: "true"
      MALWARE_SCANNING_ENABLED: "true"
      CONTENT_FILTERING_ENABLED: "true"
      THREAT_DETECTION_ENABLED: "true"
    
    volumes:
      # Read-only application code
      - ./src:/app/src:ro
      - ./static:/app/static:ro
      
      # Writable volumes with security constraints
      - sora-uploads:/app/uploads:rw,noexec,nosuid
      - sora-logs:/app/logs:rw,noexec,nosuid
      - sora-temp:/app/temp:rw,noexec,nosuid,nodev
    
    ports:
      - "5001:5001"
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health/security"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Database Service with Security Hardening
  postgres-secure:
    <<: *security-base
    image: postgres:15.4-alpine
    environment:
      POSTGRES_DB: sora_production
      POSTGRES_USER: sora_user
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C --auth-host=scram-sha-256"
    
    secrets:
      - db_password
    
    volumes:
      - postgres-data:/var/lib/postgresql/data:rw,noexec,nosuid
      - ./sql/security-init.sql:/docker-entrypoint-initdb.d/01-security.sql:ro
    
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c log_min_duration_statement=1000
      -c ssl=on
      -c ssl_cert_file=/etc/ssl/certs/server.crt
      -c ssl_key_file=/etc/ssl/private/server.key
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sora_user -d sora_production"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Service with Security Configuration
  redis-secure:
    <<: *security-base
    image: redis:7.2-alpine
    command: >
      redis-server
      --requirepass-file /run/secrets/redis_password
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
      --no-appendfsync-on-rewrite yes
      --tcp-keepalive 300
      --timeout 0
      --tcp-backlog 511
      --bind 0.0.0.0
      --protected-mode yes
      --port 6379
    
    secrets:
      - redis_password
    
    volumes:
      - redis-data:/data:rw,noexec,nosuid
    
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "$(cat /run/secrets/redis_password)", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Security Monitoring Service
  security-monitor:
    <<: *security-base
    build:
      context: ../../..
      dockerfile: src/deployment/docker/Dockerfile.monitoring
    environment:
      SECURITY_MONITORING_ENABLED: "true"
      THREAT_DETECTION_LEVEL: "strict"
      ALERT_WEBHOOK_URL: "${SECURITY_WEBHOOK_URL}"
      LOG_LEVEL: "INFO"
    
    volumes:
      - sora-logs:/app/logs:ro
      - security-events:/app/security-events:rw,noexec,nosuid
    
    depends_on:
      - sora-app-secure
      - postgres-secure
      - redis-secure
    
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 60s
      timeout: 30s
      retries: 3

# Security-focused Network Configuration
networks:
  sora-security-network:
    driver: bridge
    enable_ipv6: false
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: sora-secure-br
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: "1500"

# Secure Volume Configuration
volumes:
  postgres-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sora/data/postgres
  
  redis-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sora/data/redis
  
  sora-uploads:
    driver: local
    driver_opts:
      type: none
      o: bind,noexec,nosuid
      device: /opt/sora/uploads
  
  sora-logs:
    driver: local
    driver_opts:
      type: none
      o: bind,noexec,nosuid
      device: /opt/sora/logs
  
  sora-temp:
    driver: local
    driver_opts:
      type: tmpfs
      tmpfs-size: 100m
      tmpfs-mode: 1777
  
  security-events:
    driver: local
    driver_opts:
      type: none
      o: bind,noexec,nosuid
      device: /opt/sora/security/events

# Docker Secrets for Secure Credential Management
secrets:
  db_password:
    external: true
    name: sora_db_password
  
  redis_password:
    external: true
    name: sora_redis_password
  
  app_secret_key:
    external: true
    name: sora_app_secret