"""
Security Configuration Manager for C1-Image Upload Security Pipeline Deployment.

Provides comprehensive security configuration management with environment-specific settings,
deployment automation, and security validation for the C1-Image Upload Security Pipeline.
"""

import hashlib
import json
import logging
import os
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security deployment levels."""

    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class ValidationMode(Enum):
    """Image validation modes."""

    STRICT = "strict"  # Maximum security, some performance impact
    BALANCED = "balanced"  # Balanced security and performance
    PERFORMANCE = "performance"  # Optimized for speed, minimum viable security


@dataclass
class SecurityEnvironmentConfig:
    """Security configuration for specific environment."""

    # File validation settings
    max_file_size: int = 10 * 1024 * 1024  # 10MB default
    allowed_formats: List[str] = field(default_factory=lambda: ["JPEG", "PNG", "WEBP"])
    max_dimensions: tuple[int, int] = (4096, 4096)

    # Security validation settings
    validation_mode: ValidationMode = ValidationMode.BALANCED
    enable_magic_number_check: bool = True
    enable_steganography_detection: bool = True
    enable_polyglot_detection: bool = True

    # Performance settings
    max_concurrent_uploads: int = 15
    processing_timeout_seconds: int = 30
    memory_limit_mb: int = 512

    # Storage settings
    secure_storage_path: str = "/tmp/secure_uploads"
    cleanup_interval_minutes: int = 60
    temp_file_retention_hours: int = 24

    # Monitoring settings
    enable_security_logging: bool = True
    log_level: str = "INFO"
    alert_on_security_events: bool = True

    # Rate limiting
    uploads_per_minute: int = 30
    uploads_per_hour: int = 500
    enable_ip_based_limits: bool = True


class SecurityConfigManager:
    """
    Comprehensive security configuration management for C1-Image Upload Security Pipeline.

    Provides environment-specific security settings, deployment automation,
    configuration validation, and security policy enforcement.
    """

    def __init__(self, environment: SecurityLevel = SecurityLevel.DEVELOPMENT):
        self.environment = environment
        self._config_cache: Dict[str, Any] = {}
        self._config_path = Path("src/deployment/security/configs")
        self._ensure_config_directory()

        # Load environment-specific configuration
        self.config = self._load_environment_config()

        # Initialize security policies
        self.security_policies = self._initialize_security_policies()

        logger.info(f"SecurityConfigManager initialized for {environment.value}")

    def _ensure_config_directory(self) -> None:
        """Ensure configuration directory exists."""
        self._config_path.mkdir(parents=True, exist_ok=True)

        # Create environment-specific config files if they don't exist
        for env in SecurityLevel:
            config_file = self._config_path / f"{env.value}.yaml"
            if not config_file.exists():
                self._create_default_config(env, config_file)

    def _create_default_config(self, env: SecurityLevel, config_file: Path) -> None:
        """Create default configuration for environment."""

        # Environment-specific defaults
        config_defaults = {
            SecurityLevel.DEVELOPMENT: SecurityEnvironmentConfig(
                max_file_size=50 * 1024 * 1024,  # 50MB for dev
                validation_mode=ValidationMode.PERFORMANCE,
                enable_steganography_detection=False,
                max_concurrent_uploads=5,
                alert_on_security_events=False,
                uploads_per_minute=100,
                enable_ip_based_limits=False,
            ),
            SecurityLevel.TESTING: SecurityEnvironmentConfig(
                max_file_size=20 * 1024 * 1024,  # 20MB for testing
                validation_mode=ValidationMode.STRICT,
                enable_steganography_detection=True,
                max_concurrent_uploads=10,
                processing_timeout_seconds=60,
                alert_on_security_events=True,
                uploads_per_minute=50,
            ),
            SecurityLevel.STAGING: SecurityEnvironmentConfig(
                max_file_size=10 * 1024 * 1024,  # 10MB for staging
                validation_mode=ValidationMode.BALANCED,
                enable_steganography_detection=True,
                max_concurrent_uploads=15,
                processing_timeout_seconds=30,
                alert_on_security_events=True,
                uploads_per_minute=30,
                secure_storage_path="/opt/sora/staging/secure_uploads",
            ),
            SecurityLevel.PRODUCTION: SecurityEnvironmentConfig(
                max_file_size=10 * 1024 * 1024,  # 10MB for production
                validation_mode=ValidationMode.STRICT,
                enable_steganography_detection=True,
                enable_polyglot_detection=True,
                max_concurrent_uploads=15,
                processing_timeout_seconds=20,
                memory_limit_mb=256,
                alert_on_security_events=True,
                uploads_per_minute=30,
                uploads_per_hour=500,
                secure_storage_path="/opt/sora/production/secure_uploads",
                cleanup_interval_minutes=30,
                temp_file_retention_hours=12,
            ),
        }

        config = config_defaults[env]

        # Convert to dictionary for YAML serialization
        config_dict = {
            "security_level": env.value,
            "file_validation": {
                "max_file_size": config.max_file_size,
                "allowed_formats": config.allowed_formats,
                "max_dimensions": list(config.max_dimensions),
            },
            "validation_settings": {
                "mode": config.validation_mode.value,
                "enable_magic_number_check": config.enable_magic_number_check,
                "enable_steganography_detection": config.enable_steganography_detection,
                "enable_polyglot_detection": config.enable_polyglot_detection,
            },
            "performance": {
                "max_concurrent_uploads": config.max_concurrent_uploads,
                "processing_timeout_seconds": config.processing_timeout_seconds,
                "memory_limit_mb": config.memory_limit_mb,
            },
            "storage": {
                "secure_storage_path": config.secure_storage_path,
                "cleanup_interval_minutes": config.cleanup_interval_minutes,
                "temp_file_retention_hours": config.temp_file_retention_hours,
            },
            "monitoring": {
                "enable_security_logging": config.enable_security_logging,
                "log_level": config.log_level,
                "alert_on_security_events": config.alert_on_security_events,
            },
            "rate_limiting": {
                "uploads_per_minute": config.uploads_per_minute,
                "uploads_per_hour": config.uploads_per_hour,
                "enable_ip_based_limits": config.enable_ip_based_limits,
            },
        }

        with open(config_file, "w") as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)

        logger.info(f"Created default security config for {env.value}")

    def _load_environment_config(self) -> SecurityEnvironmentConfig:
        """Load configuration for current environment."""
        config_file = self._config_path / f"{self.environment.value}.yaml"

        if not config_file.exists():
            logger.warning(f"Config file {config_file} not found, using defaults")
            return SecurityEnvironmentConfig()

        try:
            with open(config_file) as f:
                config_data = yaml.safe_load(f)

            # Parse configuration data
            return SecurityEnvironmentConfig(
                max_file_size=config_data["file_validation"]["max_file_size"],
                allowed_formats=config_data["file_validation"]["allowed_formats"],
                max_dimensions=tuple(config_data["file_validation"]["max_dimensions"]),
                validation_mode=ValidationMode(
                    config_data["validation_settings"]["mode"]
                ),
                enable_magic_number_check=config_data["validation_settings"][
                    "enable_magic_number_check"
                ],
                enable_steganography_detection=config_data["validation_settings"][
                    "enable_steganography_detection"
                ],
                enable_polyglot_detection=config_data["validation_settings"][
                    "enable_polyglot_detection"
                ],
                max_concurrent_uploads=config_data["performance"][
                    "max_concurrent_uploads"
                ],
                processing_timeout_seconds=config_data["performance"][
                    "processing_timeout_seconds"
                ],
                memory_limit_mb=config_data["performance"]["memory_limit_mb"],
                secure_storage_path=config_data["storage"]["secure_storage_path"],
                cleanup_interval_minutes=config_data["storage"][
                    "cleanup_interval_minutes"
                ],
                temp_file_retention_hours=config_data["storage"][
                    "temp_file_retention_hours"
                ],
                enable_security_logging=config_data["monitoring"][
                    "enable_security_logging"
                ],
                log_level=config_data["monitoring"]["log_level"],
                alert_on_security_events=config_data["monitoring"][
                    "alert_on_security_events"
                ],
                uploads_per_minute=config_data["rate_limiting"]["uploads_per_minute"],
                uploads_per_hour=config_data["rate_limiting"]["uploads_per_hour"],
                enable_ip_based_limits=config_data["rate_limiting"][
                    "enable_ip_based_limits"
                ],
            )

        except Exception as e:
            logger.error(f"Failed to load config from {config_file}: {e}")
            return SecurityEnvironmentConfig()

    def _initialize_security_policies(self) -> Dict[str, Any]:
        """Initialize security policies based on environment."""
        policies = {
            "file_validation": {
                "required_checks": [
                    "magic_number_verification",
                    "format_consistency_check",
                    "size_validation",
                    "dimension_validation",
                ],
                "optional_checks": [],
                "security_level": self.environment.value,
            },
            "malicious_content_detection": {"enabled": True, "checks": []},
            "error_handling": {
                "expose_details": self.environment == SecurityLevel.DEVELOPMENT,
                "log_security_events": self.config.enable_security_logging,
                "alert_threshold": "LOW"
                if self.environment == SecurityLevel.PRODUCTION
                else "MEDIUM",
            },
        }

        # Add security checks based on validation mode
        if self.config.validation_mode == ValidationMode.STRICT:
            policies["malicious_content_detection"]["checks"].extend(
                [
                    "script_injection_detection",
                    "polyglot_file_detection",
                    "steganography_analysis",
                    "executable_disguise_detection",
                ]
            )
        elif self.config.validation_mode == ValidationMode.BALANCED:
            policies["malicious_content_detection"]["checks"].extend(
                ["script_injection_detection", "basic_steganography_check"]
            )

        # Add environment-specific optional checks
        if self.config.enable_steganography_detection:
            if (
                "steganography_analysis"
                not in policies["malicious_content_detection"]["checks"]
            ):
                policies["file_validation"]["optional_checks"].append(
                    "steganography_detection"
                )

        if self.config.enable_polyglot_detection:
            if (
                "polyglot_file_detection"
                not in policies["malicious_content_detection"]["checks"]
            ):
                policies["file_validation"]["optional_checks"].append(
                    "polyglot_detection"
                )

        return policies

    def get_security_config(self) -> Dict[str, Any]:
        """Get complete security configuration for deployment."""
        return {
            "environment": self.environment.value,
            "file_validation": {
                "max_file_size": self.config.max_file_size,
                "allowed_formats": self.config.allowed_formats,
                "max_dimensions": self.config.max_dimensions,
                "magic_number_check": self.config.enable_magic_number_check,
            },
            "validation_settings": {
                "mode": self.config.validation_mode.value,
                "steganography_detection": self.config.enable_steganography_detection,
                "polyglot_detection": self.config.enable_polyglot_detection,
            },
            "performance_limits": {
                "max_concurrent_uploads": self.config.max_concurrent_uploads,
                "processing_timeout": self.config.processing_timeout_seconds,
                "memory_limit_mb": self.config.memory_limit_mb,
            },
            "storage_config": {
                "secure_path": self.config.secure_storage_path,
                "cleanup_interval": self.config.cleanup_interval_minutes,
                "retention_hours": self.config.temp_file_retention_hours,
            },
            "monitoring_config": {
                "security_logging": self.config.enable_security_logging,
                "log_level": self.config.log_level,
                "alert_on_events": self.config.alert_on_security_events,
            },
            "rate_limiting": {
                "uploads_per_minute": self.config.uploads_per_minute,
                "uploads_per_hour": self.config.uploads_per_hour,
                "ip_based_limits": self.config.enable_ip_based_limits,
            },
            "security_policies": self.security_policies,
        }

    def validate_deployment_config(self) -> Dict[str, Any]:
        """Validate deployment configuration for security compliance."""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "security_score": 100,
            "recommendations": [],
        }

        # File size validation
        if self.config.max_file_size > 50 * 1024 * 1024:  # 50MB
            validation_results["warnings"].append(
                f"Large file size limit ({self.config.max_file_size / 1024 / 1024:.1f}MB) may impact performance"
            )
            validation_results["security_score"] -= 5

        # Security validation checks
        if not self.config.enable_magic_number_check:
            validation_results["errors"].append(
                "Magic number verification must be enabled"
            )
            validation_results["valid"] = False
            validation_results["security_score"] -= 20

        # Production-specific validations
        if self.environment == SecurityLevel.PRODUCTION:
            if self.config.validation_mode != ValidationMode.STRICT:
                validation_results["warnings"].append(
                    "Production should use STRICT validation mode for maximum security"
                )
                validation_results["security_score"] -= 10

            if not self.config.enable_steganography_detection:
                validation_results["warnings"].append(
                    "Steganography detection recommended for production"
                )
                validation_results["security_score"] -= 5

            if not self.config.alert_on_security_events:
                validation_results["errors"].append(
                    "Security event alerting must be enabled in production"
                )
                validation_results["valid"] = False
                validation_results["security_score"] -= 15

        # Performance vs security balance
        if (
            self.config.validation_mode == ValidationMode.PERFORMANCE
            and self.environment in [SecurityLevel.STAGING, SecurityLevel.PRODUCTION]
        ):
            validation_results["warnings"].append(
                "Performance mode may compromise security in staging/production"
            )
            validation_results["security_score"] -= 15

        # Storage path validation
        if not os.path.isabs(self.config.secure_storage_path):
            validation_results["errors"].append("Secure storage path must be absolute")
            validation_results["valid"] = False

        # Rate limiting validation
        if self.config.uploads_per_minute > 100:
            validation_results["warnings"].append("High rate limits may allow abuse")
            validation_results["security_score"] -= 5

        # Generate recommendations based on findings
        if validation_results["security_score"] < 90:
            validation_results["recommendations"].append(
                "Consider enabling all security features for better protection"
            )

        if self.config.memory_limit_mb > 1024:  # 1GB
            validation_results["recommendations"].append(
                "Reduce memory limit to prevent resource exhaustion attacks"
            )

        return validation_results

    def generate_deployment_manifest(self) -> Dict[str, Any]:
        """Generate deployment manifest for security configuration."""
        config = self.get_security_config()
        validation = self.validate_deployment_config()

        return {
            "deployment_metadata": {
                "environment": self.environment.value,
                "config_version": "1.0.0",
                "created_at": self._get_timestamp(),
                "config_hash": self._calculate_config_hash(config),
            },
            "security_configuration": config,
            "validation_results": validation,
            "deployment_ready": validation["valid"]
            and validation["security_score"] >= 80,
            "required_environment_vars": self._get_required_env_vars(),
            "infrastructure_requirements": self._get_infrastructure_requirements(),
        }

    def export_environment_config(self, target_path: Optional[str] = None) -> str:
        """Export environment configuration for deployment."""
        if target_path is None:
            target_path = f"security_config_{self.environment.value}.json"

        manifest = self.generate_deployment_manifest()

        with open(target_path, "w") as f:
            json.dump(manifest, f, indent=2, default=str)

        logger.info(f"Security configuration exported to {target_path}")
        return target_path

    def _get_required_env_vars(self) -> List[str]:
        """Get required environment variables for deployment."""
        base_vars = [
            "SECURITY_ENVIRONMENT",
            "SECURE_STORAGE_PATH",
            "MAX_FILE_SIZE",
            "ALLOWED_FORMATS",
        ]

        if self.config.enable_security_logging:
            base_vars.extend(["SECURITY_LOG_LEVEL", "SECURITY_LOG_PATH"])

        if self.config.alert_on_security_events:
            base_vars.extend(["ALERT_WEBHOOK_URL", "ALERT_EMAIL_RECIPIENTS"])

        return base_vars

    def _get_infrastructure_requirements(self) -> Dict[str, Any]:
        """Get infrastructure requirements for deployment."""
        return {
            "storage": {
                "secure_upload_directory": self.config.secure_storage_path,
                "disk_space_minimum": "10GB",
                "backup_required": True,
            },
            "compute": {
                "memory_per_worker": f"{self.config.memory_limit_mb}MB",
                "cpu_cores_minimum": 2,
                "concurrent_processing": self.config.max_concurrent_uploads,
            },
            "network": {
                "rate_limiting_required": True,
                "firewall_rules": [
                    "Block file upload from known malicious IPs",
                    "Rate limit upload endpoints",
                ],
            },
            "monitoring": {
                "security_event_logging": self.config.enable_security_logging,
                "alerting_required": self.config.alert_on_security_events,
                "metrics_collection": True,
            },
        }

    def _calculate_config_hash(self, config: Dict[str, Any]) -> str:
        """Calculate hash of configuration for integrity verification."""
        config_str = json.dumps(config, sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()[:16]

    def _get_timestamp(self) -> str:
        """Get current timestamp for deployment tracking."""
        from datetime import datetime

        return datetime.utcnow().isoformat() + "Z"


# Factory function for creating security config manager
def create_security_config_manager(
    environment: Optional[str] = None,
) -> SecurityConfigManager:
    """
    Factory function to create SecurityConfigManager with environment detection.

    Args:
        environment: Optional environment override

    Returns:
        Configured SecurityConfigManager instance
    """
    if environment is None:
        # Auto-detect environment from environment variables
        environment = os.getenv("SECURITY_ENVIRONMENT", "development")

    try:
        security_level = SecurityLevel(environment.lower())
    except ValueError:
        logger.warning(
            f"Unknown environment '{environment}', defaulting to development"
        )
        security_level = SecurityLevel.DEVELOPMENT

    return SecurityConfigManager(security_level)


# Module-global security config manager instance
security_config_manager = create_security_config_manager()
