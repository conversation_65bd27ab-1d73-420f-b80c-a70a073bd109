"""
Security Operations Monitor for C1-Image Upload Security Pipeline.

Comprehensive operational security monitoring with threat detection, security logging
without sensitive data exposure, comprehensive security health checks, and real-time
security observability for production deployment operations.

This module provides:
- Real-time operational security monitoring
- Advanced threat detection and behavioral analysis
- Security health checks with comprehensive validation
- Secure logging without sensitive data exposure
- Performance monitoring with security focus
- Incident detection and automated response
- Security metrics collection and analysis
- Integration with deployment and operations systems

Usage:
    from src.deployment.security.security_operations_monitor import SecurityOperationsMonitor

    ops_monitor = SecurityOperationsMonitor(
        environment="production",
        monitoring_level="comprehensive"
    )

    # Start monitoring
    await ops_monitor.start_monitoring()

    # Monitor security event
    await ops_monitor.monitor_security_event(
        event_type="file_upload_validation",
        event_data={"file_hash": "abc123", "validation_result": "malicious"}
    )
"""

import asyncio
import hashlib
import json
import logging
import os
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Union

import psutil

from src.deployment.security.security_config_manager import (
    SecurityLevel,
)
from src.deployment.security.security_monitoring import (
    AlertSeverity,
    SecurityEventType,
    SecurityMonitor,
    ThreatLevel,
)
from src.monitoring.f4_alerting import F4AlertManager
from src.monitoring.f4_metrics import F4MetricsCollector

logger = logging.getLogger(__name__)


class MonitoringLevel(Enum):
    """Security monitoring intensity levels."""

    BASIC = "basic"  # Basic security monitoring
    STANDARD = "standard"  # Standard monitoring with threat detection
    COMPREHENSIVE = "comprehensive"  # Comprehensive monitoring with advanced features
    PARANOID = "paranoid"  # Maximum monitoring with all features enabled


class SecurityHealthStatus(Enum):
    """Security health status levels."""

    HEALTHY = "healthy"
    WARNING = "warning"
    DEGRADED = "degraded"
    CRITICAL = "critical"
    COMPROMISED = "compromised"


class ThreatDetectionMode(Enum):
    """Threat detection operating modes."""

    PASSIVE = "passive"  # Log only, no active response
    ACTIVE = "active"  # Active threat detection with alerts
    DEFENSIVE = "defensive"  # Active detection with automated defensive actions
    AGGRESSIVE = "aggressive"  # Aggressive detection with immediate blocking


@dataclass
class SecurityOperationsConfig:
    """Configuration for security operations monitoring."""

    # Monitoring settings
    monitoring_level: MonitoringLevel = MonitoringLevel.STANDARD
    threat_detection_mode: ThreatDetectionMode = ThreatDetectionMode.ACTIVE
    health_check_interval: int = 60  # seconds
    metrics_collection_interval: int = 30  # seconds

    # Threat detection settings
    threat_detection_enabled: bool = True
    behavioral_analysis_enabled: bool = True
    anomaly_detection_enabled: bool = True
    pattern_recognition_enabled: bool = True

    # Performance monitoring
    performance_monitoring_enabled: bool = True
    resource_monitoring_enabled: bool = True
    latency_monitoring_enabled: bool = True

    # Security logging
    secure_logging_enabled: bool = True
    log_retention_days: int = 30
    log_encryption_enabled: bool = True
    pii_sanitization_enabled: bool = True

    # Alerting settings
    real_time_alerting_enabled: bool = True
    alert_escalation_enabled: bool = True
    incident_response_enabled: bool = True

    # Health check settings
    comprehensive_health_checks: bool = True
    security_baseline_validation: bool = True
    compliance_checking: bool = True


@dataclass
class SecurityHealthCheck:
    """Security health check result."""

    check_id: str
    check_name: str
    check_type: str
    status: SecurityHealthStatus
    timestamp: datetime
    duration_ms: float
    details: Dict[str, Any] = field(default_factory=dict)
    issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    metrics: Dict[str, float] = field(default_factory=dict)


class ThreatDetectionEngine:
    """Advanced threat detection engine with behavioral analysis."""

    def __init__(self, config: SecurityOperationsConfig):
        self.config = config
        self.threat_patterns = defaultdict(list)
        self.behavioral_baselines = {}
        self.anomaly_scores = defaultdict(float)
        self.threat_indicators = defaultdict(set)

        # Detection thresholds
        self.anomaly_threshold = 0.7
        self.threat_escalation_threshold = 0.8
        self.critical_threat_threshold = 0.9

        # Pattern tracking
        self.file_upload_patterns = deque(maxlen=1000)
        self.ip_behavior_patterns = defaultdict(lambda: deque(maxlen=100))
        self.validation_failure_patterns = deque(maxlen=500)

        logger.info("ThreatDetectionEngine initialized")

    def analyze_file_upload_event(
        self,
        file_hash: str,
        file_size: int,
        file_type: str,
        source_ip: str,
        validation_result: str,
        processing_time: float,
    ) -> Dict[str, Any]:
        """Analyze file upload event for threats."""

        threat_analysis = {
            "threat_score": 0.0,
            "threat_indicators": [],
            "behavioral_anomalies": [],
            "recommendations": [],
            "severity": "low",
        }

        # Track upload pattern
        upload_event = {
            "timestamp": time.time(),
            "file_hash": file_hash,
            "file_size": file_size,
            "file_type": file_type,
            "source_ip": source_ip,
            "validation_result": validation_result,
            "processing_time": processing_time,
        }

        self.file_upload_patterns.append(upload_event)
        self.ip_behavior_patterns[source_ip].append(upload_event)

        # Malicious file detection
        if validation_result == "malicious":
            threat_analysis["threat_score"] += 0.8
            threat_analysis["threat_indicators"].append("malicious_file_detected")
            threat_analysis["severity"] = "critical"

        # Suspicious file patterns
        if validation_result == "suspicious":
            threat_analysis["threat_score"] += 0.4
            threat_analysis["threat_indicators"].append("suspicious_file_pattern")

        # Behavioral analysis
        behavioral_analysis = self._analyze_behavioral_patterns(source_ip, upload_event)
        threat_analysis["behavioral_anomalies"] = behavioral_analysis["anomalies"]
        threat_analysis["threat_score"] += behavioral_analysis["anomaly_score"]

        # Rate analysis
        rate_analysis = self._analyze_upload_rate(source_ip)
        if rate_analysis["rate_exceeded"]:
            threat_analysis["threat_score"] += 0.3
            threat_analysis["threat_indicators"].append("high_upload_rate")

        # File size anomaly detection
        size_analysis = self._analyze_file_size_anomalies(file_size, file_type)
        if size_analysis["anomaly_detected"]:
            threat_analysis["threat_score"] += 0.2
            threat_analysis["behavioral_anomalies"].append("unusual_file_size")

        # Processing time analysis
        time_analysis = self._analyze_processing_time_anomalies(
            processing_time, file_size
        )
        if time_analysis["anomaly_detected"]:
            threat_analysis["threat_score"] += 0.1
            threat_analysis["behavioral_anomalies"].append("unusual_processing_time")

        # Determine final severity
        if threat_analysis["threat_score"] >= self.critical_threat_threshold:
            threat_analysis["severity"] = "critical"
        elif threat_analysis["threat_score"] >= self.threat_escalation_threshold:
            threat_analysis["severity"] = "high"
        elif threat_analysis["threat_score"] >= self.anomaly_threshold:
            threat_analysis["severity"] = "medium"

        # Generate recommendations
        if threat_analysis["threat_score"] > 0.5:
            threat_analysis["recommendations"].extend(
                [
                    "Monitor source IP closely",
                    "Review recent uploads from this source",
                    "Consider temporary rate limiting",
                ]
            )

        if "malicious_file_detected" in threat_analysis["threat_indicators"]:
            threat_analysis["recommendations"].extend(
                [
                    "Block source IP immediately",
                    "Scan all recent uploads from this source",
                    "Update threat detection signatures",
                ]
            )

        return threat_analysis

    def _analyze_behavioral_patterns(
        self, source_ip: str, current_event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze behavioral patterns for anomalies."""

        ip_history = self.ip_behavior_patterns[source_ip]

        analysis = {"anomalies": [], "anomaly_score": 0.0, "patterns_detected": []}

        if len(ip_history) < 5:  # Not enough data for analysis
            return analysis

        # Analyze upload frequency
        recent_uploads = [
            e for e in ip_history if time.time() - e["timestamp"] < 3600
        ]  # Last hour
        if len(recent_uploads) > 20:  # More than 20 uploads in an hour
            analysis["anomalies"].append("high_frequency_uploads")
            analysis["anomaly_score"] += 0.3

        # Analyze file type patterns
        file_types = [e["file_type"] for e in ip_history]
        unique_types = set(file_types)
        if len(unique_types) > 5:  # Many different file types
            analysis["anomalies"].append("diverse_file_types")
            analysis["anomaly_score"] += 0.2

        # Analyze file size patterns
        file_sizes = [e["file_size"] for e in ip_history]
        avg_size = sum(file_sizes) / len(file_sizes)
        current_size = current_event["file_size"]

        if current_size > avg_size * 3:  # Much larger than average
            analysis["anomalies"].append("unusually_large_file")
            analysis["anomaly_score"] += 0.1

        # Analyze validation failure patterns
        failures = [
            e
            for e in ip_history
            if e["validation_result"] in ["suspicious", "malicious"]
        ]
        failure_rate = len(failures) / len(ip_history)

        if failure_rate > 0.3:  # More than 30% failures
            analysis["anomalies"].append("high_failure_rate")
            analysis["anomaly_score"] += 0.4

        return analysis

    def _analyze_upload_rate(self, source_ip: str) -> Dict[str, Any]:
        """Analyze upload rate for anomalies."""

        ip_history = self.ip_behavior_patterns[source_ip]
        current_time = time.time()

        # Count uploads in different time windows
        uploads_last_minute = len(
            [e for e in ip_history if current_time - e["timestamp"] < 60]
        )
        uploads_last_hour = len(
            [e for e in ip_history if current_time - e["timestamp"] < 3600]
        )

        return {
            "rate_exceeded": uploads_last_minute > 10 or uploads_last_hour > 100,
            "uploads_last_minute": uploads_last_minute,
            "uploads_last_hour": uploads_last_hour,
        }

    def _analyze_file_size_anomalies(
        self, file_size: int, file_type: str
    ) -> Dict[str, Any]:
        """Analyze file size for anomalies based on type."""

        # Expected size ranges by file type (in bytes)
        expected_ranges = {
            "JPEG": (1000, 10_000_000),  # 1KB to 10MB
            "PNG": (1000, 5_000_000),  # 1KB to 5MB
            "WEBP": (1000, 8_000_000),  # 1KB to 8MB
            "GIF": (1000, 20_000_000),  # 1KB to 20MB (for animations)
        }

        min_size, max_size = expected_ranges.get(
            file_type, (0, 50_000_000)
        )  # Default range

        anomaly_detected = file_size < min_size or file_size > max_size

        return {
            "anomaly_detected": anomaly_detected,
            "file_size": file_size,
            "expected_range": (min_size, max_size),
            "size_category": "too_small"
            if file_size < min_size
            else "too_large"
            if file_size > max_size
            else "normal",
        }

    def _analyze_processing_time_anomalies(
        self, processing_time: float, file_size: int
    ) -> Dict[str, Any]:
        """Analyze processing time for anomalies."""

        # Expected processing time based on file size (rough estimates)
        expected_time_per_mb = 0.5  # 0.5 seconds per MB
        file_size_mb = file_size / (1024 * 1024)
        expected_time = max(0.1, file_size_mb * expected_time_per_mb)

        # Allow 5x variance
        anomaly_detected = processing_time > expected_time * 5

        return {
            "anomaly_detected": anomaly_detected,
            "processing_time": processing_time,
            "expected_time": expected_time,
            "performance_ratio": processing_time / expected_time,
        }

    def get_threat_summary(self) -> Dict[str, Any]:
        """Get summary of current threat landscape."""

        current_time = time.time()
        recent_uploads = [
            e for e in self.file_upload_patterns if current_time - e["timestamp"] < 3600
        ]

        # Calculate threat statistics
        malicious_files = len(
            [e for e in recent_uploads if e["validation_result"] == "malicious"]
        )
        suspicious_files = len(
            [e for e in recent_uploads if e["validation_result"] == "suspicious"]
        )
        total_uploads = len(recent_uploads)

        # IP analysis
        active_ips = set(e["source_ip"] for e in recent_uploads)
        high_activity_ips = [
            ip
            for ip in active_ips
            if len([e for e in recent_uploads if e["source_ip"] == ip]) > 10
        ]

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "total_uploads_last_hour": total_uploads,
            "malicious_files_detected": malicious_files,
            "suspicious_files_detected": suspicious_files,
            "threat_rate_percent": (malicious_files + suspicious_files)
            / total_uploads
            * 100
            if total_uploads > 0
            else 0,
            "active_source_ips": len(active_ips),
            "high_activity_ips": len(high_activity_ips),
            "threat_indicators_active": len(self.threat_indicators),
            "anomaly_scores": dict(self.anomaly_scores),
        }


class SecurityHealthMonitor:
    """Comprehensive security health monitoring."""

    def __init__(self, config: SecurityOperationsConfig):
        self.config = config
        self.health_checks = {}
        self.health_history = deque(maxlen=1000)
        self.baseline_metrics = {}

        logger.info("SecurityHealthMonitor initialized")

    async def perform_comprehensive_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive security health check."""

        logger.info("Performing comprehensive security health check")

        start_time = datetime.now(timezone.utc)
        health_results = {
            "overall_status": SecurityHealthStatus.HEALTHY,
            "timestamp": start_time.isoformat(),
            "check_results": {},
            "critical_issues": [],
            "warnings": [],
            "recommendations": [],
            "metrics": {},
        }

        # Define health checks based on configuration
        health_checks = [
            ("container_security", self._check_container_security),
            ("network_security", self._check_network_security),
            ("file_system_security", self._check_file_system_security),
            ("process_security", self._check_process_security),
            ("memory_security", self._check_memory_security),
            ("log_security", self._check_log_security),
            ("monitoring_health", self._check_monitoring_health),
            ("alert_system_health", self._check_alert_system_health),
        ]

        # Add compliance checks if enabled
        if self.config.compliance_checking:
            health_checks.append(("compliance_status", self._check_compliance_status))

        # Execute health checks
        for check_name, check_function in health_checks:
            try:
                check_start = time.time()
                check_result = await check_function()
                check_duration = (time.time() - check_start) * 1000

                health_check = SecurityHealthCheck(
                    check_id=f"health_{check_name}_{int(time.time())}",
                    check_name=check_name,
                    check_type="security_health",
                    status=check_result["status"],
                    timestamp=datetime.now(timezone.utc),
                    duration_ms=check_duration,
                    details=check_result.get("details", {}),
                    issues=check_result.get("issues", []),
                    recommendations=check_result.get("recommendations", []),
                    metrics=check_result.get("metrics", {}),
                )

                health_results["check_results"][check_name] = {
                    "status": check_result["status"].value,
                    "duration_ms": check_duration,
                    "details": check_result.get("details", {}),
                    "issues": check_result.get("issues", []),
                    "recommendations": check_result.get("recommendations", []),
                    "metrics": check_result.get("metrics", {}),
                }

                # Aggregate issues and recommendations
                health_results["warnings"].extend(check_result.get("issues", []))
                health_results["recommendations"].extend(
                    check_result.get("recommendations", [])
                )

                # Update overall status
                if check_result["status"] == SecurityHealthStatus.CRITICAL:
                    health_results["overall_status"] = SecurityHealthStatus.CRITICAL
                    health_results["critical_issues"].extend(
                        check_result.get("issues", [])
                    )
                elif (
                    check_result["status"] == SecurityHealthStatus.DEGRADED
                    and health_results["overall_status"]
                    != SecurityHealthStatus.CRITICAL
                ):
                    health_results["overall_status"] = SecurityHealthStatus.DEGRADED
                elif (
                    check_result["status"] == SecurityHealthStatus.WARNING
                    and health_results["overall_status"] == SecurityHealthStatus.HEALTHY
                ):
                    health_results["overall_status"] = SecurityHealthStatus.WARNING

                # Store check in history
                self.health_checks[check_name] = health_check

            except Exception as e:
                logger.error(f"Health check {check_name} failed: {e}")
                health_results["check_results"][check_name] = {
                    "status": "error",
                    "error": str(e),
                    "duration_ms": 0,
                }
                health_results["critical_issues"].append(
                    f"Health check {check_name} failed: {e}"
                )
                health_results["overall_status"] = SecurityHealthStatus.CRITICAL

        # Store health check in history
        self.health_history.append(health_results)

        # Calculate total duration
        total_duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        health_results["total_duration_seconds"] = total_duration

        logger.info(
            f"Health check completed in {total_duration:.2f}s - Status: {health_results['overall_status'].value}"
        )

        return health_results

    async def _check_container_security(self) -> Dict[str, Any]:
        """Check container security status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Check Docker daemon status
            import subprocess

            result = subprocess.run(
                ["docker", "info"], capture_output=True, text=True, timeout=10
            )

            if result.returncode != 0:
                check_result["status"] = SecurityHealthStatus.CRITICAL
                check_result["issues"].append("Docker daemon not accessible")
                return check_result

            # Check running containers
            result = subprocess.run(
                ["docker", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if result.returncode == 0 and result.stdout:
                containers = []
                for line in result.stdout.strip().split("\n"):
                    if line:
                        try:
                            containers.append(json.loads(line))
                        except json.JSONDecodeError:
                            continue

                check_result["details"]["running_containers"] = len(containers)
                check_result["metrics"]["container_count"] = len(containers)

                # Check for containers running as root
                root_containers = 0
                privileged_containers = 0

                for container in containers:
                    # Check container configuration (simplified)
                    inspect_result = subprocess.run(
                        ["docker", "inspect", container.get("ID", "")],
                        capture_output=True,
                        text=True,
                        timeout=5,
                    )

                    if inspect_result.returncode == 0:
                        try:
                            inspect_data = json.loads(inspect_result.stdout)[0]
                            config = inspect_data.get("Config", {})
                            host_config = inspect_data.get("HostConfig", {})

                            # Check if running as root
                            if config.get("User") == "" or config.get("User") == "root":
                                root_containers += 1

                            # Check if privileged
                            if host_config.get("Privileged", False):
                                privileged_containers += 1

                        except (json.JSONDecodeError, IndexError, KeyError):
                            continue

                if root_containers > 0:
                    check_result["status"] = SecurityHealthStatus.WARNING
                    check_result["issues"].append(
                        f"{root_containers} containers running as root"
                    )
                    check_result["recommendations"].append(
                        "Configure containers to run as non-root user"
                    )

                if privileged_containers > 0:
                    check_result["status"] = SecurityHealthStatus.CRITICAL
                    check_result["issues"].append(
                        f"{privileged_containers} containers running in privileged mode"
                    )
                    check_result["recommendations"].append(
                        "Remove privileged mode from containers"
                    )

                check_result["metrics"]["root_containers"] = root_containers
                check_result["metrics"]["privileged_containers"] = privileged_containers

        except subprocess.TimeoutExpired:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append("Docker commands timed out")
        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Container security check failed: {e}")

        return check_result

    async def _check_network_security(self) -> Dict[str, Any]:
        """Check network security status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Check open ports
            import socket

            # Check common ports that should not be open
            dangerous_ports = [22, 23, 21, 135, 139, 445, 1433, 3306, 5432, 6379, 27017]
            open_dangerous_ports = []

            for port in dangerous_ports:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(("localhost", port))
                sock.close()

                if result == 0:  # Port is open
                    open_dangerous_ports.append(port)

            if open_dangerous_ports:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append(
                    f"Potentially dangerous ports open: {open_dangerous_ports}"
                )
                check_result["recommendations"].append("Review and secure open ports")

            check_result["metrics"]["open_dangerous_ports"] = len(open_dangerous_ports)
            check_result["details"]["dangerous_ports_found"] = open_dangerous_ports

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Network security check failed: {e}")

        return check_result

    async def _check_file_system_security(self) -> Dict[str, Any]:
        """Check file system security status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Check critical directories permissions
            critical_paths = [
                "/opt/sora/security-deployment",
                "/opt/sora/data",
                "/opt/sora/logs",
                "/opt/sora/backups",
            ]

            permission_issues = []

            for path in critical_paths:
                if os.path.exists(path):
                    stat_info = os.stat(path)
                    mode = stat_info.st_mode

                    # Check if world-writable
                    if mode & 0o002:
                        permission_issues.append(f"{path} is world-writable")

                    # Check if group-writable for sensitive paths
                    if "security" in path and mode & 0o020:
                        permission_issues.append(f"{path} is group-writable")

            if permission_issues:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].extend(permission_issues)
                check_result["recommendations"].append("Fix file system permissions")

            check_result["metrics"]["permission_issues"] = len(permission_issues)

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"File system security check failed: {e}")

        return check_result

    async def _check_process_security(self) -> Dict[str, Any]:
        """Check process security status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Get process information
            root_processes = 0
            high_cpu_processes = 0
            suspicious_processes = []

            for proc in psutil.process_iter(["pid", "name", "username", "cpu_percent"]):
                try:
                    proc_info = proc.info

                    # Count root processes
                    if proc_info["username"] == "root":
                        root_processes += 1

                    # Check for high CPU usage
                    if proc_info["cpu_percent"] and proc_info["cpu_percent"] > 80:
                        high_cpu_processes += 1

                    # Check for suspicious process names
                    suspicious_names = [
                        "nc",
                        "netcat",
                        "ncat",
                        "socat",
                        "tcpdump",
                        "wireshark",
                    ]
                    if any(
                        name in proc_info["name"].lower() for name in suspicious_names
                    ):
                        suspicious_processes.append(proc_info["name"])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if suspicious_processes:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append(
                    f"Suspicious processes detected: {suspicious_processes}"
                )
                check_result["recommendations"].append("Review running processes")

            if high_cpu_processes > 5:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append(
                    f"{high_cpu_processes} processes using high CPU"
                )

            check_result["metrics"]["root_processes"] = root_processes
            check_result["metrics"]["high_cpu_processes"] = high_cpu_processes
            check_result["metrics"]["suspicious_processes"] = len(suspicious_processes)

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Process security check failed: {e}")

        return check_result

    async def _check_memory_security(self) -> Dict[str, Any]:
        """Check memory security status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Check memory usage
            memory = psutil.virtual_memory()

            memory_usage_percent = memory.percent
            available_gb = memory.available / (1024**3)

            if memory_usage_percent > 90:
                check_result["status"] = SecurityHealthStatus.CRITICAL
                check_result["issues"].append(
                    f"Critical memory usage: {memory_usage_percent:.1f}%"
                )
            elif memory_usage_percent > 80:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append(
                    f"High memory usage: {memory_usage_percent:.1f}%"
                )

            if available_gb < 0.5:  # Less than 500MB available
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append(
                    f"Low available memory: {available_gb:.2f}GB"
                )
                check_result["recommendations"].append(
                    "Monitor for potential memory exhaustion attacks"
                )

            check_result["metrics"]["memory_usage_percent"] = memory_usage_percent
            check_result["metrics"]["available_memory_gb"] = available_gb
            check_result["details"]["total_memory_gb"] = memory.total / (1024**3)

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Memory security check failed: {e}")

        return check_result

    async def _check_log_security(self) -> Dict[str, Any]:
        """Check log security status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Check log directories
            log_paths = [
                "/opt/sora/logs",
                "/var/log",
                "/opt/sora/security-deployment/logs",
            ]

            log_issues = []
            total_log_size = 0

            for log_path in log_paths:
                if os.path.exists(log_path):
                    # Check permissions
                    stat_info = os.stat(log_path)
                    if stat_info.st_mode & 0o044:  # World or group readable
                        log_issues.append(
                            f"Log directory {log_path} has overly permissive permissions"
                        )

                    # Calculate size
                    for root, dirs, files in os.walk(log_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                total_log_size += os.path.getsize(file_path)
                            except OSError:
                                continue

            if log_issues:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].extend(log_issues)
                check_result["recommendations"].append("Fix log directory permissions")

            log_size_gb = total_log_size / (1024**3)
            if log_size_gb > 10:  # More than 10GB of logs
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append(
                    f"Large log files detected: {log_size_gb:.2f}GB"
                )
                check_result["recommendations"].append(
                    "Implement log rotation and cleanup"
                )

            check_result["metrics"]["total_log_size_gb"] = log_size_gb
            check_result["metrics"]["log_permission_issues"] = len(log_issues)

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Log security check failed: {e}")

        return check_result

    async def _check_monitoring_health(self) -> Dict[str, Any]:
        """Check monitoring system health."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        # This would check the actual monitoring system health
        # For now, we'll simulate basic checks

        try:
            # Check if monitoring is active (placeholder)
            monitoring_active = True  # Would check actual monitoring status

            if not monitoring_active:
                check_result["status"] = SecurityHealthStatus.CRITICAL
                check_result["issues"].append("Security monitoring is not active")
                check_result["recommendations"].append(
                    "Restart security monitoring system"
                )

            check_result["metrics"]["monitoring_active"] = 1 if monitoring_active else 0

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Monitoring health check failed: {e}")

        return check_result

    async def _check_alert_system_health(self) -> Dict[str, Any]:
        """Check alert system health."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        try:
            # Check alert channels (placeholder)
            alert_channels_active = 2  # Would check actual alert channels

            if alert_channels_active == 0:
                check_result["status"] = SecurityHealthStatus.CRITICAL
                check_result["issues"].append("No alert channels are active")
            elif alert_channels_active == 1:
                check_result["status"] = SecurityHealthStatus.WARNING
                check_result["issues"].append("Only one alert channel is active")
                check_result["recommendations"].append(
                    "Configure backup alert channels"
                )

            check_result["metrics"]["active_alert_channels"] = alert_channels_active

        except Exception as e:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(f"Alert system health check failed: {e}")

        return check_result

    async def _check_compliance_status(self) -> Dict[str, Any]:
        """Check compliance status."""

        check_result = {
            "status": SecurityHealthStatus.HEALTHY,
            "details": {},
            "issues": [],
            "recommendations": [],
            "metrics": {},
        }

        # Placeholder for compliance checking
        # Would implement actual compliance validation

        compliance_score = 85  # Simulated compliance score

        if compliance_score < 70:
            check_result["status"] = SecurityHealthStatus.CRITICAL
            check_result["issues"].append(f"Low compliance score: {compliance_score}%")
        elif compliance_score < 85:
            check_result["status"] = SecurityHealthStatus.WARNING
            check_result["issues"].append(
                f"Moderate compliance score: {compliance_score}%"
            )

        check_result["metrics"]["compliance_score"] = compliance_score

        return check_result


class SecurityOperationsMonitor:
    """
    Comprehensive Security Operations Monitor for C1-Image Upload Security Pipeline.

    Provides real-time operational security monitoring with threat detection,
    security logging without sensitive data exposure, comprehensive security health checks,
    and integration with deployment and operations systems.
    """

    def __init__(
        self,
        environment: Union[str, SecurityLevel] = SecurityLevel.DEVELOPMENT,
        monitoring_level: Union[str, MonitoringLevel] = MonitoringLevel.STANDARD,
        config: Optional[SecurityOperationsConfig] = None,
    ):
        """
        Initialize Security Operations Monitor.

        Args:
            environment: Deployment environment
            monitoring_level: Security monitoring intensity level
            config: Optional custom configuration
        """
        # Parse enum values
        if isinstance(environment, str):
            environment = SecurityLevel(environment.lower())
        if isinstance(monitoring_level, str):
            monitoring_level = MonitoringLevel(monitoring_level.lower())

        self.environment = environment
        self.monitoring_level = monitoring_level

        # Initialize configuration
        if config is None:
            config = self._create_default_config(monitoring_level)
        self.config = config

        # Initialize components
        self.metrics_collector = F4MetricsCollector()
        self.security_monitor = SecurityMonitor(
            metrics_collector=self.metrics_collector,
            log_file=f"/opt/sora/logs/security_operations_{environment.value}.log",
        )
        self.threat_detection_engine = ThreatDetectionEngine(config)
        self.health_monitor = SecurityHealthMonitor(config)
        self.alert_manager = F4AlertManager()

        # Monitoring state
        self.monitoring_active = False
        self.monitoring_tasks = []
        self.last_health_check = None
        self.performance_baselines = {}

        # Secure logging setup
        self._setup_secure_logging()

        logger.info(
            f"SecurityOperationsMonitor initialized - Environment: {environment.value}, Level: {monitoring_level.value}"
        )

    def _create_default_config(
        self, monitoring_level: MonitoringLevel
    ) -> SecurityOperationsConfig:
        """Create default configuration based on monitoring level."""

        base_config = SecurityOperationsConfig(monitoring_level=monitoring_level)

        if monitoring_level == MonitoringLevel.BASIC:
            base_config.threat_detection_enabled = True
            base_config.behavioral_analysis_enabled = False
            base_config.anomaly_detection_enabled = False
            base_config.comprehensive_health_checks = False

        elif monitoring_level == MonitoringLevel.STANDARD:
            base_config.threat_detection_enabled = True
            base_config.behavioral_analysis_enabled = True
            base_config.anomaly_detection_enabled = True
            base_config.comprehensive_health_checks = True

        elif monitoring_level == MonitoringLevel.COMPREHENSIVE:
            base_config.threat_detection_enabled = True
            base_config.behavioral_analysis_enabled = True
            base_config.anomaly_detection_enabled = True
            base_config.pattern_recognition_enabled = True
            base_config.comprehensive_health_checks = True
            base_config.security_baseline_validation = True
            base_config.compliance_checking = True

        elif monitoring_level == MonitoringLevel.PARANOID:
            base_config.threat_detection_enabled = True
            base_config.behavioral_analysis_enabled = True
            base_config.anomaly_detection_enabled = True
            base_config.pattern_recognition_enabled = True
            base_config.comprehensive_health_checks = True
            base_config.security_baseline_validation = True
            base_config.compliance_checking = True
            base_config.threat_detection_mode = ThreatDetectionMode.AGGRESSIVE
            base_config.health_check_interval = 30  # More frequent checks
            base_config.metrics_collection_interval = 15

        return base_config

    def _setup_secure_logging(self) -> None:
        """Setup secure logging with PII sanitization."""

        # Create secure logger for operations
        self.secure_logger = logging.getLogger("security_operations")
        self.secure_logger.setLevel(logging.INFO)

        # Remove existing handlers
        for handler in self.secure_logger.handlers[:]:
            self.secure_logger.removeHandler(handler)

        # Create secure log formatter
        secure_formatter = logging.Formatter(
            "%(asctime)s - SECURITY-OPS - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S UTC",
        )

        # File handler for operations logs
        ops_log_path = (
            f"/opt/sora/logs/security_operations_{self.environment.value}.log"
        )
        os.makedirs(os.path.dirname(ops_log_path), exist_ok=True)

        file_handler = logging.FileHandler(ops_log_path)
        file_handler.setFormatter(secure_formatter)
        self.secure_logger.addHandler(file_handler)

        # Console handler for development
        if self.environment == SecurityLevel.DEVELOPMENT:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(secure_formatter)
            self.secure_logger.addHandler(console_handler)

    def _sanitize_log_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize log data to remove sensitive information."""

        sanitized = data.copy()

        # Remove or hash sensitive fields
        sensitive_fields = ["password", "token", "key", "secret", "credential"]

        for key in list(sanitized.keys()):
            if any(field in key.lower() for field in sensitive_fields):
                sanitized[key] = "[REDACTED]"
            elif key == "file_content":
                sanitized[key] = "[FILE_CONTENT_REDACTED]"
            elif key == "user_data" and isinstance(sanitized[key], dict):
                # Sanitize nested user data
                sanitized[key] = self._sanitize_user_data(sanitized[key])
            elif isinstance(sanitized[key], str) and len(sanitized[key]) > 100:
                # Truncate very long strings
                sanitized[key] = sanitized[key][:100] + "...[TRUNCATED]"

        return sanitized

    def _sanitize_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize user-related data."""

        sanitized = user_data.copy()

        # Hash IP addresses for privacy
        if "ip_address" in sanitized:
            ip = sanitized["ip_address"]
            ip_hash = hashlib.sha256(ip.encode()).hexdigest()[:12]
            sanitized["ip_address"] = f"ip_{ip_hash}"

        # Remove email addresses and personal info
        personal_fields = ["email", "name", "phone", "address"]
        for field in personal_fields:
            if field in sanitized:
                sanitized[field] = "[PII_REDACTED]"

        return sanitized

    async def start_monitoring(self) -> None:
        """Start comprehensive security operations monitoring."""

        if self.monitoring_active:
            logger.warning("Security operations monitoring already active")
            return

        logger.info("Starting security operations monitoring")
        self.monitoring_active = True

        # Start component monitoring
        self.security_monitor.start_real_time_monitoring(
            interval_seconds=self.config.metrics_collection_interval
        )
        self.metrics_collector.start_collection()

        # Start monitoring tasks
        if self.config.comprehensive_health_checks:
            health_task = asyncio.create_task(self._health_monitoring_loop())
            self.monitoring_tasks.append(health_task)

        if self.config.threat_detection_enabled:
            threat_task = asyncio.create_task(self._threat_monitoring_loop())
            self.monitoring_tasks.append(threat_task)

        if self.config.performance_monitoring_enabled:
            perf_task = asyncio.create_task(self._performance_monitoring_loop())
            self.monitoring_tasks.append(perf_task)

        self.secure_logger.info(
            f"Security operations monitoring started - Level: {self.monitoring_level.value}"
        )

    async def stop_monitoring(self) -> None:
        """Stop security operations monitoring."""

        if not self.monitoring_active:
            return

        logger.info("Stopping security operations monitoring")
        self.monitoring_active = False

        # Stop component monitoring
        self.security_monitor.stop_real_time_monitoring()
        self.metrics_collector.stop_collection()

        # Cancel monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()

        # Wait for tasks to complete
        if self.monitoring_tasks:
            await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)

        self.monitoring_tasks.clear()
        self.secure_logger.info("Security operations monitoring stopped")

    async def monitor_security_event(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        source_ip: Optional[str] = None,
        user_session: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Monitor and analyze a security event."""

        start_time = time.time()

        # Sanitize event data for logging
        sanitized_data = self._sanitize_log_data(event_data)

        monitoring_result = {
            "event_type": event_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "threat_analysis": {},
            "monitoring_actions": [],
            "alerts_generated": [],
            "processing_time_ms": 0,
        }

        try:
            # Perform threat analysis based on event type
            if event_type == "file_upload_validation":
                threat_analysis = (
                    self.threat_detection_engine.analyze_file_upload_event(
                        file_hash=event_data.get("file_hash", ""),
                        file_size=event_data.get("file_size", 0),
                        file_type=event_data.get("file_type", ""),
                        source_ip=source_ip or "unknown",
                        validation_result=event_data.get(
                            "validation_result", "unknown"
                        ),
                        processing_time=event_data.get("processing_time", 0.0),
                    )
                )

                monitoring_result["threat_analysis"] = threat_analysis

                # Generate security event
                security_event_type = (
                    SecurityEventType.MALICIOUS_FILE_DETECTED
                    if threat_analysis["severity"] == "critical"
                    else SecurityEventType.SUSPICIOUS_UPLOAD_PATTERN
                )

                severity_map = {
                    "low": AlertSeverity.LOW,
                    "medium": AlertSeverity.MEDIUM,
                    "high": AlertSeverity.HIGH,
                    "critical": AlertSeverity.CRITICAL,
                }

                alert_severity = severity_map.get(
                    threat_analysis["severity"], AlertSeverity.LOW
                )

                if threat_analysis["severity"] == "critical":
                    threat_level = ThreatLevel.CRITICAL_THREAT
                elif threat_analysis["severity"] == "high":
                    threat_level = ThreatLevel.MALICIOUS
                elif threat_analysis["severity"] == "medium":
                    threat_level = ThreatLevel.SUSPICIOUS
                else:
                    threat_level = ThreatLevel.BENIGN

                # Record security event
                security_event = self.security_monitor.record_security_event(
                    event_type=security_event_type,
                    severity=alert_severity,
                    threat_level=threat_level,
                    source_ip=source_ip,
                    user_session=user_session,
                    file_hash=event_data.get("file_hash"),
                    file_name=event_data.get("file_name"),
                    file_size=event_data.get("file_size"),
                    validation_details=sanitized_data,
                    threat_indicators=threat_analysis["threat_indicators"],
                    metadata={
                        "event_type": event_type,
                        "threat_score": threat_analysis["threat_score"],
                        "behavioral_anomalies": threat_analysis["behavioral_anomalies"],
                    },
                )

                monitoring_result["monitoring_actions"].append(
                    "security_event_recorded"
                )

                # Handle defensive actions
                if self.config.threat_detection_mode in [
                    ThreatDetectionMode.DEFENSIVE,
                    ThreatDetectionMode.AGGRESSIVE,
                ]:
                    if threat_analysis["severity"] in ["high", "critical"]:
                        defensive_actions = await self._execute_defensive_actions(
                            threat_analysis, source_ip
                        )
                        monitoring_result["monitoring_actions"].extend(
                            defensive_actions
                        )

            else:
                # Generic event monitoring
                self.secure_logger.info(
                    f"Security event monitored: {event_type}",
                    extra={"event_data": sanitized_data},
                )
                monitoring_result["monitoring_actions"].append("event_logged")

            # Record performance metrics
            processing_time = (time.time() - start_time) * 1000
            monitoring_result["processing_time_ms"] = processing_time

            self.metrics_collector.record_config_load_time(processing_time, "success")

            return monitoring_result

        except Exception as e:
            logger.error(f"Error monitoring security event {event_type}: {e}")

            # Record error event
            self.security_monitor.record_security_event(
                event_type=SecurityEventType.SYSTEM_SECURITY_ALERT,
                severity=AlertSeverity.HIGH,
                threat_level=ThreatLevel.SUSPICIOUS,
                validation_details={"error": str(e), "event_type": event_type},
                metadata={"monitoring_error": True},
            )

            monitoring_result["error"] = str(e)
            monitoring_result["processing_time_ms"] = (time.time() - start_time) * 1000

            return monitoring_result

    async def _execute_defensive_actions(
        self, threat_analysis: Dict[str, Any], source_ip: Optional[str]
    ) -> List[str]:
        """Execute defensive actions based on threat analysis."""

        actions_taken = []

        try:
            # Rate limiting for high-threat sources
            if source_ip and threat_analysis["threat_score"] > 0.7:
                # In a real implementation, this would integrate with rate limiting system
                self.secure_logger.warning(
                    f"High threat score detected for IP {source_ip}: {threat_analysis['threat_score']:.2f}"
                )
                actions_taken.append("rate_limiting_applied")

            # Temporary blocking for critical threats
            if (
                self.config.threat_detection_mode == ThreatDetectionMode.AGGRESSIVE
                and threat_analysis["severity"] == "critical"
            ):
                self.secure_logger.critical(
                    f"Critical threat detected - IP {source_ip} flagged for blocking"
                )
                actions_taken.append("ip_flagged_for_blocking")

            # Alert escalation
            if threat_analysis["threat_score"] > 0.8:
                await self._escalate_security_alert(threat_analysis, source_ip)
                actions_taken.append("alert_escalated")

        except Exception as e:
            logger.error(f"Error executing defensive actions: {e}")
            actions_taken.append(f"defensive_action_error: {e}")

        return actions_taken

    async def _escalate_security_alert(
        self, threat_analysis: Dict[str, Any], source_ip: Optional[str]
    ) -> None:
        """Escalate security alert to appropriate channels."""

        alert_message = f"High-severity security threat detected - Score: {threat_analysis['threat_score']:.2f}, Indicators: {threat_analysis['threat_indicators']}"

        # Send escalated alert
        self.alert_manager.send_alert(
            alert_type="security_threat_escalation",
            severity="critical",
            message=alert_message,
            context={
                "threat_score": threat_analysis["threat_score"],
                "threat_indicators": threat_analysis["threat_indicators"],
                "source_ip": source_ip,
                "escalation_reason": "automated_threat_detection",
            },
        )

    async def _health_monitoring_loop(self) -> None:
        """Continuous health monitoring loop."""

        while self.monitoring_active:
            try:
                # Perform health check
                health_result = (
                    await self.health_monitor.perform_comprehensive_health_check()
                )
                self.last_health_check = health_result

                # Log health status
                status = health_result["overall_status"]
                self.secure_logger.info(
                    f"Health check completed - Status: {status.value}"
                )

                # Send alerts for critical health issues
                if status in [
                    SecurityHealthStatus.CRITICAL,
                    SecurityHealthStatus.COMPROMISED,
                ]:
                    await self._send_health_alert(health_result)

                # Wait for next check
                await asyncio.sleep(self.config.health_check_interval)

            except Exception as e:
                logger.error(f"Health monitoring loop error: {e}")
                await asyncio.sleep(min(self.config.health_check_interval, 60))

    async def _threat_monitoring_loop(self) -> None:
        """Continuous threat monitoring loop."""

        while self.monitoring_active:
            try:
                # Get threat summary
                threat_summary = self.threat_detection_engine.get_threat_summary()

                # Check for concerning patterns
                if threat_summary["threat_rate_percent"] > 10:  # More than 10% threats
                    self.secure_logger.warning(
                        f"High threat rate detected: {threat_summary['threat_rate_percent']:.1f}%"
                    )

                if (
                    threat_summary["malicious_files_detected"] > 5
                ):  # More than 5 malicious files in an hour
                    await self._send_threat_alert(threat_summary)

                # Wait for next check
                await asyncio.sleep(self.config.metrics_collection_interval)

            except Exception as e:
                logger.error(f"Threat monitoring loop error: {e}")
                await asyncio.sleep(min(self.config.metrics_collection_interval, 60))

    async def _performance_monitoring_loop(self) -> None:
        """Continuous performance monitoring loop."""

        while self.monitoring_active:
            try:
                # Collect performance metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage("/")

                # Record performance metrics
                self.security_monitor.record_performance_event(
                    concurrent_uploads=0,  # Would get actual count
                    processing_time=0.1,  # Would get actual processing time
                    memory_usage_mb=memory.used / (1024**2),
                )

                # Check for performance issues
                if cpu_percent > 90:
                    self.secure_logger.warning(
                        f"High CPU usage detected: {cpu_percent:.1f}%"
                    )

                if memory.percent > 90:
                    self.secure_logger.warning(
                        f"High memory usage detected: {memory.percent:.1f}%"
                    )

                # Wait for next check
                await asyncio.sleep(self.config.metrics_collection_interval)

            except Exception as e:
                logger.error(f"Performance monitoring loop error: {e}")
                await asyncio.sleep(min(self.config.metrics_collection_interval, 60))

    async def _send_health_alert(self, health_result: Dict[str, Any]) -> None:
        """Send health alert for critical issues."""

        status = health_result["overall_status"]
        critical_issues = health_result.get("critical_issues", [])

        alert_message = f"Security health status: {status.value}"
        if critical_issues:
            alert_message += f" - Issues: {', '.join(critical_issues[:3])}"

        self.alert_manager.send_alert(
            alert_type="security_health_critical",
            severity="critical",
            message=alert_message,
            context={
                "health_status": status.value,
                "critical_issues": critical_issues,
                "check_timestamp": health_result["timestamp"],
            },
        )

    async def _send_threat_alert(self, threat_summary: Dict[str, Any]) -> None:
        """Send threat alert for high threat activity."""

        alert_message = f"High threat activity detected - {threat_summary['malicious_files_detected']} malicious files, {threat_summary['threat_rate_percent']:.1f}% threat rate"

        self.alert_manager.send_alert(
            alert_type="high_threat_activity",
            severity="high",
            message=alert_message,
            context=threat_summary,
        )

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status and metrics."""

        return {
            "monitoring_active": self.monitoring_active,
            "environment": self.environment.value,
            "monitoring_level": self.monitoring_level.value,
            "config": {
                "threat_detection_enabled": self.config.threat_detection_enabled,
                "behavioral_analysis_enabled": self.config.behavioral_analysis_enabled,
                "comprehensive_health_checks": self.config.comprehensive_health_checks,
                "threat_detection_mode": self.config.threat_detection_mode.value,
            },
            "last_health_check": self.last_health_check,
            "threat_summary": self.threat_detection_engine.get_threat_summary()
            if self.monitoring_active
            else {},
            "metrics_summary": self.security_monitor.get_security_dashboard()
            if self.monitoring_active
            else {},
            "monitoring_tasks_count": len(self.monitoring_tasks),
        }


# Factory function for creating security operations monitor
def create_security_operations_monitor(
    environment: Union[str, SecurityLevel] = SecurityLevel.DEVELOPMENT,
    monitoring_level: Union[str, MonitoringLevel] = MonitoringLevel.STANDARD,
    config: Optional[SecurityOperationsConfig] = None,
) -> SecurityOperationsMonitor:
    """
    Factory function to create SecurityOperationsMonitor with environment detection.

    Args:
        environment: Deployment environment
        monitoring_level: Security monitoring intensity level
        config: Optional custom configuration

    Returns:
        Configured SecurityOperationsMonitor instance
    """

    # Auto-detect environment from environment variables if string
    if isinstance(environment, str):
        env_map = {
            "dev": SecurityLevel.DEVELOPMENT,
            "development": SecurityLevel.DEVELOPMENT,
            "test": SecurityLevel.TESTING,
            "testing": SecurityLevel.TESTING,
            "stage": SecurityLevel.STAGING,
            "staging": SecurityLevel.STAGING,
            "prod": SecurityLevel.PRODUCTION,
            "production": SecurityLevel.PRODUCTION,
        }
        environment = env_map.get(environment.lower(), SecurityLevel.DEVELOPMENT)

    # Auto-detect monitoring level from environment variables if string
    if isinstance(monitoring_level, str):
        level_map = {
            "basic": MonitoringLevel.BASIC,
            "standard": MonitoringLevel.STANDARD,
            "comprehensive": MonitoringLevel.COMPREHENSIVE,
            "paranoid": MonitoringLevel.PARANOID,
        }
        monitoring_level = level_map.get(
            monitoring_level.lower(), MonitoringLevel.STANDARD
        )

    return SecurityOperationsMonitor(
        environment=environment, monitoring_level=monitoring_level, config=config
    )


# Module-global security operations monitor instance
security_operations_monitor = create_security_operations_monitor(
    environment=os.getenv("SECURITY_ENVIRONMENT", "development"),
    monitoring_level=os.getenv("SECURITY_MONITORING_LEVEL", "standard"),
)
