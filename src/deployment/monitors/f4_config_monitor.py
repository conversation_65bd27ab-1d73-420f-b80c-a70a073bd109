#!/usr/bin/env python3
"""
F4 Configuration Health Monitor

Continuous monitoring service for F4 environment configuration health,
provider availability, and configuration switching performance.

This service provides:
- Real-time configuration validation and health checks
- Provider availability monitoring and alerting
- Configuration switching performance monitoring
- Environment variable validation and error detection
- Integration health monitoring for dependent services
- Automated alerting and reporting for configuration issues

Usage:
    python -m src.deployment.monitors.f4_config_monitor

Environment Variables:
    MONITOR_INTERVAL: Monitoring interval in seconds (default: 60)
    ALERT_THRESHOLD_CONFIG_LOAD_TIME: Alert threshold for config load time in ms
    ALERT_THRESHOLD_PROVIDER_SWITCH_TIME: Alert threshold for provider switching in ms
    CONFIG_MONITOR_LOG_LEVEL: Log level for monitor (default: INFO)
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List
from uuid import uuid4

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.factory import ConfigurationFactory
from src.config.veo3_settings import get_cached_veo3_settings, validate_veo3_environment

# Setup logging
logging.basicConfig(
    level=getattr(logging, os.getenv("CONFIG_MONITOR_LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("/tmp/f4_config_monitor.log", mode="a"),
    ],
)

logger = logging.getLogger(__name__)


class F4ConfigurationMonitor:
    """
    Continuous monitoring service for F4 configuration health and performance.

    Provides comprehensive monitoring of:
    - Configuration loading and validation performance
    - Provider availability and switching capabilities
    - Environment variable health and completeness
    - Integration health with dependent services
    - Performance metrics and threshold alerting
    """

    def __init__(self):
        """Initialize the F4 configuration monitor."""
        self.monitor_interval = int(os.getenv("MONITOR_INTERVAL", "60"))
        self.config_load_threshold = int(
            os.getenv("ALERT_THRESHOLD_CONFIG_LOAD_TIME", "5000")
        )
        self.provider_switch_threshold = int(
            os.getenv("ALERT_THRESHOLD_PROVIDER_SWITCH_TIME", "10000")
        )

        self.monitoring_active = True
        self.last_check_time = None
        self.check_count = 0
        self.error_count = 0
        self.alert_count = 0

        # Performance metrics storage
        self.performance_metrics = {
            "config_load_times": [],
            "provider_switch_times": [],
            "validation_times": [],
            "provider_availability_history": [],
        }

        logger.info(
            f"F4 Configuration Monitor initialized - Interval: {self.monitor_interval}s"
        )

    async def run_health_check(self) -> Dict[str, Any]:
        """
        Run comprehensive F4 configuration health check.

        Returns:
            Dict containing health check results and performance metrics
        """
        check_id = str(uuid4())[:8]
        start_time = time.time()

        logger.info(f"Starting F4 health check {check_id}")

        health_result = {
            "check_id": check_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_status": "healthy",
            "checks": {},
            "performance": {},
            "alerts": [],
            "errors": [],
        }

        try:
            # 1. Configuration Loading Performance
            config_check = await self._check_configuration_loading()
            health_result["checks"]["configuration_loading"] = config_check

            # 2. Provider Availability
            provider_check = await self._check_provider_availability()
            health_result["checks"]["provider_availability"] = provider_check

            # 3. Environment Variables Validation
            env_check = await self._check_environment_variables()
            health_result["checks"]["environment_variables"] = env_check

            # 4. Provider Switching Performance
            switching_check = await self._check_provider_switching()
            health_result["checks"]["provider_switching"] = switching_check

            # 5. Integration Health
            integration_check = await self._check_integration_health()
            health_result["checks"]["integration_health"] = integration_check

            # 6. Performance Metrics Analysis
            performance_analysis = await self._analyze_performance_metrics()
            health_result["performance"] = performance_analysis

            # Determine overall status
            health_result["overall_status"] = self._determine_overall_status(
                health_result
            )

            # Generate alerts if needed
            alerts = self._generate_alerts(health_result)
            health_result["alerts"] = alerts

            self.check_count += 1

        except Exception as e:
            logger.error(f"Health check {check_id} failed: {e}")
            health_result["overall_status"] = "error"
            health_result["errors"].append(f"Health check failed: {str(e)}")
            self.error_count += 1

        total_time = (time.time() - start_time) * 1000
        health_result["check_duration_ms"] = total_time
        self.last_check_time = datetime.now(timezone.utc)

        logger.info(
            f"F4 health check {check_id} completed - Status: {health_result['overall_status']} ({total_time:.2f}ms)"
        )

        return health_result

    async def _check_configuration_loading(self) -> Dict[str, Any]:
        """Check configuration loading performance and correctness."""
        start_time = time.time()

        try:
            # Test basic configuration factory
            base_config = ConfigurationFactory.get_base_config()

            # Test Veo3 settings loading
            veo3_settings = get_cached_veo3_settings()

            # Test provider configuration creation
            veo3_config = ConfigurationFactory.create_veo3_config()
            azure_config = ConfigurationFactory.get_azure_config()

            load_time_ms = (time.time() - start_time) * 1000
            self.performance_metrics["config_load_times"].append(load_time_ms)

            # Keep only last 100 measurements
            if len(self.performance_metrics["config_load_times"]) > 100:
                self.performance_metrics["config_load_times"] = (
                    self.performance_metrics["config_load_times"][-100:]
                )

            return {
                "status": "healthy",
                "load_time_ms": load_time_ms,
                "threshold_ms": self.config_load_threshold,
                "exceeds_threshold": load_time_ms > self.config_load_threshold,
                "configurations_loaded": {
                    "base_config": bool(base_config),
                    "veo3_settings": bool(veo3_settings),
                    "veo3_config": bool(veo3_config),
                    "azure_config": bool(azure_config),
                },
            }

        except Exception as e:
            logger.error(f"Configuration loading check failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "load_time_ms": None,
                "threshold_ms": self.config_load_threshold,
                "exceeds_threshold": True,
            }

    async def _check_provider_availability(self) -> Dict[str, Any]:
        """Check availability of all configured providers."""
        try:
            # Get provider availability
            availability = ConfigurationFactory.get_provider_availability()

            # Validate each provider configuration
            azure_validation = ConfigurationFactory.validate_provider_configuration(
                "azure_sora"
            )
            veo3_validation = ConfigurationFactory.validate_provider_configuration(
                "google_veo3"
            )

            # Store availability history
            availability_snapshot = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "azure_sora": availability.get("azure_sora", False),
                "google_veo3": availability.get("google_veo3", False),
            }
            self.performance_metrics["provider_availability_history"].append(
                availability_snapshot
            )

            # Keep only last 100 measurements
            if len(self.performance_metrics["provider_availability_history"]) > 100:
                self.performance_metrics["provider_availability_history"] = (
                    self.performance_metrics["provider_availability_history"][-100:]
                )

            # Determine status
            total_available = sum(availability.values())
            status = "healthy" if total_available > 0 else "critical"

            return {
                "status": status,
                "availability": availability,
                "total_available": total_available,
                "validations": {
                    "azure_sora": azure_validation,
                    "google_veo3": veo3_validation,
                },
                "default_provider": ConfigurationFactory.get_default_provider(),
            }

        except Exception as e:
            logger.error(f"Provider availability check failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "availability": {},
                "total_available": 0,
            }

    async def _check_environment_variables(self) -> Dict[str, Any]:
        """Validate F4 environment variables and configuration completeness."""
        try:
            # List of critical F4 environment variables
            critical_vars = ["USE_MOCK_VEO", "DEFAULT_PROVIDER", "DEPLOYMENT_TYPE"]

            # List of optional F4 environment variables
            optional_vars = [
                "GOOGLE_PROJECT_ID",
                "GOOGLE_CLIENT_ID",
                "GOOGLE_CLIENT_SECRET",
                "VEO3_MODEL_VERSION",
                "VEO3_TIMEOUT",
                "CONFIG_VALIDATION_ENABLED",
            ]

            missing_critical = []
            missing_optional = []
            present_vars = {}

            # Check critical variables
            for var in critical_vars:
                value = os.getenv(var)
                if value is None:
                    missing_critical.append(var)
                else:
                    present_vars[var] = value

            # Check optional variables
            for var in optional_vars:
                value = os.getenv(var)
                if value is None:
                    missing_optional.append(var)
                else:
                    present_vars[var] = value

            # Run Veo3 environment validation
            veo3_validation = validate_veo3_environment()

            # Determine status
            if missing_critical:
                status = "critical"
            elif not veo3_validation.get("valid", False):
                status = "warning"
            else:
                status = "healthy"

            return {
                "status": status,
                "critical_variables": {
                    "required": critical_vars,
                    "missing": missing_critical,
                    "present": len(critical_vars) - len(missing_critical),
                },
                "optional_variables": {
                    "available": optional_vars,
                    "missing": missing_optional,
                    "present": len(optional_vars) - len(missing_optional),
                },
                "veo3_validation": veo3_validation,
                "deployment_type": os.getenv("DEPLOYMENT_TYPE", "unknown"),
                "environment": os.getenv("F4_DEPLOYMENT_ENVIRONMENT", "unknown"),
            }

        except Exception as e:
            logger.error(f"Environment variables check failed: {e}")
            return {"status": "error", "error": str(e)}

    async def _check_provider_switching(self) -> Dict[str, Any]:
        """Test provider switching performance and functionality."""
        start_time = time.time()

        try:
            # Test provider configuration creation for both providers
            providers = ["azure_sora", "google_veo3"]
            switch_results = {}

            for provider in providers:
                provider_start = time.time()
                try:
                    config = ConfigurationFactory.create_provider_config(provider)
                    provider_time = (time.time() - provider_start) * 1000
                    switch_results[provider] = {
                        "status": "success",
                        "switch_time_ms": provider_time,
                        "config_created": bool(config),
                    }
                except Exception as e:
                    provider_time = (time.time() - provider_start) * 1000
                    switch_results[provider] = {
                        "status": "error",
                        "switch_time_ms": provider_time,
                        "error": str(e),
                    }

            total_switch_time = (time.time() - start_time) * 1000
            self.performance_metrics["provider_switch_times"].append(total_switch_time)

            # Keep only last 100 measurements
            if len(self.performance_metrics["provider_switch_times"]) > 100:
                self.performance_metrics["provider_switch_times"] = (
                    self.performance_metrics["provider_switch_times"][-100:]
                )

            # Test optimal provider selection
            try:
                optimal_config = ConfigurationFactory.get_optimal_provider_config()
                optimal_status = "success"
            except Exception as e:
                optimal_config = None
                optimal_status = f"error: {str(e)}"

            # Determine overall status
            successful_switches = sum(
                1 for r in switch_results.values() if r["status"] == "success"
            )
            if successful_switches == 0:
                status = "critical"
            elif successful_switches < len(providers):
                status = "warning"
            elif total_switch_time > self.provider_switch_threshold:
                status = "warning"
            else:
                status = "healthy"

            return {
                "status": status,
                "total_switch_time_ms": total_switch_time,
                "threshold_ms": self.provider_switch_threshold,
                "exceeds_threshold": total_switch_time > self.provider_switch_threshold,
                "provider_results": switch_results,
                "successful_switches": successful_switches,
                "optimal_provider_selection": {
                    "status": optimal_status,
                    "config_available": bool(optimal_config),
                },
            }

        except Exception as e:
            logger.error(f"Provider switching check failed: {e}")
            return {"status": "error", "error": str(e), "total_switch_time_ms": None}

    async def _check_integration_health(self) -> Dict[str, Any]:
        """Check integration health with dependent services."""
        try:
            integrations = {}

            # Check database connectivity (if available)
            try:
                from src.database.connection import get_db_connection

                db_conn = get_db_connection()
                integrations["database"] = {
                    "status": "healthy" if db_conn else "error",
                    "available": bool(db_conn),
                }
            except Exception as e:
                integrations["database"] = {
                    "status": "error",
                    "error": str(e),
                    "available": False,
                }

            # Check Redis connectivity (if available)
            try:
                import redis

                redis_url = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
                r = redis.from_url(redis_url)
                r.ping()
                integrations["redis"] = {"status": "healthy", "available": True}
            except Exception as e:
                integrations["redis"] = {
                    "status": "error",
                    "error": str(e),
                    "available": False,
                }

            # Check application endpoints (if running)
            try:
                import requests

                app_url = "http://localhost:5001/health/config"
                response = requests.get(app_url, timeout=10)
                integrations["application"] = {
                    "status": "healthy" if response.status_code == 200 else "warning",
                    "status_code": response.status_code,
                    "available": True,
                }
            except Exception as e:
                integrations["application"] = {
                    "status": "warning",
                    "error": str(e),
                    "available": False,
                }

            # Determine overall integration status
            healthy_integrations = sum(
                1 for i in integrations.values() if i["status"] == "healthy"
            )
            total_integrations = len(integrations)

            if healthy_integrations == 0:
                status = "critical"
            elif healthy_integrations < total_integrations:
                status = "warning"
            else:
                status = "healthy"

            return {
                "status": status,
                "integrations": integrations,
                "healthy_count": healthy_integrations,
                "total_count": total_integrations,
            }

        except Exception as e:
            logger.error(f"Integration health check failed: {e}")
            return {"status": "error", "error": str(e)}

    async def _analyze_performance_metrics(self) -> Dict[str, Any]:
        """Analyze performance metrics and trends."""
        try:
            analysis = {
                "config_loading": self._analyze_metric_list(
                    self.performance_metrics["config_load_times"],
                    "Config Load Time (ms)",
                ),
                "provider_switching": self._analyze_metric_list(
                    self.performance_metrics["provider_switch_times"],
                    "Provider Switch Time (ms)",
                ),
                "provider_availability": self._analyze_availability_history(),
            }

            return analysis

        except Exception as e:
            logger.error(f"Performance metrics analysis failed: {e}")
            return {"error": str(e)}

    def _analyze_metric_list(
        self, metrics: List[float], metric_name: str
    ) -> Dict[str, Any]:
        """Analyze a list of performance metrics."""
        if not metrics:
            return {"metric_name": metric_name, "count": 0, "statistics": None}

        return {
            "metric_name": metric_name,
            "count": len(metrics),
            "statistics": {
                "min": min(metrics),
                "max": max(metrics),
                "avg": sum(metrics) / len(metrics),
                "latest": metrics[-1] if metrics else None,
            },
            "trend": self._calculate_trend(metrics),
        }

    def _analyze_availability_history(self) -> Dict[str, Any]:
        """Analyze provider availability history."""
        history = self.performance_metrics["provider_availability_history"]

        if not history:
            return {"count": 0, "availability": {}}

        # Calculate availability percentages
        azure_available = sum(1 for h in history if h.get("azure_sora", False))
        veo3_available = sum(1 for h in history if h.get("google_veo3", False))
        total_checks = len(history)

        return {
            "count": total_checks,
            "availability": {
                "azure_sora": {
                    "available_count": azure_available,
                    "percentage": (azure_available / total_checks) * 100
                    if total_checks > 0
                    else 0,
                },
                "google_veo3": {
                    "available_count": veo3_available,
                    "percentage": (veo3_available / total_checks) * 100
                    if total_checks > 0
                    else 0,
                },
            },
            "latest": history[-1] if history else None,
        }

    def _calculate_trend(self, metrics: List[float]) -> str:
        """Calculate trend direction for metrics."""
        if len(metrics) < 5:
            return "insufficient_data"

        recent = metrics[-5:]
        earlier = metrics[-10:-5] if len(metrics) >= 10 else metrics[:-5]

        if not earlier:
            return "insufficient_data"

        recent_avg = sum(recent) / len(recent)
        earlier_avg = sum(earlier) / len(earlier)

        diff_percent = ((recent_avg - earlier_avg) / earlier_avg) * 100

        if diff_percent > 10:
            return "increasing"
        elif diff_percent < -10:
            return "decreasing"
        else:
            return "stable"

    def _determine_overall_status(self, health_result: Dict[str, Any]) -> str:
        """Determine overall health status from individual checks."""
        checks = health_result.get("checks", {})

        statuses = [check.get("status", "unknown") for check in checks.values()]

        if "error" in statuses or "critical" in statuses:
            return "critical"
        elif "warning" in statuses:
            return "warning"
        elif all(status == "healthy" for status in statuses):
            return "healthy"
        else:
            return "unknown"

    def _generate_alerts(self, health_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate alerts based on health check results."""
        alerts = []

        try:
            # Configuration loading performance alert
            config_check = health_result.get("checks", {}).get(
                "configuration_loading", {}
            )
            if config_check.get("exceeds_threshold", False):
                alerts.append(
                    {
                        "type": "performance",
                        "severity": "warning",
                        "message": f"Configuration loading time ({config_check.get('load_time_ms', 0):.2f}ms) exceeds threshold ({self.config_load_threshold}ms)",
                        "metric": "config_load_time",
                        "value": config_check.get("load_time_ms", 0),
                        "threshold": self.config_load_threshold,
                    }
                )

            # Provider switching performance alert
            switching_check = health_result.get("checks", {}).get(
                "provider_switching", {}
            )
            if switching_check.get("exceeds_threshold", False):
                alerts.append(
                    {
                        "type": "performance",
                        "severity": "warning",
                        "message": f"Provider switching time ({switching_check.get('total_switch_time_ms', 0):.2f}ms) exceeds threshold ({self.provider_switch_threshold}ms)",
                        "metric": "provider_switch_time",
                        "value": switching_check.get("total_switch_time_ms", 0),
                        "threshold": self.provider_switch_threshold,
                    }
                )

            # Provider availability alert
            provider_check = health_result.get("checks", {}).get(
                "provider_availability", {}
            )
            if provider_check.get("total_available", 0) == 0:
                alerts.append(
                    {
                        "type": "availability",
                        "severity": "critical",
                        "message": "No video generation providers are available",
                        "metric": "provider_availability",
                        "value": 0,
                        "available_providers": provider_check.get("availability", {}),
                    }
                )

            # Environment variables alert
            env_check = health_result.get("checks", {}).get("environment_variables", {})
            missing_critical = env_check.get("critical_variables", {}).get(
                "missing", []
            )
            if missing_critical:
                alerts.append(
                    {
                        "type": "configuration",
                        "severity": "critical",
                        "message": f"Missing critical environment variables: {', '.join(missing_critical)}",
                        "metric": "environment_variables",
                        "missing_variables": missing_critical,
                    }
                )

            self.alert_count += len(alerts)

        except Exception as e:
            logger.error(f"Alert generation failed: {e}")
            alerts.append(
                {
                    "type": "system",
                    "severity": "error",
                    "message": f"Alert generation failed: {str(e)}",
                    "error": str(e),
                }
            )

        return alerts

    async def save_health_report(self, health_result: Dict[str, Any]) -> str:
        """Save health check report to file."""
        try:
            report_dir = Path("/tmp/f4_health_reports")
            report_dir.mkdir(exist_ok=True)

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"f4_health_report_{timestamp}.json"

            with open(report_file, "w") as f:
                json.dump(health_result, f, indent=2, default=str)

            logger.info(f"Health report saved to {report_file}")
            return str(report_file)

        except Exception as e:
            logger.error(f"Failed to save health report: {e}")
            return ""

    async def send_alerts(self, alerts: List[Dict[str, Any]]) -> None:
        """Send alerts to configured endpoints."""
        if not alerts:
            return

        try:
            # Log alerts
            for alert in alerts:
                severity = alert.get("severity", "unknown")
                message = alert.get("message", "Unknown alert")
                logger.log(
                    logging.ERROR
                    if severity in ["critical", "error"]
                    else logging.WARNING,
                    f"F4 ALERT [{severity.upper()}]: {message}",
                )

            # Send to external systems (if configured)
            slack_webhook = os.getenv("SLACK_WEBHOOK_URL")
            if slack_webhook:
                await self._send_slack_alerts(slack_webhook, alerts)

            alert_email = os.getenv("ALERT_EMAIL")
            if alert_email:
                await self._send_email_alerts(alert_email, alerts)

        except Exception as e:
            logger.error(f"Failed to send alerts: {e}")

    async def _send_slack_alerts(
        self, webhook_url: str, alerts: List[Dict[str, Any]]
    ) -> None:
        """Send alerts to Slack."""
        try:
            import requests

            critical_alerts = [a for a in alerts if a.get("severity") == "critical"]
            warning_alerts = [a for a in alerts if a.get("severity") == "warning"]

            if critical_alerts or warning_alerts:
                message = "🚨 F4 Configuration Health Alerts\n\n"

                if critical_alerts:
                    message += "**CRITICAL ISSUES:**\n"
                    for alert in critical_alerts:
                        message += (
                            f"• {alert.get('message', 'Unknown critical issue')}\n"
                        )
                    message += "\n"

                if warning_alerts:
                    message += "**WARNINGS:**\n"
                    for alert in warning_alerts:
                        message += f"• {alert.get('message', 'Unknown warning')}\n"
                    message += "\n"

                message += f"Time: {datetime.now(timezone.utc).isoformat()}\n"
                message += "Monitor: F4 Configuration Health Monitor"

                response = requests.post(
                    webhook_url, json={"text": message}, timeout=10
                )

                if response.status_code == 200:
                    logger.info("Alerts sent to Slack successfully")
                else:
                    logger.error(
                        f"Failed to send Slack alerts: HTTP {response.status_code}"
                    )

        except Exception as e:
            logger.error(f"Slack alert sending failed: {e}")

    async def _send_email_alerts(
        self, email: str, alerts: List[Dict[str, Any]]
    ) -> None:
        """Send alerts via email."""
        try:

            # This is a basic implementation - would need proper SMTP configuration
            logger.info(
                f"Email alerts would be sent to {email} (not implemented in this demo)"
            )

        except Exception as e:
            logger.error(f"Email alert sending failed: {e}")

    def get_monitor_status(self) -> Dict[str, Any]:
        """Get current monitor status and statistics."""
        return {
            "monitor_active": self.monitoring_active,
            "monitor_interval": self.monitor_interval,
            "last_check": self.last_check_time.isoformat()
            if self.last_check_time
            else None,
            "statistics": {
                "total_checks": self.check_count,
                "error_count": self.error_count,
                "alert_count": self.alert_count,
                "uptime_seconds": time.time()
                - getattr(self, "start_time", time.time()),
            },
            "thresholds": {
                "config_load_threshold_ms": self.config_load_threshold,
                "provider_switch_threshold_ms": self.provider_switch_threshold,
            },
        }

    async def run_monitoring_loop(self) -> None:
        """Run the main monitoring loop."""
        self.start_time = time.time()
        logger.info("F4 Configuration Monitor started")

        try:
            while self.monitoring_active:
                try:
                    # Run health check
                    health_result = await self.run_health_check()

                    # Save report
                    await self.save_health_report(health_result)

                    # Send alerts if any
                    alerts = health_result.get("alerts", [])
                    if alerts:
                        await self.send_alerts(alerts)

                    # Log summary
                    logger.info(
                        f"Health check completed - Status: {health_result['overall_status']}, Alerts: {len(alerts)}"
                    )

                except Exception as e:
                    logger.error(f"Monitoring loop error: {e}")
                    self.error_count += 1

                # Wait for next check
                if self.monitoring_active:
                    await asyncio.sleep(self.monitor_interval)

        except KeyboardInterrupt:
            logger.info("Monitor interrupted by user")
        except Exception as e:
            logger.error(f"Monitoring loop failed: {e}")
        finally:
            self.monitoring_active = False
            logger.info("F4 Configuration Monitor stopped")

    def stop_monitoring(self) -> None:
        """Stop the monitoring loop."""
        logger.info("Stopping F4 Configuration Monitor...")
        self.monitoring_active = False


async def main():
    """Main entry point for the F4 configuration monitor."""
    monitor = F4ConfigurationMonitor()

    try:
        await monitor.run_monitoring_loop()
    except KeyboardInterrupt:
        logger.info("Monitor interrupted")
    except Exception as e:
        logger.error(f"Monitor failed: {e}")
        sys.exit(1)
    finally:
        monitor.stop_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
