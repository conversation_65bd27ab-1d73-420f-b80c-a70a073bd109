# Production Deployment Module

Enterprise-grade deployment infrastructure with Docker composition, automated setup, comprehensive monitoring, and production-ready security for the multi-user Sora video generation platform.

## Architecture Overview

**Production-Ready Multi-User Infrastructure**: Scalable container orchestration with load balancing, background processing, comprehensive monitoring, and security hardening designed for 15+ concurrent users.

**Infrastructure as Code**: All deployment configurations managed via version control with automated setup, monitoring, and maintenance procedures.

**High Availability Design**: Multi-service architecture with redundancy, failover mechanisms, and horizontal scaling support for enterprise deployment.

```
src/deployment/
├── docker/
│   ├── docker-compose.yml                    # Development/staging deployment
│   ├── docker-compose.simple.yml             # Simple 5-container deployment
│   ├── docker-compose.production.yml         # Production deployment
│   ├── Dockerfile                            # Flask application container
│   ├── Dockerfile.worker                     # Celery worker container
│   ├── entrypoint.sh                         # ✅ NEW: Automatic database initialization
│   └── nginx/                                # Nginx configuration files
└── scripts/
    ├── setup.sh                              # Production setup automation
    └── health_check.sh                       # Comprehensive health monitoring
```

## Automatic Database Initialization

**✅ PRODUCTION-READY**: All Docker deployments now include automatic database initialization via `entrypoint.sh` script.

### Key Features
- **Zero-Configuration Setup**: Database tables created automatically on first run
- **Migration Safety**: Runs `flask db upgrade` before application startup
- **Connection Verification**: Waits for PostgreSQL to be ready before proceeding
- **Production Ready**: Works across all deployment configurations (simple, development, production)

### How It Works
```bash
# Container startup sequence:
1. 🚀 Container starts with entrypoint.sh
2. ⏳ Waits for database connection (10 second delay + verification)
3. 🔄 Runs Flask database migrations automatically
4. ✅ Starts application with gunicorn
5. 🎉 Ready for use - no manual setup required!
```

### Benefits for Team Collaboration
- **New developers**: Clone repo → `docker-compose up` → works immediately
- **Fresh deployments**: No manual database setup steps required
- **Production**: Reliable database initialization in production environments
- **CI/CD**: Automated deployments without manual intervention

## Docker Composition Architecture

### Development/Staging Configuration (`docker-compose.yml`)

**Multi-Service Architecture** with 16 containerized services providing complete development and staging environment.

```yaml
# Core Application Services
- nginx-lb: Load balancer with 3 Flask app instances
- sora-app-1/2/3: Flask application instances
- celery-worker-1/2/3/4: Background processing workers
- celery-scheduler: Periodic task scheduling
- flower: Celery monitoring interface

# Data Storage Services
- postgres: Primary database with persistent storage
- redis: Cache, session storage, and message broker
- redis-sentinel: High availability and failover

# Monitoring Stack
- prometheus: Time-series metrics collection
- grafana: Visualization dashboards
- elasticsearch: Log aggregation and search
- logstash: Log processing and parsing
- kibana: Log analysis interface

# Network Configuration
- sora-network: Custom bridge network for service isolation
```

**Key Features**:
- **Load Balancing**: Nginx reverse proxy with upstream health checks
- **Background Processing**: 4 Celery workers for parallel video generation
- **Session Management**: Redis-based session storage with persistence
- **Monitoring**: Full observability stack with metrics, logs, and dashboards
- **Development Ready**: Hot reloading, debug ports, and development tools

### Production Configuration (`docker-compose.production.yml`)

**Production-Hardened** deployment with enhanced security, performance, and high availability features.

```yaml
# Production Enhancements
- Enhanced resource constraints and limits
- Docker Swarm deployment specifications
- Redis Sentinel for automatic failover
- PostgreSQL master/replica replication
- SSL termination with production certificates
- Overlay networking for multi-host deployment
- Persistent storage with bind mounts to /opt/sora/
- Production logging and monitoring configuration
```

**Production Features**:
- **High Availability**: Redis Sentinel with automatic failover
- **Database Replication**: PostgreSQL master/replica setup
- **SSL Termination**: Production-grade SSL configuration
- **Resource Management**: CPU and memory constraints for stability
- **Security Hardening**: Non-root execution, network isolation
- **Monitoring**: Enhanced monitoring with persistent storage

## Production Setup Automation (`setup.sh`)

**Comprehensive 816-line production setup script** providing fully automated deployment preparation and system configuration.

### System Requirements Validation

```bash
# Operating System Support
- Ubuntu 20.04/22.04 LTS
- CentOS 7/8, RHEL 7/8
- Debian 10/11

# Hardware Requirements
- Minimum 16GB RAM (32GB recommended)
- 100GB+ available disk space
- 4+ CPU cores
- Network connectivity for external APIs

# Software Dependencies
- Docker Engine 20.10+
- Docker Compose 2.0+
- System packages (curl, openssl, certbot)
```

### Security Hardening

```bash
# Firewall Configuration
ufw --force enable
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8080/tcp  # Monitoring (restricted)

# Intrusion Prevention
fail2ban-client start
fail2ban-client add sshd
fail2ban-client add nginx-http-auth

# SSL Certificate Management
certbot certonly --nginx \
  --domains $DOMAIN_NAME \
  --email $SSL_EMAIL \
  --agree-tos --non-interactive
```

### Directory Structure Creation

```bash
# Production Directory Layout
/opt/sora/
├── data/
│   ├── postgres/          # Database storage
│   ├── redis/             # Redis persistence
│   ├── uploads/           # Video file storage
│   └── logs/              # Application logs
├── config/
│   ├── nginx/             # Nginx configuration
│   ├── ssl/               # SSL certificates
│   ├── monitoring/        # Prometheus, Grafana configs
│   └── env/               # Environment configurations
├── backups/
│   ├── daily/             # Daily backups
│   ├── weekly/            # Weekly backups
│   └── monthly/           # Monthly backups
└── scripts/
    ├── backup.sh          # Automated backup scripts
    ├── restore.sh         # Disaster recovery scripts
    └── maintenance.sh     # Routine maintenance
```

### Environment Configuration

```bash
# Secure Configuration Generation
generate_random_key() {
    openssl rand -base64 32
}

# Environment Variables
FLASK_SECRET_KEY=$(generate_random_key)
POSTGRES_PASSWORD=$(generate_random_key)
REDIS_PASSWORD=$(generate_random_key)
CELERY_SECRET=$(generate_random_key)

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY="$AZURE_API_KEY"
AZURE_OPENAI_ENDPOINT="$AZURE_ENDPOINT"
AZURE_OPENAI_API_VERSION="2024-02-15-preview"
```

### Database Initialization

```bash
# PostgreSQL Setup
docker-compose exec postgres createdb sora_production
docker-compose exec sora-app-1 flask db upgrade

# Redis Configuration
docker-compose exec redis redis-cli CONFIG SET requirepass "$REDIS_PASSWORD"
docker-compose exec redis redis-cli CONFIG SET maxmemory 4gb
docker-compose exec redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### Monitoring Setup

```bash
# Prometheus Configuration
configure_prometheus() {
    cat > /opt/sora/config/monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'sora-app'
    static_configs:
      - targets: ['sora-app-1:5000', 'sora-app-2:5000', 'sora-app-3:5000']
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-lb:9113']
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
EOF
}

# Grafana Dashboard Import
import_grafana_dashboards() {
    curl -X POST \
      *********************************/api/dashboards/db \
      -H "Content-Type: application/json" \
      -d @/opt/sora/config/monitoring/sora-dashboard.json
}
```

## Health Monitoring System (`health_check.sh`)

**Comprehensive 672-line health monitoring script** providing multi-layer system validation and alerting.

### Multi-Layer Health Checks

#### Docker Services Health
```bash
check_docker_services() {
    local services=(
        "nginx-lb" "sora-app-1" "sora-app-2" "sora-app-3"
        "celery-worker-1" "celery-worker-2" "celery-worker-3" "celery-worker-4"
        "celery-scheduler" "flower" "postgres" "redis" "redis-sentinel"
        "prometheus" "grafana" "elasticsearch" "logstash" "kibana"
    )
    
    for service in "${services[@]}"; do
        local health_status=$(docker-compose ps --services --filter "health=healthy" | grep -c "$service")
        if [[ $health_status -eq 0 ]]; then
            log_error "Service $service is unhealthy"
            return 1
        fi
    done
    return 0
}
```

#### System Resources Monitoring
```bash
check_system_resources() {
    # CPU Usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2+$4}' | cut -d'%' -f1)
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        log_warning "High CPU usage: ${cpu_usage}%"
    fi
    
    # Memory Usage
    local memory_usage=$(free | grep '^Mem' | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$memory_usage > 85" | bc -l) )); then
        log_warning "High memory usage: ${memory_usage}%"
    fi
    
    # Disk Usage
    local disk_usage=$(df -h /opt/sora | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    if [[ $disk_usage -gt 80 ]]; then
        log_warning "High disk usage: ${disk_usage}%"
    fi
    
    # Load Average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | cut -d, -f1 | xargs)
    local cpu_cores=$(nproc)
    if (( $(echo "$load_avg > $cpu_cores" | bc -l) )); then
        log_warning "High load average: $load_avg (cores: $cpu_cores)"
    fi
}
```

#### Application Endpoints Health
```bash
check_application_endpoints() {
    local endpoints=(
        "http://localhost/health:200"
        "http://localhost/health/database:200"
        "http://localhost/health/azure:200"
        "http://localhost/metrics:200"
        "http://localhost/queue/stats:200"
    )
    
    for endpoint_spec in "${endpoints[@]}"; do
        local endpoint="${endpoint_spec%:*}"
        local expected_status="${endpoint_spec#*:}"
        
        local response=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint" --max-time 10)
        if [[ "$response" != "$expected_status" ]]; then
            log_error "Endpoint $endpoint returned $response, expected $expected_status"
            return 1
        fi
        
        # Response time check
        local response_time=$(curl -s -o /dev/null -w "%{time_total}" "$endpoint" --max-time 10)
        if (( $(echo "$response_time > 2.0" | bc -l) )); then
            log_warning "Slow response from $endpoint: ${response_time}s"
        fi
    done
    return 0
}
```

#### Database and Cache Health
```bash
check_database_health() {
    # PostgreSQL connectivity
    local pg_status=$(docker-compose exec -T postgres pg_isready -U postgres)
    if [[ $? -ne 0 ]]; then
        log_error "PostgreSQL is not ready: $pg_status"
        return 1
    fi
    
    # Database connection count
    local connections=$(docker-compose exec -T postgres psql -U postgres -t -c "SELECT count(*) FROM pg_stat_activity;")
    if [[ $connections -gt 50 ]]; then
        log_warning "High database connections: $connections"
    fi
    
    # Redis connectivity
    local redis_status=$(docker-compose exec -T redis redis-cli ping)
    if [[ "$redis_status" != "PONG" ]]; then
        log_error "Redis is not responding: $redis_status"
        return 1
    fi
    
    # Redis memory usage
    local redis_memory=$(docker-compose exec -T redis redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r\n')
    log_info "Redis memory usage: $redis_memory"
}
```

#### Queue System Monitoring
```bash
check_queue_system() {
    # Celery worker status
    local worker_count=$(docker-compose exec -T celery-worker-1 celery -A src.job_queue.celery_app inspect active | grep -c "celery@")
    if [[ $worker_count -lt 4 ]]; then
        log_warning "Only $worker_count Celery workers active (expected 4)"
    fi
    
    # Queue depth monitoring
    local queue_depth=$(docker-compose exec -T redis redis-cli llen celery)
    if [[ $queue_depth -gt 100 ]]; then
        log_warning "High queue depth: $queue_depth jobs"
    fi
    
    # Failed job rate
    local failed_jobs=$(docker-compose exec -T celery-worker-1 celery -A src.job_queue.celery_app inspect stats | grep -c "failure")
    if [[ $failed_jobs -gt 10 ]]; then
        log_warning "High failed job count: $failed_jobs"
    fi
}
```

### Alert System

#### Tiered Alerting
```bash
# Alert severity levels
ALERT_HEALTHY=0
ALERT_WARNING=1
ALERT_UNHEALTHY=2
ALERT_CRITICAL=3

# Alert thresholds
CPU_WARNING_THRESHOLD=80
CPU_CRITICAL_THRESHOLD=90
MEMORY_WARNING_THRESHOLD=85
MEMORY_CRITICAL_THRESHOLD=95
DISK_WARNING_THRESHOLD=80
DISK_CRITICAL_THRESHOLD=90
```

#### Notification Channels
```bash
send_slack_alert() {
    local message="$1"
    local severity="$2"
    local color="warning"
    
    case $severity in
        $ALERT_CRITICAL) color="danger" ;;
        $ALERT_UNHEALTHY) color="warning" ;;
        $ALERT_WARNING) color="warning" ;;
        $ALERT_HEALTHY) color="good" ;;
    esac
    
    curl -X POST "$SLACK_WEBHOOK_URL" \
        -H 'Content-type: application/json' \
        --data "{
            \"attachments\": [{
                \"color\": \"$color\",
                \"title\": \"Sora Health Alert\",
                \"text\": \"$message\",
                \"footer\": \"$(hostname)\",
                \"ts\": \"$(date +%s)\"
            }]
        }"
}

send_email_alert() {
    local subject="$1"
    local message="$2"
    local severity="$3"
    
    echo "$message" | mail -s "[SORA-${severity}] $subject" "$ALERT_EMAIL"
}
```

#### JSON Report Generation
```bash
generate_health_report() {
    cat << EOF > "/opt/sora/logs/health-report-$(date +%Y%m%d-%H%M%S).json"
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "hostname": "$(hostname)",
    "overall_status": "$OVERALL_STATUS",
    "services": {
        "nginx": "$NGINX_STATUS",
        "app_instances": "$APP_STATUS",
        "celery_workers": "$CELERY_STATUS",
        "database": "$DATABASE_STATUS",
        "cache": "$CACHE_STATUS",
        "monitoring": "$MONITORING_STATUS"
    },
    "resources": {
        "cpu_usage": "$CPU_USAGE",
        "memory_usage": "$MEMORY_USAGE",
        "disk_usage": "$DISK_USAGE",
        "load_average": "$LOAD_AVERAGE"
    },
    "queue_metrics": {
        "queue_depth": "$QUEUE_DEPTH",
        "active_workers": "$ACTIVE_WORKERS",
        "failed_jobs": "$FAILED_JOBS"
    },
    "ssl_certificates": {
        "expiry_date": "$SSL_EXPIRY",
        "days_until_expiry": "$SSL_DAYS_LEFT"
    }
}
EOF
}
```

## Deployment Patterns

### Production-Ready Multi-User Infrastructure

#### Load Balancing Configuration
```nginx
upstream sora_app {
    least_conn;
    server sora-app-1:5000 max_fails=3 fail_timeout=30s;
    server sora-app-2:5000 max_fails=3 fail_timeout=30s;
    server sora-app-3:5000 max_fails=3 fail_timeout=30s;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN_NAME/privkey.pem;
    
    location / {
        proxy_pass http://sora_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Health check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_connect_timeout 5s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

#### High Availability Database
```yaml
# PostgreSQL Master-Replica Configuration
postgres-master:
  image: postgres:15
  environment:
    POSTGRES_REPLICATION_MODE: master
    POSTGRES_REPLICATION_USER: replicator
    POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD}
  volumes:
    - postgres_master_data:/var/lib/postgresql/data

postgres-replica:
  image: postgres:15
  environment:
    POSTGRES_REPLICATION_MODE: slave
    POSTGRES_MASTER_HOST: postgres-master
    POSTGRES_REPLICATION_USER: replicator
    POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD}
  volumes:
    - postgres_replica_data:/var/lib/postgresql/data
```

#### Redis High Availability
```yaml
redis-master:
  image: redis:7-alpine
  command: redis-server --requirepass ${REDIS_PASSWORD}
  volumes:
    - redis_master_data:/data

redis-sentinel:
  image: redis:7-alpine
  command: redis-sentinel /usr/local/etc/redis/sentinel.conf
  depends_on:
    - redis-master
  volumes:
    - ./config/redis/sentinel.conf:/usr/local/etc/redis/sentinel.conf
```

### Comprehensive Monitoring Stack

#### Prometheus Configuration
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "sora_alerts.yml"

scrape_configs:
  - job_name: 'sora-app'
    static_configs:
      - targets: ['sora-app-1:5000', 'sora-app-2:5000', 'sora-app-3:5000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  - job_name: 'celery'
    static_configs:
      - targets: ['flower:5555']
    metrics_path: '/metrics'
    
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
```

#### Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "Sora Video Generation Platform",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(flask_http_requests_total[5m])",
            "legendFormat": "{{instance}} - {{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Queue Depth",
        "type": "graph",
        "targets": [
          {
            "expr": "celery_queue_length",
            "legendFormat": "{{queue}}"
          }
        ]
      },
      {
        "title": "Video Generation Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(video_generation_success_total[5m]) / rate(video_generation_total[5m]) * 100",
            "legendFormat": "Success Rate %"
          }
        ]
      }
    ]
  }
}
```

## Security and Compliance

### Security Hardening Checklist

```bash
# System Security
- [x] UFW firewall configured with minimal port exposure
- [x] Fail2ban configured for SSH and web services
- [x] SSL/TLS certificates with automatic renewal
- [x] Non-root container execution
- [x] Network segmentation with custom Docker networks
- [x] Regular security updates via automated scripts

# Application Security
- [x] Environment variable secret management
- [x] Database connection encryption
- [x] Redis password authentication
- [x] Nginx security headers configuration
- [x] Rate limiting at proxy level
- [x] Input validation and sanitization

# Monitoring Security
- [x] Prometheus authentication
- [x] Grafana admin password changes
- [x] ELK stack access controls
- [x] Audit logging for all operations
- [x] Log retention and rotation policies
```

### Compliance Features

#### Audit Logging
```bash
# System audit logging
auditctl -w /opt/sora/config -p wa -k sora_config_changes
auditctl -w /opt/sora/data -p wa -k sora_data_access
auditctl -w /opt/sora/scripts -p x -k sora_script_execution

# Application audit logging
log_audit_event() {
    local event="$1"
    local user="$2"
    local details="$3"
    
    echo "$(date -u +%Y-%m-%dT%H:%M:%SZ) AUDIT: $event by $user - $details" \
        >> /opt/sora/logs/audit.log
}
```

#### Backup and Retention
```bash
# Automated backup schedule
backup_database() {
    local backup_date=$(date +%Y%m%d_%H%M%S)
    local backup_file="/opt/sora/backups/daily/postgres_${backup_date}.sql.gz"
    
    docker-compose exec -T postgres pg_dump -U postgres sora_production \
        | gzip > "$backup_file"
    
    log_audit_event "DATABASE_BACKUP" "system" "Created backup: $backup_file"
}

# Retention policy
cleanup_old_backups() {
    find /opt/sora/backups/daily -name "*.sql.gz" -mtime +7 -delete
    find /opt/sora/backups/weekly -name "*.sql.gz" -mtime +30 -delete
    find /opt/sora/backups/monthly -name "*.sql.gz" -mtime +365 -delete
}
```

## Usage Examples

### ✅ Simple Development Setup (Recommended)

**For quick development and testing - Zero configuration required!**

```bash
# 1. Clone repository
git clone <repository-url>
cd sora-poc

# 2. Start simple deployment (5 containers)
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# 3. That's it! 🎉
# - Database tables created automatically
# - Application ready at http://localhost
# - Video generation working immediately
```

### Production Deployment

```bash
# 1. Initial setup
cd /opt/sora
./scripts/setup.sh --environment production --domain your-domain.com

# 2. Deploy services (with automatic database initialization)
docker-compose -f docker/docker-compose.production.yml up -d

# 3. Verify deployment
./scripts/health_check.sh --verbose --report

# 4. Monitor services
docker-compose -f docker/docker-compose.production.yml logs -f
```

### Development Environment (Full Stack)

```bash
# 1. Start development stack (16 containers with monitoring)
docker-compose -f src/deployment/docker/docker-compose.yml up -d

# 2. Monitor services
docker-compose logs -f sora-app-1 celery-worker-1

# 3. Access monitoring
# Grafana: http://localhost:3000 (admin/admin)
# Flower: http://localhost:5555
# Kibana: http://localhost:5601
```

### Health Monitoring

```bash
# Basic health check
./scripts/health_check.sh

# Detailed health check with JSON report
./scripts/health_check.sh --detailed --json --report

# Continuous monitoring
watch -n 30 './scripts/health_check.sh --summary'

# Alert testing
./scripts/health_check.sh --test-alerts
```

## Performance Optimization

### Resource Management

```yaml
# Docker service resource limits
services:
  sora-app-1:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
          
  celery-worker-1:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### Caching Strategy

```bash
# Nginx caching configuration
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Redis caching configuration
redis-cli CONFIG SET maxmemory 8gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

## Dependencies

### System Requirements
- **Docker Engine**: 20.10+ with BuildKit support
- **Docker Compose**: 2.0+ with version 3.8 support
- **Operating System**: Ubuntu 20.04+, CentOS 7+, or compatible
- **Hardware**: 16GB+ RAM, 4+ CPU cores, 100GB+ storage

### External Services
- **Let's Encrypt**: SSL certificate automation
- **Azure OpenAI**: Video generation API
- **SMTP Server**: Email notifications (optional)
- **Slack**: Alert notifications (optional)

## Development Guidelines

### Deployment Best Practices
- **Immutable Infrastructure**: Use Docker images for reproducible deployments
- **Environment Parity**: Keep development, staging, and production environments identical
- **Configuration Management**: Use environment variables for all configuration
- **Monitoring First**: Deploy monitoring before application services
- **Security by Design**: Apply security hardening from the start

### Maintenance Procedures
- **Regular Updates**: Keep all services updated with security patches
- **Health Monitoring**: Run health checks every 5-10 minutes
- **Backup Verification**: Test backup restore procedures monthly
- **Performance Monitoring**: Review metrics and optimize resource usage
- **Log Management**: Rotate logs and monitor disk usage

### Emergency Procedures
- **Service Recovery**: Automated restart policies for failed services
- **Database Failover**: Redis Sentinel for automatic failover
- **Disaster Recovery**: Automated backup and restore procedures
- **Communication**: Slack integration for real-time alerts
- **Escalation**: Multi-tier alerting with appropriate contacts