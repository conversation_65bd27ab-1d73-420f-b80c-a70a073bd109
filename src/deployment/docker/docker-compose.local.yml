# Docker Compose override for local development
# This file allows running Docker containers locally with localhost networking

version: '3.8'

services:
  redis:
    ports:
      - "6379:6379"
    networks:
      - sora-network

  postgres:
    ports:
      - "5432:5432"
    networks:
      - sora-network

  web:
    env_file:
      - .env.local
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DATABASE_URL=**************************************************************/sora_production
    depends_on:
      - redis
      - postgres
    networks:
      - sora-network

  worker:
    env_file:
      - .env.local
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DATABASE_URL=**************************************************************/sora_production
    depends_on:
      - redis
      - postgres
    networks:
      - sora-network

networks:
  sora-network:
    driver: bridge