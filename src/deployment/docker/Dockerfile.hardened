# Security-Hardened Multi-stage Dockerfile for C1-Image Upload Security Pipeline
# Implements defense-in-depth with minimal attack surface

ARG PYTHON_VERSION=3.11
ARG SECURITY_LEVEL=standard

# Stage 1: Security-focused build environment
FROM python:${PYTHON_VERSION}-slim as security-builder

# Build arguments
ARG SECURITY_LEVEL

# Install build dependencies with security focus
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    gcc \
    g++ \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Create build user (non-root)
RUN groupadd -r builduser && useradd -r -g builduser builduser

# Set working directory
WORKDIR /build

# Install uv package manager
RUN pip install --no-cache-dir uv

# Copy dependency files
COPY --chown=builduser:builduser pyproject.toml uv.lock ./

# Switch to build user
USER builduser

# Install dependencies in isolated environment
RUN uv sync --frozen

# Stage 2: Security-hardened runtime environment
FROM python:${PYTHON_VERSION}-slim as security-runtime

# Build arguments
ARG SECURITY_LEVEL

# Labels for security tracking
LABEL maintainer="<EMAIL>" \
      version="1.0" \
      security.level="${SECURITY_LEVEL}" \
      security.scanner="enabled" \
      security.readonly="true" \
      security.user="sora"

# Security-focused system setup
RUN set -eux; \
    apt-get update; \
    apt-get install -y --no-install-recommends \
        # Runtime dependencies
        libpq5 \
        curl \
        ca-certificates \
        # FFmpeg for video processing (minimal)
        ffmpeg \
        # Security tools
        dumb-init \
    ; \
    # Security cleanup
    apt-get autoremove -y; \
    apt-get clean; \
    rm -rf /var/lib/apt/lists/*; \
    rm -rf /tmp/*; \
    rm -rf /var/tmp/*; \
    rm -rf /root/.cache; \
    # Remove package manager caches
    find /var/cache -type f -delete 2>/dev/null || true; \
    find /var/log -type f -delete 2>/dev/null || true

# Create security-hardened user and groups
RUN set -eux; \
    # Create sora group with specific GID
    groupadd -g 1001 sora; \
    # Create sora user with specific UID, no shell, no home
    useradd -u 1001 -g sora -s /sbin/nologin -M sora; \
    # Create app group for additional security
    groupadd -g 1002 app; \
    # Add sora user to app group
    usermod -a -G app sora

# Set secure working directory
WORKDIR /app

# Copy Python environment from builder
COPY --from=security-builder --chown=root:root /build/.venv /app/.venv

# Copy application code with secure permissions
COPY --chown=root:app . .

# Security-specific configuration based on security level
RUN set -eux; \
    if [ "$SECURITY_LEVEL" = "strict" ] || [ "$SECURITY_LEVEL" = "paranoid" ]; then \
        # Strict security: remove potential attack vectors
        find /app -name "*.pyc" -delete; \
        find /app -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true; \
        find /app -name "*.pyo" -delete 2>/dev/null || true; \
        # Remove documentation and examples
        find /app/.venv -name "docs" -type d -exec rm -rf {} + 2>/dev/null || true; \
        find /app/.venv -name "examples" -type d -exec rm -rf {} + 2>/dev/null || true; \
        find /app/.venv -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true; \
    fi

# Create secure directory structure with minimal permissions
RUN set -eux; \
    # Application directories (sora:app with specific permissions)
    mkdir -p /app/uploads /app/logs /app/temp /app/static; \
    chown sora:app /app/uploads /app/logs /app/temp; \
    chmod 750 /app/uploads /app/logs /app/temp; \
    chown root:app /app/static; \
    chmod 755 /app/static; \
    # Configuration directories (root:app, read-only for app)
    mkdir -p /app/config /app/security; \
    chown root:app /app/config /app/security; \
    chmod 750 /app/config /app/security; \
    # Secure Python environment permissions
    chown -R root:app /app/.venv; \
    chmod -R 755 /app/.venv; \
    # Make source code read-only
    find /app/src -type f -exec chmod 644 {} \;; \
    find /app/src -type d -exec chmod 755 {} \;; \
    chown -R root:app /app/src; \
    # Secure entrypoint
    chmod 755 /app/src/deployment/docker/entrypoint-hardened.sh; \
    chown root:root /app/src/deployment/docker/entrypoint-hardened.sh

# Copy and secure environment configuration
COPY --chown=root:app src/deployment/security/configs/.env.security /app/.env
RUN chmod 640 /app/.env

# Environment variables for security
ENV PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH="/app" \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    # Security environment variables
    SECURITY_HEADERS_ENABLED=true \
    RATE_LIMITING_ENABLED=true \
    INPUT_VALIDATION_STRICT=true \
    AUDIT_LOGGING_ENABLED=true \
    DEBUG=false \
    FLASK_ENV=production \
    # Image security pipeline
    IMAGE_SECURITY_ENABLED=true \
    MALWARE_SCANNING_ENABLED=true \
    CONTENT_FILTERING_ENABLED=true \
    THREAT_DETECTION_ENABLED=true \
    # Container security
    CONTAINER_READONLY=true \
    SECURITY_LEVEL=${SECURITY_LEVEL}

# Switch to non-root user for security
USER sora:app

# Expose port (non-privileged)
EXPOSE 5001

# Security-focused health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health/security || exit 1

# Use dumb-init for proper signal handling and security
ENTRYPOINT ["dumb-init", "--"]

# Run with hardened entrypoint
CMD ["/app/src/deployment/docker/entrypoint-hardened.sh"]