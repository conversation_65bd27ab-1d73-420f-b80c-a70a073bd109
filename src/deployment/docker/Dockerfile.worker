# Celery worker container optimized for video generation tasks
# Single-stage build with security hardening

FROM python:3.11-slim as production

# Create non-root user
RUN groupadd -r celery && useradd -r -g celery celery

# Install runtime dependencies including FFmpeg for video processing
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    gcc \
    g++ \
    libpq-dev \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install uv
RUN pip install uv

# Copy dependency files
COPY pyproject.toml ./
COPY uv.lock ./

# Install dependencies and create fresh virtual environment in container
RUN uv sync --frozen

# Copy application
COPY . .

# Copy environment file for Docker deployment
COPY src/deployment/docker/.env /app/.env

# Create directories and set permissions
RUN mkdir -p /app/uploads /app/logs && \
    chown -R celery:celery /app

# Set environment to use UV's virtual environment
ENV PATH="/app/.venv/bin:$PATH"

# Switch to non-root user
USER celery

# Health check for worker
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=3 \
    CMD /app/.venv/bin/python -m celery -A src.job_queue.celery_app inspect ping || exit 1

# Run Celery worker with optimized settings for video generation
CMD ["/app/.venv/bin/python", "-m", "celery", "-A", "src.job_queue.celery_app", "worker", "--loglevel=info", "--concurrency=2", "--max-tasks-per-child=100", "--prefetch-multiplier=1"]