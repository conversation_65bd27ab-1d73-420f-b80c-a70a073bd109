# F4 Environment Configuration - Google Veo3 Integration Override
# This Docker Compose override enhances the base deployment with F4 configuration capabilities

version: '3.8'

services:
  # Enhanced Application Configuration for F4
  app:
    environment:
      # F4 Environment Configuration Variables
      USE_MOCK_VEO: ${USE_MOCK_VEO:-true}
      DEFAULT_PROVIDER: ${DEFAULT_PROVIDER:-azure_sora}
      
      # Google Cloud Configuration
      GOOGLE_PROJECT_ID: ${GOOGLE_PROJECT_ID:-}
      GOOGLE_LOCATION: ${GOOGLE_LOCATION:-us-central1}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET:-}
      
      # Google Veo3 Model Configuration
      VEO3_MODEL_VERSION: ${VEO3_MODEL_VERSION:-veo-3.0-generate-preview}
      VEO3_TIMEOUT: ${VEO3_TIMEOUT:-300}
      VEO3_MAX_RETRIES: ${VEO3_MAX_RETRIES:-3}
      VEO3_RETRY_DELAY: ${VEO3_RETRY_DELAY:-2}
      VEO3_GENERATION_TIMEOUT: ${VEO3_GENERATION_TIMEOUT:-1800}
      VEO3_RATE_LIMIT_RPM: ${VEO3_RATE_LIMIT_RPM:-30}
      
      # F4 Configuration Service Settings
      CONFIG_VALIDATION_ENABLED: ${CONFIG_VALIDATION_ENABLED:-true}
      CONFIG_CACHE_TTL: ${CONFIG_CACHE_TTL:-300}
      CONFIG_AUTO_RELOAD: ${CONFIG_AUTO_RELOAD:-false}
      
      # Deployment Type Detection
      DEPLOYMENT_TYPE: docker
      F4_DEPLOYMENT_ENVIRONMENT: ${F4_DEPLOYMENT_ENVIRONMENT:-production}
    
    # Google Cloud Service Account Secret Mounting
    secrets:
      - google_service_account
    
    # Health Check Enhancement for F4
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health/config"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    
    # Additional Volume for Configuration
    volumes:
      - video_storage:/app/uploads
      - static_files:/app/static
      - f4_config_cache:/app/config_cache

  # Enhanced Worker Configuration for F4
  worker:
    environment:
      # F4 Environment Configuration Variables (same as app)
      USE_MOCK_VEO: ${USE_MOCK_VEO:-true}
      DEFAULT_PROVIDER: ${DEFAULT_PROVIDER:-azure_sora}
      
      # Google Cloud Configuration
      GOOGLE_PROJECT_ID: ${GOOGLE_PROJECT_ID:-}
      GOOGLE_LOCATION: ${GOOGLE_LOCATION:-us-central1}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET:-}
      
      # Google Veo3 Model Configuration
      VEO3_MODEL_VERSION: ${VEO3_MODEL_VERSION:-veo-3.0-generate-preview}
      VEO3_TIMEOUT: ${VEO3_TIMEOUT:-300}
      VEO3_MAX_RETRIES: ${VEO3_MAX_RETRIES:-3}
      VEO3_RETRY_DELAY: ${VEO3_RETRY_DELAY:-2}
      VEO3_GENERATION_TIMEOUT: ${VEO3_GENERATION_TIMEOUT:-1800}
      VEO3_RATE_LIMIT_RPM: ${VEO3_RATE_LIMIT_RPM:-30}
      
      # F4 Configuration Service Settings
      CONFIG_VALIDATION_ENABLED: ${CONFIG_VALIDATION_ENABLED:-true}
      CONFIG_CACHE_TTL: ${CONFIG_CACHE_TTL:-300}
      CONFIG_AUTO_RELOAD: ${CONFIG_AUTO_RELOAD:-false}
      
      # Deployment Type Detection
      DEPLOYMENT_TYPE: docker
      F4_DEPLOYMENT_ENVIRONMENT: ${F4_DEPLOYMENT_ENVIRONMENT:-production}
      
      # Worker-specific F4 settings
      WORKER_CONFIG_VALIDATION: ${WORKER_CONFIG_VALIDATION:-true}
      WORKER_PROVIDER_SWITCHING: ${WORKER_PROVIDER_SWITCHING:-enabled}
    
    # Google Cloud Service Account Secret Mounting
    secrets:
      - google_service_account
    
    # Enhanced Health Check for Workers
    healthcheck:
      test: ["CMD", "python", "-c", "from src.config.factory import ConfigurationFactory; print('Config OK' if ConfigurationFactory.get_provider_availability() else exit(1))"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    
    # Additional Volume for Configuration Cache
    volumes:
      - video_storage:/app/uploads
      - f4_config_cache:/app/config_cache

  # F4 Configuration Health Monitor (New Service)
  config-monitor:
    build:
      context: ../../..
      dockerfile: src/deployment/docker/Dockerfile
    container_name: sora-config-monitor-f4
    environment:
      # Monitoring Configuration
      MONITOR_INTERVAL: ${CONFIG_MONITOR_INTERVAL:-60}
      ALERT_THRESHOLD_CONFIG_LOAD_TIME: ${ALERT_THRESHOLD_CONFIG_LOAD_TIME:-5000}
      ALERT_THRESHOLD_PROVIDER_SWITCH_TIME: ${ALERT_THRESHOLD_PROVIDER_SWITCH_TIME:-10000}
      
      # F4 Configuration Variables (for monitoring)
      USE_MOCK_VEO: ${USE_MOCK_VEO:-true}
      DEFAULT_PROVIDER: ${DEFAULT_PROVIDER:-azure_sora}
      GOOGLE_PROJECT_ID: ${GOOGLE_PROJECT_ID:-}
      
      # Database and Cache Access
      DATABASE_URL: postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production
      CELERY_BROKER_URL: redis://redis:6379/0
      
      # Deployment Environment
      DEPLOYMENT_TYPE: docker
      F4_DEPLOYMENT_ENVIRONMENT: ${F4_DEPLOYMENT_ENVIRONMENT:-production}
    
    command: ["python", "-m", "src.deployment.monitors.f4_config_monitor"]
    
    networks:
      - sora-network
    
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 120s
      timeout: 30s
      retries: 2
      start_period: 180s
    
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.1'

# F4 Configuration Secrets
secrets:
  google_service_account:
    # Google Cloud Service Account JSON file
    # Mount path: /run/secrets/google_service_account
    file: ${GOOGLE_SERVICE_ACCOUNT_FILE:-/opt/sora/secrets/google-service-account.json}

# F4 Configuration Volumes
volumes:
  f4_config_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${F4_CONFIG_CACHE_PATH:-/opt/sora/data/f4_config_cache}

# Enhanced network configuration (optional)
networks:
  sora-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16