#!/bin/bash
# Security-Hardened Entrypoint for C1-Image Upload Security Pipeline
# Implements comprehensive security validation and monitoring

set -euo pipefail
IFS=$'\n\t'

readonly SCRIPT_NAME="$(basename "$0")"
readonly SECURITY_LEVEL="${SECURITY_LEVEL:-standard}"
readonly LOG_LEVEL="${LOG_LEVEL:-INFO}"

# Security logging function
log_security() {
    local level="$1"
    local message="$2"
    local timestamp
    timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    echo "[$timestamp] [SECURITY-$level] [$SCRIPT_NAME] $message" >&2
    
    # Log to security events if directory exists
    if [[ -d "/app/security-events" ]]; then
        echo "{\"timestamp\":\"$timestamp\",\"level\":\"$level\",\"component\":\"entrypoint\",\"message\":\"$message\",\"security_level\":\"$SECURITY_LEVEL\"}" >> "/app/security-events/entrypoint.log"
    fi
}

# Security validation functions
validate_environment() {
    log_security "INFO" "Starting environment security validation"
    
    # Check required security environment variables
    local required_vars=(
        "DATABASE_URL"
        "SECRET_KEY"
        "SECURITY_LEVEL"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_security "CRITICAL" "Required security environment variable $var is not set"
            exit 1
        fi
    done
    
    # Validate security level
    case "$SECURITY_LEVEL" in
        basic|standard|strict|paranoid)
            log_security "INFO" "Security level validated: $SECURITY_LEVEL"
            ;;
        *)
            log_security "ERROR" "Invalid security level: $SECURITY_LEVEL"
            exit 1
            ;;
    esac
    
    # Check for insecure configurations
    if [[ "${DEBUG:-false}" = "true" ]]; then
        log_security "CRITICAL" "DEBUG mode is enabled in production - security violation"
        exit 1
    fi
    
    if [[ "${FLASK_ENV:-production}" != "production" ]]; then
        log_security "WARNING" "FLASK_ENV is not set to production: ${FLASK_ENV:-}"
    fi
    
    log_security "INFO" "Environment security validation completed"
}

validate_file_permissions() {
    log_security "INFO" "Starting file permissions security validation"
    
    # Check critical file permissions
    local critical_files=(
        "/app/.env:640"
        "/app/src:755"
        "/app/uploads:750"
        "/app/logs:750"
    )
    
    for file_spec in "${critical_files[@]}"; do
        local file_path="${file_spec%:*}"
        local expected_perm="${file_spec#*:}"
        
        if [[ -e "$file_path" ]]; then
            local actual_perm
            actual_perm=$(stat -c "%a" "$file_path")
            
            if [[ "$actual_perm" != "$expected_perm" ]]; then
                log_security "WARNING" "File $file_path has permissions $actual_perm, expected $expected_perm"
                
                # Auto-fix in strict/paranoid mode
                if [[ "$SECURITY_LEVEL" = "strict" ]] || [[ "$SECURITY_LEVEL" = "paranoid" ]]; then
                    if chmod "$expected_perm" "$file_path" 2>/dev/null; then
                        log_security "INFO" "Fixed permissions for $file_path to $expected_perm"
                    else
                        log_security "ERROR" "Failed to fix permissions for $file_path"
                    fi
                fi
            fi
        fi
    done
    
    log_security "INFO" "File permissions validation completed"
}

validate_network_security() {
    log_security "INFO" "Starting network security validation"
    
    # Check if running in privileged mode (security violation)
    if [[ -r "/proc/self/status" ]]; then
        if grep -q "NoNewPrivs:.*0" "/proc/self/status" 2>/dev/null; then
            log_security "CRITICAL" "Container is running with new privileges enabled - security violation"
            exit 1
        fi
    fi
    
    # Verify non-root execution
    if [[ "$(id -u)" = "0" ]]; then
        log_security "CRITICAL" "Running as root user - security violation"
        exit 1
    fi
    
    # Check user and group
    local current_user
    local current_group
    current_user=$(id -un)
    current_group=$(id -gn)
    
    if [[ "$current_user" != "sora" ]] || [[ "$current_group" != "sora" ]]; then
        log_security "ERROR" "Running as unexpected user:group $current_user:$current_group, expected sora:sora"
        exit 1
    fi
    
    log_security "INFO" "Network security validation completed"
}

wait_for_database() {
    log_security "INFO" "Waiting for database connection with security validation"
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_security "INFO" "Database connection attempt $attempt/$max_attempts"
        
        if python3 -c "
import psycopg2
import os
import sys
import ssl
from urllib.parse import urlparse

try:
    db_url = os.getenv('DATABASE_URL')
    parsed = urlparse(db_url)
    
    # Security: Validate database connection parameters
    if parsed.scheme != 'postgresql':
        print('ERROR: Database URL scheme must be postgresql')
        sys.exit(1)
    
    if not parsed.hostname:
        print('ERROR: Database hostname not specified')
        sys.exit(1)
    
    # Attempt secure connection
    conn = psycopg2.connect(
        db_url,
        sslmode='prefer',  # Prefer SSL connections
        connect_timeout=10,
        application_name='sora-security-hardened'
    )
    
    # Test connection with security query
    cur = conn.cursor()
    cur.execute('SELECT version(), current_user, inet_server_addr(), inet_server_port()')
    db_info = cur.fetchone()
    
    print(f'SUCCESS: Connected to {db_info[0]} as {db_info[1]} at {db_info[2]}:{db_info[3]}')
    
    cur.close()
    conn.close()
    sys.exit(0)
    
except psycopg2.OperationalError as e:
    print(f'CONNECTION_ERROR: {e}')
    sys.exit(1)
except Exception as e:
    print(f'ERROR: {e}')
    sys.exit(1)
" 2>&1; then
            log_security "INFO" "Database connection established successfully"
            return 0
        else
            log_security "WARNING" "Database connection attempt $attempt failed"
            sleep 2
        fi
        
        ((attempt++))
    done
    
    log_security "CRITICAL" "Failed to establish database connection after $max_attempts attempts"
    exit 1
}

run_security_migrations() {
    log_security "INFO" "Running database migrations with security validation"
    
    # Set secure environment for migration
    export FLASK_APP="src.main:create_app"
    export PYTHONPATH="/app:$PYTHONPATH"
    
    # Run migrations with error handling
    if python3 -m flask db upgrade 2>&1 | tee -a "/app/logs/migration.log"; then
        log_security "INFO" "Database migrations completed successfully"
    else
        local migration_exit_code=$?
        log_security "WARNING" "Migration command failed with exit code $migration_exit_code"
        
        # Validate that required tables exist
        if python3 -c "
import psycopg2
import os
import sys

try:
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cur = conn.cursor()
    
    # Check for critical tables
    required_tables = ['video_jobs', 'providers', 'user_sessions']
    
    for table in required_tables:
        cur.execute(
            'SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = %s)',
            (table,)
        )
        exists = cur.fetchone()[0]
        
        if not exists:
            print(f'ERROR: Required table {table} does not exist')
            sys.exit(1)
        else:
            print(f'OK: Table {table} exists')
    
    cur.close()
    conn.close()
    print('SUCCESS: All required tables validated')
    
except Exception as e:
    print(f'ERROR: Table validation failed: {e}')
    sys.exit(1)
"; then
            log_security "INFO" "Database schema validation passed"
        else
            log_security "CRITICAL" "Database schema validation failed"
            exit 1
        fi
    fi
}

initialize_security_monitoring() {
    log_security "INFO" "Initializing security monitoring"
    
    # Create security event directories
    local security_dirs=(
        "/app/security-events"
        "/app/logs/security"
        "/app/temp/security"
    )
    
    for dir in "${security_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            if mkdir -p "$dir" 2>/dev/null; then
                log_security "INFO" "Created security directory: $dir"
                chmod 750 "$dir" 2>/dev/null || true
            else
                log_security "WARNING" "Failed to create security directory: $dir"
            fi
        fi
    done
    
    # Initialize security monitoring based on level
    case "$SECURITY_LEVEL" in
        strict|paranoid)
            log_security "INFO" "Initializing enhanced security monitoring for $SECURITY_LEVEL mode"
            
            # Start background security monitor if available
            if [[ -x "/app/.venv/bin/python" ]] && [[ -f "/app/src/deployment/security/security_monitoring.py" ]]; then
                log_security "INFO" "Starting background security monitoring"
                # Note: In production, this would be a separate container
                # python3 /app/src/deployment/security/security_monitoring.py &
            fi
            ;;
        *)
            log_security "INFO" "Standard security monitoring for $SECURITY_LEVEL mode"
            ;;
    esac
}

start_application() {
    log_security "INFO" "Starting Flask application with security hardening"
    
    # Security-focused gunicorn configuration
    local gunicorn_config=(
        "--bind" "0.0.0.0:5001"
        "--workers" "3"
        "--worker-class" "sync"
        "--worker-connections" "1000"
        "--max-requests" "1000"
        "--max-requests-jitter" "100"
        "--timeout" "120"
        "--keep-alive" "2"
        "--preload"
        "--enable-stdio-inheritance"
        "--access-logfile" "/app/logs/access.log"
        "--error-logfile" "/app/logs/error.log"
        "--log-level" "$LOG_LEVEL"
        "--capture-output"
        "--access-logformat" "%({x-forwarded-for}i)s %(l)s %(u)s %(t)s \"%(r)s\" %(s)s %(b)s \"%(f)s\" \"%(a)s\" %(D)s"
    )
    
    # Add security-specific configuration based on level
    case "$SECURITY_LEVEL" in
        strict|paranoid)
            gunicorn_config+=(
                "--limit-request-line" "2048"
                "--limit-request-fields" "50"
                "--limit-request-field-size" "1024"
            )
            ;;
    esac
    
    log_security "INFO" "Executing gunicorn with security configuration"
    
    # Start application with security monitoring
    exec python3 -m gunicorn "${gunicorn_config[@]}" "src.main:create_app()"
}

# Signal handlers for graceful shutdown
cleanup() {
    log_security "INFO" "Received shutdown signal, performing security cleanup"
    
    # Clean up temporary files
    if [[ -d "/app/temp" ]]; then
        find /app/temp -type f -delete 2>/dev/null || true
        log_security "INFO" "Cleaned up temporary files"
    fi
    
    # Final security log
    log_security "INFO" "Security-hardened container shutdown completed"
    
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main execution flow
main() {
    log_security "INFO" "Starting security-hardened C1-Image Upload Security Pipeline"
    log_security "INFO" "Container security level: $SECURITY_LEVEL"
    
    # Security validation phase
    validate_environment
    validate_file_permissions
    validate_network_security
    
    # Database connection phase
    wait_for_database
    run_security_migrations
    
    # Security monitoring initialization
    initialize_security_monitoring
    
    # Application startup
    start_application
}

# Execute main function
main "$@"