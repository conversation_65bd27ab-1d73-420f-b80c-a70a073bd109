# Single-stage Docker build for Sora Flask Application
# Optimized for production deployment with security hardening

FROM python:3.11-slim as production

# Create non-root user for security
RUN groupadd -r sora && useradd -r -g sora sora

# Install runtime dependencies including FFmpeg for video processing
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    gcc \
    g++ \
    libpq-dev \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install uv
RUN pip install uv

# Copy dependency files
COPY pyproject.toml ./
COPY uv.lock ./

# Install dependencies and create fresh virtual environment in container
RUN uv sync --frozen

# Copy application code
COPY . .

# Copy environment file for Docker deployment
COPY src/deployment/docker/.env /app/.env

# Copy and setup entrypoint script
COPY src/deployment/docker/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Create directories and set permissions
RUN mkdir -p /app/uploads /app/logs /app/static && \
    chown -R sora:sora /app

# Set environment to use UV's virtual environment
ENV PATH="/app/.venv/bin:$PATH"

# Switch to non-root user
USER sora

# Expose port
EXPOSE 5001

# Health check using existing health endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Run application with entrypoint script (includes database migrations)
CMD ["/app/entrypoint.sh"]