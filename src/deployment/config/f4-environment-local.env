# F4 Environment Configuration - Local Development
# Optimized for local development with mock providers and reduced resource usage

# =============================================================================
# F4 Provider Configuration
# =============================================================================

# Provider Selection (Local Development Defaults)
USE_MOCK_VEO=true
DEFAULT_PROVIDER=azure_sora

# Google Cloud Configuration (Optional for local development)
GOOGLE_PROJECT_ID=
GOOGLE_LOCATION=us-central1
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Google Veo3 Model Configuration (Development Settings)
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=120
VEO3_MAX_RETRIES=2
VEO3_RETRY_DELAY=1
VEO3_GENERATION_TIMEOUT=600
VEO3_RATE_LIMIT_RPM=10

# =============================================================================
# F4 Configuration Management
# =============================================================================

# Configuration Validation
CONFIG_VALIDATION_ENABLED=true
CONFIG_CACHE_TTL=60
CONFIG_AUTO_RELOAD=true

# Deployment Environment
DEPLOYMENT_TYPE=local
F4_DEPLOYMENT_ENVIRONMENT=development

# Configuration Monitoring
CONFIG_MONITOR_INTERVAL=300
ALERT_THRESHOLD_CONFIG_LOAD_TIME=10000
ALERT_THRESHOLD_PROVIDER_SWITCH_TIME=15000

# =============================================================================
# Performance Settings (Local Development)
# =============================================================================

# Worker Configuration
WORKER_CONCURRENCY=1
WORKER_MAX_TASKS_PER_CHILD=50
WORKER_CONFIG_VALIDATION=true
WORKER_PROVIDER_SWITCHING=enabled

# Rate Limiting (Relaxed for development)
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS_PER_MINUTE=120

# =============================================================================
# Development Debug Settings
# =============================================================================

# Debug and Logging
DEBUG=true
LOG_LEVEL=DEBUG
CONFIG_DEBUG_MODE=true

# Flask Development Settings
FLASK_ENV=development
FLASK_DEBUG=true

# =============================================================================
# Local File Paths
# =============================================================================

# Configuration Cache
F4_CONFIG_CACHE_PATH=/tmp/sora_config_cache

# Service Account (Optional)
GOOGLE_SERVICE_ACCOUNT_FILE=/tmp/google-service-account.json

# =============================================================================
# Example Environment Variables
# =============================================================================

# Uncomment and modify these for your local setup:

# For testing with real Google Veo3 API locally:
# USE_MOCK_VEO=false
# GOOGLE_PROJECT_ID=your-project-id-12345
# GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=your-client-secret

# For testing Azure Sora API:
# AZURE_OPENAI_API_KEY=your-azure-api-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
# AZURE_OPENAI_API_VERSION=2024-02-15-preview
# AZURE_OPENAI_SORA_DEPLOYMENT=sora

# Database (Use local SQLite for development)
# DATABASE_URL=sqlite:///./sora_local.db

# =============================================================================
# Comments and Documentation
# =============================================================================

# This configuration file is optimized for local development with:
# - Mock providers enabled by default
# - Reduced timeouts and resource usage
# - Enhanced debugging and validation
# - Auto-reload capabilities for rapid development
# - Relaxed rate limiting for testing

# To switch to real providers for testing:
# 1. Set USE_MOCK_VEO=false
# 2. Provide valid GOOGLE_PROJECT_ID and credentials
# 3. Ensure proper authentication setup
# 4. Test with small requests first

# For more configuration options, see:
# - src/config/veo3_settings.py
# - src/config/factory.py
# - Documentation in docs/CONFIGURATION.md