# F4 Environment Configuration - Production Deployment
# Optimized for production with real providers, security, and performance

# =============================================================================
# F4 Provider Configuration (Production)
# =============================================================================

# Provider Selection (Production Settings)
USE_MOCK_VEO=false
DEFAULT_PROVIDER=azure_sora

# Google Cloud Configuration (Required for production Veo3)
GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
GOOGLE_LOCATION=us-central1
GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}

# Google Veo3 Model Configuration (Production Settings)
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=300
VEO3_MAX_RETRIES=3
VEO3_RETRY_DELAY=2
VEO3_GENERATION_TIMEOUT=1800
VEO3_RATE_LIMIT_RPM=30

# =============================================================================
# F4 Configuration Management (Production)
# =============================================================================

# Configuration Validation (Strict)
CONFIG_VALIDATION_ENABLED=true
CONFIG_CACHE_TTL=300
CONFIG_AUTO_RELOAD=false

# Deployment Environment
DEPLOYMENT_TYPE=docker
F4_DEPLOYMENT_ENVIRONMENT=production

# Configuration Monitoring (Production)
CONFIG_MONITOR_INTERVAL=60
ALERT_THRESHOLD_CONFIG_LOAD_TIME=5000
ALERT_THRESHOLD_PROVIDER_SWITCH_TIME=10000

# =============================================================================
# Performance Settings (Production)
# =============================================================================

# Worker Configuration (Production)
WORKER_CONCURRENCY=4
WORKER_MAX_TASKS_PER_CHILD=100
WORKER_CONFIG_VALIDATION=true
WORKER_PROVIDER_SWITCHING=enabled

# Rate Limiting (Production)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# =============================================================================
# Security Settings (Production)
# =============================================================================

# Security and Logging
DEBUG=false
LOG_LEVEL=INFO
CONFIG_DEBUG_MODE=false

# Flask Production Settings
FLASK_ENV=production
FLASK_DEBUG=false

# Security Headers
SECURE_HEADERS_ENABLED=true
FORCE_HTTPS=true

# =============================================================================
# Production File Paths
# =============================================================================

# Configuration Cache (Production)
F4_CONFIG_CACHE_PATH=/opt/sora/data/f4_config_cache

# Service Account (Production - Mounted as Docker Secret)
GOOGLE_SERVICE_ACCOUNT_FILE=/run/secrets/google_service_account

# =============================================================================
# Production Database and Infrastructure
# =============================================================================

# Database (Production)
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres:5432/sora_production

# Redis (Production)
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# =============================================================================
# Monitoring and Alerting (Production)
# =============================================================================

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=60

# Alert Configuration
ALERT_EMAIL=${ALERT_EMAIL}
SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}

# Metrics and Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
METRICS_COLLECTION_INTERVAL=30

# =============================================================================
# Backup and Recovery (Production)
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
BACKUP_RETENTION_DAYS=30

# Configuration Backup
CONFIG_BACKUP_ENABLED=true
CONFIG_BACKUP_INTERVAL=86400

# =============================================================================
# Load Balancing and Scaling (Production)
# =============================================================================

# Load Balancing
NGINX_ENABLED=true
LOAD_BALANCER_ENABLED=true

# Auto-scaling
AUTO_SCALING_ENABLED=false
MAX_WORKERS=8
MIN_WORKERS=2

# =============================================================================
# SSL and Certificates (Production)
# =============================================================================

# SSL Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/opt/sora/ssl/fullchain.pem
SSL_KEY_PATH=/opt/sora/ssl/privkey.pem

# Certificate Management
CERT_AUTO_RENEWAL=true
CERT_EXPIRY_ALERT_DAYS=7

# =============================================================================
# Required Environment Variables (Production)
# =============================================================================

# These must be set in your production environment:

# Azure OpenAI (Required)
AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
AZURE_OPENAI_SORA_DEPLOYMENT=${AZURE_OPENAI_SORA_DEPLOYMENT}

# Application Security (Required)
SECRET_KEY=${SECRET_KEY}
DB_PASSWORD=${DB_PASSWORD}

# =============================================================================
# Production Validation Checklist
# =============================================================================

# Before deploying to production, ensure:
# ✓ All required environment variables are set
# ✓ Google Cloud service account is properly configured
# ✓ Azure OpenAI credentials are valid
# ✓ SSL certificates are installed
# ✓ Backup procedures are tested
# ✓ Monitoring and alerting are configured
# ✓ Health checks are passing
# ✓ Security settings are reviewed

# =============================================================================
# Comments and Documentation
# =============================================================================

# This configuration file is optimized for production with:
# - Real provider APIs (no mock mode)
# - Enhanced security and validation
# - Production performance settings
# - Comprehensive monitoring and alerting
# - Backup and recovery capabilities
# - SSL/TLS termination
# - Auto-scaling readiness

# For more configuration options, see:
# - src/config/veo3_settings.py
# - src/config/factory.py
# - Documentation in docs/DEPLOYMENT_GUIDE.md