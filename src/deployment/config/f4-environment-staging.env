# F4 Environment Configuration - Staging Deployment
# Production-like environment for testing and validation before production deployment

# =============================================================================
# F4 Provider Configuration (Staging)
# =============================================================================

# Provider Selection (Staging Settings - Real APIs for testing)
USE_MOCK_VEO=false
DEFAULT_PROVIDER=azure_sora

# Google Cloud Configuration (Staging Project)
GOOGLE_PROJECT_ID=${GOOGLE_STAGING_PROJECT_ID:-${GOOGLE_PROJECT_ID}}
GOOGLE_LOCATION=us-central1
GOOGLE_CLIENT_ID=${GOOGLE_STAGING_CLIENT_ID:-${GOOGLE_CLIENT_ID}}
GOOGLE_CLIENT_SECRET=${GOOGLE_STAGING_CLIENT_SECRET:-${GOOGLE_CLIENT_SECRET}}

# Google Veo3 Model Configuration (Staging Settings)
VEO3_MODEL_VERSION=veo-3.0-generate-preview
VEO3_TIMEOUT=300
VEO3_MAX_RETRIES=3
VEO3_RETRY_DELAY=2
VEO3_GENERATION_TIMEOUT=1200
VEO3_RATE_LIMIT_RPM=20

# =============================================================================
# F4 Configuration Management (Staging)
# =============================================================================

# Configuration Validation (Enabled with enhanced logging)
CONFIG_VALIDATION_ENABLED=true
CONFIG_CACHE_TTL=180
CONFIG_AUTO_RELOAD=false

# Deployment Environment
DEPLOYMENT_TYPE=docker
F4_DEPLOYMENT_ENVIRONMENT=staging

# Configuration Monitoring (Staging)
CONFIG_MONITOR_INTERVAL=120
ALERT_THRESHOLD_CONFIG_LOAD_TIME=7500
ALERT_THRESHOLD_PROVIDER_SWITCH_TIME=12500

# =============================================================================
# Performance Settings (Staging)
# =============================================================================

# Worker Configuration (Staging)
WORKER_CONCURRENCY=2
WORKER_MAX_TASKS_PER_CHILD=75
WORKER_CONFIG_VALIDATION=true
WORKER_PROVIDER_SWITCHING=enabled

# Rate Limiting (Staging - Moderate)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=80

# =============================================================================
# Debugging and Testing (Staging)
# =============================================================================

# Debugging (Enhanced for staging testing)
DEBUG=false
LOG_LEVEL=INFO
CONFIG_DEBUG_MODE=true

# Flask Staging Settings
FLASK_ENV=staging
FLASK_DEBUG=false

# Enhanced Testing
INTEGRATION_TESTING_ENABLED=true
API_TESTING_ENABLED=true

# =============================================================================
# Staging File Paths
# =============================================================================

# Configuration Cache (Staging)
F4_CONFIG_CACHE_PATH=/opt/sora/staging/f4_config_cache

# Service Account (Staging)
GOOGLE_SERVICE_ACCOUNT_FILE=/run/secrets/google_service_account_staging

# =============================================================================
# Staging Database and Infrastructure
# =============================================================================

# Database (Staging)
DATABASE_URL=postgresql://sora_staging_user:${STAGING_DB_PASSWORD}@postgres:5432/sora_staging

# Redis (Staging)
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/1

# =============================================================================
# Monitoring and Testing (Staging)
# =============================================================================

# Health Check Configuration (Frequent for testing)
HEALTH_CHECK_TIMEOUT=20
HEALTH_CHECK_INTERVAL=30

# Alert Configuration (Staging alerts)
ALERT_EMAIL=${STAGING_ALERT_EMAIL}
SLACK_WEBHOOK_URL=${STAGING_SLACK_WEBHOOK_URL}

# Metrics and Monitoring (Staging)
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
METRICS_COLLECTION_INTERVAL=15

# =============================================================================
# Testing and Validation (Staging)
# =============================================================================

# End-to-End Testing
E2E_TESTING_ENABLED=true
LOAD_TESTING_ENABLED=true
SECURITY_TESTING_ENABLED=true

# Provider Testing
PROVIDER_TESTING_ENABLED=true
MOCK_PROVIDER_COMPARISON=true

# Configuration Testing
CONFIG_STRESS_TESTING=true
PROVIDER_SWITCHING_TESTING=true

# =============================================================================
# Backup and Recovery (Staging)
# =============================================================================

# Backup Configuration (Staging)
BACKUP_ENABLED=true
BACKUP_INTERVAL=7200
BACKUP_RETENTION_DAYS=7

# Configuration Backup
CONFIG_BACKUP_ENABLED=true
CONFIG_BACKUP_INTERVAL=43200

# =============================================================================
# Security Settings (Staging)
# =============================================================================

# Security (Production-like with testing flexibility)
SECURE_HEADERS_ENABLED=true
FORCE_HTTPS=false

# SSL Configuration (Optional for staging)
SSL_ENABLED=false
SSL_CERT_PATH=/opt/sora/staging/ssl/fullchain.pem
SSL_KEY_PATH=/opt/sora/staging/ssl/privkey.pem

# =============================================================================
# Load Testing and Performance (Staging)
# =============================================================================

# Load Testing Configuration
LOAD_TEST_CONCURRENT_USERS=10
LOAD_TEST_DURATION=300
LOAD_TEST_RAMP_UP=60

# Performance Testing
PERFORMANCE_BASELINE_ENABLED=true
PERFORMANCE_REGRESSION_TESTING=true

# =============================================================================
# Required Environment Variables (Staging)
# =============================================================================

# Azure OpenAI (Staging Environment)
AZURE_OPENAI_API_KEY=${AZURE_STAGING_OPENAI_API_KEY:-${AZURE_OPENAI_API_KEY}}
AZURE_OPENAI_ENDPOINT=${AZURE_STAGING_OPENAI_ENDPOINT:-${AZURE_OPENAI_ENDPOINT}}
AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
AZURE_OPENAI_SORA_DEPLOYMENT=${AZURE_STAGING_SORA_DEPLOYMENT:-${AZURE_OPENAI_SORA_DEPLOYMENT}}

# Application Security (Staging)
SECRET_KEY=${STAGING_SECRET_KEY:-${SECRET_KEY}}
DB_PASSWORD=${STAGING_DB_PASSWORD:-${DB_PASSWORD}}

# =============================================================================
# Staging-Specific Features
# =============================================================================

# Feature Flags for Testing
FEATURE_FLAG_VEO3_INTEGRATION=true
FEATURE_FLAG_PROVIDER_SWITCHING=true
FEATURE_FLAG_ENHANCED_MONITORING=true

# API Testing
API_TESTING_MOCK_FALLBACK=true
API_TESTING_TIMEOUT_SCENARIOS=true
API_TESTING_ERROR_SCENARIOS=true

# =============================================================================
# Data Management (Staging)
# =============================================================================

# Data Refresh
DATA_REFRESH_ENABLED=true
DATA_REFRESH_INTERVAL=86400

# Data Anonymization
DATA_ANONYMIZATION_ENABLED=true
PII_MASKING_ENABLED=true

# =============================================================================
# Staging Validation Checklist
# =============================================================================

# Before promoting to production, validate:
# ✓ All provider APIs are working correctly
# ✓ Configuration switching is seamless
# ✓ Performance meets requirements
# ✓ Security testing passes
# ✓ Load testing within limits
# ✓ Monitoring and alerting functional
# ✓ Backup and recovery tested
# ✓ End-to-end workflows complete

# =============================================================================
# Comments and Documentation
# =============================================================================

# This configuration file is optimized for staging with:
# - Real provider APIs for production-like testing
# - Enhanced monitoring and validation
# - Load and performance testing capabilities
# - Security testing with flexibility
# - Data management and refresh capabilities
# - Feature flags for controlled testing
# - Production-like infrastructure

# Staging Environment Purpose:
# - Validate configuration changes before production
# - Test provider switching and failover scenarios
# - Perform load and stress testing
# - Validate monitoring and alerting
# - Test backup and recovery procedures
# - Regression testing for new features

# For more information, see:
# - docs/STAGING_GUIDE.md
# - docs/TESTING_PROCEDURES.md
# - docs/DEPLOYMENT_PIPELINE.md