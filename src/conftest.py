"""Pytest configuration and fixtures."""

import os
import sys
import tempfile
from pathlib import Path

import pytest

# Add project root to Python path for proper imports
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.main import create_app


@pytest.fixture
def app():
    """
    Create Flask application for testing.

    Returns:
        Flask: Test Flask application instance
    """
    # Create temporary directory for test uploads
    test_upload_dir = tempfile.mkdtemp()

    # Set test environment variables
    os.environ["FLASK_ENV"] = "testing"
    os.environ["UPLOAD_FOLDER"] = test_upload_dir
    os.environ["SECRET_KEY"] = "test-secret-key"
    
    # Use in-memory SQLite database for testing
    os.environ["DATABASE_URL"] = "sqlite:///:memory:"

    app = create_app()
    app.config["TESTING"] = True
    app.config["WTF_CSRF_ENABLED"] = False

    # Create database tables for testing
    with app.app_context():
        from src.main import db
        db.create_all()

    yield app

    # Cleanup
    import shutil

    shutil.rmtree(test_upload_dir, ignore_errors=True)


@pytest.fixture
def client(app):
    """
    Create test client.

    Args:
        app: Flask application fixture

    Returns:
        FlaskClient: Test client for making requests
    """
    return app.test_client()


@pytest.fixture
def runner(app):
    """
    Create test CLI runner.

    Args:
        app: Flask application fixture

    Returns:
        FlaskCliRunner: CLI runner for testing commands
    """
    return app.test_cli_runner()
