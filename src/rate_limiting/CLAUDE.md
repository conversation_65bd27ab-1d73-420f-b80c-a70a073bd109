# Rate Limiting Module Documentation

## Overview

Production-ready distributed rate limiting system using Redis backend with multiple strategies. Provides global rate limiting for Azure API protection, per-session limits, and adaptive rate control based on system performance.

## Module Structure

```
src/rate_limiting/
├── limiter.py             # Global rate limiter with Redis
├── strategy.py            # Legacy strategy implementation
├── strategies/            # Modular strategy implementations
│   ├── __init__.py
│   ├── base.py           # Abstract base class
│   ├── sliding_window.py # Sliding window algorithm
│   ├── token_bucket.py   # Token bucket algorithm
│   └── adaptive.py       # Adaptive rate limiting
└── tests/                # Co-located rate limiting tests
    ├── test_limiter.py
    ├── test_integration.py
    ├── test_sliding_window_strategy.py
    ├── test_token_bucket_strategy.py
    └── test_adaptive_strategy.py
```

## Rate Limiting Strategies

### Strategy Architecture
```python
# Abstract base class for all strategies
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class RateLimitStrategy(ABC):
    """Abstract base class for rate limiting strategies."""
    
    @abstractmethod
    async def is_allowed(self, key: str, limit: int, window_seconds: int) -> bool:
        """Check if request is allowed under rate limit."""
        pass
    
    @abstractmethod
    async def get_status(self, key: str) -> Dict[str, Any]:
        """Get current rate limit status."""
        pass
    
    @abstractmethod
    async def reset(self, key: str) -> None:
        """Reset rate limit for key."""
        pass
```

### Sliding Window Strategy
High precision rate limiting with exact request counting within time windows.

```python
# src/rate_limiting/strategies/sliding_window.py
from typing import Dict, Any
from redis import Redis
import time

class SlidingWindowStrategy(RateLimitStrategy):
    """Sliding window rate limiting strategy."""
    
    def __init__(self, redis_client: Redis):
        self.redis_client = redis_client
    
    async def is_allowed(self, key: str, limit: int, window_seconds: int) -> bool:
        """Check if request is allowed using sliding window."""
        now = time.time()
        window_start = now - window_seconds
        
        # Redis pipeline for atomic operations
        with self.redis_client.pipeline() as pipe:
            pipe.multi()
            
            # Remove expired requests
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(now): now})
            
            # Set expiration
            pipe.expire(key, window_seconds + 1)
            
            results = pipe.execute()
        
        current_count = results[1]  # Count after cleanup
        return current_count < limit
    
    async def get_status(self, key: str) -> Dict[str, Any]:
        """Get sliding window status."""
        now = time.time()
        window_start = now - 60  # Default 1-minute window
        
        current_count = self.redis_client.zcount(key, window_start, now)
        oldest_request = self.redis_client.zrange(key, 0, 0, withscores=True)
        
        return {
            "strategy": "sliding_window",
            "current_count": current_count,
            "oldest_request": oldest_request[0][1] if oldest_request else None,
            "window_start": window_start,
            "timestamp": now
        }
```

### Token Bucket Strategy
Burst-friendly rate limiting allowing temporary bursts within overall rate limits.

```python
# src/rate_limiting/strategies/token_bucket.py
import asyncio
import time
from typing import Dict, Any

class TokenBucketStrategy(RateLimitStrategy):
    """Token bucket rate limiting strategy."""
    
    def __init__(self, redis_client: Redis):
        self.redis_client = redis_client
    
    async def is_allowed(self, key: str, limit: int, window_seconds: int) -> bool:
        """Check if request is allowed using token bucket."""
        now = time.time()
        bucket_key = f"bucket:{key}"
        last_refill_key = f"bucket:{key}:last_refill"
        
        # Get current bucket state
        current_tokens = float(self.redis_client.get(bucket_key) or limit)
        last_refill = float(self.redis_client.get(last_refill_key) or now)
        
        # Calculate tokens to add based on time elapsed
        elapsed = now - last_refill
        refill_rate = limit / window_seconds  # tokens per second
        tokens_to_add = elapsed * refill_rate
        
        # Update bucket with new tokens (capped at limit)
        new_tokens = min(limit, current_tokens + tokens_to_add)
        
        if new_tokens >= 1.0:
            # Consume one token
            final_tokens = new_tokens - 1.0
            
            # Update Redis atomically
            with self.redis_client.pipeline() as pipe:
                pipe.multi()
                pipe.set(bucket_key, final_tokens, ex=window_seconds * 2)
                pipe.set(last_refill_key, now, ex=window_seconds * 2)
                pipe.execute()
            
            return True
        else:
            # Not enough tokens, update last refill time only
            self.redis_client.set(last_refill_key, now, ex=window_seconds * 2)
            return False
    
    async def get_status(self, key: str) -> Dict[str, Any]:
        """Get token bucket status."""
        bucket_key = f"bucket:{key}"
        last_refill_key = f"bucket:{key}:last_refill"
        
        current_tokens = float(self.redis_client.get(bucket_key) or 0)
        last_refill = float(self.redis_client.get(last_refill_key) or time.time())
        
        return {
            "strategy": "token_bucket",
            "current_tokens": current_tokens,
            "last_refill": last_refill,
            "timestamp": time.time()
        }
```

### Adaptive Strategy
Intelligent rate limiting that adjusts limits based on system performance and error rates.

```python
# src/rate_limiting/strategies/adaptive.py
from typing import Dict, Any, Optional
import time

class AdaptiveStrategy(RateLimitStrategy):
    """Adaptive rate limiting strategy."""
    
    def __init__(self, redis_client: Redis, base_strategy: RateLimitStrategy):
        self.redis_client = redis_client
        self.base_strategy = base_strategy
        self.metrics_window = 300  # 5 minutes
    
    async def is_allowed(self, key: str, limit: int, window_seconds: int) -> bool:
        """Check if request is allowed with adaptive limit."""
        # Calculate adaptive limit based on system metrics
        adaptive_limit = await self._calculate_adaptive_limit(key, limit)
        
        # Use base strategy with adaptive limit
        return await self.base_strategy.is_allowed(key, adaptive_limit, window_seconds)
    
    async def _calculate_adaptive_limit(self, key: str, base_limit: int) -> int:
        """Calculate adaptive limit based on system performance."""
        metrics = await self._get_system_metrics(key)
        
        # Adjustment factors
        error_rate_factor = self._calculate_error_rate_factor(metrics.get("error_rate", 0))
        response_time_factor = self._calculate_response_time_factor(metrics.get("avg_response_time", 0))
        
        # Calculate final adjustment (conservative approach)
        adjustment_factor = min(error_rate_factor, response_time_factor)
        adaptive_limit = int(base_limit * adjustment_factor)
        
        # Ensure minimum limit
        return max(1, adaptive_limit)
    
    def _calculate_error_rate_factor(self, error_rate: float) -> float:
        """Calculate adjustment factor based on error rate."""
        if error_rate > 0.1:  # 10% error rate
            return 0.5  # Reduce limit by 50%
        elif error_rate > 0.05:  # 5% error rate
            return 0.8  # Reduce limit by 20%
        else:
            return 1.0  # No adjustment
    
    def _calculate_response_time_factor(self, avg_response_time: float) -> float:
        """Calculate adjustment factor based on response time."""
        if avg_response_time > 5000:  # 5 seconds
            return 0.6  # Reduce limit by 40%
        elif avg_response_time > 2000:  # 2 seconds
            return 0.8  # Reduce limit by 20%
        else:
            return 1.0  # No adjustment
    
    async def _get_system_metrics(self, key: str) -> Dict[str, float]:
        """Get system performance metrics from Redis."""
        metrics_key = f"metrics:{key}"
        
        # Get stored metrics
        raw_metrics = self.redis_client.hgetall(metrics_key)
        
        if not raw_metrics:
            return {"error_rate": 0.0, "avg_response_time": 0.0}
        
        return {
            "error_rate": float(raw_metrics.get(b"error_rate", 0)),
            "avg_response_time": float(raw_metrics.get(b"avg_response_time", 0))
        }
```

## Global Rate Limiter

### Redis-Based Implementation
```python
# src/rate_limiting/limiter.py
from typing import Dict, Any, Optional
from redis import Redis
from src.rate_limiting.strategies.sliding_window import SlidingWindowStrategy
from src.rate_limiting.strategies.token_bucket import TokenBucketStrategy
from src.rate_limiting.strategies.adaptive import AdaptiveStrategy

class GlobalRateLimiter:
    """Global rate limiter with configurable strategies."""
    
    def __init__(
        self,
        redis_client: Redis,
        default_strategy: str = "sliding_window",
        default_limit: int = 10,
        default_window: int = 60
    ):
        self.redis_client = redis_client
        self.default_limit = default_limit
        self.default_window = default_window
        
        # Initialize strategies
        self.strategies = {
            "sliding_window": SlidingWindowStrategy(redis_client),
            "token_bucket": TokenBucketStrategy(redis_client),
            "adaptive": AdaptiveStrategy(
                redis_client,
                SlidingWindowStrategy(redis_client)  # Base strategy
            )
        }
        
        self.default_strategy_name = default_strategy
    
    async def is_allowed(
        self,
        key: str,
        limit: Optional[int] = None,
        window_seconds: Optional[int] = None,
        strategy: Optional[str] = None
    ) -> bool:
        """Check if request is allowed under rate limit."""
        # Use defaults if not provided
        limit = limit or self.default_limit
        window_seconds = window_seconds or self.default_window
        strategy = strategy or self.default_strategy_name
        
        # Get strategy implementation
        strategy_impl = self.strategies.get(strategy)
        if not strategy_impl:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        return await strategy_impl.is_allowed(key, limit, window_seconds)
    
    async def get_status(self, key: str, strategy: Optional[str] = None) -> Dict[str, Any]:
        """Get rate limit status for key."""
        strategy = strategy or self.default_strategy_name
        strategy_impl = self.strategies.get(strategy)
        
        if not strategy_impl:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        return await strategy_impl.get_status(key)
    
    async def reset(self, key: str, strategy: Optional[str] = None) -> None:
        """Reset rate limit for key."""
        strategy = strategy or self.default_strategy_name
        strategy_impl = self.strategies.get(strategy)
        
        if not strategy_impl:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        await strategy_impl.reset(key)
```

## Integration Patterns

### Azure API Protection
```python
# Rate limiting for Azure OpenAI API calls
from src.rate_limiting.limiter import GlobalRateLimiter

class AzureAPIRateLimit:
    """Rate limiting specifically for Azure API calls."""
    
    def __init__(self, redis_client: Redis):
        self.rate_limiter = GlobalRateLimiter(
            redis_client=redis_client,
            default_strategy="sliding_window",
            default_limit=10,  # Azure API limit: 10 req/sec
            default_window=1   # Per second
        )
    
    async def check_azure_limit(self, endpoint: str) -> bool:
        """Check if Azure API call is allowed."""
        key = f"azure_api:{endpoint}"
        return await self.rate_limiter.is_allowed(key)
    
    async def check_global_azure_limit(self) -> bool:
        """Check global Azure API limit across all endpoints."""
        return await self.rate_limiter.is_allowed("azure_api:global")
```

### Per-Session Rate Limiting
```python
class SessionRateLimit:
    """Rate limiting per user session."""
    
    def __init__(self, redis_client: Redis):
        self.rate_limiter = GlobalRateLimiter(
            redis_client=redis_client,
            default_strategy="token_bucket",  # Allow bursts
            default_limit=60,   # 60 requests
            default_window=60   # Per minute
        )
    
    async def check_session_limit(self, session_id: str) -> bool:
        """Check if session request is allowed."""
        key = f"session:{session_id}"
        return await self.rate_limiter.is_allowed(key)
    
    async def check_ip_limit(self, ip_address: str) -> bool:
        """Check IP-based rate limit."""
        key = f"ip:{ip_address}"
        return await self.rate_limiter.is_allowed(
            key,
            limit=300,    # 300 requests
            window=3600   # Per hour
        )
```

## Configuration

### Environment Variables
```bash
# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=10
RATE_LIMIT_STRATEGY=sliding_window

# Redis Configuration for Rate Limiting
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Azure API Rate Limiting
AZURE_API_RATE_LIMIT=10          # requests per second
AZURE_API_RATE_LIMIT_STRATEGY=sliding_window

# Adaptive Rate Limiting
ADAPTIVE_RATE_LIMIT_ENABLED=true
ADAPTIVE_ERROR_RATE_THRESHOLD=0.05
ADAPTIVE_RESPONSE_TIME_THRESHOLD=2000
```

### Factory Pattern
```python
from src.config.environments import get_environment_config
from redis import Redis

def create_rate_limiter() -> GlobalRateLimiter:
    """Factory function to create configured rate limiter."""
    config = get_environment_config()
    
    # Create Redis client
    redis_client = Redis(
        host=config.redis_host,
        port=config.redis_port,
        db=config.redis_db,
        password=config.redis_password,
        decode_responses=False  # Keep binary for rate limiting
    )
    
    return GlobalRateLimiter(
        redis_client=redis_client,
        default_strategy=config.rate_limit_strategy,
        default_limit=config.rate_limit_requests_per_minute,
        default_window=60
    )
```

## Testing Patterns

### Strategy Testing
```python
import pytest
import time
from unittest.mock import Mock
from src.rate_limiting.strategies.sliding_window import SlidingWindowStrategy

class TestSlidingWindowStrategy:
    """Test sliding window strategy."""
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        return Mock()
    
    @pytest.fixture
    def strategy(self, redis_mock):
        """Create strategy with mocked Redis."""
        return SlidingWindowStrategy(redis_mock)
    
    @pytest.mark.asyncio
    async def test_allows_requests_within_limit(self, strategy, redis_mock):
        """Test that requests within limit are allowed."""
        # Mock Redis responses for window operations
        redis_mock.pipeline.return_value.__enter__.return_value.execute.return_value = [
            None,  # zremrangebyscore result
            5,     # zcard result (current count)
            None,  # zadd result
            None   # expire result
        ]
        
        result = await strategy.is_allowed("test_key", limit=10, window_seconds=60)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_blocks_requests_over_limit(self, strategy, redis_mock):
        """Test that requests over limit are blocked."""
        # Mock Redis responses showing limit exceeded
        redis_mock.pipeline.return_value.__enter__.return_value.execute.return_value = [
            None,  # zremrangebyscore result
            15,    # zcard result (over limit of 10)
            None,  # zadd result
            None   # expire result
        ]
        
        result = await strategy.is_allowed("test_key", limit=10, window_seconds=60)
        assert result is False
```

### Integration Testing
```python
import redis
import pytest
from src.rate_limiting.limiter import GlobalRateLimiter

class TestRateLimitingIntegration:
    """Test rate limiting with real Redis."""
    
    @pytest.fixture
    def redis_client(self):
        """Create Redis client for testing."""
        client = redis.Redis(host='localhost', port=6379, db=15)  # Test DB
        yield client
        client.flushdb()  # Clean up after test
    
    @pytest.fixture
    def rate_limiter(self, redis_client):
        """Create rate limiter with real Redis."""
        return GlobalRateLimiter(
            redis_client=redis_client,
            default_limit=5,
            default_window=60
        )
    
    @pytest.mark.asyncio
    async def test_rate_limiting_workflow(self, rate_limiter):
        """Test complete rate limiting workflow."""
        key = "test_integration"
        
        # First 5 requests should be allowed
        for i in range(5):
            result = await rate_limiter.is_allowed(key)
            assert result is True, f"Request {i+1} should be allowed"
        
        # 6th request should be blocked
        result = await rate_limiter.is_allowed(key)
        assert result is False, "Request over limit should be blocked"
        
        # Reset should allow requests again
        await rate_limiter.reset(key)
        result = await rate_limiter.is_allowed(key)
        assert result is True, "Request after reset should be allowed"
```

## Performance Optimization

### Redis Pipeline Usage
```python
async def batch_check_limits(self, keys: List[str], limit: int, window: int) -> Dict[str, bool]:
    """Check multiple rate limits in batch."""
    results = {}
    
    # Use Redis pipeline for efficiency
    with self.redis_client.pipeline() as pipe:
        pipe.multi()
        
        for key in keys:
            now = time.time()
            window_start = now - window
            
            # Add operations for each key
            pipe.zremrangebyscore(key, 0, window_start)
            pipe.zcard(key)
            pipe.zadd(key, {str(now): now})
            pipe.expire(key, window + 1)
        
        # Execute all operations
        pipeline_results = pipe.execute()
    
    # Process results (4 operations per key)
    for i, key in enumerate(keys):
        count_result = pipeline_results[i * 4 + 1]  # zcard result
        results[key] = count_result < limit
    
    return results
```

### Connection Pooling
```python
import redis

# Create connection pool for rate limiting
rate_limit_pool = redis.ConnectionPool(
    host='localhost',
    port=6379,
    db=0,
    max_connections=20,
    retry_on_timeout=True,
    socket_connect_timeout=1,
    socket_timeout=1
)

redis_client = redis.Redis(connection_pool=rate_limit_pool)
```

## Monitoring and Metrics

### Rate Limit Metrics
```python
from src.monitoring.metrics import MetricsCollector

class RateLimitMonitoring:
    """Monitor rate limiting performance."""
    
    def __init__(self, rate_limiter: GlobalRateLimiter):
        self.rate_limiter = rate_limiter
        self.metrics = MetricsCollector()
    
    async def track_rate_limit_check(self, key: str, allowed: bool):
        """Track rate limit check metrics."""
        self.metrics.increment("rate_limit_checks_total")
        
        if allowed:
            self.metrics.increment("rate_limit_checks_allowed")
        else:
            self.metrics.increment("rate_limit_checks_blocked")
        
        # Track by key type
        key_type = key.split(":")[0] if ":" in key else "unknown"
        self.metrics.increment(f"rate_limit_checks_{key_type}")
    
    async def get_rate_limit_stats(self) -> Dict[str, Any]:
        """Get rate limiting statistics."""
        return {
            "total_checks": self.metrics.get_counter("rate_limit_checks_total"),
            "allowed_checks": self.metrics.get_counter("rate_limit_checks_allowed"),
            "blocked_checks": self.metrics.get_counter("rate_limit_checks_blocked"),
            "block_rate": self._calculate_block_rate(),
            "strategies_active": len(self.rate_limiter.strategies),
            "redis_status": await self._check_redis_status()
        }
    
    def _calculate_block_rate(self) -> float:
        """Calculate rate limit block percentage."""
        total = self.metrics.get_counter("rate_limit_checks_total")
        blocked = self.metrics.get_counter("rate_limit_checks_blocked")
        
        if total == 0:
            return 0.0
        
        return (blocked / total) * 100
```

## Error Handling

### Rate Limit Exceptions
```python
class RateLimitError(Exception):
    """Base exception for rate limiting errors."""
    pass

class RateLimitExceeded(RateLimitError):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, key: str, retry_after: Optional[int] = None):
        self.key = key
        self.retry_after = retry_after
        message = f"Rate limit exceeded for {key}"
        if retry_after:
            message += f", retry after {retry_after} seconds"
        super().__init__(message)

class RateLimitConfigError(RateLimitError):
    """Raised for rate limit configuration errors."""
    pass

class RedisConnectionError(RateLimitError):
    """Raised when Redis connection fails."""
    pass
```

### Graceful Degradation
```python
async def is_allowed_with_fallback(self, key: str, limit: int, window: int) -> bool:
    """Check rate limit with graceful degradation."""
    try:
        return await self.is_allowed(key, limit, window)
    except redis.ConnectionError:
        # Log error and allow request (fail open)
        logger.error(f"Redis connection failed for rate limit check: {key}")
        self.metrics.increment("rate_limit_redis_errors")
        return True  # Fail open for availability
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Rate limit check failed: {e}", exc_info=True)
        self.metrics.increment("rate_limit_unexpected_errors")
        return True  # Fail open
```

## Best Practices

1. **Strategy Selection**: Choose appropriate strategy for use case
   - Sliding window: Precise rate limiting
   - Token bucket: Burst-friendly limiting  
   - Adaptive: Dynamic adjustment based on performance

2. **Redis Optimization**: Use pipelines for batch operations

3. **Error Handling**: Implement graceful degradation (fail open)

4. **Monitoring**: Track rate limit metrics and Redis health

5. **Configuration**: Use environment-based configuration

6. **Testing**: Test with real Redis and mock strategies separately

7. **Performance**: Use connection pooling and efficient Redis operations

8. **Security**: Validate all inputs and sanitize keys

9. **Scalability**: Design for distributed deployment

10. **Documentation**: Document rate limit policies and thresholds

## Troubleshooting

### Common Issues

**High Redis Memory Usage**
```bash
# Check Redis memory usage
redis-cli info memory

# Monitor rate limit keys
redis-cli --scan --pattern "bucket:*" | head -10
redis-cli --scan --pattern "session:*" | head -10

# Clean expired keys
redis-cli eval "return redis.call('del', unpack(redis.call('keys', ARGV[1])))" 0 "expired_key_pattern:*"
```

**Rate Limiting Not Working**
```bash
# Check Redis connectivity
redis-cli ping

# Verify rate limit configuration
python -c "
from src.rate_limiting.limiter import GlobalRateLimiter
from redis import Redis
client = Redis()
limiter = GlobalRateLimiter(client)
print('Rate limiter created successfully')
"

# Test rate limiting manually
python -c "
import asyncio
from src.rate_limiting.limiter import GlobalRateLimiter
from redis import Redis

async def test():
    client = Redis()
    limiter = GlobalRateLimiter(client, default_limit=2, default_window=60)
    
    for i in range(5):
        allowed = await limiter.is_allowed('test')
        print(f'Request {i+1}: {allowed}')

asyncio.run(test())
"
```

**Performance Issues**
```bash
# Monitor Redis performance
redis-cli --latency-history -h localhost -p 6379

# Check connection pool usage
redis-cli info clients

# Profile rate limiting performance
python -m cProfile -s cumtime your_rate_limit_test.py
```

### Development Commands
```bash
# Test individual strategies
uv run pytest src/rate_limiting/tests/test_sliding_window_strategy.py -v

# Test integration with Redis
uv run pytest src/rate_limiting/tests/test_integration.py -v

# Performance testing
uv run pytest src/rate_limiting/tests/ -m "performance" -v

# Check Redis configuration
redis-cli config get "*max*"
```