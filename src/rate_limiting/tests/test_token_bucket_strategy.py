"""Tests for TokenBucketStrategy - Token bucket rate limiting algorithm."""

import time
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
import redis

from src.rate_limiting.strategies.token_bucket import TokenBucketStrategy


@pytest.mark.unit
class TestTokenBucketStrategyInitialization:
    """Test TokenBucketStrategy initialization."""

    def test_initialization_with_sync_redis(self):
        """Test initialization with synchronous Redis client."""
        mock_redis = Mock(spec=redis.Redis)
        strategy = TokenBucketStrategy(mock_redis)

        assert strategy.redis == mock_redis
        assert strategy.is_async is False

    def test_initialization_with_async_redis(self):
        """Test initialization with asynchronous Redis client."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        strategy = TokenBucketStrategy(mock_redis)

        assert strategy.redis == mock_redis
        assert strategy.is_async is True

    def test_initialization_stores_client_reference(self):
        """Test that initialization properly stores client reference."""
        mock_redis = Mock(spec=redis.Redis)
        strategy = TokenBucketStrategy(mock_redis)

        assert strategy.redis is mock_redis


@pytest.mark.unit
class TestTokenBucketStrategyIsAllowed:
    """Test TokenBucketStrategy is_allowed method."""

    def test_is_allowed_sync_first_request(self):
        """Test first request is allowed (bucket starts full)."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]  # No existing bucket data
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Verify bucket state was updated
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 9.0,  # 10 - 1 (consumed)
            "last_refill": 1000.0
        })
        mock_redis.expire.assert_called_once_with("bucket:test_key", 120)  # window * 2

    def test_is_allowed_sync_with_existing_bucket(self):
        """Test request with existing bucket data."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["7.5", "950.0"]  # 7.5 tokens, last refill at 950
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 950.0 = 50.0
        # tokens_to_add = (50.0 / 60) * 10 = 8.33
        # new_tokens = min(10, 7.5 + 8.33) = 10.0
        # after consumption = 10.0 - 1.0 = 9.0
        
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 9.0,
            "last_refill": 1000.0
        })

    def test_is_allowed_sync_bucket_empty(self):
        """Test request denied when bucket is empty."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.5", "990.0"]  # 0.5 tokens, recent refill
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 990.0 = 10.0
        # tokens_to_add = (10.0 / 60) * 10 = 1.67
        # new_tokens = min(10, 0.5 + 1.67) = 2.17
        # 2.17 >= 1.0, so should be allowed
        assert result is True
        
        # Verify bucket state was updated with consumed token
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 1.17,  # 2.17 - 1.0 (consumed)
            "last_refill": 1000.0
        })
        
    def test_is_allowed_sync_bucket_empty_no_time_elapsed(self):
        """Test request denied when bucket is empty and no time elapsed."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.5", "1000.0"]  # 0.5 tokens, no time elapsed
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is False
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 1000.0 = 0.0
        # tokens_to_add = (0.0 / 60) * 10 = 0.0
        # new_tokens = min(10, 0.5 + 0.0) = 0.5
        # 0.5 < 1.0, so request denied
        
        # Verify bucket state was still updated
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 0.5,  # No change
            "last_refill": 1000.0
        })

    def test_is_allowed_sync_bucket_refill_after_time(self):
        """Test bucket refill after time has elapsed."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["2.0", "940.0"]  # 2 tokens, 60 seconds ago
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 940.0 = 60.0
        # tokens_to_add = (60.0 / 60) * 10 = 10.0
        # new_tokens = min(10, 2.0 + 10.0) = 10.0 (capped at limit)
        # after consumption = 10.0 - 1.0 = 9.0
        
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 9.0,
            "last_refill": 1000.0
        })

    def test_is_allowed_sync_redis_error_fail_open(self):
        """Test that Redis errors cause fail-open behavior."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.side_effect = redis.RedisError("Connection failed")
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True  # Fail open

    def test_is_allowed_sync_with_async_client_raises_error(self):
        """Test that using sync method with async client raises error."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        strategy = TokenBucketStrategy(mock_redis)
        
        with pytest.raises(ValueError, match="Use is_allowed_async"):
            strategy.is_allowed("test_key", 10, 60)

    def test_is_allowed_sync_key_format(self):
        """Test that bucket key is formatted correctly."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            strategy.is_allowed("test_key", 10, 60)
        
        # Verify bucket key format
        mock_redis.hmget.assert_called_once_with("bucket:test_key", "tokens", "last_refill")
        
        # Verify update uses same key
        args, kwargs = mock_redis.hmset.call_args
        assert args[0] == "bucket:test_key"


@pytest.mark.unit
class TestTokenBucketStrategyIsAllowedAsync:
    """Test TokenBucketStrategy is_allowed_async method."""

    @pytest.mark.asyncio
    async def test_is_allowed_async_first_request(self):
        """Test first request is allowed (async)."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget = AsyncMock(return_value=[None, None])
        mock_redis.hmset = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # Verify bucket state was updated
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 9.0,
            "last_refill": 1000.0
        })
        mock_redis.expire.assert_called_once_with("bucket:test_key", 120)

    @pytest.mark.asyncio
    async def test_is_allowed_async_with_existing_bucket(self):
        """Test request with existing bucket data (async)."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget = AsyncMock(return_value=["5.0", "970.0"])
        mock_redis.hmset = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 970.0 = 30.0
        # tokens_to_add = (30.0 / 60) * 10 = 5.0
        # new_tokens = min(10, 5.0 + 5.0) = 10.0
        # after consumption = 10.0 - 1.0 = 9.0
        
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 9.0,
            "last_refill": 1000.0
        })

    @pytest.mark.asyncio
    async def test_is_allowed_async_bucket_empty(self):
        """Test request denied when bucket is empty (async)."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget = AsyncMock(return_value=["0.3", "1000.0"])  # 0.3 tokens, no time elapsed
        mock_redis.hmset = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is False
        
        # Verify bucket state was still updated
        mock_redis.hmset.assert_called_once_with("bucket:test_key", {
            "tokens": 0.3,
            "last_refill": 1000.0
        })

    @pytest.mark.asyncio
    async def test_is_allowed_async_redis_error_fail_open(self):
        """Test that Redis errors cause fail-open behavior (async)."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget.side_effect = Exception("Connection failed")
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True  # Fail open

    @pytest.mark.asyncio
    async def test_is_allowed_async_with_sync_client_raises_error(self):
        """Test that using async method with sync client raises error."""
        mock_redis = Mock(spec=redis.Redis)
        strategy = TokenBucketStrategy(mock_redis)
        
        with pytest.raises(ValueError, match="Use is_allowed.*for sync Redis client"):
            await strategy.is_allowed_async("test_key", 10, 60)


@pytest.mark.unit
class TestTokenBucketStrategyGetStats:
    """Test TokenBucketStrategy get_stats method."""

    def test_get_stats_success(self):
        """Test successful stats retrieval."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["7.5", "950.0"]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "token_bucket"
        assert stats["current_tokens"] == 7.5
        assert stats["last_refill"] == datetime.fromtimestamp(950.0).isoformat()
        assert stats["time_since_refill"] == 50.0

    def test_get_stats_no_bucket_data(self):
        """Test stats with no existing bucket data."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "token_bucket"
        assert stats["current_tokens"] == 0.0
        assert stats["last_refill"] == datetime.fromtimestamp(1000.0).isoformat()
        assert stats["time_since_refill"] == 0.0

    def test_get_stats_partial_bucket_data(self):
        """Test stats with partial bucket data."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["3.2", None]  # Tokens but no refill time
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "token_bucket"
        assert stats["current_tokens"] == 3.2
        assert stats["last_refill"] == datetime.fromtimestamp(1000.0).isoformat()
        assert stats["time_since_refill"] == 0.0

    def test_get_stats_redis_error(self):
        """Test stats retrieval with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.side_effect = redis.RedisError("Connection failed")
        
        strategy = TokenBucketStrategy(mock_redis)
        
        stats = strategy.get_stats("test_key")
        
        assert "error" in stats
        assert stats["error"] == "Connection failed"

    def test_get_stats_correct_redis_calls(self):
        """Test that get_stats makes correct Redis calls."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["5.0", "900.0"]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        strategy.get_stats("test_key")
        
        mock_redis.hmget.assert_called_once_with("bucket:test_key", "tokens", "last_refill")


@pytest.mark.unit
class TestTokenBucketStrategyReset:
    """Test TokenBucketStrategy reset method."""

    def test_reset_success(self):
        """Test successful reset."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 1  # Key existed and was deleted
        
        strategy = TokenBucketStrategy(mock_redis)
        
        result = strategy.reset("test_key")
        
        assert result is True
        mock_redis.delete.assert_called_once_with("bucket:test_key")

    def test_reset_key_not_found(self):
        """Test reset when key doesn't exist."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 0  # Key didn't exist
        
        strategy = TokenBucketStrategy(mock_redis)
        
        result = strategy.reset("test_key")
        
        assert result is False
        mock_redis.delete.assert_called_once_with("bucket:test_key")

    def test_reset_redis_error(self):
        """Test reset with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.side_effect = redis.RedisError("Connection failed")
        
        strategy = TokenBucketStrategy(mock_redis)
        
        result = strategy.reset("test_key")
        
        assert result is False


@pytest.mark.unit
class TestTokenBucketStrategyTokenCalculations:
    """Test TokenBucketStrategy token calculations."""

    def test_token_refill_calculation(self):
        """Test token refill calculation accuracy."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["3.0", "940.0"]  # 3 tokens, 60 seconds ago
        
        strategy = TokenBucketStrategy(mock_redis)
        
        # Test with different time intervals
        test_cases = [
            (1000.0, 60.0, 10.0),  # 60 seconds elapsed, 10 token limit
            (970.0, 30.0, 10.0),   # 30 seconds elapsed, 10 token limit
            (1060.0, 120.0, 10.0), # 120 seconds elapsed, 10 token limit
        ]
        
        for current_time, elapsed_time, limit in test_cases:
            with patch('time.time', return_value=current_time):
                strategy.is_allowed("test_key", limit, 60)
                
                # Calculate expected tokens
                tokens_to_add = (elapsed_time / 60) * limit
                expected_tokens = min(limit, 3.0 + tokens_to_add)
                
                if expected_tokens >= 1.0:
                    expected_tokens -= 1.0  # Consume one token
                
                args, kwargs = mock_redis.hmset.call_args
                assert args[1]["tokens"] == expected_tokens

    def test_token_bucket_capacity_limit(self):
        """Test that token bucket respects capacity limit."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["8.0", "880.0"]  # 8 tokens, 120 seconds ago
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 880.0 = 120.0
        # tokens_to_add = (120.0 / 60) * 10 = 20.0
        # new_tokens = min(10, 8.0 + 20.0) = 10.0 (capped at limit)
        # after consumption = 10.0 - 1.0 = 9.0
        
        args, kwargs = mock_redis.hmset.call_args
        assert args[1]["tokens"] == 9.0

    def test_fractional_tokens(self):
        """Test handling of fractional tokens."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.7", "990.0"]  # 0.7 tokens, 10 seconds ago
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        # Calculate expected tokens:
        # time_elapsed = 1000.0 - 990.0 = 10.0
        # tokens_to_add = (10.0 / 60) * 10 = 1.67
        # new_tokens = min(10, 0.7 + 1.67) = 2.37
        # 2.37 >= 1.0, so allowed
        # after consumption = 2.37 - 1.0 = 1.37
        
        assert result is True
        
        args, kwargs = mock_redis.hmset.call_args
        expected_tokens = 1.37
        assert abs(args[1]["tokens"] - expected_tokens) < 0.01  # Float precision

    def test_zero_elapsed_time(self):
        """Test behavior when no time has elapsed."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["5.0", "1000.0"]  # 5 tokens, no time elapsed
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # No tokens added, just consumed
        args, kwargs = mock_redis.hmset.call_args
        assert args[1]["tokens"] == 4.0  # 5.0 - 1.0


@pytest.mark.unit
class TestTokenBucketStrategyEdgeCases:
    """Test TokenBucketStrategy edge cases."""

    def test_zero_limit(self):
        """Test behavior with zero limit."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 0, 60)
        
        assert result is False  # 0 tokens, can't fulfill request

    def test_zero_window(self):
        """Test behavior with zero window."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.0", "990.0"]  # 0 tokens, 10 seconds ago
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            # Zero window should cause division by zero
            with pytest.raises(ZeroDivisionError):
                strategy.is_allowed("test_key", 10, 0)

    def test_large_time_gap(self):
        """Test behavior with very large time gap."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["1.0", "1.0"]  # 1 token, very old timestamp
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000000.0):  # Large current time
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Should refill to full capacity
        args, kwargs = mock_redis.hmset.call_args
        assert args[1]["tokens"] == 9.0  # Full bucket minus consumed token

    def test_negative_time_difference(self):
        """Test behavior with negative time difference (clock skew)."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["5.0", "1100.0"]  # Future timestamp
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):  # Current time is in the past
            result = strategy.is_allowed("test_key", 10, 60)
        
        # Negative time elapsed should reduce tokens
        # tokens_to_add = (1000.0 - 1100.0) / 60 * 10 = -16.67
        # new_tokens = min(10, 5.0 + (-16.67)) = min(10, -11.67) = -11.67
        # Since -11.67 < 1.0, request should be denied
        
        assert result is False  # Should be denied due to negative tokens
        
        args, kwargs = mock_redis.hmset.call_args
        assert args[1]["tokens"] < 0  # Negative tokens


@pytest.mark.unit
@pytest.mark.integration
class TestTokenBucketStrategyIntegration:
    """Integration tests for TokenBucketStrategy."""

    def test_burst_traffic_handling(self):
        """Test handling of burst traffic."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Start with full bucket
        mock_redis.hmget.side_effect = [
            ["10.0", "1000.0"],  # Full bucket
            ["9.0", "1000.0"],   # After 1st request
            ["8.0", "1000.0"],   # After 2nd request
            ["7.0", "1000.0"],   # After 3rd request
        ]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            # Multiple rapid requests
            results = []
            for i in range(4):
                result = strategy.is_allowed("test_key", 10, 60)
                results.append(result)
        
        # All should be allowed due to burst capacity
        assert all(results)
        
        # Verify all requests were processed
        assert mock_redis.hmset.call_count == 4

    def test_sustained_traffic_rate_limiting(self):
        """Test sustained traffic rate limiting."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Simulate sustained traffic over time
        mock_redis.hmget.side_effect = [
            ["10.0", "1000.0"],  # Full bucket
            ["9.0", "1000.0"],   # After 1st request
            ["8.0", "1000.0"],   # After 2nd request
            ["7.0", "1000.0"],   # After 3rd request
            ["6.0", "1000.0"],   # After 4th request
            ["5.0", "1000.0"],   # After 5th request
            ["4.0", "1000.0"],   # After 6th request
            ["3.0", "1000.0"],   # After 7th request
            ["2.0", "1000.0"],   # After 8th request
            ["1.0", "1000.0"],   # After 9th request
            ["0.0", "1000.0"],   # After 10th request
            ["0.0", "1000.0"],   # 11th request - should be denied
        ]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            results = []
            for i in range(12):
                result = strategy.is_allowed("test_key", 10, 60)
                results.append(result)
        
        # First 10 should be allowed, 11th and 12th should be denied
        assert results[:10] == [True] * 10
        assert results[10:] == [False] * 2

    def test_refill_over_time(self):
        """Test token refill over time."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Test sequence: empty bucket -> time passes -> refill -> allow
        mock_redis.hmget.side_effect = [
            ["0.0", "1000.0"],   # Empty bucket
            ["1.0", "1060.0"],   # After 60 seconds, 1 token added
        ]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        # First request should be denied (empty bucket)
        with patch('time.time', return_value=1000.0):
            result1 = strategy.is_allowed("test_key", 10, 60)
        
        assert result1 is False
        
        # Second request after 60 seconds should be allowed
        with patch('time.time', return_value=1060.0):
            result2 = strategy.is_allowed("test_key", 10, 60)
        
        assert result2 is True

    def test_key_isolation(self):
        """Test that different keys maintain separate buckets."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]  # No existing data
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result1 = strategy.is_allowed("key1", 10, 60)
            result2 = strategy.is_allowed("key2", 10, 60)
        
        assert result1 is True
        assert result2 is True
        
        # Verify different bucket keys were used
        calls = mock_redis.hmget.call_args_list
        assert calls[0][0][0] == "bucket:key1"
        assert calls[1][0][0] == "bucket:key2"

    def test_complete_workflow(self):
        """Test complete token bucket workflow."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Simulate complete workflow
        mock_redis.hmget.side_effect = [
            [None, None],         # First request - new bucket
            ["9.0", "1000.0"],    # Second request - existing bucket
            ["8.0", "1000.0"],    # Third request - bucket draining
        ]
        
        strategy = TokenBucketStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            # First request - initializes bucket
            result1 = strategy.is_allowed("test_key", 10, 60)
            assert result1 is True
            
            # Second request - uses existing bucket
            result2 = strategy.is_allowed("test_key", 10, 60)
            assert result2 is True
            
            # Third request - continues draining
            result3 = strategy.is_allowed("test_key", 10, 60)
            assert result3 is True
        
        # Verify all requests were processed
        assert mock_redis.hmset.call_count == 3
        assert mock_redis.expire.call_count == 3