"""Tests for SlidingWindowStrategy - Sliding window rate limiting algorithm."""

import time
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
import redis

from src.rate_limiting.strategies.sliding_window import SlidingWindowStrategy


@pytest.mark.unit
class TestSlidingWindowStrategyInitialization:
    """Test SlidingWindowStrategy initialization."""

    def test_initialization_with_sync_redis(self):
        """Test initialization with synchronous Redis client."""
        mock_redis = Mock(spec=redis.Redis)
        strategy = SlidingWindowStrategy(mock_redis)

        assert strategy.redis == mock_redis
        assert strategy.is_async is False

    def test_initialization_with_async_redis(self):
        """Test initialization with asynchronous Redis client."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        strategy = SlidingWindowStrategy(mock_redis)

        assert strategy.redis == mock_redis
        assert strategy.is_async is True

    def test_initialization_stores_client_reference(self):
        """Test that initialization properly stores client reference."""
        mock_redis = Mock(spec=redis.Redis)
        strategy = SlidingWindowStrategy(mock_redis)

        assert strategy.redis is mock_redis


@pytest.mark.unit
class TestSlidingWindowStrategyIsAllowed:
    """Test SlidingWindowStrategy is_allowed method."""

    def test_is_allowed_sync_first_request(self):
        """Test first request is allowed."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Mock pipeline operations
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 0]  # zremrangebyscore result, zcard result
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Verify pipeline operations
        mock_pipe.zremrangebyscore.assert_called_once_with("test_key", 0, 940.0)  # 1000 - 60
        mock_pipe.zcard.assert_called_once_with("test_key")
        mock_pipe.execute.assert_called_once()
        
        # Verify request was added
        mock_redis.zadd.assert_called_once_with("test_key", {"1000.0": 1000.0})
        mock_redis.expire.assert_called_once_with("test_key", 120)  # window * 2

    def test_is_allowed_sync_under_limit(self):
        """Test request allowed when under limit."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Mock pipeline operations - 5 requests in window
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 5]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Verify request was added
        mock_redis.zadd.assert_called_once_with("test_key", {"1000.0": 1000.0})

    def test_is_allowed_sync_at_limit(self):
        """Test request denied when at limit."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Mock pipeline operations - 10 requests in window (at limit)
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 10]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is False
        
        # Verify request was NOT added
        mock_redis.zadd.assert_not_called()

    def test_is_allowed_sync_over_limit(self):
        """Test request denied when over limit."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Mock pipeline operations - 15 requests in window (over limit)
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 15]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is False
        
        # Verify request was NOT added
        mock_redis.zadd.assert_not_called()

    def test_is_allowed_sync_redis_error_fail_open(self):
        """Test that Redis errors cause fail-open behavior."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.pipeline.side_effect = redis.RedisError("Connection failed")
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True  # Fail open

    def test_is_allowed_sync_with_async_client_raises_error(self):
        """Test that using sync method with async client raises error."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        strategy = SlidingWindowStrategy(mock_redis)
        
        with pytest.raises(ValueError, match="Use is_allowed_async"):
            strategy.is_allowed("test_key", 10, 60)

    def test_is_allowed_sync_window_calculations(self):
        """Test correct window calculations."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Mock pipeline operations
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 3]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        current_time = 1500.0
        window = 300  # 5 minutes
        expected_window_start = current_time - window  # 1200.0
        
        with patch('time.time', return_value=current_time):
            strategy.is_allowed("test_key", 10, window)
        
        # Verify window start calculation
        mock_pipe.zremrangebyscore.assert_called_once_with("test_key", 0, expected_window_start)


@pytest.mark.unit
class TestSlidingWindowStrategyIsAllowedAsync:
    """Test SlidingWindowStrategy is_allowed_async method."""

    @pytest.mark.asyncio
    async def test_is_allowed_async_first_request(self):
        """Test first request is allowed (async)."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        
        # Mock pipeline operations
        mock_pipe = Mock()
        mock_pipe.execute = AsyncMock(return_value=[None, 0])  # zremrangebyscore result, zcard result
        mock_redis.pipeline.return_value = mock_pipe
        
        # Mock async zadd and expire
        mock_redis.zadd = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # Verify pipeline operations
        mock_pipe.zremrangebyscore.assert_called_once_with("test_key", 0, 940.0)
        mock_pipe.zcard.assert_called_once_with("test_key")
        mock_pipe.execute.assert_called_once()
        
        # Verify request was added
        mock_redis.zadd.assert_called_once_with("test_key", {"1000.0": 1000.0})
        mock_redis.expire.assert_called_once_with("test_key", 120)

    @pytest.mark.asyncio
    async def test_is_allowed_async_under_limit(self):
        """Test request allowed when under limit (async)."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        
        # Mock pipeline operations - 7 requests in window
        mock_pipe = Mock()
        mock_pipe.execute = AsyncMock(return_value=[None, 7])
        mock_redis.pipeline.return_value = mock_pipe
        
        # Mock async zadd and expire
        mock_redis.zadd = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # Verify request was added
        mock_redis.zadd.assert_called_once_with("test_key", {"1000.0": 1000.0})

    @pytest.mark.asyncio
    async def test_is_allowed_async_at_limit(self):
        """Test request denied when at limit (async)."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        
        # Mock pipeline operations - 10 requests in window (at limit)
        mock_pipe = Mock()
        mock_pipe.execute = AsyncMock(return_value=[None, 10])
        mock_redis.pipeline.return_value = mock_pipe
        
        # Mock async zadd and expire
        mock_redis.zadd = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is False
        
        # Verify request was NOT added
        mock_redis.zadd.assert_not_called()

    @pytest.mark.asyncio
    async def test_is_allowed_async_redis_error_fail_open(self):
        """Test that Redis errors cause fail-open behavior (async)."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.pipeline.side_effect = Exception("Connection failed")
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True  # Fail open

    @pytest.mark.asyncio
    async def test_is_allowed_async_with_sync_client_raises_error(self):
        """Test that using async method with sync client raises error."""
        mock_redis = Mock(spec=redis.Redis)
        strategy = SlidingWindowStrategy(mock_redis)
        
        with pytest.raises(ValueError, match="Use is_allowed.*for sync Redis client"):
            await strategy.is_allowed_async("test_key", 10, 60)


@pytest.mark.unit
class TestSlidingWindowStrategyGetStats:
    """Test SlidingWindowStrategy get_stats method."""

    def test_get_stats_success(self):
        """Test successful stats retrieval."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zcard.return_value = 7
        mock_redis.zrange.return_value = [("1000.0", 1000.0)]
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1060.0):
            stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "sliding_window"
        assert stats["current_requests"] == 7
        assert stats["window_start"] == datetime.fromtimestamp(1000.0).isoformat()
        assert stats["window_end"] == datetime.fromtimestamp(1060.0).isoformat()
        assert stats["window_duration"] == 60.0

    def test_get_stats_empty_window(self):
        """Test stats with empty window."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zcard.return_value = 0
        mock_redis.zrange.return_value = []
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1060.0):
            stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "sliding_window"
        assert stats["current_requests"] == 0
        assert stats["window_start"] == datetime.fromtimestamp(1060.0).isoformat()
        assert stats["window_duration"] == 0.0

    def test_get_stats_redis_error(self):
        """Test stats retrieval with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zcard.side_effect = redis.RedisError("Connection failed")
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        stats = strategy.get_stats("test_key")
        
        assert "error" in stats
        assert stats["error"] == "Connection failed"

    def test_get_stats_correct_redis_calls(self):
        """Test that get_stats makes correct Redis calls."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zcard.return_value = 5
        mock_redis.zrange.return_value = [("950.0", 950.0)]
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        strategy.get_stats("test_key")
        
        mock_redis.zcard.assert_called_once_with("test_key")
        mock_redis.zrange.assert_called_once_with("test_key", 0, 0, withscores=True)


@pytest.mark.unit
class TestSlidingWindowStrategyReset:
    """Test SlidingWindowStrategy reset method."""

    def test_reset_success(self):
        """Test successful reset."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 1  # Key existed and was deleted
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        result = strategy.reset("test_key")
        
        assert result is True
        mock_redis.delete.assert_called_once_with("test_key")

    def test_reset_key_not_found(self):
        """Test reset when key doesn't exist."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 0  # Key didn't exist
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        result = strategy.reset("test_key")
        
        assert result is False
        mock_redis.delete.assert_called_once_with("test_key")

    def test_reset_redis_error(self):
        """Test reset with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.side_effect = redis.RedisError("Connection failed")
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        result = strategy.reset("test_key")
        
        assert result is False


@pytest.mark.unit
class TestSlidingWindowStrategyEdgeCases:
    """Test SlidingWindowStrategy edge cases."""

    def test_zero_limit(self):
        """Test behavior with zero limit."""
        mock_redis = Mock(spec=redis.Redis)
        
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 0]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 0, 60)
        
        assert result is False  # 0 < 0 is False

    def test_zero_window(self):
        """Test behavior with zero window."""
        mock_redis = Mock(spec=redis.Redis)
        
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 0]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 10, 0)
        
        assert result is True
        
        # Verify window start calculation (all entries should be removed)
        mock_pipe.zremrangebyscore.assert_called_once_with("test_key", 0, 1000.0)

    def test_large_window(self):
        """Test behavior with large window."""
        mock_redis = Mock(spec=redis.Redis)
        
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 5]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        large_window = 86400  # 24 hours
        current_time = 1000000.0
        
        with patch('time.time', return_value=current_time):
            result = strategy.is_allowed("test_key", 10, large_window)
        
        assert result is True
        
        # Verify TTL is set correctly (2 * window)
        mock_redis.expire.assert_called_once_with("test_key", large_window * 2)

    def test_very_high_limit(self):
        """Test behavior with very high limit."""
        mock_redis = Mock(spec=redis.Redis)
        
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 999999]  # High current count
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result = strategy.is_allowed("test_key", 1000000, 60)  # Very high limit
        
        assert result is True  # 999999 < 1000000


@pytest.mark.unit
@pytest.mark.integration
class TestSlidingWindowStrategyIntegration:
    """Integration tests for SlidingWindowStrategy."""

    def test_multiple_requests_sequence(self):
        """Test sequence of multiple requests."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Mock pipeline for multiple calls
        mock_pipe = Mock()
        # First call: 0 requests, second call: 1 request, third call: 2 requests
        mock_pipe.execute.side_effect = [[None, 0], [None, 1], [None, 2]]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            # First request - should be allowed
            result1 = strategy.is_allowed("test_key", 3, 60)
            assert result1 is True
            
            # Second request - should be allowed
            result2 = strategy.is_allowed("test_key", 3, 60)
            assert result2 is True
            
            # Third request - should be allowed
            result3 = strategy.is_allowed("test_key", 3, 60)
            assert result3 is True
        
        # Verify all requests were added
        assert mock_redis.zadd.call_count == 3

    def test_window_expiration_cleanup(self):
        """Test that expired entries are properly cleaned up."""
        mock_redis = Mock(spec=redis.Redis)
        
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 2]  # 2 requests after cleanup
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        current_time = 2000.0
        window = 300  # 5 minutes
        window_start = current_time - window  # 1700.0
        
        with patch('time.time', return_value=current_time):
            strategy.is_allowed("test_key", 10, window)
        
        # Verify cleanup call
        mock_pipe.zremrangebyscore.assert_called_once_with("test_key", 0, window_start)

    def test_concurrent_access_simulation(self):
        """Test behavior under simulated concurrent access."""
        mock_redis = Mock(spec=redis.Redis)
        
        # Simulate varying request counts
        mock_pipe = Mock()
        mock_pipe.execute.side_effect = [
            [None, 5],  # First request
            [None, 6],  # Second request
            [None, 9],  # Third request
            [None, 10]  # Fourth request - at limit
        ]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            results = []
            for _ in range(4):
                result = strategy.is_allowed("test_key", 10, 60)
                results.append(result)
        
        # First 3 should be allowed, 4th should be denied
        assert results == [True, True, True, False]
        
        # Verify only 3 requests were added (4th was denied)
        assert mock_redis.zadd.call_count == 3

    def test_key_isolation(self):
        """Test that different keys are isolated."""
        mock_redis = Mock(spec=redis.Redis)
        
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 0]
        mock_redis.pipeline.return_value = mock_pipe
        
        strategy = SlidingWindowStrategy(mock_redis)
        
        with patch('time.time', return_value=1000.0):
            result1 = strategy.is_allowed("key1", 10, 60)
            result2 = strategy.is_allowed("key2", 10, 60)
        
        assert result1 is True
        assert result2 is True
        
        # Verify different keys were used
        calls = mock_pipe.zremrangebyscore.call_args_list
        assert calls[0][0][0] == "key1"
        assert calls[1][0][0] == "key2"