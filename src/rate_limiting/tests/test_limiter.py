"""Tests for Redis-based rate limiter."""

import time
from unittest.mock import MagicMock, patch

import pytest
import redis

from src.rate_limiting.limiter import (
    GlobalRateLimiter,
    RateLimitConfig,
    RateLimitExceeded,
    create_rate_limiter,
)


@pytest.mark.unit
class TestRateLimitConfig:
    """Test rate limit configuration model."""

    def test_default_config(self):
        """Test default configuration values."""
        config = RateLimitConfig()

        assert config.requests_per_second == 10
        assert config.burst_allowance == 5
        assert config.window_size == 1
        assert config.max_retries == 3
        assert config.backoff_multiplier == 1.5
        assert config.redis_key_prefix == "sora_rate_limit"
        assert config.redis_key_ttl == 60

    def test_custom_config(self):
        """Test custom configuration values."""
        config = RateLimitConfig(
            requests_per_second=20,
            burst_allowance=10,
            window_size=2,
            max_retries=5,
            backoff_multiplier=2.0,
            redis_key_prefix="custom_prefix",
            redis_key_ttl=120
        )

        assert config.requests_per_second == 20
        assert config.burst_allowance == 10
        assert config.window_size == 2
        assert config.max_retries == 5
        assert config.backoff_multiplier == 2.0
        assert config.redis_key_prefix == "custom_prefix"
        assert config.redis_key_ttl == 120


@pytest.mark.unit
class TestRateLimitExceeded:
    """Test rate limit exceeded exception."""

    def test_exception_creation(self):
        """Test exception creation with retry_after."""
        exc = RateLimitExceeded(retry_after=1.5)

        assert exc.retry_after == 1.5
        assert "1.50 seconds" in str(exc)

    def test_exception_with_custom_message(self):
        """Test exception with custom message."""
        exc = RateLimitExceeded(retry_after=2.0, message="Custom rate limit message")

        assert exc.retry_after == 2.0
        assert "Custom rate limit message" in str(exc)
        assert "2.00 seconds" in str(exc)


@pytest.mark.integration
class TestGlobalRateLimiter:
    """Test global rate limiter implementation."""

    @pytest.fixture
    def mock_redis(self):
        """Create mock Redis client."""
        mock_redis = MagicMock()
        mock_redis.pipeline.return_value = mock_redis
        mock_redis.execute.return_value = [None, 5, None, None]  # Current count: 5
        return mock_redis

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return RateLimitConfig(requests_per_second=10)

    def test_sync_limiter_initialization(self, mock_redis, config):
        """Test synchronous rate limiter initialization."""
        limiter = GlobalRateLimiter(mock_redis, config)

        assert limiter.redis == mock_redis
        assert limiter.config == config
        assert limiter.is_async is False
        assert limiter._min_interval == 0.1  # 1/10 requests per second

    def test_async_limiter_detection(self, config):
        """Test async Redis client detection."""
        # Mock async Redis client
        mock_async_redis = MagicMock()
        mock_async_redis.__class__.__name__ = 'Redis'  # Simulate aioredis.Redis

        with patch('src.rate_limiting.limiter.aioredis') as mock_aioredis:
            mock_aioredis.Redis = type(mock_async_redis)

            limiter = GlobalRateLimiter(mock_async_redis, config)
            # Note: This test is simplified since we can't easily mock isinstance check
            assert limiter.redis == mock_async_redis

    def test_acquire_success(self, mock_redis, config):
        """Test successful token acquisition."""
        # Mock Redis to return count under limit
        mock_redis.execute.return_value = [None, 5, None, None]  # Under limit of 10

        limiter = GlobalRateLimiter(mock_redis, config)
        result = limiter.acquire(timeout=5)

        assert result is True
        mock_redis.pipeline.assert_called()
        mock_redis.execute.assert_called()

    def test_acquire_rate_limit_exceeded(self, mock_redis, config):
        """Test rate limit exceeded."""
        # Mock Redis to return count at limit
        mock_redis.execute.return_value = [None, 10, None, None]  # At limit of 10

        limiter = GlobalRateLimiter(mock_redis, config)

        with patch('time.sleep') as mock_sleep:
            with pytest.raises(RateLimitExceeded):
                limiter.acquire(timeout=1)  # Short timeout to fail quickly

            # Should have attempted retries
            assert mock_sleep.call_count > 0

    def test_acquire_timeout(self, mock_redis, config):
        """Test acquisition timeout."""
        # Mock Redis to always return count at limit
        mock_redis.execute.return_value = [None, 10, None, None]

        limiter = GlobalRateLimiter(mock_redis, config)

        with patch('time.sleep'):
            result = limiter.acquire(timeout=0.1)  # Very short timeout
            assert result is False

    def test_acquire_redis_error_fallback(self, mock_redis, config):
        """Test fallback behavior on Redis error."""
        # Mock Redis to raise an error
        mock_redis.execute.side_effect = redis.RedisError("Connection failed")

        limiter = GlobalRateLimiter(mock_redis, config)
        result = limiter.acquire()

        # Should fall back to allowing request
        assert result is True

    def test_calculate_retry_delay(self, mock_redis, config):
        """Test retry delay calculation."""
        limiter = GlobalRateLimiter(mock_redis, config)

        # Test exponential backoff
        delay1 = limiter._calculate_retry_delay(1)
        delay2 = limiter._calculate_retry_delay(2)
        delay3 = limiter._calculate_retry_delay(3)

        assert delay1 < delay2 < delay3
        assert delay3 <= 5.0  # Should be capped at 5 seconds

    def test_get_current_usage(self, mock_redis, config):
        """Test current usage statistics."""
        # Mock Redis to return current count
        mock_redis.zcount.return_value = 7

        limiter = GlobalRateLimiter(mock_redis, config)
        stats = limiter.get_current_usage()

        assert stats['current_requests'] == 7
        assert stats['max_requests'] == 10
        assert stats['utilization_percent'] == 70.0
        assert stats['requests_available'] == 3
        assert 'window_start' in stats
        assert 'window_end' in stats

    def test_get_current_usage_redis_error(self, mock_redis, config):
        """Test current usage statistics with Redis error."""
        mock_redis.zcount.side_effect = redis.RedisError("Connection failed")

        limiter = GlobalRateLimiter(mock_redis, config)
        stats = limiter.get_current_usage()

        assert 'error' in stats
        assert stats['error'] == "Connection failed"


@pytest.mark.unit
class TestRateLimiterCreation:
    """Test rate limiter factory functions."""

    @patch('redis.from_url')
    @patch('src.rate_limiting.limiter.safe_int_from_env')
    def test_create_rate_limiter_success(self, mock_safe_int, mock_redis_from_url):
        """Test successful rate limiter creation."""
        # Mock environment configuration
        mock_safe_int.return_value = 15

        # Mock Redis client
        mock_redis_client = MagicMock()
        mock_redis_from_url.return_value = mock_redis_client

        limiter = create_rate_limiter("redis://localhost:6379/1")

        assert isinstance(limiter, GlobalRateLimiter)
        assert limiter.config.requests_per_second == 15

        # Verify Redis connection was tested
        mock_redis_client.ping.assert_called_once()

    @patch('redis.from_url')
    def test_create_rate_limiter_connection_error(self, mock_redis_from_url):
        """Test rate limiter creation with Redis connection error."""
        # Mock Redis to raise connection error
        mock_redis_from_url.side_effect = redis.ConnectionError("Cannot connect")

        with pytest.raises(redis.ConnectionError):
            create_rate_limiter("redis://invalid:6379/0")


@pytest.mark.integration
@pytest.mark.integration
class TestRateLimiterIntegration:
    """Integration tests for rate limiter with real Redis."""

    @pytest.fixture
    def redis_client(self):
        """Create real Redis client for integration tests."""
        try:
            client = redis.Redis(host='localhost', port=6379, db=15, decode_responses=True)
            client.ping()  # Test connection
            yield client
            # Cleanup after test
            client.flushdb()
        except redis.ConnectionError:
            pytest.skip("Redis not available for integration tests")

    def test_rate_limiting_enforcement(self, redis_client):
        """Test actual rate limiting enforcement."""
        config = RateLimitConfig(requests_per_second=2)  # Very low limit for testing
        limiter = GlobalRateLimiter(redis_client, config)

        # First 2 requests should succeed
        assert limiter.acquire() is True
        assert limiter.acquire() is True

        # Third request should be blocked
        with patch('time.sleep') as mock_sleep:
            try:
                result = limiter.acquire(timeout=0.1)  # Short timeout
                # If it doesn't raise exception, it should return False
                assert result is False
            except RateLimitExceeded:
                # This is also acceptable behavior
                pass

    def test_rate_limiting_recovery(self, redis_client):
        """Test rate limiting recovery after time window."""
        config = RateLimitConfig(requests_per_second=1, window_size=1)
        limiter = GlobalRateLimiter(redis_client, config)

        # Acquire token
        assert limiter.acquire() is True

        # Wait for window to reset
        time.sleep(1.1)

        # Should be able to acquire again
        assert limiter.acquire() is True

    def test_sliding_window_behavior(self, redis_client):
        """Test sliding window rate limiting behavior."""
        config = RateLimitConfig(requests_per_second=3, window_size=1)
        limiter = GlobalRateLimiter(redis_client, config)

        # Use all tokens quickly
        assert limiter.acquire() is True
        assert limiter.acquire() is True
        assert limiter.acquire() is True

        # Fourth should be blocked
        with patch('time.sleep'):
            try:
                result = limiter.acquire(timeout=0.1)
                assert result is False
            except RateLimitExceeded:
                pass

        # Wait a bit and try again
        time.sleep(0.5)

        # Still should be blocked (sliding window)
        with patch('time.sleep'):
            try:
                result = limiter.acquire(timeout=0.1)
                assert result is False
            except RateLimitExceeded:
                pass

    def test_usage_statistics_accuracy(self, redis_client):
        """Test accuracy of usage statistics."""
        config = RateLimitConfig(requests_per_second=5)
        limiter = GlobalRateLimiter(redis_client, config)

        # Acquire some tokens
        limiter.acquire()
        limiter.acquire()
        limiter.acquire()

        stats = limiter.get_current_usage()

        assert stats['current_requests'] == 3
        assert stats['max_requests'] == 5
        assert stats['utilization_percent'] == 60.0
        assert stats['requests_available'] == 2
