"""Tests for AdaptiveStrategy - Adaptive rate limiting algorithm."""

from unittest.mock import Mock, patch

import pytest
import redis

from src.rate_limiting.strategies.adaptive import AdaptiveStrategy
from src.rate_limiting.strategies.base import RateLimitStrategy


class MockBaseStrategy(RateLimitStrategy):
    """Mock base strategy for testing."""
    
    def __init__(self):
        self.is_allowed_calls = []
        self.is_allowed_async_calls = []
        self.is_allowed_result = True
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        self.is_allowed_calls.append((key, limit, window))
        return self.is_allowed_result
    
    async def is_allowed_async(self, key: str, limit: int, window: int) -> bool:
        self.is_allowed_async_calls.append((key, limit, window))
        return self.is_allowed_result
    
    def get_stats(self, key: str) -> dict:
        return {"strategy": "mock", "test": "data"}
    
    def reset(self, key: str) -> bool:
        return True


@pytest.mark.unit
class TestAdaptiveStrategyInitialization:
    """Test AdaptiveStrategy initialization."""

    def test_initialization_with_sync_redis(self):
        """Test initialization with synchronous Redis client."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)

        assert strategy.redis == mock_redis
        assert strategy.base_strategy == mock_base_strategy
        assert strategy.is_async is False

    def test_initialization_with_async_redis(self):
        """Test initialization with asynchronous Redis client."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)

        assert strategy.redis == mock_redis
        assert strategy.base_strategy == mock_base_strategy
        assert strategy.is_async is True

    def test_initialization_stores_references(self):
        """Test that initialization properly stores references."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)

        assert strategy.redis is mock_redis
        assert strategy.base_strategy is mock_base_strategy


@pytest.mark.unit
class TestAdaptiveStrategyIsAllowed:
    """Test AdaptiveStrategy is_allowed method."""

    def test_is_allowed_with_no_metrics(self):
        """Test is_allowed with no existing metrics."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]  # No metrics
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Verify metrics were fetched
        mock_redis.hmget.assert_called_once_with("metrics:test_key", "error_rate", "response_time")
        
        # Verify base strategy was called with original limit (no adjustment)
        assert len(mock_base_strategy.is_allowed_calls) == 1
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 10, 60)

    def test_is_allowed_with_low_error_rate(self):
        """Test is_allowed with low error rate."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.05", "150.0"]  # 5% error rate, 150ms response
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # With low error rate and fast response, no adjustment (factor = 1.0)
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 10, 60)

    def test_is_allowed_with_high_error_rate(self):
        """Test is_allowed with high error rate."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.15", "200.0"]  # 15% error rate, 200ms response
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # With high error rate (>10%), adjustment factor = 0.5
        # adaptive_limit = max(1, int(10 * 0.5)) = 5
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 5, 60)

    def test_is_allowed_with_slow_response_time(self):
        """Test is_allowed with slow response time."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.05", "3000.0"]  # 5% error rate, 3000ms response
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # With slow response time (>2000ms), adjustment factor = 0.8
        # adaptive_limit = max(1, int(10 * 0.8)) = 8
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 8, 60)

    def test_is_allowed_with_very_slow_response_time(self):
        """Test is_allowed with very slow response time."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.05", "6000.0"]  # 5% error rate, 6000ms response
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # With very slow response time (>5000ms), adjustment factor = 0.7
        # adaptive_limit = max(1, int(10 * 0.7)) = 7
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 7, 60)

    def test_is_allowed_with_combined_bad_metrics(self):
        """Test is_allowed with both high error rate and slow response."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.20", "6000.0"]  # 20% error rate, 6000ms response
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # High error rate takes precedence (adjustment factor = 0.5)
        # adaptive_limit = max(1, int(10 * 0.5)) = 5
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 5, 60)

    def test_is_allowed_minimum_limit_enforced(self):
        """Test that minimum limit of 1 is enforced."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.20", "6000.0"]  # High error rate
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 1, 60)  # Very low base limit
        
        assert result is True
        
        # With base limit 1 and adjustment 0.5, result should be max(1, int(1 * 0.5)) = 1
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 1, 60)

    def test_is_allowed_redis_error_uses_base_limit(self):
        """Test that Redis errors fallback to base limit."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.side_effect = redis.RedisError("Connection failed")
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        
        # Should use original limit when Redis fails
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 10, 60)

    def test_is_allowed_delegates_to_base_strategy(self):
        """Test that is_allowed properly delegates to base strategy."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.05", "100.0"]
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = False  # Base strategy denies
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is False  # Should return base strategy result


@pytest.mark.unit
class TestAdaptiveStrategyIsAllowedAsync:
    """Test AdaptiveStrategy is_allowed_async method."""

    @pytest.mark.asyncio
    async def test_is_allowed_async_with_no_metrics(self):
        """Test is_allowed_async with no existing metrics."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget = AsyncMock(return_value=[None, None])
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # Verify metrics were fetched
        mock_redis.hmget.assert_called_once_with("metrics:test_key", "error_rate", "response_time")
        
        # Verify base strategy was called with original limit
        assert len(mock_base_strategy.is_allowed_async_calls) == 1
        assert mock_base_strategy.is_allowed_async_calls[0] == ("test_key", 10, 60)

    @pytest.mark.asyncio
    async def test_is_allowed_async_with_high_error_rate(self):
        """Test is_allowed_async with high error rate."""
        import redis.asyncio as aioredis
        from unittest.mock import AsyncMock
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget = AsyncMock(return_value=["0.15", "200.0"])
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # With high error rate, adaptive limit should be reduced
        assert mock_base_strategy.is_allowed_async_calls[0] == ("test_key", 5, 60)

    @pytest.mark.asyncio
    async def test_is_allowed_async_redis_error_uses_base_limit(self):
        """Test that Redis errors fallback to base limit (async)."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget.side_effect = Exception("Connection failed")
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = True
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is True
        
        # Should use original limit when Redis fails
        assert mock_base_strategy.is_allowed_async_calls[0] == ("test_key", 10, 60)

    @pytest.mark.asyncio
    async def test_is_allowed_async_delegates_to_base_strategy(self):
        """Test that is_allowed_async properly delegates to base strategy."""
        import redis.asyncio as aioredis
        mock_redis = Mock(spec=aioredis.Redis)
        mock_redis.hmget.return_value = ["0.05", "100.0"]
        
        mock_base_strategy = MockBaseStrategy()
        mock_base_strategy.is_allowed_result = False
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = await strategy.is_allowed_async("test_key", 10, 60)
        
        assert result is False


@pytest.mark.unit
class TestAdaptiveStrategyCalculateAdjustment:
    """Test AdaptiveStrategy adjustment calculation."""

    def test_calculate_adjustment_good_metrics(self):
        """Test adjustment calculation with good metrics."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Good metrics: low error rate, fast response
        adjustment = strategy._calculate_adjustment(0.05, 500.0)
        
        assert adjustment == 1.0  # No adjustment needed

    def test_calculate_adjustment_high_error_rate(self):
        """Test adjustment calculation with high error rate."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # High error rate
        adjustment = strategy._calculate_adjustment(0.15, 500.0)
        
        assert adjustment == 0.5  # Severe reduction

    def test_calculate_adjustment_slow_response(self):
        """Test adjustment calculation with slow response."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Slow response time
        adjustment = strategy._calculate_adjustment(0.05, 3000.0)
        
        assert adjustment == 0.8  # Moderate reduction

    def test_calculate_adjustment_very_slow_response(self):
        """Test adjustment calculation with very slow response."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Very slow response time
        adjustment = strategy._calculate_adjustment(0.05, 6000.0)
        
        assert adjustment == 0.7  # Significant reduction

    def test_calculate_adjustment_priority_order(self):
        """Test that error rate takes priority over response time."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # High error rate with very slow response
        adjustment = strategy._calculate_adjustment(0.15, 6000.0)
        
        assert adjustment == 0.5  # Error rate takes precedence

    def test_calculate_adjustment_edge_cases(self):
        """Test adjustment calculation edge cases."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Zero error rate, zero response time
        adjustment1 = strategy._calculate_adjustment(0.0, 0.0)
        assert adjustment1 == 1.0
        
        # Exactly at thresholds
        adjustment2 = strategy._calculate_adjustment(0.1, 2000.0)
        assert adjustment2 == 0.8  # Response time threshold
        
        adjustment3 = strategy._calculate_adjustment(0.1, 5000.0)
        assert adjustment3 == 0.7  # Higher response time threshold


@pytest.mark.unit
class TestAdaptiveStrategyGetStats:
    """Test AdaptiveStrategy get_stats method."""

    def test_get_stats_success(self):
        """Test successful stats retrieval."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.08", "1500.0"]
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "adaptive"
        assert stats["base_strategy"] == "mock"
        assert stats["error_rate"] == 0.08
        assert stats["response_time_ms"] == 1500.0
        assert stats["adaptive_adjustment"] == 0.8  # 1500ms response time
        assert stats["test"] == "data"  # From base strategy

    def test_get_stats_no_metrics(self):
        """Test stats with no existing metrics."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = [None, None]
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "adaptive"
        assert stats["error_rate"] == 0.0
        assert stats["response_time_ms"] == 100.0
        assert stats["adaptive_adjustment"] == 1.0

    def test_get_stats_partial_metrics(self):
        """Test stats with partial metrics."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.12", None]  # Error rate but no response time
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        stats = strategy.get_stats("test_key")
        
        assert stats["strategy"] == "adaptive"
        assert stats["error_rate"] == 0.12
        assert stats["response_time_ms"] == 100.0  # Default value
        assert stats["adaptive_adjustment"] == 0.5  # Based on high error rate

    def test_get_stats_redis_error(self):
        """Test stats retrieval with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.side_effect = redis.RedisError("Connection failed")
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        stats = strategy.get_stats("test_key")
        
        # Should return only base strategy stats
        assert stats["strategy"] == "mock"
        assert stats["test"] == "data"


@pytest.mark.unit
class TestAdaptiveStrategyReset:
    """Test AdaptiveStrategy reset method."""

    def test_reset_success(self):
        """Test successful reset."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 1  # Metrics key existed
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.reset("test_key")
        
        assert result is True
        
        # Verify metrics key was deleted
        mock_redis.delete.assert_called_once_with("metrics:test_key")

    def test_reset_no_metrics_key(self):
        """Test reset when metrics key doesn't exist."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 0  # Metrics key didn't exist
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.reset("test_key")
        
        assert result is True  # Base strategy reset succeeded

    def test_reset_redis_error(self):
        """Test reset with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.side_effect = redis.RedisError("Connection failed")
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.reset("test_key")
        
        assert result is False


@pytest.mark.unit
class TestAdaptiveStrategyUpdateMetrics:
    """Test AdaptiveStrategy update_metrics method."""

    def test_update_metrics_success(self):
        """Test successful metrics update."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.update_metrics("test_key", 0.05, 250.0)
        
        assert result is True
        
        # Verify metrics were updated
        mock_redis.hmset.assert_called_once_with("metrics:test_key", {
            "error_rate": 0.05,
            "response_time": 250.0
        })
        mock_redis.expire.assert_called_once_with("metrics:test_key", 3600)

    def test_update_metrics_redis_error(self):
        """Test metrics update with Redis error."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmset.side_effect = redis.RedisError("Connection failed")
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        result = strategy.update_metrics("test_key", 0.05, 250.0)
        
        assert result is False

    def test_update_metrics_validates_input(self):
        """Test that update_metrics accepts valid input values."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Test various valid inputs
        test_cases = [
            (0.0, 0.0),      # Zero values
            (0.5, 1000.0),   # Moderate values
            (1.0, 10000.0),  # High values
        ]
        
        for error_rate, response_time in test_cases:
            result = strategy.update_metrics("test_key", error_rate, response_time)
            assert result is True


@pytest.mark.unit
class TestAdaptiveStrategyEdgeCases:
    """Test AdaptiveStrategy edge cases."""

    def test_extreme_adjustment_scenarios(self):
        """Test extreme adjustment scenarios."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Test with very small base limit
        mock_redis.hmget.return_value = ["0.20", "100.0"]  # High error rate
        result = strategy.is_allowed("test_key", 1, 60)
        
        assert result is True
        # Should enforce minimum limit of 1
        assert mock_base_strategy.is_allowed_calls[-1] == ("test_key", 1, 60)

    def test_floating_point_precision(self):
        """Test handling of floating point precision in calculations."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Test with values that might cause floating point issues
        mock_redis.hmget.return_value = ["0.1000000001", "1999.9999999"]
        result = strategy.is_allowed("test_key", 10, 60)
        
        assert result is True
        # Should still work correctly despite floating point precision
        assert len(mock_base_strategy.is_allowed_calls) == 1

    def test_metrics_key_format(self):
        """Test that metrics key is formatted correctly."""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.hmget.return_value = ["0.05", "100.0"]
        
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        strategy.is_allowed("test_key", 10, 60)
        
        # Verify metrics key format
        mock_redis.hmget.assert_called_once_with("metrics:test_key", "error_rate", "response_time")


@pytest.mark.unit
@pytest.mark.integration
class TestAdaptiveStrategyIntegration:
    """Integration tests for AdaptiveStrategy."""

    def test_complete_adaptive_workflow(self):
        """Test complete adaptive workflow with metrics updates."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Step 1: Update metrics
        mock_redis.hmget.return_value = ["0.05", "100.0"]
        strategy.update_metrics("test_key", 0.05, 100.0)
        
        # Step 2: Check if allowed (should use good metrics)
        result = strategy.is_allowed("test_key", 10, 60)
        assert result is True
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 10, 60)
        
        # Step 3: Update metrics to bad values
        mock_redis.hmget.return_value = ["0.15", "100.0"]
        strategy.update_metrics("test_key", 0.15, 100.0)
        
        # Step 4: Check if allowed (should use reduced limit)
        result = strategy.is_allowed("test_key", 10, 60)
        assert result is True
        assert mock_base_strategy.is_allowed_calls[1] == ("test_key", 5, 60)

    def test_multiple_keys_isolation(self):
        """Test that different keys maintain separate metrics."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Different metrics for different keys
        mock_redis.hmget.side_effect = [
            ["0.05", "100.0"],  # Good metrics for key1
            ["0.15", "100.0"],  # Bad metrics for key2
        ]
        
        result1 = strategy.is_allowed("key1", 10, 60)
        result2 = strategy.is_allowed("key2", 10, 60)
        
        assert result1 is True
        assert result2 is True
        
        # Verify different limits were used
        assert mock_base_strategy.is_allowed_calls[0] == ("key1", 10, 60)  # No adjustment
        assert mock_base_strategy.is_allowed_calls[1] == ("key2", 5, 60)   # Reduced limit

    def test_fallback_behavior_consistency(self):
        """Test consistent fallback behavior across operations."""
        mock_redis = Mock(spec=redis.Redis)
        mock_base_strategy = MockBaseStrategy()
        
        strategy = AdaptiveStrategy(mock_redis, mock_base_strategy)
        
        # Test Redis failures in different operations
        mock_redis.hmget.side_effect = redis.RedisError("Connection failed")
        
        # is_allowed should fallback to base limit
        result = strategy.is_allowed("test_key", 10, 60)
        assert result is True
        assert mock_base_strategy.is_allowed_calls[0] == ("test_key", 10, 60)
        
        # get_stats should fallback to base strategy stats
        stats = strategy.get_stats("test_key")
        assert stats["strategy"] == "mock"
        
        # update_metrics should return False
        result = strategy.update_metrics("test_key", 0.05, 100.0)
        assert result is False

    def test_async_sync_consistency(self):
        """Test that async and sync methods produce consistent results."""
        import redis.asyncio as aioredis
        
        # Test sync version
        mock_redis_sync = Mock(spec=redis.Redis)
        mock_redis_sync.hmget.return_value = ["0.15", "100.0"]
        
        mock_base_strategy_sync = MockBaseStrategy()
        strategy_sync = AdaptiveStrategy(mock_redis_sync, mock_base_strategy_sync)
        
        result_sync = strategy_sync.is_allowed("test_key", 10, 60)
        
        # Test async version
        mock_redis_async = Mock(spec=aioredis.Redis)
        mock_redis_async.hmget.return_value = ["0.15", "100.0"]
        
        mock_base_strategy_async = MockBaseStrategy()
        strategy_async = AdaptiveStrategy(mock_redis_async, mock_base_strategy_async)
        
        # Both should produce same result
        assert result_sync is True
        assert mock_base_strategy_sync.is_allowed_calls[0] == ("test_key", 5, 60)
        
        # Async version should call async method
        import asyncio
        result_async = asyncio.run(strategy_async.is_allowed_async("test_key", 10, 60))
        assert result_async is True
        assert mock_base_strategy_async.is_allowed_async_calls[0] == ("test_key", 5, 60)