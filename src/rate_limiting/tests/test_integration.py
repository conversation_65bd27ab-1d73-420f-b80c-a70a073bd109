"""Integration tests for rate limiting with SoraClient."""

import time
from unittest.mock import MagicMock, patch

import pytest
import redis

from src.features.sora_integration.client import SoraClient
from src.rate_limiting.limiter import (
    GlobalRateLimiter,
    RateLimitConfig,
    RateLimitExceeded,
)


@pytest.mark.integration
@pytest.mark.integration
class TestSoraClientRateLimitingIntegration:
    """Integration tests for SoraClient with distributed rate limiting."""

    @pytest.fixture
    def mock_azure_config(self):
        """Mock Azure configuration."""
        return {
            "endpoint": "https://test.openai.azure.com",
            "api_version": "2024-02-15-preview",
            "deployment_name": "sora",
            "api_key": "test-api-key",
            "redis_url": "redis://localhost:6379/15"
        }

    @pytest.fixture
    def redis_client(self):
        """Create real Redis client for integration tests."""
        try:
            client = redis.Redis(host='localhost', port=6379, db=15, decode_responses=True)
            client.ping()  # Test connection
            yield client
            # Cleanup after test
            client.flushdb()
        except redis.ConnectionError:
            pytest.skip("Redis not available for integration tests")

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_sora_client_distributed_rate_limiter_initialization(self, mock_get_config, mock_azure_config, redis_client):
        """Test SoraClient initializes distributed rate limiter successfully."""
        mock_get_config.return_value = mock_azure_config

        with patch('src.rate_limiting.limiter.create_rate_limiter') as mock_create_limiter:
            mock_limiter = MagicMock()
            mock_create_limiter.return_value = mock_limiter

            client = SoraClient()

            assert client._distributed_limiter == mock_limiter
            mock_create_limiter.assert_called_once_with("redis://localhost:6379/15")

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    @patch('src.rate_limiting.limiter.create_rate_limiter')
    def test_sora_client_rate_limiter_fallback_on_error(self, mock_create_limiter, mock_get_config, mock_azure_config):
        """Test SoraClient falls back to local rate limiting on Redis error."""
        mock_get_config.return_value = mock_azure_config
        mock_create_limiter.side_effect = redis.ConnectionError("Redis unavailable")

        client = SoraClient()

        assert client._distributed_limiter is None

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_sora_client_uses_distributed_rate_limiting(self, mock_get_config, mock_azure_config, redis_client):
        """Test SoraClient uses distributed rate limiting when available."""
        mock_get_config.return_value = mock_azure_config

        # Create real distributed limiter
        config = RateLimitConfig(requests_per_second=2)  # Low limit for testing
        limiter = GlobalRateLimiter(redis_client, config)

        with patch('src.rate_limiting.limiter.create_rate_limiter', return_value=limiter):
            client = SoraClient()

            # Mock successful token acquisition
            with patch.object(limiter, 'acquire', return_value=True) as mock_acquire:
                client._rate_limit()
                mock_acquire.assert_called_once_with(timeout=30)

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_sora_client_handles_rate_limit_exceeded(self, mock_get_config, mock_azure_config, redis_client):
        """Test SoraClient handles RateLimitExceeded exception."""
        mock_get_config.return_value = mock_azure_config

        # Create distributed limiter that will exceed limit
        config = RateLimitConfig(requests_per_second=1)
        limiter = GlobalRateLimiter(redis_client, config)

        with patch('src.rate_limiting.limiter.create_rate_limiter', return_value=limiter):
            client = SoraClient()

            # Mock rate limit exceeded
            with patch.object(limiter, 'acquire', side_effect=RateLimitExceeded(1.0)):
                with patch('time.sleep') as mock_sleep:
                    client._rate_limit()
                    mock_sleep.assert_called_once_with(1.0)

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_sora_client_falls_back_on_limiter_error(self, mock_get_config, mock_azure_config, redis_client):
        """Test SoraClient falls back to local limiting on limiter error."""
        mock_get_config.return_value = mock_azure_config

        config = RateLimitConfig(requests_per_second=5)
        limiter = GlobalRateLimiter(redis_client, config)

        with patch('src.rate_limiting.limiter.create_rate_limiter', return_value=limiter):
            client = SoraClient()

            # Mock limiter error
            with patch.object(limiter, 'acquire', side_effect=Exception("Limiter error")):
                with patch.object(client, '_local_rate_limit') as mock_local:
                    client._rate_limit()
                    mock_local.assert_called_once()

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_sora_client_timeout_fallback(self, mock_get_config, mock_azure_config, redis_client):
        """Test SoraClient handles rate limiter timeout."""
        mock_get_config.return_value = mock_azure_config

        config = RateLimitConfig(requests_per_second=1)
        limiter = GlobalRateLimiter(redis_client, config)

        with patch('src.rate_limiting.limiter.create_rate_limiter', return_value=limiter):
            client = SoraClient()

            # Mock timeout (returns False)
            with patch.object(limiter, 'acquire', return_value=False):
                with patch.object(client, '_local_rate_limit') as mock_local:
                    client._rate_limit()
                    mock_local.assert_called_once()

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_local_rate_limiting_functionality(self, mock_get_config, mock_azure_config):
        """Test local rate limiting functionality."""
        mock_get_config.return_value = mock_azure_config

        # Don't initialize distributed limiter
        with patch('src.rate_limiting.limiter.create_rate_limiter', side_effect=Exception()):
            client = SoraClient()
            assert client._distributed_limiter is None

            # Test local rate limiting
            start_time = time.time()
            client._rate_limit()
            client._rate_limit()  # Should cause delay
            end_time = time.time()

            # Should have some delay due to rate limiting
            elapsed = end_time - start_time
            assert elapsed >= 0.05  # At least 50ms (less than 100ms interval due to overhead)

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    @patch('requests.request')
    def test_rate_limiting_in_make_request(self, mock_request, mock_get_config, mock_azure_config, redis_client):
        """Test rate limiting is enforced in _make_request method."""
        mock_get_config.return_value = mock_azure_config

        # Mock successful HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {}
        mock_response.json.return_value = {"status": "success"}
        mock_request.return_value = mock_response

        config = RateLimitConfig(requests_per_second=5)
        limiter = GlobalRateLimiter(redis_client, config)

        with patch('src.rate_limiting.limiter.create_rate_limiter', return_value=limiter):
            client = SoraClient()

            with patch.object(limiter, 'acquire', return_value=True) as mock_acquire:
                response = client._make_request(
                    "GET",
                    "https://test.example.com/test",
                    request_id="test-123"
                )

                # Verify rate limiting was called
                mock_acquire.assert_called_once_with(timeout=30)
                assert response.status_code == 200


@pytest.mark.integration
@pytest.mark.integration
class TestMultiClientRateLimitingCoordination:
    """Test rate limiting coordination across multiple SoraClient instances."""

    @pytest.fixture
    def redis_client(self):
        """Create real Redis client for integration tests."""
        try:
            client = redis.Redis(host='localhost', port=6379, db=15, decode_responses=True)
            client.ping()  # Test connection
            yield client
            # Cleanup after test
            client.flushdb()
        except redis.ConnectionError:
            pytest.skip("Redis not available for integration tests")

    @pytest.fixture
    def mock_azure_config(self):
        """Mock Azure configuration."""
        return {
            "endpoint": "https://test.openai.azure.com",
            "api_version": "2024-02-15-preview",
            "deployment_name": "sora",
            "api_key": "test-api-key",
            "redis_url": "redis://localhost:6379/15"
        }

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_multiple_clients_coordinate_rate_limiting(self, mock_get_config, mock_azure_config, redis_client):
        """Test multiple SoraClient instances coordinate through Redis."""
        mock_get_config.return_value = mock_azure_config

        # Create multiple clients
        with patch('src.rate_limiting.limiter.create_rate_limiter') as mock_create_limiter:
            # All clients use same Redis instance
            config = RateLimitConfig(requests_per_second=3)  # Low limit for testing

            def create_limiter_side_effect(redis_url):
                return GlobalRateLimiter(redis_client, config)

            mock_create_limiter.side_effect = create_limiter_side_effect

            client1 = SoraClient()
            client2 = SoraClient()
            client3 = SoraClient()

            # All should acquire tokens successfully (under limit)
            assert client1._distributed_limiter.acquire() is True
            assert client2._distributed_limiter.acquire() is True
            assert client3._distributed_limiter.acquire() is True

            # Fourth acquisition should be blocked (over limit)
            with patch('time.sleep'):
                try:
                    result = client1._distributed_limiter.acquire(timeout=0.1)
                    assert result is False
                except RateLimitExceeded:
                    pass  # Also acceptable

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_rate_limiting_statistics_across_clients(self, mock_get_config, mock_azure_config, redis_client):
        """Test rate limiting statistics are shared across clients."""
        mock_get_config.return_value = mock_azure_config

        config = RateLimitConfig(requests_per_second=5)

        with patch('src.rate_limiting.limiter.create_rate_limiter') as mock_create_limiter:
            def create_limiter_side_effect(redis_url):
                return GlobalRateLimiter(redis_client, config)

            mock_create_limiter.side_effect = create_limiter_side_effect

            client1 = SoraClient()
            client2 = SoraClient()

            # Client1 acquires tokens
            client1._distributed_limiter.acquire()
            client1._distributed_limiter.acquire()

            # Client2 should see the usage from client1
            stats = client2._distributed_limiter.get_current_usage()
            assert stats['current_requests'] == 2
            assert stats['requests_available'] == 3

    @patch('src.config.factory.ConfigurationFactory.get_azure_config')
    def test_rate_limiting_recovery_coordination(self, mock_get_config, mock_azure_config, redis_client):
        """Test rate limiting recovery works across multiple clients."""
        mock_get_config.return_value = mock_azure_config

        config = RateLimitConfig(requests_per_second=2, window_size=1)

        with patch('src.rate_limiting.limiter.create_rate_limiter') as mock_create_limiter:
            def create_limiter_side_effect(redis_url):
                return GlobalRateLimiter(redis_client, config)

            mock_create_limiter.side_effect = create_limiter_side_effect

            client1 = SoraClient()
            client2 = SoraClient()

            # Use up all tokens
            assert client1._distributed_limiter.acquire() is True
            assert client2._distributed_limiter.acquire() is True

            # Should be blocked now
            with patch('time.sleep'):
                try:
                    result = client1._distributed_limiter.acquire(timeout=0.1)
                    assert result is False
                except RateLimitExceeded:
                    pass

            # Wait for window to reset
            time.sleep(1.1)

            # Should be able to acquire again
            assert client2._distributed_limiter.acquire() is True


@pytest.mark.integration
@pytest.mark.integration
@pytest.mark.performance
class TestRateLimitingPerformance:
    """Performance tests for rate limiting implementation."""

    @pytest.fixture
    def redis_client(self):
        """Create real Redis client for performance tests."""
        try:
            client = redis.Redis(host='localhost', port=6379, db=15, decode_responses=True)
            client.ping()
            yield client
            client.flushdb()
        except redis.ConnectionError:
            pytest.skip("Redis not available for performance tests")

    def test_rate_limiter_performance(self, redis_client):
        """Test rate limiter performance under load."""
        config = RateLimitConfig(requests_per_second=100)  # High limit
        limiter = GlobalRateLimiter(redis_client, config)

        # Time multiple acquisitions
        start_time = time.time()
        acquisitions = 50

        for _ in range(acquisitions):
            limiter.acquire()

        end_time = time.time()
        elapsed = end_time - start_time

        # Should complete reasonably quickly (less than 1 second for 50 acquisitions)
        assert elapsed < 1.0

        # Average acquisition time should be reasonable
        avg_time = elapsed / acquisitions
        assert avg_time < 0.02  # Less than 20ms per acquisition

    def test_concurrent_rate_limiter_usage(self, redis_client):
        """Test rate limiter performance with concurrent usage simulation."""
        import threading

        config = RateLimitConfig(requests_per_second=20)
        limiter = GlobalRateLimiter(redis_client, config)

        results = []
        errors = []

        def worker():
            try:
                result = limiter.acquire(timeout=1)
                results.append(result)
            except Exception as e:
                errors.append(e)

        # Create multiple threads to simulate concurrent clients
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=worker)
            threads.append(thread)

        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        end_time = time.time()

        # Verify results
        assert len(errors) == 0  # No errors should occur
        assert len(results) == 10  # All threads should complete

        # Should complete in reasonable time (allowing for rate limiting)
        assert end_time - start_time < 5.0
