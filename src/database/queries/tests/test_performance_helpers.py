"""Unit tests for performance query helpers."""

import time
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.database.models import Base
from src.database.queries.performance_helpers import PerformanceQueryHelper


class TestPerformanceQueryHelper:
    """Test performance query helper functionality."""

    @pytest.fixture
    def test_engine(self):
        """Create in-memory SQLite engine for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture
    def test_session(self, test_engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=test_engine)
        session = Session()
        yield session
        session.close()

    def test_measure_query_time_context_manager(self):
        """Test query time measurement context manager."""
        with PerformanceQueryHelper.measure_query_time() as timer:
            # Simulate some work
            time.sleep(0.01)  # 10ms

        assert "start_time" in timer
        assert "end_time" in timer
        assert "execution_time_ms" in timer
        assert timer["execution_time_ms"] >= 10  # At least 10ms
        assert (
            timer["execution_time_ms"] < 100
        )  # Less than 100ms (reasonable upper bound)

    def test_validate_index_usage_mock_explain(self, test_session):
        """Test index usage validation with mocked EXPLAIN ANALYZE."""
        import src.database.queries.performance_helpers as ph_module

        original_get_db_session = ph_module.get_db_session

        # Mock session with EXPLAIN ANALYZE result
        mock_session = MagicMock()
        mock_result = [
            ("Index Scan using idx_provider_status on video_jobs",),
            ("  Index Cond: (api_provider = 'azure_sora'::text)",),
            ("  Rows Removed by Index Recheck: 0",),
        ]
        mock_session.execute.return_value.fetchall.return_value = mock_result

        ph_module.get_db_session = lambda: mock_session

        try:
            result = PerformanceQueryHelper.validate_index_usage(
                "SELECT * FROM video_jobs WHERE api_provider = 'azure_sora'"
            )

            assert result["uses_index"] is True
            assert result["performance_grade"] == "A"  # Index scan without seq scan
            assert result["scan_type"] == "Index"
            assert result["analysis_successful"] is True
            assert "explain_plan" in result

        finally:
            ph_module.get_db_session = original_get_db_session

    def test_validate_index_usage_sequential_scan(self, test_session):
        """Test index usage validation with sequential scan."""
        import src.database.queries.performance_helpers as ph_module

        original_get_db_session = ph_module.get_db_session

        # Mock session with sequential scan result
        mock_session = MagicMock()
        mock_result = [
            ("Seq Scan on video_jobs",),
            ("  Filter: (api_provider = 'azure_sora'::text)",),
            ("  Rows Removed by Filter: 100",),
        ]
        mock_session.execute.return_value.fetchall.return_value = mock_result

        ph_module.get_db_session = lambda: mock_session

        try:
            result = PerformanceQueryHelper.validate_index_usage(
                "SELECT * FROM video_jobs WHERE some_unindexed_field = 'value'"
            )

            assert result["uses_index"] is False
            assert result["uses_sequential_scan"] is True
            assert result["performance_grade"] == "D"  # Poor performance
            assert result["scan_type"] == "Sequential"

        finally:
            ph_module.get_db_session = original_get_db_session

    def test_validate_index_usage_error_handling(self, test_session):
        """Test error handling in index usage validation."""
        import src.database.queries.performance_helpers as ph_module

        original_get_db_session = ph_module.get_db_session

        # Mock session that raises an error
        mock_session = MagicMock()
        mock_session.execute.side_effect = Exception("Database error")

        ph_module.get_db_session = lambda: mock_session

        try:
            result = PerformanceQueryHelper.validate_index_usage(
                "SELECT * FROM video_jobs"
            )

            assert result["analysis_successful"] is False
            assert result["performance_grade"] == "F"
            assert "error" in result
            assert result["uses_index"] is False

        finally:
            ph_module.get_db_session = original_get_db_session

    @patch("src.database.queries.performance_helpers.ProviderQueryHelper")
    def test_benchmark_provider_queries_success(self, mock_provider_helper):
        """Test successful provider query benchmarking."""
        # Mock provider query methods to return quickly
        mock_provider_helper.get_jobs_by_provider.return_value = []
        mock_provider_helper.get_provider_statistics.return_value = {}
        mock_provider_helper.get_active_queue_by_provider.return_value = []
        mock_provider_helper.search_jobs_by_provider.return_value = []
        mock_provider_helper.get_provider_performance_metrics.return_value = {}

        benchmarks = PerformanceQueryHelper.benchmark_provider_queries()

        assert benchmarks["overall_performance"] in [
            "EXCELLENT",
            "GOOD",
            "FAIR",
            "POOR",
        ]
        assert "test_results" in benchmarks
        assert "summary" in benchmarks
        assert "recommendations" in benchmarks

        # Check test results structure
        test_results = benchmarks["test_results"]
        assert "provider_filter" in test_results
        assert "provider_stats" in test_results
        assert "active_queue" in test_results
        assert "search_query" in test_results
        assert "performance_metrics" in test_results

        # Each test should have required fields
        for test_name, result in test_results.items():
            assert "execution_time_ms" in result
            assert "threshold_ms" in result
            assert "meets_threshold" in result
            assert "test_description" in result

    @patch("src.database.queries.performance_helpers.ProviderQueryHelper")
    def test_benchmark_provider_queries_poor_performance(self, mock_provider_helper):
        """Test benchmarking with poor performance results."""

        # Mock slow provider query methods
        def slow_method(*args, **kwargs):
            time.sleep(0.2)  # 200ms - exceeds all thresholds
            return []

        mock_provider_helper.get_jobs_by_provider.side_effect = slow_method
        mock_provider_helper.get_provider_statistics.side_effect = slow_method
        mock_provider_helper.get_active_queue_by_provider.side_effect = slow_method
        mock_provider_helper.search_jobs_by_provider.side_effect = slow_method
        mock_provider_helper.get_provider_performance_metrics.side_effect = slow_method

        benchmarks = PerformanceQueryHelper.benchmark_provider_queries()

        assert benchmarks["overall_performance"] == "POOR"
        assert benchmarks["summary"]["performance_score"] == 0.0  # All tests failed
        assert len(benchmarks["recommendations"]) > 0

        # All tests should fail to meet thresholds
        for test_result in benchmarks["test_results"].values():
            assert test_result["meets_threshold"] is False
            assert test_result["execution_time_ms"] >= 200

    def test_benchmark_provider_queries_error_handling(self):
        """Test error handling in benchmark provider queries."""
        # Mock ProviderQueryHelper to raise exceptions
        with patch(
            "src.database.queries.performance_helpers.ProviderQueryHelper"
        ) as mock_helper:
            mock_helper.get_jobs_by_provider.side_effect = Exception("Database error")

            benchmarks = PerformanceQueryHelper.benchmark_provider_queries()

            assert benchmarks["overall_performance"] == "ERROR"
            assert "error" in benchmarks

    def test_generate_performance_recommendations_failing_tests(self):
        """Test performance recommendations generation for failing tests."""
        test_results = {
            "provider_filter": {"meets_threshold": False},
            "provider_stats": {"meets_threshold": False},
            "active_queue": {"meets_threshold": False},
            "search_query": {"meets_threshold": False},
        }

        recommendations = PerformanceQueryHelper._generate_performance_recommendations(
            test_results
        )

        assert len(recommendations) == 4
        assert any(
            "composite index on (api_provider, status, created_at)" in rec
            for rec in recommendations
        )
        assert any("materialized views or caching" in rec for rec in recommendations)
        assert any(
            "composite index on (api_provider, status, priority)" in rec
            for rec in recommendations
        )
        assert any("full-text search indexes" in rec for rec in recommendations)

    def test_generate_performance_recommendations_all_passing(self):
        """Test performance recommendations when all tests pass."""
        test_results = {
            "provider_filter": {"meets_threshold": True},
            "provider_stats": {"meets_threshold": True},
            "active_queue": {"meets_threshold": True},
            "search_query": {"meets_threshold": True},
        }

        recommendations = PerformanceQueryHelper._generate_performance_recommendations(
            test_results
        )

        assert len(recommendations) == 1
        assert (
            recommendations[0]
            == "All queries meet performance thresholds - excellent performance!"
        )

    def test_validate_provider_indexes_success(self, test_session):
        """Test successful provider index validation."""
        import src.database.queries.performance_helpers as ph_module

        original_get_db_session = ph_module.get_db_session

        # Mock session with proper index information
        mock_session = MagicMock()
        mock_session.bind = test_session.bind

        # Mock inspector
        with patch("src.database.queries.performance_helpers.inspect") as mock_inspect:
            mock_inspector = MagicMock()
            mock_inspect.return_value = mock_inspector

            # Mock existing indexes
            mock_inspector.get_indexes.return_value = [
                {
                    "name": "idx_provider_status",
                    "column_names": ["api_provider", "status"],
                },
                {
                    "name": "idx_provider_created",
                    "column_names": ["api_provider", "created_at"],
                },
            ]

            ph_module.get_db_session = lambda: mock_session

            try:
                result = PerformanceQueryHelper.validate_provider_indexes()

                assert result["validation_successful"] is True
                assert result["all_required_indexes_valid"] is True
                assert "required_indexes" in result
                assert "beneficial_indexes" in result
                assert "recommendations" in result

                # Check required indexes
                required = result["required_indexes"]
                assert required["idx_provider_status"]["status"] == "VALID"
                assert required["idx_provider_created"]["status"] == "VALID"

            finally:
                ph_module.get_db_session = original_get_db_session

    def test_validate_provider_indexes_missing_indexes(self, test_session):
        """Test provider index validation with missing indexes."""
        import src.database.queries.performance_helpers as ph_module

        original_get_db_session = ph_module.get_db_session

        # Mock session
        mock_session = MagicMock()
        mock_session.bind = test_session.bind

        # Mock inspector with no indexes
        with patch("src.database.queries.performance_helpers.inspect") as mock_inspect:
            mock_inspector = MagicMock()
            mock_inspect.return_value = mock_inspector
            mock_inspector.get_indexes.return_value = []  # No indexes

            ph_module.get_db_session = lambda: mock_session

            try:
                result = PerformanceQueryHelper.validate_provider_indexes()

                assert result["validation_successful"] is True
                assert result["all_required_indexes_valid"] is False

                # Check that missing indexes are identified
                required = result["required_indexes"]
                assert required["idx_provider_status"]["status"] == "INVALID"
                assert required["idx_provider_created"]["status"] == "INVALID"

                # Should have recommendations for missing indexes
                recommendations = result["recommendations"]
                assert any(
                    "CRITICAL: Create missing required index" in rec
                    for rec in recommendations
                )

            finally:
                ph_module.get_db_session = original_get_db_session

    def test_identify_beneficial_indexes(self):
        """Test identification of beneficial additional indexes."""
        existing_indexes = [
            {"name": "idx_provider_status"},
            {"name": "idx_provider_created"},
            {"name": "idx_session_provider"},  # One beneficial index exists
        ]

        beneficial = PerformanceQueryHelper._identify_beneficial_indexes(
            existing_indexes
        )

        assert "idx_session_provider" in beneficial
        assert "idx_provider_priority" in beneficial
        assert "idx_provider_completed" in beneficial

        # Check that existing one is marked as exists
        assert beneficial["idx_session_provider"]["exists"] is True
        assert beneficial["idx_provider_priority"]["exists"] is False
        assert beneficial["idx_provider_completed"]["exists"] is False

    def test_generate_index_recommendations_missing_required(self):
        """Test index recommendations for missing required indexes."""
        index_validation = {
            "idx_provider_status": {
                "status": "INVALID",
                "required_columns": ["api_provider", "status"],
            },
            "idx_provider_created": {
                "status": "VALID",
                "required_columns": ["api_provider", "created_at"],
            },
        }

        beneficial_indexes = {
            "idx_session_provider": {
                "exists": False,
                "columns": ["session_id", "api_provider"],
            },
        }

        recommendations = PerformanceQueryHelper._generate_index_recommendations(
            index_validation, beneficial_indexes
        )

        # Should have critical recommendation for missing required index
        assert any(
            "CRITICAL: Create missing required index idx_provider_status" in rec
            for rec in recommendations
        )

        # Should have optional recommendation for beneficial index
        assert any(
            "OPTIONAL: Consider creating index idx_session_provider" in rec
            for rec in recommendations
        )

    def test_generate_index_recommendations_all_good(self):
        """Test index recommendations when everything is configured correctly."""
        index_validation = {
            "idx_provider_status": {"status": "VALID"},
            "idx_provider_created": {"status": "VALID"},
        }

        beneficial_indexes = {
            "idx_session_provider": {"exists": True},
        }

        recommendations = PerformanceQueryHelper._generate_index_recommendations(
            index_validation, beneficial_indexes
        )

        assert len(recommendations) == 1
        assert recommendations[0] == "All required indexes are properly configured!"

    def test_get_query_performance_report_success(self, test_session):
        """Test successful query performance report generation."""
        # Mock all the methods used in the report
        with patch.object(
            PerformanceQueryHelper, "benchmark_provider_queries"
        ) as mock_bench, patch.object(
            PerformanceQueryHelper, "validate_provider_indexes"
        ) as mock_index, patch.object(
            PerformanceQueryHelper, "validate_index_usage"
        ) as mock_usage:
            # Setup mock returns
            mock_bench.return_value = {
                "summary": {"performance_score": 85.0},
                "overall_performance": "GOOD",
            }

            mock_index.return_value = {"all_required_indexes_valid": True}

            mock_usage.return_value = {"performance_grade": "A", "uses_index": True}

            report = PerformanceQueryHelper.get_query_performance_report()

            assert report["report_type"] == "provider_query_performance"
            assert "sections" in report
            assert "benchmarks" in report["sections"]
            assert "index_validation" in report["sections"]
            assert "query_analysis" in report["sections"]

            # Should calculate overall score
            assert "overall_performance_score" in report
            assert "overall_grade" in report
            assert report["overall_grade"] == "GOOD"  # 85 * 0.6 + 40 = 91

    def test_get_query_performance_report_needs_improvement(self):
        """Test query performance report with poor performance."""
        with patch.object(
            PerformanceQueryHelper, "benchmark_provider_queries"
        ) as mock_bench, patch.object(
            PerformanceQueryHelper, "validate_provider_indexes"
        ) as mock_index, patch.object(
            PerformanceQueryHelper, "validate_index_usage"
        ) as mock_usage:
            # Setup poor performance mocks
            mock_bench.return_value = {
                "summary": {"performance_score": 30.0},
                "overall_performance": "POOR",
            }

            mock_index.return_value = {"all_required_indexes_valid": False}

            mock_usage.return_value = {"performance_grade": "D", "uses_index": False}

            report = PerformanceQueryHelper.get_query_performance_report()

            # Should get poor grade (30 * 0.6 + 0 = 18)
            assert report["overall_performance_score"] == 18.0
            assert report["overall_grade"] == "NEEDS_IMPROVEMENT"

    def test_get_query_performance_report_error_handling(self):
        """Test error handling in query performance report."""
        with patch.object(
            PerformanceQueryHelper, "benchmark_provider_queries"
        ) as mock_bench:
            mock_bench.side_effect = Exception("Benchmark failed")

            report = PerformanceQueryHelper.get_query_performance_report()

            assert report["overall_grade"] == "ERROR"
            assert "error" in report

    def test_performance_thresholds_constants(self):
        """Test that performance thresholds are defined correctly."""
        thresholds = PerformanceQueryHelper.PERFORMANCE_THRESHOLDS

        assert "provider_filter_query" in thresholds
        assert "provider_stats_query" in thresholds
        assert "active_queue_query" in thresholds
        assert "search_query" in thresholds

        # Check reasonable threshold values
        assert thresholds["provider_filter_query"] == 100  # 100ms
        assert thresholds["provider_stats_query"] == 150  # 150ms
        assert thresholds["active_queue_query"] == 80  # 80ms
        assert thresholds["search_query"] == 200  # 200ms

    def test_measure_query_time_with_exception(self):
        """Test query time measurement when exception occurs."""
        try:
            with PerformanceQueryHelper.measure_query_time() as timer:
                time.sleep(0.01)
                raise ValueError("Test exception")
        except ValueError:
            pass

        # Timer should still record end time even with exception
        assert "execution_time_ms" in timer
        assert timer["execution_time_ms"] >= 10

    def test_context_manager_functionality(self):
        """Test that measure_query_time works as proper context manager."""
        # Test with successful completion
        with PerformanceQueryHelper.measure_query_time() as timer1:
            time.sleep(0.01)

        # Test with exception
        try:
            with PerformanceQueryHelper.measure_query_time() as timer2:
                time.sleep(0.01)
                raise Exception("Test")
        except Exception:
            pass

        # Both should have timing information
        assert "execution_time_ms" in timer1
        assert "execution_time_ms" in timer2
        assert timer1["execution_time_ms"] > 0
        assert timer2["execution_time_ms"] > 0
