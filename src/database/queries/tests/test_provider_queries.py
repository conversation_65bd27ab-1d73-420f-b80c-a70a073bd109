"""Unit tests for provider query helpers."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import List

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.database.models import Base, VideoJobDB
from src.database.queries.provider_queries import ProviderQueryHelper


class TestProviderQueryHelper:
    """Test provider query helper functionality."""

    @pytest.fixture
    def test_engine(self):
        """Create in-memory SQLite engine for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture
    def test_session(self, test_engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=test_engine)
        session = Session()
        yield session
        session.close()

    @pytest.fixture
    def sample_jobs(self, test_session) -> List[VideoJobDB]:
        """Create sample video jobs for testing."""
        jobs = [
            VideoJobDB(
                id=f"azure-job-{i}",
                prompt=f"Azure test prompt {i}",
                status=["pending", "running", "succeeded", "failed"][i % 4],
                created_at=datetime.utcnow() - timedelta(hours=i),
                api_provider="azure_sora",
                session_id=f"session-{i % 3}",
                priority=i % 5,
                audio_generated=False,
                input_image_path=None,
            )
            for i in range(10)
        ]

        jobs.extend(
            [
                VideoJobDB(
                    id=f"veo3-job-{i}",
                    prompt=f"Veo3 test prompt {i}",
                    status=["pending", "running", "succeeded", "failed"][i % 4],
                    created_at=datetime.utcnow() - timedelta(hours=i + 10),
                    api_provider="google_veo3",
                    session_id=f"session-{i % 3}",
                    priority=i % 5,
                    audio_generated=True,
                    input_image_path=f"/test/image-{i}.jpg",
                )
                for i in range(8)
            ]
        )

        for job in jobs:
            test_session.add(job)
        test_session.commit()

        return jobs

    def test_get_jobs_by_provider_azure(self, test_session, sample_jobs):
        """Test filtering jobs by Azure Sora provider."""
        # Mock get_db_session context manager
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            azure_jobs = ProviderQueryHelper.get_jobs_by_provider("azure_sora")

            assert len(azure_jobs) == 10
            assert all(job.api_provider == "azure_sora" for job in azure_jobs)
            # Should be ordered by created_at desc (newest first)
            assert azure_jobs[0].created_at > azure_jobs[-1].created_at

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_jobs_by_provider_veo3(self, test_session, sample_jobs):
        """Test filtering jobs by Google Veo3 provider."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            veo3_jobs = ProviderQueryHelper.get_jobs_by_provider("google_veo3")

            assert len(veo3_jobs) == 8
            assert all(job.api_provider == "google_veo3" for job in veo3_jobs)
            assert all(job.audio_generated is True for job in veo3_jobs)
            assert all(job.input_image_path is not None for job in veo3_jobs)

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_jobs_by_provider_with_status_filter(self, test_session, sample_jobs):
        """Test filtering jobs by provider and status."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            succeeded_azure = ProviderQueryHelper.get_jobs_by_provider(
                "azure_sora", status="succeeded"
            )

            # Should find succeeded jobs (indices 2, 6 based on i % 4)
            expected_count = sum(1 for i in range(10) if i % 4 == 2)
            assert len(succeeded_azure) == expected_count
            assert all(job.status == "succeeded" for job in succeeded_azure)
            assert all(job.api_provider == "azure_sora" for job in succeeded_azure)

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_jobs_by_provider_with_session_filter(self, test_session, sample_jobs):
        """Test filtering jobs by provider and session."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            session_jobs = ProviderQueryHelper.get_jobs_by_provider(
                "azure_sora", session_id="session-1"
            )

            # Should find jobs where i % 3 == 1 (indices 1, 4, 7)
            expected_count = sum(1 for i in range(10) if i % 3 == 1)
            assert len(session_jobs) == expected_count
            assert all(job.session_id == "session-1" for job in session_jobs)
            assert all(job.api_provider == "azure_sora" for job in session_jobs)

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_jobs_by_provider_with_limit(self, test_session, sample_jobs):
        """Test limiting number of results."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            limited_jobs = ProviderQueryHelper.get_jobs_by_provider(
                "azure_sora", limit=5
            )

            assert len(limited_jobs) == 5
            assert all(job.api_provider == "azure_sora" for job in limited_jobs)

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_provider_statistics(self, test_session, sample_jobs):
        """Test provider statistics calculation."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            stats = ProviderQueryHelper.get_provider_statistics()

            assert "azure_sora" in stats
            assert "google_veo3" in stats
            assert "aggregate" in stats

            # Azure stats
            azure_stats = stats["azure_sora"]
            assert azure_stats["total"] == 10
            assert "pending" in azure_stats
            assert "running" in azure_stats
            assert "succeeded" in azure_stats
            assert "failed" in azure_stats
            assert "success_rate" in azure_stats

            # Veo3 stats
            veo3_stats = stats["google_veo3"]
            assert veo3_stats["total"] == 8

            # Aggregate stats
            aggregate = stats["aggregate"]
            assert aggregate["total_jobs"] == 18
            assert "overall_success_rate" in aggregate
            assert "query_timestamp" in aggregate

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_provider_statistics_with_session_filter(
        self, test_session, sample_jobs
    ):
        """Test provider statistics with session filtering."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            stats = ProviderQueryHelper.get_provider_statistics(session_id="session-0")

            # Should only include jobs where session_id = "session-0"
            # That's jobs where i % 3 == 0
            azure_expected = sum(1 for i in range(10) if i % 3 == 0)
            veo3_expected = sum(1 for i in range(8) if i % 3 == 0)

            assert stats["azure_sora"]["total"] == azure_expected
            assert stats["google_veo3"]["total"] == veo3_expected
            assert stats["aggregate"]["total_jobs"] == azure_expected + veo3_expected

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_provider_statistics_with_time_window(self, test_session, sample_jobs):
        """Test provider statistics with time window filtering."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            # Only jobs from last 5 hours
            stats = ProviderQueryHelper.get_provider_statistics(time_window_hours=5)

            # Should only include jobs created within last 5 hours
            # Azure jobs: i < 5 (jobs 0-4)
            # Veo3 jobs: none (all created 10+ hours ago)
            assert stats["azure_sora"]["total"] == 5
            assert stats["google_veo3"]["total"] == 0
            assert stats["aggregate"]["total_jobs"] == 5

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_active_queue_by_provider(self, test_session, sample_jobs):
        """Test getting active job queue by provider."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            active_azure = ProviderQueryHelper.get_active_queue_by_provider(
                "azure_sora"
            )

            # Should only include pending and running jobs
            expected_statuses = {"pending", "running"}
            assert all(job.status in expected_statuses for job in active_azure)
            assert all(job.api_provider == "azure_sora" for job in active_azure)

            # Should be ordered by priority desc, then created_at asc
            if len(active_azure) > 1:
                for i in range(len(active_azure) - 1):
                    current = active_azure[i]
                    next_job = active_azure[i + 1]

                    # Higher priority first, or if same priority, older first
                    assert current.priority > next_job.priority or (
                        current.priority == next_job.priority
                        and current.created_at <= next_job.created_at
                    )

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_jobs_by_time_range(self, test_session, sample_jobs):
        """Test getting jobs within time range."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            # Get jobs from 3 hours ago to 1 hour ago
            end_time = datetime.utcnow() - timedelta(hours=1)
            start_time = datetime.utcnow() - timedelta(hours=3)

            time_range_jobs = ProviderQueryHelper.get_jobs_by_time_range(
                "azure_sora", start_time, end_time
            )

            # Should include azure jobs created 1-2 hours ago (indices 1, 2)
            expected_count = 2
            assert len(time_range_jobs) == expected_count
            assert all(job.api_provider == "azure_sora" for job in time_range_jobs)
            assert all(
                start_time <= job.created_at <= end_time for job in time_range_jobs
            )

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_jobs_by_time_range_with_status(self, test_session, sample_jobs):
        """Test getting jobs within time range with status filter."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            # Get succeeded jobs from 5 hours ago to now
            start_time = datetime.utcnow() - timedelta(hours=5)
            end_time = datetime.utcnow()

            succeeded_jobs = ProviderQueryHelper.get_jobs_by_time_range(
                "azure_sora", start_time, end_time, status="succeeded"
            )

            assert all(job.status == "succeeded" for job in succeeded_jobs)
            assert all(job.api_provider == "azure_sora" for job in succeeded_jobs)
            assert all(
                start_time <= job.created_at <= end_time for job in succeeded_jobs
            )

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_search_jobs_by_provider(self, test_session, sample_jobs):
        """Test searching jobs by provider with text search."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            # Search for "test" in Azure jobs
            search_results = ProviderQueryHelper.search_jobs_by_provider(
                "azure_sora", "test"
            )

            # All Azure jobs have "test" in their prompt
            assert len(search_results) == 10
            assert all(job.api_provider == "azure_sora" for job in search_results)
            assert all("test" in job.prompt.lower() for job in search_results)

            # Search for specific number
            specific_search = ProviderQueryHelper.search_jobs_by_provider(
                "azure_sora", "prompt 3"
            )

            assert len(specific_search) == 1
            assert specific_search[0].prompt == "Azure test prompt 3"

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_search_jobs_by_provider_with_limit(self, test_session, sample_jobs):
        """Test search with limit parameter."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            limited_search = ProviderQueryHelper.search_jobs_by_provider(
                "azure_sora", "test", limit=3
            )

            assert len(limited_search) == 3
            assert all(job.api_provider == "azure_sora" for job in limited_search)
            assert all("test" in job.prompt.lower() for job in limited_search)

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_get_provider_job_distribution(self, test_session, sample_jobs):
        """Test getting job distribution across providers."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            distribution = ProviderQueryHelper.get_provider_job_distribution()

            assert distribution["total_jobs"] == 18
            assert distribution["azure_sora"] == 10
            assert distribution["google_veo3"] == 8

            # Check percentages
            assert (
                abs(distribution["azure_sora_percentage"] - 55.6) < 0.1
            )  # 10/18 * 100
            assert (
                abs(distribution["google_veo3_percentage"] - 44.4) < 0.1
            )  # 8/18 * 100

            # Check recent activity
            assert "recent_activity_24h" in distribution
            assert "azure_sora_recent" in distribution["recent_activity_24h"]
            assert "google_veo3_recent" in distribution["recent_activity_24h"]
            assert "updated_at" in distribution

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_empty_results_handling(self, test_session):
        """Test handling of empty query results."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            # No jobs in empty database
            empty_jobs = ProviderQueryHelper.get_jobs_by_provider("azure_sora")
            assert len(empty_jobs) == 0

            empty_stats = ProviderQueryHelper.get_provider_statistics()
            assert empty_stats["azure_sora"]["total"] == 0
            assert empty_stats["google_veo3"]["total"] == 0
            assert empty_stats["aggregate"]["total_jobs"] == 0

            empty_search = ProviderQueryHelper.search_jobs_by_provider(
                "azure_sora", "nonexistent"
            )
            assert len(empty_search) == 0

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_database_error_handling(self, test_session):
        """Test error handling for database failures."""
        import src.database.queries.provider_queries as pq_module

        # Mock a failing session
        class FailingSession:
            def query(self, *args):
                raise Exception("Database connection failed")

            def __enter__(self):
                return self

            def __exit__(self, *args):
                pass

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: FailingSession()

        try:
            # Should return empty list on error
            result = ProviderQueryHelper.get_jobs_by_provider("azure_sora")
            assert result == []

            # Should return error dict for statistics
            stats = ProviderQueryHelper.get_provider_statistics()
            assert "error" in stats

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_provider_performance_metrics_empty_data(self, test_session):
        """Test performance metrics with no completed jobs."""
        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            metrics = ProviderQueryHelper.get_provider_performance_metrics("azure_sora")

            assert metrics["provider"] == "azure_sora"
            assert metrics["sample_size"] == 0
            assert metrics["metrics"] == {}

        finally:
            pq_module.get_db_session = original_get_db_session

    def test_provider_performance_metrics_with_completed_jobs(self, test_session):
        """Test performance metrics with completed jobs."""
        # Create completed jobs
        completed_jobs = [
            VideoJobDB(
                id=f"completed-{i}",
                prompt=f"Completed job {i}",
                status="succeeded",
                created_at=datetime.utcnow() - timedelta(hours=1),
                completed_at=datetime.utcnow() - timedelta(minutes=30),
                api_provider="azure_sora",
                audio_generated=False,
            )
            for i in range(3)
        ]

        for job in completed_jobs:
            test_session.add(job)
        test_session.commit()

        import src.database.queries.provider_queries as pq_module

        original_get_db_session = pq_module.get_db_session
        pq_module.get_db_session = lambda: test_session

        try:
            metrics = ProviderQueryHelper.get_provider_performance_metrics("azure_sora")

            assert metrics["provider"] == "azure_sora"
            assert metrics["sample_size"] == 3
            assert "metrics" in metrics

            metrics_data = metrics["metrics"]
            assert "avg_processing_time_seconds" in metrics_data
            assert "success_rate_percent" in metrics_data
            assert metrics_data["succeeded_jobs"] == 3
            assert metrics_data["failed_jobs"] == 0

        finally:
            pq_module.get_db_session = original_get_db_session
