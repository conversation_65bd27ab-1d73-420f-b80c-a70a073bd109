"""
Database performance optimization helpers for provider queries.

Provides comprehensive performance monitoring, benchmarking, and index
validation tools for optimizing provider-aware database operations.
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, List

from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError

from src.database.connection import get_db_session
from src.database.queries.provider_queries import ProviderQueryHelper

logger = logging.getLogger(__name__)


class PerformanceQueryHelper:
    """
    Database performance optimization helpers for provider queries.

    Provides benchmarking, index validation, and performance monitoring
    tools for dual-provider database operations.
    """

    # Performance thresholds (in milliseconds)
    PERFORMANCE_THRESHOLDS = {
        "provider_filter_query": 100,  # <100ms for provider filtering
        "provider_stats_query": 150,  # <150ms for statistics
        "active_queue_query": 80,  # <80ms for queue queries
        "search_query": 200,  # <200ms for text search
    }

    @staticmethod
    @contextmanager
    def measure_query_time():
        """
        Context manager to measure query execution time.

        Yields:
            Dict[str, float]: Performance metrics including execution time
        """
        metrics = {"start_time": time.time()}
        try:
            yield metrics
        finally:
            metrics["end_time"] = time.time()
            metrics["execution_time_ms"] = (
                metrics["end_time"] - metrics["start_time"]
            ) * 1000

    @staticmethod
    def validate_index_usage(query_sql: str) -> Dict[str, Any]:
        """
        Validate that provider queries use proper indexes via EXPLAIN ANALYZE.

        Args:
            query_sql: SQL query to analyze

        Returns:
            Dict[str, Any]: Index usage analysis and performance grade
        """
        try:
            with get_db_session() as session:
                # Execute EXPLAIN ANALYZE to get query plan
                explain_result = session.execute(
                    text(f"EXPLAIN ANALYZE {query_sql}")
                ).fetchall()

                # Convert result to string for analysis
                explain_text = "\n".join([str(row[0]) for row in explain_result])

                # Analyze for index usage patterns
                uses_index = any(
                    [
                        "Index Scan" in explain_text,
                        "Using index" in explain_text.lower(),
                        "idx_provider" in explain_text.lower(),
                    ]
                )

                uses_seq_scan = "Seq Scan" in explain_text
                has_sort = "Sort" in explain_text

                # Calculate performance grade
                if uses_index and not uses_seq_scan:
                    performance_grade = "A"  # Excellent
                elif uses_index and uses_seq_scan:
                    performance_grade = "B"  # Good
                elif not uses_index and not uses_seq_scan:
                    performance_grade = "C"  # Fair
                else:
                    performance_grade = "D"  # Poor

                return {
                    "uses_index": uses_index,
                    "uses_sequential_scan": uses_seq_scan,
                    "has_sort_operation": has_sort,
                    "scan_type": "Index" if uses_index else "Sequential",
                    "performance_grade": performance_grade,
                    "explain_plan": explain_text,
                    "analysis_successful": True,
                }

        except SQLAlchemyError as e:
            logger.error(f"Error analyzing query index usage: {e}")
            return {
                "uses_index": False,
                "analysis_successful": False,
                "error": str(e),
                "performance_grade": "F",
            }

    @staticmethod
    def benchmark_provider_queries() -> Dict[str, Any]:
        """
        Comprehensive benchmark of all provider query operations.

        Returns:
            Dict[str, Any]: Benchmark results with timing and performance analysis
        """
        benchmarks = {
            "timestamp": time.time(),
            "test_results": {},
            "overall_performance": "UNKNOWN",
            "recommendations": [],
        }

        try:
            # Test 1: Provider filtering queries
            with PerformanceQueryHelper.measure_query_time() as timer:
                ProviderQueryHelper.get_jobs_by_provider(
                    "azure_sora", "succeeded", limit=50
                )

            benchmarks["test_results"]["provider_filter"] = {
                "execution_time_ms": round(timer["execution_time_ms"], 2),
                "threshold_ms": PerformanceQueryHelper.PERFORMANCE_THRESHOLDS[
                    "provider_filter_query"
                ],
                "meets_threshold": timer["execution_time_ms"]
                < PerformanceQueryHelper.PERFORMANCE_THRESHOLDS[
                    "provider_filter_query"
                ],
                "test_description": "Provider filtering with status filter (50 results)",
            }

            # Test 2: Provider statistics queries
            with PerformanceQueryHelper.measure_query_time() as timer:
                ProviderQueryHelper.get_provider_statistics()

            benchmarks["test_results"]["provider_stats"] = {
                "execution_time_ms": round(timer["execution_time_ms"], 2),
                "threshold_ms": PerformanceQueryHelper.PERFORMANCE_THRESHOLDS[
                    "provider_stats_query"
                ],
                "meets_threshold": timer["execution_time_ms"]
                < PerformanceQueryHelper.PERFORMANCE_THRESHOLDS["provider_stats_query"],
                "test_description": "Complete provider statistics calculation",
            }

            # Test 3: Active queue queries
            with PerformanceQueryHelper.measure_query_time() as timer:
                ProviderQueryHelper.get_active_queue_by_provider("azure_sora")

            benchmarks["test_results"]["active_queue"] = {
                "execution_time_ms": round(timer["execution_time_ms"], 2),
                "threshold_ms": PerformanceQueryHelper.PERFORMANCE_THRESHOLDS[
                    "active_queue_query"
                ],
                "meets_threshold": timer["execution_time_ms"]
                < PerformanceQueryHelper.PERFORMANCE_THRESHOLDS["active_queue_query"],
                "test_description": "Active job queue retrieval by provider",
            }

            # Test 4: Search queries
            with PerformanceQueryHelper.measure_query_time() as timer:
                ProviderQueryHelper.search_jobs_by_provider(
                    "azure_sora", "test", limit=25
                )

            benchmarks["test_results"]["search_query"] = {
                "execution_time_ms": round(timer["execution_time_ms"], 2),
                "threshold_ms": PerformanceQueryHelper.PERFORMANCE_THRESHOLDS[
                    "search_query"
                ],
                "meets_threshold": timer["execution_time_ms"]
                < PerformanceQueryHelper.PERFORMANCE_THRESHOLDS["search_query"],
                "test_description": "Text search within provider jobs",
            }

            # Test 5: Performance metrics calculation
            with PerformanceQueryHelper.measure_query_time() as timer:
                ProviderQueryHelper.get_provider_performance_metrics(
                    "azure_sora", hours_back=24
                )

            benchmarks["test_results"]["performance_metrics"] = {
                "execution_time_ms": round(timer["execution_time_ms"], 2),
                "threshold_ms": PerformanceQueryHelper.PERFORMANCE_THRESHOLDS[
                    "provider_stats_query"
                ],
                "meets_threshold": timer["execution_time_ms"]
                < PerformanceQueryHelper.PERFORMANCE_THRESHOLDS["provider_stats_query"],
                "test_description": "Provider performance metrics calculation (24h)",
            }

            # Calculate overall performance score
            total_tests = len(benchmarks["test_results"])
            passed_tests = sum(
                1
                for test in benchmarks["test_results"].values()
                if test["meets_threshold"]
            )
            performance_ratio = passed_tests / total_tests

            if performance_ratio >= 0.9:
                benchmarks["overall_performance"] = "EXCELLENT"
            elif performance_ratio >= 0.7:
                benchmarks["overall_performance"] = "GOOD"
            elif performance_ratio >= 0.5:
                benchmarks["overall_performance"] = "FAIR"
            else:
                benchmarks["overall_performance"] = "POOR"

            # Generate recommendations
            benchmarks["recommendations"] = (
                PerformanceQueryHelper._generate_performance_recommendations(
                    benchmarks["test_results"]
                )
            )

            benchmarks["summary"] = {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "performance_score": round(performance_ratio * 100, 1),
                "overall_grade": benchmarks["overall_performance"],
            }

        except Exception as e:
            logger.error(f"Error during performance benchmarking: {e}")
            benchmarks["error"] = str(e)
            benchmarks["overall_performance"] = "ERROR"

        return benchmarks

    @staticmethod
    def _generate_performance_recommendations(
        test_results: Dict[str, Any],
    ) -> List[str]:
        """
        Generate performance recommendations based on benchmark results.

        Args:
            test_results: Dictionary of test results

        Returns:
            List[str]: Performance improvement recommendations
        """
        recommendations = []

        for test_name, result in test_results.items():
            if not result["meets_threshold"]:
                if test_name == "provider_filter":
                    recommendations.append(
                        "Consider adding composite index on (api_provider, status, created_at) "
                        "to optimize provider filtering queries"
                    )
                elif test_name == "provider_stats":
                    recommendations.append(
                        "Statistics queries may benefit from materialized views or caching "
                        "for frequently accessed provider statistics"
                    )
                elif test_name == "active_queue":
                    recommendations.append(
                        "Add composite index on (api_provider, status, priority) "
                        "to optimize active queue queries"
                    )
                elif test_name == "search_query":
                    recommendations.append(
                        "Consider full-text search indexes on prompt and error_message columns "
                        "for better search performance"
                    )

        if not recommendations:
            recommendations.append(
                "All queries meet performance thresholds - excellent performance!"
            )

        return recommendations

    @staticmethod
    def validate_provider_indexes() -> Dict[str, Any]:
        """
        Validate that all required provider indexes exist and are effective.

        Returns:
            Dict[str, Any]: Index validation results and recommendations
        """
        try:
            with get_db_session() as session:
                inspector = inspect(session.bind)
                indexes = inspector.get_indexes("video_jobs")

                # Required provider indexes
                required_indexes = {
                    "idx_provider_status": ["api_provider", "status"],
                    "idx_provider_created": ["api_provider", "created_at"],
                }

                index_validation = {}
                existing_index_names = [idx["name"] for idx in indexes]

                for required_name, required_columns in required_indexes.items():
                    exists = required_name in existing_index_names

                    if exists:
                        # Find the index details
                        index_info = next(
                            (idx for idx in indexes if idx["name"] == required_name),
                            None,
                        )

                        if index_info:
                            actual_columns = index_info["column_names"]
                            columns_match = set(actual_columns) == set(required_columns)
                        else:
                            columns_match = False
                    else:
                        columns_match = False

                    index_validation[required_name] = {
                        "exists": exists,
                        "columns_correct": columns_match,
                        "required_columns": required_columns,
                        "status": "VALID" if (exists and columns_match) else "INVALID",
                    }

                # Check for any additional beneficial indexes
                beneficial_indexes = (
                    PerformanceQueryHelper._identify_beneficial_indexes(indexes)
                )

                # Overall validation status
                all_required_valid = all(
                    idx["status"] == "VALID" for idx in index_validation.values()
                )

                return {
                    "validation_successful": True,
                    "all_required_indexes_valid": all_required_valid,
                    "required_indexes": index_validation,
                    "beneficial_indexes": beneficial_indexes,
                    "existing_indexes": [idx["name"] for idx in indexes],
                    "recommendations": PerformanceQueryHelper._generate_index_recommendations(
                        index_validation, beneficial_indexes
                    ),
                }

        except SQLAlchemyError as e:
            logger.error(f"Error validating provider indexes: {e}")
            return {"validation_successful": False, "error": str(e)}

    @staticmethod
    def _identify_beneficial_indexes(existing_indexes: List[Dict]) -> Dict[str, Any]:
        """
        Identify additional indexes that could benefit performance.

        Args:
            existing_indexes: List of existing index information

        Returns:
            Dict[str, Any]: Analysis of beneficial additional indexes
        """
        existing_names = [idx["name"] for idx in existing_indexes]

        # Potentially beneficial composite indexes
        beneficial = {
            "idx_session_provider": {
                "columns": ["session_id", "api_provider"],
                "benefit": "Optimizes session-specific provider queries",
                "exists": "idx_session_provider" in existing_names,
            },
            "idx_provider_priority": {
                "columns": ["api_provider", "priority", "created_at"],
                "benefit": "Optimizes priority-based queue operations",
                "exists": "idx_provider_priority" in existing_names,
            },
            "idx_provider_completed": {
                "columns": ["api_provider", "completed_at"],
                "benefit": "Optimizes performance metrics calculations",
                "exists": "idx_provider_completed" in existing_names,
            },
        }

        return beneficial

    @staticmethod
    def _generate_index_recommendations(
        index_validation: Dict[str, Any], beneficial_indexes: Dict[str, Any]
    ) -> List[str]:
        """
        Generate index optimization recommendations.

        Args:
            index_validation: Results of required index validation
            beneficial_indexes: Analysis of beneficial additional indexes

        Returns:
            List[str]: Index optimization recommendations
        """
        recommendations = []

        # Check for missing required indexes
        for index_name, validation in index_validation.items():
            if validation["status"] != "VALID":
                recommendations.append(
                    f"CRITICAL: Create missing required index {index_name} "
                    f"on columns {validation['required_columns']}"
                )

        # Check for beneficial additional indexes
        for index_name, info in beneficial_indexes.items():
            if not info["exists"]:
                recommendations.append(
                    f"OPTIONAL: Consider creating index {index_name} "
                    f"on columns {info['columns']} - {info['benefit']}"
                )

        if not recommendations:
            recommendations.append("All required indexes are properly configured!")

        return recommendations

    @staticmethod
    def get_query_performance_report() -> Dict[str, Any]:
        """
        Generate comprehensive query performance report.

        Returns:
            Dict[str, Any]: Complete performance analysis report
        """
        report = {
            "timestamp": time.time(),
            "report_type": "provider_query_performance",
            "sections": {},
        }

        try:
            # Section 1: Benchmark results
            benchmark_results = PerformanceQueryHelper.benchmark_provider_queries()
            report["sections"]["benchmarks"] = benchmark_results

            # Section 2: Index validation
            index_validation = PerformanceQueryHelper.validate_provider_indexes()
            report["sections"]["index_validation"] = index_validation

            # Section 3: Sample query analysis
            sample_queries = [
                "SELECT * FROM video_jobs WHERE api_provider = 'azure_sora' AND status = 'pending' LIMIT 10",
                "SELECT api_provider, COUNT(*) FROM video_jobs GROUP BY api_provider",
                "SELECT * FROM video_jobs WHERE api_provider = 'google_veo3' ORDER BY priority DESC, created_at ASC LIMIT 5",
            ]

            query_analysis = {}
            for i, query in enumerate(sample_queries):
                analysis = PerformanceQueryHelper.validate_index_usage(query)
                query_analysis[f"sample_query_{i + 1}"] = {
                    "sql": query,
                    "analysis": analysis,
                }

            report["sections"]["query_analysis"] = query_analysis

            # Overall assessment
            overall_score = 0
            if benchmark_results.get("summary", {}).get("performance_score"):
                overall_score += benchmark_results["summary"]["performance_score"] * 0.6

            if index_validation.get("all_required_indexes_valid"):
                overall_score += 40  # 40% for having all required indexes

            report["overall_performance_score"] = round(overall_score, 1)

            if overall_score >= 90:
                report["overall_grade"] = "EXCELLENT"
            elif overall_score >= 80:
                report["overall_grade"] = "GOOD"
            elif overall_score >= 70:
                report["overall_grade"] = "FAIR"
            else:
                report["overall_grade"] = "NEEDS_IMPROVEMENT"

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            report["error"] = str(e)
            report["overall_grade"] = "ERROR"

        return report
