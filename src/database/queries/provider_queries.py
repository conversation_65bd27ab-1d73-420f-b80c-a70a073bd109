"""
Provider query helpers for dual-provider database operations.

Provides optimized database queries for Azure Sora and Google Veo3 providers
with performance monitoring and statistics collection.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Literal, Optional

from sqlalchemy import and_, desc, or_
from sqlalchemy.exc import SQLAlchemyError

from src.database.connection import get_db_session
from src.database.models import VideoJobDB

logger = logging.getLogger(__name__)

# Type aliases for better code readability
ProviderType = Literal["azure_sora", "google_veo3"]
StatusType = Literal["pending", "running", "succeeded", "failed"]


class ProviderQueryHelper:
    """
    Optimized provider-aware database queries for dual-provider support.

    Provides high-performance database operations with comprehensive monitoring
    and statistics collection for Azure Sora and Google Veo3 providers.
    """

    @staticmethod
    def get_jobs_by_provider(
        api_provider: ProviderType,
        status: Optional[StatusType] = None,
        limit: int = 100,
        session_id: Optional[str] = None,
    ) -> List[VideoJobDB]:
        """
        Get jobs filtered by provider with optional status and session filtering.

        Args:
            api_provider: Provider to filter by (azure_sora, google_veo3)
            status: Optional status filter
            limit: Maximum number of results (default: 100)
            session_id: Optional session ID filter

        Returns:
            List[VideoJobDB]: Filtered jobs ordered by creation time (newest first)
        """
        try:
            with get_db_session() as session:
                query = session.query(VideoJobDB).filter_by(api_provider=api_provider)

                # Add optional filters
                if status:
                    query = query.filter_by(status=status)
                if session_id:
                    query = query.filter_by(session_id=session_id)

                # Order by newest first and apply limit
                query = query.order_by(desc(VideoJobDB.created_at)).limit(limit)

                return query.all()

        except SQLAlchemyError as e:
            logger.error(f"Error querying jobs by provider {api_provider}: {e}")
            return []

    @staticmethod
    def get_provider_statistics(
        session_id: Optional[str] = None, time_window_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive statistics by provider for monitoring dashboard.

        Args:
            session_id: Optional session ID filter
            time_window_hours: Optional time window in hours (default: all time)

        Returns:
            Dict[str, Any]: Statistics by provider with counts and performance metrics
        """
        try:
            with get_db_session() as session:
                base_query = session.query(VideoJobDB)

                # Apply optional filters
                if session_id:
                    base_query = base_query.filter_by(session_id=session_id)

                if time_window_hours:
                    cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
                    base_query = base_query.filter(VideoJobDB.created_at >= cutoff_time)

                # Get statistics for each provider
                stats = {}
                for provider in ["azure_sora", "google_veo3"]:
                    provider_query = base_query.filter_by(api_provider=provider)

                    stats[provider] = {
                        "total": provider_query.count(),
                        "pending": provider_query.filter_by(status="pending").count(),
                        "running": provider_query.filter_by(status="running").count(),
                        "succeeded": provider_query.filter_by(
                            status="succeeded"
                        ).count(),
                        "failed": provider_query.filter_by(status="failed").count(),
                    }

                    # Calculate success rate
                    total = stats[provider]["total"]
                    succeeded = stats[provider]["succeeded"]
                    stats[provider]["success_rate"] = (
                        (succeeded / total * 100) if total > 0 else 0.0
                    )

                # Add aggregate statistics
                total_jobs = sum(s["total"] for s in stats.values())
                total_succeeded = sum(s["succeeded"] for s in stats.values())

                stats["aggregate"] = {
                    "total_jobs": total_jobs,
                    "total_succeeded": total_succeeded,
                    "overall_success_rate": (
                        (total_succeeded / total_jobs * 100) if total_jobs > 0 else 0.0
                    ),
                    "query_timestamp": datetime.utcnow().isoformat(),
                }

                return stats

        except SQLAlchemyError as e:
            logger.error(f"Error getting provider statistics: {e}")
            return {"error": str(e)}

    @staticmethod
    def get_active_queue_by_provider(
        api_provider: ProviderType, session_id: Optional[str] = None
    ) -> List[VideoJobDB]:
        """
        Get active (pending/running) jobs by provider ordered by priority and creation time.

        Args:
            api_provider: Provider to filter by
            session_id: Optional session ID filter

        Returns:
            List[VideoJobDB]: Active jobs ordered by priority (high to low) then creation time
        """
        try:
            with get_db_session() as session:
                query = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider=api_provider)
                    .filter(VideoJobDB.status.in_(["pending", "running"]))
                )

                if session_id:
                    query = query.filter_by(session_id=session_id)

                # Order by priority (high to low) then creation time (oldest first for FIFO)
                query = query.order_by(desc(VideoJobDB.priority), VideoJobDB.created_at)

                return query.all()

        except SQLAlchemyError as e:
            logger.error(f"Error getting active queue for provider {api_provider}: {e}")
            return []

    @staticmethod
    def get_jobs_by_time_range(
        api_provider: ProviderType,
        start_time: datetime,
        end_time: datetime,
        status: Optional[StatusType] = None,
    ) -> List[VideoJobDB]:
        """
        Get jobs within a specific time range for a provider.

        Args:
            api_provider: Provider to filter by
            start_time: Start of time range
            end_time: End of time range
            status: Optional status filter

        Returns:
            List[VideoJobDB]: Jobs within time range ordered by creation time
        """
        try:
            with get_db_session() as session:
                query = session.query(VideoJobDB).filter(
                    and_(
                        VideoJobDB.api_provider == api_provider,
                        VideoJobDB.created_at >= start_time,
                        VideoJobDB.created_at <= end_time,
                    )
                )

                if status:
                    query = query.filter_by(status=status)

                return query.order_by(VideoJobDB.created_at).all()

        except SQLAlchemyError as e:
            logger.error(
                f"Error getting jobs by time range for provider {api_provider}: {e}"
            )
            return []

    @staticmethod
    def get_provider_performance_metrics(
        api_provider: ProviderType, hours_back: int = 24
    ) -> Dict[str, Any]:
        """
        Get performance metrics for a specific provider.

        Args:
            api_provider: Provider to analyze
            hours_back: Hours to look back for metrics (default: 24)

        Returns:
            Dict[str, Any]: Performance metrics including average processing time
        """
        try:
            with get_db_session() as session:
                cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)

                # Get completed jobs (succeeded or failed) within time window
                completed_jobs = (
                    session.query(VideoJobDB)
                    .filter(
                        and_(
                            VideoJobDB.api_provider == api_provider,
                            VideoJobDB.status.in_(["succeeded", "failed"]),
                            VideoJobDB.created_at >= cutoff_time,
                            VideoJobDB.completed_at.isnot(None),
                        )
                    )
                    .all()
                )

                if not completed_jobs:
                    return {
                        "provider": api_provider,
                        "time_window_hours": hours_back,
                        "sample_size": 0,
                        "metrics": {},
                    }

                # Calculate processing times
                processing_times = []
                for job in completed_jobs:
                    if job.completed_at and job.created_at:
                        processing_time = (
                            job.completed_at - job.created_at
                        ).total_seconds()
                        processing_times.append(processing_time)

                if processing_times:
                    avg_processing_time = sum(processing_times) / len(processing_times)
                    min_processing_time = min(processing_times)
                    max_processing_time = max(processing_times)

                    # Calculate percentiles
                    sorted_times = sorted(processing_times)
                    n = len(sorted_times)
                    p50 = sorted_times[n // 2] if n > 0 else 0
                    p95 = sorted_times[int(n * 0.95)] if n > 0 else 0
                    p99 = sorted_times[int(n * 0.99)] if n > 0 else 0
                else:
                    avg_processing_time = min_processing_time = max_processing_time = 0
                    p50 = p95 = p99 = 0

                # Success rate for the time window
                succeeded_count = sum(
                    1 for job in completed_jobs if job.status == "succeeded"
                )
                success_rate = (
                    (succeeded_count / len(completed_jobs) * 100)
                    if completed_jobs
                    else 0
                )

                return {
                    "provider": api_provider,
                    "time_window_hours": hours_back,
                    "sample_size": len(completed_jobs),
                    "metrics": {
                        "avg_processing_time_seconds": round(avg_processing_time, 2),
                        "min_processing_time_seconds": round(min_processing_time, 2),
                        "max_processing_time_seconds": round(max_processing_time, 2),
                        "p50_processing_time_seconds": round(p50, 2),
                        "p95_processing_time_seconds": round(p95, 2),
                        "p99_processing_time_seconds": round(p99, 2),
                        "success_rate_percent": round(success_rate, 2),
                        "succeeded_jobs": succeeded_count,
                        "failed_jobs": len(completed_jobs) - succeeded_count,
                    },
                }

        except SQLAlchemyError as e:
            logger.error(
                f"Error getting performance metrics for provider {api_provider}: {e}"
            )
            return {"error": str(e)}

    @staticmethod
    def search_jobs_by_provider(
        api_provider: ProviderType, search_term: str, limit: int = 50
    ) -> List[VideoJobDB]:
        """
        Search jobs by provider with text search in prompt or error message.

        Args:
            api_provider: Provider to filter by
            search_term: Text to search for in prompt or error message
            limit: Maximum number of results

        Returns:
            List[VideoJobDB]: Matching jobs ordered by relevance and creation time
        """
        try:
            with get_db_session() as session:
                search_pattern = f"%{search_term}%"

                query = (
                    session.query(VideoJobDB)
                    .filter(
                        and_(
                            VideoJobDB.api_provider == api_provider,
                            or_(
                                VideoJobDB.prompt.like(search_pattern),
                                VideoJobDB.error_message.like(search_pattern),
                            ),
                        )
                    )
                    .order_by(desc(VideoJobDB.created_at))
                    .limit(limit)
                )

                return query.all()

        except SQLAlchemyError as e:
            logger.error(f"Error searching jobs for provider {api_provider}: {e}")
            return []

    @staticmethod
    def get_provider_job_distribution() -> Dict[str, Any]:
        """
        Get job distribution across all providers for system monitoring.

        Returns:
            Dict[str, Any]: Job distribution statistics and trends
        """
        try:
            with get_db_session() as session:
                # Total counts by provider
                distribution = {}

                for provider in ["azure_sora", "google_veo3"]:
                    count = (
                        session.query(VideoJobDB)
                        .filter_by(api_provider=provider)
                        .count()
                    )
                    distribution[provider] = count

                total_jobs = sum(distribution.values())

                # Calculate percentages
                for provider in distribution:
                    distribution[f"{provider}_percentage"] = (
                        (distribution[provider] / total_jobs * 100)
                        if total_jobs > 0
                        else 0.0
                    )

                # Recent activity (last 24 hours)
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                recent_activity = {}

                for provider in ["azure_sora", "google_veo3"]:
                    recent_count = (
                        session.query(VideoJobDB)
                        .filter(
                            and_(
                                VideoJobDB.api_provider == provider,
                                VideoJobDB.created_at >= cutoff_time,
                            )
                        )
                        .count()
                    )
                    recent_activity[f"{provider}_recent"] = recent_count

                return {
                    "total_jobs": total_jobs,
                    "distribution": distribution,
                    "recent_activity_24h": recent_activity,
                    "updated_at": datetime.utcnow().isoformat(),
                }

        except SQLAlchemyError as e:
            logger.error(f"Error getting provider job distribution: {e}")
            return {"error": str(e)}
