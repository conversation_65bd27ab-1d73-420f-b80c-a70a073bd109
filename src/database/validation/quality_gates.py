"""Quality gates framework for database schema extensions."""

import subprocess
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List

from src.database.validation.migration_validator import MigrationValidator


@dataclass
class QualityGateResult:
    """Result of a quality gate check."""

    passed: bool
    message: str
    details: Dict[str, Any]
    execution_time: float


class QualityGatesFramework:
    """
    Comprehensive quality gates for database schema extensions.

    Validates code quality, test coverage, performance, and migration safety
    before allowing production deployment.
    """

    def __init__(self, project_root: str = "/workspace"):
        """
        Initialize quality gates framework.

        Args:
            project_root (str): Root directory of the project
        """
        self.project_root = Path(project_root)
        self.results: List[QualityGateResult] = []

    def run_all_gates(self) -> Dict[str, Any]:
        """
        Run all quality gates and return comprehensive results.

        Returns:
            Dict[str, Any]: Aggregated quality gate results
        """
        print("🚀 Running F1-Database Schema Extensions Quality Gates...")

        # Core quality gates
        gates = [
            ("Code Quality", self._check_code_quality),
            ("Type Safety", self._check_type_safety),
            ("Test Coverage", self._check_test_coverage),
            ("Migration Safety", self._check_migration_safety),
            ("Performance Benchmarks", self._check_performance_benchmarks),
            ("Integration Tests", self._check_integration_tests),
            ("Provider Model Validation", self._check_provider_models),
            ("Documentation Completeness", self._check_documentation),
        ]

        all_passed = True
        gate_results = {}

        for gate_name, gate_function in gates:
            print(f"⚡ Running {gate_name}...")
            start_time = time.time()

            try:
                result = gate_function()
                execution_time = time.time() - start_time

                gate_result = QualityGateResult(
                    passed=result["passed"],
                    message=result["message"],
                    details=result.get("details", {}),
                    execution_time=execution_time,
                )

                self.results.append(gate_result)
                gate_results[gate_name] = gate_result

                status = "✅ PASSED" if result["passed"] else "❌ FAILED"
                print(f"   {status} - {result['message']} ({execution_time:.2f}s)")

                if not result["passed"]:
                    all_passed = False

            except Exception as e:
                execution_time = time.time() - start_time
                gate_result = QualityGateResult(
                    passed=False,
                    message=f"Gate execution failed: {str(e)}",
                    details={"error": str(e)},
                    execution_time=execution_time,
                )

                self.results.append(gate_result)
                gate_results[gate_name] = gate_result
                all_passed = False
                print(
                    f"   ❌ FAILED - Gate execution error: {str(e)} ({execution_time:.2f}s)"
                )

        return {
            "all_gates_passed": all_passed,
            "total_gates": len(gates),
            "passed_gates": sum(1 for r in self.results if r.passed),
            "failed_gates": sum(1 for r in self.results if not r.passed),
            "total_execution_time": sum(r.execution_time for r in self.results),
            "gate_results": gate_results,
            "summary": self._generate_summary(),
        }

    def _check_code_quality(self) -> Dict[str, Any]:
        """Check code quality using Ruff."""
        try:
            # Run Ruff linting
            result = subprocess.run(
                ["uv", "run", "ruff", "check", "src/database/", "src/core/"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=60,
            )

            if result.returncode == 0:
                return {
                    "passed": True,
                    "message": "Code quality checks passed",
                    "details": {"linting_output": result.stdout or "No issues found"},
                }
            else:
                return {
                    "passed": False,
                    "message": "Code quality issues found",
                    "details": {
                        "linting_errors": result.stdout,
                        "stderr": result.stderr,
                    },
                }

        except subprocess.TimeoutExpired:
            return {
                "passed": False,
                "message": "Code quality check timed out",
                "details": {"error": "Ruff execution exceeded 60 seconds"},
            }
        except Exception as e:
            return {
                "passed": False,
                "message": f"Code quality check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_type_safety(self) -> Dict[str, Any]:
        """Check type safety using MyPy."""
        try:
            # Run MyPy type checking
            result = subprocess.run(
                ["uv", "run", "mypy", "src/database/models.py", "src/core/models.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120,
            )

            if result.returncode == 0:
                return {
                    "passed": True,
                    "message": "Type safety checks passed",
                    "details": {"mypy_output": result.stdout or "No type errors found"},
                }
            else:
                return {
                    "passed": False,
                    "message": "Type safety errors found",
                    "details": {"type_errors": result.stdout, "stderr": result.stderr},
                }

        except subprocess.TimeoutExpired:
            return {
                "passed": False,
                "message": "Type safety check timed out",
                "details": {"error": "MyPy execution exceeded 120 seconds"},
            }
        except Exception as e:
            return {
                "passed": False,
                "message": f"Type safety check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_test_coverage(self) -> Dict[str, Any]:
        """Check test coverage for provider extensions."""
        try:
            # Run pytest with coverage for provider-related tests
            result = subprocess.run(
                [
                    "uv",
                    "run",
                    "pytest",
                    "src/database/tests/test_provider_models.py",
                    "src/core/tests/test_provider_models.py",
                    "src/database/tests/test_provider_migration.py",
                    "--cov=src.database.models",
                    "--cov=src.core.models",
                    "--cov-report=term-missing",
                    "--cov-fail-under=95",
                    "-v",
                ],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,
            )

            if result.returncode == 0:
                # Parse coverage percentage from output
                coverage_line = [
                    line for line in result.stdout.split("\n") if "TOTAL" in line
                ]
                coverage_percent = "Unknown"
                if coverage_line:
                    parts = coverage_line[0].split()
                    if len(parts) >= 4:
                        coverage_percent = parts[3].rstrip("%")

                return {
                    "passed": True,
                    "message": f"Test coverage passed ({coverage_percent}%)",
                    "details": {
                        "coverage_output": result.stdout,
                        "coverage_percentage": coverage_percent,
                    },
                }
            else:
                return {
                    "passed": False,
                    "message": "Test coverage below threshold or tests failed",
                    "details": {
                        "test_output": result.stdout,
                        "test_errors": result.stderr,
                    },
                }

        except subprocess.TimeoutExpired:
            return {
                "passed": False,
                "message": "Test coverage check timed out",
                "details": {"error": "Test execution exceeded 300 seconds"},
            }
        except Exception as e:
            return {
                "passed": False,
                "message": f"Test coverage check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_migration_safety(self) -> Dict[str, Any]:
        """Check migration safety using MigrationValidator."""
        try:
            # Use SQLite for testing migration safety
            database_url = "sqlite:///instance/sora_poc.db"
            validator = MigrationValidator(database_url)

            # Run pre-migration validation
            validation_results = validator.validate_migration_safety()

            if validation_results["safe_to_migrate"]:
                return {
                    "passed": True,
                    "message": "Migration safety validation passed",
                    "details": validation_results,
                }
            else:
                return {
                    "passed": False,
                    "message": "Migration safety validation failed",
                    "details": validation_results,
                }

        except Exception as e:
            return {
                "passed": False,
                "message": f"Migration safety check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_performance_benchmarks(self) -> Dict[str, Any]:
        """Check performance benchmarks for provider queries."""
        try:
            # Use existing database for performance testing
            database_url = "sqlite:///instance/sora_poc.db"
            validator = MigrationValidator(database_url)

            # Test query performance
            with validator.session_factory() as session:
                from src.database.models import VideoJobDB

                # Test provider query performance
                start_time = time.time()
                azure_count = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="azure_sora")
                    .count()
                )
                azure_query_time = time.time() - start_time

                start_time = time.time()
                veo3_count = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="google_veo3")
                    .count()
                )
                veo3_query_time = time.time() - start_time

                # Test combined queries
                start_time = time.time()
                combined_count = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="azure_sora", status="pending")
                    .count()
                )
                combined_query_time = time.time() - start_time

                # Check if queries meet <100ms requirement
                max_query_time = max(
                    azure_query_time, veo3_query_time, combined_query_time
                )
                meets_requirement = max_query_time < 0.1  # 100ms

            return {
                "passed": meets_requirement,
                "message": f"Performance benchmarks {'passed' if meets_requirement else 'failed'} (max: {max_query_time * 1000:.2f}ms)",
                "details": {
                    "azure_query_time_ms": azure_query_time * 1000,
                    "veo3_query_time_ms": veo3_query_time * 1000,
                    "combined_query_time_ms": combined_query_time * 1000,
                    "max_query_time_ms": max_query_time * 1000,
                    "requirement_ms": 100,
                    "azure_count": azure_count,
                    "veo3_count": veo3_count,
                    "combined_count": combined_count,
                },
            }

        except Exception as e:
            return {
                "passed": False,
                "message": f"Performance benchmark check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_integration_tests(self) -> Dict[str, Any]:
        """Check that integration tests pass."""
        try:
            # Run integration tests
            result = subprocess.run(
                [
                    "uv",
                    "run",
                    "pytest",
                    "src/database/tests/test_database.py",
                    "-v",
                    "-k",
                    "test_",  # Run all tests
                ],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=180,
            )

            passed_count = result.stdout.count(" PASSED")
            failed_count = result.stdout.count(" FAILED")
            total_tests = passed_count + failed_count

            if result.returncode == 0 and failed_count == 0:
                return {
                    "passed": True,
                    "message": f"Integration tests passed ({passed_count}/{total_tests})",
                    "details": {
                        "test_output": result.stdout,
                        "passed_tests": passed_count,
                        "failed_tests": failed_count,
                        "total_tests": total_tests,
                    },
                }
            else:
                return {
                    "passed": False,
                    "message": f"Integration tests failed ({failed_count}/{total_tests} failed)",
                    "details": {
                        "test_output": result.stdout,
                        "test_errors": result.stderr,
                        "passed_tests": passed_count,
                        "failed_tests": failed_count,
                        "total_tests": total_tests,
                    },
                }

        except subprocess.TimeoutExpired:
            return {
                "passed": False,
                "message": "Integration tests timed out",
                "details": {"error": "Test execution exceeded 180 seconds"},
            }
        except Exception as e:
            return {
                "passed": False,
                "message": f"Integration tests check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_provider_models(self) -> Dict[str, Any]:
        """Check provider model validation specifically."""
        try:
            # Test model instantiation and validation
            from datetime import datetime

            from src.core.models import VideoJob
            from src.database.models import VideoJobDB

            test_results = []

            # Test 1: Create VideoJob with Azure provider
            try:
                azure_job = VideoJob(
                    id="test-azure-validation",
                    prompt="Azure provider test",
                    status="pending",
                    created_at=datetime.utcnow(),
                    api_provider="azure_sora",
                    input_image_path=None,
                    audio_generated=False,
                )
                test_results.append(("Azure VideoJob creation", True, "Success"))
            except Exception as e:
                test_results.append(("Azure VideoJob creation", False, str(e)))

            # Test 2: Create VideoJob with Veo3 provider
            try:
                veo3_job = VideoJob(
                    id="test-veo3-validation",
                    prompt="Veo3 provider test",
                    status="pending",
                    created_at=datetime.utcnow(),
                    api_provider="google_veo3",
                    input_image_path="/test/image.jpg",
                    audio_generated=True,
                )
                test_results.append(("Veo3 VideoJob creation", True, "Success"))
            except Exception as e:
                test_results.append(("Veo3 VideoJob creation", False, str(e)))

            # Test 3: Test provider validation
            try:
                invalid_job = VideoJob(
                    id="test-invalid-provider",
                    prompt="Invalid provider test",
                    status="pending",
                    created_at=datetime.utcnow(),
                    api_provider="invalid_provider",
                )
                test_results.append(
                    (
                        "Invalid provider rejection",
                        False,
                        "Should have failed but didn't",
                    )
                )
            except Exception:
                test_results.append(
                    (
                        "Invalid provider rejection",
                        True,
                        "Correctly rejected invalid provider",
                    )
                )

            # Test 4: Test ORM conversion
            try:
                if "azure_job" in locals():
                    db_job = VideoJobDB.from_pydantic(azure_job)
                    converted_back = db_job.to_pydantic()

                    conversion_success = (
                        converted_back.api_provider == azure_job.api_provider
                        and converted_back.input_image_path
                        == azure_job.input_image_path
                        and converted_back.audio_generated == azure_job.audio_generated
                    )

                    if conversion_success:
                        test_results.append(
                            (
                                "ORM conversion",
                                True,
                                "Bidirectional conversion successful",
                            )
                        )
                    else:
                        test_results.append(
                            ("ORM conversion", False, "Conversion data mismatch")
                        )
                else:
                    test_results.append(
                        ("ORM conversion", False, "No azure_job to test")
                    )

            except Exception as e:
                test_results.append(("ORM conversion", False, str(e)))

            # Evaluate results
            total_tests = len(test_results)
            passed_tests = sum(1 for _, passed, _ in test_results if passed)
            all_passed = passed_tests == total_tests

            return {
                "passed": all_passed,
                "message": f"Provider model validation {'passed' if all_passed else 'failed'} ({passed_tests}/{total_tests})",
                "details": {
                    "test_results": test_results,
                    "passed_tests": passed_tests,
                    "total_tests": total_tests,
                },
            }

        except Exception as e:
            return {
                "passed": False,
                "message": f"Provider model validation failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_documentation(self) -> Dict[str, Any]:
        """Check documentation completeness."""
        try:
            # Check that key files exist and contain provider documentation
            required_docs = [
                (self.project_root / "src" / "database" / "models.py", "provider"),
                (self.project_root / "src" / "core" / "models.py", "api_provider"),
                (
                    self.project_root
                    / "migrations"
                    / "versions"
                    / "ec958cfc4cd8_add_provider_support.py",
                    "provider",
                ),
            ]

            doc_results = []
            for file_path, keyword in required_docs:
                if file_path.exists():
                    content = file_path.read_text()
                    if keyword.lower() in content.lower():
                        doc_results.append(
                            (
                                str(file_path.name),
                                True,
                                f"Contains '{keyword}' documentation",
                            )
                        )
                    else:
                        doc_results.append(
                            (
                                str(file_path.name),
                                False,
                                f"Missing '{keyword}' documentation",
                            )
                        )
                else:
                    doc_results.append((str(file_path.name), False, "File not found"))

            # Check for docstrings in key functions
            models_file = self.project_root / "src" / "database" / "models.py"
            if models_file.exists():
                content = models_file.read_text()
                has_docstrings = (
                    '"""' in content and "Args:" in content and "Returns:" in content
                )
                doc_results.append(
                    (
                        "Function docstrings",
                        has_docstrings,
                        "Complete function documentation",
                    )
                )

            total_checks = len(doc_results)
            passed_checks = sum(1 for _, passed, _ in doc_results if passed)
            all_passed = passed_checks == total_checks

            return {
                "passed": all_passed,
                "message": f"Documentation {'complete' if all_passed else 'incomplete'} ({passed_checks}/{total_checks})",
                "details": {
                    "documentation_checks": doc_results,
                    "passed_checks": passed_checks,
                    "total_checks": total_checks,
                },
            }

        except Exception as e:
            return {
                "passed": False,
                "message": f"Documentation check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _generate_summary(self) -> str:
        """Generate a summary of all quality gate results."""
        total_gates = len(self.results)
        passed_gates = sum(1 for r in self.results if r.passed)
        failed_gates = total_gates - passed_gates

        summary = [
            "=" * 60,
            "F1-DATABASE SCHEMA EXTENSIONS - QUALITY GATES SUMMARY",
            "=" * 60,
            f"Total Gates: {total_gates}",
            f"Passed: {passed_gates} ✅",
            f"Failed: {failed_gates} ❌",
            f"Success Rate: {(passed_gates / total_gates) * 100:.1f}%",
            f"Total Execution Time: {sum(r.execution_time for r in self.results):.2f}s",
            "",
        ]

        if failed_gates > 0:
            summary.append("FAILED GATES:")
            for i, result in enumerate(self.results):
                if not result.passed:
                    summary.append(f"  {i + 1}. {result.message}")
            summary.append("")

        summary.extend(
            [
                "DEPLOYMENT READINESS:",
                "✅ Ready for Production"
                if failed_gates == 0
                else f"❌ Not Ready - {failed_gates} gates failed",
                "=" * 60,
            ]
        )

        return "\n".join(summary)

    def generate_quality_report(self) -> str:
        """Generate detailed quality report."""
        report = [
            "=" * 80,
            "F1-DATABASE SCHEMA EXTENSIONS - COMPREHENSIVE QUALITY REPORT",
            "=" * 80,
            f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "",
        ]

        for i, result in enumerate(self.results, 1):
            status = "PASSED ✅" if result.passed else "FAILED ❌"
            report.extend(
                [
                    f"{i}. QUALITY GATE: {status}",
                    f"   Message: {result.message}",
                    f"   Execution Time: {result.execution_time:.2f}s",
                ]
            )

            if result.details:
                report.append("   Details:")
                for key, value in result.details.items():
                    if isinstance(value, (str, int, float, bool)):
                        report.append(f"     {key}: {value}")
                    elif isinstance(value, list):
                        report.append(f"     {key}: {len(value)} items")
                    else:
                        report.append(f"     {key}: {type(value).__name__}")

            report.append("")

        report.append(self._generate_summary())
        return "\n".join(report)
