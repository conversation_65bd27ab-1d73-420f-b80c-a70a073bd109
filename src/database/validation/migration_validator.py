"""Migration validation framework for provider support schema extensions."""

import logging
import time
from datetime import datetime
from typing import Any, Dict

from sqlalchemy import create_engine, inspect
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import sessionmaker

from src.core.models import VideoJob
from src.database.models import VideoJobDB

logger = logging.getLogger(__name__)


class MigrationValidator:
    """
    Validates database migrations for safety and correctness.

    Provides comprehensive validation for provider support migration
    including data preservation, performance testing, and rollback validation.
    """

    def __init__(self, database_url: str):
        """
        Initialize migration validator.

        Args:
            database_url (str): Database connection URL
        """
        self.database_url = database_url
        self.engine = create_engine(database_url, echo=False)
        self.session_factory = sessionmaker(bind=self.engine)

    def validate_migration_safety(self) -> Dict[str, Any]:
        """
        Comprehensive migration safety validation.

        Returns:
            Dict[str, Any]: Validation results with safety metrics
        """
        results = {
            "safe_to_migrate": True,
            "warnings": [],
            "errors": [],
            "metrics": {},
            "recommendations": [],
        }

        try:
            # Check current schema state
            schema_check = self._validate_current_schema()
            results["metrics"]["schema_validation"] = schema_check

            # Validate data integrity requirements
            data_check = self._validate_data_integrity()
            results["metrics"]["data_integrity"] = data_check

            # Check migration performance requirements
            performance_check = self._validate_migration_performance()
            results["metrics"]["performance"] = performance_check

            # Validate rollback safety
            rollback_check = self._validate_rollback_safety()
            results["metrics"]["rollback_safety"] = rollback_check

            # Validate provider field constraints
            provider_validation = self._validate_provider_field_constraints()
            results["metrics"]["provider_constraints"] = provider_validation

            # Validate provider data consistency
            provider_data_check = self._validate_provider_data_consistency()
            results["metrics"]["provider_data_consistency"] = provider_data_check

            # Aggregate safety assessment
            if any(
                [
                    not schema_check["valid"],
                    not data_check["valid"],
                    not performance_check["meets_requirements"],
                    not rollback_check["safe"],
                    not provider_validation["valid"],
                    not provider_data_check["valid"],
                ]
            ):
                results["safe_to_migrate"] = False

        except Exception as e:
            logger.error(f"Migration validation failed: {e}")
            results["safe_to_migrate"] = False
            results["errors"].append(f"Validation error: {str(e)}")

        return results

    def _validate_current_schema(self) -> Dict[str, Any]:
        """Validate current database schema state."""
        try:
            inspector = inspect(self.engine)

            # Check if video_jobs table exists
            tables = inspector.get_table_names()
            if "video_jobs" not in tables:
                return {"valid": False, "error": "video_jobs table not found"}

            # Check current columns
            columns = inspector.get_columns("video_jobs")
            column_names = [col["name"] for col in columns]

            # Required base columns
            required_columns = [
                "id",
                "prompt",
                "status",
                "created_at",
                "session_id",
                "priority",
                "queue_position",
                "retry_count",
            ]

            missing_columns = [
                col for col in required_columns if col not in column_names
            ]
            if missing_columns:
                return {
                    "valid": False,
                    "error": f"Missing required columns: {missing_columns}",
                }

            # Check if provider columns already exist
            provider_columns = ["api_provider", "input_image_path", "audio_generated"]
            existing_provider_columns = [
                col for col in provider_columns if col in column_names
            ]

            return {
                "valid": True,
                "current_columns": column_names,
                "existing_provider_columns": existing_provider_columns,
                "migration_needed": len(existing_provider_columns)
                < len(provider_columns),
            }

        except SQLAlchemyError as e:
            return {"valid": False, "error": f"Schema validation error: {str(e)}"}

    def _validate_data_integrity(self) -> Dict[str, Any]:
        """Validate existing data can be safely migrated."""
        try:
            with self.session_factory() as session:
                # Check total record count
                total_jobs = session.query(VideoJobDB).count()

                # Check for any NULL values in critical fields
                null_checks = {
                    "null_ids": session.query(VideoJobDB)
                    .filter(VideoJobDB.id.is_(None))
                    .count(),
                    "null_prompts": session.query(VideoJobDB)
                    .filter(VideoJobDB.prompt.is_(None))
                    .count(),
                    "null_status": session.query(VideoJobDB)
                    .filter(VideoJobDB.status.is_(None))
                    .count(),
                }

                # Check status value distribution
                status_distribution = {}
                for status in ["pending", "running", "succeeded", "failed"]:
                    count = session.query(VideoJobDB).filter_by(status=status).count()
                    status_distribution[status] = count

                # Check for any invalid status values
                valid_statuses = ["pending", "running", "succeeded", "failed"]
                invalid_status_count = (
                    session.query(VideoJobDB)
                    .filter(~VideoJobDB.status.in_(valid_statuses))
                    .count()
                )

                return {
                    "valid": all(count == 0 for count in null_checks.values())
                    and invalid_status_count == 0,
                    "total_records": total_jobs,
                    "null_checks": null_checks,
                    "status_distribution": status_distribution,
                    "invalid_status_count": invalid_status_count,
                }

        except SQLAlchemyError as e:
            return {
                "valid": False,
                "error": f"Data integrity validation error: {str(e)}",
            }

    def _validate_migration_performance(self) -> Dict[str, Any]:
        """Validate migration will meet performance requirements."""
        try:
            with self.session_factory() as session:
                # Estimate migration time based on record count
                total_records = session.query(VideoJobDB).count()

                # Test query performance before migration
                start_time = time.time()
                session.query(VideoJobDB).filter_by(status="pending").count()
                base_query_time = time.time() - start_time

                # Estimate migration time (rough calculation)
                # Assume 1000 records per second for ALTER TABLE operations
                estimated_migration_seconds = max(
                    total_records / 1000, 30
                )  # Minimum 30 seconds

                # Check if meets requirements (<5 minutes = 300 seconds)
                meets_time_requirement = estimated_migration_seconds < 300

                return {
                    "meets_requirements": meets_time_requirement,
                    "total_records": total_records,
                    "estimated_migration_time_seconds": estimated_migration_seconds,
                    "base_query_time_ms": base_query_time * 1000,
                    "time_requirement_seconds": 300,
                }

        except SQLAlchemyError as e:
            return {
                "meets_requirements": False,
                "error": f"Performance validation error: {str(e)}",
            }

    def _validate_rollback_safety(self) -> Dict[str, Any]:
        """Validate migration can be safely rolled back."""
        try:
            # Check if we have backup procedures in place
            inspector = inspect(self.engine)

            # Verify we can identify essential columns for rollback
            columns = inspector.get_columns("video_jobs")
            essential_columns = [
                "id",
                "prompt",
                "status",
                "created_at",
                "completed_at",
                "generation_id",
                "error_message",
                "file_path",
                "download_url",
                "session_id",
                "priority",
                "queue_position",
                "retry_count",
            ]

            column_names = [col["name"] for col in columns]
            missing_essential = [
                col for col in essential_columns if col not in column_names
            ]

            # Check current indexes
            indexes = inspector.get_indexes("video_jobs")
            essential_indexes = ["idx_session_status", "idx_priority_created"]

            existing_index_names = [idx["name"] for idx in indexes]
            missing_essential_indexes = [
                idx for idx in essential_indexes if idx not in existing_index_names
            ]

            return {
                "safe": len(missing_essential) == 0,
                "essential_columns_present": len(missing_essential) == 0,
                "missing_essential_columns": missing_essential,
                "essential_indexes_present": len(missing_essential_indexes) == 0,
                "missing_essential_indexes": missing_essential_indexes,
                "rollback_plan": "Drop provider columns and indexes, preserve essential data",
            }

        except SQLAlchemyError as e:
            return {"safe": False, "error": f"Rollback validation error: {str(e)}"}

    def validate_post_migration(self) -> Dict[str, Any]:
        """
        Validate system state after migration completion.

        Returns:
            Dict[str, Any]: Post-migration validation results
        """
        results = {
            "migration_successful": True,
            "errors": [],
            "warnings": [],
            "validation_metrics": {},
        }

        try:
            # Validate provider columns exist
            column_validation = self._validate_provider_columns_exist()
            results["validation_metrics"]["columns"] = column_validation

            # Validate indexes exist and perform well
            index_validation = self._validate_provider_indexes()
            results["validation_metrics"]["indexes"] = index_validation

            # Validate data conversion
            data_validation = self._validate_provider_data_conversion()
            results["validation_metrics"]["data_conversion"] = data_validation

            # Validate ORM integration
            orm_validation = self._validate_orm_integration()
            results["validation_metrics"]["orm_integration"] = orm_validation

            # Test provider-aware queries
            query_validation = self._validate_provider_queries()
            results["validation_metrics"]["provider_queries"] = query_validation

            # Aggregate results
            if any(
                [
                    not column_validation["valid"],
                    not index_validation["valid"],
                    not data_validation["valid"],
                    not orm_validation["valid"],
                    not query_validation["valid"],
                ]
            ):
                results["migration_successful"] = False

        except Exception as e:
            logger.error(f"Post-migration validation failed: {e}")
            results["migration_successful"] = False
            results["errors"].append(f"Post-migration validation error: {str(e)}")

        return results

    def _validate_provider_columns_exist(self) -> Dict[str, Any]:
        """Validate provider columns were created correctly."""
        try:
            inspector = inspect(self.engine)
            columns = inspector.get_columns("video_jobs")
            column_info = {col["name"]: col for col in columns}

            # Check required provider columns
            required_provider_columns = {
                "api_provider": {"type": "VARCHAR", "nullable": False},
                "input_image_path": {"type": "TEXT", "nullable": True},
                "audio_generated": {"type": "BOOLEAN", "nullable": False},
            }

            validation_results = {}
            all_valid = True

            for col_name, requirements in required_provider_columns.items():
                if col_name not in column_info:
                    validation_results[col_name] = {"exists": False, "valid": False}
                    all_valid = False
                else:
                    col_info = column_info[col_name]
                    validation_results[col_name] = {
                        "exists": True,
                        "type_correct": str(col_info["type"])
                        .upper()
                        .startswith(requirements["type"]),
                        "nullable_correct": col_info["nullable"]
                        == requirements["nullable"],
                        "valid": True,  # Basic existence check
                    }

            return {"valid": all_valid, "column_validation": validation_results}

        except SQLAlchemyError as e:
            return {"valid": False, "error": f"Column validation error: {str(e)}"}

    def _validate_provider_indexes(self) -> Dict[str, Any]:
        """Validate provider indexes were created and perform well."""
        try:
            inspector = inspect(self.engine)
            indexes = inspector.get_indexes("video_jobs")
            index_names = [idx["name"] for idx in indexes]

            # Check required provider indexes
            required_indexes = ["idx_provider_status", "idx_provider_created"]
            missing_indexes = [
                idx for idx in required_indexes if idx not in index_names
            ]

            # Test index performance
            performance_results = {}
            with self.session_factory() as session:
                for provider in ["azure_sora", "google_veo3"]:
                    start_time = time.time()
                    count = (
                        session.query(VideoJobDB)
                        .filter_by(api_provider=provider)
                        .count()
                    )
                    query_time = time.time() - start_time

                    performance_results[f"{provider}_query"] = {
                        "time_ms": query_time * 1000,
                        "meets_requirement": query_time < 0.1,  # <100ms requirement
                        "result_count": count,
                    }

            return {
                "valid": len(missing_indexes) == 0,
                "missing_indexes": missing_indexes,
                "performance_results": performance_results,
            }

        except SQLAlchemyError as e:
            return {"valid": False, "error": f"Index validation error: {str(e)}"}

    def _validate_provider_data_conversion(self) -> Dict[str, Any]:
        """Validate existing data was converted correctly."""
        try:
            with self.session_factory() as session:
                # Check that all existing records have default provider values
                total_jobs = session.query(VideoJobDB).count()
                jobs_with_provider = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.api_provider.isnot(None))
                    .count()
                )

                # Check default values are applied correctly
                azure_defaults = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="azure_sora", audio_generated=False)
                    .count()
                )

                return {
                    "valid": jobs_with_provider == total_jobs,
                    "total_jobs": total_jobs,
                    "jobs_with_provider": jobs_with_provider,
                    "jobs_with_defaults": azure_defaults,
                    "conversion_complete": jobs_with_provider == total_jobs,
                }

        except SQLAlchemyError as e:
            return {
                "valid": False,
                "error": f"Data conversion validation error: {str(e)}",
            }

    def _validate_orm_integration(self) -> Dict[str, Any]:
        """Validate ORM integration with provider fields."""
        try:
            with self.session_factory() as session:
                # Test creating new job with provider fields
                test_job = VideoJobDB(
                    id=f"orm-test-{int(time.time())}",
                    prompt="ORM integration test",
                    status="pending",
                    created_at=datetime.utcnow(),
                    api_provider="google_veo3",
                    input_image_path="/test/validation.jpg",
                    audio_generated=True,
                )

                session.add(test_job)
                session.commit()

                # Test conversion to Pydantic
                pydantic_job = test_job.to_pydantic()

                # Test conversion from Pydantic
                new_pydantic = VideoJob(
                    id=f"pydantic-test-{int(time.time())}",
                    prompt="Pydantic conversion test",
                    status="running",
                    created_at=datetime.utcnow(),
                    api_provider="azure_sora",
                    input_image_path=None,
                    audio_generated=False,
                )

                new_db_job = VideoJobDB.from_pydantic(new_pydantic)
                session.add(new_db_job)
                session.commit()

                # Clean up test data
                session.delete(test_job)
                session.delete(new_db_job)
                session.commit()

                return {
                    "valid": True,
                    "to_pydantic_works": isinstance(pydantic_job, VideoJob),
                    "from_pydantic_works": isinstance(new_db_job, VideoJobDB),
                    "provider_fields_converted": (
                        pydantic_job.api_provider == "google_veo3"
                        and pydantic_job.input_image_path == "/test/validation.jpg"
                        and pydantic_job.audio_generated is True
                    ),
                }

        except Exception as e:
            return {
                "valid": False,
                "error": f"ORM integration validation error: {str(e)}",
            }

    def _validate_provider_queries(self) -> Dict[str, Any]:
        """Validate provider-aware query patterns work correctly."""
        try:
            with self.session_factory() as session:
                # Test basic provider filtering
                azure_jobs = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="azure_sora")
                    .limit(5)
                    .all()
                )
                veo3_jobs = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="google_veo3")
                    .limit(5)
                    .all()
                )

                # Test combined provider and status queries
                azure_pending = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="azure_sora", status="pending")
                    .count()
                )

                # Test provider with session queries
                if azure_jobs:
                    session_azure = (
                        session.query(VideoJobDB)
                        .filter_by(
                            api_provider="azure_sora",
                            session_id=azure_jobs[0].session_id,
                        )
                        .count()
                    )
                else:
                    session_azure = 0

                return {
                    "valid": True,
                    "azure_jobs_found": len(azure_jobs),
                    "veo3_jobs_found": len(veo3_jobs),
                    "azure_pending_count": azure_pending,
                    "session_provider_queries_work": session_azure >= 0,
                }

        except SQLAlchemyError as e:
            return {
                "valid": False,
                "error": f"Provider query validation error: {str(e)}",
            }

    def generate_migration_report(
        self, pre_migration_validation: Dict, post_migration_validation: Dict
    ) -> str:
        """
        Generate comprehensive migration report.

        Args:
            pre_migration_validation: Results from validate_migration_safety()
            post_migration_validation: Results from validate_post_migration()

        Returns:
            str: Formatted migration report
        """
        report = []
        report.append("=" * 60)
        report.append("DATABASE MIGRATION VALIDATION REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.utcnow().isoformat()}")
        report.append("")

        # Pre-migration validation
        report.append("PRE-MIGRATION VALIDATION:")
        report.append("-" * 30)
        report.append(
            f"Safe to migrate: {pre_migration_validation.get('safe_to_migrate', False)}"
        )

        if pre_migration_validation.get("errors"):
            report.append("ERRORS:")
            for error in pre_migration_validation["errors"]:
                report.append(f"  - {error}")

        if pre_migration_validation.get("warnings"):
            report.append("WARNINGS:")
            for warning in pre_migration_validation["warnings"]:
                report.append(f"  - {warning}")

        # Metrics
        metrics = pre_migration_validation.get("metrics", {})
        if metrics:
            report.append("\nPERFORMANCE METRICS:")
            if "performance" in metrics:
                perf = metrics["performance"]
                report.append(
                    f"  Estimated migration time: {perf.get('estimated_migration_time_seconds', 'N/A')}s"
                )
                report.append(f"  Total records: {perf.get('total_records', 'N/A')}")

        # Post-migration validation
        if post_migration_validation:
            report.append("\nPOST-MIGRATION VALIDATION:")
            report.append("-" * 30)
            report.append(
                f"Migration successful: {post_migration_validation.get('migration_successful', False)}"
            )

            validation_metrics = post_migration_validation.get("validation_metrics", {})
            if "provider_queries" in validation_metrics:
                queries = validation_metrics["provider_queries"]
                report.append(
                    f"Provider queries working: {queries.get('valid', False)}"
                )

        report.append("\n" + "=" * 60)
        return "\n".join(report)

    def _validate_provider_field_constraints(self) -> Dict[str, Any]:
        """
        Validate provider field constraints and business rules.

        Returns:
            Dict[str, Any]: Provider field constraint validation results
        """
        try:
            inspector = inspect(self.engine)
            columns = inspector.get_columns("video_jobs")
            column_info = {col["name"]: col for col in columns}

            validation_results = {
                "valid": True,
                "constraint_checks": {},
                "warnings": [],
                "errors": [],
            }

            # Check api_provider field constraints
            if "api_provider" in column_info:
                api_provider_col = column_info["api_provider"]
                validation_results["constraint_checks"]["api_provider"] = {
                    "exists": True,
                    "not_null": api_provider_col.get("nullable") is False,
                    "has_default": api_provider_col.get("default") is not None,
                    "correct_type": "VARCHAR" in str(api_provider_col["type"]).upper(),
                    "length_appropriate": "20" in str(api_provider_col["type"]),
                }

                # Validate allowed values with test query
                with self.session_factory() as session:
                    try:
                        # Check for invalid provider values
                        valid_providers = ["azure_sora", "google_veo3"]
                        invalid_providers = (
                            session.query(VideoJobDB)
                            .filter(~VideoJobDB.api_provider.in_(valid_providers))
                            .count()
                        )

                        validation_results["constraint_checks"]["api_provider"][
                            "no_invalid_values"
                        ] = invalid_providers == 0

                        if invalid_providers > 0:
                            validation_results["errors"].append(
                                f"Found {invalid_providers} jobs with invalid api_provider values"
                            )
                            validation_results["valid"] = False

                    except Exception as e:
                        validation_results["warnings"].append(
                            f"Could not validate api_provider values: {str(e)}"
                        )
            else:
                validation_results["constraint_checks"]["api_provider"] = {
                    "exists": False,
                    "migration_required": True,
                }

            # Check input_image_path field constraints
            if "input_image_path" in column_info:
                input_image_col = column_info["input_image_path"]
                validation_results["constraint_checks"]["input_image_path"] = {
                    "exists": True,
                    "nullable": input_image_col.get("nullable") is True,
                    "correct_type": "TEXT" in str(input_image_col["type"]).upper(),
                }

                # Check for reasonable path lengths and formats
                with self.session_factory() as session:
                    try:
                        # Check for excessively long paths
                        long_paths = (
                            session.query(VideoJobDB)
                            .filter(func.length(VideoJobDB.input_image_path) > 1000)
                            .count()
                        )

                        if long_paths > 0:
                            validation_results["warnings"].append(
                                f"Found {long_paths} jobs with very long input_image_path values (>1000 chars)"
                            )

                    except Exception as e:
                        validation_results["warnings"].append(
                            f"Could not validate input_image_path constraints: {str(e)}"
                        )
            else:
                validation_results["constraint_checks"]["input_image_path"] = {
                    "exists": False,
                    "migration_required": True,
                }

            # Check audio_generated field constraints
            if "audio_generated" in column_info:
                audio_col = column_info["audio_generated"]
                validation_results["constraint_checks"]["audio_generated"] = {
                    "exists": True,
                    "not_null": audio_col.get("nullable") is False,
                    "correct_type": "BOOLEAN" in str(audio_col["type"]).upper(),
                    "has_default": audio_col.get("default") is not None,
                }

                # Validate boolean values
                with self.session_factory() as session:
                    try:
                        # Check for non-boolean values (should be impossible but let's verify)
                        total_audio_jobs = (
                            session.query(VideoJobDB)
                            .filter(VideoJobDB.audio_generated.isnot(None))
                            .count()
                        )

                        true_count = (
                            session.query(VideoJobDB)
                            .filter(VideoJobDB.audio_generated == True)
                            .count()
                        )

                        false_count = (
                            session.query(VideoJobDB)
                            .filter(VideoJobDB.audio_generated == False)
                            .count()
                        )

                        if (true_count + false_count) != total_audio_jobs:
                            validation_results["errors"].append(
                                "Found non-boolean values in audio_generated field"
                            )
                            validation_results["valid"] = False

                    except Exception as e:
                        validation_results["warnings"].append(
                            f"Could not validate audio_generated values: {str(e)}"
                        )
            else:
                validation_results["constraint_checks"]["audio_generated"] = {
                    "exists": False,
                    "migration_required": True,
                }

            return validation_results

        except SQLAlchemyError as e:
            return {
                "valid": False,
                "error": f"Provider constraint validation error: {str(e)}",
            }

    def _validate_provider_data_consistency(self) -> Dict[str, Any]:
        """
        Validate data consistency for provider-specific fields.

        Returns:
            Dict[str, Any]: Provider data consistency validation results
        """
        try:
            with self.session_factory() as session:
                validation_results = {
                    "valid": True,
                    "consistency_checks": {},
                    "warnings": [],
                    "errors": [],
                }

                # Check 1: Azure Sora jobs should not have input_image_path
                azure_with_images = (
                    session.query(VideoJobDB)
                    .filter(
                        VideoJobDB.api_provider == "azure_sora",
                        VideoJobDB.input_image_path.isnot(None),
                    )
                    .count()
                )

                validation_results["consistency_checks"]["azure_image_consistency"] = {
                    "azure_jobs_with_images": azure_with_images,
                    "consistent": azure_with_images == 0,
                }

                if azure_with_images > 0:
                    validation_results["warnings"].append(
                        f"Found {azure_with_images} Azure Sora jobs with input_image_path "
                        "(unusual but not necessarily invalid)"
                    )

                # Check 2: Google Veo3 jobs should have input_image_path (when they exist)
                veo3_jobs = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.api_provider == "google_veo3")
                    .count()
                )

                veo3_without_images = (
                    session.query(VideoJobDB)
                    .filter(
                        VideoJobDB.api_provider == "google_veo3",
                        VideoJobDB.input_image_path.is_(None),
                    )
                    .count()
                )

                validation_results["consistency_checks"]["veo3_image_consistency"] = {
                    "total_veo3_jobs": veo3_jobs,
                    "veo3_jobs_without_images": veo3_without_images,
                    "consistent": veo3_jobs == 0 or veo3_without_images == 0,
                }

                if veo3_jobs > 0 and veo3_without_images > 0:
                    validation_results["warnings"].append(
                        f"Found {veo3_without_images} Google Veo3 jobs without input_image_path "
                        "(may indicate incomplete data)"
                    )

                # Check 3: Provider distribution balance
                total_jobs = session.query(VideoJobDB).count()
                azure_jobs = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.api_provider == "azure_sora")
                    .count()
                )
                veo3_jobs = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.api_provider == "google_veo3")
                    .count()
                )

                # Check for unknown providers
                known_provider_jobs = azure_jobs + veo3_jobs
                unknown_provider_jobs = total_jobs - known_provider_jobs

                validation_results["consistency_checks"]["provider_distribution"] = {
                    "total_jobs": total_jobs,
                    "azure_sora_jobs": azure_jobs,
                    "google_veo3_jobs": veo3_jobs,
                    "unknown_provider_jobs": unknown_provider_jobs,
                    "all_jobs_have_known_providers": unknown_provider_jobs == 0,
                }

                if unknown_provider_jobs > 0:
                    validation_results["errors"].append(
                        f"Found {unknown_provider_jobs} jobs with unknown api_provider values"
                    )
                    validation_results["valid"] = False

                # Check 4: Audio generation flag consistency
                audio_enabled_jobs = (
                    session.query(VideoJobDB)
                    .filter(VideoJobDB.audio_generated == True)
                    .count()
                )

                # Currently, audio generation is planned for future, so should be mostly False
                if audio_enabled_jobs > 0:
                    validation_results["consistency_checks"]["audio_generation"] = {
                        "jobs_with_audio": audio_enabled_jobs,
                        "note": "Audio generation found - verify this is intentional",
                    }
                    validation_results["warnings"].append(
                        f"Found {audio_enabled_jobs} jobs with audio generation enabled"
                    )

                # Check 5: Referential integrity for provider-specific operations
                # Ensure jobs with provider-specific fields have consistent status progression
                succeeded_jobs_by_provider = {}
                failed_jobs_by_provider = {}

                for provider in ["azure_sora", "google_veo3"]:
                    succeeded_jobs_by_provider[provider] = (
                        session.query(VideoJobDB)
                        .filter(
                            VideoJobDB.api_provider == provider,
                            VideoJobDB.status == "succeeded",
                        )
                        .count()
                    )

                    failed_jobs_by_provider[provider] = (
                        session.query(VideoJobDB)
                        .filter(
                            VideoJobDB.api_provider == provider,
                            VideoJobDB.status == "failed",
                        )
                        .count()
                    )

                validation_results["consistency_checks"]["status_distribution"] = {
                    "succeeded_by_provider": succeeded_jobs_by_provider,
                    "failed_by_provider": failed_jobs_by_provider,
                }

                # Calculate success rates for warnings
                for provider in ["azure_sora", "google_veo3"]:
                    total_completed = (
                        succeeded_jobs_by_provider[provider]
                        + failed_jobs_by_provider[provider]
                    )
                    if total_completed > 0:
                        success_rate = (
                            succeeded_jobs_by_provider[provider] / total_completed
                        ) * 100
                        if success_rate < 50:  # Less than 50% success rate
                            validation_results["warnings"].append(
                                f"Low success rate for {provider}: {success_rate:.1f}%"
                            )

                return validation_results

        except SQLAlchemyError as e:
            return {
                "valid": False,
                "error": f"Provider data consistency validation error: {str(e)}",
            }
