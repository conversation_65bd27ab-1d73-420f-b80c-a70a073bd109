"""
F1 Health Monitoring System for Database Schema Extensions.

Provides comprehensive operational health monitoring for F1 database schema
extensions with provider-specific metrics, performance benchmarks, and
production readiness validation.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List

from sqlalchemy import text

from src.database.connection import get_db_session
from src.database.models import VideoJobDB
from src.database.queries.performance_helpers import PerformanceQueryHelper
from src.database.queries.provider_queries import ProviderQueryHelper

logger = logging.getLogger(__name__)


class F1HealthMonitor:
    """Operational health monitoring for F1 database schema extensions."""

    def __init__(self):
        """Initialize F1 health monitor with query helpers."""
        self.provider_helper = ProviderQueryHelper()
        self.performance_helper = PerformanceQueryHelper()

    def get_database_health_status(self) -> Dict[str, Any]:
        """
        Get comprehensive database health status.

        Returns:
            Dict[str, Any]: Complete database health assessment
        """
        try:
            with get_db_session() as session:
                # Basic connectivity test
                session.execute(text("SELECT 1")).scalar()

                # Provider field accessibility test
                provider_test = session.query(VideoJobDB.api_provider).first()

                # Connection pool status
                pool_info = self._get_connection_pool_info(session)

                # Test F1 schema extensions
                schema_validation = self._validate_f1_schema(session)

                return {
                    "status": "healthy",
                    "timestamp": datetime.utcnow().isoformat(),
                    "database_connectivity": True,
                    "provider_fields_accessible": provider_test is not None,
                    "connection_pool": pool_info,
                    "f1_schema_validation": schema_validation,
                    "health_grade": "A"
                    if schema_validation.get("valid", False)
                    else "B",
                }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "database_connectivity": False,
                "health_grade": "F",
            }

    def get_provider_performance_metrics(self) -> Dict[str, Any]:
        """
        Get provider-specific performance metrics.

        Returns:
            Dict[str, Any]: Comprehensive provider performance analysis
        """
        try:
            # Query performance benchmarks
            benchmarks = self.performance_helper.benchmark_provider_queries()

            # Provider statistics with error handling
            try:
                stats = self.provider_helper.get_provider_statistics()
            except Exception as e:
                logger.warning(f"Provider statistics failed: {e}")
                stats = {"error": str(e)}

            # Calculate performance grades
            provider_filter_ms = benchmarks.get("provider_filter_ms", 1000)
            provider_stats_ms = benchmarks.get("provider_stats_ms", 1000)

            azure_grade = (
                "A"
                if provider_filter_ms < 50
                else "B"
                if provider_filter_ms < 100
                else "C"
            )
            stats_grade = (
                "A"
                if provider_stats_ms < 75
                else "B"
                if provider_stats_ms < 150
                else "C"
            )
            overall_grade = (
                "A"
                if azure_grade == "A" and stats_grade == "A"
                else "B"
                if azure_grade in ["A", "B"] and stats_grade in ["A", "B"]
                else "C"
            )

            # Performance target validation
            targets_met = (
                provider_filter_ms < 100
                and provider_stats_ms < 150
                and benchmarks.get("combined_query_ms", 1000) < 100
            )

            return {
                "performance_benchmarks": benchmarks,
                "provider_statistics": stats,
                "performance_grades": {
                    "provider_queries": azure_grade,
                    "statistics_queries": stats_grade,
                    "overall": overall_grade,
                },
                "targets_met": targets_met,
                "performance_summary": {
                    "provider_filter_ms": provider_filter_ms,
                    "provider_stats_ms": provider_stats_ms,
                    "combined_query_ms": benchmarks.get("combined_query_ms", 0),
                    "meets_f1_requirements": targets_met,
                },
                "timestamp": datetime.utcnow().isoformat(),
            }
        except Exception as e:
            logger.error(f"Provider performance check failed: {e}")
            return {
                "error": str(e),
                "performance_grades": {"overall": "F"},
                "targets_met": False,
                "timestamp": datetime.utcnow().isoformat(),
            }

    def get_provider_load_balance_status(self) -> Dict[str, Any]:
        """
        Monitor provider load balancing status.

        Returns:
            Dict[str, Any]: Provider load balance analysis
        """
        try:
            with get_db_session() as session:
                # Get current provider loads
                azure_pending = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="azure_sora", status="pending")
                    .count()
                )

                veo3_pending = (
                    session.query(VideoJobDB)
                    .filter_by(api_provider="google_veo3", status="pending")
                    .count()
                )

                total_pending = azure_pending + veo3_pending

                # Calculate load balance ratio
                azure_ratio = (
                    (azure_pending / total_pending * 100) if total_pending > 0 else 0
                )
                veo3_ratio = (
                    (veo3_pending / total_pending * 100) if total_pending > 0 else 0
                )

                # Determine balance status
                imbalance_threshold = 30  # 70/30 split is acceptable
                is_balanced = (
                    abs(azure_ratio - veo3_ratio) <= imbalance_threshold
                    or total_pending == 0
                )

                # Get historical load trends (last 24 hours)
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                azure_recent = (
                    session.query(VideoJobDB)
                    .filter(
                        VideoJobDB.api_provider == "azure_sora",
                        VideoJobDB.created_at >= cutoff_time,
                    )
                    .count()
                )

                veo3_recent = (
                    session.query(VideoJobDB)
                    .filter(
                        VideoJobDB.api_provider == "google_veo3",
                        VideoJobDB.created_at >= cutoff_time,
                    )
                    .count()
                )

                return {
                    "load_balance": {
                        "azure_sora": {
                            "pending_jobs": azure_pending,
                            "percentage": round(azure_ratio, 2),
                            "recent_24h": azure_recent,
                        },
                        "google_veo3": {
                            "pending_jobs": veo3_pending,
                            "percentage": round(veo3_ratio, 2),
                            "recent_24h": veo3_recent,
                        },
                    },
                    "is_balanced": is_balanced,
                    "total_pending": total_pending,
                    "balance_status": "balanced" if is_balanced else "imbalanced",
                    "balance_grade": "A" if is_balanced else "C",
                    "trends": {
                        "total_recent_24h": azure_recent + veo3_recent,
                        "azure_trend": "increasing"
                        if azure_recent > azure_pending
                        else "stable",
                        "veo3_trend": "increasing"
                        if veo3_recent > veo3_pending
                        else "stable",
                    },
                    "timestamp": datetime.utcnow().isoformat(),
                }
        except Exception as e:
            logger.error(f"Load balance monitoring failed: {e}")
            return {
                "error": str(e),
                "balance_status": "error",
                "balance_grade": "F",
                "timestamp": datetime.utcnow().isoformat(),
            }

    def get_migration_health_status(self) -> Dict[str, Any]:
        """
        Check F1 migration health and rollback readiness.

        Returns:
            Dict[str, Any]: Migration health and rollback status
        """
        try:
            with get_db_session() as session:
                # Check provider field schema integrity
                schema_check = self._validate_provider_schema(session)

                # Check index existence and utilization
                index_check = self._validate_provider_indexes(session)

                # Check backward compatibility
                compatibility_check = self._validate_backward_compatibility(session)

                # Performance regression check
                performance_check = self._check_performance_regression()

                overall_health = (
                    schema_check["valid"]
                    and index_check["valid"]
                    and compatibility_check["valid"]
                    and performance_check.get("acceptable", True)
                )

                # Calculate health grade
                valid_checks = sum(
                    [
                        schema_check["valid"],
                        index_check["valid"],
                        compatibility_check["valid"],
                        performance_check.get("acceptable", True),
                    ]
                )

                if valid_checks == 4:
                    health_grade = "A"
                elif valid_checks >= 3:
                    health_grade = "B"
                elif valid_checks >= 2:
                    health_grade = "C"
                else:
                    health_grade = "F"

                return {
                    "migration_health": "healthy" if overall_health else "degraded",
                    "health_grade": health_grade,
                    "schema_integrity": schema_check,
                    "index_status": index_check,
                    "backward_compatibility": compatibility_check,
                    "performance_status": performance_check,
                    "rollback_ready": overall_health,
                    "checks_passed": valid_checks,
                    "total_checks": 4,
                    "timestamp": datetime.utcnow().isoformat(),
                }
        except Exception as e:
            logger.error(f"Migration health check failed: {e}")
            return {
                "error": str(e),
                "migration_health": "error",
                "health_grade": "F",
                "rollback_ready": False,
                "timestamp": datetime.utcnow().isoformat(),
            }

    def get_system_monitoring_dashboard(self) -> Dict[str, Any]:
        """
        Get comprehensive system monitoring dashboard for F1.

        Returns:
            Dict[str, Any]: Complete system monitoring dashboard
        """
        try:
            # Collect all monitoring data
            database_health = self.get_database_health_status()
            performance_metrics = self.get_provider_performance_metrics()
            load_balance = self.get_provider_load_balance_status()
            migration_health = self.get_migration_health_status()

            # Calculate overall system grade
            grades = [
                database_health.get("health_grade", "F"),
                performance_metrics.get("performance_grades", {}).get("overall", "F"),
                load_balance.get("balance_grade", "F"),
                migration_health.get("health_grade", "F"),
            ]

            grade_scores = {"A": 4, "B": 3, "C": 2, "D": 1, "F": 0}
            avg_score = sum(grade_scores.get(grade, 0) for grade in grades) / len(
                grades
            )

            if avg_score >= 3.5:
                overall_grade = "A"
            elif avg_score >= 2.5:
                overall_grade = "B"
            elif avg_score >= 1.5:
                overall_grade = "C"
            else:
                overall_grade = "F"

            # System status determination
            status_healthy = all(
                [
                    database_health.get("status") == "healthy",
                    performance_metrics.get("targets_met", False),
                    load_balance.get("balance_status") == "balanced",
                    migration_health.get("migration_health") == "healthy",
                ]
            )

            return {
                "system_status": "healthy" if status_healthy else "degraded",
                "overall_grade": overall_grade,
                "component_grades": {
                    "database": database_health.get("health_grade", "F"),
                    "performance": performance_metrics.get(
                        "performance_grades", {}
                    ).get("overall", "F"),
                    "load_balance": load_balance.get("balance_grade", "F"),
                    "migration": migration_health.get("health_grade", "F"),
                },
                "monitoring_data": {
                    "database_health": database_health,
                    "performance_metrics": performance_metrics,
                    "load_balance_status": load_balance,
                    "migration_health": migration_health,
                },
                "alerts": self._generate_alerts(
                    database_health, performance_metrics, load_balance, migration_health
                ),
                "recommendations": self._generate_recommendations(
                    database_health, performance_metrics, load_balance, migration_health
                ),
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"System monitoring dashboard failed: {e}")
            return {
                "system_status": "error",
                "overall_grade": "F",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    def _get_connection_pool_info(self, session) -> Dict[str, Any]:
        """Get database connection pool information."""
        try:
            engine = session.bind
            if hasattr(engine.pool, "size"):
                return {
                    "pool_size": engine.pool.size(),
                    "checked_out": engine.pool.checkedout(),
                    "overflow": engine.pool.overflow(),
                    "checked_in": engine.pool.checkedin(),
                    "status": "healthy",
                }
            else:
                return {"status": "unknown", "message": "Pool info not available"}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _validate_f1_schema(self, session) -> Dict[str, Any]:
        """Validate F1 schema extensions are properly installed."""
        try:
            # Test F1 columns exist and are accessible
            result = session.execute(
                text("""
                SELECT api_provider, input_image_path, audio_generated 
                FROM video_jobs LIMIT 1
            """)
            ).first()

            # Test default values work
            test_count = (
                session.query(VideoJobDB).filter_by(api_provider="azure_sora").count()
            )

            return {
                "valid": True,
                "f1_columns_accessible": True,
                "default_provider_working": True,
                "azure_sora_jobs": test_count,
                "test_query_successful": result is not None,
            }
        except Exception as e:
            return {"valid": False, "error": str(e), "f1_columns_accessible": False}

    def _validate_provider_schema(self, session) -> Dict[str, Any]:
        """Validate provider field schema integrity."""
        try:
            # Test provider field accessibility
            result = session.execute(
                text("""
                SELECT api_provider, input_image_path, audio_generated 
                FROM video_jobs LIMIT 1
            """)
            ).first()

            # Test provider constraints
            provider_values = session.execute(
                text("""
                SELECT DISTINCT api_provider FROM video_jobs
            """)
            ).fetchall()

            valid_providers = [
                row[0]
                for row in provider_values
                if row[0] in ["azure_sora", "google_veo3"]
            ]

            return {
                "valid": True,
                "provider_fields_accessible": True,
                "test_query_successful": result is not None,
                "valid_providers_found": valid_providers,
                "schema_integrity": "intact",
            }
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "provider_fields_accessible": False,
                "schema_integrity": "corrupted",
            }

    def _validate_provider_indexes(self, session) -> Dict[str, Any]:
        """Validate provider index existence and performance."""
        try:
            if "postgresql" in str(session.bind.url):
                # PostgreSQL index validation
                indexes = session.execute(
                    text("""
                    SELECT indexname, indexdef FROM pg_indexes 
                    WHERE tablename = 'video_jobs' 
                    AND indexname IN ('idx_provider_status', 'idx_provider_created')
                """)
                ).fetchall()

                index_count = len(indexes)
                expected_indexes = 2

                return {
                    "valid": index_count == expected_indexes,
                    "indexes_found": index_count,
                    "expected_indexes": expected_indexes,
                    "index_details": [
                        {"name": idx[0], "definition": idx[1]} for idx in indexes
                    ],
                    "database_type": "postgresql",
                }
            else:
                # SQLite - check index usage with EXPLAIN
                test_query = "SELECT * FROM video_jobs WHERE api_provider = 'azure_sora' AND status = 'pending'"
                index_analysis = self.performance_helper.validate_index_usage(
                    test_query, session
                )

                return {
                    "valid": index_analysis.get("uses_index", False),
                    "database_type": "sqlite",
                    "index_usage_analysis": index_analysis,
                    "performance_grade": index_analysis.get("performance_grade", "F"),
                }
        except Exception as e:
            return {"valid": False, "error": str(e), "database_type": "unknown"}

    def _validate_backward_compatibility(self, session) -> Dict[str, Any]:
        """Validate backward compatibility with existing Azure Sora workflows."""
        try:
            # Test creating job without explicit provider (should default to azure_sora)
            import uuid

            test_job = VideoJobDB(
                id=str(uuid.uuid4()),
                prompt="Backward compatibility test",
                status="pending",
                session_id="compatibility-test",
            )
            session.add(test_job)
            session.flush()

            # Verify default provider assignment
            backward_compatible = test_job.api_provider == "azure_sora"
            audio_default = test_job.audio_generated == False

            # Test existing queries still work
            existing_jobs = (
                session.query(VideoJobDB).filter_by(status="pending").count()
            )

            # Cleanup
            session.delete(test_job)
            session.commit()

            return {
                "valid": backward_compatible and audio_default,
                "default_provider_assignment": backward_compatible,
                "azure_sora_default": test_job.api_provider == "azure_sora",
                "audio_flag_default": audio_default,
                "existing_queries_work": existing_jobs >= 0,
                "compatibility_grade": "A"
                if backward_compatible and audio_default
                else "F",
            }
        except Exception as e:
            return {"valid": False, "error": str(e), "compatibility_grade": "F"}

    def _check_performance_regression(self) -> Dict[str, Any]:
        """Check for performance regression after F1 migration."""
        try:
            # Run performance benchmarks
            benchmarks = self.performance_helper.benchmark_provider_queries()

            # Define acceptable performance thresholds
            thresholds = {
                "provider_filter_ms": 100,
                "provider_stats_ms": 150,
                "combined_query_ms": 100,
            }

            acceptable = all(
                benchmarks.get(key, 1000) <= threshold
                for key, threshold in thresholds.items()
            )

            return {
                "acceptable": acceptable,
                "benchmarks": benchmarks,
                "thresholds": thresholds,
                "performance_grade": "A" if acceptable else "C",
            }
        except Exception as e:
            return {"acceptable": False, "error": str(e), "performance_grade": "F"}

    def _generate_alerts(
        self, database_health, performance_metrics, load_balance, migration_health
    ) -> List[Dict[str, str]]:
        """Generate system alerts based on monitoring data."""
        alerts = []

        # Database health alerts
        if database_health.get("status") != "healthy":
            alerts.append(
                {
                    "level": "critical",
                    "component": "database",
                    "message": f"Database health check failed: {database_health.get('error', 'Unknown error')}",
                }
            )

        # Performance alerts
        if not performance_metrics.get("targets_met", True):
            alerts.append(
                {
                    "level": "warning",
                    "component": "performance",
                    "message": "Performance targets not met - queries may be slower than expected",
                }
            )

        # Load balance alerts
        if load_balance.get("balance_status") == "imbalanced":
            alerts.append(
                {
                    "level": "warning",
                    "component": "load_balance",
                    "message": "Provider load is imbalanced - consider workload redistribution",
                }
            )

        # Migration health alerts
        if migration_health.get("migration_health") != "healthy":
            alerts.append(
                {
                    "level": "warning",
                    "component": "migration",
                    "message": "Migration health check detected issues - review system integrity",
                }
            )

        return alerts

    def _generate_recommendations(
        self, database_health, performance_metrics, load_balance, migration_health
    ) -> List[Dict[str, str]]:
        """Generate system recommendations based on monitoring data."""
        recommendations = []

        # Performance recommendations
        perf_grade = performance_metrics.get("performance_grades", {}).get(
            "overall", "F"
        )
        if perf_grade in ["C", "D", "F"]:
            recommendations.append(
                {
                    "component": "performance",
                    "action": "Consider running database maintenance and index optimization",
                    "priority": "medium",
                }
            )

        # Load balance recommendations
        if load_balance.get("balance_status") == "imbalanced":
            recommendations.append(
                {
                    "component": "load_balance",
                    "action": "Review provider routing logic and consider load balancing adjustments",
                    "priority": "low",
                }
            )

        # Migration recommendations
        if not migration_health.get("rollback_ready", True):
            recommendations.append(
                {
                    "component": "migration",
                    "action": "Ensure backup procedures are in place before proceeding with production changes",
                    "priority": "high",
                }
            )

        return recommendations
