"""Database connection management for Sora POC."""

from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from .models import Base


class DatabaseManager:
    """
    Database connection manager with pooling support.

    Handles database URL configuration, connection pooling,
    and session management for production scalability.
    """

    def __init__(self) -> None:
        """
        Initialize database manager with environment-based config.

        Raises:
            ValueError: If DATABASE_URL environment variable is missing
        """
        from src.config.factory import ConfigurationFactory

        config = ConfigurationFactory.get_base_config()
        self.database_url = config.DATABASE_URL

        if not self.database_url:
            # Default to SQLite for development if no URL provided
            self.database_url = "sqlite:///sora_poc.db"

        # Connection pooling configuration (conditional based on database type)
        engine_args = {
            "echo": config.SQL_DEBUG,
        }

        # SQLite doesn't support pooling options
        if not self.database_url.startswith("sqlite"):
            engine_args.update(
                {
                    "pool_size": 10,
                    "max_overflow": 20,
                    "pool_pre_ping": True,  # Validate connections before use
                }
            )

        self.engine = create_engine(self.database_url, **engine_args)

        self.session_factory = sessionmaker(bind=self.engine)

    def init_db(self) -> None:
        """
        Initialize database tables.

        Creates all tables defined in models if they don't exist.
        """
        Base.metadata.create_all(bind=self.engine)

    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Get database session with automatic cleanup.

        Yields:
            Session: SQLAlchemy database session

        Raises:
            Exception: Database operation errors
        """
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()


# Global database manager instance
_db_manager = None


def get_db_manager() -> DatabaseManager:
    """
    Get global database manager instance.

    Returns:
        DatabaseManager: Singleton database manager
    """
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


def get_db_session() -> Generator[Session, None, None]:
    """
    Get database session context manager.

    Yields:
        Session: SQLAlchemy database session
    """
    manager = get_db_manager()
    return manager.get_session()


def get_db() -> Session:
    """
    Get database session for direct usage.

    WARNING: This returns a session without automatic cleanup.
    Caller is responsible for committing and closing the session.

    Returns:
        Session: SQLAlchemy database session
    """
    manager = get_db_manager()
    return manager.session_factory()


def init_db() -> None:
    """
    Initialize database tables.

    Creates all tables if they don't exist.
    """
    manager = get_db_manager()
    manager.init_db()
