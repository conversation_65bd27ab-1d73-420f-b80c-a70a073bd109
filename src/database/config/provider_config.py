"""Environment-specific database configuration for dual-provider support.

This module provides database configuration management specifically tailored for
dual-provider video generation systems. It handles environment detection,
performance tuning, and provider-specific optimization while maintaining
compatibility with existing database connection patterns.
"""

import logging
import os
from dataclasses import dataclass, field
from typing import Any, Dict, Literal, Optional
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


@dataclass
class ProviderDatabaseConfig:
    """Database configuration for dual-provider support.

    This configuration class provides environment-aware database settings
    optimized for dual-provider video generation systems. It handles both
    local development and production deployment scenarios with automatic
    environment detection and performance tuning.
    """

    # Provider-specific connection settings
    enable_provider_monitoring: bool = True
    provider_query_timeout: int = 5000  # milliseconds
    provider_index_maintenance: bool = True

    # Performance tuning
    provider_connection_pool_size: int = 10
    provider_query_cache_size: int = 1000
    provider_statement_timeout: int = 30000  # milliseconds

    # Migration settings
    enable_zero_downtime_migrations: bool = True
    migration_safety_checks: bool = True
    migration_backup_enabled: bool = True

    # Provider-specific optimization
    optimize_for_provider_queries: bool = True
    provider_partition_strategy: Literal["none", "by_provider", "by_date"] = "none"
    enable_provider_statistics_cache: bool = True

    # Environment-specific settings
    environment: Optional[str] = field(default=None)
    deployment_type: Optional[str] = field(default=None)

    @classmethod
    def from_environment(
        cls, environment: Optional[str] = None
    ) -> "ProviderDatabaseConfig":
        """Create configuration from environment variables.

        This method automatically detects the deployment environment and applies
        appropriate database configuration settings for optimal performance.

        Args:
            environment: Override environment detection (for testing)

        Returns:
            ProviderDatabaseConfig: Environment-optimized configuration
        """
        detected_env = environment or cls._detect_environment()
        deployment_type = cls._detect_deployment_type()

        logger.info(
            f"Creating provider database config for {detected_env} environment, deployment: {deployment_type}"
        )

        config = cls(
            # Basic configuration from environment variables
            enable_provider_monitoring=os.getenv(
                "ENABLE_PROVIDER_MONITORING", "true"
            ).lower()
            == "true",
            provider_query_timeout=int(os.getenv("PROVIDER_QUERY_TIMEOUT", "5000")),
            provider_index_maintenance=os.getenv(
                "PROVIDER_INDEX_MAINTENANCE", "true"
            ).lower()
            == "true",
            # Performance configuration
            provider_connection_pool_size=int(os.getenv("PROVIDER_POOL_SIZE", "10")),
            provider_query_cache_size=int(os.getenv("PROVIDER_CACHE_SIZE", "1000")),
            provider_statement_timeout=int(
                os.getenv("PROVIDER_STATEMENT_TIMEOUT", "30000")
            ),
            # Migration configuration
            enable_zero_downtime_migrations=os.getenv(
                "ZERO_DOWNTIME_MIGRATIONS", "true"
            ).lower()
            == "true",
            migration_safety_checks=os.getenv("MIGRATION_SAFETY_CHECKS", "true").lower()
            == "true",
            migration_backup_enabled=os.getenv(
                "MIGRATION_BACKUP_ENABLED", "true"
            ).lower()
            == "true",
            # Provider optimization
            optimize_for_provider_queries=os.getenv(
                "OPTIMIZE_PROVIDER_QUERIES", "true"
            ).lower()
            == "true",
            provider_partition_strategy=os.getenv(
                "PROVIDER_PARTITION_STRATEGY", "none"
            ),
            enable_provider_statistics_cache=os.getenv(
                "PROVIDER_STATS_CACHE", "true"
            ).lower()
            == "true",
            # Environment tracking
            environment=detected_env,
            deployment_type=deployment_type,
        )

        # Apply environment-specific overrides
        config._apply_environment_overrides(detected_env, deployment_type)

        logger.info(
            f"Provider database config created: pool_size={config.provider_connection_pool_size}, timeout={config.provider_query_timeout}ms"
        )
        return config

    def _apply_environment_overrides(
        self, environment: str, deployment_type: str
    ) -> None:
        """Apply environment-specific configuration overrides.

        Args:
            environment: Environment name (development, testing, production)
            deployment_type: Deployment type (local, docker, production)
        """
        if deployment_type == "docker":
            # Docker-specific optimizations
            self.provider_connection_pool_size = max(
                self.provider_connection_pool_size, 15
            )
            self.provider_query_timeout = max(
                self.provider_query_timeout, 10000
            )  # Longer timeout for Docker
            self.provider_statement_timeout = max(
                self.provider_statement_timeout, 60000
            )
            logger.info("Applied Docker deployment optimizations")

        elif deployment_type == "production":
            # Production-specific optimizations
            self.provider_connection_pool_size = max(
                self.provider_connection_pool_size, 20
            )
            self.provider_query_cache_size = max(self.provider_query_cache_size, 2000)
            self.enable_provider_statistics_cache = True
            self.migration_backup_enabled = True
            logger.info("Applied production deployment optimizations")

        elif deployment_type == "local":
            # Local development optimizations
            self.provider_connection_pool_size = min(
                self.provider_connection_pool_size, 5
            )
            self.provider_query_timeout = min(self.provider_query_timeout, 3000)
            self.enable_provider_statistics_cache = (
                False  # Disable caching for development
            )
            logger.info("Applied local development optimizations")

    @staticmethod
    def _detect_environment() -> str:
        """Detect current environment from environment variables.

        Returns:
            Environment name (development, testing, production)
        """
        flask_env = os.getenv("FLASK_ENV", "").lower()
        if flask_env in ["development", "testing", "production"]:
            return flask_env

        # Fallback detection based on other environment variables
        if os.getenv("DATABASE_URL", "").startswith("sqlite"):
            return "development"
        elif "test" in os.getenv("DATABASE_URL", "").lower():
            return "testing"
        else:
            return "production"

    @staticmethod
    def _detect_deployment_type() -> str:
        """Detect deployment type from environment.

        Returns:
            Deployment type (local, docker, production)
        """
        # Check for Docker environment
        if os.path.exists("/.dockerenv") or os.getenv("DOCKER_CONTAINER"):
            return "docker"

        # Check for Kubernetes environment
        if os.getenv("KUBERNETES_SERVICE_HOST"):
            return "production"

        # Check for cloud deployment indicators
        cloud_indicators = ["HEROKU", "AWS_", "GOOGLE_CLOUD", "AZURE_"]
        for indicator in cloud_indicators:
            if any(key.startswith(indicator) for key in os.environ):
                return "production"

        # Default to local development
        return "local"

    def get_database_url_with_provider_support(self, base_url: str) -> str:
        """Enhance database URL with provider-specific settings.

        Args:
            base_url: Base database URL from configuration

        Returns:
            Enhanced database URL with provider-specific parameters
        """
        if not base_url:
            return base_url

        # Parse URL to determine database type
        parsed = urlparse(base_url)

        if parsed.scheme.startswith("postgresql"):
            # PostgreSQL-specific enhancements
            params = {
                "application_name": "sora_poc_dual_provider",
                "connect_timeout": str(self.provider_query_timeout // 1000),
                "statement_timeout": str(self.provider_statement_timeout),
            }

            # Add pooling parameters if not already present
            if "pool_size" not in base_url:
                params["pool_size"] = str(self.provider_connection_pool_size)

            # Build parameter string
            param_string = "&".join(f"{k}={v}" for k, v in params.items())

            # Add parameters to URL
            separator = "&" if "?" in base_url else "?"
            enhanced_url = f"{base_url}{separator}{param_string}"

            logger.info("Enhanced PostgreSQL URL with provider support")
            return enhanced_url

        elif parsed.scheme.startswith("sqlite"):
            # SQLite-specific enhancements
            if "?" not in base_url:
                enhanced_url = (
                    f"{base_url}?timeout={self.provider_query_timeout // 1000}"
                )
            else:
                enhanced_url = (
                    f"{base_url}&timeout={self.provider_query_timeout // 1000}"
                )

            logger.info("Enhanced SQLite URL with provider support")
            return enhanced_url

        # Return unmodified URL for unsupported database types
        logger.warning(
            f"Database type not recognized for provider enhancement: {parsed.scheme}"
        )
        return base_url

    def get_sqlalchemy_engine_args(self) -> Dict[str, Any]:
        """Get SQLAlchemy engine arguments optimized for provider operations.

        Returns:
            Dict containing SQLAlchemy engine configuration
        """
        engine_args = {
            "pool_size": self.provider_connection_pool_size,
            "max_overflow": self.provider_connection_pool_size * 2,
            "pool_pre_ping": True,
            "pool_recycle": 3600,  # Recycle connections after 1 hour
        }

        # Environment-specific optimizations
        if self.deployment_type == "production":
            engine_args.update(
                {
                    "pool_timeout": 30,
                    "pool_reset_on_return": "commit",
                    "echo": False,
                }
            )
        elif self.deployment_type == "development" or self.deployment_type == "local":
            engine_args.update(
                {
                    "echo": os.getenv("SQL_DEBUG", "false").lower() == "true",
                    "pool_timeout": 10,
                }
            )

        logger.info(
            f"Generated SQLAlchemy engine args for {self.deployment_type} deployment"
        )
        return engine_args

    def get_provider_query_optimizations(self) -> Dict[str, Any]:
        """Get provider-specific query optimization settings.

        Returns:
            Dict containing query optimization configuration
        """
        optimizations = {
            "enable_provider_indexes": True,
            "use_provider_partitioning": self.provider_partition_strategy != "none",
            "cache_provider_statistics": self.enable_provider_statistics_cache,
            "optimize_cross_provider_queries": self.optimize_for_provider_queries,
            "provider_query_hints": {
                "use_index_scan": True,
                "prefer_hash_joins": True,
                "enable_parallel_query": self.deployment_type == "production",
            },
        }

        # Add partition-specific settings
        if self.provider_partition_strategy == "by_provider":
            optimizations["partition_config"] = {
                "partition_key": "api_provider",
                "partition_values": ["azure_sora", "google_veo3"],
            }
        elif self.provider_partition_strategy == "by_date":
            optimizations["partition_config"] = {
                "partition_key": "created_at",
                "partition_interval": "monthly",
            }

        return optimizations

    def get_migration_settings(self) -> Dict[str, Any]:
        """Get migration-specific configuration settings.

        Returns:
            Dict containing migration configuration
        """
        return {
            "enable_zero_downtime": self.enable_zero_downtime_migrations,
            "safety_checks_enabled": self.migration_safety_checks,
            "backup_before_migration": self.migration_backup_enabled,
            "migration_timeout": self.provider_statement_timeout
            * 2,  # Double timeout for migrations
            "rollback_strategy": "automatic"
            if self.migration_safety_checks
            else "manual",
            "migration_lock_timeout": 300,  # 5 minutes
            "validate_schema_after_migration": True,
            "migration_logging": {
                "log_level": "INFO",
                "log_queries": self.environment != "production",
                "log_performance": True,
            },
        }

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration settings for correctness.

        Returns:
            Dict containing validation results and any issues found
        """
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "performance_recommendations": [],
        }

        # Validate timeout settings
        if self.provider_query_timeout < 1000:
            validation_results["warnings"].append(
                "Provider query timeout is very low (<1s)"
            )
        elif self.provider_query_timeout > 60000:
            validation_results["warnings"].append(
                "Provider query timeout is very high (>60s)"
            )

        # Validate pool size
        if self.provider_connection_pool_size < 1:
            validation_results["errors"].append(
                "Connection pool size must be at least 1"
            )
            validation_results["valid"] = False
        elif self.provider_connection_pool_size > 50:
            validation_results["warnings"].append(
                "Very large connection pool size may impact performance"
            )

        # Performance recommendations
        if self.deployment_type == "production":
            if not self.enable_provider_statistics_cache:
                validation_results["performance_recommendations"].append(
                    "Consider enabling provider statistics cache for production"
                )
            if self.provider_connection_pool_size < 10:
                validation_results["performance_recommendations"].append(
                    "Consider increasing connection pool size for production workloads"
                )

        # Environment-specific validation
        if self.deployment_type == "local" and self.provider_connection_pool_size > 10:
            validation_results["performance_recommendations"].append(
                "Consider reducing connection pool size for local development"
            )

        logger.info(
            f"Configuration validation completed: valid={validation_results['valid']}"
        )
        return validation_results

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary format.

        Returns:
            Dict representation of configuration
        """
        return {
            "provider_monitoring": self.enable_provider_monitoring,
            "query_timeout_ms": self.provider_query_timeout,
            "index_maintenance": self.provider_index_maintenance,
            "connection_pool_size": self.provider_connection_pool_size,
            "query_cache_size": self.provider_query_cache_size,
            "statement_timeout_ms": self.provider_statement_timeout,
            "zero_downtime_migrations": self.enable_zero_downtime_migrations,
            "migration_safety_checks": self.migration_safety_checks,
            "migration_backup": self.migration_backup_enabled,
            "optimize_provider_queries": self.optimize_for_provider_queries,
            "partition_strategy": self.provider_partition_strategy,
            "statistics_cache": self.enable_provider_statistics_cache,
            "environment": self.environment,
            "deployment_type": self.deployment_type,
        }

    def __str__(self) -> str:
        """String representation for logging and debugging."""
        return (
            f"ProviderDatabaseConfig("
            f"env={self.environment}, "
            f"deployment={self.deployment_type}, "
            f"pool_size={self.provider_connection_pool_size}, "
            f"timeout={self.provider_query_timeout}ms"
            f")"
        )


class ProviderDatabaseConfigFactory:
    """Factory for creating provider database configurations.

    This factory provides convenient methods for creating database configurations
    for specific environments and deployment scenarios while maintaining
    environment variable respect and providing sensible defaults.
    """

    @staticmethod
    def create_for_environment(environment: str) -> ProviderDatabaseConfig:
        """Create configuration for specific environment.

        Args:
            environment: Environment name (development, testing, production)

        Returns:
            ProviderDatabaseConfig: Environment-specific configuration
        """
        # Temporarily override environment detection
        original_env = os.environ.get("FLASK_ENV")
        os.environ["FLASK_ENV"] = environment

        try:
            config = ProviderDatabaseConfig.from_environment(environment)
            logger.info(f"Created database config for {environment} environment")
            return config
        finally:
            # Restore original environment
            if original_env is not None:
                os.environ["FLASK_ENV"] = original_env
            elif "FLASK_ENV" in os.environ:
                del os.environ["FLASK_ENV"]

    @staticmethod
    def create_for_testing() -> ProviderDatabaseConfig:
        """Create configuration optimized for testing.

        Returns:
            ProviderDatabaseConfig: Testing-optimized configuration
        """
        return ProviderDatabaseConfig(
            enable_provider_monitoring=False,  # Disable monitoring for tests
            provider_query_timeout=2000,  # Shorter timeout for tests
            provider_index_maintenance=False,  # Disable maintenance for tests
            provider_connection_pool_size=2,  # Minimal pool for tests
            provider_query_cache_size=100,  # Small cache for tests
            provider_statement_timeout=10000,  # Short statement timeout
            enable_zero_downtime_migrations=False,  # Disable for tests
            migration_safety_checks=True,  # Always validate in tests
            migration_backup_enabled=False,  # No backup needed for tests
            optimize_for_provider_queries=False,  # Disable optimizations
            provider_partition_strategy="none",  # No partitioning in tests
            enable_provider_statistics_cache=False,  # No caching in tests
            environment="testing",
            deployment_type="local",
        )

    @staticmethod
    def create_high_performance() -> ProviderDatabaseConfig:
        """Create configuration optimized for high performance.

        Returns:
            ProviderDatabaseConfig: High-performance configuration
        """
        config = ProviderDatabaseConfig.from_environment()

        # Apply high-performance overrides
        config.provider_connection_pool_size = max(
            config.provider_connection_pool_size, 25
        )
        config.provider_query_cache_size = max(config.provider_query_cache_size, 5000)
        config.enable_provider_statistics_cache = True
        config.optimize_for_provider_queries = True
        config.provider_partition_strategy = "by_provider"  # Enable partitioning

        logger.info("Created high-performance database configuration")
        return config

    @staticmethod
    def get_recommended_config() -> ProviderDatabaseConfig:
        """Get recommended configuration based on current environment.

        Returns:
            ProviderDatabaseConfig: Recommended configuration for current environment
        """
        config = ProviderDatabaseConfig.from_environment()
        validation = config.validate_configuration()

        if not validation["valid"]:
            logger.warning(f"Configuration validation failed: {validation['errors']}")

        if validation["performance_recommendations"]:
            logger.info(
                f"Performance recommendations: {validation['performance_recommendations']}"
            )

        return config
