"""SQLAlchemy ORM models for Sora video generation POC."""

from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Enum, Index, Integer, String, Text
from sqlalchemy.orm import declarative_base

from src.core.models import VideoJob

Base = declarative_base()


class VideoJobDB(Base):
    """
    SQLAlchemy ORM model for video generation jobs.

    Provides database persistence for video generation workflow
    with proper indexing for performance.
    """

    __tablename__ = "video_jobs"

    # Primary key
    id = Column(String(36), primary_key=True)

    # Job details
    prompt = Column(Text, nullable=False)
    status = Column(
        Enum("pending", "running", "succeeded", "failed", name="job_status"),
        nullable=False,
        index=True,  # Index for status queries
    )

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    completed_at = Column(DateTime, nullable=True)

    # Azure API details
    generation_id = Column(String(255), nullable=True)
    error_message = Column(Text, nullable=True)

    # File details
    file_path = Column(String(500), nullable=True, index=True)
    file_size = Column(Integer, nullable=True)
    download_url = Column(String(500), nullable=True)

    # Multi-user support columns
    session_id = Column(
        String(255), nullable=True, index=True
    )  # User session identifier
    priority = Column(
        Integer, default=0, nullable=False
    )  # Job priority (higher = more urgent)
    queue_position = Column(Integer, nullable=True)  # Position in queue
    retry_count = Column(Integer, default=0, nullable=False)  # Number of retry attempts

    # Dual-provider support columns
    api_provider = Column(
        String(20), nullable=False, default="azure_sora", index=True
    )  # API provider (azure_sora, google_veo3)
    input_image_path = Column(Text, nullable=True)  # Veo3 image input path
    audio_generated = Column(
        Boolean, nullable=False, default=False
    )  # Audio generation flag

    # C3 Provider-aware extensions
    provider_job_id = Column(String(255), nullable=True)  # Provider-specific job ID
    provider_metadata = Column(
        Text, nullable=True
    )  # JSON string for provider-specific metadata
    provider_queue_position = Column(
        Integer, nullable=True
    )  # Position in provider-specific queue
    provider_estimated_completion = Column(
        DateTime, nullable=True
    )  # Provider-specific ETA
    provider_health_status = Column(
        String(20), nullable=True, default="healthy"
    )  # Provider health when job was submitted

    # Performance indexes for multi-user queries
    __table_args__ = (
        Index("idx_session_status", "session_id", "status"),
        Index("idx_priority_created", "priority", "created_at"),
        Index("idx_queue_position", "queue_position"),
        # Dual-provider performance indexes
        Index("idx_provider_status", "api_provider", "status"),
        Index("idx_provider_created", "api_provider", "created_at"),
        # C3 Provider-aware performance indexes
        Index("idx_provider_queue_position", "api_provider", "provider_queue_position"),
        Index("idx_provider_health_status", "api_provider", "provider_health_status"),
        Index("idx_session_provider_status", "session_id", "api_provider", "status"),
        Index(
            "idx_provider_estimated_completion",
            "api_provider",
            "provider_estimated_completion",
        ),
    )

    def to_pydantic(self) -> VideoJob:
        """
        Convert ORM model to Pydantic model.

        Returns:
            VideoJob: Pydantic model with validation
        """
        import json

        # Parse provider metadata if it exists
        provider_metadata = None
        if self.provider_metadata:
            try:
                provider_metadata = json.loads(self.provider_metadata)
            except (json.JSONDecodeError, TypeError):
                provider_metadata = None

        return VideoJob(
            id=self.id,
            prompt=self.prompt,
            status=self.status,
            created_at=self.created_at,
            completed_at=self.completed_at,
            generation_id=self.generation_id,
            error_message=self.error_message,
            file_path=self.file_path,
            download_url=self.download_url,
            session_id=self.session_id,
            priority=self.priority,
            queue_position=self.queue_position,
            retry_count=self.retry_count,
            api_provider=self.api_provider,
            input_image_path=self.input_image_path,
            audio_generated=self.audio_generated,
            # C3 Provider-aware extensions
            provider_job_id=self.provider_job_id,
            provider_metadata=provider_metadata,
            provider_queue_position=self.provider_queue_position,
            provider_estimated_completion=self.provider_estimated_completion,
            provider_health_status=self.provider_health_status or "healthy",
        )

    @classmethod
    def from_pydantic(cls, video_job: VideoJob) -> "VideoJobDB":
        """
        Create ORM model from Pydantic model.

        Args:
            video_job (VideoJob): Pydantic model to convert

        Returns:
            VideoJobDB: ORM model for database persistence
        """
        import json

        # Serialize provider metadata if it exists
        provider_metadata_str = None
        if video_job.provider_metadata:
            try:
                provider_metadata_str = json.dumps(video_job.provider_metadata)
            except (TypeError, ValueError):
                provider_metadata_str = None

        return cls(
            id=video_job.id,
            prompt=video_job.prompt,
            status=video_job.status,
            created_at=video_job.created_at,
            completed_at=video_job.completed_at,
            generation_id=video_job.generation_id,
            error_message=video_job.error_message,
            file_path=video_job.file_path,
            download_url=video_job.download_url,
            session_id=video_job.session_id,
            priority=video_job.priority,
            queue_position=video_job.queue_position,
            retry_count=video_job.retry_count,
            api_provider=video_job.api_provider,
            input_image_path=video_job.input_image_path,
            audio_generated=video_job.audio_generated,
            # C3 Provider-aware extensions
            provider_job_id=video_job.provider_job_id,
            provider_metadata=provider_metadata_str,
            provider_queue_position=video_job.provider_queue_position,
            provider_estimated_completion=video_job.provider_estimated_completion,
            provider_health_status=video_job.provider_health_status,
        )

    def update_from_pydantic(self, video_job: VideoJob) -> None:
        """
        Update ORM model fields from Pydantic model.

        Args:
            video_job (VideoJob): Pydantic model with updated data
        """
        import json

        # Serialize provider metadata if it exists
        provider_metadata_str = None
        if video_job.provider_metadata:
            try:
                provider_metadata_str = json.dumps(video_job.provider_metadata)
            except (TypeError, ValueError):
                provider_metadata_str = None

        self.prompt = video_job.prompt
        self.status = video_job.status
        self.completed_at = video_job.completed_at
        self.generation_id = video_job.generation_id
        self.error_message = video_job.error_message
        self.file_path = video_job.file_path
        self.download_url = video_job.download_url
        self.session_id = video_job.session_id
        self.priority = video_job.priority
        self.queue_position = video_job.queue_position
        self.retry_count = video_job.retry_count
        self.api_provider = video_job.api_provider
        self.input_image_path = video_job.input_image_path
        self.audio_generated = video_job.audio_generated
        # C3 Provider-aware extensions
        self.provider_job_id = video_job.provider_job_id
        self.provider_metadata = provider_metadata_str
        self.provider_queue_position = video_job.provider_queue_position
        self.provider_estimated_completion = video_job.provider_estimated_completion
        self.provider_health_status = video_job.provider_health_status

    def __repr__(self) -> str:
        """
        String representation for debugging.

        Returns:
            str: Human-readable representation
        """
        return f"<VideoJobDB(id='{self.id}', status='{self.status}', prompt='{self.prompt[:50]}...', provider='{self.api_provider}')>"


class ProviderSessionDB(Base):
    """
    SQLAlchemy ORM model for tracking provider preferences and usage per session.

    Provides database persistence for session-based provider preferences
    and usage statistics in multi-provider environments.
    """

    __tablename__ = "provider_sessions"

    # Primary key
    id = Column(String(36), primary_key=True)

    # Session details
    session_id = Column(String(255), nullable=False, index=True)

    # Provider preferences
    preferred_provider = Column(String(20), nullable=True)  # User's preferred provider
    last_used_provider = Column(String(20), nullable=True)  # Last provider used

    # Usage statistics
    azure_sora_usage_count = Column(Integer, default=0, nullable=False)
    google_veo3_usage_count = Column(Integer, default=0, nullable=False)
    total_jobs_submitted = Column(Integer, default=0, nullable=False)

    # Provider-specific settings (JSON string)
    provider_settings = Column(
        Text, nullable=True
    )  # JSON string for provider-specific settings

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    last_activity = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Performance indexes
    __table_args__ = (
        Index("idx_session_provider_usage", "session_id", "last_used_provider"),
        Index("idx_session_activity", "session_id", "last_activity"),
        Index("idx_provider_preference", "session_id", "preferred_provider"),
    )

    def __repr__(self) -> str:
        """
        String representation for debugging.

        Returns:
            str: Human-readable representation
        """
        return f"<ProviderSessionDB(session_id='{self.session_id}', preferred='{self.preferred_provider}', total_jobs={self.total_jobs_submitted})>"


class ProviderHealthDB(Base):
    """
    SQLAlchemy ORM model for tracking provider health status and metrics.

    Provides database persistence for provider health monitoring
    and historical health data for analytics.
    """

    __tablename__ = "provider_health"

    # Primary key
    id = Column(String(36), primary_key=True)

    # Provider details
    provider_name = Column(String(20), nullable=False, index=True)

    # Health status
    health_status = Column(String(20), nullable=False)  # healthy, degraded, unhealthy
    health_details = Column(Text, nullable=True)  # JSON string for detailed health info

    # Performance metrics
    response_time_ms = Column(Integer, nullable=True)
    success_rate = Column(Integer, nullable=True)  # Percentage (0-100)
    queue_length = Column(Integer, default=0, nullable=False)
    active_workers = Column(Integer, default=0, nullable=False)

    # Timestamps
    checked_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Performance indexes
    __table_args__ = (
        Index("idx_health_provider_status", "provider_name", "health_status"),
        Index("idx_provider_checked_at", "provider_name", "checked_at"),
        Index("idx_health_timestamp", "checked_at"),
    )

    def __repr__(self) -> str:
        """
        String representation for debugging.

        Returns:
            str: Human-readable representation
        """
        return f"<ProviderHealthDB(provider='{self.provider_name}', status='{self.health_status}', checked_at='{self.checked_at}')>"


class ImageUploadRecord(Base):
    """
    SQLAlchemy ORM model for tracking image upload records in C1 Image Security Pipeline.

    Provides database persistence for image uploads with security validation
    and processing status tracking.
    """

    __tablename__ = "image_uploads"

    # Primary key
    id = Column(String(36), primary_key=True)

    # Image details
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=True)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)

    # Security validation status
    security_status = Column(
        Enum("pending", "approved", "rejected", "processing", name="security_status"),
        nullable=False,
        default="pending",
        index=True,
    )

    # Validation details
    validation_score = Column(Integer, nullable=True)  # 0-100 safety score
    validation_details = Column(
        Text, nullable=True
    )  # JSON string for detailed validation results
    rejection_reason = Column(Text, nullable=True)  # Reason for rejection if applicable

    # Session and user context
    session_id = Column(String(255), nullable=False, index=True)
    client_ip = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)

    # Processing metadata
    processed = Column(Boolean, nullable=False, default=False)
    processing_duration_ms = Column(Integer, nullable=True)

    # Timestamps
    uploaded_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    validated_at = Column(DateTime, nullable=True)
    processed_at = Column(DateTime, nullable=True)

    # Performance indexes for C1 integration
    __table_args__ = (
        Index("idx_session_security_status", "session_id", "security_status"),
        Index("idx_security_status_uploaded", "security_status", "uploaded_at"),
        Index("idx_filename_session", "filename", "session_id"),
        Index("idx_validation_score", "validation_score"),
    )

    def __repr__(self) -> str:
        """
        String representation for debugging.

        Returns:
            str: Human-readable representation
        """
        return f"<ImageUploadRecord(id='{self.id}', filename='{self.filename}', status='{self.security_status}', session='{self.session_id}')>"
