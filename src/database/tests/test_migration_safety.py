"""Tests for migration safety and rollback procedures."""

import os
import tempfile
import uuid

import pytest
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.orm import sessionmaker

from src.database.validation.migration_validator import MigrationValidator


class MigrationSafetyValidator:
    """Additional migration safety validation helper for testing."""

    def __init__(self):
        self.warnings = []
        self.errors = []

    def validate_migration_sql(self, sql: str) -> tuple[bool, list[str]]:
        """
        Validate migration SQL for safety patterns.

        Args:
            sql (str): Migration SQL to validate

        Returns:
            tuple[bool, list[str]]: (is_safe, warnings)
        """
        warnings = []
        is_safe = True

        # Check for unsafe patterns
        unsafe_patterns = [
            ("DROP TABLE", "Table drops are dangerous"),
            ("ALTER TABLE", "ALTER TABLE without DEFAULT is risky"),
            ("NOT NULL", "NOT NULL columns without DEFAULT can break existing data"),
        ]

        sql_upper = sql.upper()

        for pattern, message in unsafe_patterns:
            if pattern in sql_upper:
                if pattern == "NOT NULL" and "DEFAULT" not in sql_upper:
                    warnings.append(f"NOT NULL column without DEFAULT: {message}")
                    is_safe = False
                elif pattern == "DROP TABLE":
                    warnings.append(f"Dangerous operation: {message}")
                    is_safe = False

        return is_safe, warnings


@pytest.mark.migration_safety
class TestMigrationSafety:
    """Test migration safety and rollback procedures."""

    @pytest.fixture
    def temp_database(self):
        """Create temporary database for migration testing."""
        # Create temporary SQLite database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        temp_db.close()

        db_url = f"sqlite:///{temp_db.name}"
        engine = create_engine(db_url)

        # Create original schema (pre-provider fields)
        with engine.connect() as conn:
            conn.execute(
                text("""
                CREATE TABLE video_jobs (
                    id VARCHAR(36) PRIMARY KEY,
                    prompt TEXT NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    created_at DATETIME,
                    session_id VARCHAR(255),
                    priority INTEGER DEFAULT 0,
                    queue_position INTEGER,
                    retry_count INTEGER DEFAULT 0
                )
            """)
            )
            conn.commit()

        yield engine, temp_db.name

        # Cleanup
        if os.path.exists(temp_db.name):
            os.unlink(temp_db.name)

    def test_zero_downtime_migration_simulation(self, temp_database):
        """Test zero-downtime migration procedure."""
        engine, db_path = temp_database

        # Insert test data before migration
        with engine.connect() as conn:
            conn.execute(
                text("""
                INSERT INTO video_jobs (id, prompt, status, created_at, session_id)
                VALUES ('test-1', 'Test prompt', 'succeeded', datetime('now'), 'session-1')
            """)
            )
            conn.commit()

        # Simulate adding provider fields with defaults
        with engine.connect() as conn:
            # Phase 1: Add nullable columns with defaults
            conn.execute(
                text("""
                ALTER TABLE video_jobs 
                ADD COLUMN api_provider VARCHAR(20) DEFAULT 'azure_sora'
            """)
            )
            conn.execute(
                text("""
                ALTER TABLE video_jobs 
                ADD COLUMN input_image_path TEXT
            """)
            )
            conn.execute(
                text("""
                ALTER TABLE video_jobs 
                ADD COLUMN audio_generated BOOLEAN DEFAULT 0
            """)
            )
            conn.commit()

            # Phase 2: Update existing data
            conn.execute(
                text("""
                UPDATE video_jobs 
                SET api_provider = 'azure_sora' 
                WHERE api_provider IS NULL
            """)
            )
            conn.commit()

            # Validate data integrity
            result = conn.execute(
                text("""
                SELECT id, prompt, status, api_provider, input_image_path, audio_generated
                FROM video_jobs WHERE id = 'test-1'
            """)
            ).fetchone()

            assert result is not None
            assert result[3] == "azure_sora"  # api_provider
            assert result[4] is None  # input_image_path
            assert result[5] == 0  # audio_generated

    def test_rollback_procedure_validation(self, temp_database):
        """Test rollback procedures maintain data integrity."""
        engine, db_path = temp_database

        # Insert test data
        with engine.connect() as conn:
            conn.execute(
                text("""
                INSERT INTO video_jobs (id, prompt, status, created_at, session_id)
                VALUES ('rollback-test', 'Rollback test', 'pending', datetime('now'), 'session-1')
            """)
            )
            conn.commit()

        # Apply migration
        with engine.connect() as conn:
            conn.execute(
                text(
                    'ALTER TABLE video_jobs ADD COLUMN api_provider VARCHAR(20) DEFAULT "azure_sora"'
                )
            )
            conn.execute(
                text("ALTER TABLE video_jobs ADD COLUMN input_image_path TEXT")
            )
            conn.execute(
                text(
                    "ALTER TABLE video_jobs ADD COLUMN audio_generated BOOLEAN DEFAULT 0"
                )
            )
            conn.execute(text('UPDATE video_jobs SET api_provider = "azure_sora"'))
            conn.commit()

        # Verify migration applied
        with engine.connect() as conn:
            result = conn.execute(
                text("""
                SELECT api_provider FROM video_jobs WHERE id = 'rollback-test'
            """)
            ).fetchone()
            assert result[0] == "azure_sora"

        # Test rollback - SQLite doesn't support DROP COLUMN, so we simulate with table recreation
        with engine.connect() as conn:
            # Create new table with original schema
            conn.execute(
                text("""
                CREATE TABLE video_jobs_backup AS 
                SELECT id, prompt, status, created_at, session_id, priority, queue_position, retry_count
                FROM video_jobs
            """)
            )

            # Drop original table and rename backup
            conn.execute(text("DROP TABLE video_jobs"))
            conn.execute(text("ALTER TABLE video_jobs_backup RENAME TO video_jobs"))
            conn.commit()

            # Validate essential data is preserved
            result = conn.execute(
                text("""
                SELECT id, prompt, status FROM video_jobs WHERE id = 'rollback-test'
            """)
            ).fetchone()

            assert result is not None
            assert result[0] == "rollback-test"  # id preserved
            assert result[1] == "Rollback test"  # prompt preserved
            assert result[2] == "pending"  # status preserved

    def test_migration_safety_validator(self):
        """Test migration safety validation logic."""
        validator = MigrationSafetyValidator()

        # Test safe migration SQL
        safe_sql = """
            ALTER TABLE video_jobs 
            ADD COLUMN api_provider VARCHAR(20) DEFAULT 'azure_sora';
        """
        is_safe, warnings = validator.validate_migration_sql(safe_sql)
        assert is_safe is True
        assert len(warnings) == 0

        # Test unsafe migration SQL
        unsafe_sql = """
            ALTER TABLE video_jobs 
            ADD COLUMN api_provider VARCHAR(20) NOT NULL;
        """
        is_safe, warnings = validator.validate_migration_sql(unsafe_sql)
        assert is_safe is False
        assert len(warnings) > 0
        assert any("NOT NULL column without DEFAULT" in warning for warning in warnings)

        # Test dangerous DROP TABLE
        dangerous_sql = """
            DROP TABLE video_jobs;
        """
        is_safe, warnings = validator.validate_migration_sql(dangerous_sql)
        assert is_safe is False
        assert any("Table drops are dangerous" in warning for warning in warnings)

    def test_migration_validator_integration(self):
        """Test MigrationValidator integration with temporary database."""
        # Create temporary database for testing
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        temp_db.close()

        try:
            db_url = f"sqlite:///{temp_db.name}"

            # Initialize with base schema
            engine = create_engine(db_url)
            with engine.connect() as conn:
                conn.execute(
                    text("""
                    CREATE TABLE video_jobs (
                        id VARCHAR(36) PRIMARY KEY,
                        prompt TEXT NOT NULL,
                        status VARCHAR(20) NOT NULL,
                        created_at DATETIME,
                        session_id VARCHAR(255)
                    )
                """)
                )

                # Insert test data
                conn.execute(
                    text("""
                    INSERT INTO video_jobs (id, prompt, status, created_at, session_id)
                    VALUES ('test-migration', 'Test prompt', 'pending', datetime('now'), 'test-session')
                """)
                )
                conn.commit()

            # Test migration validator
            validator = MigrationValidator(db_url)
            validation_results = validator.validate_migration_safety()

            # Should be safe to migrate
            assert validation_results["safe_to_migrate"] is True
            assert "schema_validation" in validation_results["metrics"]
            assert "data_integrity" in validation_results["metrics"]

            # Validate schema check
            schema_check = validation_results["metrics"]["schema_validation"]
            assert schema_check["valid"] is True
            assert "video_jobs" in str(schema_check.get("current_columns", []))

            # Validate data integrity check
            data_check = validation_results["metrics"]["data_integrity"]
            assert data_check["valid"] is True
            assert data_check["total_records"] >= 1

        finally:
            if os.path.exists(temp_db.name):
                os.unlink(temp_db.name)

    def test_concurrent_migration_safety(self, temp_database):
        """Test migration safety with concurrent operations."""
        engine, db_path = temp_database

        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Insert test data before migration
            with engine.connect() as conn:
                conn.execute(
                    text("""
                    INSERT INTO video_jobs (id, prompt, status, created_at, session_id)
                    VALUES ('concurrent-test-1', 'Concurrent test', 'running', datetime('now'), 'test-session')
                """)
                )
                conn.commit()

            # Simulate reading during migration (should not fail)
            with engine.connect() as conn:
                result = conn.execute(
                    text("""
                    SELECT * FROM video_jobs WHERE id = 'concurrent-test-1'
                """)
                ).fetchone()
                assert result is not None

                # Apply provider migration
                conn.execute(
                    text(
                        'ALTER TABLE video_jobs ADD COLUMN api_provider VARCHAR(20) DEFAULT "azure_sora"'
                    )
                )
                conn.execute(
                    text("ALTER TABLE video_jobs ADD COLUMN input_image_path TEXT")
                )
                conn.execute(
                    text(
                        "ALTER TABLE video_jobs ADD COLUMN audio_generated BOOLEAN DEFAULT 0"
                    )
                )
                conn.commit()

                # Verify data still accessible during migration
                result = conn.execute(
                    text("""
                    SELECT id, prompt, status, api_provider FROM video_jobs WHERE id = 'concurrent-test-1'
                """)
                ).fetchone()
                assert result is not None
                assert result[0] == "concurrent-test-1"
                assert result[3] == "azure_sora"  # Default applied

        finally:
            session.close()

    def test_migration_rollback_data_integrity(self, temp_database):
        """Test data integrity during migration rollback."""
        engine, db_path = temp_database

        test_data = [
            ("rollback-test-1", "Rollback test 1", "succeeded"),
            ("rollback-test-2", "Rollback test 2", "pending"),
            ("rollback-test-3", "Rollback test 3", "failed"),
        ]

        # Insert test data
        with engine.connect() as conn:
            for job_id, prompt, status in test_data:
                conn.execute(
                    text("""
                    INSERT INTO video_jobs (id, prompt, status, created_at, session_id)
                    VALUES (?, ?, ?, datetime('now'), 'rollback-session')
                """),
                    (job_id, prompt, status),
                )
            conn.commit()

        # Apply migration with provider data
        with engine.connect() as conn:
            conn.execute(
                text(
                    'ALTER TABLE video_jobs ADD COLUMN api_provider VARCHAR(20) DEFAULT "azure_sora"'
                )
            )
            conn.execute(
                text("ALTER TABLE video_jobs ADD COLUMN input_image_path TEXT")
            )
            conn.execute(
                text(
                    "ALTER TABLE video_jobs ADD COLUMN audio_generated BOOLEAN DEFAULT 0"
                )
            )

            # Update some records with Veo3 data
            conn.execute(
                text("""
                UPDATE video_jobs 
                SET api_provider = 'google_veo3', input_image_path = '/test/image.jpg'
                WHERE id = 'rollback-test-2'
            """)
            )
            conn.commit()

        # Verify migration data exists
        with engine.connect() as conn:
            result = conn.execute(
                text("""
                SELECT api_provider, input_image_path FROM video_jobs WHERE id = 'rollback-test-2'
            """)
            ).fetchone()
            assert result[0] == "google_veo3"
            assert result[1] == "/test/image.jpg"

        # Simulate rollback by recreating table without provider columns
        with engine.connect() as conn:
            # Create backup with essential data
            conn.execute(
                text("""
                CREATE TABLE video_jobs_rollback AS 
                SELECT id, prompt, status, created_at, session_id, priority, queue_position, retry_count
                FROM video_jobs
            """)
            )

            # Replace original table
            conn.execute(text("DROP TABLE video_jobs"))
            conn.execute(text("ALTER TABLE video_jobs_rollback RENAME TO video_jobs"))
            conn.commit()

            # Verify all essential data preserved
            for job_id, expected_prompt, expected_status in test_data:
                result = conn.execute(
                    text("""
                    SELECT id, prompt, status FROM video_jobs WHERE id = ?
                """),
                    (job_id,),
                ).fetchone()

                assert result is not None
                assert result[0] == job_id
                assert result[1] == expected_prompt
                assert result[2] == expected_status

    def test_batch_alter_table_usage(self):
        """Test that migration uses batch_alter_table for SQLite compatibility."""
        # This test verifies the migration pattern follows SQLite best practices
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        temp_db.close()

        try:
            db_url = f"sqlite:///{temp_db.name}"
            engine = create_engine(db_url)

            # Create base schema
            with engine.connect() as conn:
                conn.execute(
                    text("""
                    CREATE TABLE video_jobs (
                        id VARCHAR(36) PRIMARY KEY,
                        prompt TEXT NOT NULL,
                        status VARCHAR(20) NOT NULL
                    )
                """)
                )

                # Insert test data
                conn.execute(
                    text("""
                    INSERT INTO video_jobs (id, prompt, status)
                    VALUES ('batch-test', 'Batch test', 'pending')
                """)
                )
                conn.commit()

            # Verify we can simulate the batch alter pattern
            with engine.connect() as conn:
                # This simulates what batch_alter_table does internally
                conn.execute(
                    text("""
                    CREATE TABLE video_jobs_new AS 
                    SELECT id, prompt, status FROM video_jobs
                """)
                )

                # Add new columns to new table structure
                conn.execute(
                    text(
                        'ALTER TABLE video_jobs_new ADD COLUMN api_provider VARCHAR(20) DEFAULT "azure_sora"'
                    )
                )
                conn.execute(
                    text("ALTER TABLE video_jobs_new ADD COLUMN input_image_path TEXT")
                )
                conn.execute(
                    text(
                        "ALTER TABLE video_jobs_new ADD COLUMN audio_generated BOOLEAN DEFAULT 0"
                    )
                )

                # Replace original table
                conn.execute(text("DROP TABLE video_jobs"))
                conn.execute(text("ALTER TABLE video_jobs_new RENAME TO video_jobs"))
                conn.commit()

            # Verify table still exists and is functional
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            assert "video_jobs" in tables

            # Verify data and new columns
            with engine.connect() as conn:
                result = conn.execute(
                    text("""
                    SELECT id, prompt, status, api_provider, input_image_path, audio_generated
                    FROM video_jobs WHERE id = 'batch-test'
                """)
                ).fetchone()

                assert result is not None
                assert result[0] == "batch-test"
                assert result[1] == "Batch test"
                assert result[2] == "pending"
                assert result[3] == "azure_sora"  # Default applied
                assert result[4] is None  # NULL for new column
                assert result[5] == 0  # Default False

        finally:
            if os.path.exists(temp_db.name):
                os.unlink(temp_db.name)

    def test_migration_performance_impact(self, temp_database):
        """Test migration performance with realistic data volume."""
        engine, db_path = temp_database

        # Create realistic test data volume
        test_jobs = []
        for i in range(100):
            test_jobs.append(
                {
                    "id": f"perf-test-{i}-{uuid.uuid4()}",
                    "prompt": f"Performance test job {i}",
                    "status": ["pending", "running", "succeeded", "failed"][i % 4],
                    "session_id": f"perf-session-{i % 10}",
                }
            )

        # Insert test data
        with engine.connect() as conn:
            for job in test_jobs:
                conn.execute(
                    text("""
                    INSERT INTO video_jobs (id, prompt, status, created_at, session_id)
                    VALUES (?, ?, ?, datetime('now'), ?)
                """),
                    (job["id"], job["prompt"], job["status"], job["session_id"]),
                )
            conn.commit()

        # Measure migration performance
        import time

        start_time = time.perf_counter()

        with engine.connect() as conn:
            # Apply provider migration
            conn.execute(
                text(
                    'ALTER TABLE video_jobs ADD COLUMN api_provider VARCHAR(20) DEFAULT "azure_sora"'
                )
            )
            conn.execute(
                text("ALTER TABLE video_jobs ADD COLUMN input_image_path TEXT")
            )
            conn.execute(
                text(
                    "ALTER TABLE video_jobs ADD COLUMN audio_generated BOOLEAN DEFAULT 0"
                )
            )

            # Update all records with defaults
            conn.execute(
                text(
                    'UPDATE video_jobs SET api_provider = "azure_sora" WHERE api_provider IS NULL'
                )
            )
            conn.commit()

        migration_time = time.perf_counter() - start_time

        # Migration should complete within reasonable time for test data
        assert migration_time < 5.0, (
            f"Migration took {migration_time:.2f}s, exceeds 5s limit for 100 records"
        )

        # Verify all data migrated correctly
        with engine.connect() as conn:
            migrated_count = conn.execute(
                text("""
                SELECT COUNT(*) FROM video_jobs WHERE api_provider = 'azure_sora'
            """)
            ).fetchone()[0]

            assert migrated_count == 100, (
                f"Expected 100 migrated records, got {migrated_count}"
            )
