"""Tests for provider-aware database models."""

from datetime import datetime

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.models import VideoJob
from src.database.models import Base, VideoJobDB


class TestVideoJobDBProviderSupport:
    """Test VideoJobDB model with provider support."""

    @pytest.fixture
    def test_engine(self):
        """Create in-memory SQLite engine for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture
    def test_session(self, test_engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=test_engine)
        session = Session()
        yield session
        session.close()

    def test_create_job_with_azure_provider(self, test_session):
        """Test creating a job with Azure Sora provider."""
        job = VideoJobDB(
            id="test-job-azure-123",
            prompt="Test Azure prompt",
            status="pending",
            api_provider="azure_sora",
            input_image_path=None,
            audio_generated=False,
        )

        test_session.add(job)
        test_session.commit()

        # Verify job was created correctly
        retrieved = (
            test_session.query(VideoJobDB).filter_by(id="test-job-azure-123").first()
        )
        assert retrieved is not None
        assert retrieved.api_provider == "azure_sora"
        assert retrieved.input_image_path is None
        assert retrieved.audio_generated is False

    def test_create_job_with_veo3_provider(self, test_session):
        """Test creating a job with Google Veo3 provider."""
        job = VideoJobDB(
            id="test-job-veo3-456",
            prompt="Test Veo3 prompt",
            status="pending",
            api_provider="google_veo3",
            input_image_path="/path/to/image.jpg",
            audio_generated=True,
        )

        test_session.add(job)
        test_session.commit()

        # Verify job was created correctly
        retrieved = (
            test_session.query(VideoJobDB).filter_by(id="test-job-veo3-456").first()
        )
        assert retrieved is not None
        assert retrieved.api_provider == "google_veo3"
        assert retrieved.input_image_path == "/path/to/image.jpg"
        assert retrieved.audio_generated is True

    def test_default_provider_values(self, test_session):
        """Test default values for provider fields."""
        job = VideoJobDB(
            id="test-job-defaults-789",
            prompt="Test default values",
            status="pending",
        )

        test_session.add(job)
        test_session.commit()

        retrieved = (
            test_session.query(VideoJobDB).filter_by(id="test-job-defaults-789").first()
        )
        assert retrieved.api_provider == "azure_sora"  # Default provider
        assert retrieved.input_image_path is None
        assert retrieved.audio_generated is False

    def test_to_pydantic_with_provider_fields(self, test_session):
        """Test conversion to Pydantic model includes provider fields."""
        job = VideoJobDB(
            id="test-conversion-123",
            prompt="Test conversion",
            status="running",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            input_image_path="/test/image.png",
            audio_generated=True,
        )

        test_session.add(job)
        test_session.commit()

        # Convert to Pydantic
        pydantic_job = job.to_pydantic()

        assert isinstance(pydantic_job, VideoJob)
        assert pydantic_job.id == "test-conversion-123"
        assert pydantic_job.api_provider == "google_veo3"
        assert pydantic_job.input_image_path == "/test/image.png"
        assert pydantic_job.audio_generated is True

    def test_from_pydantic_with_provider_fields(self):
        """Test creation from Pydantic model includes provider fields."""
        # Create Pydantic job
        pydantic_job = VideoJob(
            id="test-from-pydantic-456",
            prompt="Test from Pydantic",
            status="succeeded",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            input_image_path="/pydantic/image.jpg",
            audio_generated=True,
        )

        # Convert from Pydantic
        db_job = VideoJobDB.from_pydantic(pydantic_job)

        assert db_job.id == "test-from-pydantic-456"
        assert db_job.api_provider == "google_veo3"
        assert db_job.input_image_path == "/pydantic/image.jpg"
        assert db_job.audio_generated is True

    def test_update_from_pydantic_with_provider_fields(self, test_session):
        """Test updating from Pydantic model includes provider fields."""
        # Create initial job
        job = VideoJobDB(
            id="test-update-789",
            prompt="Initial prompt",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="azure_sora",
            input_image_path=None,
            audio_generated=False,
        )

        test_session.add(job)
        test_session.commit()

        # Create updated Pydantic job
        updated_pydantic = VideoJob(
            id="test-update-789",
            prompt="Updated prompt",
            status="running",
            created_at=job.created_at,
            api_provider="google_veo3",
            input_image_path="/updated/image.png",
            audio_generated=True,
        )

        # Update from Pydantic
        job.update_from_pydantic(updated_pydantic)
        test_session.commit()

        # Verify updates
        assert job.prompt == "Updated prompt"
        assert job.api_provider == "google_veo3"
        assert job.input_image_path == "/updated/image.png"
        assert job.audio_generated is True

    def test_provider_indexes_exist(self, test_engine):
        """Test that provider-specific indexes exist."""
        inspector = __import__("sqlalchemy").inspect(test_engine)
        indexes = inspector.get_indexes("video_jobs")

        index_names = [idx["name"] for idx in indexes]
        assert "idx_provider_status" in index_names
        assert "idx_provider_created" in index_names

    def test_query_by_provider(self, test_session):
        """Test querying jobs by provider."""
        # Create jobs with different providers
        azure_job = VideoJobDB(
            id="azure-job-1",
            prompt="Azure job",
            status="pending",
            api_provider="azure_sora",
        )

        veo3_job = VideoJobDB(
            id="veo3-job-1",
            prompt="Veo3 job",
            status="pending",
            api_provider="google_veo3",
        )

        test_session.add_all([azure_job, veo3_job])
        test_session.commit()

        # Query by provider
        azure_jobs = (
            test_session.query(VideoJobDB).filter_by(api_provider="azure_sora").all()
        )
        veo3_jobs = (
            test_session.query(VideoJobDB).filter_by(api_provider="google_veo3").all()
        )

        assert len(azure_jobs) == 1
        assert len(veo3_jobs) == 1
        assert azure_jobs[0].id == "azure-job-1"
        assert veo3_jobs[0].id == "veo3-job-1"

    def test_provider_status_combined_query(self, test_session):
        """Test querying by provider and status combination."""
        # Create jobs with different provider/status combinations
        jobs = [
            VideoJobDB(
                id="1", prompt="test", status="pending", api_provider="azure_sora"
            ),
            VideoJobDB(
                id="2", prompt="test", status="running", api_provider="azure_sora"
            ),
            VideoJobDB(
                id="3", prompt="test", status="pending", api_provider="google_veo3"
            ),
            VideoJobDB(
                id="4", prompt="test", status="succeeded", api_provider="google_veo3"
            ),
        ]

        test_session.add_all(jobs)
        test_session.commit()

        # Query by provider and status
        azure_pending = (
            test_session.query(VideoJobDB)
            .filter_by(api_provider="azure_sora", status="pending")
            .all()
        )

        veo3_succeeded = (
            test_session.query(VideoJobDB)
            .filter_by(api_provider="google_veo3", status="succeeded")
            .all()
        )

        assert len(azure_pending) == 1
        assert len(veo3_succeeded) == 1
        assert azure_pending[0].id == "1"
        assert veo3_succeeded[0].id == "4"
