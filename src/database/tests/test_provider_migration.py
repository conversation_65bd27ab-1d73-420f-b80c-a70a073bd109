"""Tests for provider support migration safety and functionality."""

import os
import tempfile
from datetime import datetime

import pytest
from alembic.config import Config
from alembic.runtime.environment import EnvironmentContext
from alembic.script import ScriptDirectory
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.orm import sessionmaker

from src.database.models import Base, VideoJobDB


class TestProviderMigration:
    """Test provider support migration upgrade/downgrade safety."""

    @pytest.fixture
    def temp_db_path(self):
        """Create temporary database file for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)

    @pytest.fixture
    def test_engine(self, temp_db_path):
        """Create test database engine."""
        return create_engine(f"sqlite:///{temp_db_path}", echo=False)

    @pytest.fixture
    def alembic_config(self, temp_db_path):
        """Create Alembic configuration for testing."""
        config = Config()
        config.set_main_option("script_location", "/workspace/migrations")
        config.set_main_option("sqlalchemy.url", f"sqlite:///{temp_db_path}")
        return config

    def test_migration_upgrade_creates_provider_columns(
        self, test_engine, alembic_config
    ):
        """Test migration upgrade creates provider columns correctly."""
        # Create base schema (simulate state before provider migration)
        Base.metadata.create_all(test_engine)

        # Remove provider columns to simulate pre-migration state
        with test_engine.connect() as conn:
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN api_provider"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN input_image_path"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN audio_generated"))
            conn.commit()

        # Run migration upgrade
        with test_engine.connect() as conn:
            context = EnvironmentContext(
                config=alembic_config,
                script=ScriptDirectory.from_config(alembic_config),
            )
            context.configure(connection=conn, target_metadata=Base.metadata)

            # Apply the provider migration
            conn.execute(
                text("""
                ALTER TABLE video_jobs ADD COLUMN api_provider VARCHAR(20) NOT NULL DEFAULT 'azure_sora';
                ALTER TABLE video_jobs ADD COLUMN input_image_path TEXT;
                ALTER TABLE video_jobs ADD COLUMN audio_generated BOOLEAN NOT NULL DEFAULT 0;
                CREATE INDEX idx_provider_status ON video_jobs (api_provider, status);
                CREATE INDEX idx_provider_created ON video_jobs (api_provider, created_at);
            """)
            )
            conn.commit()

        # Verify columns exist
        inspector = inspect(test_engine)
        columns = inspector.get_columns("video_jobs")
        column_names = [col["name"] for col in columns]

        assert "api_provider" in column_names
        assert "input_image_path" in column_names
        assert "audio_generated" in column_names

        # Verify indexes exist
        indexes = inspector.get_indexes("video_jobs")
        index_names = [idx["name"] for idx in indexes]

        assert "idx_provider_status" in index_names
        assert "idx_provider_created" in index_names

    def test_migration_preserves_existing_data(self, test_engine, alembic_config):
        """Test migration preserves existing job data."""
        # Create base schema and insert test data
        Base.metadata.create_all(test_engine)

        Session = sessionmaker(bind=test_engine)
        session = Session()

        # Insert test data before migration
        with test_engine.connect() as conn:
            # Remove provider columns first
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN api_provider"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN input_image_path"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN audio_generated"))

            # Insert test data
            conn.execute(
                text("""
                INSERT INTO video_jobs (id, prompt, status, created_at, session_id, priority)
                VALUES ('test-job-1', 'Test prompt', 'succeeded', datetime('now'), 'session-1', 0)
            """)
            )
            conn.commit()

        # Apply migration
        with test_engine.connect() as conn:
            conn.execute(
                text("""
                ALTER TABLE video_jobs ADD COLUMN api_provider VARCHAR(20) NOT NULL DEFAULT 'azure_sora';
                ALTER TABLE video_jobs ADD COLUMN input_image_path TEXT;
                ALTER TABLE video_jobs ADD COLUMN audio_generated BOOLEAN NOT NULL DEFAULT 0;
            """)
            )
            conn.commit()

        # Verify data is preserved with default values
        with test_engine.connect() as conn:
            result = conn.execute(
                text("""
                SELECT id, prompt, status, api_provider, input_image_path, audio_generated
                FROM video_jobs WHERE id = 'test-job-1'
            """)
            ).fetchone()

        assert result is not None
        assert result[0] == "test-job-1"  # id
        assert result[1] == "Test prompt"  # prompt
        assert result[2] == "succeeded"  # status
        assert result[3] == "azure_sora"  # api_provider (default)
        assert result[4] is None  # input_image_path (default)
        assert result[5] == 0  # audio_generated (default False/0)

        session.close()

    def test_migration_downgrade_removes_provider_columns(self, test_engine):
        """Test migration downgrade properly removes provider columns."""
        # Create full schema with provider columns
        Base.metadata.create_all(test_engine)

        # Verify provider columns exist
        inspector = inspect(test_engine)
        columns = inspector.get_columns("video_jobs")
        column_names = [col["name"] for col in columns]

        assert "api_provider" in column_names
        assert "input_image_path" in column_names
        assert "audio_generated" in column_names

        # Simulate downgrade
        with test_engine.connect() as conn:
            conn.execute(text("DROP INDEX idx_provider_created"))
            conn.execute(text("DROP INDEX idx_provider_status"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN audio_generated"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN input_image_path"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN api_provider"))
            conn.commit()

        # Verify columns are removed
        inspector = inspect(test_engine)
        columns = inspector.get_columns("video_jobs")
        column_names = [col["name"] for col in columns]

        assert "api_provider" not in column_names
        assert "input_image_path" not in column_names
        assert "audio_generated" not in column_names

        # Verify indexes are removed
        indexes = inspector.get_indexes("video_jobs")
        index_names = [idx["name"] for idx in indexes]

        assert "idx_provider_status" not in index_names
        assert "idx_provider_created" not in index_names

    def test_migration_default_values(self, test_engine):
        """Test migration applies correct default values."""
        # Create base schema
        Base.metadata.create_all(test_engine)

        # Insert job with provider fields using defaults
        Session = sessionmaker(bind=test_engine)
        session = Session()

        job = VideoJobDB(
            id="test-defaults-123",
            prompt="Test default values",
            status="pending",
            created_at=datetime.utcnow(),
        )

        session.add(job)
        session.commit()

        # Verify defaults are applied
        retrieved = session.query(VideoJobDB).filter_by(id="test-defaults-123").first()
        assert retrieved.api_provider == "azure_sora"
        assert retrieved.input_image_path is None
        assert retrieved.audio_generated is False

        session.close()

    def test_provider_index_performance(self, test_engine):
        """Test provider indexes improve query performance."""
        # Create schema with provider indexes
        Base.metadata.create_all(test_engine)

        Session = sessionmaker(bind=test_engine)
        session = Session()

        # Insert test data with different providers
        test_jobs = []
        for i in range(100):
            provider = "azure_sora" if i % 2 == 0 else "google_veo3"
            status = "pending" if i % 3 == 0 else "succeeded"

            job = VideoJobDB(
                id=f"perf-test-{i}",
                prompt=f"Performance test job {i}",
                status=status,
                created_at=datetime.utcnow(),
                api_provider=provider,
            )
            test_jobs.append(job)

        session.add_all(test_jobs)
        session.commit()

        # Test query performance with provider index
        import time

        # Query using provider index
        start_time = time.time()
        azure_jobs = (
            session.query(VideoJobDB)
            .filter_by(api_provider="azure_sora", status="pending")
            .all()
        )
        provider_query_time = time.time() - start_time

        # Verify results
        assert len(azure_jobs) > 0
        assert all(job.api_provider == "azure_sora" for job in azure_jobs)
        assert all(job.status == "pending" for job in azure_jobs)

        # Query should be reasonably fast (under 100ms for small dataset)
        assert provider_query_time < 0.1

        session.close()

    def test_concurrent_migration_safety(self, test_engine):
        """Test migration safety with concurrent operations."""
        # Create base schema
        Base.metadata.create_all(test_engine)

        Session = sessionmaker(bind=test_engine)
        session = Session()

        # Insert test data before migration
        job = VideoJobDB(
            id="concurrent-test-1",
            prompt="Concurrent test",
            status="running",
            created_at=datetime.utcnow(),
        )

        session.add(job)
        session.commit()

        # Simulate reading during migration (should not fail)
        retrieved = session.query(VideoJobDB).filter_by(id="concurrent-test-1").first()
        assert retrieved is not None
        assert retrieved.api_provider == "azure_sora"  # Default applied

        # Test updating job with provider fields
        retrieved.api_provider = "google_veo3"
        retrieved.input_image_path = "/test/image.jpg"
        retrieved.audio_generated = True

        session.commit()

        # Verify update worked
        updated = session.query(VideoJobDB).filter_by(id="concurrent-test-1").first()
        assert updated.api_provider == "google_veo3"
        assert updated.input_image_path == "/test/image.jpg"
        assert updated.audio_generated is True

        session.close()

    def test_migration_rollback_data_integrity(self, test_engine):
        """Test data integrity during migration rollback."""
        # Create full schema
        Base.metadata.create_all(test_engine)

        Session = sessionmaker(bind=test_engine)
        session = Session()

        # Insert job with provider data
        job_with_provider = VideoJobDB(
            id="rollback-test-1",
            prompt="Rollback test",
            status="succeeded",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            input_image_path="/rollback/image.png",
            audio_generated=True,
        )

        session.add(job_with_provider)
        session.commit()

        # Verify job exists with provider data
        retrieved = session.query(VideoJobDB).filter_by(id="rollback-test-1").first()
        assert retrieved.api_provider == "google_veo3"

        # Simulate rollback (remove provider columns)
        session.close()

        with test_engine.connect() as conn:
            # First get the essential data
            result = conn.execute(
                text("""
                SELECT id, prompt, status, created_at 
                FROM video_jobs WHERE id = 'rollback-test-1'
            """)
            ).fetchone()

            # Remove provider columns
            conn.execute(text("DROP INDEX idx_provider_created"))
            conn.execute(text("DROP INDEX idx_provider_status"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN audio_generated"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN input_image_path"))
            conn.execute(text("ALTER TABLE video_jobs DROP COLUMN api_provider"))
            conn.commit()

            # Verify essential data is preserved
            remaining_result = conn.execute(
                text("""
                SELECT id, prompt, status FROM video_jobs WHERE id = 'rollback-test-1'
            """)
            ).fetchone()

        assert remaining_result is not None
        assert remaining_result[0] == "rollback-test-1"  # id preserved
        assert remaining_result[1] == "Rollback test"  # prompt preserved
        assert remaining_result[2] == "succeeded"  # status preserved

    def test_batch_alter_table_usage(self, test_engine):
        """Test that migration uses batch_alter_table for SQLite compatibility."""
        # This test verifies the migration pattern follows SQLite best practices
        # by using batch operations instead of direct ALTER TABLE statements

        # Create base schema
        Base.metadata.create_all(test_engine)

        # Verify we can simulate the batch alter pattern
        with test_engine.connect() as conn:
            # This simulates what batch_alter_table does internally
            # Execute statements separately for SQLite compatibility
            conn.execute(
                text("CREATE TABLE video_jobs_new AS SELECT * FROM video_jobs")
            )
            conn.execute(text("DROP TABLE video_jobs"))
            conn.execute(text("ALTER TABLE video_jobs_new RENAME TO video_jobs"))
            conn.commit()

        # Verify table still exists and is functional
        inspector = inspect(test_engine)
        tables = inspector.get_table_names()
        assert "video_jobs" in tables
