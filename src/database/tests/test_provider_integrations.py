"""Integration testing framework for F1 database schema extensions.

This module provides comprehensive testing for F1 integration points with
dependent modules (C3, I1, I2) and external services (Azure Sora, Google Veo3).
All tests maintain backward compatibility guarantees and validate performance
requirements for production deployment.
"""

import time
from datetime import datetime
from uuid import uuid4

import pytest

from src.core.models import VideoJob
from src.database.config.provider_config import (
    ProviderDatabaseConfig,
    ProviderDatabaseConfigFactory,
)
from src.database.connection import get_db_session, init_db
from src.database.integrations.provider_integrations import F1ExternalIntegrations
from src.database.interfaces.provider_interface import (
    F1IntegrationManager,
    F1ProviderInterface,
)
from src.database.models import VideoJobDB


class TestF1ProviderInterface:
    """Test F1 provider interface for dependent module integration."""

    @pytest.fixture
    def provider_interface(self):
        """Create provider interface for testing."""
        return F1ProviderInterface()

    @pytest.fixture
    def sample_azure_job(self):
        """Create sample Azure Sora job for testing."""
        return VideoJob(
            id=str(uuid4()),
            prompt="Test Azure Sora video generation",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="azure_sora",
            session_id=f"test_session_{uuid4().hex[:8]}",
        )

    @pytest.fixture
    def sample_veo3_job(self):
        """Create sample Google Veo3 job for testing."""
        return VideoJob(
            id=str(uuid4()),
            prompt="Test Google Veo3 video generation",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            session_id=f"test_session_{uuid4().hex[:8]}",
            input_image_path="/test/image.jpg",
            audio_generated=True,
        )

    def test_c3_integration_readiness(self, provider_interface):
        """Test F1 readiness for C3 Job Queue Extensions integration."""
        readiness = provider_interface.prepare_for_c3_integration()

        # Validate integration readiness
        assert readiness["queue_integration_ready"] is True
        assert readiness["backward_compatibility"] is True
        assert "provider_query_methods" in readiness

        # Validate required methods are available
        required_methods = [
            "get_jobs_by_provider",
            "get_provider_statistics",
            "get_provider_load_balance",
        ]

        for method in required_methods:
            assert method in readiness["provider_query_methods"]

        # Validate supported providers
        assert "azure_sora" in readiness["supported_providers"]
        assert "google_veo3" in readiness["supported_providers"]

        # Validate schema validation results
        if "schema_validation" in readiness:
            schema_validation = readiness["schema_validation"]
            assert schema_validation["api_provider_field"] is True
            assert schema_validation["provider_indexes"] is True

    def test_i1_integration_readiness(self, provider_interface):
        """Test F1 readiness for I1 Real Veo3 API Integration."""
        readiness = provider_interface.prepare_for_i1_integration()

        # Validate Veo3 integration readiness
        assert readiness["veo3_integration_ready"] is True
        assert readiness["backward_compatibility"] is True

        # Validate schema support
        schema_support = readiness["schema_support"]
        assert schema_support["input_image_path_field"] is True
        assert schema_support["audio_generated_field"] is True
        assert schema_support["api_provider_veo3_support"] is True

        # Validate query capabilities
        required_capabilities = [
            "get_veo3_jobs_with_images",
            "get_jobs_by_provider_and_session",
        ]

        for capability in required_capabilities:
            assert capability in readiness["query_capabilities"]

    def test_i2_integration_readiness(self, provider_interface):
        """Test F1 readiness for I2 System Integration."""
        readiness = provider_interface.prepare_for_i2_integration()

        # Validate system integration readiness
        assert readiness["system_integration_ready"] is True
        assert readiness["backward_compatibility"] is True

        # Validate monitoring capabilities
        monitoring = readiness["monitoring_capabilities"]
        assert monitoring["dual_provider_statistics"] is True
        assert monitoring["cross_provider_monitoring"] is True
        assert monitoring["performance_metrics"] is True

        # Validate supported operations
        required_operations = [
            "provider_load_balancing",
            "provider_performance_tracking",
            "cross_provider_reporting",
        ]

        for operation in required_operations:
            assert operation in readiness["supported_operations"]

    def test_provider_validation(self, provider_interface):
        """Test provider validation for supported APIs."""
        # Test Azure Sora validation
        azure_validation = provider_interface.validate_provider_support("azure_sora")
        assert azure_validation["provider_supported"] is True
        assert azure_validation["ready_for_job_creation"] is True
        assert azure_validation["schema_ready"] is True

        # Validate Azure features
        azure_features = azure_validation["supported_features"]
        assert "text_to_video" in azure_features
        assert "status_tracking" in azure_features

        # Test Veo3 validation
        veo3_validation = provider_interface.validate_provider_support("google_veo3")
        assert veo3_validation["provider_supported"] is True
        assert veo3_validation["ready_for_job_creation"] is True

        # Validate Veo3 features
        veo3_features = veo3_validation["supported_features"]
        assert "image_to_video" in veo3_features
        assert "audio_generation" in veo3_features
        assert "base64_image_input" in veo3_features

        # Test invalid provider
        invalid_validation = provider_interface.validate_provider_support(
            "invalid_provider"
        )
        assert invalid_validation["provider_supported"] is False
        assert "error" in invalid_validation

    def test_cross_module_interface_generation(self, provider_interface):
        """Test cross-module interface specification generation."""
        interface_spec = provider_interface.get_cross_module_interface()

        # Validate all required interfaces are present
        required_interfaces = [
            "c3_job_queue_interface",
            "i1_veo3_api_interface",
            "i2_system_integration_interface",
        ]

        for interface in required_interfaces:
            assert interface in interface_spec
            assert interface_spec[interface]["backward_compatibility"] is True

        # Validate shared capabilities
        shared = interface_spec["shared_capabilities"]
        assert shared["provider_validation"] is True
        assert shared["session_aware_queries"] is True
        assert shared["performance_optimized_indexes"] is True

    def test_job_creation_for_providers(
        self, provider_interface, sample_azure_job, sample_veo3_job
    ):
        """Test job creation with provider assignment."""
        # Ensure database is initialized
        init_db()

        # Test Azure Sora job creation
        with get_db_session() as session:
            azure_job_db = provider_interface.create_job_for_provider(
                sample_azure_job, "azure_sora", sample_azure_job.session_id
            )

            assert azure_job_db.api_provider == "azure_sora"
            assert azure_job_db.session_id == sample_azure_job.session_id
            assert azure_job_db.prompt == sample_azure_job.prompt

        # Test Veo3 job creation
        with get_db_session() as session:
            veo3_job_db = provider_interface.create_job_for_provider(
                sample_veo3_job, "google_veo3", sample_veo3_job.session_id
            )

            assert veo3_job_db.api_provider == "google_veo3"
            assert veo3_job_db.input_image_path == sample_veo3_job.input_image_path
            assert veo3_job_db.audio_generated == sample_veo3_job.audio_generated

    def test_provider_load_balancing(self, provider_interface):
        """Test provider load balancing functionality."""
        load_balance = provider_interface.get_provider_load_balance()

        # Validate structure
        assert isinstance(load_balance, dict)
        assert "azure_sora" in load_balance
        assert "google_veo3" in load_balance

        # Validate values are non-negative integers
        for provider, count in load_balance.items():
            assert isinstance(count, int)
            assert count >= 0

    def test_health_check_functionality(self, provider_interface):
        """Test health check status generation."""
        health_status = provider_interface.get_health_check_status()

        # Validate health status structure
        assert "status" in health_status
        assert health_status["status"] in ["healthy", "unhealthy"]

        if health_status["status"] == "healthy":
            assert health_status["database_connectivity"] is True
            assert "response_time_ms" in health_status
            assert "provider_counts" in health_status
            assert "supported_providers" in health_status

            # Validate provider counts
            provider_counts = health_status["provider_counts"]
            assert "azure_sora" in provider_counts
            assert "google_veo3" in provider_counts


class TestProviderDatabaseConfig:
    """Test provider database configuration management."""

    def test_environment_configuration_loading(self):
        """Test environment-based configuration loading."""
        config = ProviderDatabaseConfig.from_environment()

        # Validate basic configuration
        assert isinstance(config.provider_query_timeout, int)
        assert config.provider_query_timeout > 0
        assert isinstance(config.enable_provider_monitoring, bool)
        assert isinstance(config.provider_connection_pool_size, int)
        assert config.provider_connection_pool_size > 0

    def test_configuration_validation(self):
        """Test configuration validation functionality."""
        config = ProviderDatabaseConfig.from_environment()
        validation = config.validate_configuration()

        # Validate validation structure
        assert "valid" in validation
        assert "warnings" in validation
        assert "errors" in validation
        assert "performance_recommendations" in validation

        # Configuration should generally be valid
        assert isinstance(validation["valid"], bool)
        assert isinstance(validation["warnings"], list)
        assert isinstance(validation["errors"], list)

    def test_database_url_enhancement(self):
        """Test database URL enhancement with provider support."""
        config = ProviderDatabaseConfig.from_environment()

        # Test PostgreSQL URL enhancement
        postgres_url = "postgresql://user:pass@localhost/db"
        enhanced_url = config.get_database_url_with_provider_support(postgres_url)

        assert "application_name=sora_poc_dual_provider" in enhanced_url
        assert "connect_timeout" in enhanced_url

        # Test SQLite URL enhancement
        sqlite_url = "sqlite:///test.db"
        enhanced_sqlite = config.get_database_url_with_provider_support(sqlite_url)

        assert "timeout" in enhanced_sqlite

    def test_sqlalchemy_engine_args(self):
        """Test SQLAlchemy engine argument generation."""
        config = ProviderDatabaseConfig.from_environment()
        engine_args = config.get_sqlalchemy_engine_args()

        # Validate required engine arguments
        required_args = ["pool_size", "max_overflow", "pool_pre_ping", "pool_recycle"]
        for arg in required_args:
            assert arg in engine_args

        # Validate values
        assert engine_args["pool_size"] > 0
        assert engine_args["max_overflow"] > 0
        assert engine_args["pool_pre_ping"] is True

    def test_factory_configurations(self):
        """Test configuration factory methods."""
        # Test testing configuration
        test_config = ProviderDatabaseConfigFactory.create_for_testing()
        assert test_config.environment == "testing"
        assert test_config.enable_provider_monitoring is False
        assert test_config.provider_connection_pool_size <= 5

        # Test high performance configuration
        hp_config = ProviderDatabaseConfigFactory.create_high_performance()
        assert hp_config.provider_connection_pool_size >= 25
        assert hp_config.enable_provider_statistics_cache is True
        assert hp_config.optimize_for_provider_queries is True


class TestF1ExternalIntegrations:
    """Test external service integration points."""

    @pytest.fixture
    def external_integrations(self):
        """Create external integrations manager for testing."""
        config = ProviderDatabaseConfigFactory.create_for_testing()
        return F1ExternalIntegrations(config)

    def test_azure_sora_integration_preparation(self, external_integrations):
        """Test Azure Sora integration preparation."""
        integration_status = external_integrations.prepare_for_azure_sora_integration()

        # Validate integration status structure
        assert "provider" in integration_status
        assert integration_status["provider"] == "azure_sora"
        assert "backward_compatibility" in integration_status
        assert integration_status["backward_compatibility"] is True

        # Validate integration features
        if "integration_features" in integration_status:
            features = integration_status["integration_features"]
            assert features["text_to_video"] is True
            assert features["status_tracking"] is True
            assert features["session_isolation"] is True

    def test_veo3_integration_preparation(self, external_integrations):
        """Test Google Veo3 integration preparation."""
        integration_status = external_integrations.prepare_for_veo3_integration()

        # Validate integration status
        assert "provider" in integration_status
        assert integration_status["provider"] == "google_veo3"
        assert "veo3_schema_version" in integration_status

        # Validate Veo3 features if preparation successful
        if (
            integration_status.get("integration_status")
            == "ready_for_veo3_module_development"
        ):
            features = integration_status["integration_features"]
            assert features["image_to_video"] is True
            assert features["audio_generation"] is True
            assert features["base64_image_input"] is True

    def test_cross_module_interface_specification(self, external_integrations):
        """Test cross-module interface specification generation."""
        interface_spec = external_integrations.get_cross_module_interface()

        # Should not contain error if successful
        if "error" not in interface_spec:
            # Validate interface specifications
            assert "c3_job_queue_interface" in interface_spec
            assert "i1_veo3_api_interface" in interface_spec
            assert "i2_system_integration_interface" in interface_spec

            # Validate shared capabilities
            assert "shared_capabilities" in interface_spec
            assert "database_specifications" in interface_spec

            # Validate version information
            assert "interface_version" in interface_spec
            assert interface_spec["interface_version"] == "F1_v1.0"

    def test_integration_dependency_validation(self, external_integrations):
        """Test comprehensive integration dependency validation."""
        validation_results = external_integrations.validate_integration_dependencies()

        # Validate results structure
        assert "overall_status" in validation_results
        assert "validations" in validation_results
        assert "dependencies" in validation_results
        assert "validation_timestamp" in validation_results

        # Status should be determinable
        assert validation_results["overall_status"] in [
            "ready",
            "issues_found",
            "failed",
            "validating",
        ]

        # Should have validation results for key components
        validations = validation_results["validations"]
        expected_validations = ["azure_sora", "google_veo3", "database_config"]

        for validation_key in expected_validations:
            if validation_key in validations:
                # If validation exists, it should have backward compatibility
                assert (
                    validations[validation_key].get("backward_compatibility", True)
                    is True
                )

    def test_integration_health_status(self, external_integrations):
        """Test integration health status generation."""
        health_status = external_integrations.get_integration_health_status()

        # Validate health status structure
        assert "overall_health" in health_status
        assert health_status["overall_health"] in [
            "healthy",
            "degraded",
            "unhealthy",
            "checking",
        ]

        if health_status["overall_health"] != "unhealthy":
            # Validate health components
            assert "service_integrations" in health_status
            assert "module_interfaces" in health_status
            assert "performance_metrics" in health_status


class TestF1IntegrationManager:
    """Test integration manager coordination functionality."""

    @pytest.fixture
    def integration_manager(self):
        """Create integration manager for testing."""
        return F1IntegrationManager()

    def test_all_integrations_validation(self, integration_manager):
        """Test validation of all dependent module integrations."""
        validation_results = integration_manager.validate_all_integrations()

        # Validate results structure
        assert "c3_integration" in validation_results
        assert "i1_integration" in validation_results
        assert "i2_integration" in validation_results
        assert "cross_module_interface" in validation_results
        assert "overall_status" in validation_results

        # Overall status should be determinable
        assert validation_results["overall_status"] in ["ready", "issues_found"]

        # All integrations should maintain backward compatibility
        for integration_key in ["c3_integration", "i1_integration", "i2_integration"]:
            integration = validation_results[integration_key]
            assert integration.get("backward_compatibility", True) is True

    def test_integration_summary_generation(self, integration_manager):
        """Test comprehensive integration summary generation."""
        summary = integration_manager.get_integration_summary()

        # Validate summary structure
        expected_keys = [
            "integration_validations",
            "health_status",
            "load_balance",
            "supported_providers",
            "backward_compatibility",
        ]

        for key in expected_keys:
            assert key in summary

        # Validate backward compatibility guarantee
        assert summary["backward_compatibility"] == "guaranteed"

        # Validate supported providers
        assert "azure_sora" in summary["supported_providers"]
        assert "google_veo3" in summary["supported_providers"]


class TestPerformanceIntegrations:
    """Test performance aspects of F1 integrations."""

    def test_provider_query_performance(self):
        """Test provider query performance meets requirements."""
        provider_interface = F1ProviderInterface()

        # Test provider statistics query performance
        start_time = time.time()
        with get_db_session() as session:
            stats = provider_interface.query_helper.get_provider_statistics(
                session=session
            )
        end_time = time.time()

        query_time_ms = (end_time - start_time) * 1000

        # Should complete within reasonable time (< 500ms for test environment)
        assert query_time_ms < 500
        assert isinstance(stats, dict)

    def test_load_balance_query_performance(self):
        """Test load balance query performance."""
        provider_interface = F1ProviderInterface()

        start_time = time.time()
        load_balance = provider_interface.get_provider_load_balance()
        end_time = time.time()

        query_time_ms = (end_time - start_time) * 1000

        # Should complete quickly (< 200ms)
        assert query_time_ms < 200
        assert isinstance(load_balance, dict)
        assert len(load_balance) >= 2  # At least azure_sora and google_veo3

    def test_health_check_performance(self):
        """Test health check performance requirements."""
        provider_interface = F1ProviderInterface()

        start_time = time.time()
        health_status = provider_interface.get_health_check_status()
        end_time = time.time()

        check_time_ms = (end_time - start_time) * 1000

        # Health check should be fast (< 1000ms)
        assert check_time_ms < 1000

        # Should include response time in results
        if health_status["status"] == "healthy":
            assert "response_time_ms" in health_status
            # Response time should be reasonable
            assert health_status["response_time_ms"] < 1000


class TestBackwardCompatibility:
    """Test backward compatibility guarantees."""

    def test_existing_queries_still_work(self):
        """Test that existing database queries continue to work."""
        # Ensure database is initialized
        init_db()

        # Test basic job queries (existing functionality)
        with get_db_session() as session:
            # This query should work exactly as before F1 implementation
            jobs = session.query(VideoJobDB).filter_by(status="pending").all()
            assert isinstance(jobs, list)

            # Provider-specific queries should also work
            azure_jobs = (
                session.query(VideoJobDB).filter_by(api_provider="azure_sora").all()
            )
            assert isinstance(azure_jobs, list)

    def test_existing_job_creation_patterns(self):
        """Test that existing job creation patterns still work."""
        # Create job using existing pattern
        job = VideoJob(
            id=str(uuid4()),
            prompt="Test existing pattern",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="azure_sora",  # Default provider
        )

        job_db = VideoJobDB.from_pydantic(job)

        # Should create successfully with default provider
        assert job_db.api_provider == "azure_sora"
        assert job_db.prompt == job.prompt
        assert job_db.status == job.status

    def test_pydantic_model_compatibility(self):
        """Test Pydantic model conversion compatibility."""
        # Create database record
        job_db = VideoJobDB(
            id=str(uuid4()),
            prompt="Test compatibility",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="azure_sora",
        )

        # Convert to Pydantic (existing functionality)
        pydantic_job = job_db.to_pydantic()

        # Should maintain all fields
        assert pydantic_job.id == job_db.id
        assert pydantic_job.prompt == job_db.prompt
        assert pydantic_job.status == job_db.status
        assert pydantic_job.api_provider == job_db.api_provider

        # Convert back to database model
        job_db_2 = VideoJobDB.from_pydantic(pydantic_job)

        # Should maintain consistency
        assert job_db_2.api_provider == job_db.api_provider
        assert job_db_2.prompt == job_db.prompt


# Test configuration for pytest
def pytest_configure(config):
    """Configure pytest for F1 integration testing."""
    # Add custom markers
    config.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="run integration tests",
    )
    config.addoption(
        "--performance",
        action="store_true",
        default=False,
        help="run performance tests",
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection based on options."""
    if config.getoption("--integration"):
        # Run all tests when integration flag is set
        return

    if config.getoption("--performance"):
        # Only run performance tests
        performance_tests = [
            item for item in items if "performance" in item.name.lower()
        ]
        items[:] = performance_tests
        return

    # Skip slow tests by default
    skip_integration = pytest.mark.skip(reason="need --integration option to run")
    for item in items:
        if "integration" in item.keywords or "slow" in item.keywords:
            item.add_marker(skip_integration)
