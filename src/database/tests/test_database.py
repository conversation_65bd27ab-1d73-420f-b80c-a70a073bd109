"""Tests for database functionality."""

import os
import tempfile
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError, SQLAlchemyError
from sqlalchemy.orm import sessionmaker

from src.core.models import VideoJob
from src.database.connection import (
    DatabaseManager,
    get_db_manager,
    get_db_session,
    init_db,
)
from src.database.models import Base, VideoJobDB


@pytest.mark.integration
class TestDatabaseModels:
    """Test database model functionality."""

    @pytest.fixture
    def test_engine(self):
        """Create test database engine."""
        # Use in-memory SQLite for testing
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture
    def test_session(self, test_engine):
        """Create test database session."""
        Session = sessionmaker(bind=test_engine)
        session = Session()
        yield session
        session.close()

    @pytest.fixture
    def sample_video_job(self):
        """Create sample VideoJob for testing."""
        return VideoJob(
            id="test-job-123",
            prompt="A cat playing piano",
            status="pending",
            created_at=datetime.now(),
        )

    def test_video_job_db_creation(self, test_session, sample_video_job):
        """Test creating VideoJobDB from Pydantic model."""
        # Create ORM model from Pydantic
        job_db = VideoJobDB.from_pydantic(sample_video_job)

        # Save to database
        test_session.add(job_db)
        test_session.commit()

        # Verify saved correctly
        saved_job = test_session.query(VideoJobDB).filter_by(id="test-job-123").first()
        assert saved_job is not None
        assert saved_job.prompt == "A cat playing piano"
        assert saved_job.status == "pending"

    def test_video_job_db_to_pydantic(self, test_session, sample_video_job):
        """Test converting ORM model back to Pydantic."""
        # Create and save ORM model
        job_db = VideoJobDB.from_pydantic(sample_video_job)
        test_session.add(job_db)
        test_session.commit()

        # Convert back to Pydantic
        retrieved_job = (
            test_session.query(VideoJobDB).filter_by(id="test-job-123").first()
        )
        pydantic_job = retrieved_job.to_pydantic()

        # Verify conversion
        assert pydantic_job.id == sample_video_job.id
        assert pydantic_job.prompt == sample_video_job.prompt
        assert pydantic_job.status == sample_video_job.status

    def test_video_job_db_update(self, test_session, sample_video_job):
        """Test updating ORM model from Pydantic."""
        # Create and save initial job
        job_db = VideoJobDB.from_pydantic(sample_video_job)
        test_session.add(job_db)
        test_session.commit()

        # Update the Pydantic model
        sample_video_job.status = "succeeded"
        sample_video_job.completed_at = datetime.now()
        sample_video_job.file_path = "/path/to/video.mp4"

        # Update ORM model
        job_db.update_from_pydantic(sample_video_job)
        test_session.commit()

        # Verify update
        updated_job = (
            test_session.query(VideoJobDB).filter_by(id="test-job-123").first()
        )
        assert updated_job.status == "succeeded"
        assert updated_job.completed_at is not None
        assert updated_job.file_path == "/path/to/video.mp4"

    def test_video_job_db_repr(self, sample_video_job):
        """Test string representation."""
        job_db = VideoJobDB.from_pydantic(sample_video_job)
        repr_str = repr(job_db)

        assert "VideoJobDB" in repr_str
        assert "test-job-123" in repr_str
        assert "pending" in repr_str


@pytest.mark.integration
class TestDatabaseConnection:
    """Test database connection management."""

    def test_database_manager_initialization(self):
        """Test DatabaseManager initialization."""
        from src.database.connection import DatabaseManager

        # Test with default SQLite
        manager = DatabaseManager()
        assert manager.database_url is not None
        assert "sqlite" in manager.database_url

    def test_database_manager_with_custom_url(self):
        """Test DatabaseManager with custom database URL."""
        from src.database.connection import DatabaseManager

        # Set custom URL
        custom_url = "sqlite:///test_custom.db"
        os.environ["DATABASE_URL"] = custom_url

        try:
            manager = DatabaseManager()
            assert manager.database_url == custom_url
        finally:
            # Clean up environment
            if "DATABASE_URL" in os.environ:
                del os.environ["DATABASE_URL"]

    def test_database_session_context_manager(self):
        """Test database session context manager."""
        from src.database.connection import DatabaseManager

        manager = DatabaseManager()
        manager.init_db()

        # Test successful transaction
        with manager.get_session() as session:
            job_db = VideoJobDB(
                id="test-context-job",
                prompt="Test prompt",
                status="pending",
                created_at=datetime.now(),
            )
            session.add(job_db)

        # Verify job was saved
        with manager.get_session() as session:
            saved_job = (
                session.query(VideoJobDB).filter_by(id="test-context-job").first()
            )
            assert saved_job is not None

    def test_database_session_rollback_on_error(self):
        """Test database session rollback on error."""
        from src.database.connection import DatabaseManager

        manager = DatabaseManager()
        manager.init_db()

        # Test rollback on error
        with pytest.raises(ValueError):
            with manager.get_session() as session:
                job_db = VideoJobDB(
                    id="test-rollback-job",
                    prompt="Test prompt",
                    status="pending",
                    created_at=datetime.now(),
                )
                session.add(job_db)
                # Force an error
                raise ValueError("Test error")

        # Verify job was not saved due to rollback
        with manager.get_session() as session:
            saved_job = (
                session.query(VideoJobDB).filter_by(id="test-rollback-job").first()
            )
            assert saved_job is None


@pytest.mark.unit
class TestDatabaseManagerEdgeCases:
    """Test DatabaseManager edge cases and error scenarios."""

    def test_database_manager_with_invalid_url(self):
        """Test DatabaseManager with invalid database URL."""
        with patch.dict(os.environ, {"DATABASE_URL": "invalid://malformed-url"}):
            with patch("src.config.factory.ConfigurationFactory"):
                # Should still create manager but engine creation might fail
                with pytest.raises(Exception):  # SQLAlchemy will raise on invalid URL
                    manager = DatabaseManager()
                    # Try to use the engine
                    manager.init_db()

    def test_database_manager_missing_config(self):
        """Test DatabaseManager when configuration is missing."""
        mock_config = MagicMock()
        mock_config.DATABASE_URL = None
        mock_config.SQL_DEBUG = False

        with patch("src.config.factory.ConfigurationFactory") as mock_factory:
            mock_factory.get_base_config.return_value = mock_config

            manager = DatabaseManager()
            # Should fall back to SQLite default
            assert "sqlite:///sora_poc.db" in manager.database_url

    def test_database_manager_postgresql_config(self):
        """Test DatabaseManager with PostgreSQL configuration."""
        postgres_url = "postgresql://user:pass@localhost:5432/testdb"

        with patch.dict(os.environ, {"DATABASE_URL": postgres_url}):
            with patch("src.config.factory.ConfigurationFactory") as mock_factory:
                mock_config = MagicMock()
                mock_config.DATABASE_URL = postgres_url
                mock_config.SQL_DEBUG = True
                mock_factory.get_base_config.return_value = mock_config

                # Mock create_engine to avoid actual PostgreSQL connection
                with patch(
                    "src.database.connection.create_engine"
                ) as mock_create_engine:
                    mock_engine = MagicMock()
                    mock_create_engine.return_value = mock_engine

                    manager = DatabaseManager()

                    # Verify PostgreSQL-specific engine args were used
                    mock_create_engine.assert_called_once()
                    call_args = mock_create_engine.call_args
                    engine_args = call_args[1]

                    assert "pool_size" in engine_args
                    assert "max_overflow" in engine_args
                    assert "pool_pre_ping" in engine_args
                    assert engine_args["echo"] is True  # SQL_DEBUG = True

    def test_database_manager_sqlite_config(self):
        """Test DatabaseManager with SQLite configuration (no pooling)."""
        sqlite_url = "sqlite:///test.db"

        with patch.dict(os.environ, {"DATABASE_URL": sqlite_url}):
            with patch("src.config.factory.ConfigurationFactory") as mock_factory:
                mock_config = MagicMock()
                mock_config.DATABASE_URL = sqlite_url
                mock_config.SQL_DEBUG = False
                mock_factory.get_base_config.return_value = mock_config

                with patch(
                    "src.database.connection.create_engine"
                ) as mock_create_engine:
                    mock_engine = MagicMock()
                    mock_create_engine.return_value = mock_engine

                    manager = DatabaseManager()

                    # Verify SQLite doesn't get pooling options
                    call_args = mock_create_engine.call_args
                    engine_args = call_args[1]

                    assert "pool_size" not in engine_args
                    assert "max_overflow" not in engine_args
                    assert "pool_pre_ping" not in engine_args
                    assert engine_args["echo"] is False

    def test_database_session_commit_error(self):
        """Test database session handling when commit fails."""
        manager = DatabaseManager()
        manager.init_db()

        with patch.object(manager, "session_factory") as mock_factory:
            mock_session = MagicMock()
            mock_session.commit.side_effect = SQLAlchemyError("Commit failed")
            mock_factory.return_value = mock_session

            # Should raise exception and call rollback
            with pytest.raises(SQLAlchemyError):
                with manager.get_session() as session:
                    session.add(MagicMock())

            # Verify rollback was called
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()

    def test_database_init_db_error(self):
        """Test init_db when database operations fail."""
        manager = DatabaseManager()

        with patch("src.database.models.Base") as mock_base:
            mock_base.metadata.create_all.side_effect = OperationalError(
                "Database error", None, None
            )

            # init_db should propagate the error
            with pytest.raises(OperationalError):
                manager.init_db()

    def test_database_session_close_error(self):
        """Test session context manager when close fails."""
        manager = DatabaseManager()
        manager.init_db()

        with patch.object(manager, "session_factory") as mock_factory:
            mock_session = MagicMock()
            mock_session.close.side_effect = Exception("Close failed")
            mock_factory.return_value = mock_session

            # Should still complete the context manager even if close fails
            # The exception will be raised but not break the test
            try:
                with manager.get_session() as session:
                    pass  # Do nothing, just test context manager
            except Exception as e:
                assert "Close failed" in str(e)

            # Verify close was attempted
            mock_session.close.assert_called_once()


@pytest.mark.unit
class TestDatabaseGlobalFunctions:
    """Test module-level database functions."""

    def test_get_db_manager_singleton(self):
        """Test get_db_manager returns singleton instance."""
        # Clear any existing global instance
        import src.database.connection as db_conn

        db_conn._db_manager = None

        # First call should create instance
        manager1 = get_db_manager()
        assert manager1 is not None

        # Second call should return same instance
        manager2 = get_db_manager()
        assert manager1 is manager2

    def test_get_db_manager_recreate_after_clear(self):
        """Test get_db_manager creates new instance after clearing global."""
        import src.database.connection as db_conn

        # Get initial instance
        manager1 = get_db_manager()

        # Clear global instance
        db_conn._db_manager = None

        # Should create new instance
        manager2 = get_db_manager()
        assert manager1 is not manager2
        assert manager2 is not None

    def test_get_db_session_context_manager(self):
        """Test get_db_session returns proper context manager."""
        with patch("src.database.connection.get_db_manager") as mock_get_manager:
            mock_manager = MagicMock()
            mock_context = MagicMock()
            mock_manager.get_session.return_value = mock_context
            mock_get_manager.return_value = mock_manager

            # Test that get_db_session returns manager's context
            result = get_db_session()
            assert result is mock_context
            mock_manager.get_session.assert_called_once()

    def test_init_db_function(self):
        """Test module-level init_db function."""
        with patch("src.database.connection.get_db_manager") as mock_get_manager:
            mock_manager = MagicMock()
            mock_get_manager.return_value = mock_manager

            # Test init_db calls manager's init_db
            init_db()

            mock_get_manager.assert_called_once()
            mock_manager.init_db.assert_called_once()

    def test_get_db_manager_with_config_error(self):
        """Test get_db_manager when configuration fails."""
        import src.database.connection as db_conn

        db_conn._db_manager = None

        with patch("src.database.connection.DatabaseManager") as mock_db_manager:
            mock_db_manager.side_effect = Exception("Config error")

            # Should propagate configuration errors
            with pytest.raises(Exception, match="Config error"):
                get_db_manager()


@pytest.mark.integration
class TestDatabaseIntegrationEdgeCases:
    """Integration tests for database edge cases."""

    def test_database_with_temporary_file(self):
        """Test database operations with temporary SQLite file."""
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as temp_db:
            temp_db_path = temp_db.name

        try:
            # Test with temporary database file
            temp_url = f"sqlite:///{temp_db_path}"

            with patch.dict(os.environ, {"DATABASE_URL": temp_url}):
                # Clear singleton to force new instance
                import src.database.connection as db_conn

                db_conn._db_manager = None

                manager = get_db_manager()
                manager.init_db()

                # Test database operations
                test_job = VideoJob(
                    id="temp-test-job",
                    prompt="Temporary test",
                    status="pending",
                    created_at=datetime.now(),
                )

                with manager.get_session() as session:
                    job_db = VideoJobDB.from_pydantic(test_job)
                    session.add(job_db)

                # Verify job was saved
                with manager.get_session() as session:
                    saved_job = (
                        session.query(VideoJobDB).filter_by(id="temp-test-job").first()
                    )
                    assert saved_job is not None
                    assert saved_job.prompt == "Temporary test"

        finally:
            # Clean up temporary file
            if os.path.exists(temp_db_path):
                os.unlink(temp_db_path)

    def test_database_concurrent_access_simulation(self):
        """Test database manager with simulated concurrent access."""
        manager = DatabaseManager()
        manager.init_db()

        # Simulate multiple "concurrent" sessions
        sessions_data = []

        for i in range(5):
            with manager.get_session() as session:
                test_job = VideoJob(
                    id=f"concurrent-job-{i}",
                    prompt=f"Concurrent test {i}",
                    status="pending",
                    created_at=datetime.now(),
                )

                job_db = VideoJobDB.from_pydantic(test_job)
                session.add(job_db)
                sessions_data.append(job_db.id)

        # Verify all jobs were saved
        with manager.get_session() as session:
            for job_id in sessions_data:
                saved_job = session.query(VideoJobDB).filter_by(id=job_id).first()
                assert saved_job is not None

    def test_database_large_data_handling(self):
        """Test database handling with larger data within validation limits."""
        manager = DatabaseManager()
        manager.init_db()

        # Create job with maximum allowed prompt (within validation limits)
        large_prompt = "A" * 500  # Max allowed by validation
        large_job = VideoJob(
            id="large-data-job",
            prompt=large_prompt,
            status="pending",
            created_at=datetime.now(),
        )

        with manager.get_session() as session:
            job_db = VideoJobDB.from_pydantic(large_job)
            session.add(job_db)

        # Verify large data was saved and retrieved correctly
        with manager.get_session() as session:
            saved_job = session.query(VideoJobDB).filter_by(id="large-data-job").first()
            assert saved_job is not None
            assert len(saved_job.prompt) == 500
            assert saved_job.prompt == large_prompt

    def test_database_connection_recovery(self):
        """Test database connection recovery after errors."""
        manager = DatabaseManager()
        manager.init_db()

        # First, successful operation
        with manager.get_session() as session:
            test_job = VideoJob(
                id="recovery-test-job",
                prompt="Recovery test",
                status="pending",
                created_at=datetime.now(),
            )
            job_db = VideoJobDB.from_pydantic(test_job)
            session.add(job_db)

        # Simulate connection issue and recovery
        try:
            with manager.get_session() as session:
                # Force an error
                session.execute(text("INVALID SQL STATEMENT"))
        except Exception:
            pass  # Expected to fail

        # Should be able to recover and perform operations
        with manager.get_session() as session:
            recovery_job = VideoJob(
                id="post-recovery-job",
                prompt="Post recovery test",
                status="pending",
                created_at=datetime.now(),
            )
            job_db = VideoJobDB.from_pydantic(recovery_job)
            session.add(job_db)

        # Verify both jobs exist
        with manager.get_session() as session:
            job1 = session.query(VideoJobDB).filter_by(id="recovery-test-job").first()
            job2 = session.query(VideoJobDB).filter_by(id="post-recovery-job").first()
            assert job1 is not None
            assert job2 is not None


@pytest.mark.performance
class TestDatabasePerformance:
    """Performance and stress tests for database operations."""

    def test_database_session_cleanup_performance(self):
        """Test that database sessions are properly cleaned up."""
        manager = DatabaseManager()
        manager.init_db()

        # Track initial connection count (if possible)
        initial_connections = getattr(manager.engine.pool, "checkedout", 0)

        # Create many sessions to test cleanup
        for i in range(20):
            with manager.get_session() as session:
                # Just open and close sessions
                pass

        # All sessions should be cleaned up
        final_connections = getattr(manager.engine.pool, "checkedout", 0)
        assert final_connections == initial_connections

    def test_database_bulk_operations(self):
        """Test database performance with bulk operations."""
        manager = DatabaseManager()
        manager.init_db()

        # Bulk insert test
        jobs_to_insert = []
        for i in range(100):
            test_job = VideoJob(
                id=f"bulk-job-{i}",
                prompt=f"Bulk test job {i}",
                status="pending",
                created_at=datetime.now(),
            )
            jobs_to_insert.append(VideoJobDB.from_pydantic(test_job))

        # Insert all jobs in one transaction
        with manager.get_session() as session:
            session.add_all(jobs_to_insert)

        # Verify all jobs were inserted
        with manager.get_session() as session:
            count = (
                session.query(VideoJobDB)
                .filter(VideoJobDB.id.like("bulk-job-%"))
                .count()
            )
            assert count == 100

        # Bulk query test
        with manager.get_session() as session:
            all_bulk_jobs = (
                session.query(VideoJobDB).filter(VideoJobDB.id.like("bulk-job-%")).all()
            )
            assert len(all_bulk_jobs) == 100
