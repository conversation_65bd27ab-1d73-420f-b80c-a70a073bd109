"""Comprehensive tests for provider-aware database queries."""

import time
import uuid
from datetime import datetime, timedelta
from unittest.mock import patch

import pytest

from src.database.connection import get_db_session
from src.database.models import VideoJobDB
from src.database.queries.performance_helpers import PerformanceQueryHelper
from src.database.queries.provider_queries import Provider<PERSON>ueryHelper


@pytest.mark.f1_database
class TestProviderQueries:
    """Comprehensive tests for provider-aware database queries."""

    @pytest.fixture
    def sample_jobs(self):
        """Create sample jobs for testing."""
        jobs = []
        with get_db_session() as session:
            # Create Azure Sora jobs
            for i in range(3):
                job = VideoJobDB(
                    id=str(uuid.uuid4()),
                    prompt=f"Azure test prompt {i}",
                    status="succeeded" if i % 2 == 0 else "pending",
                    api_provider="azure_sora",
                    session_id="test-session-azure",
                    created_at=datetime.utcnow(),
                )
                session.add(job)
                jobs.append(job)

            # Create Google Veo3 jobs
            for i in range(2):
                job = VideoJobDB(
                    id=str(uuid.uuid4()),
                    prompt=f"Veo3 test prompt {i}",
                    status="running" if i == 0 else "failed",
                    api_provider="google_veo3",
                    input_image_path=f"/test/image/{i}.jpg",
                    audio_generated=True,
                    session_id="test-session-veo3",
                    created_at=datetime.utcnow(),
                )
                session.add(job)
                jobs.append(job)

            session.commit()
            yield jobs

            # Cleanup
            for job in jobs:
                session.delete(job)
            session.commit()

    def test_get_jobs_by_provider(self, sample_jobs):
        """Test provider filtering functionality."""
        # Test Azure Sora filtering
        azure_jobs = ProviderQueryHelper.get_jobs_by_provider("azure_sora")
        assert len(azure_jobs) >= 3  # At least our test jobs

        # Filter to only our test jobs
        test_azure_jobs = [
            job for job in azure_jobs if job.session_id == "test-session-azure"
        ]
        assert len(test_azure_jobs) == 3
        assert all(job.api_provider == "azure_sora" for job in test_azure_jobs)

        # Test Google Veo3 filtering
        veo3_jobs = ProviderQueryHelper.get_jobs_by_provider("google_veo3")
        test_veo3_jobs = [
            job for job in veo3_jobs if job.session_id == "test-session-veo3"
        ]
        assert len(test_veo3_jobs) == 2
        assert all(job.api_provider == "google_veo3" for job in test_veo3_jobs)
        assert all(job.input_image_path is not None for job in test_veo3_jobs)

    def test_get_jobs_by_provider_with_status(self, sample_jobs):
        """Test provider filtering with status filter."""
        # Test Azure Sora succeeded jobs
        azure_succeeded = ProviderQueryHelper.get_jobs_by_provider(
            "azure_sora", "succeeded"
        )
        test_azure_succeeded = [
            job for job in azure_succeeded if job.session_id == "test-session-azure"
        ]
        assert len(test_azure_succeeded) == 2  # Even numbered jobs
        assert all(job.status == "succeeded" for job in test_azure_succeeded)

        # Test Azure Sora pending jobs
        azure_pending = ProviderQueryHelper.get_jobs_by_provider(
            "azure_sora", "pending"
        )
        test_azure_pending = [
            job for job in azure_pending if job.session_id == "test-session-azure"
        ]
        assert len(test_azure_pending) == 1  # Odd numbered job
        assert all(job.status == "pending" for job in test_azure_pending)

    def test_get_jobs_by_provider_with_limit(self, sample_jobs):
        """Test provider filtering with limit."""
        azure_jobs = ProviderQueryHelper.get_jobs_by_provider("azure_sora", limit=2)
        assert len(azure_jobs) <= 2
        assert all(job.api_provider == "azure_sora" for job in azure_jobs)

    def test_provider_statistics(self, sample_jobs):
        """Test provider statistics generation."""
        stats = ProviderQueryHelper.get_provider_statistics()

        assert "azure_sora" in stats
        assert "google_veo3" in stats

        # Validate Azure stats (at least our test data)
        azure_stats = stats["azure_sora"]
        assert azure_stats["total"] >= 3
        assert azure_stats["succeeded"] >= 2  # Even numbered jobs
        assert azure_stats["pending"] >= 1  # Odd numbered jobs

        # Validate Veo3 stats
        veo3_stats = stats["google_veo3"]
        assert veo3_stats["total"] >= 2
        assert veo3_stats["running"] >= 1
        assert veo3_stats["failed"] >= 1

    def test_get_jobs_by_provider_and_session(self, sample_jobs):
        """Test provider and session filtering."""
        # Test Azure jobs for specific session
        azure_session_jobs = ProviderQueryHelper.get_jobs_by_provider_and_session(
            "azure_sora", "test-session-azure"
        )
        assert len(azure_session_jobs) == 3
        assert all(job.session_id == "test-session-azure" for job in azure_session_jobs)
        assert all(job.api_provider == "azure_sora" for job in azure_session_jobs)

        # Test Veo3 jobs for specific session
        veo3_session_jobs = ProviderQueryHelper.get_jobs_by_provider_and_session(
            "google_veo3", "test-session-veo3"
        )
        assert len(veo3_session_jobs) == 2
        assert all(job.session_id == "test-session-veo3" for job in veo3_session_jobs)
        assert all(job.api_provider == "google_veo3" for job in veo3_session_jobs)

    def test_get_veo3_jobs_with_images(self, sample_jobs):
        """Test Veo3 jobs with image filtering."""
        veo3_with_images = ProviderQueryHelper.get_veo3_jobs_with_images()
        test_veo3_with_images = [
            job for job in veo3_with_images if job.session_id == "test-session-veo3"
        ]

        assert len(test_veo3_with_images) == 2
        assert all(job.api_provider == "google_veo3" for job in test_veo3_with_images)
        assert all(job.input_image_path is not None for job in test_veo3_with_images)

    def test_get_recent_jobs_by_provider(self, sample_jobs):
        """Test recent jobs filtering."""
        # Test recent Azure jobs (within 24 hours)
        recent_azure = ProviderQueryHelper.get_recent_jobs_by_provider(
            "azure_sora", hours=24
        )
        test_recent_azure = [
            job for job in recent_azure if job.session_id == "test-session-azure"
        ]

        assert len(test_recent_azure) == 3  # All our test jobs are recent
        assert all(job.api_provider == "azure_sora" for job in test_recent_azure)

        # Test recent jobs with short window (should have no results for old data)
        recent_short = ProviderQueryHelper.get_recent_jobs_by_provider(
            "azure_sora", hours=0
        )
        assert len(recent_short) == 0

    def test_provider_query_session_management(self, sample_jobs):
        """Test queries work with session management."""
        with get_db_session() as session:
            # Test using session parameter
            azure_jobs = ProviderQueryHelper.get_jobs_by_provider(
                "azure_sora", session=session
            )
            test_azure_jobs = [
                job for job in azure_jobs if job.session_id == "test-session-azure"
            ]
            assert len(test_azure_jobs) == 3

            # Test statistics with session
            stats = ProviderQueryHelper.get_provider_statistics(session=session)
            assert "azure_sora" in stats
            assert "google_veo3" in stats


@pytest.mark.performance
class TestProviderQueryPerformance:
    """Test provider query performance requirements."""

    @pytest.fixture
    def performance_test_data(self):
        """Create performance test data."""
        jobs = []
        with get_db_session() as session:
            # Create sufficient test data for performance testing
            for i in range(50):
                provider = "azure_sora" if i % 2 == 0 else "google_veo3"
                status = ["pending", "running", "succeeded", "failed"][i % 4]

                job = VideoJobDB(
                    id=f"perf-test-{i}-{uuid.uuid4()}",
                    prompt=f"Performance test job {i}",
                    status=status,
                    api_provider=provider,
                    session_id=f"perf-session-{i % 5}",
                    created_at=datetime.utcnow() - timedelta(minutes=i),
                    input_image_path=f"/test/image/{i}.jpg"
                    if provider == "google_veo3"
                    else None,
                    audio_generated=i % 3 == 0,
                )
                session.add(job)
                jobs.append(job)

            session.commit()
            yield jobs

            # Cleanup
            for job in jobs:
                session.delete(job)
            session.commit()

    def test_query_performance_benchmarks(self, performance_test_data):
        """Test that provider queries meet performance requirements."""
        # Benchmark provider filtering
        start_time = time.perf_counter()
        azure_jobs = ProviderQueryHelper.get_jobs_by_provider(
            "azure_sora", "succeeded", limit=50
        )
        query_time = time.perf_counter() - start_time

        # Must be <100ms (0.1 seconds)
        assert query_time < 0.1, (
            f"Provider query took {query_time * 1000:.2f}ms, exceeds 100ms limit"
        )

        # Benchmark statistics generation
        start_time = time.perf_counter()
        stats = ProviderQueryHelper.get_provider_statistics()
        stats_time = time.perf_counter() - start_time

        # Must be <150ms (0.15 seconds)
        assert stats_time < 0.15, (
            f"Statistics query took {stats_time * 1000:.2f}ms, exceeds 150ms limit"
        )

        # Benchmark combined provider and session query
        start_time = time.perf_counter()
        session_jobs = ProviderQueryHelper.get_jobs_by_provider_and_session(
            "azure_sora", "perf-session-1"
        )
        session_query_time = time.perf_counter() - start_time

        assert session_query_time < 0.1, (
            f"Session query took {session_query_time * 1000:.2f}ms, exceeds 100ms limit"
        )

    def test_performance_helper_benchmarks(self, performance_test_data):
        """Test PerformanceQueryHelper benchmarks."""
        benchmarks = PerformanceQueryHelper.benchmark_provider_queries()

        # Validate benchmark structure
        assert "provider_filter_ms" in benchmarks
        assert "provider_stats_ms" in benchmarks
        assert "combined_query_ms" in benchmarks
        assert "performance_target_met" in benchmarks
        assert "test_metadata" in benchmarks

        # Validate performance requirements
        assert benchmarks["provider_filter_ms"] < 100, (
            f"Provider filter took {benchmarks['provider_filter_ms']:.2f}ms"
        )
        assert benchmarks["provider_stats_ms"] < 150, (
            f"Provider stats took {benchmarks['provider_stats_ms']:.2f}ms"
        )
        assert benchmarks["combined_query_ms"] < 100, (
            f"Combined query took {benchmarks['combined_query_ms']:.2f}ms"
        )

        # Overall performance target should be met
        assert benchmarks["performance_target_met"] is True, (
            "Performance targets not met"
        )

        # Validate metadata
        metadata = benchmarks["test_metadata"]
        assert metadata["total_jobs"] >= 50
        assert metadata["azure_sora_count"] >= 25
        assert metadata["google_veo3_count"] >= 25

    def test_index_utilization_validation(self, performance_test_data):
        """Test that provider queries utilize proper indexes."""
        helper = PerformanceQueryHelper()

        # Test provider filtering query
        query_sql = """
            SELECT * FROM video_jobs 
            WHERE api_provider = 'azure_sora' AND status = 'succeeded'
            ORDER BY created_at DESC
        """

        index_validation = helper.validate_index_usage(query_sql)

        # Validate index analysis structure
        assert "uses_index" in index_validation
        assert "index_names" in index_validation
        assert "scan_type" in index_validation
        assert "performance_grade" in index_validation
        assert "explanation" in index_validation

        # Should achieve reasonable performance grade
        assert index_validation["performance_grade"] in ["A", "B"], (
            "Provider queries must use indexes efficiently"
        )

    def test_bulk_operations_performance(self):
        """Test bulk operation performance requirements."""
        benchmarks = PerformanceQueryHelper.benchmark_bulk_operations(
            operation_count=20
        )

        # Validate benchmark structure
        assert "bulk_insert_ms" in benchmarks
        assert "bulk_query_ms" in benchmarks
        assert "bulk_update_ms" in benchmarks
        assert "operations_count" in benchmarks
        assert "meets_bulk_requirements" in benchmarks

        # Performance requirements for bulk operations
        assert benchmarks["bulk_insert_ms"] < 1000, (
            f"Bulk insert took {benchmarks['bulk_insert_ms']:.2f}ms"
        )
        assert benchmarks["bulk_query_ms"] < 500, (
            f"Bulk query took {benchmarks['bulk_query_ms']:.2f}ms"
        )
        assert benchmarks["bulk_update_ms"] < 1000, (
            f"Bulk update took {benchmarks['bulk_update_ms']:.2f}ms"
        )

        # Should meet bulk requirements
        assert benchmarks["meets_bulk_requirements"] is True, (
            "Bulk performance requirements not met"
        )

    def test_concurrent_query_performance(self):
        """Test concurrent query performance for multi-user scenarios."""
        # Test with reduced concurrency for CI environments
        results = PerformanceQueryHelper.stress_test_concurrent_queries(
            concurrent_sessions=3, queries_per_session=5
        )

        # Validate results structure
        assert "total_queries" in results
        assert "successful_queries" in results
        assert "failed_queries" in results
        assert "average_query_time_ms" in results
        assert "max_query_time_ms" in results
        assert "concurrent_performance_acceptable" in results

        # Validate all queries completed successfully
        assert results["successful_queries"] == results["total_queries"]
        assert results["failed_queries"] == 0

        # Performance requirements for concurrent queries
        assert results["average_query_time_ms"] < 200.0, (
            f"Average query time {results['average_query_time_ms']:.2f}ms exceeds 200ms"
        )
        assert results["max_query_time_ms"] < 500.0, (
            f"Max query time {results['max_query_time_ms']:.2f}ms exceeds 500ms"
        )

        # Overall concurrent performance should be acceptable
        assert results["concurrent_performance_acceptable"] is True, (
            "Concurrent performance not acceptable"
        )


@pytest.mark.quality_gates
class TestF1QualityGates:
    """Test F1 module quality gates and production readiness."""

    def test_backward_compatibility_validation(self):
        """Test that provider extensions maintain backward compatibility."""
        with get_db_session() as session:
            # Create job using old Azure-only pattern
            legacy_job = VideoJobDB(
                id=f"legacy-test-job-{uuid.uuid4()}",
                prompt="Legacy Azure test",
                status="pending",
                session_id="legacy-session",
                created_at=datetime.utcnow(),
                # Note: api_provider will use default 'azure_sora'
            )
            session.add(legacy_job)
            session.commit()

            # Verify job was created with proper defaults
            retrieved = session.query(VideoJobDB).filter_by(id=legacy_job.id).first()
            assert retrieved is not None
            assert retrieved.api_provider == "azure_sora"  # Default applied
            assert retrieved.input_image_path is None  # Nullable
            assert retrieved.audio_generated is False  # Default applied

            # Cleanup
            session.delete(retrieved)
            session.commit()

    def test_dual_provider_schema_readiness(self):
        """Test schema is ready for dual-provider operations."""
        with get_db_session() as session:
            test_jobs = []

            # Test Azure Sora job creation
            azure_job = VideoJobDB(
                id=f"azure-readiness-test-{uuid.uuid4()}",
                prompt="Azure readiness test",
                status="pending",
                api_provider="azure_sora",
                session_id="readiness-session",
                created_at=datetime.utcnow(),
            )
            session.add(azure_job)
            test_jobs.append(azure_job)

            # Test Google Veo3 job creation
            veo3_job = VideoJobDB(
                id=f"veo3-readiness-test-{uuid.uuid4()}",
                prompt="Veo3 readiness test",
                status="pending",
                api_provider="google_veo3",
                input_image_path="/test/image.jpg",
                audio_generated=True,
                session_id="readiness-session",
                created_at=datetime.utcnow(),
            )
            session.add(veo3_job)
            test_jobs.append(veo3_job)

            session.commit()

            # Verify both providers work correctly
            azure_retrieved = (
                session.query(VideoJobDB).filter_by(id=azure_job.id).first()
            )
            veo3_retrieved = session.query(VideoJobDB).filter_by(id=veo3_job.id).first()

            assert azure_retrieved.api_provider == "azure_sora"
            assert veo3_retrieved.api_provider == "google_veo3"
            assert veo3_retrieved.input_image_path == "/test/image.jpg"
            assert veo3_retrieved.audio_generated is True

            # Cleanup
            for job in test_jobs:
                session.delete(job)
            session.commit()

    def test_provider_query_error_handling(self):
        """Test error handling in provider queries."""
        # Test with invalid provider
        jobs = ProviderQueryHelper.get_jobs_by_provider("invalid_provider")
        assert isinstance(jobs, list)
        assert len(jobs) == 0

        # Test with invalid session
        with patch("src.database.connection.get_db_session") as mock_session:
            mock_session.side_effect = Exception("Database error")

            with pytest.raises(Exception):
                ProviderQueryHelper.get_jobs_by_provider("azure_sora")

    def test_performance_degradation_detection(self):
        """Test performance degradation detection."""
        benchmarks = PerformanceQueryHelper.benchmark_provider_queries()

        # Performance regression thresholds
        performance_thresholds = {
            "provider_filter_ms": 100.0,
            "provider_stats_ms": 150.0,
            "combined_query_ms": 100.0,
        }

        performance_issues = []
        for metric, threshold in performance_thresholds.items():
            if benchmarks[metric] > threshold:
                performance_issues.append(
                    f"{metric}: {benchmarks[metric]:.2f}ms > {threshold}ms"
                )

        assert len(performance_issues) == 0, (
            f"Performance degradation detected: {performance_issues}"
        )

    def test_schema_validation_completeness(self):
        """Test that all provider schema features are properly implemented."""

        from src.database.models import VideoJobDB

        # Verify all provider fields exist in model
        model_fields = [attr for attr in dir(VideoJobDB) if not attr.startswith("_")]

        required_provider_fields = [
            "api_provider",
            "input_image_path",
            "audio_generated",
        ]

        for field in required_provider_fields:
            assert field in model_fields, (
                f"Required provider field '{field}' not found in VideoJobDB"
            )

        # Verify model methods exist
        required_methods = ["to_pydantic", "from_pydantic", "update_from_pydantic"]
        for method in required_methods:
            assert hasattr(VideoJobDB, method), (
                f"Required method '{method}' not found in VideoJobDB"
            )
            assert callable(getattr(VideoJobDB, method)), f"'{method}' is not callable"
