# Database Module Documentation

## Overview

Production-ready database layer with SQLAlchemy ORM, Flask-Migrate integration, and comprehensive connection management. Supports PostgreSQL for production and SQLite for development with automatic schema migrations and connection pooling.

## Module Structure

```
src/database/
├── models.py          # SQLAlchemy ORM models with multi-user schema
├── connection.py      # Database connection management
└── tests/             # Co-located database tests
    └── test_database.py
```

## Database Models

### VideoJobDB Model
```python
# src/database/models.py
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from typing import Optional, Dict, Any
import json
from datetime import datetime

Base = declarative_base()

class VideoJobDB(Base):
    """SQLAlchemy ORM model for video generation jobs."""
    
    __tablename__ = 'video_jobs'
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Job identification
    job_id = Column(String(100), unique=True, nullable=False, index=True)
    session_id = Column(String(100), nullable=False, index=True)
    
    # Job parameters
    prompt = Column(Text, nullable=False)
    duration = Column(Integer, nullable=True)  # Video duration in seconds
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    model = Column(String(50), nullable=True)
    
    # Job status and progress
    status = Column(String(20), nullable=False, default='pending', index=True)
    progress = Column(Float, default=0.0)
    message = Column(Text, nullable=True)
    
    # Azure API integration
    generation_id = Column(String(200), nullable=True, index=True)
    azure_job_id = Column(String(200), nullable=True)
    
    # File management
    file_path = Column(Text, nullable=True)
    download_url = Column(Text, nullable=True)
    file_size = Column(Integer, nullable=True)  # File size in bytes
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    
    # User context
    client_ip = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    
    # Additional metadata (JSON field)
    metadata = Column(Text, nullable=True)  # JSON string for flexibility
    
    # Database indexes for performance
    __table_args__ = (
        Index('idx_session_status', 'session_id', 'status'),
        Index('idx_status_created', 'status', 'created_at'),
        Index('idx_generation_id', 'generation_id'),
        Index('idx_created_at', 'created_at'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'session_id': self.session_id,
            'prompt': self.prompt,
            'duration': self.duration,
            'width': self.width,
            'height': self.height,
            'model': self.model,
            'status': self.status,
            'progress': self.progress,
            'message': self.message,
            'generation_id': self.generation_id,
            'azure_job_id': self.azure_job_id,
            'file_path': self.file_path,
            'download_url': self.download_url,
            'file_size': self.file_size,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'failed_at': self.failed_at.isoformat() if self.failed_at else None,
            'client_ip': self.client_ip,
            'user_agent': self.user_agent,
            'metadata': json.loads(self.metadata) if self.metadata else None
        }
    
    @classmethod
    def from_core_model(cls, core_job, session_id: str, client_ip: str = None, user_agent: str = None):
        """Create VideoJobDB from core VideoJob model."""
        from src.core.models import VideoJob
        
        return cls(
            job_id=core_job.job_id,
            session_id=session_id,
            prompt=core_job.prompt,
            duration=core_job.duration,
            width=core_job.width,
            height=core_job.height,
            model=core_job.model,
            status=core_job.status,
            progress=core_job.progress,
            message=core_job.message,
            generation_id=core_job.generation_id,
            file_path=core_job.file_path,
            download_url=core_job.download_url,
            error_message=core_job.error_message,
            client_ip=client_ip,
            user_agent=user_agent
        )
    
    def to_core_model(self):
        """Convert VideoJobDB to core VideoJob model."""
        from src.core.models import VideoJob
        
        return VideoJob(
            job_id=self.job_id,
            prompt=self.prompt,
            duration=self.duration,
            width=self.width,
            height=self.height,
            model=self.model,
            status=self.status,
            progress=self.progress,
            message=self.message,
            generation_id=self.generation_id,
            file_path=self.file_path,
            download_url=self.download_url,
            error_message=self.error_message,
            created_at=self.created_at,
            updated_at=self.updated_at
        )
    
    def update_status(self, status: str, **kwargs):
        """Update job status with timestamp tracking."""
        self.status = status
        self.updated_at = datetime.utcnow()
        
        # Set appropriate timestamp based on status
        if status == 'running' and not self.started_at:
            self.started_at = datetime.utcnow()
        elif status == 'succeeded' and not self.completed_at:
            self.completed_at = datetime.utcnow()
        elif status == 'failed' and not self.failed_at:
            self.failed_at = datetime.utcnow()
        
        # Update additional fields
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def set_metadata(self, metadata: Dict[str, Any]):
        """Set metadata as JSON string."""
        self.metadata = json.dumps(metadata) if metadata else None
    
    def get_metadata(self) -> Optional[Dict[str, Any]]:
        """Get metadata as Python dict."""
        return json.loads(self.metadata) if self.metadata else None
    
    def __repr__(self):
        return f"<VideoJobDB(job_id='{self.job_id}', status='{self.status}', session_id='{self.session_id}')>"
```

## Connection Management

### Database Connection Factory
```python
# src/database/connection.py
from sqlalchemy import create_engine, Pool
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, StaticPool
from contextlib import contextmanager
from typing import Generator, Optional, Dict, Any
import logging
import os

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Production-ready database connection management."""
    
    def __init__(self, database_url: str, echo: bool = False):
        self.database_url = database_url
        self.echo = echo
        self.engine = None
        self.session_factory = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize SQLAlchemy engine with appropriate configuration."""
        engine_kwargs = {
            'echo': self.echo,
            'future': True,  # Use SQLAlchemy 2.0 style
        }
        
        # Configure connection pooling based on database type
        if self.database_url.startswith('sqlite'):
            # SQLite configuration
            engine_kwargs.update({
                'poolclass': StaticPool,
                'connect_args': {
                    'check_same_thread': False,  # Allow multi-threading
                    'timeout': 20
                }
            })
        else:
            # PostgreSQL/other database configuration
            engine_kwargs.update({
                'poolclass': QueuePool,
                'pool_size': 20,
                'max_overflow': 30,
                'pool_pre_ping': True,  # Validate connections
                'pool_recycle': 3600,   # 1 hour
                'connect_args': {
                    'connect_timeout': 10,
                    'application_name': 'sora_poc'
                }
            })
        
        self.engine = create_engine(self.database_url, **engine_kwargs)
        self.session_factory = sessionmaker(bind=self.engine, expire_on_commit=False)
        
        logger.info(f"Database engine initialized: {self.database_url.split('@')[-1] if '@' in self.database_url else 'sqlite'}")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Get database session with automatic cleanup.
        
        Usage:
            with db_manager.get_session() as session:
                result = session.query(VideoJobDB).all()
        """
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """Create all database tables."""
        from src.database.models import Base
        Base.metadata.create_all(self.engine)
        logger.info("Database tables created")
    
    def drop_tables(self):
        """Drop all database tables (use with caution)."""
        from src.database.models import Base
        Base.metadata.drop_all(self.engine)
        logger.info("Database tables dropped")
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get database engine information."""
        return {
            'url': self.database_url.split('@')[-1] if '@' in self.database_url else self.database_url,
            'pool_size': getattr(self.engine.pool, 'size', 'N/A'),
            'checked_out': getattr(self.engine.pool, 'checkedout', 'N/A'),
            'overflow': getattr(self.engine.pool, 'overflow', 'N/A'),
            'checked_in': getattr(self.engine.pool, 'checkedin', 'N/A'),
        }
    
    def test_connection(self) -> bool:
        """Test database connectivity."""
        try:
            with self.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("SELECT 1")).scalar()
                return result == 1
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def close(self):
        """Close database connections."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections closed")

# Global database manager instance
_db_manager: Optional[DatabaseManager] = None

def initialize_database(database_url: str, echo: bool = False) -> DatabaseManager:
    """Initialize global database manager."""
    global _db_manager
    _db_manager = DatabaseManager(database_url, echo)
    return _db_manager

def get_database_manager() -> DatabaseManager:
    """Get global database manager instance."""
    if _db_manager is None:
        raise RuntimeError("Database not initialized. Call initialize_database() first.")
    return _db_manager

@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """Get database session using global manager."""
    db_manager = get_database_manager()
    with db_manager.get_session() as session:
        yield session
```

## Migration Management

### Flask-Migrate Integration
```python
# Migration setup in main application
from flask import Flask
from flask_migrate import Migrate
from src.database.models import Base
from src.database.connection import initialize_database, get_database_manager

def setup_database(app: Flask):
    """Set up database with Flask application."""
    # Initialize database connection
    database_url = app.config.get('DATABASE_URL', 'sqlite:///sora_poc.db')
    echo = app.config.get('SQL_DEBUG', False)
    
    db_manager = initialize_database(database_url, echo)
    
    # Set up Flask-Migrate
    migrate = Migrate(app, Base)
    
    # Store database manager in app context
    app.db_manager = db_manager
    
    return db_manager

# Migration commands
# uv run flask --app src.main:create_app db init       # Initialize migrations
# uv run flask --app src.main:create_app db migrate -m "Description"  # Create migration
# uv run flask --app src.main:create_app db upgrade    # Apply migrations
# uv run flask --app src.main:create_app db downgrade  # Rollback migrations
```

## Repository Pattern

### JobRepository Implementation
```python
# src/api/job_repository.py - Repository pattern for database operations
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from src.database.models import VideoJobDB
from src.database.connection import get_db_session
from src.core.models import VideoJob
import logging

logger = logging.getLogger(__name__)

class JobRepository:
    """Repository for video job database operations."""
    
    def create_job(self, job: VideoJob, session_id: str, client_ip: str = None, user_agent: str = None) -> VideoJobDB:
        """Create new job in database."""
        with get_db_session() as session:
            db_job = VideoJobDB.from_core_model(job, session_id, client_ip, user_agent)
            session.add(db_job)
            session.flush()  # Get the ID
            session.refresh(db_job)
            
            logger.info(f"Created job {db_job.job_id} for session {session_id}")
            return db_job
    
    def get_job_by_id(self, job_id: str) -> Optional[VideoJobDB]:
        """Get job by job ID."""
        with get_db_session() as session:
            return session.query(VideoJobDB).filter_by(job_id=job_id).first()
    
    def get_job_by_generation_id(self, generation_id: str) -> Optional[VideoJobDB]:
        """Get job by Azure generation ID."""
        with get_db_session() as session:
            return session.query(VideoJobDB).filter_by(generation_id=generation_id).first()
    
    def get_jobs_by_session(self, session_id: str, limit: int = 100) -> List[VideoJobDB]:
        """Get all jobs for a session."""
        with get_db_session() as session:
            return (session.query(VideoJobDB)
                   .filter_by(session_id=session_id)
                   .order_by(desc(VideoJobDB.created_at))
                   .limit(limit)
                   .all())
    
    def get_jobs_by_status(self, status: str, limit: int = 100) -> List[VideoJobDB]:
        """Get jobs by status."""
        with get_db_session() as session:
            return (session.query(VideoJobDB)
                   .filter_by(status=status)
                   .order_by(desc(VideoJobDB.created_at))
                   .limit(limit)
                   .all())
    
    def get_active_jobs(self, session_id: str = None) -> List[VideoJobDB]:
        """Get all active (pending/running) jobs."""
        with get_db_session() as session:
            query = session.query(VideoJobDB).filter(
                VideoJobDB.status.in_(['pending', 'running'])
            )
            
            if session_id:
                query = query.filter_by(session_id=session_id)
            
            return query.order_by(desc(VideoJobDB.created_at)).all()
    
    def update_job_by_id(self, job_id: str, update_data: Dict[str, Any]) -> bool:
        """Update job by job ID."""
        with get_db_session() as session:
            job = session.query(VideoJobDB).filter_by(job_id=job_id).first()
            
            if not job:
                return False
            
            # Update fields
            for key, value in update_data.items():
                if hasattr(job, key):
                    setattr(job, key, value)
            
            # Handle status updates with timestamps
            if 'status' in update_data:
                job.update_status(update_data['status'], **{
                    k: v for k, v in update_data.items() if k != 'status'
                })
            
            logger.info(f"Updated job {job_id}: {update_data}")
            return True
    
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status with automatic timestamp handling."""
        return self.update_job_by_id(job_id, {'status': status, **kwargs})
    
    def delete_job(self, job_id: str) -> bool:
        """Delete job by job ID."""
        with get_db_session() as session:
            job = session.query(VideoJobDB).filter_by(job_id=job_id).first()
            
            if not job:
                return False
            
            session.delete(job)
            logger.info(f"Deleted job {job_id}")
            return True
    
    def get_job_statistics(self, session_id: str = None) -> Dict[str, Any]:
        """Get job statistics."""
        with get_db_session() as session:
            query = session.query(VideoJobDB)
            
            if session_id:
                query = query.filter_by(session_id=session_id)
            
            # Count by status
            total_jobs = query.count()
            pending_jobs = query.filter_by(status='pending').count()
            running_jobs = query.filter_by(status='running').count()
            succeeded_jobs = query.filter_by(status='succeeded').count()
            failed_jobs = query.filter_by(status='failed').count()
            
            return {
                'total_jobs': total_jobs,
                'pending_jobs': pending_jobs,
                'running_jobs': running_jobs,
                'succeeded_jobs': succeeded_jobs,
                'failed_jobs': failed_jobs,
                'active_jobs': pending_jobs + running_jobs,
                'completed_jobs': succeeded_jobs + failed_jobs,
                'success_rate': (succeeded_jobs / total_jobs * 100) if total_jobs > 0 else 0
            }
    
    def cleanup_old_jobs(self, older_than_days: int = 7) -> int:
        """Clean up old completed jobs."""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=older_than_days)
        
        with get_db_session() as session:
            old_jobs = session.query(VideoJobDB).filter(
                and_(
                    VideoJobDB.status.in_(['succeeded', 'failed']),
                    VideoJobDB.completed_at < cutoff_date
                )
            ).all()
            
            count = len(old_jobs)
            for job in old_jobs:
                session.delete(job)
            
            logger.info(f"Cleaned up {count} old jobs")
            return count
```

## Configuration

### Database Configuration
```bash
# Development (SQLite)
DATABASE_URL=sqlite:///sora_poc.db
SQL_DEBUG=false

# Production (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/sora_prod

# Connection Pool Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# Migration Settings
MIGRATION_DIR=migrations
```

### Environment-Specific Configuration
```python
from src.config.environments import get_environment_config

def get_database_config():
    """Get database configuration for current environment."""
    config = get_environment_config()
    
    if config.environment == 'production':
        return {
            'url': config.database_url,
            'pool_size': 20,
            'max_overflow': 30,
            'pool_recycle': 3600,
            'echo': False
        }
    elif config.environment == 'testing':
        return {
            'url': 'sqlite:///:memory:',
            'pool_size': 1,
            'max_overflow': 0,
            'echo': False
        }
    else:  # development
        return {
            'url': config.database_url or 'sqlite:///sora_poc.db',
            'pool_size': 5,
            'max_overflow': 10,
            'echo': config.sql_debug or False
        }
```

## Testing Patterns

### Database Testing Setup
```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.database.models import Base, VideoJobDB
from src.database.connection import DatabaseManager

@pytest.fixture(scope='session')
def test_engine():
    """Create test database engine."""
    engine = create_engine('sqlite:///:memory:', echo=False)
    Base.metadata.create_all(engine)
    return engine

@pytest.fixture
def test_session(test_engine):
    """Create test database session."""
    Session = sessionmaker(bind=test_engine)
    session = Session()
    yield session
    session.close()

@pytest.fixture
def test_db_manager():
    """Create test database manager."""
    return DatabaseManager('sqlite:///:memory:', echo=False)

class TestVideoJobDB:
    """Test VideoJobDB model."""
    
    def test_create_job(self, test_session):
        """Test creating a video job."""
        job = VideoJobDB(
            job_id='test-job-123',
            session_id='test-session-456',
            prompt='Test prompt',
            duration=5,
            status='pending'
        )
        
        test_session.add(job)
        test_session.commit()
        
        # Verify job was created
        retrieved = test_session.query(VideoJobDB).filter_by(job_id='test-job-123').first()
        assert retrieved is not None
        assert retrieved.prompt == 'Test prompt'
        assert retrieved.status == 'pending'
    
    def test_job_to_dict(self, test_session):
        """Test job serialization to dictionary."""
        job = VideoJobDB(
            job_id='test-job-456',
            session_id='test-session-789',
            prompt='Test prompt',
            status='running',
            progress=50.0
        )
        
        job_dict = job.to_dict()
        
        assert job_dict['job_id'] == 'test-job-456'
        assert job_dict['session_id'] == 'test-session-789'
        assert job_dict['prompt'] == 'Test prompt'
        assert job_dict['status'] == 'running'
        assert job_dict['progress'] == 50.0
    
    def test_status_update_timestamps(self, test_session):
        """Test automatic timestamp updates for status changes."""
        job = VideoJobDB(
            job_id='test-job-789',
            session_id='test-session-123',
            prompt='Test prompt',
            status='pending'
        )
        
        test_session.add(job)
        test_session.commit()
        
        # Update to running
        job.update_status('running')
        assert job.status == 'running'
        assert job.started_at is not None
        
        # Update to succeeded
        job.update_status('succeeded')
        assert job.status == 'succeeded'
        assert job.completed_at is not None

class TestJobRepository:
    """Test JobRepository operations."""
    
    @pytest.fixture
    def job_repository(self, test_db_manager):
        """Create job repository with test database."""
        # Mock the global database manager
        import src.database.connection
        src.database.connection._db_manager = test_db_manager
        
        from src.api.job_repository import JobRepository
        return JobRepository()
    
    def test_create_and_get_job(self, job_repository):
        """Test creating and retrieving a job."""
        from src.core.models import VideoJob
        
        # Create core model
        core_job = VideoJob(
            job_id='test-job-abc',
            prompt='Test repository prompt',
            duration=10
        )
        
        # Create in database
        db_job = job_repository.create_job(core_job, 'test-session-def')
        
        # Retrieve from database
        retrieved = job_repository.get_job_by_id('test-job-abc')
        
        assert retrieved is not None
        assert retrieved.job_id == 'test-job-abc'
        assert retrieved.session_id == 'test-session-def'
        assert retrieved.prompt == 'Test repository prompt'
    
    def test_get_jobs_by_session(self, job_repository):
        """Test retrieving jobs by session."""
        from src.core.models import VideoJob
        
        session_id = 'test-session-ghi'
        
        # Create multiple jobs for the session
        for i in range(3):
            job = VideoJob(
                job_id=f'test-job-{i}',
                prompt=f'Test prompt {i}',
                duration=5
            )
            job_repository.create_job(job, session_id)
        
        # Retrieve jobs for session
        session_jobs = job_repository.get_jobs_by_session(session_id)
        
        assert len(session_jobs) == 3
        assert all(job.session_id == session_id for job in session_jobs)
```

## Performance Optimization

### Query Optimization
```python
# Efficient queries with proper indexing
def get_recent_jobs_optimized(session_id: str, limit: int = 20):
    """Get recent jobs with optimized query."""
    with get_db_session() as session:
        return (session.query(VideoJobDB)
               .filter_by(session_id=session_id)
               .order_by(desc(VideoJobDB.created_at))
               .limit(limit)
               .all())

# Bulk operations for efficiency
def bulk_update_job_status(job_ids: List[str], status: str):
    """Update multiple jobs efficiently."""
    with get_db_session() as session:
        session.query(VideoJobDB)\
               .filter(VideoJobDB.job_id.in_(job_ids))\
               .update({'status': status}, synchronize_session=False)

# Pagination for large result sets
def get_jobs_paginated(page: int = 1, per_page: int = 50, session_id: str = None):
    """Get jobs with pagination."""
    with get_db_session() as session:
        query = session.query(VideoJobDB)
        
        if session_id:
            query = query.filter_by(session_id=session_id)
        
        return query.order_by(desc(VideoJobDB.created_at))\
                   .offset((page - 1) * per_page)\
                   .limit(per_page)\
                   .all()
```

### Connection Pool Monitoring
```python
def get_connection_pool_stats():
    """Get connection pool statistics."""
    db_manager = get_database_manager()
    engine = db_manager.engine
    
    if hasattr(engine.pool, 'size'):
        return {
            'pool_size': engine.pool.size(),
            'checked_out': engine.pool.checkedout(),
            'overflow': engine.pool.overflow(),
            'checked_in': engine.pool.checkedin(),
            'status': 'healthy' if engine.pool.checkedout() < engine.pool.size() else 'degraded'
        }
    else:
        return {'status': 'unknown', 'message': 'Pool statistics not available'}
```

## Best Practices

1. **Connection Management**: Always use context managers for database sessions
2. **Transaction Handling**: Proper commit/rollback in session management
3. **Indexing**: Add appropriate indexes for frequently queried columns
4. **Migration Safety**: Test migrations thoroughly before production deployment
5. **Pool Configuration**: Configure connection pools based on expected load
6. **Error Handling**: Implement comprehensive error handling for database operations
7. **Testing**: Use in-memory SQLite for fast unit tests
8. **Security**: Use parameterized queries to prevent SQL injection
9. **Performance**: Monitor query performance and optimize slow queries
10. **Backup**: Implement regular database backups for production

## Troubleshooting

### Common Issues

**Connection Pool Exhaustion**
```python
# Monitor pool status
from src.database.connection import get_database_manager

db_manager = get_database_manager()
pool_info = db_manager.get_engine_info()
print(f"Pool status: {pool_info}")

# Check for connection leaks
import gc
from sqlalchemy.orm import Session

sessions = [obj for obj in gc.get_objects() if isinstance(obj, Session)]
print(f"Active sessions: {len(sessions)}")
```

**Migration Issues**
```bash
# Check current migration version
uv run flask --app src.main:create_app db current

# Show migration history
uv run flask --app src.main:create_app db history

# Manually apply specific migration
uv run flask --app src.main:create_app db upgrade <revision_id>

# Create custom migration
uv run flask --app src.main:create_app db revision -m "Custom migration"
```

**Performance Issues**
```sql
-- Check PostgreSQL query performance
EXPLAIN ANALYZE SELECT * FROM video_jobs WHERE session_id = 'test' ORDER BY created_at DESC;

-- Check for missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename = 'video_jobs';

-- Monitor connection usage
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE application_name = 'sora_poc';
```

### Development Commands
```bash
# Test database operations
uv run pytest src/database/tests/ -v

# Initialize database
uv run python -c "
from src.database.connection import initialize_database
from src.database.models import Base

db_manager = initialize_database('sqlite:///test.db')
db_manager.create_tables()
print('Database initialized')
"

# Test connection
uv run python -c "
from src.database.connection import initialize_database

db_manager = initialize_database('sqlite:///sora_poc.db')
if db_manager.test_connection():
    print('Database connection successful')
else:
    print('Database connection failed')
"

# Migration management
uv run flask --app src.main:create_app db init
uv run flask --app src.main:create_app db migrate -m "Initial migration"
uv run flask --app src.main:create_app db upgrade
```