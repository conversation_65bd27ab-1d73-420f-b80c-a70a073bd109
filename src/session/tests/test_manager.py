"""Tests for session manager functionality."""

from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest

from src.session.manager import (
    SessionManager,
    get_or_create_session,
    get_session_manager,
)


@pytest.mark.unit
class TestSessionManager:
    """Test cases for SessionManager functionality."""

    @pytest.fixture
    def mock_config(self):
        """Create mock configuration for testing."""
        config = Mock()
        config.SESSION_LIFETIME_HOURS = 24
        config.SESSION_CLEANUP_INTERVAL_HOURS = 6
        config.MAX_SESSIONS_PER_IP = 10
        return config

    @pytest.fixture
    def session_manager(self, mock_config):
        """Create SessionManager instance with mocked configuration."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config",
            return_value=mock_config,
        ):
            return SessionManager("testing")

    def test_session_manager_initialization(self, session_manager, mock_config):
        """Test SessionManager initialization with configuration."""
        assert session_manager.session_lifetime_hours == 24
        assert session_manager.cleanup_interval_hours == 6
        assert session_manager.max_sessions_per_ip == 10
        assert isinstance(session_manager._sessions, dict)
        assert len(session_manager._sessions) == 0

    def test_create_session_success(self, session_manager):
        """Test successful session creation."""
        client_ip = "*************"

        session_id, session_data = session_manager.create_session(client_ip)

        # Verify session ID format and length
        assert isinstance(session_id, str)
        assert len(session_id) == 64  # SHA-256 hex = 64 chars

        # Verify session data structure
        assert session_data["session_id"] == session_id
        assert session_data["client_ip"] == client_ip
        assert session_data["job_count"] == 0
        assert session_data["total_jobs_submitted"] == 0
        assert session_data["is_active"] is True
        assert isinstance(session_data["created_at"], datetime)
        assert isinstance(session_data["last_active"], datetime)
        assert isinstance(session_data["active_jobs"], list)

        # Verify session is stored
        assert session_id in session_manager._sessions

    def test_create_session_without_ip(self, session_manager):
        """Test session creation without client IP."""
        session_id, session_data = session_manager.create_session()

        assert session_data["client_ip"] is None
        assert session_data["session_id"] == session_id
        assert session_id in session_manager._sessions

    def test_create_session_ip_limit_exceeded(self, session_manager):
        """Test session creation when IP limit is exceeded."""
        client_ip = "*************"

        # Create sessions up to the limit (10)
        for i in range(10):
            session_manager.create_session(client_ip)

        # 11th session should fail
        with pytest.raises(ValueError, match="Maximum 10 sessions per IP exceeded"):
            session_manager.create_session(client_ip)

    def test_get_session_valid(self, session_manager):
        """Test getting a valid session."""
        # Create a session
        session_id, original_data = session_manager.create_session("*************")

        # Get the session
        retrieved_data = session_manager.get_session(session_id)

        assert retrieved_data is not None
        assert retrieved_data["session_id"] == session_id
        assert retrieved_data["client_ip"] == "*************"

        # last_active should be updated
        assert retrieved_data["last_active"] >= original_data["last_active"]

    def test_get_session_invalid_id(self, session_manager):
        """Test getting session with invalid ID."""
        result = session_manager.get_session("nonexistent-session-id")
        assert result is None

        result = session_manager.get_session("")
        assert result is None

        result = session_manager.get_session(None)
        assert result is None

    def test_get_session_expired(self, session_manager):
        """Test getting an expired session."""
        # Create a session
        session_id, _ = session_manager.create_session()

        # Manually expire the session by setting old last_active time
        session_manager._sessions[session_id]["last_active"] = (
            datetime.utcnow() - timedelta(hours=25)  # Older than 24h lifetime
        )

        # Getting expired session should return None and clean it up
        result = session_manager.get_session(session_id)
        assert result is None
        assert session_id not in session_manager._sessions

    def test_validate_session_valid(self, session_manager):
        """Test validation of valid session."""
        session_id, _ = session_manager.create_session("*************")

        is_valid, reason = session_manager.validate_session(session_id, "*************")

        assert is_valid is True
        assert reason == "Valid session"

    def test_validate_session_invalid_format(self, session_manager):
        """Test validation of session with invalid format."""
        # Empty session ID
        is_valid, reason = session_manager.validate_session("")
        assert is_valid is False
        assert "Empty session ID" in reason

        # Wrong length session ID
        is_valid, reason = session_manager.validate_session("short-id")
        assert is_valid is False
        assert "Invalid session ID format" in reason

    def test_validate_session_not_found(self, session_manager):
        """Test validation of non-existent session."""
        # Valid format but non-existent
        fake_session_id = "a" * 64  # 64 chars, valid format

        is_valid, reason = session_manager.validate_session(fake_session_id)
        assert is_valid is False
        assert "Session not found or expired" in reason

    def test_validate_session_ip_mismatch(self, session_manager):
        """Test validation with IP mismatch (should log warning but allow)."""
        session_id, _ = session_manager.create_session("*************")

        # Different IP should be allowed but logged
        is_valid, reason = session_manager.validate_session(session_id, "*************")
        assert is_valid is True  # Still valid for POC
        assert reason == "Valid session"

    def test_validate_session_deactivated(self, session_manager):
        """Test validation of deactivated session."""
        session_id, _ = session_manager.create_session()

        # Deactivate the session
        session_manager.deactivate_session(session_id)

        is_valid, reason = session_manager.validate_session(session_id)
        assert is_valid is False
        assert "Session is deactivated" in reason

    def test_update_session_activity_success(self, session_manager):
        """Test successful session activity update."""
        session_id, original_data = session_manager.create_session()

        # Update activity with new data
        activity_data = {
            "job_count": 2,
            "total_jobs_submitted": 5,
            "active_jobs": ["job1", "job2"],
        }

        success = session_manager.update_session_activity(session_id, activity_data)
        assert success is True

        # Verify updates
        updated_session = session_manager.get_session(session_id)
        assert updated_session["job_count"] == 2
        assert updated_session["total_jobs_submitted"] == 5
        assert updated_session["active_jobs"] == ["job1", "job2"]
        assert updated_session["last_active"] > original_data["last_active"]

    def test_update_session_activity_invalid_session(self, session_manager):
        """Test activity update for invalid session."""
        success = session_manager.update_session_activity(
            "nonexistent", {"job_count": 1}
        )
        assert success is False

    def test_update_session_activity_restricted_fields(self, session_manager):
        """Test that only allowed fields can be updated."""
        session_id, _ = session_manager.create_session()

        # Try to update restricted fields
        activity_data = {
            "session_id": "hacked-id",  # Not allowed
            "created_at": datetime.utcnow(),  # Not allowed
            "job_count": 5,  # Allowed
        }

        session_manager.update_session_activity(session_id, activity_data)

        # Verify only allowed fields were updated
        updated_session = session_manager.get_session(session_id)
        assert updated_session["session_id"] == session_id  # Unchanged
        assert updated_session["job_count"] == 5  # Updated

    def test_deactivate_session_success(self, session_manager):
        """Test successful session deactivation."""
        session_id, _ = session_manager.create_session()

        success = session_manager.deactivate_session(session_id)
        assert success is True

        # Verify session is deactivated
        session = session_manager.get_session(session_id)
        assert session["is_active"] is False
        assert "deactivated_at" in session

    def test_deactivate_session_invalid(self, session_manager):
        """Test deactivation of invalid session."""
        success = session_manager.deactivate_session("nonexistent")
        assert success is False

    def test_cleanup_expired_sessions(self, session_manager):
        """Test cleanup of expired sessions."""
        # Create some sessions
        session1_id, _ = session_manager.create_session()
        session2_id, _ = session_manager.create_session()
        session3_id, _ = session_manager.create_session()

        # Expire some sessions manually
        session_manager._sessions[session1_id]["last_active"] = (
            datetime.utcnow() - timedelta(hours=25)
        )
        session_manager._sessions[session2_id]["last_active"] = (
            datetime.utcnow() - timedelta(hours=25)
        )
        # session3 remains active

        # Run cleanup
        cleaned_count = session_manager.cleanup_expired_sessions()

        assert cleaned_count == 2
        assert session1_id not in session_manager._sessions
        assert session2_id not in session_manager._sessions
        assert session3_id in session_manager._sessions  # Still active

    def test_get_session_stats(self, session_manager):
        """Test session statistics generation."""
        # Create some sessions with various states
        active_session, _ = session_manager.create_session()
        expired_session, _ = session_manager.create_session()

        # Update session activity
        session_manager.update_session_activity(
            active_session, {"total_jobs_submitted": 3}
        )

        # Expire one session
        session_manager._sessions[expired_session]["last_active"] = (
            datetime.utcnow() - timedelta(hours=25)
        )

        stats = session_manager.get_session_stats()

        assert stats["total_sessions"] == 2
        assert stats["active_sessions"] == 1
        assert stats["expired_sessions"] == 1
        assert stats["total_jobs_across_sessions"] == 3
        assert stats["session_lifetime_hours"] == 24
        assert "timestamp" in stats
        assert "ip_distribution" in stats

    def test_generate_secure_session_id_uniqueness(self, session_manager):
        """Test that session ID generation produces unique IDs."""
        ids = set()

        # Generate multiple IDs
        for _ in range(100):
            session_id = session_manager._generate_secure_session_id()
            assert len(session_id) == 64
            assert session_id not in ids  # Should be unique
            ids.add(session_id)

    def test_check_ip_session_limit(self, session_manager):
        """Test IP session limit checking."""
        client_ip = "*************"

        # Create sessions up to limit
        for i in range(9):  # 9 sessions (below limit of 10)
            session_manager.create_session(client_ip)

        # Should not exceed limit yet
        assert not session_manager._check_ip_session_limit(client_ip)

        # Create one more (reaching limit)
        session_manager.create_session(client_ip)

        # Should now be at limit
        assert session_manager._check_ip_session_limit(client_ip)

    def test_session_expiry_logic(self, session_manager):
        """Test session expiry checking logic."""
        # Create test session data
        active_session = {"last_active": datetime.utcnow(), "is_active": True}

        expired_session = {
            "last_active": datetime.utcnow() - timedelta(hours=25),
            "is_active": True,
        }

        no_activity_session = {}

        # Test expiry logic
        assert not session_manager._is_session_expired(active_session)
        assert session_manager._is_session_expired(expired_session)
        assert session_manager._is_session_expired(no_activity_session)


@pytest.mark.unit
class TestSessionManagerHelpers:
    """Test cases for SessionManager helper functions."""

    def test_get_session_manager_singleton(self):
        """Test that get_session_manager returns singleton instance."""
        with patch("src.config.factory.ConfigurationFactory.get_base_config"):
            manager1 = get_session_manager()
            manager2 = get_session_manager()

            # Should be the same instance
            assert manager1 is manager2

    def test_get_or_create_session_existing(self):
        """Test get_or_create_session with existing valid session."""
        with patch("src.session.manager.get_session_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_session_data = {"session_id": "existing-session", "is_active": True}
            mock_manager.get_session.return_value = mock_session_data
            mock_get_manager.return_value = mock_manager

            session_id, session_data = get_or_create_session(
                "existing-session", "*************"
            )

            assert session_id == "existing-session"
            assert session_data == mock_session_data
            mock_manager.get_session.assert_called_once_with("existing-session")

    def test_get_or_create_session_create_new(self):
        """Test get_or_create_session creating new session."""
        with patch("src.session.manager.get_session_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_session.return_value = None  # No existing session
            mock_manager.create_session.return_value = (
                "new-session",
                {"session_id": "new-session"},
            )
            mock_get_manager.return_value = mock_manager

            session_id, session_data = get_or_create_session(
                "invalid-session", "*************"
            )

            assert session_id == "new-session"
            mock_manager.create_session.assert_called_once_with("*************")

    def test_get_or_create_session_no_existing_id(self):
        """Test get_or_create_session without existing session ID."""
        with patch("src.session.manager.get_session_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.create_session.return_value = (
                "new-session",
                {"session_id": "new-session"},
            )
            mock_get_manager.return_value = mock_manager

            session_id, session_data = get_or_create_session(None, "*************")

            assert session_id == "new-session"
            mock_manager.create_session.assert_called_once_with("*************")


@pytest.mark.unit
class TestSessionManagerIntegration:
    """Integration tests for SessionManager with realistic scenarios."""

    def test_multi_user_session_isolation(self):
        """Test session isolation across multiple users."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            mock_config.return_value = Mock(
                SESSION_LIFETIME_HOURS=24,
                SESSION_CLEANUP_INTERVAL_HOURS=6,
                MAX_SESSIONS_PER_IP=10,
            )

            manager = SessionManager("testing")

            # Create sessions for different users/IPs
            user1_session, user1_data = manager.create_session("*************")
            user2_session, user2_data = manager.create_session("*************")
            user3_session, user3_data = manager.create_session(
                "*************"
            )  # Same IP as user1

            # Verify sessions are isolated
            assert user1_session != user2_session != user3_session
            assert user1_data["client_ip"] == user3_data["client_ip"]  # Same IP
            assert user1_data["client_ip"] != user2_data["client_ip"]  # Different IP

            # Update activity for user1
            manager.update_session_activity(user1_session, {"job_count": 5})

            # Verify other users unaffected
            user2_updated = manager.get_session(user2_session)
            user3_updated = manager.get_session(user3_session)

            assert user2_updated["job_count"] == 0  # Unchanged
            assert user3_updated["job_count"] == 0  # Unchanged

            # User1 should have updated count
            user1_updated = manager.get_session(user1_session)
            assert user1_updated["job_count"] == 5

    def test_session_lifecycle_complete(self):
        """Test complete session lifecycle from creation to cleanup."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            mock_config.return_value = Mock(
                SESSION_LIFETIME_HOURS=1,  # Short lifetime for testing
                SESSION_CLEANUP_INTERVAL_HOURS=1,
                MAX_SESSIONS_PER_IP=5,
            )

            manager = SessionManager("testing")

            # 1. Create session
            session_id, session_data = manager.create_session("*************")
            assert manager.get_session(session_id) is not None

            # 2. Update activity multiple times
            for i in range(3):
                manager.update_session_activity(
                    session_id, {"job_count": i + 1, "total_jobs_submitted": i + 1}
                )

            # 3. Validate session
            is_valid, reason = manager.validate_session(session_id)
            assert is_valid is True

            # 4. Manually expire session
            manager._sessions[session_id]["last_active"] = (
                datetime.utcnow() - timedelta(hours=2)  # Older than 1h lifetime
            )

            # 5. Attempt to get expired session
            expired_session = manager.get_session(session_id)
            assert expired_session is None

            # 6. Verify session was cleaned up
            assert session_id not in manager._sessions

    def test_concurrent_session_creation_simulation(self):
        """Test simulation of concurrent session creation."""
        with patch(
            "src.config.factory.ConfigurationFactory.get_base_config"
        ) as mock_config:
            mock_config.return_value = Mock(
                SESSION_LIFETIME_HOURS=24,
                SESSION_CLEANUP_INTERVAL_HOURS=6,
                MAX_SESSIONS_PER_IP=5,
            )

            manager = SessionManager("testing")

            # Simulate multiple users creating sessions rapidly
            client_ips = [f"192.168.1.{i}" for i in range(100, 110)]
            sessions = []

            for ip in client_ips:
                # Each IP creates multiple sessions (up to limit)
                for _ in range(3):  # 3 sessions per IP
                    session_id, session_data = manager.create_session(ip)
                    sessions.append((session_id, ip))

            # Verify all sessions were created
            assert len(sessions) == 30  # 10 IPs * 3 sessions each

            # Verify session isolation by IP
            ip_sessions = {}
            for session_id, ip in sessions:
                if ip not in ip_sessions:
                    ip_sessions[ip] = []
                ip_sessions[ip].append(session_id)

            # Each IP should have exactly 3 sessions
            for ip, session_ids in ip_sessions.items():
                assert len(session_ids) == 3

                # Verify all sessions for this IP are unique
                assert len(set(session_ids)) == 3
