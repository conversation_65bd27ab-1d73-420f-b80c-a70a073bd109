"""Tests for session isolation functionality."""

from datetime import datetime
from unittest.mock import Mock, patch

import pytest

from src.session.isolation import SessionIsolation, get_session_isolation


@pytest.mark.unit
class TestSessionIsolation:
    """Test cases for SessionIsolation functionality."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create mock session manager for testing."""
        manager = Mock()
        manager.validate_session.return_value = (True, "Valid session")
        manager.get_session.return_value = {
            "session_id": "test-session",
            "is_active": True,
            "created_at": datetime.utcnow(),
        }
        return manager

    @pytest.fixture
    def mock_job_repository(self):
        """Create mock job repository for testing."""
        return Mock()

    @pytest.fixture
    def session_isolation(self, mock_session_manager, mock_job_repository):
        """Create SessionIsolation instance with mocked dependencies."""
        with patch(
            "src.session.isolation.get_session_manager",
            return_value=mock_session_manager,
        ):
            with patch(
                "src.api.job_repository.JobRepository", return_value=mock_job_repository
            ):
                isolation = SessionIsolation()
                isolation.job_repository = mock_job_repository  # Ensure mock is used
                return isolation

    def test_session_isolation_initialization(self, session_isolation):
        """Test SessionIsolation initialization."""
        assert session_isolation is not None
        assert hasattr(session_isolation, "session_manager")
        assert hasattr(session_isolation, "job_repository")

    def test_validate_job_ownership_success(
        self, session_isolation, mock_job_repository
    ):
        """Test successful job ownership validation."""
        # Setup mock job
        mock_job = Mock()
        mock_job.session_id = "test-session"
        mock_job_repository.get_job_by_id.return_value = mock_job

        # Test ownership validation
        is_owner = session_isolation.validate_job_ownership("test-session", "test-job")

        assert is_owner is True
        mock_job_repository.get_job_by_id.assert_called_once_with("test-job")

    def test_validate_job_ownership_invalid_session(
        self, session_isolation, mock_session_manager
    ):
        """Test job ownership validation with invalid session."""
        # Setup invalid session
        mock_session_manager.validate_session.return_value = (False, "Invalid session")

        is_owner = session_isolation.validate_job_ownership(
            "invalid-session", "test-job"
        )

        assert is_owner is False
        mock_session_manager.validate_session.assert_called_once_with("invalid-session")

    def test_validate_job_ownership_job_not_found(
        self, session_isolation, mock_job_repository
    ):
        """Test job ownership validation when job doesn't exist."""
        mock_job_repository.get_job_by_id.return_value = None

        is_owner = session_isolation.validate_job_ownership(
            "test-session", "nonexistent-job"
        )

        assert is_owner is False

    def test_validate_job_ownership_wrong_owner(
        self, session_isolation, mock_job_repository
    ):
        """Test job ownership validation with wrong owner."""
        # Setup mock job with different session
        mock_job = Mock()
        mock_job.session_id = "other-session"
        mock_job_repository.get_job_by_id.return_value = mock_job

        is_owner = session_isolation.validate_job_ownership("test-session", "test-job")

        assert is_owner is False

    def test_get_session_jobs_success(self, session_isolation, mock_job_repository):
        """Test successful retrieval of session jobs."""
        # Setup mock jobs
        mock_jobs = [
            Mock(id="job1", prompt="test 1", status="completed", file_path="/path1"),
            Mock(id="job2", prompt="test 2", status="running", file_path=None),
        ]
        mock_job_repository.get_jobs_by_session.return_value = mock_jobs

        jobs = session_isolation.get_session_jobs("test-session")

        assert len(jobs) == 2
        assert jobs[0]["id"] == "job1"
        assert jobs[1]["id"] == "job2"
        mock_job_repository.get_jobs_by_session.assert_called_once_with("test-session")

    def test_get_session_jobs_with_status_filter(
        self, session_isolation, mock_job_repository
    ):
        """Test session jobs retrieval with status filter."""
        mock_jobs = [Mock(id="job1", status="completed")]
        mock_job_repository.get_jobs_by_session_and_status.return_value = mock_jobs

        jobs = session_isolation.get_session_jobs("test-session", "completed")

        assert len(jobs) == 1
        mock_job_repository.get_jobs_by_session_and_status.assert_called_once_with(
            "test-session", "completed"
        )

    def test_get_session_jobs_invalid_session(
        self, session_isolation, mock_session_manager
    ):
        """Test session jobs retrieval with invalid session."""
        mock_session_manager.validate_session.return_value = (False, "Invalid session")

        jobs = session_isolation.get_session_jobs("invalid-session")

        assert jobs == []

    def test_validate_session_file_access_success(
        self, session_isolation, mock_job_repository
    ):
        """Test successful file access validation."""
        # Setup mock jobs with file path
        mock_jobs = [
            Mock(id="job1", file_path="/path/to/video.mp4"),
            Mock(id="job2", file_path="/path/to/other.mp4"),
        ]
        mock_job_repository.get_jobs_by_session.return_value = mock_jobs

        # Mock get_session_jobs to return sanitized job data
        with patch.object(session_isolation, "get_session_jobs") as mock_get_jobs:
            mock_get_jobs.return_value = [
                {"id": "job1", "file_path": "/path/to/video.mp4"},
                {"id": "job2", "file_path": "/path/to/other.mp4"},
            ]

            can_access = session_isolation.validate_session_file_access(
                "test-session", "/path/to/video.mp4"
            )

            assert can_access is True

    def test_validate_session_file_access_denied(self, session_isolation):
        """Test file access validation denial."""
        # Mock get_session_jobs to return jobs without the requested file
        with patch.object(session_isolation, "get_session_jobs") as mock_get_jobs:
            mock_get_jobs.return_value = [
                {"id": "job1", "file_path": "/path/to/other.mp4"},
            ]

            can_access = session_isolation.validate_session_file_access(
                "test-session", "/path/to/video.mp4"
            )

            assert can_access is False

    def test_get_session_resource_usage(self, session_isolation, mock_session_manager):
        """Test session resource usage calculation."""
        # Mock session data
        mock_session_manager.get_session.return_value = {
            "session_id": "test-session",
            "created_at": datetime.utcnow(),
            "last_active": datetime.utcnow(),
        }

        # Mock get_session_jobs to return job data
        with patch.object(session_isolation, "get_session_jobs") as mock_get_jobs:
            mock_get_jobs.return_value = [
                {
                    "id": "job1",
                    "status": "succeeded",
                    "duration": 5,
                    "file_path": "/path1",
                },
                {
                    "id": "job2",
                    "status": "succeeded",
                    "duration": 10,
                    "file_path": "/path2",
                },
                {"id": "job3", "status": "pending", "duration": 3},
                {"id": "job4", "status": "failed", "duration": 7},
            ]

            usage = session_isolation.get_session_resource_usage("test-session")

            assert usage["session_id"] == "test-session"
            assert usage["total_jobs"] == 4
            assert usage["completed_jobs"] == 2
            assert usage["pending_jobs"] == 1
            assert usage["failed_jobs"] == 1
            assert usage["success_rate"] == 50.0  # 2/4 * 100
            assert (
                usage["estimated_storage_bytes"] == 15 * 1024 * 1024
            )  # 15MB (5+10 seconds)

    def test_get_session_resource_usage_invalid_session(
        self, session_isolation, mock_session_manager
    ):
        """Test resource usage for invalid session."""
        mock_session_manager.validate_session.return_value = (False, "Invalid session")

        usage = session_isolation.get_session_resource_usage("invalid-session")

        assert "error" in usage
        assert "Invalid session" in usage["error"]

    def test_cleanup_session_data_success(self, session_isolation, mock_job_repository):
        """Test successful session data cleanup."""
        # Mock get_session_jobs to return jobs
        with patch.object(session_isolation, "get_session_jobs") as mock_get_jobs:
            mock_get_jobs.return_value = [
                {"id": "job1", "file_path": "/tmp/video1.mp4"},
                {"id": "job2", "file_path": "/tmp/video2.mp4"},
                {"id": "job3", "file_path": None},  # No file
            ]

            # Mock file operations
            with patch("os.path.exists", return_value=True):
                with patch("os.remove") as mock_remove:
                    mock_job_repository.delete_job.return_value = True

                    result = session_isolation.cleanup_session_data(
                        "test-session", include_files=True
                    )

                    assert result["session_id"] == "test-session"
                    assert result["jobs_found"] == 3
                    assert result["jobs_deleted"] == 3
                    assert result["files_deleted"] == 2  # Only 2 jobs had files
                    assert len(result["errors"]) == 0

                    # Verify file deletion calls
                    assert mock_remove.call_count == 2

    def test_cleanup_session_data_without_files(
        self, session_isolation, mock_job_repository
    ):
        """Test session data cleanup without file deletion."""
        with patch.object(session_isolation, "get_session_jobs") as mock_get_jobs:
            mock_get_jobs.return_value = [
                {"id": "job1", "file_path": "/tmp/video1.mp4"},
            ]

            mock_job_repository.delete_job.return_value = True

            result = session_isolation.cleanup_session_data(
                "test-session", include_files=False
            )

            assert result["jobs_deleted"] == 1
            assert result["files_deleted"] == 0  # Files not deleted

    def test_cleanup_session_data_with_errors(
        self, session_isolation, mock_job_repository
    ):
        """Test session data cleanup with errors."""
        with patch.object(session_isolation, "get_session_jobs") as mock_get_jobs:
            mock_get_jobs.return_value = [
                {"id": "job1", "file_path": "/tmp/video1.mp4"},
                {"id": "job2", "file_path": "/tmp/video2.mp4"},
            ]

            # Mock file deletion to fail
            with patch("os.path.exists", return_value=True):
                with patch("os.remove", side_effect=OSError("Permission denied")):
                    mock_job_repository.delete_job.return_value = True

                    result = session_isolation.cleanup_session_data(
                        "test-session", include_files=True
                    )

                    assert result["files_deleted"] == 0
                    assert len(result["errors"]) == 2  # 2 file deletion errors
                    assert "Permission denied" in str(result["errors"])

    def test_sanitize_job_data(self, session_isolation):
        """Test job data sanitization."""
        # Create mock job with various attributes
        mock_job = Mock()
        mock_job.id = "job123"
        mock_job.prompt = "test prompt"
        mock_job.status = "completed"
        mock_job.created_at = datetime.utcnow()
        mock_job.completed_at = datetime.utcnow()
        mock_job.error_message = None
        mock_job.file_path = "/tmp/video.mp4"
        mock_job.download_url = "https://example.com/video.mp4"
        mock_job.session_id = "session123"
        mock_job.queue_position = 1
        mock_job.priority = 5

        sanitized = session_isolation._sanitize_job_data(mock_job)

        # Verify expected fields are present
        assert sanitized["id"] == "job123"
        assert sanitized["prompt"] == "test prompt"
        assert sanitized["status"] == "completed"
        assert sanitized["file_path"] == "/tmp/video.mp4"
        assert sanitized["download_url"] == "https://example.com/video.mp4"
        assert sanitized["session_id"] == "session123"
        assert sanitized["queue_position"] == 1
        assert sanitized["priority"] == 5

        # Verify None values are excluded
        assert "error_message" not in sanitized

    def test_sanitize_job_data_minimal(self, session_isolation):
        """Test job data sanitization with minimal required fields."""
        mock_job = Mock()
        mock_job.id = "job123"
        mock_job.prompt = "test"
        mock_job.status = "pending"
        mock_job.created_at = datetime.utcnow()
        mock_job.completed_at = None
        mock_job.error_message = None
        mock_job.file_path = None
        mock_job.download_url = None

        # Mock job without session-specific attributes
        delattr(mock_job, "session_id")
        delattr(mock_job, "queue_position")
        delattr(mock_job, "priority")

        sanitized = session_isolation._sanitize_job_data(mock_job)

        # Should only contain non-None basic fields
        expected_fields = {"id", "prompt", "status", "created_at"}
        actual_fields = set(sanitized.keys())

        assert expected_fields.issubset(actual_fields)
        assert "session_id" not in sanitized  # Not present on mock job
        assert "error_message" not in sanitized  # None value excluded


@pytest.mark.unit
class TestSessionIsolationHelpers:
    """Test cases for SessionIsolation helper functions."""

    def test_get_session_isolation_singleton(self):
        """Test that get_session_isolation returns singleton instance."""
        with patch("src.session.isolation.get_session_manager"):
            with patch("src.api.job_repository.JobRepository"):
                isolation1 = get_session_isolation()
                isolation2 = get_session_isolation()

                # Should be the same instance
                assert isolation1 is isolation2


@pytest.mark.unit
class TestSessionIsolationIntegration:
    """Integration tests for SessionIsolation with realistic scenarios."""

    def test_multi_user_data_isolation(self):
        """Test data isolation between multiple users."""
        with patch("src.session.isolation.get_session_manager") as mock_get_manager:
            with patch("src.api.job_repository.JobRepository") as mock_repo_class:
                # Setup session manager
                mock_manager = Mock()
                mock_manager.validate_session.return_value = (True, "Valid session")
                mock_get_manager.return_value = mock_manager

                # Setup repository with jobs from different sessions
                mock_repo = Mock()

                user1_jobs = [
                    Mock(
                        id="job1", session_id="session1", file_path="/user1/video1.mp4"
                    ),
                    Mock(
                        id="job2", session_id="session1", file_path="/user1/video2.mp4"
                    ),
                ]

                user2_jobs = [
                    Mock(
                        id="job3", session_id="session2", file_path="/user2/video1.mp4"
                    ),
                ]

                # Mock repository methods
                mock_repo.get_jobs_by_session.side_effect = lambda session: {
                    "session1": user1_jobs,
                    "session2": user2_jobs,
                }.get(session, [])

                mock_repo.get_job_by_id.side_effect = lambda job_id: {
                    "job1": user1_jobs[0],
                    "job2": user1_jobs[1],
                    "job3": user2_jobs[0],
                }.get(job_id)

                mock_repo_class.return_value = mock_repo

                isolation = SessionIsolation()

                # Test job ownership isolation
                assert isolation.validate_job_ownership("session1", "job1") is True
                assert isolation.validate_job_ownership("session1", "job2") is True
                assert (
                    isolation.validate_job_ownership("session1", "job3") is False
                )  # Belongs to session2

                assert isolation.validate_job_ownership("session2", "job3") is True
                assert (
                    isolation.validate_job_ownership("session2", "job1") is False
                )  # Belongs to session1

                # Test file access isolation
                assert (
                    isolation.validate_session_file_access(
                        "session1", "/user1/video1.mp4"
                    )
                    is True
                )
                assert (
                    isolation.validate_session_file_access(
                        "session1", "/user2/video1.mp4"
                    )
                    is False
                )

                assert (
                    isolation.validate_session_file_access(
                        "session2", "/user2/video1.mp4"
                    )
                    is True
                )
                assert (
                    isolation.validate_session_file_access(
                        "session2", "/user1/video1.mp4"
                    )
                    is False
                )

    def test_session_cleanup_isolation(self):
        """Test that session cleanup only affects the target session."""
        with patch("src.session.isolation.get_session_manager") as mock_get_manager:
            with patch("src.api.job_repository.JobRepository") as mock_repo_class:
                # Setup
                mock_manager = Mock()
                mock_manager.validate_session.return_value = (True, "Valid session")
                mock_manager.deactivate_session.return_value = True
                mock_get_manager.return_value = mock_manager

                mock_repo = Mock()

                # Session1 has 2 jobs, Session2 has 1 job
                session1_jobs = [
                    {"id": "job1", "file_path": "/session1/video1.mp4"},
                    {"id": "job2", "file_path": "/session1/video2.mp4"},
                ]

                session2_jobs = [
                    {"id": "job3", "file_path": "/session2/video1.mp4"},
                ]

                def mock_get_session_jobs(session_id):
                    if session_id == "session1":
                        return session1_jobs
                    elif session_id == "session2":
                        return session2_jobs
                    return []

                mock_repo.delete_job.return_value = True
                mock_repo_class.return_value = mock_repo

                isolation = SessionIsolation()

                # Mock get_session_jobs method
                with patch.object(
                    isolation, "get_session_jobs", side_effect=mock_get_session_jobs
                ):
                    with patch("os.path.exists", return_value=True):
                        with patch("os.remove") as mock_remove:
                            # Cleanup session1
                            result = isolation.cleanup_session_data(
                                "session1", include_files=True
                            )

                            assert result["jobs_found"] == 2
                            assert result["jobs_deleted"] == 2
                            assert result["files_deleted"] == 2

                            # Verify only session1 jobs were deleted
                            delete_calls = [
                                call[0][0]
                                for call in mock_repo.delete_job.call_args_list
                            ]
                            assert "job1" in delete_calls
                            assert "job2" in delete_calls

                            # Verify only session1 files were deleted
                            remove_calls = [
                                call[0][0] for call in mock_remove.call_args_list
                            ]
                            assert "/session1/video1.mp4" in remove_calls
                            assert "/session1/video2.mp4" in remove_calls
                            assert "/session2/video1.mp4" not in remove_calls

    def test_resource_usage_calculation_accuracy(self):
        """Test accurate resource usage calculation for complex scenarios."""
        with patch("src.session.isolation.get_session_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.validate_session.return_value = (True, "Valid session")
            mock_manager.get_session.return_value = {
                "session_id": "test-session",
                "created_at": datetime.utcnow(),
                "last_active": datetime.utcnow(),
            }
            mock_get_manager.return_value = mock_manager

            isolation = SessionIsolation()

            # Complex job mix
            complex_jobs = [
                {
                    "id": "job1",
                    "status": "succeeded",
                    "duration": 10,
                    "file_path": "/video1.mp4",
                },
                {
                    "id": "job2",
                    "status": "succeeded",
                    "duration": 5,
                    "file_path": "/video2.mp4",
                },
                {"id": "job3", "status": "failed", "duration": 8},
                {"id": "job4", "status": "pending", "duration": 3},
                {"id": "job5", "status": "running", "duration": 15},
                {
                    "id": "job6",
                    "status": "succeeded",
                    "duration": 20,
                    "file_path": "/video3.mp4",
                },
            ]

            with patch.object(isolation, "get_session_jobs", return_value=complex_jobs):
                usage = isolation.get_session_resource_usage("test-session")

                # Verify calculations
                assert usage["total_jobs"] == 6
                assert usage["completed_jobs"] == 3  # succeeded status
                assert usage["pending_jobs"] == 1
                assert usage["running_jobs"] == 1
                assert usage["failed_jobs"] == 1
                assert usage["success_rate"] == 50.0  # 3/6 * 100

                # Storage calculation: 10 + 5 + 20 = 35 seconds = 35MB
                expected_storage_mb = 35.0
                assert usage["estimated_storage_mb"] == expected_storage_mb
