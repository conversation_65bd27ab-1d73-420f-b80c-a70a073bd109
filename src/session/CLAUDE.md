# Session Module Documentation

## Overview

Production-ready session management system with cryptographically secure session IDs, user data isolation, IP-based validation, and automatic cleanup. Supports multi-user workflows with session-based job isolation and resource management.

## Module Structure

```
src/session/
├── manager.py          # Session creation and validation
├── isolation.py        # User data isolation patterns
└── tests/              # Co-located session tests
    ├── test_manager.py
    └── test_isolation.py
```

## Session Management

### SessionManager Class
```python
# src/session/manager.py
import secrets
import time
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class SessionData:
    """Session data container."""
    session_id: str
    client_ip: str
    created_at: datetime
    last_activity: datetime
    user_agent: Optional[str] = None
    active_jobs: int = 0
    max_jobs: int = 3

class SessionManager:
    """Manages user sessions with security and isolation."""
    
    def __init__(self, redis_client, session_lifetime_hours: int = 24):
        self.redis_client = redis_client
        self.session_lifetime = session_lifetime_hours * 3600  # Convert to seconds
        self.max_sessions_per_ip = 10
        self.session_prefix = "session:"
        self.ip_sessions_prefix = "ip_sessions:"
    
    def create_session(self, client_ip: str, user_agent: Optional[str] = None) -> Tuple[str, SessionData]:
        """
        Create new cryptographically secure session.
        
        Args:
            client_ip: Client IP address for validation
            user_agent: Optional user agent string
            
        Returns:
            Tuple of (session_id, session_data)
            
        Raises:
            ValueError: If IP has too many active sessions
        """
        # Check IP session limit
        if not self._check_ip_session_limit(client_ip):
            raise ValueError(f"Too many active sessions for IP {client_ip}")
        
        # Generate cryptographically secure session ID
        session_id = self._generate_secure_session_id()
        
        # Create session data
        now = datetime.utcnow()
        session_data = SessionData(
            session_id=session_id,
            client_ip=client_ip,
            created_at=now,
            last_activity=now,
            user_agent=user_agent,
            active_jobs=0,
            max_jobs=3
        )
        
        # Store session in Redis
        self._store_session(session_data)
        self._track_ip_session(client_ip, session_id)
        
        return session_id, session_data
    
    def get_session(self, session_id: str) -> Optional[SessionData]:
        """Get session data by ID."""
        session_key = f"{self.session_prefix}{session_id}"
        session_data = self.redis_client.hgetall(session_key)
        
        if not session_data:
            return None
        
        # Convert Redis hash to SessionData
        return SessionData(
            session_id=session_id,
            client_ip=session_data[b'client_ip'].decode(),
            created_at=datetime.fromisoformat(session_data[b'created_at'].decode()),
            last_activity=datetime.fromisoformat(session_data[b'last_activity'].decode()),
            user_agent=session_data.get(b'user_agent', b'').decode() or None,
            active_jobs=int(session_data.get(b'active_jobs', 0)),
            max_jobs=int(session_data.get(b'max_jobs', 3))
        )
    
    def validate_session(self, session_id: str, client_ip: str) -> bool:
        """
        Validate session ID and IP consistency.
        
        Args:
            session_id: Session ID to validate
            client_ip: Client IP to verify against session
            
        Returns:
            bool: True if session is valid and IP matches
        """
        session_data = self.get_session(session_id)
        
        if not session_data:
            return False
        
        # Check IP consistency
        if session_data.client_ip != client_ip:
            return False
        
        # Check session expiration
        if self._is_session_expired(session_data):
            self.delete_session(session_id)
            return False
        
        # Update last activity
        self._update_last_activity(session_id)
        
        return True
    
    def get_or_create_session(self, client_ip: str, user_agent: Optional[str] = None) -> Tuple[str, SessionData]:
        """
        Get existing session for IP or create new one.
        
        Args:
            client_ip: Client IP address
            user_agent: Optional user agent string
            
        Returns:
            Tuple of (session_id, session_data)
        """
        # Try to find existing active session for IP
        existing_sessions = self._get_ip_sessions(client_ip)
        
        for session_id in existing_sessions:
            session_data = self.get_session(session_id)
            if session_data and not self._is_session_expired(session_data):
                # Update last activity and return existing session
                self._update_last_activity(session_id)
                return session_id, session_data
        
        # No valid existing session, create new one
        return self.create_session(client_ip, user_agent)
    
    def delete_session(self, session_id: str) -> bool:
        """Delete session and cleanup related data."""
        session_data = self.get_session(session_id)
        if not session_data:
            return False
        
        # Remove session data
        session_key = f"{self.session_prefix}{session_id}"
        self.redis_client.delete(session_key)
        
        # Remove from IP tracking
        self._untrack_ip_session(session_data.client_ip, session_id)
        
        return True
    
    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions and return count."""
        cleaned_count = 0
        
        # Get all session keys
        session_pattern = f"{self.session_prefix}*"
        session_keys = self.redis_client.keys(session_pattern)
        
        for session_key in session_keys:
            session_id = session_key.decode().replace(self.session_prefix, "")
            session_data = self.get_session(session_id)
            
            if session_data and self._is_session_expired(session_data):
                self.delete_session(session_id)
                cleaned_count += 1
        
        return cleaned_count
    
    def _generate_secure_session_id(self) -> str:
        """Generate cryptographically secure session ID."""
        # Use 32 bytes (256 bits) of secure random data
        return secrets.token_urlsafe(32)
    
    def _store_session(self, session_data: SessionData) -> None:
        """Store session data in Redis."""
        session_key = f"{self.session_prefix}{session_data.session_id}"
        
        session_hash = {
            'client_ip': session_data.client_ip,
            'created_at': session_data.created_at.isoformat(),
            'last_activity': session_data.last_activity.isoformat(),
            'user_agent': session_data.user_agent or '',
            'active_jobs': session_data.active_jobs,
            'max_jobs': session_data.max_jobs
        }
        
        self.redis_client.hset(session_key, mapping=session_hash)
        self.redis_client.expire(session_key, self.session_lifetime)
    
    def _check_ip_session_limit(self, client_ip: str) -> bool:
        """Check if IP has exceeded session limit."""
        ip_sessions = self._get_ip_sessions(client_ip)
        return len(ip_sessions) < self.max_sessions_per_ip
    
    def _track_ip_session(self, client_ip: str, session_id: str) -> None:
        """Track session for IP address."""
        ip_key = f"{self.ip_sessions_prefix}{client_ip}"
        self.redis_client.sadd(ip_key, session_id)
        self.redis_client.expire(ip_key, self.session_lifetime + 3600)  # Extra hour buffer
    
    def _untrack_ip_session(self, client_ip: str, session_id: str) -> None:
        """Remove session from IP tracking."""
        ip_key = f"{self.ip_sessions_prefix}{client_ip}"
        self.redis_client.srem(ip_key, session_id)
    
    def _get_ip_sessions(self, client_ip: str) -> set:
        """Get all sessions for IP address."""
        ip_key = f"{self.ip_sessions_prefix}{client_ip}"
        sessions = self.redis_client.smembers(ip_key)
        return {session.decode() for session in sessions}
    
    def _is_session_expired(self, session_data: SessionData) -> bool:
        """Check if session has expired."""
        now = datetime.utcnow()
        expiry_time = session_data.last_activity + timedelta(seconds=self.session_lifetime)
        return now > expiry_time
    
    def _update_last_activity(self, session_id: str) -> None:
        """Update session last activity timestamp."""
        session_key = f"{self.session_prefix}{session_id}"
        now = datetime.utcnow().isoformat()
        self.redis_client.hset(session_key, 'last_activity', now)
```

## User Data Isolation

### SessionIsolation Class
```python
# src/session/isolation.py
from typing import Dict, List, Any, Optional
from src.core.interfaces import JobRepositoryInterface

class SessionIsolation:
    """Provides user data isolation based on sessions."""
    
    def __init__(self, job_repository: JobRepositoryInterface):
        self.job_repository = job_repository
    
    def get_session_jobs(self, session_id: str) -> List[Dict[str, Any]]:
        """Get all jobs for a specific session."""
        return self.job_repository.get_jobs_by_session(session_id)
    
    def get_session_active_jobs(self, session_id: str) -> List[Dict[str, Any]]:
        """Get active jobs for session."""
        all_jobs = self.get_session_jobs(session_id)
        return [job for job in all_jobs if job['status'] in ['pending', 'running']]
    
    def can_submit_job(self, session_id: str, max_concurrent: int = 3) -> bool:
        """Check if session can submit new job."""
        active_jobs = self.get_session_active_jobs(session_id)
        return len(active_jobs) < max_concurrent
    
    def get_session_statistics(self, session_id: str) -> Dict[str, Any]:
        """Get comprehensive session statistics."""
        all_jobs = self.get_session_jobs(session_id)
        active_jobs = self.get_session_active_jobs(session_id)
        
        # Calculate statistics
        total_jobs = len(all_jobs)
        succeeded_jobs = len([job for job in all_jobs if job['status'] == 'succeeded'])
        failed_jobs = len([job for job in all_jobs if job['status'] == 'failed'])
        
        return {
            'session_id': session_id,
            'total_jobs': total_jobs,
            'active_jobs': len(active_jobs),
            'succeeded_jobs': succeeded_jobs,
            'failed_jobs': failed_jobs,
            'success_rate': succeeded_jobs / total_jobs if total_jobs > 0 else 0,
            'can_submit_more': self.can_submit_job(session_id),
            'queue_position': self._calculate_queue_position(active_jobs)
        }
    
    def cleanup_session_data(self, session_id: str) -> Dict[str, int]:
        """Clean up all data for a session."""
        jobs = self.get_session_jobs(session_id)
        
        # Cancel active jobs
        cancelled_jobs = 0
        for job in jobs:
            if job['status'] in ['pending', 'running']:
                self.job_repository.update_job_status(job['job_id'], 'cancelled')
                cancelled_jobs += 1
        
        # Clean up completed job files (if needed)
        cleaned_files = self._cleanup_session_files(session_id, jobs)
        
        return {
            'cancelled_jobs': cancelled_jobs,
            'cleaned_files': cleaned_files,
            'total_jobs': len(jobs)
        }
    
    def _calculate_queue_position(self, active_jobs: List[Dict[str, Any]]) -> int:
        """Calculate position in queue for session."""
        pending_jobs = [job for job in active_jobs if job['status'] == 'pending']
        return len(pending_jobs)
    
    def _cleanup_session_files(self, session_id: str, jobs: List[Dict[str, Any]]) -> int:
        """Clean up files associated with session jobs."""
        import os
        cleaned_count = 0
        
        for job in jobs:
            if job.get('file_path') and os.path.exists(job['file_path']):
                try:
                    os.remove(job['file_path'])
                    cleaned_count += 1
                except OSError:
                    pass  # File already removed or permission issue
        
        return cleaned_count
```

## Multi-User Patterns

### Session-Based Job Processing
```python
# Multi-user patterns with session isolation
from src.session.manager import SessionManager
from src.session.isolation import SessionIsolation

class MultiUserWorkflow:
    """Handles multi-user workflow with session isolation."""
    
    def __init__(self, session_manager: SessionManager, session_isolation: SessionIsolation):
        self.session_manager = session_manager
        self.session_isolation = session_isolation
    
    def process_user_request(self, client_ip: str, user_agent: str, request_data: dict) -> dict:
        """Process user request with session management."""
        # Get or create session
        session_id, session_data = self.session_manager.get_or_create_session(client_ip, user_agent)
        
        # Check if user can submit job
        if not self.session_isolation.can_submit_job(session_id):
            return {
                'error': 'Too many active jobs',
                'session_id': session_id,
                'active_jobs': len(self.session_isolation.get_session_active_jobs(session_id))
            }
        
        # Process the request with session context
        return self._process_with_session(session_id, request_data)
    
    def get_user_status(self, session_id: str, client_ip: str) -> dict:
        """Get user status with validation."""
        # Validate session
        if not self.session_manager.validate_session(session_id, client_ip):
            return {'error': 'Invalid session'}
        
        # Get session statistics
        return self.session_isolation.get_session_statistics(session_id)
    
    def _process_with_session(self, session_id: str, request_data: dict) -> dict:
        """Process request with session context."""
        # Implementation depends on specific use case
        return {
            'session_id': session_id,
            'status': 'processed',
            'data': request_data
        }
```

## Configuration

### Environment Variables
```bash
# Session Management
SESSION_LIFETIME_HOURS=24
SESSION_CLEANUP_INTERVAL_HOURS=6
MAX_SESSIONS_PER_IP=10

# Multi-User Queue Management
MAX_CONCURRENT_JOBS_PER_SESSION=3

# Redis Configuration for Sessions
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### Factory Pattern
```python
from src.config.environments import get_environment_config
from redis import Redis

def create_session_manager() -> SessionManager:
    """Factory function to create configured session manager."""
    config = get_environment_config()
    
    redis_client = Redis(
        host=config.redis_host,
        port=config.redis_port,
        db=config.redis_db,
        decode_responses=False
    )
    
    return SessionManager(
        redis_client=redis_client,
        session_lifetime_hours=config.session_lifetime_hours
    )
```

## Security Features

### Cryptographic Session IDs
```python
import secrets
import base64

def generate_secure_session_id() -> str:
    """Generate cryptographically secure session ID."""
    # 32 bytes = 256 bits of entropy
    random_bytes = secrets.token_bytes(32)
    
    # URL-safe base64 encoding
    session_id = base64.urlsafe_b64encode(random_bytes).decode('ascii')
    
    # Remove padding for cleaner URLs
    return session_id.rstrip('=')

def validate_session_id_format(session_id: str) -> bool:
    """Validate session ID format for security."""
    if not session_id:
        return False
    
    # Check length (32 bytes -> ~43 chars in base64)
    if len(session_id) < 40 or len(session_id) > 50:
        return False
    
    # Check for valid base64 characters
    valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_')
    return all(c in valid_chars for c in session_id)
```

### IP Validation
```python
def validate_ip_consistency(session_data: SessionData, request_ip: str) -> bool:
    """Validate IP consistency with security considerations."""
    # Exact IP match required
    if session_data.client_ip != request_ip:
        return False
    
    # Additional security checks can be added here:
    # - Geolocation consistency
    # - User agent consistency
    # - Rate limiting per IP
    
    return True

def is_ip_suspicious(client_ip: str, redis_client) -> bool:
    """Check if IP shows suspicious activity."""
    # Check for rapid session creation
    ip_key = f"ip_activity:{client_ip}"
    recent_activity = redis_client.zcount(ip_key, time.time() - 300, time.time())
    
    if recent_activity > 10:  # More than 10 actions in 5 minutes
        return True
    
    # Track current activity
    redis_client.zadd(ip_key, {str(time.time()): time.time()})
    redis_client.expire(ip_key, 3600)  # 1 hour
    
    return False
```

## Testing Patterns

### Session Manager Testing
```python
import pytest
from unittest.mock import Mock
from src.session.manager import SessionManager, SessionData

class TestSessionManager:
    """Test session management functionality."""
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        return Mock()
    
    @pytest.fixture
    def session_manager(self, redis_mock):
        """Create session manager with mocked Redis."""
        return SessionManager(redis_mock, session_lifetime_hours=24)
    
    def test_create_session(self, session_manager):
        """Test session creation."""
        client_ip = "***********"
        user_agent = "Test Browser"
        
        session_id, session_data = session_manager.create_session(client_ip, user_agent)
        
        # Verify session ID is secure
        assert len(session_id) >= 40
        assert session_data.client_ip == client_ip
        assert session_data.user_agent == user_agent
        assert session_data.active_jobs == 0
    
    def test_session_validation(self, session_manager, redis_mock):
        """Test session validation."""
        # Mock Redis response for valid session
        redis_mock.hgetall.return_value = {
            b'client_ip': b'***********',
            b'created_at': b'2023-01-01T00:00:00',
            b'last_activity': b'2023-01-01T00:30:00',
            b'user_agent': b'Test Browser',
            b'active_jobs': b'0',
            b'max_jobs': b'3'
        }
        
        # Test validation with correct IP
        is_valid = session_manager.validate_session("test_session", "***********")
        assert is_valid is True
        
        # Test validation with incorrect IP
        is_valid = session_manager.validate_session("test_session", "***********")
        assert is_valid is False
```

### Isolation Testing
```python
class TestSessionIsolation:
    """Test session isolation functionality."""
    
    @pytest.fixture
    def job_repository_mock(self):
        """Mock job repository."""
        return Mock()
    
    @pytest.fixture
    def session_isolation(self, job_repository_mock):
        """Create session isolation with mocked repository."""
        return SessionIsolation(job_repository_mock)
    
    def test_get_session_jobs(self, session_isolation, job_repository_mock):
        """Test getting session jobs."""
        # Mock repository response
        mock_jobs = [
            {'job_id': 'job1', 'status': 'completed', 'session_id': 'session1'},
            {'job_id': 'job2', 'status': 'running', 'session_id': 'session1'}
        ]
        job_repository_mock.get_jobs_by_session.return_value = mock_jobs
        
        jobs = session_isolation.get_session_jobs('session1')
        assert len(jobs) == 2
        job_repository_mock.get_jobs_by_session.assert_called_once_with('session1')
    
    def test_can_submit_job(self, session_isolation, job_repository_mock):
        """Test job submission limits."""
        # Mock active jobs at limit
        mock_jobs = [
            {'job_id': f'job{i}', 'status': 'running', 'session_id': 'session1'}
            for i in range(3)
        ]
        job_repository_mock.get_jobs_by_session.return_value = mock_jobs
        
        can_submit = session_isolation.can_submit_job('session1', max_concurrent=3)
        assert can_submit is False
```

## Performance Optimization

### Session Caching
```python
from functools import lru_cache
import time

class CachedSessionManager(SessionManager):
    """Session manager with local caching for performance."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._session_cache = {}
        self._cache_ttl = 300  # 5 minutes
    
    def get_session(self, session_id: str) -> Optional[SessionData]:
        """Get session with local caching."""
        # Check local cache first
        if session_id in self._session_cache:
            cached_data, timestamp = self._session_cache[session_id]
            if time.time() - timestamp < self._cache_ttl:
                return cached_data
        
        # Fetch from Redis
        session_data = super().get_session(session_id)
        
        # Cache the result
        if session_data:
            self._session_cache[session_id] = (session_data, time.time())
        
        return session_data
    
    def invalidate_cache(self, session_id: str) -> None:
        """Invalidate cached session."""
        self._session_cache.pop(session_id, None)
```

### Batch Operations
```python
def cleanup_multiple_sessions(self, session_ids: List[str]) -> Dict[str, int]:
    """Clean up multiple sessions efficiently."""
    results = {'deleted': 0, 'not_found': 0}
    
    # Use Redis pipeline for batch operations
    with self.redis_client.pipeline() as pipe:
        pipe.multi()
        
        for session_id in session_ids:
            session_key = f"{self.session_prefix}{session_id}"
            pipe.delete(session_key)
        
        # Execute all deletions
        delete_results = pipe.execute()
    
    # Process results
    for deleted_count in delete_results:
        if deleted_count > 0:
            results['deleted'] += 1
        else:
            results['not_found'] += 1
    
    return results
```

## Monitoring and Health

### Session Metrics
```python
def get_session_metrics(self) -> Dict[str, Any]:
    """Get session system metrics."""
    # Count active sessions
    session_pattern = f"{self.session_prefix}*"
    all_sessions = self.redis_client.keys(session_pattern)
    
    # Count sessions by IP
    ip_pattern = f"{self.ip_sessions_prefix}*"
    ip_keys = self.redis_client.keys(ip_pattern)
    
    # Calculate average sessions per IP
    total_ips = len(ip_keys)
    avg_sessions_per_ip = len(all_sessions) / total_ips if total_ips > 0 else 0
    
    return {
        'total_active_sessions': len(all_sessions),
        'total_unique_ips': total_ips,
        'avg_sessions_per_ip': avg_sessions_per_ip,
        'redis_memory_usage': self._get_redis_memory_usage(),
        'session_cleanup_due': self._sessions_needing_cleanup()
    }

def _sessions_needing_cleanup(self) -> int:
    """Count sessions that need cleanup."""
    cleanup_count = 0
    session_pattern = f"{self.session_prefix}*"
    
    for session_key in self.redis_client.keys(session_pattern):
        session_id = session_key.decode().replace(self.session_prefix, "")
        session_data = self.get_session(session_id)
        
        if session_data and self._is_session_expired(session_data):
            cleanup_count += 1
    
    return cleanup_count
```

## Best Practices

1. **Secure Session IDs**: Use cryptographically secure random generation
2. **IP Validation**: Always validate IP consistency for security
3. **Session Expiration**: Implement automatic cleanup of expired sessions
4. **Resource Limits**: Enforce limits on sessions per IP and jobs per session
5. **Data Isolation**: Ensure complete isolation between user sessions
6. **Monitoring**: Track session metrics and unusual activity
7. **Testing**: Test session security and isolation thoroughly
8. **Caching**: Use local caching for frequently accessed sessions
9. **Batch Operations**: Use Redis pipelines for efficiency
10. **Error Handling**: Handle Redis connection failures gracefully

## Troubleshooting

### Common Issues

**Session Not Found**
```bash
# Check Redis for session
redis-cli hgetall session:your_session_id

# Check IP tracking
redis-cli smembers ip_sessions:***********

# List all sessions
redis-cli keys "session:*" | head -10
```

**Too Many Sessions**
```bash
# Check sessions per IP
python -c "
from src.session.manager import SessionManager
from redis import Redis
manager = SessionManager(Redis())
metrics = manager.get_session_metrics()
print(f'Total sessions: {metrics[\"total_active_sessions\"]}')
print(f'Average per IP: {metrics[\"avg_sessions_per_ip\"]:.2f}')
"

# Clean up expired sessions
python -c "
from src.session.manager import SessionManager
from redis import Redis
manager = SessionManager(Redis())
cleaned = manager.cleanup_expired_sessions()
print(f'Cleaned up {cleaned} expired sessions')
"
```

**Redis Memory Issues**
```bash
# Check Redis memory usage
redis-cli info memory

# Count session-related keys
redis-cli eval "return #redis.call('keys', 'session:*')" 0
redis-cli eval "return #redis.call('keys', 'ip_sessions:*')" 0

# Clean up if needed
redis-cli eval "
local keys = redis.call('keys', 'session:*')
for i=1,#keys do
    local ttl = redis.call('ttl', keys[i])
    if ttl < 0 then
        redis.call('del', keys[i])
    end
end
return #keys
" 0
```

### Development Commands
```bash
# Test session management
uv run pytest src/session/tests/ -v

# Test session isolation
uv run pytest src/session/tests/test_isolation.py -v

# Create test session
python -c "
from src.session.manager import SessionManager
from redis import Redis
manager = SessionManager(Redis())
session_id, data = manager.create_session('127.0.0.1', 'Test')
print(f'Created session: {session_id}')
print(f'Session data: {data}')
"
```