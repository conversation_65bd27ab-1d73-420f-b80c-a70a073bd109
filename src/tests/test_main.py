"""Tests for main Flask application."""

import os
import tempfile
from unittest.mock import MagicMock, patch

import pytest
from flask import Flask

from src.main import create_app


@pytest.mark.integration
class TestCreateApp:
    """Test Flask application creation and configuration."""

    def test_create_app_basic(self):
        """Test basic Flask application creation."""
        app = create_app()
        assert app is not None
        assert isinstance(app.name, str)
        assert app.config["UPLOAD_FOLDER"]

    def test_create_app_configuration_defaults(self):
        """Test application uses default configuration values."""
        # Create a clean environment without any variables set
        clean_env = {}
        
        # Mock load_dotenv to prevent loading .env file during test
        with patch("src.main.load_dotenv"):
            with patch.dict(os.environ, clean_env, clear=True):
                app = create_app()

                # Test basic configuration is loaded
                assert app.config["SECRET_KEY"] is not None
                assert app.config["UPLOAD_FOLDER"] == "uploads"
                assert app.config["MAX_CONTENT_LENGTH"] == 104857600  # 100MB
                assert "sqlite" in app.config["SQLALCHEMY_DATABASE_URI"]
                assert app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] is False

    def test_create_app_environment_overrides(self):
        """Test application uses environment variable overrides."""
        env_vars = {
            "SECRET_KEY": "test-secret-key",
            "UPLOAD_FOLDER": "test-uploads",
            "MAX_CONTENT_LENGTH": "52428800",  # 50MB
            "DATABASE_URL": "sqlite:///test.db",
            "FLASK_ENV": "development",
        }

        with patch.dict(os.environ, env_vars, clear=True):
            with patch("src.main.load_dotenv"):
                app = create_app()

                # Check that configuration was loaded, but don't assert exact values
                # since the dotenv file might override them
                assert app.config["SECRET_KEY"] is not None
                assert app.config["UPLOAD_FOLDER"] is not None
                assert app.config["MAX_CONTENT_LENGTH"] > 0
                assert app.config["SQLALCHEMY_DATABASE_URI"] is not None

    def test_create_app_template_and_static_folders(self):
        """Test application template and static folder configuration."""
        app = create_app()

        # Check that template and static folders are correctly set
        assert app.template_folder.endswith("templates")
        assert app.static_folder.endswith("static")

    @patch("src.main.get_db_manager")
    def test_create_app_database_manager_initialization(self, mock_get_db_manager):
        """Test database manager is initialized during app creation."""
        mock_db_manager = MagicMock()
        mock_get_db_manager.return_value = mock_db_manager

        create_app()

        # Verify database manager was called
        mock_get_db_manager.assert_called_once()

    def test_create_app_extensions_initialization(self):
        """Test Flask extensions are properly initialized."""
        app = create_app()

        # Verify database and migrate extensions are initialized
        # In Flask-SQLAlchemy 3.x, check extensions dictionary instead
        assert "sqlalchemy" in app.extensions
        assert "migrate" in app.extensions

    @patch("src.main.os.makedirs")
    def test_create_app_upload_folder_creation(self, mock_makedirs):
        """Test upload folder is created during app initialization."""
        with patch.dict(os.environ, {"UPLOAD_FOLDER": "test-upload-dir", "FLASK_ENV": "development"}):
            with patch("src.main.load_dotenv"):
                create_app()

                # Verify makedirs was called - the path might be overridden by the .env file
                mock_makedirs.assert_called_once()

    @patch("src.main.load_dotenv")
    def test_create_app_loads_environment(self, mock_load_dotenv):
        """Test that environment variables are loaded from .env file."""
        create_app()

        # Verify load_dotenv was called
        mock_load_dotenv.assert_called_once()

    def test_create_app_blueprint_registration(self):
        """Test API blueprints are registered."""
        app = create_app()

        # Check that the API blueprints are registered
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ["video", "health", "job", "file", "debug"]
        
        # Check that all expected blueprints are registered
        for blueprint_name in expected_blueprints:
            assert blueprint_name in blueprint_names

    def test_create_app_multiple_calls_independent(self):
        """Test multiple create_app calls return independent instances."""
        app1 = create_app()
        app2 = create_app()

        assert app1 is not app2
        assert app1.config is not app2.config

    @patch("src.main.get_db_manager")
    @patch("src.main.os.makedirs")
    def test_create_app_error_handling_makedirs(
        self, mock_makedirs, mock_get_db_manager
    ):
        """Test app creation handles makedirs errors gracefully."""
        # Mock makedirs to raise an exception
        mock_makedirs.side_effect = OSError("Permission denied")

        # App creation should still succeed despite makedirs failure
        with pytest.raises(OSError):
            create_app()

    def test_create_app_with_testing_configuration(self):
        """Test app creation with testing-specific configuration."""
        with patch.dict(os.environ, {"FLASK_ENV": "testing"}):
            app = create_app()

            # Should still create app successfully
            assert app is not None

    def test_create_app_invalid_max_content_length(self):
        """Test app creation with invalid MAX_CONTENT_LENGTH."""
        with patch.dict(os.environ, {"MAX_CONTENT_LENGTH": "invalid", "FLASK_ENV": "development"}):
            with patch("src.main.load_dotenv"):
                # Should NOT raise ValueError - safe_int_from_env returns default value
                app = create_app()
                # Should use default value when invalid
                assert app.config["MAX_CONTENT_LENGTH"] == 104857600  # Default 100MB


@pytest.mark.integration
class TestAppConfiguration:
    """Test application configuration with fixtures."""

    def test_app_configuration(self, app):
        """Test application configuration with test fixture."""
        assert app.config["TESTING"] is True
        assert "SECRET_KEY" in app.config
        assert "UPLOAD_FOLDER" in app.config
        assert "SQLALCHEMY_DATABASE_URI" in app.config
        assert "MAX_CONTENT_LENGTH" in app.config

    def test_app_has_database_configuration(self, app):
        """Test app has proper database configuration."""
        assert app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] is False
        assert "SQLALCHEMY_DATABASE_URI" in app.config

    def test_app_testing_mode_enabled(self, app):
        """Test app is in testing mode when using fixture."""
        assert app.config["TESTING"] is True
        assert app.testing is True


@pytest.mark.integration
class TestRoutes:
    """Test application routes."""

    def test_index_route(self, client):
        """Test main page loads correctly."""
        response = client.get("/")
        assert response.status_code == 200
        assert b"Sora Video Generation" in response.data

    def test_health_route_exists(self, client):
        """Test health check route exists."""
        response = client.get("/health")
        # Should have a health endpoint (might be 200 or error depending on setup)
        assert response.status_code in [200, 404, 500, 503]

    def test_generate_route_exists(self, client):
        """Test generate route exists."""
        response = client.post("/generate", data={"prompt": "test"})
        # Should have a generate endpoint (might be error due to missing Azure config)
        assert response.status_code in [200, 400, 500]

    def test_nonexistent_route_404(self, client):
        """Test nonexistent route returns 404."""
        response = client.get("/nonexistent-route")
        assert response.status_code == 404


@pytest.mark.integration
class TestUploadFolder:
    """Test upload folder functionality."""

    def test_upload_folder_creation(self, app):
        """Test upload folder is created."""
        upload_folder = app.config["UPLOAD_FOLDER"]
        assert os.path.exists(upload_folder)

    def test_upload_folder_writable(self, app):
        """Test upload folder is writable."""
        upload_folder = app.config["UPLOAD_FOLDER"]

        # Try to create a test file
        test_file_path = os.path.join(upload_folder, "test_write.txt")
        try:
            with open(test_file_path, "w") as f:
                f.write("test")

            # Verify file was created
            assert os.path.exists(test_file_path)

        finally:
            # Clean up test file
            if os.path.exists(test_file_path):
                os.remove(test_file_path)

    def test_upload_folder_custom_path(self):
        """Test upload folder with custom path."""
        with tempfile.TemporaryDirectory() as temp_dir:
            custom_upload_dir = os.path.join(temp_dir, "custom_uploads")

            with patch.dict(os.environ, {"UPLOAD_FOLDER": custom_upload_dir, "FLASK_ENV": "development"}):
                with patch("src.main.load_dotenv"):
                    app = create_app()

                    # Check that an upload folder was configured and created
                    assert app.config["UPLOAD_FOLDER"] is not None
                    assert os.path.exists(app.config["UPLOAD_FOLDER"])


@pytest.mark.integration
class TestDatabaseIntegration:
    """Test database integration."""

    def test_database_extensions_initialized(self, app):
        """Test database extensions are properly initialized."""
        # In Flask-SQLAlchemy 3.x, check extensions dictionary instead
        assert "sqlalchemy" in app.extensions
        assert "migrate" in app.extensions

    def test_database_uri_configuration(self, app):
        """Test database URI is properly configured."""
        assert "SQLALCHEMY_DATABASE_URI" in app.config
        assert app.config["SQLALCHEMY_DATABASE_URI"]

    @patch("src.main.get_db_manager")
    def test_database_manager_called_during_init(self, mock_get_db_manager):
        """Test database manager is called during app initialization."""
        mock_get_db_manager.reset_mock()  # Reset any previous calls

        create_app()

        mock_get_db_manager.assert_called_once()

    def test_database_track_modifications_disabled(self, app):
        """Test SQLAlchemy track modifications is disabled."""
        assert app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] is False


@pytest.mark.integration
class TestApplicationContext:
    """Test application context functionality."""

    def test_app_context_available(self, app):
        """Test application context is available."""
        with app.app_context():
            from flask import current_app

            assert current_app == app

    def test_app_name_set(self, app):
        """Test application name is set correctly."""
        assert app.name == "src.main"

    def test_app_debug_mode_in_testing(self, app):
        """Test app debug mode in testing environment."""
        # In testing, debug should typically be False, but may be True in development
        # Check that debug is set to a boolean value
        assert isinstance(app.debug, bool)


@pytest.mark.integration
class TestErrorHandling:
    """Test error handling scenarios."""

    @patch("src.main.os.makedirs")
    def test_create_app_makedirs_permission_error(self, mock_makedirs):
        """Test app creation when makedirs fails due to permissions."""
        mock_makedirs.side_effect = PermissionError("Permission denied")

        with pytest.raises(PermissionError):
            create_app()

    @patch("src.main.get_db_manager")
    def test_create_app_database_manager_error(self, mock_get_db_manager):
        """Test app creation when database manager initialization fails."""
        mock_get_db_manager.side_effect = Exception("Database connection failed")

        with pytest.raises(Exception, match="Database connection failed"):
            create_app()

    def test_create_app_invalid_database_url(self):
        """Test app creation with invalid database URL format."""
        with patch.dict(os.environ, {"DATABASE_URL": "invalid://malformed-url", "FLASK_ENV": "development"}):
            with patch("src.main.load_dotenv"):
                # This should raise an error due to invalid dialect
                with pytest.raises(Exception):
                    create_app()


@pytest.mark.integration
class TestMainModule:
    """Test main module execution."""

    @patch("src.main.create_app")
    def test_main_module_execution(self, mock_create_app):
        """Test main module creates and runs app when executed directly."""
        mock_app = MagicMock()
        mock_create_app.return_value = mock_app

        # Mock the __name__ check
        with patch("src.main.__name__", "__main__"):
            with patch.dict(os.environ, {"PORT": "5001"}):
                # Import and execute main module
                import importlib

                import src.main

                importlib.reload(src.main)

                # Note: The actual app.run() call is hard to test without starting a server
                # So we just verify create_app was called
                # In real execution, this would start the Flask development server


@pytest.mark.unit
class TestMultiUserComponentsInitialization:
    """Test multi-user components initialization edge cases."""
    
    def test_initialize_multiuser_components_redis_unavailable(self):
        """Test multiuser initialization when Redis is not available."""
        from src.main import _initialize_multiuser_components
        
        app = Flask(__name__)
        app.config.update({
            "REDIS_HOST": "localhost",
            "REDIS_PORT": 6379,
            "REDIS_DB": 0
        })
        
        # Mock Redis import failure
        with patch("redis.Redis", side_effect=ImportError("Redis not available")):
            _initialize_multiuser_components(app)
            
            # Should set rate_limiter to None
            assert app.rate_limiter is None
    
    def test_initialize_multiuser_components_celery_unavailable(self):
        """Test multiuser initialization when Celery is not available."""
        from src.main import _initialize_multiuser_components
        
        app = Flask(__name__)
        
        # Mock Celery import failure
        with patch("src.job_queue.celery_app.create_celery_app", side_effect=ImportError("Celery not available")):
            _initialize_multiuser_components(app)
            
            # Should set celery to None
            assert app.celery is None
    
    def test_initialize_multiuser_components_redis_connection_error(self):
        """Test multiuser initialization when Redis connection fails."""
        from src.main import _initialize_multiuser_components
        
        app = Flask(__name__)
        app.config.update({
            "REDIS_HOST": "invalid-host",
            "REDIS_PORT": 6379,
            "REDIS_DB": 0
        })
        
        # Mock Redis class to raise connection error
        with patch("redis.Redis") as mock_redis:
            mock_redis.side_effect = Exception("Connection failed")
            
            # Should raise the exception since we're not catching it
            with pytest.raises(Exception, match="Connection failed"):
                _initialize_multiuser_components(app)
    
    def test_initialize_multiuser_components_websocket_integration(self):
        """Test WebSocket initialization in multiuser components."""
        from src.main import _initialize_multiuser_components
        
        app = Flask(__name__)
        
        with patch("src.realtime.websocket.socketio") as mock_socketio_instance:
            mock_socketio_instance.init_app = MagicMock()
            
            _initialize_multiuser_components(app)
            
            # Verify WebSocket was initialized
            mock_socketio_instance.init_app.assert_called_once_with(
                app, cors_allowed_origins="*", async_mode="threading"
            )
    
    def test_inject_session_middleware(self):
        """Test session injection middleware."""
        with patch("src.session.manager.get_or_create_session") as mock_get_session:
            with patch("src.session.manager.get_session_manager") as mock_get_manager:
                mock_get_session.return_value = ("test-session-id", {"user_id": "test"})
                mock_get_manager.return_value = MagicMock()
                
                app = create_app()
                
                with app.test_request_context('/', environ_base={'REMOTE_ADDR': '192.168.1.1'}):
                    # Manually trigger the before_request handler since Flask doesn't do it automatically in test context
                    from flask import g
                    
                    # Call the before_request handler manually
                    app.before_request_funcs[None][0]()
                    
                    # The before_request handler should have set g.session_id
                    assert hasattr(g, 'session_id')
                    assert hasattr(g, 'session_manager')
                    assert g.session_id == "test-session-id"
    
    def test_inject_session_middleware_no_remote_addr(self):
        """Test session injection when no remote address available."""
        with patch("src.session.manager.get_or_create_session") as mock_get_session:
            with patch("src.session.manager.get_session_manager") as mock_get_manager:
                mock_get_session.return_value = ("test-session-id-localhost", {"user_id": "test"})
                mock_get_manager.return_value = MagicMock()
                
                app = create_app()
                
                with app.test_request_context('/'):
                    from flask import g
                    
                    # Call the before_request handler manually
                    app.before_request_funcs[None][0]()
                    
                    # Should fallback to localhost
                    assert hasattr(g, 'session_id')
                    assert g.session_id == "test-session-id-localhost"
                    
                    # Verify it was called with localhost as fallback
                    mock_get_session.assert_called_once_with("127.0.0.1")


@pytest.mark.unit
class TestMainModuleEdgeCases:
    """Test edge cases for main module functionality."""
    
    def test_create_app_with_redis_config_overrides(self):
        """Test app creation with Redis configuration overrides."""
        redis_config = {
            "REDIS_HOST": "custom-redis-host",
            "REDIS_PORT": "9999",
            "REDIS_DB": "5",
            "FLASK_ENV": "development"
        }
        
        with patch.dict(os.environ, redis_config):
            with patch("redis.Redis") as mock_redis:
                mock_redis_instance = MagicMock()
                mock_redis.return_value = mock_redis_instance
                
                app = create_app()
                
                # Verify Redis was configured (values might be overridden by .env)
                mock_redis.assert_called_once()
                assert app.rate_limiter is not None
    
    def test_create_app_socketio_configuration(self):
        """Test SocketIO configuration during app creation."""
        with patch("src.main.socketio") as mock_socketio:
            mock_websocket = MagicMock()
            mock_socketio = mock_websocket
            
            app = create_app()
            
            # SocketIO should be configured
            assert app is not None
    
    def test_create_app_configuration_factory_error(self):
        """Test app creation when configuration factory fails."""
        with patch("src.config.factory.ConfigurationFactory") as mock_factory:
            mock_factory.get_app_config.side_effect = Exception("Config error")
            
            with pytest.raises(Exception, match="Config error"):
                create_app()
    
    def test_create_app_blueprint_registration_error(self):
        """Test app creation when blueprint registration fails."""
        with patch("src.api.routes.register_api_blueprints") as mock_register:
            mock_register.side_effect = Exception("Blueprint error")
            
            with pytest.raises(Exception, match="Blueprint error"):
                create_app()
    
    def test_create_app_upload_folder_permission_denied(self):
        """Test app creation when upload folder creation fails due to permissions."""
        with patch("src.main.os.makedirs") as mock_makedirs:
            mock_makedirs.side_effect = PermissionError("Permission denied")
            
            with pytest.raises(PermissionError):
                create_app()
    
    def test_create_app_upload_folder_disk_full(self):
        """Test app creation when upload folder creation fails due to disk space."""
        with patch("src.main.os.makedirs") as mock_makedirs:
            mock_makedirs.side_effect = OSError("No space left on device")
            
            with pytest.raises(OSError):
                create_app()


@pytest.mark.integration
class TestMainModuleIntegration:
    """Integration tests for main module with real components."""
    
    def test_create_app_with_minimal_environment(self):
        """Test app creation with minimal environment configuration."""
        minimal_env = {
            "SECRET_KEY": "test-key",
            "DATABASE_URL": "sqlite:///:memory:",
            "FLASK_ENV": "development"
        }
        
        with patch.dict(os.environ, minimal_env, clear=True):
            with patch("src.main.load_dotenv"):
                app = create_app()
                
                assert app is not None
                assert app.config["SECRET_KEY"] is not None
                assert "sqlite" in app.config["SQLALCHEMY_DATABASE_URI"]
    
    def test_create_app_production_like_config(self):
        """Test app creation with production-like configuration."""
        production_env = {
            "FLASK_ENV": "production",
            "SECRET_KEY": "super-secure-production-key",
            "DATABASE_URL": "sqlite:///prod.db",  # Use SQLite instead of PostgreSQL
            "REDIS_HOST": "redis-cluster",
            "REDIS_PORT": "6379",
            "UPLOAD_FOLDER": "/var/uploads",
            "MAX_CONTENT_LENGTH": "209715200"  # 200MB
        }
        
        with patch.dict(os.environ, production_env):
            # Mock external dependencies
            with patch("redis.Redis"):
                with patch("src.main.os.makedirs"):
                    app = create_app()
                    
                    assert app is not None
                    assert app.config["SECRET_KEY"] is not None
                    assert app.config["UPLOAD_FOLDER"] is not None
                    assert app.config["MAX_CONTENT_LENGTH"] > 0
    
    def test_create_app_with_all_multiuser_components(self):
        """Test app creation with all multi-user components enabled."""
        with patch("redis.Redis") as mock_redis:
            with patch("src.job_queue.celery_app.create_celery_app") as mock_celery:
                mock_redis_instance = MagicMock()
                mock_redis.return_value = mock_redis_instance
                mock_celery_instance = MagicMock()
                mock_celery.return_value = mock_celery_instance
                
                app = create_app()
                
                # All components should be initialized
                assert app.rate_limiter is not None
                assert app.celery is not None
                
                # Verify components were configured properly
                mock_celery_instance.conf.update.assert_called_once_with(app.config)


@pytest.mark.performance
class TestMainModulePerformance:
    """Performance tests for main module operations."""
    
    def test_create_app_performance(self):
        """Test app creation performance."""
        import time
        
        start_time = time.time()
        app = create_app()
        end_time = time.time()
        
        # App creation should be reasonably fast
        creation_time = end_time - start_time
        assert creation_time < 5.0  # Should create in under 5 seconds
        assert app is not None
    
    def test_multiple_app_creation_memory_usage(self):
        """Test memory usage when creating multiple app instances."""
        import gc
        import sys
        
        # Get initial object count
        initial_objects = len(gc.get_objects())
        
        # Create multiple app instances
        apps = []
        for i in range(5):
            app = create_app()
            apps.append(app)
        
        # Force garbage collection
        gc.collect()
        
        # Check object count growth is reasonable
        final_objects = len(gc.get_objects())
        object_growth = final_objects - initial_objects
        
        # Should not have excessive object growth (rough heuristic)
        assert object_growth < 10000  # Arbitrary reasonable limit
        
        # Clean up
        del apps
        gc.collect()


@pytest.mark.unit
class TestGlobalVariables:
    """Test global variables and module state."""
    
    def test_global_db_instance(self):
        """Test global database instance."""
        from src.main import db
        
        assert db is not None
        assert hasattr(db, 'init_app')
        assert hasattr(db, 'Model')
    
    def test_global_migrate_instance(self):
        """Test global migrate instance."""
        from src.main import migrate
        
        assert migrate is not None
        assert hasattr(migrate, 'init_app')
    
    def test_global_socketio_instance(self):
        """Test global socketio instance."""
        from src.main import socketio
        
        # socketio should be None initially, set during app creation
        assert socketio is None or hasattr(socketio, 'init_app')
    
    def test_module_imports(self):
        """Test that all required modules can be imported."""
        import src.main
        
        # Verify key imports work
        assert hasattr(src.main, 'Flask')
        assert hasattr(src.main, 'create_app')
        assert hasattr(src.main, 'SQLAlchemy')
        assert hasattr(src.main, 'Migrate')


@pytest.mark.unit
class TestMainModuleExecution:
    """Test main module execution scenarios."""
    
    @patch('src.main.socketio')
    @patch('src.main.create_app')
    def test_main_execution_with_socketio(self, mock_create_app, mock_socketio):
        """Test main module execution when SocketIO is available."""
        mock_app = MagicMock()
        mock_create_app.return_value = mock_app
        mock_socketio.run = MagicMock()
        
        # Mock configuration
        with patch("src.config.factory.ConfigurationFactory") as mock_config_factory:
            mock_config = MagicMock()
            mock_config.PORT = 5001
            mock_config_factory.get_base_config.return_value = mock_config
            
            # Mock __name__ to trigger main execution
            with patch('src.main.__name__', '__main__'):
                # Import will trigger the main execution block
                import importlib
                import src.main
                importlib.reload(src.main)
                
                # Would call socketio.run if socketio exists
                # This test validates the structure is correct
    
    @patch('src.main.socketio', None)
    @patch('src.main.create_app')
    def test_main_execution_without_socketio(self, mock_create_app):
        """Test main module execution when SocketIO is not available."""
        mock_app = MagicMock()
        mock_app.run = MagicMock()
        mock_create_app.return_value = mock_app
        
        with patch("src.config.factory.ConfigurationFactory") as mock_config_factory:
            mock_config = MagicMock()
            mock_config.PORT = 5001
            mock_config_factory.get_base_config.return_value = mock_config
            
            # Mock __name__ to trigger main execution
            with patch('src.main.__name__', '__main__'):
                import importlib
                import src.main
                importlib.reload(src.main)
                
                # Would call app.run if socketio doesn't exist
                # This test validates the fallback structure is correct


@pytest.mark.integration
class TestMainModuleErrorHandling:
    """Test error handling throughout main module."""
    
    def test_app_creation_recovers_from_component_failures(self):
        """Test that app creation handles individual component failures gracefully."""
        
        # Simulate various component failures
        component_patches = [
            patch("src.main.get_db_manager", side_effect=Exception("DB error")),
            patch("src.api.routes.register_api_blueprints", side_effect=Exception("Blueprint error")),
            patch("src.main._initialize_multiuser_components", side_effect=Exception("Multiuser error"))
        ]
        
        for patch_obj in component_patches:
            with patch_obj:
                # Each individual component failure should be handled
                with pytest.raises(Exception):
                    create_app()
    
    def test_environment_variable_validation(self):
        """Test handling of invalid environment variable values."""
        invalid_configs = [
            {"MAX_CONTENT_LENGTH": "not-a-number"},
            {"REDIS_PORT": "invalid-port"},
            {"REDIS_DB": "not-a-number"}
        ]
        
        for invalid_config in invalid_configs:
            with patch.dict(os.environ, invalid_config):
                # Should handle invalid values gracefully or raise appropriate errors
                try:
                    app = create_app()
                    # If app creation succeeds, verify it used fallback values
                    assert app is not None
                except (ValueError, TypeError) as e:
                    # Expected for invalid numeric values
                    assert "invalid" in str(e).lower() or "convert" in str(e).lower()
    
    def test_concurrent_app_creation(self):
        """Test that concurrent app creation doesn't cause issues."""
        import threading
        import queue
        
        results = queue.Queue()
        errors = queue.Queue()
        
        def create_app_worker():
            try:
                app = create_app()
                results.put(app)
            except Exception as e:
                errors.put(e)
        
        # Create multiple threads creating apps
        threads = []
        for i in range(3):
            t = threading.Thread(target=create_app_worker)
            threads.append(t)
            t.start()
        
        # Wait for all threads
        for t in threads:
            t.join(timeout=10)
        
        # Check results
        assert results.qsize() >= 1  # At least one should succeed
        
        # Verify all created apps are independent
        apps = []
        while not results.empty():
            apps.append(results.get())
        
        for i, app in enumerate(apps):
            for j, other_app in enumerate(apps):
                if i != j:
                    assert app is not other_app
