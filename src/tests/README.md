# src/tests/ Module Documentation

**Global Testing Infrastructure for Sora Video Generation System**

## Overview

The `src/tests/` module contains **comprehensive, cross-cutting test suites** that validate system-wide behavior, performance, and reliability. These tests complement the co-located unit tests found in each module's `tests/` subdirectory by focusing on **integration, performance, security, and end-to-end scenarios**.

**Module Purpose**: Global testing infrastructure for cross-component validation, performance benchmarking, and production readiness verification.

## Test Suite Architecture

### 🏗️ Two-Layer Testing Architecture

The project employs a **two-layer testing strategy**:

1. **Co-located Unit Tests** (`*/tests/`): Module-specific tests in each component directory
2. **Global Integration Tests** (`src/tests/`): Cross-cutting tests for system-wide behavior

### 📊 Testing Statistics

- **Total Test Files**: 7 global test files + 20+ co-located test modules
- **Test Coverage**: 89% pass rate across 275+ tests
- **Test Categories**: 6 major categories with comprehensive coverage
- **Performance Tests**: 36 dedicated performance test methods
- **Security Tests**: 20+ security vulnerability tests

## Test Configuration

### 🔧 Global Test Configuration

**File**: `src/conftest.py`
- **Purpose**: Central pytest configuration and fixtures
- **Key Fixtures**:
  - `app()`: Flask application with test environment setup
  - `client()`: Flask test client for HTTP requests
  - `runner()`: CLI test runner for command testing
- **Test Database**: In-memory SQLite for fast, isolated testing
- **Environment**: Automatically configured test environment variables

**File**: `pyproject.toml`
- **Test Discovery**: `testpaths = ["src"]` - searches all src/ subdirectories
- **Test Markers**: 6 defined markers for test categorization
- **Coverage**: Source-based coverage with HTML/XML reporting
- **Quality Tools**: Integrated ruff, mypy, and pytest configuration

### 🏷️ Test Markers

```python
# Defined in pyproject.toml
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests", 
    "asyncio: marks tests as asyncio tests",
    "performance: marks tests as performance tests",
    "security: marks tests as security tests",
]
```

## Test Categories

### 1. 🔗 Integration Testing (`test_integration_e2e.py`)

**Purpose**: End-to-end integration tests with real Azure OpenAI Sora API

**Key Test Classes**:
- `TestVideoGenerationIntegration`: Complete video generation workflow validation

**Test Scenarios**:
- Real Azure API integration with job creation, polling, and completion
- Database persistence verification throughout workflow
- Video file generation and download validation
- Complete end-to-end workflow with cleanup

**Requirements**:
- Valid Azure OpenAI credentials in environment
- Network connectivity to Azure API
- Sufficient API quota for video generation

**Usage**:
```bash
# Run integration tests (requires Azure credentials)
uv run pytest src/tests/test_integration_e2e.py -v

# Skip integration tests
uv run pytest -m "not integration"
```

### 2. 🏃 Performance Testing (`test_performance.py`)

**Purpose**: Comprehensive performance benchmarking and regression detection

**Test Classes** (8 categories, 36+ test methods):
- `TestMemoryUsagePerformance`: Memory profiling and leak detection
- `TestResponseTimeBenchmarks`: API response time under load
- `TestDatabasePerformance`: Database query and connection performance
- `TestConcurrentRequestHandling`: Multi-user load testing (20+ users)
- `TestResourceUtilizationMonitoring`: CPU, memory, disk I/O monitoring
- `TestCachePerformance`: Cache hit rates and memory usage
- `TestQueueProcessingPerformance`: Background job processing efficiency
- `TestPerformanceRegression`: Baseline validation and alerting

**Key Features**:
- **Memory Profiling**: `tracemalloc` integration for memory leak detection
- **Resource Monitoring**: `psutil` integration for CPU/memory/disk metrics
- **Concurrent Testing**: `ThreadPoolExecutor` for multi-user simulation
- **Performance Baselines**: Configurable thresholds and regression detection
- **Comprehensive Metrics**: Statistical analysis and reporting

**Performance Configuration**:
```python
PERFORMANCE_CONFIG = {
    "memory_threshold_mb": 50,          # Max memory increase
    "response_time_threshold_ms": 100,   # Max response time
    "throughput_threshold_rps": 50,      # Min requests per second
    "concurrent_users": 20,              # Concurrent users for load testing
    "cache_hit_ratio_threshold": 0.8,   # Min cache hit ratio
}
```

**Usage**:
```bash
# Run all performance tests
uv run pytest src/tests/test_performance.py -v

# Run specific performance category
uv run pytest src/tests/test_performance.py::TestMemoryUsagePerformance -v

# Run fast performance tests only
uv run pytest -m "performance and not slow"
```

### 3. 🔒 Security Testing (`test_security.py`)

**Purpose**: OWASP Top 10 vulnerability testing and security validation

**Test Classes**:
- `TestInputValidationSecurity`: SQL injection, XSS, command injection protection
- `TestPathTraversalSecurity`: Directory traversal attack prevention
- `TestAuthenticationSecurity`: Authentication bypass and session security
- `TestRateLimitingSecurity`: Rate limiting effectiveness and bypass attempts
- `TestFileUploadSecurity`: File upload vulnerability testing
- `TestSecurityHeaders`: HTTP security headers validation
- `TestSessionSecurity`: Session management security
- `TestPenetrationTesting`: Comprehensive attack simulation

**Security Test Coverage**:
- **SQL Injection**: Multiple payload types and parameter injection
- **XSS Protection**: Script injection and DOM manipulation attacks
- **Path Traversal**: Directory traversal and file system access attempts
- **Authentication**: Session hijacking and authentication bypass
- **File Upload**: Malicious file upload and execution prevention
- **Rate Limiting**: Brute force and API abuse protection

**Usage**:
```bash
# Run security tests
uv run pytest src/tests/test_security.py -v

# Run all security-marked tests
uv run pytest -m "security"
```

### 4. 🔄 Load Testing (`test_load_testing.py`)

**Purpose**: Multi-user concurrent load testing with pytest integration

**Test Classes**:
- `TestConcurrentVideoGeneration`: 15+ simultaneous video generation requests
- `TestQueueManagementUnderLoad`: Queue fairness and processing under load
- `TestWebSocketLoadTesting`: Real-time update performance under load
- `TestDatabaseLoadTesting`: Database performance under concurrent access

**Load Testing Features**:
- **Concurrent Users**: 15+ simultaneous users with unique sessions
- **Response Time Analysis**: Statistical analysis of response times
- **Queue Performance**: Fair queue processing and position management
- **WebSocket Stress**: Real-time update performance under load
- **Failure Analysis**: Error categorization and recovery testing

**Usage**:
```bash
# Run load tests
uv run pytest src/tests/test_load_testing.py -v

# Run load tests with extended timeout
uv run pytest src/tests/test_load_testing.py --timeout=300
```

### 5. 🧩 Component Integration (`test_component_integration.py`)

**Purpose**: Cross-component interaction and integration validation

**Test Classes**:
- `TestSoraClientComponentIntegration`: SoraClient component interaction
- `TestDatabaseQueueIntegration`: Database and queue system integration
- `TestWebSocketSessionIntegration`: WebSocket and session management
- `TestRateLimitingIntegration`: Rate limiting across components

**Integration Scenarios**:
- Component initialization and dependency injection
- Cross-component communication patterns
- Shared resource management (database connections, Redis)
- Error propagation and recovery across components

**Usage**:
```bash
# Run component integration tests
uv run pytest src/tests/test_component_integration.py -v
```

### 6. 🛡️ Error Recovery (`test_error_recovery.py`)

**Purpose**: System resilience and fault tolerance validation

**Test Classes**:
- `TestAzureAPIErrorRecovery`: Azure API failure scenarios and recovery
- `TestDatabaseErrorRecovery`: Database connection loss and reconnection
- `TestRedisErrorRecovery`: Redis failure handling and fallback
- `TestCascadeFailureRecovery`: Cross-component failure scenarios

**Error Recovery Scenarios**:
- **Azure API Failures**: Rate limiting, timeouts, service unavailability
- **Database Errors**: Connection loss, transaction failures, deadlocks
- **Redis Failures**: Connection loss, memory exhaustion, cluster failures
- **Cascade Failures**: Multi-component failure scenarios and recovery

**Usage**:
```bash
# Run error recovery tests
uv run pytest src/tests/test_error_recovery.py -v
```

### 7. 🚀 Main Application (`test_main.py`)

**Purpose**: Flask application creation and configuration validation

**Test Classes**:
- `TestCreateApp`: Flask application initialization and configuration

**Test Scenarios**:
- Basic Flask application creation and setup
- Configuration defaults and environment variable overrides
- Template and static folder configuration
- Database manager initialization
- Extension registration (SQLAlchemy, Flask-Migrate)
- Upload folder creation and permissions

**Usage**:
```bash
# Run main application tests
uv run pytest src/tests/test_main.py -v
```

## Co-located Module Tests

### 📁 Module-Specific Test Structure

Each module contains its own `tests/` subdirectory with focused unit tests:

```
src/
├── api/tests/                    # API route and endpoint tests
├── core/tests/                   # Core model and validation tests
├── config/tests/                 # Configuration and security tests
├── database/tests/               # Database ORM and connection tests
├── features/sora_integration/tests/  # Azure API integration tests
├── job_queue/tests/              # Background processing tests
├── monitoring/tests/             # Health check and metrics tests
├── rate_limiting/tests/          # Rate limiting strategy tests
├── realtime/tests/               # WebSocket and real-time tests
└── session/tests/                # Session management tests
```

### 🧪 Co-located Test Patterns

**Naming Convention**: `test_<module_name>.py`
**Test Discovery**: Automatic pytest discovery via `testpaths = ["src"]`
**Import Pattern**: Direct imports from parent module
**Fixture Usage**: Module-specific fixtures + global fixtures from `conftest.py`

## Test Execution

### 🚀 Running Tests

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test module
uv run pytest src/tests/test_performance.py -v

# Run specific test class
uv run pytest src/tests/test_security.py::TestInputValidationSecurity -v

# Run tests by marker
uv run pytest -m "unit"                    # Unit tests only
uv run pytest -m "integration"             # Integration tests only
uv run pytest -m "performance"             # Performance tests only
uv run pytest -m "security"                # Security tests only
uv run pytest -m "not slow"                # Skip slow tests

# Run tests with timeout
uv run pytest --timeout=300                # 5 minute timeout
```

### 📊 Coverage Reporting

```bash
# Generate coverage report
uv run pytest --cov=src --cov-report=html

# View coverage in browser
open htmlcov/index.html

# Generate XML coverage for CI/CD
uv run pytest --cov=src --cov-report=xml
```

## Performance Profiling

### 🔍 Performance Profiler Usage

```python
from src.tests.test_performance import PerformanceProfiler, performance_monitor

# Create profiler instance
profiler = PerformanceProfiler()

# Profile code block
with performance_monitor(profiler, "operation_name") as p:
    # Your code here
    p.record_metric("custom_metric", value)

# Get comprehensive metrics
metrics = profiler.stop_profiling()
stats = profiler.get_metric_stats("custom_metric")
```

### 📈 Performance Metrics

**Memory Metrics**:
- Memory usage (RSS) in MB
- Memory growth and leak detection
- Top memory consumers by line
- Garbage collection statistics

**CPU Metrics**:
- CPU utilization percentage
- Processing time measurements
- Resource utilization patterns

**Response Time Metrics**:
- Mean, median, standard deviation
- Min/max response times
- Percentile analysis

## Test Development Guidelines

### ✅ Best Practices

1. **Unique Test Data**: Always use `uuid.uuid4()` for unique identifiers
2. **Proper Mocking**: Mock external dependencies (Azure API, Redis, database)
3. **Test Isolation**: Each test should be independent and cleanup after itself
4. **Comprehensive Coverage**: Test happy path, edge cases, and error conditions
5. **Performance Baselines**: Define clear performance thresholds and assertions
6. **Security Testing**: Include security validation in all user input handling

### 🔧 Test Fixtures

**Global Fixtures** (`src/conftest.py`):
- `app`: Flask application with test configuration
- `client`: Flask test client for HTTP requests
- `runner`: CLI test runner

**Module-Specific Fixtures**:
- Database sessions and test data
- Mock external services
- Component-specific test setup

### 📝 Test Documentation

Each test file includes:
- **Module docstring**: Purpose and scope of tests
- **Class docstrings**: Test category and coverage
- **Method docstrings**: Specific test scenario and validation
- **Inline comments**: Complex test logic and assertions

## Integration with CI/CD

### 🔄 Continuous Integration

**Test Execution**: All tests run automatically on code changes
**Coverage Reporting**: HTML and XML coverage reports generated
**Performance Baselines**: Performance regression detection
**Security Scanning**: Automated security vulnerability testing

**Recommended CI/CD Pipeline**:
```yaml
test:
  - uv run pytest --cov=src --cov-report=xml
  - uv run pytest -m "security" --strict-markers
  - uv run pytest -m "performance" --timeout=600
```

## Troubleshooting

### 🐛 Common Issues

1. **Test Database Conflicts**: Use unique IDs with `uuid.uuid4()`
2. **Environment Variables**: Mock `load_dotenv()` for clean test environments
3. **SQLAlchemy 2.x**: Use `text()` wrapper for raw SQL queries
4. **Flask-SQLAlchemy 3.x**: Check `app.extensions` instead of `db.app`
5. **Azure API Limits**: Use mocking for most tests, real API for integration only

### 📋 Test Health Check

```bash
# Check test configuration
uv run pytest --collect-only

# Validate test markers
uv run pytest --strict-markers

# Check test coverage
uv run pytest --cov=src --cov-report=term-missing
```

## Future Enhancements

### 🚀 Planned Improvements

1. **Load Testing Automation**: Continuous load testing in CI/CD
2. **Performance Benchmarking**: Automated performance baseline updates
3. **Security Scanning Integration**: SAST/DAST tool integration
4. **Chaos Engineering**: Fault injection and resilience testing
5. **Contract Testing**: API contract validation
6. **Visual Testing**: UI screenshot comparison testing

### 📊 Metrics and Monitoring

- **Test Execution Time**: Track test performance over time
- **Coverage Trends**: Monitor test coverage changes
- **Failure Rates**: Track test stability and reliability
- **Performance Baselines**: Automated performance regression detection

## Contributing

### 🤝 Adding New Tests

1. **Choose Location**: Global (`src/tests/`) vs module-specific (`module/tests/`)
2. **Follow Patterns**: Use existing test structure and naming conventions
3. **Add Markers**: Use appropriate pytest markers for categorization
4. **Document Tests**: Include comprehensive docstrings and comments
5. **Test Coverage**: Ensure new functionality has comprehensive test coverage

### 📋 Test Review Checklist

- [ ] Tests are independent and isolated
- [ ] External dependencies are properly mocked
- [ ] Test data uses unique identifiers
- [ ] Performance tests have clear baselines
- [ ] Security tests cover relevant attack vectors
- [ ] Documentation is comprehensive and up-to-date
- [ ] Tests follow project coding standards