"""
Comprehensive performance testing suite for the video generation system.

This module provides extensive performance testing capabilities including:
- Memory usage profiling and monitoring
- Response time benchmarks and API performance measurement
- Database performance and connection pooling validation
- Concurrent request handling and load testing
- Resource utilization monitoring (CPU, memory, disk)
- Cache performance testing
- Queue processing performance and worker efficiency
- Performance baseline assertions and regression detection
- Comprehensive metrics collection and reporting

The tests are designed to identify performance bottlenecks, memory leaks,
and regression issues in the video generation system.
"""

import asyncio
import gc
import json
import logging
import os
import psutil
import sqlite3
import statistics
import sys
import tempfile
import threading
import time
import tracemalloc
from concurrent.futures import ThreadPoolExecutor, as_completed
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from unittest.mock import AsyncMock, Mock, patch

import pytest
import redis

from src.api.job_repository import JobRepository
from src.config.factory import ConfigurationFactory
from src.core.models import GenerationParams, VideoJob
from src.database.connection import get_db_session
from src.database.models import VideoJobDB
from src.features.sora_integration.client import SoraClient
from src.features.sora_integration.http_client import SoraHttpClient
from src.features.sora_integration.job_manager import VideoJobManager
from src.features.sora_integration.video_downloader import VideoDownloader
from src.job_queue.manager import QueueManager
from src.rate_limiting.limiter import GlobalRateLimiter
from src.rate_limiting.strategies.adaptive import AdaptiveStrategy
from src.rate_limiting.strategies.sliding_window import SlidingWindowStrategy
from src.rate_limiting.strategies.token_bucket import TokenBucketStrategy
from src.session.manager import SessionManager, get_or_create_session


# Performance testing configuration
PERFORMANCE_CONFIG = {
    "memory_threshold_mb": 50,  # Maximum memory increase in MB
    "response_time_threshold_ms": 100,  # Maximum response time in milliseconds
    "throughput_threshold_rps": 50,  # Minimum requests per second
    "concurrent_users": 20,  # Number of concurrent users for load testing
    "stress_test_duration": 30,  # Duration of stress tests in seconds
    "database_connection_pool_size": 10,  # Database connection pool size
    "cache_hit_ratio_threshold": 0.8,  # Minimum cache hit ratio
    "queue_processing_threshold": 100,  # Minimum queue processing rate
}


class PerformanceProfiler:
    """
    Performance profiler for tracking memory, CPU, and timing metrics.
    
    Provides comprehensive performance monitoring capabilities including:
    - Memory usage tracking with tracemalloc
    - CPU utilization monitoring
    - Response time measurement
    - Resource utilization analysis
    - Performance regression detection
    """
    
    def __init__(self) -> None:
        """Initialize the performance profiler."""
        self.metrics: Dict[str, List[float]] = {}
        self.memory_snapshots: List[tracemalloc.Snapshot] = []
        self.start_time: Optional[float] = None
        self.process = psutil.Process()
        
    def start_profiling(self) -> None:
        """Start performance profiling."""
        tracemalloc.start()
        self.start_time = time.time()
        
    def stop_profiling(self) -> Dict[str, Any]:
        """Stop profiling and return performance metrics."""
        if self.start_time is None:
            raise ValueError("Profiling not started")
            
        # Get memory snapshot
        snapshot = tracemalloc.take_snapshot()
        self.memory_snapshots.append(snapshot)
        
        # Calculate metrics
        duration = time.time() - self.start_time
        
        # Memory metrics
        memory_usage = self.process.memory_info().rss / 1024 / 1024  # MB
        cpu_percent = self.process.cpu_percent()
        
        # Get top memory consumers
        top_stats = snapshot.statistics('lineno')[:10]
        
        metrics = {
            "duration_seconds": duration,
            "memory_usage_mb": memory_usage,
            "cpu_percent": cpu_percent,
            "top_memory_consumers": [
                {
                    "file": stat.traceback.format()[0],
                    "size_mb": stat.size / 1024 / 1024,
                    "count": stat.count
                }
                for stat in top_stats
            ]
        }
        
        tracemalloc.stop()
        return metrics
        
    def record_metric(self, name: str, value: float) -> None:
        """Record a performance metric."""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(value)
        
    def get_metric_stats(self, name: str) -> Dict[str, float]:
        """Get statistical summary of a metric."""
        if name not in self.metrics or not self.metrics[name]:
            return {}
            
        values = self.metrics[name]
        return {
            "mean": statistics.mean(values),
            "median": statistics.median(values),
            "stdev": statistics.stdev(values) if len(values) > 1 else 0,
            "min": min(values),
            "max": max(values),
            "count": len(values)
        }


@contextmanager
def performance_monitor(profiler: PerformanceProfiler, test_name: str):
    """Context manager for performance monitoring."""
    profiler.start_profiling()
    start_time = time.time()
    
    try:
        yield profiler
    finally:
        end_time = time.time()
        metrics = profiler.stop_profiling()
        
        # Record timing metric
        profiler.record_metric(f"{test_name}_duration", end_time - start_time)
        
        # Log performance metrics
        logging.info(f"Performance metrics for {test_name}: {metrics}")


class PerformanceTestBase:
    """Base class for performance tests with common utilities."""
    
    def setup_method(self):
        """Setup method for each test."""
        self.profiler = PerformanceProfiler()
        self.temp_dir = tempfile.mkdtemp()
        
    def teardown_method(self):
        """Teardown method for each test."""
        # Cleanup temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
        # Force garbage collection
        gc.collect()
        
    def assert_performance_baseline(self, metric_name: str, value: float, 
                                   threshold: float, comparison: str = "less_than") -> None:
        """Assert performance baseline with threshold."""
        if comparison == "less_than":
            assert value < threshold, f"{metric_name}: {value} >= {threshold}"
        elif comparison == "greater_than":
            assert value > threshold, f"{metric_name}: {value} <= {threshold}"
        else:
            raise ValueError(f"Unknown comparison: {comparison}")
            
    def create_test_video_job(self, job_id: str, **kwargs) -> VideoJob:
        """Create a test video job with default parameters."""
        # Create parameter dict with defaults
        params = {
            "id": job_id,
            "prompt": kwargs.get("prompt", f"Test prompt for {job_id}"),
            "status": kwargs.get("status", "pending"),
            "created_at": kwargs.get("created_at", datetime.utcnow()),
            "updated_at": kwargs.get("updated_at", datetime.utcnow()),
            "session_id": kwargs.get("session_id", "test-session"),
            "generation_id": kwargs.get("generation_id", f"gen-{job_id}"),
        }
        
        # Update with any additional kwargs, allowing overrides
        for key, value in kwargs.items():
            if key not in ["prompt", "status", "created_at", "updated_at", "session_id", "generation_id"]:
                params[key] = value
        
        return VideoJob(**params)
        
    def create_test_generation_params(self, **kwargs) -> GenerationParams:
        """Create test generation parameters."""
        # Create parameter dict with defaults
        params = {
            "prompt": kwargs.get("prompt", "Test video generation"),
            "duration": kwargs.get("duration", 5),
            "width": kwargs.get("width", 1920),
            "height": kwargs.get("height", 1080),
            "model": kwargs.get("model", "sora-1.0-turbo"),
        }
        
        # Update with any additional kwargs, allowing overrides
        for key, value in kwargs.items():
            if key not in ["prompt", "duration", "width", "height", "model"]:
                params[key] = value
        
        return GenerationParams(**params)
    
    def create_video_job_from_params(self, client: SoraClient, params: GenerationParams) -> VideoJob:
        """Helper method to create video job from GenerationParams."""
        ui_parameters = {
            "width": params.width,
            "height": params.height,
            "duration": params.duration,
            "model": params.model
        }
        return client.create_video_job(params.prompt, ui_parameters)


@pytest.mark.performance
@pytest.mark.slow
class TestMemoryUsagePerformance(PerformanceTestBase):
    """Test memory usage patterns and detect memory leaks."""
    
    def test_memory_usage_video_generation_workflow(self):
        """Test memory consumption during video generation workflow."""
        with performance_monitor(self.profiler, "memory_video_generation"):
            # Mock external dependencies
            with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
                 patch('src.features.sora_integration.http_client.requests.get') as mock_get:
                
                # Setup mocks
                mock_post.return_value.status_code = 200
                mock_post.return_value.json.return_value = {
                    "data": [{"id": "gen-123", "status": "pending"}]
                }
                mock_get.return_value.status_code = 200
                mock_get.return_value.json.return_value = {
                    "data": [{"id": "gen-123", "status": "completed", "video_url": "test.mp4"}]
                }
                
                # Test memory usage during workflow
                initial_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                
                # Simulate video generation workflow
                client = SoraClient()
                jobs = []
                
                for i in range(50):
                    prompt = f"Memory test video {i}"
                    ui_parameters = {
                        "width": 1920,
                        "height": 1080,
                        "duration": 5,
                        "model": "sora-1.0-turbo"
                    }
                    job = client.create_video_job(prompt, ui_parameters)
                    jobs.append(job)
                    
                    # Poll job status
                    job = client.poll_job_status(job.id, job.generation_id)
                
                final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                memory_increase = final_memory - initial_memory
                
                # Assert memory usage is within acceptable limits
                self.assert_performance_baseline(
                    "memory_increase_mb", memory_increase, 
                    PERFORMANCE_CONFIG["memory_threshold_mb"], "less_than"
                )
                
    def test_memory_leak_detection(self):
        """Test for memory leaks in repetitive operations."""
        with performance_monitor(self.profiler, "memory_leak_detection"):
            # Perform many iterations of object creation/destruction
            memory_samples = []
            
            for cycle in range(10):
                # Create many objects
                objects = []
                for i in range(1000):
                    job = self.create_test_video_job(f"leak-test-{cycle}-{i}")
                    objects.append(job)
                
                # Record memory usage
                memory_usage = self.profiler.process.memory_info().rss / 1024 / 1024
                memory_samples.append(memory_usage)
                
                # Clear objects
                objects.clear()
                gc.collect()
                
            # Check for memory leak pattern
            if len(memory_samples) > 5:
                # Calculate memory growth trend
                first_half = memory_samples[:5]
                second_half = memory_samples[5:]
                
                avg_first = statistics.mean(first_half)
                avg_second = statistics.mean(second_half)
                
                # Memory growth should be minimal
                memory_growth = (avg_second - avg_first) / avg_first
                assert memory_growth < 0.1, f"Potential memory leak detected: {memory_growth:.2%} growth"
                
    def test_large_dataset_memory_efficiency(self):
        """Test memory efficiency with large datasets."""
        with performance_monitor(self.profiler, "large_dataset_memory"):
            # Create large dataset
            num_jobs = 5000
            jobs = []
            
            initial_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            
            for i in range(num_jobs):
                job = self.create_test_video_job(
                    f"large-dataset-{i}",
                    prompt=f"Large dataset test prompt {i}"
                )
                jobs.append(job)
                
                # Sample memory usage periodically
                if i % 1000 == 0:
                    current_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                    self.profiler.record_metric("memory_per_1000_jobs", current_memory - initial_memory)
            
            final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            memory_per_job = (final_memory - initial_memory) / num_jobs
            
            # Assert memory efficiency
            assert memory_per_job < 0.01, f"Memory per job too high: {memory_per_job:.4f} MB"
            
    def test_garbage_collection_performance(self):
        """Test garbage collection performance and effectiveness."""
        with performance_monitor(self.profiler, "garbage_collection"):
            # Test GC under different scenarios
            gc_times = []
            
            for scenario in range(5):
                # Create objects of different sizes
                objects = []
                for i in range(2000):
                    if scenario % 2 == 0:
                        # Large objects
                        job = self.create_test_video_job(
                            f"gc-test-{scenario}-{i}",
                            prompt=f"Large test prompt {scenario}-{i}"
                        )
                    else:
                        # Small objects
                        job = self.create_test_video_job(f"gc-test-{scenario}-{i}")
                    objects.append(job)
                
                # Measure GC performance
                start_time = time.time()
                objects.clear()
                gc.collect()
                gc_time = time.time() - start_time
                
                gc_times.append(gc_time)
                self.profiler.record_metric("gc_time", gc_time)
            
            # Assert GC performance
            max_gc_time = max(gc_times)
            assert max_gc_time < 0.1, f"GC took too long: {max_gc_time:.3f} seconds"
            
    def test_memory_profiling_integration(self):
        """Test integration with memory profiling tools."""
        with performance_monitor(self.profiler, "memory_profiling"):
            # Test tracemalloc integration (it's already started by performance_monitor)
            # tracemalloc.start()  # Not needed as performance_monitor already starts it
            
            # Create objects with known memory patterns
            large_objects = []
            for i in range(100):
                # Create objects with predictable memory usage
                job = self.create_test_video_job(
                    f"profiling-{i}",
                    prompt=f"Memory profiling test {i}",
                    metadata={"test_data": list(range(1000))}
                )
                large_objects.append(job)
            
            # Take memory snapshot
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')[:5]
            
            # Verify memory tracking works
            assert len(top_stats) > 0, "Memory profiling not capturing data"
            
            # Check for expected memory patterns
            total_size = sum(stat.size for stat in top_stats)
            assert total_size > 0, "No memory usage detected"
            
            # tracemalloc.stop()  # Not needed as performance_monitor will stop it


@pytest.mark.performance
@pytest.mark.slow
class TestResponseTimeBenchmarks(PerformanceTestBase):
    """Test response time benchmarks and API performance."""
    
    def test_api_response_time_benchmarks(self):
        """Test API response time under various conditions."""
        with performance_monitor(self.profiler, "api_response_time"):
            response_times = []
            
            # Test different API operations
            operations = [
                ("job_creation", self._test_job_creation_response_time),
                ("job_polling", self._test_job_polling_response_time),
                ("job_retrieval", self._test_job_retrieval_response_time),
                ("status_update", self._test_status_update_response_time),
            ]
            
            for operation_name, operation_func in operations:
                times = operation_func()
                response_times.extend(times)
                
                # Calculate statistics
                avg_time = statistics.mean(times)
                p95_time = sorted(times)[int(len(times) * 0.95)]
                
                self.profiler.record_metric(f"{operation_name}_avg_time", avg_time)
                self.profiler.record_metric(f"{operation_name}_p95_time", p95_time)
                
                # Assert response time benchmarks
                assert avg_time < 0.1, f"{operation_name} average time too high: {avg_time:.3f}s"
                assert p95_time < 0.2, f"{operation_name} P95 time too high: {p95_time:.3f}s"
                
    def _test_job_creation_response_time(self) -> List[float]:
        """Test job creation response time."""
        times = []
        
        with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "data": [{"id": "gen-123", "status": "pending"}]
            }
            
            client = SoraClient()
            
            for i in range(50):
                params = self.create_test_generation_params(prompt=f"Benchmark test {i}")
                
                start_time = time.time()
                job = self.create_video_job_from_params(client, params)
                end_time = time.time()
                
                times.append(end_time - start_time)
                
        return times
        
    def _test_job_polling_response_time(self) -> List[float]:
        """Test job polling response time."""
        times = []
        
        with patch('src.features.sora_integration.http_client.requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "data": [{"id": "gen-123", "status": "completed", "video_url": "test.mp4"}]
            }
            
            client = SoraClient()
            
            for i in range(50):
                start_time = time.time()
                job = client.poll_job_status(f"job-{i}", "gen-123")
                end_time = time.time()
                
                times.append(end_time - start_time)
                
        return times
        
    def _test_job_retrieval_response_time(self) -> List[float]:
        """Test job retrieval response time."""
        times = []
        
        # Setup test data
        with get_db_session() as session:
            repo = JobRepository(session)
            
            # Create test jobs
            for i in range(50):
                job = self.create_test_video_job(f"retrieval-test-{i}")
                repo.create_job(job)
            
            # Test retrieval times
            for i in range(50):
                start_time = time.time()
                job = repo.get_job_by_id(f"retrieval-test-{i}")
                end_time = time.time()
                
                times.append(end_time - start_time)
                
        return times
        
    def _test_status_update_response_time(self) -> List[float]:
        """Test status update response time."""
        times = []
        
        with get_db_session() as session:
            repo = JobRepository(session)
            
            # Create test jobs
            for i in range(50):
                job = self.create_test_video_job(f"status-test-{i}")
                repo.create_job(job)
            
            # Test status update times
            for i in range(50):
                start_time = time.time()
                repo.update_job_status(f"status-test-{i}", "running")
                end_time = time.time()
                
                times.append(end_time - start_time)
                
        return times
        
    def test_response_time_under_load(self):
        """Test response time under increasing load."""
        with performance_monitor(self.profiler, "response_time_load"):
            load_levels = [1, 5, 10, 20, 50]
            
            for load_level in load_levels:
                response_times = []
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": "gen-123", "status": "pending"}]
                    }
                    
                    client = SoraClient()
                    
                    # Simulate load
                    for i in range(load_level):
                        params = self.create_test_generation_params(
                            prompt=f"Load test {load_level}-{i}"
                        )
                        
                        start_time = time.time()
                        job = self.create_video_job_from_params(client, params)
                        end_time = time.time()
                        
                        response_times.append(end_time - start_time)
                
                # Calculate metrics
                avg_time = statistics.mean(response_times)
                self.profiler.record_metric(f"load_{load_level}_avg_time", avg_time)
                
                # Assert response time doesn't degrade significantly
                expected_max = 0.1 * (1 + load_level * 0.01)  # Allow slight increase
                assert avg_time < expected_max, f"Response time degraded under load {load_level}: {avg_time:.3f}s"
                
    def test_concurrent_response_time_stability(self):
        """Test response time stability under concurrent requests."""
        with performance_monitor(self.profiler, "concurrent_response_time"):
            num_threads = 10
            requests_per_thread = 20
            
            def make_concurrent_requests(thread_id):
                response_times = []
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": f"gen-{thread_id}", "status": "pending"}]
                    }
                    
                    client = SoraClient()
                    
                    for i in range(requests_per_thread):
                        params = self.create_test_generation_params(
                            prompt=f"Concurrent test {thread_id}-{i}"
                        )
                        
                        start_time = time.time()
                        job = self.create_video_job_from_params(client, params)
                        end_time = time.time()
                        
                        response_times.append(end_time - start_time)
                        
                return response_times
            
            # Execute concurrent requests
            all_times = []
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(make_concurrent_requests, i) for i in range(num_threads)]
                
                for future in as_completed(futures):
                    times = future.result()
                    all_times.extend(times)
            
            # Analyze response time stability
            avg_time = statistics.mean(all_times)
            stdev_time = statistics.stdev(all_times)
            
            # Assert stability
            assert stdev_time < avg_time * 0.5, f"Response time too variable: {stdev_time:.3f}s stddev"
            assert avg_time < 0.15, f"Average response time too high: {avg_time:.3f}s"


@pytest.mark.performance
@pytest.mark.slow
class TestDatabasePerformance(PerformanceTestBase):
    """Test database performance and connection pooling."""
    
    def test_database_connection_pooling_performance(self):
        """Test database connection pooling efficiency."""
        with performance_monitor(self.profiler, "db_connection_pooling"):
            # Test connection acquisition times
            connection_times = []
            
            for i in range(100):
                start_time = time.time()
                with get_db_session() as session:
                    # Simple query to ensure connection is active
                    session.execute("SELECT 1")
                end_time = time.time()
                
                connection_times.append(end_time - start_time)
                
            # Analyze connection performance
            avg_time = statistics.mean(connection_times)
            max_time = max(connection_times)
            
            # Assert connection pooling efficiency
            assert avg_time < 0.01, f"Connection acquisition too slow: {avg_time:.4f}s"
            assert max_time < 0.05, f"Maximum connection time too high: {max_time:.4f}s"
            
    def test_database_query_performance(self):
        """Test database query performance under various conditions."""
        with performance_monitor(self.profiler, "db_query_performance"):
            with get_db_session() as session:
                repo = JobRepository(session)
                
                # Create test data
                num_jobs = 1000
                for i in range(num_jobs):
                    job = self.create_test_video_job(f"query-test-{i}")
                    repo.create_job(job)
                
                # Test different query patterns
                query_tests = [
                    ("single_job_lookup", lambda: repo.get_job_by_id("query-test-500")),
                    ("status_filter", lambda: repo.get_jobs_by_status("pending")),
                    ("recent_jobs", lambda: repo.get_recent_jobs(limit=50)),
                    ("job_count", lambda: repo.get_job_count()),
                ]
                
                for test_name, query_func in query_tests:
                    times = []
                    
                    for _ in range(10):
                        start_time = time.time()
                        result = query_func()
                        end_time = time.time()
                        
                        times.append(end_time - start_time)
                    
                    avg_time = statistics.mean(times)
                    self.profiler.record_metric(f"{test_name}_query_time", avg_time)
                    
                    # Assert query performance
                    assert avg_time < 0.05, f"{test_name} query too slow: {avg_time:.4f}s"
                    
    def test_bulk_operations_performance(self):
        """Test bulk database operations performance."""
        with performance_monitor(self.profiler, "bulk_operations"):
            with get_db_session() as session:
                repo = JobRepository(session)
                
                # Test bulk insert
                num_jobs = 500
                jobs = [self.create_test_video_job(f"bulk-{i}") for i in range(num_jobs)]
                
                start_time = time.time()
                for job in jobs:
                    repo.create_job(job)
                bulk_insert_time = time.time() - start_time
                
                # Test bulk update
                start_time = time.time()
                for i in range(num_jobs):
                    repo.update_job_status(f"bulk-{i}", "running")
                bulk_update_time = time.time() - start_time
                
                # Test bulk select
                start_time = time.time()
                all_jobs = repo.get_all_jobs()
                bulk_select_time = time.time() - start_time
                
                # Assert bulk operation performance
                insert_rate = num_jobs / bulk_insert_time
                update_rate = num_jobs / bulk_update_time
                
                assert insert_rate > 100, f"Bulk insert rate too low: {insert_rate:.0f} jobs/s"
                assert update_rate > 200, f"Bulk update rate too low: {update_rate:.0f} jobs/s"
                assert bulk_select_time < 0.1, f"Bulk select too slow: {bulk_select_time:.3f}s"
                
    def test_concurrent_database_access(self):
        """Test database performance under concurrent access."""
        with performance_monitor(self.profiler, "concurrent_db_access"):
            num_threads = 20
            operations_per_thread = 50
            
            def db_operations(thread_id):
                operation_times = []
                
                with get_db_session() as session:
                    repo = JobRepository(session)
                    
                    for i in range(operations_per_thread):
                        # Mix of operations
                        start_time = time.time()
                        
                        if i % 3 == 0:
                            # Create
                            job = self.create_test_video_job(f"concurrent-{thread_id}-{i}")
                            repo.create_job(job)
                        elif i % 3 == 1:
                            # Update
                            job_id = f"concurrent-{thread_id}-{i-1}"
                            try:
                                repo.update_job_status(job_id, "running")
                            except:
                                pass  # Job might not exist
                        else:
                            # Read
                            try:
                                repo.get_job_by_id(f"concurrent-{thread_id}-{i-2}")
                            except:
                                pass  # Job might not exist
                        
                        end_time = time.time()
                        operation_times.append(end_time - start_time)
                
                return operation_times
            
            # Execute concurrent operations
            all_times = []
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(db_operations, i) for i in range(num_threads)]
                
                for future in as_completed(futures):
                    times = future.result()
                    all_times.extend(times)
            
            # Analyze concurrent performance
            avg_time = statistics.mean(all_times)
            p95_time = sorted(all_times)[int(len(all_times) * 0.95)]
            
            # Assert concurrent database performance
            assert avg_time < 0.02, f"Concurrent DB operations too slow: {avg_time:.4f}s"
            assert p95_time < 0.1, f"P95 DB operation time too high: {p95_time:.4f}s"
            
    def test_database_index_performance(self):
        """Test database index performance and effectiveness."""
        with performance_monitor(self.profiler, "db_index_performance"):
            with get_db_session() as session:
                repo = JobRepository(session)
                
                # Create large dataset
                num_jobs = 2000
                for i in range(num_jobs):
                    job = self.create_test_video_job(
                        f"index-test-{i}",
                        status=["pending", "running", "completed"][i % 3]
                    )
                    repo.create_job(job)
                
                # Test indexed queries
                indexed_queries = [
                    ("by_id", lambda: repo.get_job_by_id("index-test-1000")),
                    ("by_status", lambda: repo.get_jobs_by_status("pending")),
                    ("by_session", lambda: repo.get_jobs_by_session("test-session")),
                ]
                
                for query_name, query_func in indexed_queries:
                    times = []
                    
                    for _ in range(20):
                        start_time = time.time()
                        result = query_func()
                        end_time = time.time()
                        
                        times.append(end_time - start_time)
                    
                    avg_time = statistics.mean(times)
                    self.profiler.record_metric(f"indexed_{query_name}_time", avg_time)
                    
                    # Assert index effectiveness
                    assert avg_time < 0.01, f"Indexed query {query_name} too slow: {avg_time:.4f}s"


@pytest.mark.performance
@pytest.mark.slow
class TestConcurrentRequestHandling(PerformanceTestBase):
    """Test concurrent request handling and load performance."""
    
    def test_concurrent_user_simulation(self):
        """Test system performance with concurrent users."""
        with performance_monitor(self.profiler, "concurrent_users"):
            num_users = PERFORMANCE_CONFIG["concurrent_users"]
            requests_per_user = 10
            
            def simulate_user(user_id):
                user_metrics = {
                    "requests_completed": 0,
                    "total_time": 0,
                    "errors": 0
                }
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
                     patch('src.session.manager.redis.Redis') as mock_redis_class:
                    
                    # Setup mocks
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": f"gen-{user_id}", "status": "pending"}]
                    }
                    
                    mock_redis = Mock()
                    mock_redis_class.return_value = mock_redis
                    mock_redis.exists.return_value = False
                    mock_redis.hset.return_value = True
                    
                    try:
                        start_time = time.time()
                        
                        # Simulate user session
                        session_id, session_data = get_or_create_session(f"192.168.1.{user_id}")
                        
                        # Create video generation requests
                        client = SoraClient()
                        for i in range(requests_per_user):
                            params = self.create_test_generation_params(
                                prompt=f"User {user_id} request {i}"
                            )
                            job = self.create_video_job_from_params(client, params)
                            user_metrics["requests_completed"] += 1
                        
                        user_metrics["total_time"] = time.time() - start_time
                        
                    except Exception as e:
                        user_metrics["errors"] += 1
                        logging.error(f"User {user_id} error: {e}")
                
                return user_metrics
            
            # Execute concurrent users
            all_metrics = []
            with ThreadPoolExecutor(max_workers=num_users) as executor:
                futures = [executor.submit(simulate_user, i) for i in range(num_users)]
                
                for future in as_completed(futures):
                    metrics = future.result()
                    all_metrics.append(metrics)
            
            # Analyze concurrent performance
            total_requests = sum(m["requests_completed"] for m in all_metrics)
            total_errors = sum(m["errors"] for m in all_metrics)
            avg_time_per_user = statistics.mean([m["total_time"] for m in all_metrics])
            
            # Assert concurrent performance
            error_rate = total_errors / (total_requests + total_errors) if total_requests > 0 else 1
            assert error_rate < 0.05, f"Error rate too high: {error_rate:.2%}"
            
            requests_per_second = total_requests / avg_time_per_user
            assert requests_per_second > 20, f"Throughput too low: {requests_per_second:.1f} RPS"
            
    def test_load_balancing_performance(self):
        """Test load balancing and request distribution."""
        with performance_monitor(self.profiler, "load_balancing"):
            # Simulate multiple worker processes
            num_workers = 5
            requests_per_worker = 100
            
            def worker_process(worker_id):
                worker_metrics = {
                    "requests_processed": 0,
                    "processing_time": 0,
                    "queue_time": 0
                }
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": f"gen-{worker_id}", "status": "pending"}]
                    }
                    
                    client = SoraClient()
                    
                    start_time = time.time()
                    
                    for i in range(requests_per_worker):
                        # Simulate queue time
                        queue_start = time.time()
                        time.sleep(0.001)  # Small delay to simulate queueing
                        queue_end = time.time()
                        
                        # Process request
                        process_start = time.time()
                        params = self.create_test_generation_params(
                            prompt=f"Worker {worker_id} request {i}"
                        )
                        job = self.create_video_job_from_params(client, params)
                        process_end = time.time()
                        
                        worker_metrics["requests_processed"] += 1
                        worker_metrics["queue_time"] += queue_end - queue_start
                        worker_metrics["processing_time"] += process_end - process_start
                    
                    worker_metrics["total_time"] = time.time() - start_time
                
                return worker_metrics
            
            # Execute worker processes
            worker_metrics = []
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(worker_process, i) for i in range(num_workers)]
                
                for future in as_completed(futures):
                    metrics = future.result()
                    worker_metrics.append(metrics)
            
            # Analyze load balancing
            processing_times = [m["processing_time"] for m in worker_metrics]
            requests_processed = [m["requests_processed"] for m in worker_metrics]
            
            # Check load distribution
            avg_processing_time = statistics.mean(processing_times)
            stdev_processing_time = statistics.stdev(processing_times)
            
            # Assert load balancing effectiveness
            assert stdev_processing_time < avg_processing_time * 0.3, \
                f"Load imbalance detected: {stdev_processing_time:.3f}s stddev"
            
            # Check all workers processed requests
            assert all(count > 0 for count in requests_processed), \
                "Some workers didn't process any requests"
                
    def test_throughput_under_sustained_load(self):
        """Test system throughput under sustained load."""
        with performance_monitor(self.profiler, "sustained_load"):
            duration = 30  # seconds
            concurrent_requests = 10
            
            def sustained_requests(request_id):
                requests_completed = 0
                start_time = time.time()
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": f"gen-{request_id}", "status": "pending"}]
                    }
                    
                    client = SoraClient()
                    
                    while time.time() - start_time < duration:
                        try:
                            params = self.create_test_generation_params(
                                prompt=f"Sustained load request {request_id}-{requests_completed}"
                            )
                            job = self.create_video_job_from_params(client, params)
                            requests_completed += 1
                            
                            # Small delay to prevent overwhelming
                            time.sleep(0.01)
                            
                        except Exception as e:
                            logging.error(f"Sustained load error: {e}")
                            break
                
                return requests_completed
            
            # Execute sustained load
            with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
                futures = [executor.submit(sustained_requests, i) for i in range(concurrent_requests)]
                
                total_requests = sum(future.result() for future in as_completed(futures))
            
            # Calculate throughput
            throughput = total_requests / duration
            
            # Assert sustained throughput
            assert throughput > PERFORMANCE_CONFIG["throughput_threshold_rps"], \
                f"Throughput too low: {throughput:.1f} RPS"
                
    def test_request_queuing_performance(self):
        """Test request queuing and processing efficiency."""
        with performance_monitor(self.profiler, "request_queuing"):
            # Simulate burst of requests
            burst_size = 100
            processing_capacity = 20  # requests per second
            
            request_times = []
            queue_times = []
            
            def process_request_batch(batch_id):
                batch_times = []
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": f"gen-{batch_id}", "status": "pending"}]
                    }
                    
                    client = SoraClient()
                    
                    for i in range(10):  # 10 requests per batch
                        queue_start = time.time()
                        
                        # Simulate queue delay
                        time.sleep(1 / processing_capacity)
                        
                        queue_end = time.time()
                        
                        # Process request
                        process_start = time.time()
                        params = self.create_test_generation_params(
                            prompt=f"Batch {batch_id} request {i}"
                        )
                        job = self.create_video_job_from_params(client, params)
                        process_end = time.time()
                        
                        batch_times.append({
                            "queue_time": queue_end - queue_start,
                            "process_time": process_end - process_start,
                            "total_time": process_end - queue_start
                        })
                
                return batch_times
            
            # Process request batches
            all_times = []
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(process_request_batch, i) for i in range(10)]
                
                for future in as_completed(futures):
                    times = future.result()
                    all_times.extend(times)
            
            # Analyze queuing performance
            avg_queue_time = statistics.mean([t["queue_time"] for t in all_times])
            avg_process_time = statistics.mean([t["process_time"] for t in all_times])
            avg_total_time = statistics.mean([t["total_time"] for t in all_times])
            
            # Assert queuing efficiency
            assert avg_queue_time < 0.1, f"Queue time too high: {avg_queue_time:.3f}s"
            assert avg_process_time < 0.05, f"Process time too high: {avg_process_time:.3f}s"
            assert avg_total_time < 0.15, f"Total time too high: {avg_total_time:.3f}s"


@pytest.mark.performance
@pytest.mark.slow
class TestResourceUtilizationMonitoring(PerformanceTestBase):
    """Test resource utilization monitoring and optimization."""
    
    def test_cpu_utilization_monitoring(self):
        """Test CPU utilization under various workloads."""
        with performance_monitor(self.profiler, "cpu_utilization"):
            # Baseline CPU usage
            baseline_cpu = self.profiler.process.cpu_percent(interval=1)
            
            # CPU-intensive workload
            cpu_samples = []
            
            def cpu_intensive_task():
                # Simulate CPU-intensive video processing
                for i in range(10000):
                    # Simulate complex calculations
                    result = sum(j * j for j in range(1000))
                    
                    # Sample CPU usage
                    if i % 1000 == 0:
                        cpu_usage = self.profiler.process.cpu_percent()
                        cpu_samples.append(cpu_usage)
            
            # Execute CPU-intensive task
            start_time = time.time()
            cpu_intensive_task()
            end_time = time.time()
            
            # Analyze CPU utilization
            peak_cpu = max(cpu_samples) if cpu_samples else 0
            avg_cpu = statistics.mean(cpu_samples) if cpu_samples else 0
            
            # Assert CPU utilization is reasonable
            assert peak_cpu < 90, f"CPU utilization too high: {peak_cpu:.1f}%"
            assert avg_cpu > baseline_cpu, f"CPU utilization not increased: {avg_cpu:.1f}%"
            
            # Log CPU metrics
            self.profiler.record_metric("peak_cpu_usage", peak_cpu)
            self.profiler.record_metric("avg_cpu_usage", avg_cpu)
            
    def test_memory_utilization_patterns(self):
        """Test memory utilization patterns and optimization."""
        with performance_monitor(self.profiler, "memory_utilization"):
            # Monitor memory usage over time
            memory_samples = []
            
            def memory_intensive_task():
                # Simulate memory-intensive operations
                large_objects = []
                
                for i in range(1000):
                    # Create large objects
                    job = self.create_test_video_job(
                        f"memory-util-{i}",
                        prompt=f"Memory utilization test {i}",
                        metadata={"large_data": list(range(1000))}
                    )
                    large_objects.append(job)
                    
                    # Sample memory usage
                    if i % 100 == 0:
                        memory_usage = self.profiler.process.memory_info().rss / 1024 / 1024
                        memory_samples.append(memory_usage)
                
                # Clear objects and force GC
                large_objects.clear()
                gc.collect()
                
                # Final memory sample
                final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                memory_samples.append(final_memory)
            
            # Execute memory-intensive task
            initial_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            memory_intensive_task()
            
            # Analyze memory utilization
            peak_memory = max(memory_samples)
            final_memory = memory_samples[-1]
            memory_growth = peak_memory - initial_memory
            memory_cleanup = peak_memory - final_memory
            
            # Assert memory utilization patterns
            assert memory_growth < 100, f"Memory growth too high: {memory_growth:.1f} MB"
            assert memory_cleanup > memory_growth * 0.8, f"Memory cleanup insufficient: {memory_cleanup:.1f} MB"
            
            # Log memory metrics
            self.profiler.record_metric("peak_memory_usage", peak_memory)
            self.profiler.record_metric("memory_growth", memory_growth)
            self.profiler.record_metric("memory_cleanup", memory_cleanup)
            
    def test_disk_io_performance(self):
        """Test disk I/O performance and optimization."""
        with performance_monitor(self.profiler, "disk_io"):
            # Create test files
            test_files = []
            file_sizes = [1024, 10240, 102400]  # 1KB, 10KB, 100KB
            
            for size in file_sizes:
                for i in range(10):
                    file_path = os.path.join(self.temp_dir, f"test_{size}_{i}.dat")
                    
                    # Write test data
                    start_time = time.time()
                    with open(file_path, 'wb') as f:
                        f.write(b'x' * size)
                    write_time = time.time() - start_time
                    
                    # Read test data
                    start_time = time.time()
                    with open(file_path, 'rb') as f:
                        data = f.read()
                    read_time = time.time() - start_time
                    
                    test_files.append({
                        "size": size,
                        "write_time": write_time,
                        "read_time": read_time,
                        "write_speed": size / write_time if write_time > 0 else 0,
                        "read_speed": size / read_time if read_time > 0 else 0
                    })
            
            # Analyze disk I/O performance
            for size in file_sizes:
                size_files = [f for f in test_files if f["size"] == size]
                avg_write_speed = statistics.mean([f["write_speed"] for f in size_files])
                avg_read_speed = statistics.mean([f["read_speed"] for f in size_files])
                
                # Assert I/O performance
                assert avg_write_speed > 1000, f"Write speed too low for {size}B files: {avg_write_speed:.0f} B/s"
                assert avg_read_speed > 10000, f"Read speed too low for {size}B files: {avg_read_speed:.0f} B/s"
                
                # Log I/O metrics
                self.profiler.record_metric(f"write_speed_{size}", avg_write_speed)
                self.profiler.record_metric(f"read_speed_{size}", avg_read_speed)
                
    def test_network_resource_monitoring(self):
        """Test network resource utilization monitoring."""
        with performance_monitor(self.profiler, "network_resources"):
            # Simulate network operations
            network_operations = []
            
            with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
                 patch('src.features.sora_integration.http_client.requests.get') as mock_get:
                
                # Setup mocks with realistic delays
                def mock_post_with_delay(*args, **kwargs):
                    time.sleep(0.01)  # Simulate network latency
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"data": [{"id": "gen-123", "status": "pending"}]}
                    return response
                
                def mock_get_with_delay(*args, **kwargs):
                    time.sleep(0.005)  # Simulate network latency
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"data": [{"id": "gen-123", "status": "completed"}]}
                    return response
                
                mock_post.side_effect = mock_post_with_delay
                mock_get.side_effect = mock_get_with_delay
                
                client = SoraClient()
                
                # Perform network operations
                for i in range(50):
                    start_time = time.time()
                    
                    # Create job (POST request)
                    params = self.create_test_generation_params(prompt=f"Network test {i}")
                    job = self.create_video_job_from_params(client, params)
                    
                    # Poll status (GET request)
                    job = client.poll_job_status(job.id, job.generation_id)
                    
                    end_time = time.time()
                    
                    network_operations.append({
                        "operation": "create_and_poll",
                        "duration": end_time - start_time,
                        "network_calls": 2
                    })
            
            # Analyze network resource usage
            avg_duration = statistics.mean([op["duration"] for op in network_operations])
            total_network_calls = sum(op["network_calls"] for op in network_operations)
            
            # Assert network efficiency
            assert avg_duration < 0.05, f"Network operations too slow: {avg_duration:.3f}s"
            assert total_network_calls == 100, f"Unexpected network call count: {total_network_calls}"
            
            # Log network metrics
            self.profiler.record_metric("avg_network_operation_time", avg_duration)
            self.profiler.record_metric("total_network_calls", total_network_calls)
            
    def test_resource_cleanup_efficiency(self):
        """Test resource cleanup and garbage collection efficiency."""
        with performance_monitor(self.profiler, "resource_cleanup"):
            # Create and cleanup resources multiple times
            cleanup_metrics = []
            
            for cycle in range(10):
                # Create resources
                resources = []
                
                start_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                
                for i in range(1000):
                    job = self.create_test_video_job(
                        f"cleanup-{cycle}-{i}",
                        prompt=f"Resource cleanup test {cycle}-{i}"
                    )
                    resources.append(job)
                
                peak_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                
                # Cleanup resources
                cleanup_start = time.time()
                resources.clear()
                gc.collect()
                cleanup_end = time.time()
                
                final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
                
                cleanup_metrics.append({
                    "cycle": cycle,
                    "memory_growth": peak_memory - start_memory,
                    "memory_cleanup": peak_memory - final_memory,
                    "cleanup_time": cleanup_end - cleanup_start,
                    "cleanup_efficiency": (peak_memory - final_memory) / (peak_memory - start_memory) if peak_memory > start_memory else 0
                })
            
            # Analyze cleanup efficiency
            avg_cleanup_time = statistics.mean([m["cleanup_time"] for m in cleanup_metrics])
            avg_cleanup_efficiency = statistics.mean([m["cleanup_efficiency"] for m in cleanup_metrics])
            
            # Assert cleanup efficiency
            assert avg_cleanup_time < 0.1, f"Cleanup too slow: {avg_cleanup_time:.3f}s"
            assert avg_cleanup_efficiency > 0.8, f"Cleanup efficiency too low: {avg_cleanup_efficiency:.2f}"
            
            # Log cleanup metrics
            self.profiler.record_metric("avg_cleanup_time", avg_cleanup_time)
            self.profiler.record_metric("avg_cleanup_efficiency", avg_cleanup_efficiency)


@pytest.mark.performance
@pytest.mark.slow
class TestCachePerformance(PerformanceTestBase):
    """Test cache performance and effectiveness."""
    
    def test_configuration_cache_performance(self):
        """Test configuration cache performance and hit rates."""
        with performance_monitor(self.profiler, "config_cache"):
            # Test configuration cache hits
            cache_hits = 0
            cache_misses = 0
            
            # Multiple configuration requests
            for i in range(100):
                start_time = time.time()
                
                # Get configuration (should be cached after first request)
                config = ConfigurationFactory.get_video_config()
                
                end_time = time.time()
                request_time = end_time - start_time
                
                # First request is a cache miss, subsequent are hits
                if i == 0:
                    cache_misses += 1
                    first_request_time = request_time
                else:
                    cache_hits += 1
                
                # Cache hits should be much faster
                if i > 0:
                    assert request_time < first_request_time * 0.1, \
                        f"Cache hit not fast enough: {request_time:.4f}s vs {first_request_time:.4f}s"
            
            # Calculate cache hit rate
            cache_hit_rate = cache_hits / (cache_hits + cache_misses)
            
            # Assert cache performance
            assert cache_hit_rate > 0.95, f"Cache hit rate too low: {cache_hit_rate:.2%}"
            
            # Log cache metrics
            self.profiler.record_metric("config_cache_hit_rate", cache_hit_rate)
            
    def test_session_cache_performance(self):
        """Test session cache performance and efficiency."""
        with performance_monitor(self.profiler, "session_cache"):
            with patch('src.session.manager.redis.Redis') as mock_redis_class:
                mock_redis = Mock()
                mock_redis_class.return_value = mock_redis
                
                # Session cache scenarios
                session_times = []
                
                # Test session creation and retrieval
                for i in range(50):
                    client_ip = f"192.168.1.{i % 10}"  # Repeat some IPs
                    
                    # First request for IP (cache miss)
                    mock_redis.exists.return_value = False
                    mock_redis.hset.return_value = True
                    
                    start_time = time.time()
                    session_id, session_data = get_or_create_session(client_ip)
                    end_time = time.time()
                    
                    session_times.append({
                        "ip": client_ip,
                        "is_new": True,
                        "time": end_time - start_time
                    })
                    
                    # Second request for same IP (cache hit)
                    mock_redis.exists.return_value = True
                    mock_redis.hgetall.return_value = {
                        "session_id": session_id.encode(),
                        "created_at": str(int(time.time())).encode()
                    }
                    
                    start_time = time.time()
                    cached_session_id, cached_session_data = get_or_create_session(client_ip)
                    end_time = time.time()
                    
                    session_times.append({
                        "ip": client_ip,
                        "is_new": False,
                        "time": end_time - start_time
                    })
                
                # Analyze session cache performance
                new_session_times = [t["time"] for t in session_times if t["is_new"]]
                cached_session_times = [t["time"] for t in session_times if not t["is_new"]]
                
                avg_new_time = statistics.mean(new_session_times)
                avg_cached_time = statistics.mean(cached_session_times)
                
                # Assert session cache efficiency
                assert avg_cached_time < avg_new_time * 0.5, \
                    f"Session cache not efficient: {avg_cached_time:.4f}s vs {avg_new_time:.4f}s"
                
                # Log session cache metrics
                self.profiler.record_metric("avg_new_session_time", avg_new_time)
                self.profiler.record_metric("avg_cached_session_time", avg_cached_time)
                
    def test_job_cache_performance(self):
        """Test job caching performance and effectiveness."""
        with performance_monitor(self.profiler, "job_cache"):
            # Test job caching with database
            with get_db_session() as session:
                repo = JobRepository(session)
                
                # Create test jobs
                job_ids = []
                for i in range(100):
                    job = self.create_test_video_job(f"cache-test-{i}")
                    created_job = repo.create_job(job)
                    job_ids.append(created_job.id)
                
                # Test job retrieval performance
                retrieval_times = []
                
                for job_id in job_ids:
                    # First retrieval (cache miss)
                    start_time = time.time()
                    job = repo.get_job_by_id(job_id)
                    end_time = time.time()
                    
                    retrieval_times.append({
                        "job_id": job_id,
                        "retrieval": "first",
                        "time": end_time - start_time
                    })
                    
                    # Second retrieval (potential cache hit)
                    start_time = time.time()
                    job = repo.get_job_by_id(job_id)
                    end_time = time.time()
                    
                    retrieval_times.append({
                        "job_id": job_id,
                        "retrieval": "second",
                        "time": end_time - start_time
                    })
                
                # Analyze job cache performance
                first_retrievals = [t["time"] for t in retrieval_times if t["retrieval"] == "first"]
                second_retrievals = [t["time"] for t in retrieval_times if t["retrieval"] == "second"]
                
                avg_first_time = statistics.mean(first_retrievals)
                avg_second_time = statistics.mean(second_retrievals)
                
                # Assert job cache effectiveness
                assert avg_first_time < 0.01, f"Job retrieval too slow: {avg_first_time:.4f}s"
                assert avg_second_time <= avg_first_time, f"Second retrieval not optimized: {avg_second_time:.4f}s"
                
                # Log job cache metrics
                self.profiler.record_metric("avg_first_job_retrieval", avg_first_time)
                self.profiler.record_metric("avg_second_job_retrieval", avg_second_time)
                
    def test_cache_memory_usage(self):
        """Test cache memory usage and optimization."""
        with performance_monitor(self.profiler, "cache_memory"):
            # Monitor memory usage during cache operations
            initial_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            
            # Create large cache dataset
            cache_data = {}
            for i in range(10000):
                key = f"cache_key_{i}"
                value = {
                    "id": f"job_{i}",
                    "data": f"cached data {i}" * 10,
                    "timestamp": time.time()
                }
                cache_data[key] = value
            
            cache_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            
            # Test cache access patterns
            access_times = []
            for i in range(1000):
                key = f"cache_key_{i % 100}"  # Access pattern with locality
                
                start_time = time.time()
                value = cache_data.get(key)
                end_time = time.time()
                
                access_times.append(end_time - start_time)
            
            # Clear cache
            cache_data.clear()
            gc.collect()
            
            final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            
            # Analyze cache memory usage
            cache_memory_usage = cache_memory - initial_memory
            memory_cleanup = cache_memory - final_memory
            avg_access_time = statistics.mean(access_times)
            
            # Assert cache memory efficiency
            assert cache_memory_usage < 50, f"Cache memory usage too high: {cache_memory_usage:.1f} MB"
            assert memory_cleanup > cache_memory_usage * 0.8, f"Cache cleanup insufficient: {memory_cleanup:.1f} MB"
            assert avg_access_time < 0.0001, f"Cache access too slow: {avg_access_time:.6f}s"
            
            # Log cache memory metrics
            self.profiler.record_metric("cache_memory_usage", cache_memory_usage)
            self.profiler.record_metric("cache_memory_cleanup", memory_cleanup)
            self.profiler.record_metric("avg_cache_access_time", avg_access_time)
            
    def test_cache_invalidation_performance(self):
        """Test cache invalidation performance and correctness."""
        with performance_monitor(self.profiler, "cache_invalidation"):
            # Simulate cache with TTL
            cache_data = {}
            cache_timestamps = {}
            ttl = 1  # 1 second TTL
            
            def get_from_cache(key):
                if key in cache_data:
                    if time.time() - cache_timestamps[key] < ttl:
                        return cache_data[key]
                    else:
                        # Cache expired, invalidate
                        del cache_data[key]
                        del cache_timestamps[key]
                return None
            
            def set_in_cache(key, value):
                cache_data[key] = value
                cache_timestamps[key] = time.time()
            
            # Test cache invalidation
            invalidation_times = []
            
            for i in range(100):
                key = f"invalidation_test_{i}"
                value = f"test_value_{i}"
                
                # Set in cache
                set_in_cache(key, value)
                
                # Access immediately (should hit)
                cached_value = get_from_cache(key)
                assert cached_value == value, "Cache hit failed"
                
                # Wait for expiration
                time.sleep(ttl + 0.01)
                
                # Access after expiration (should miss and invalidate)
                start_time = time.time()
                cached_value = get_from_cache(key)
                end_time = time.time()
                
                invalidation_times.append(end_time - start_time)
                assert cached_value is None, "Cache invalidation failed"
            
            # Analyze cache invalidation performance
            avg_invalidation_time = statistics.mean(invalidation_times)
            
            # Assert cache invalidation efficiency
            assert avg_invalidation_time < 0.001, f"Cache invalidation too slow: {avg_invalidation_time:.6f}s"
            
            # Log cache invalidation metrics
            self.profiler.record_metric("avg_invalidation_time", avg_invalidation_time)


@pytest.mark.performance
@pytest.mark.slow
class TestQueueProcessingPerformance(PerformanceTestBase):
    """Test queue processing performance and worker efficiency."""
    
    def test_job_queue_throughput(self):
        """Test job queue processing throughput."""
        with performance_monitor(self.profiler, "job_queue_throughput"):
            # Mock queue manager
            with patch('src.job_queue.manager.redis.Redis') as mock_redis_class:
                mock_redis = Mock()
                mock_redis_class.return_value = mock_redis
                
                # Setup queue mock
                queue_data = []
                
                def mock_lpush(key, value):
                    queue_data.append(value)
                    return len(queue_data)
                
                def mock_brpop(keys, timeout):
                    if queue_data:
                        return (keys[0], queue_data.pop(0))
                    return None
                
                mock_redis.lpush.side_effect = mock_lpush
                mock_redis.brpop.side_effect = mock_brpop
                mock_redis.llen.return_value = len(queue_data)
                
                # Test job queue processing
                queue_manager = QueueManager()
                
                # Add jobs to queue
                num_jobs = 1000
                start_time = time.time()
                
                for i in range(num_jobs):
                    job_data = {
                        "id": f"queue-test-{i}",
                        "prompt": f"Queue test {i}",
                        "session_id": "test-session"
                    }
                    queue_manager.enqueue_job("test-session", job_data)
                
                enqueue_time = time.time() - start_time
                
                # Process jobs from queue
                processed_jobs = []
                start_time = time.time()
                
                while len(processed_jobs) < num_jobs:
                    job = queue_manager.dequeue_job(timeout=0.1)
                    if job:
                        processed_jobs.append(job)
                    else:
                        break
                
                dequeue_time = time.time() - start_time
                
                # Calculate throughput
                enqueue_throughput = num_jobs / enqueue_time
                dequeue_throughput = len(processed_jobs) / dequeue_time if dequeue_time > 0 else 0
                
                # Assert queue throughput
                assert enqueue_throughput > 500, f"Enqueue throughput too low: {enqueue_throughput:.0f} jobs/s"
                assert dequeue_throughput > 500, f"Dequeue throughput too low: {dequeue_throughput:.0f} jobs/s"
                assert len(processed_jobs) == num_jobs, f"Job loss detected: {len(processed_jobs)} != {num_jobs}"
                
                # Log queue metrics
                self.profiler.record_metric("enqueue_throughput", enqueue_throughput)
                self.profiler.record_metric("dequeue_throughput", dequeue_throughput)
                
    def test_worker_processing_efficiency(self):
        """Test worker processing efficiency and resource usage."""
        with performance_monitor(self.profiler, "worker_efficiency"):
            # Mock worker processing
            def mock_worker_process(job_data):
                # Simulate processing time
                time.sleep(0.01)
                return {"status": "completed", "result": f"processed_{job_data['id']}"}
            
            # Test worker efficiency
            num_workers = 5
            jobs_per_worker = 50
            
            def worker_thread(worker_id):
                worker_metrics = {
                    "jobs_processed": 0,
                    "total_time": 0,
                    "processing_times": []
                }
                
                start_time = time.time()
                
                for i in range(jobs_per_worker):
                    job_data = {
                        "id": f"worker-{worker_id}-job-{i}",
                        "prompt": f"Worker {worker_id} job {i}"
                    }
                    
                    process_start = time.time()
                    result = mock_worker_process(job_data)
                    process_end = time.time()
                    
                    worker_metrics["jobs_processed"] += 1
                    worker_metrics["processing_times"].append(process_end - process_start)
                
                worker_metrics["total_time"] = time.time() - start_time
                return worker_metrics
            
            # Execute workers
            worker_results = []
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(worker_thread, i) for i in range(num_workers)]
                
                for future in as_completed(futures):
                    result = future.result()
                    worker_results.append(result)
            
            # Analyze worker efficiency
            total_jobs = sum(w["jobs_processed"] for w in worker_results)
            total_time = max(w["total_time"] for w in worker_results)
            
            all_processing_times = []
            for w in worker_results:
                all_processing_times.extend(w["processing_times"])
            
            avg_processing_time = statistics.mean(all_processing_times)
            overall_throughput = total_jobs / total_time
            
            # Assert worker efficiency
            assert total_jobs == num_workers * jobs_per_worker, f"Job loss detected: {total_jobs}"
            assert avg_processing_time < 0.02, f"Worker processing too slow: {avg_processing_time:.4f}s"
            assert overall_throughput > 100, f"Overall throughput too low: {overall_throughput:.0f} jobs/s"
            
            # Log worker metrics
            self.profiler.record_metric("avg_worker_processing_time", avg_processing_time)
            self.profiler.record_metric("overall_worker_throughput", overall_throughput)
            
    def test_queue_priority_performance(self):
        """Test queue priority handling performance."""
        with performance_monitor(self.profiler, "queue_priority"):
            # Mock priority queue
            with patch('src.job_queue.manager.redis.Redis') as mock_redis_class:
                mock_redis = Mock()
                mock_redis_class.return_value = mock_redis
                
                # Priority queue data structures
                high_priority_queue = []
                normal_priority_queue = []
                low_priority_queue = []
                
                def mock_lpush(key, value):
                    if "high" in key:
                        high_priority_queue.append(value)
                    elif "low" in key:
                        low_priority_queue.append(value)
                    else:
                        normal_priority_queue.append(value)
                    return 1
                
                def mock_brpop(keys, timeout):
                    # Process high priority first
                    if high_priority_queue:
                        return (keys[0], high_priority_queue.pop(0))
                    elif normal_priority_queue:
                        return (keys[1], normal_priority_queue.pop(0))
                    elif low_priority_queue:
                        return (keys[2], low_priority_queue.pop(0))
                    return None
                
                mock_redis.lpush.side_effect = mock_lpush
                mock_redis.brpop.side_effect = mock_brpop
                
                # Test priority queue performance
                queue_manager = QueueManager()
                
                # Add jobs with different priorities
                priorities = ["high", "normal", "low"]
                jobs_per_priority = 100
                
                start_time = time.time()
                
                for priority in priorities:
                    for i in range(jobs_per_priority):
                        job_data = {
                            "id": f"{priority}-job-{i}",
                            "prompt": f"{priority} priority job {i}",
                            "priority": priority
                        }
                        queue_manager.enqueue_job("test-session", job_data, priority=priority)
                
                enqueue_time = time.time() - start_time
                
                # Process jobs and verify priority order
                processed_jobs = []
                start_time = time.time()
                
                while len(processed_jobs) < jobs_per_priority * len(priorities):
                    job = queue_manager.dequeue_job(timeout=0.1)
                    if job:
                        processed_jobs.append(job)
                    else:
                        break
                
                process_time = time.time() - start_time
                
                # Verify priority order
                high_priority_count = sum(1 for job in processed_jobs[:jobs_per_priority] if "high" in job.get("id", ""))
                
                # Assert priority performance
                assert high_priority_count > jobs_per_priority * 0.8, f"Priority order not maintained: {high_priority_count}"
                
                # Calculate throughput
                throughput = len(processed_jobs) / process_time if process_time > 0 else 0
                assert throughput > 200, f"Priority queue throughput too low: {throughput:.0f} jobs/s"
                
                # Log priority metrics
                self.profiler.record_metric("priority_queue_throughput", throughput)
                self.profiler.record_metric("priority_order_accuracy", high_priority_count / jobs_per_priority)
                
    def test_queue_scalability_limits(self):
        """Test queue scalability under high load."""
        with performance_monitor(self.profiler, "queue_scalability"):
            # Test scalability with increasing load
            load_levels = [100, 500, 1000, 2000, 5000]
            
            for load_level in load_levels:
                with patch('src.job_queue.manager.redis.Redis') as mock_redis_class:
                    mock_redis = Mock()
                    mock_redis_class.return_value = mock_redis
                    
                    queue_data = []
                    
                    def mock_lpush(key, value):
                        queue_data.append(value)
                        return len(queue_data)
                    
                    def mock_brpop(keys, timeout):
                        if queue_data:
                            return (keys[0], queue_data.pop(0))
                        return None
                    
                    mock_redis.lpush.side_effect = mock_lpush
                    mock_redis.brpop.side_effect = mock_brpop
                    mock_redis.llen.return_value = len(queue_data)
                    
                    queue_manager = QueueManager()
                    
                    # Test scalability at this load level
                    start_time = time.time()
                    
                    # Enqueue jobs
                    for i in range(load_level):
                        job_data = {
                            "id": f"scale-test-{load_level}-{i}",
                            "prompt": f"Scalability test {i}"
                        }
                        queue_manager.enqueue_job("test-session", job_data)
                    
                    enqueue_time = time.time() - start_time
                    
                    # Dequeue jobs
                    processed = 0
                    start_time = time.time()
                    
                    while processed < load_level:
                        job = queue_manager.dequeue_job(timeout=0.1)
                        if job:
                            processed += 1
                        else:
                            break
                    
                    dequeue_time = time.time() - start_time
                    
                    # Calculate performance metrics
                    enqueue_rate = load_level / enqueue_time if enqueue_time > 0 else 0
                    dequeue_rate = processed / dequeue_time if dequeue_time > 0 else 0
                    
                    # Assert scalability
                    expected_min_rate = max(100, 1000 - (load_level - 100) * 0.1)  # Allow degradation
                    assert enqueue_rate > expected_min_rate, f"Enqueue rate degraded at {load_level}: {enqueue_rate:.0f} jobs/s"
                    assert dequeue_rate > expected_min_rate, f"Dequeue rate degraded at {load_level}: {dequeue_rate:.0f} jobs/s"
                    
                    # Log scalability metrics
                    self.profiler.record_metric(f"enqueue_rate_{load_level}", enqueue_rate)
                    self.profiler.record_metric(f"dequeue_rate_{load_level}", dequeue_rate)
                    
    def test_queue_error_handling_performance(self):
        """Test queue error handling performance impact."""
        with performance_monitor(self.profiler, "queue_error_handling"):
            # Test error scenarios
            error_rates = [0.1, 0.2, 0.3]  # 10%, 20%, 30% error rates
            
            for error_rate in error_rates:
                with patch('src.job_queue.manager.redis.Redis') as mock_redis_class:
                    mock_redis = Mock()
                    mock_redis_class.return_value = mock_redis
                    
                    queue_data = []
                    call_count = 0
                    
                    def mock_lpush_with_errors(key, value):
                        nonlocal call_count
                        call_count += 1
                        
                        # Simulate errors
                        if call_count % int(1 / error_rate) == 0:
                            raise redis.ConnectionError("Simulated error")
                        
                        queue_data.append(value)
                        return len(queue_data)
                    
                    def mock_brpop_with_errors(keys, timeout):
                        nonlocal call_count
                        call_count += 1
                        
                        # Simulate errors
                        if call_count % int(1 / error_rate) == 0:
                            raise redis.ConnectionError("Simulated error")
                        
                        if queue_data:
                            return (keys[0], queue_data.pop(0))
                        return None
                    
                    mock_redis.lpush.side_effect = mock_lpush_with_errors
                    mock_redis.brpop.side_effect = mock_brpop_with_errors
                    
                    queue_manager = QueueManager()
                    
                    # Test error handling performance
                    num_jobs = 200
                    successful_enqueues = 0
                    successful_dequeues = 0
                    
                    start_time = time.time()
                    
                    # Enqueue with error handling
                    for i in range(num_jobs):
                        try:
                            job_data = {
                                "id": f"error-test-{error_rate}-{i}",
                                "prompt": f"Error test {i}"
                            }
                            queue_manager.enqueue_job("test-session", job_data)
                            successful_enqueues += 1
                        except redis.ConnectionError:
                            # Expected error, continue
                            pass
                    
                    enqueue_time = time.time() - start_time
                    
                    # Dequeue with error handling
                    start_time = time.time()
                    
                    while successful_dequeues < successful_enqueues:
                        try:
                            job = queue_manager.dequeue_job(timeout=0.1)
                            if job:
                                successful_dequeues += 1
                        except redis.ConnectionError:
                            # Expected error, continue
                            pass
                        except:
                            # Other errors, break
                            break
                    
                    dequeue_time = time.time() - start_time
                    
                    # Calculate performance with errors
                    enqueue_rate = successful_enqueues / enqueue_time if enqueue_time > 0 else 0
                    dequeue_rate = successful_dequeues / dequeue_time if dequeue_time > 0 else 0
                    
                    # Assert error handling doesn't severely impact performance
                    min_expected_rate = 50 * (1 - error_rate)  # Adjust expectation for error rate
                    assert enqueue_rate > min_expected_rate, f"Error handling impacts enqueue rate too much: {enqueue_rate:.0f} jobs/s"
                    assert dequeue_rate > min_expected_rate, f"Error handling impacts dequeue rate too much: {dequeue_rate:.0f} jobs/s"
                    
                    # Log error handling metrics
                    self.profiler.record_metric(f"enqueue_rate_errors_{error_rate}", enqueue_rate)
                    self.profiler.record_metric(f"dequeue_rate_errors_{error_rate}", dequeue_rate)


@pytest.mark.performance
@pytest.mark.slow
class TestPerformanceRegression(PerformanceTestBase):
    """Test performance regression detection and baseline validation."""
    
    def test_performance_baseline_validation(self):
        """Test performance against established baselines."""
        with performance_monitor(self.profiler, "baseline_validation"):
            # Define performance baselines
            baselines = {
                "job_creation_time": 0.05,  # 50ms
                "job_retrieval_time": 0.01,  # 10ms
                "memory_usage_per_job": 0.001,  # 1KB per job
                "throughput_min": 100,  # 100 jobs/s
                "response_time_p95": 0.1,  # 100ms P95
            }
            
            # Test job creation performance
            job_creation_times = []
            
            with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                mock_post.return_value.status_code = 200
                mock_post.return_value.json.return_value = {
                    "data": [{"id": "gen-123", "status": "pending"}]
                }
                
                client = SoraClient()
                
                for i in range(100):
                    params = self.create_test_generation_params(prompt=f"Baseline test {i}")
                    
                    start_time = time.time()
                    job = self.create_video_job_from_params(client, params)
                    end_time = time.time()
                    
                    job_creation_times.append(end_time - start_time)
            
            # Test job retrieval performance
            job_retrieval_times = []
            
            with get_db_session() as session:
                repo = JobRepository(session)
                
                # Create test jobs
                for i in range(100):
                    job = self.create_test_video_job(f"baseline-retrieval-{i}")
                    repo.create_job(job)
                
                # Test retrieval times
                for i in range(100):
                    start_time = time.time()
                    job = repo.get_job_by_id(f"baseline-retrieval-{i}")
                    end_time = time.time()
                    
                    job_retrieval_times.append(end_time - start_time)
            
            # Test memory usage per job
            initial_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            
            jobs = []
            for i in range(1000):
                job = self.create_test_video_job(f"baseline-memory-{i}")
                jobs.append(job)
            
            final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            memory_per_job = (final_memory - initial_memory) / 1000
            
            # Calculate performance metrics
            avg_creation_time = statistics.mean(job_creation_times)
            avg_retrieval_time = statistics.mean(job_retrieval_times)
            p95_creation_time = sorted(job_creation_times)[int(len(job_creation_times) * 0.95)]
            
            # Validate against baselines
            self.assert_performance_baseline("job_creation_time", avg_creation_time, baselines["job_creation_time"])
            self.assert_performance_baseline("job_retrieval_time", avg_retrieval_time, baselines["job_retrieval_time"])
            self.assert_performance_baseline("memory_usage_per_job", memory_per_job, baselines["memory_usage_per_job"])
            self.assert_performance_baseline("response_time_p95", p95_creation_time, baselines["response_time_p95"])
            
            # Log baseline validation results
            self.profiler.record_metric("baseline_job_creation_time", avg_creation_time)
            self.profiler.record_metric("baseline_job_retrieval_time", avg_retrieval_time)
            self.profiler.record_metric("baseline_memory_per_job", memory_per_job)
            self.profiler.record_metric("baseline_p95_response_time", p95_creation_time)
            
    def test_performance_regression_detection(self):
        """Test detection of performance regressions."""
        with performance_monitor(self.profiler, "regression_detection"):
            # Simulate performance regression scenarios
            regression_scenarios = [
                {"name": "memory_leak", "factor": 2.0},
                {"name": "slow_queries", "factor": 3.0},
                {"name": "inefficient_algorithm", "factor": 1.5},
            ]
            
            for scenario in regression_scenarios:
                # Simulate the regression
                if scenario["name"] == "memory_leak":
                    # Simulate memory leak by creating objects without cleanup
                    leaked_objects = []
                    for i in range(1000):
                        job = self.create_test_video_job(
                            f"leak-{i}",
                            prompt=f"Memory leak test {i}"
                        )
                        leaked_objects.append(job)
                    
                    # Measure memory usage
                    memory_usage = self.profiler.process.memory_info().rss / 1024 / 1024
                    
                    # Detect regression
                    baseline_memory = 50  # MB
                    if memory_usage > baseline_memory * scenario["factor"]:
                        self.profiler.record_metric(f"regression_detected_{scenario['name']}", 1)
                    else:
                        self.profiler.record_metric(f"regression_detected_{scenario['name']}", 0)
                
                elif scenario["name"] == "slow_queries":
                    # Simulate slow database queries
                    query_times = []
                    
                    with get_db_session() as session:
                        repo = JobRepository(session)
                        
                        for i in range(50):
                            # Simulate slow query with artificial delay
                            start_time = time.time()
                            time.sleep(0.01 * scenario["factor"])  # Artificial slowdown
                            job = repo.get_job_by_id(f"slow-query-{i}")
                            end_time = time.time()
                            
                            query_times.append(end_time - start_time)
                    
                    avg_query_time = statistics.mean(query_times)
                    baseline_query_time = 0.01  # 10ms
                    
                    if avg_query_time > baseline_query_time * scenario["factor"]:
                        self.profiler.record_metric(f"regression_detected_{scenario['name']}", 1)
                    else:
                        self.profiler.record_metric(f"regression_detected_{scenario['name']}", 0)
                
                elif scenario["name"] == "inefficient_algorithm":
                    # Simulate inefficient algorithm
                    processing_times = []
                    
                    for i in range(100):
                        start_time = time.time()
                        
                        # Simulate O(n²) instead of O(n) algorithm
                        for j in range(int(100 * scenario["factor"])):
                            for k in range(int(100 * scenario["factor"])):
                                _ = j * k
                        
                        end_time = time.time()
                        processing_times.append(end_time - start_time)
                    
                    avg_processing_time = statistics.mean(processing_times)
                    baseline_processing_time = 0.001  # 1ms
                    
                    if avg_processing_time > baseline_processing_time * scenario["factor"]:
                        self.profiler.record_metric(f"regression_detected_{scenario['name']}", 1)
                    else:
                        self.profiler.record_metric(f"regression_detected_{scenario['name']}", 0)
            
            # Verify regression detection
            memory_leak_detected = self.profiler.metrics.get("regression_detected_memory_leak", [0])[-1]
            slow_queries_detected = self.profiler.metrics.get("regression_detected_slow_queries", [0])[-1]
            inefficient_algo_detected = self.profiler.metrics.get("regression_detected_inefficient_algorithm", [0])[-1]
            
            # Assert regression detection works
            assert memory_leak_detected == 1, "Memory leak regression not detected"
            assert slow_queries_detected == 1, "Slow query regression not detected"
            assert inefficient_algo_detected == 1, "Inefficient algorithm regression not detected"
            
    def test_performance_trend_analysis(self):
        """Test performance trend analysis over time."""
        with performance_monitor(self.profiler, "trend_analysis"):
            # Simulate performance measurements over time
            time_points = 10
            measurements = []
            
            for t in range(time_points):
                # Simulate gradual performance degradation
                degradation_factor = 1 + (t * 0.1)  # 10% degradation per time point
                
                # Measure job creation performance
                job_creation_times = []
                
                with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {
                        "data": [{"id": "gen-123", "status": "pending"}]
                    }
                    
                    client = SoraClient()
                    
                    for i in range(20):
                        params = self.create_test_generation_params(prompt=f"Trend test {t}-{i}")
                        
                        start_time = time.time()
                        
                        # Simulate degradation
                        time.sleep(0.001 * degradation_factor)
                        
                        job = self.create_video_job_from_params(client, params)
                        end_time = time.time()
                        
                        job_creation_times.append(end_time - start_time)
                
                avg_time = statistics.mean(job_creation_times)
                measurements.append({
                    "time_point": t,
                    "avg_creation_time": avg_time,
                    "degradation_factor": degradation_factor
                })
            
            # Analyze trend
            creation_times = [m["avg_creation_time"] for m in measurements]
            
            # Calculate trend (linear regression slope)
            n = len(creation_times)
            x_values = list(range(n))
            
            # Simple linear regression
            x_mean = statistics.mean(x_values)
            y_mean = statistics.mean(creation_times)
            
            numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, creation_times))
            denominator = sum((x - x_mean) ** 2 for x in x_values)
            
            slope = numerator / denominator if denominator != 0 else 0
            
            # Assert trend detection
            assert slope > 0, f"Performance degradation trend not detected: slope={slope:.6f}"
            
            # Check if slope indicates significant degradation
            significant_degradation_threshold = 0.001  # 1ms per time point
            assert slope > significant_degradation_threshold, f"Trend analysis sensitivity too low: slope={slope:.6f}"
            
            # Log trend analysis results
            self.profiler.record_metric("performance_trend_slope", slope)
            self.profiler.record_metric("performance_degradation_detected", 1 if slope > significant_degradation_threshold else 0)
            
    def test_performance_alerting_thresholds(self):
        """Test performance alerting thresholds and triggers."""
        with performance_monitor(self.profiler, "alerting_thresholds"):
            # Define alerting thresholds
            thresholds = {
                "response_time_critical": 0.5,  # 500ms
                "memory_usage_critical": 100,  # 100MB
                "error_rate_critical": 0.1,  # 10%
                "throughput_critical": 10,  # 10 jobs/s
            }
            
            alerts_triggered = []
            
            # Test response time alerting
            response_times = []
            
            with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                # Simulate slow responses
                def slow_response(*args, **kwargs):
                    time.sleep(0.6)  # Exceeds threshold
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"data": [{"id": "gen-123", "status": "pending"}]}
                    return response
                
                mock_post.side_effect = slow_response
                
                client = SoraClient()
                
                for i in range(10):
                    params = self.create_test_generation_params(prompt=f"Alert test {i}")
                    
                    start_time = time.time()
                    job = self.create_video_job_from_params(client, params)
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    
                    if response_time > thresholds["response_time_critical"]:
                        alerts_triggered.append("response_time_critical")
            
            # Test memory usage alerting
            initial_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            
            # Create large objects to trigger memory alert
            large_objects = []
            for i in range(10000):
                job = self.create_test_video_job(
                    f"memory-alert-{i}",
                    prompt=f"Memory alert test {i}"
                )
                large_objects.append(job)
            
            peak_memory = self.profiler.process.memory_info().rss / 1024 / 1024
            memory_usage = peak_memory - initial_memory
            
            if memory_usage > thresholds["memory_usage_critical"]:
                alerts_triggered.append("memory_usage_critical")
            
            # Test error rate alerting
            error_count = 0
            total_requests = 100
            
            with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                # Simulate errors
                def error_response(*args, **kwargs):
                    nonlocal error_count
                    error_count += 1
                    if error_count % 5 == 0:  # 20% error rate
                        raise Exception("Simulated error")
                    
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"data": [{"id": "gen-123", "status": "pending"}]}
                    return response
                
                mock_post.side_effect = error_response
                
                client = SoraClient()
                actual_errors = 0
                
                for i in range(total_requests):
                    try:
                        params = self.create_test_generation_params(prompt=f"Error test {i}")
                        job = self.create_video_job_from_params(client, params)
                    except Exception:
                        actual_errors += 1
                
                error_rate = actual_errors / total_requests
                
                if error_rate > thresholds["error_rate_critical"]:
                    alerts_triggered.append("error_rate_critical")
            
            # Test throughput alerting
            throughput_start = time.time()
            
            with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
                # Simulate slow processing
                def slow_processing(*args, **kwargs):
                    time.sleep(0.2)  # Very slow processing
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"data": [{"id": "gen-123", "status": "pending"}]}
                    return response
                
                mock_post.side_effect = slow_processing
                
                client = SoraClient()
                
                jobs_processed = 0
                for i in range(20):
                    params = self.create_test_generation_params(prompt=f"Throughput test {i}")
                    job = self.create_video_job_from_params(client, params)
                    jobs_processed += 1
            
            throughput_duration = time.time() - throughput_start
            throughput = jobs_processed / throughput_duration
            
            if throughput < thresholds["throughput_critical"]:
                alerts_triggered.append("throughput_critical")
            
            # Assert alerting system works
            assert "response_time_critical" in alerts_triggered, "Response time alert not triggered"
            assert "memory_usage_critical" in alerts_triggered, "Memory usage alert not triggered"
            assert "error_rate_critical" in alerts_triggered, "Error rate alert not triggered"
            assert "throughput_critical" in alerts_triggered, "Throughput alert not triggered"
            
            # Log alerting results
            self.profiler.record_metric("alerts_triggered_count", len(alerts_triggered))
            for alert in set(alerts_triggered):
                self.profiler.record_metric(f"alert_{alert}_triggered", 1)


if __name__ == "__main__":
    # Run performance tests
    pytest.main([
        __file__,
        "-v",
        "-m", "performance",
        "--tb=short",
        "--durations=10"
    ])