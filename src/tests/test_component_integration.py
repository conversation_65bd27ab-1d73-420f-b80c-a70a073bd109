"""Integration tests for component interactions."""

import asyncio
import os
import tempfile
import time
from datetime import datetime, timedelta
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
import redis
from flask import Flask

from src.api.job_repository import JobRepository
from src.core.models import GenerationParams, VideoJob
from src.database.connection import get_db_session
from src.database.models import VideoJobDB
from src.features.sora_integration.client import SoraClient
from src.features.sora_integration.http_client import SoraHttpClient
from src.features.sora_integration.job_manager import VideoJobManager
from src.features.sora_integration.video_downloader import VideoDownloader
from src.job_queue.manager import QueueManager
from src.job_queue.tasks import process_video_generation
from src.rate_limiting.limiter import GlobalRateLimiter
from src.rate_limiting.strategies.sliding_window import SlidingWindowStrategy
from src.realtime.broadcaster import broadcast_job_status
from src.session.manager import <PERSON><PERSON>anager, get_or_create_session


@pytest.mark.integration
class TestSoraClientComponentIntegration:
    """Test SoraClient component interactions."""
    
    def test_sora_client_component_initialization(self):
        """Test SoraClient properly initializes all components."""
        client = SoraClient()
        
        # Verify all components are initialized
        assert isinstance(client.http_client, SoraHttpClient)
        assert isinstance(client.job_manager, VideoJobManager)
        assert isinstance(client.video_downloader, VideoDownloader)
        
        # Verify components share the same http_client
        assert client.job_manager.http_client is client.http_client
        # Note: VideoDownloader uses its own requests session, not the shared http_client
    
    def test_sora_client_video_generation_workflow(self):
        """Test complete video generation workflow through SoraClient."""
        with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
             patch('src.features.sora_integration.http_client.requests.get') as mock_get, \
             patch('src.features.sora_integration.video_downloader.requests.get') as mock_download:
            
            # Mock job creation response
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "data": [{
                    "id": "gen-123",
                    "object": "video_generation",
                    "created": int(time.time()),
                    "status": "pending",
                    "prompt": "test prompt"
                }]
            }
            
            # Mock job polling responses
            mock_get.side_effect = [
                # First poll - still running
                Mock(status_code=200, json=lambda: {
                    "data": [{
                        "id": "gen-123",
                        "status": "running",
                        "progress": 0.5
                    }]
                }),
                # Second poll - completed
                Mock(status_code=200, json=lambda: {
                    "data": [{
                        "id": "gen-123",
                        "status": "completed",
                        "video_url": "https://example.com/video.mp4",
                        "progress": 1.0
                    }]
                })
            ]
            
            # Mock video download
            mock_download.return_value.status_code = 200
            mock_download.return_value.headers = {"content-type": "video/mp4"}
            mock_download.return_value.content = b"fake video content"
            
            client = SoraClient()
            params = GenerationParams(
                prompt="test prompt",
                duration=5,
                width=1920,
                height=1080
            )
            
            # Test job creation
            job = client.create_video_job(params)
            assert job.status == "pending"
            assert job.generation_id == "gen-123"
            
            # Test job polling
            with patch('time.sleep'):  # Skip actual sleep
                final_job = client.poll_job_status(job.id, "gen-123")
                assert final_job.status == "succeeded"
                assert final_job.file_path is not None
                assert final_job.download_url == "https://example.com/video.mp4"
    
    def test_sora_client_error_handling_integration(self):
        """Test error handling across SoraClient components."""
        with patch('src.features.sora_integration.http_client.requests.post') as mock_post:
            # Mock HTTP error
            mock_post.side_effect = Exception("Network error")
            
            client = SoraClient()
            params = GenerationParams(
                prompt="test prompt",
                duration=5,
                width=1920,
                height=1080
            )
            
            # Should return failed job instead of raising
            job = client.create_video_job(params)
            assert job.status == "failed"
            assert "Network error" in job.error_message


@pytest.mark.integration
class TestAPIBlueprintIntegration:
    """Test API blueprint integration."""
    
    def test_api_blueprint_registration(self):
        """Test that all API blueprints are properly registered."""
        from src.main import create_app
        
        app = create_app()
        
        # Check that blueprints are registered
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ['video_routes', 'health_routes', 'job_routes', 'file_routes']
        
        for expected in expected_blueprints:
            assert expected in blueprint_names, f"Blueprint {expected} not registered"
    
    def test_api_endpoint_integration(self):
        """Test integration between API endpoints and dependencies."""
        from src.main import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # Test health endpoint integration
            response = client.get('/health')
            assert response.status_code == 200
            
            data = response.get_json()
            assert data['success'] is True
            assert 'data' in data
            assert 'database' in data['data']
            assert 'azure_api' in data['data']
    
    def test_api_error_handling_integration(self):
        """Test error handling integration across API endpoints."""
        from src.main import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # Test invalid endpoint
            response = client.get('/nonexistent')
            assert response.status_code == 404
            
            # Test invalid method
            response = client.patch('/health')
            assert response.status_code == 405


@pytest.mark.integration
class TestRateLimitingIntegration:
    """Test rate limiting integration."""
    
    def test_rate_limiting_with_redis(self):
        """Test rate limiting integration with Redis."""
        # Mock Redis client
        mock_redis = Mock(spec=redis.Redis)
        mock_pipe = Mock()
        mock_pipe.execute.return_value = [None, 0]
        mock_redis.pipeline.return_value = mock_pipe
        
        # Test sliding window strategy with rate limiter
        strategy = SlidingWindowStrategy(mock_redis)
        rate_limiter = GlobalRateLimiter(mock_redis, strategy=strategy)
        
        # Test rate limiting logic
        with patch('time.time', return_value=1000.0):
            # First request should be allowed
            assert rate_limiter.is_allowed("test_key", 10, 60) is True
            
            # Verify Redis operations
            mock_pipe.zremrangebyscore.assert_called_once()
            mock_pipe.zcard.assert_called_once()
            mock_redis.zadd.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_async_rate_limiting_integration(self):
        """Test async rate limiting integration."""
        import redis.asyncio as aioredis
        
        # Mock async Redis client
        mock_redis = Mock(spec=aioredis.Redis)
        mock_pipe = Mock()
        mock_pipe.execute = AsyncMock(return_value=[None, 0])
        mock_redis.pipeline.return_value = mock_pipe
        mock_redis.zadd = AsyncMock()
        mock_redis.expire = AsyncMock()
        
        strategy = SlidingWindowStrategy(mock_redis)
        rate_limiter = GlobalRateLimiter(mock_redis, strategy=strategy)
        
        # Test async rate limiting
        with patch('time.time', return_value=1000.0):
            result = await rate_limiter.is_allowed_async("test_key", 10, 60)
            assert result is True
            
            # Verify async Redis operations
            mock_pipe.execute.assert_called_once()
            mock_redis.zadd.assert_called_once()


@pytest.mark.integration
class TestSessionManagementIntegration:
    """Test session management integration."""
    
    def test_session_creation_integration(self):
        """Test session creation and management integration."""
        with patch('src.session.manager.redis.Redis') as mock_redis_class:
            mock_redis = Mock()
            mock_redis_class.return_value = mock_redis
            mock_redis.exists.return_value = False
            mock_redis.hset.return_value = True
            mock_redis.expire.return_value = True
            
            session_manager = SessionManager()
            session_id, session_data = session_manager.create_session("***********")
            
            assert len(session_id) == 64  # Cryptographically secure
            assert session_data['ip_address'] == "***********"
            assert 'created_at' in session_data
            
            # Verify Redis operations
            mock_redis.hset.assert_called_once()
            mock_redis.expire.assert_called_once()
    
    def test_session_integration_with_get_or_create(self):
        """Test session integration with get_or_create function."""
        with patch('src.session.manager.redis.Redis') as mock_redis_class:
            mock_redis = Mock()
            mock_redis_class.return_value = mock_redis
            mock_redis.exists.return_value = False
            mock_redis.hset.return_value = True
            mock_redis.expire.return_value = True
            
            session_id, session_data = get_or_create_session("***********")
            
            assert session_id is not None
            assert session_data is not None
            assert session_data['ip_address'] == "***********"


@pytest.mark.integration
class TestDatabaseIntegration:
    """Test database integration."""
    
    def test_database_session_management(self):
        """Test database session management integration."""
        from src.database.connection import DatabaseManager
        
        # Test session creation and cleanup
        with get_db_session() as session:
            assert session is not None
            
            # Test basic query
            result = session.execute("SELECT 1").scalar()
            assert result == 1
    
    def test_job_repository_integration(self):
        """Test job repository integration with database."""
        # Create test job
        job = VideoJob(
            id="test-job-123",
            prompt="test prompt",
            status="pending",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        with get_db_session() as session:
            repo = JobRepository(session)
            
            # Test job creation
            created_job = repo.create_job(job)
            assert created_job.id == "test-job-123"
            assert created_job.status == "pending"
            
            # Test job retrieval
            retrieved_job = repo.get_job_by_id("test-job-123")
            assert retrieved_job is not None
            assert retrieved_job.id == "test-job-123"
            
            # Test job update
            repo.update_job_status("test-job-123", "running")
            updated_job = repo.get_job_by_id("test-job-123")
            assert updated_job.status == "running"


@pytest.mark.integration
class TestEndToEndWorkflow:
    """Test end-to-end video generation workflow."""
    
    def test_complete_video_generation_workflow(self):
        """Test complete video generation workflow integration."""
        with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
             patch('src.features.sora_integration.http_client.requests.get') as mock_get, \
             patch('src.features.sora_integration.video_downloader.requests.get') as mock_download, \
             patch('src.session.manager.redis.Redis') as mock_redis_class, \
             patch('src.realtime.broadcaster.socketio') as mock_socketio:
            
            # Mock Redis for session management
            mock_redis = Mock()
            mock_redis_class.return_value = mock_redis
            mock_redis.exists.return_value = False
            mock_redis.hset.return_value = True
            mock_redis.expire.return_value = True
            
            # Mock Sora API responses
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "data": [{
                    "id": "gen-123",
                    "object": "video_generation",
                    "created": int(time.time()),
                    "status": "pending",
                    "prompt": "test prompt"
                }]
            }
            
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "data": [{
                    "id": "gen-123",
                    "status": "completed",
                    "video_url": "https://example.com/video.mp4",
                    "progress": 1.0
                }]
            }
            
            mock_download.return_value.status_code = 200
            mock_download.return_value.headers = {"content-type": "video/mp4"}
            mock_download.return_value.content = b"fake video content"
            
            # Mock WebSocket broadcasting
            mock_socketio.emit = Mock()
            
            # Step 1: Create session
            session_id, session_data = get_or_create_session("***********")
            assert session_id is not None
            
            # Step 2: Create video generation job
            params = GenerationParams(
                prompt="test prompt",
                duration=5,
                width=1920,
                height=1080
            )
            
            with get_db_session() as db_session:
                job = VideoJob(
                    id="test-job-123",
                    prompt=params.prompt,
                    status="pending",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    session_id=session_id
                )
                
                repo = JobRepository(db_session)
                created_job = repo.create_job(job)
                
                # Step 3: Process video generation (simplified)
                client = SoraClient()
                
                # Test job creation
                api_job = client.create_video_job(params)
                assert api_job.generation_id == "gen-123"
                
                # Test job polling
                with patch('time.sleep'):  # Skip actual sleep
                    final_job = client.poll_job_status(api_job.id, "gen-123")
                    assert final_job.status == "succeeded"
                    assert final_job.file_path is not None
                
                # Step 4: Update database with results
                repo.update_job_status(created_job.id, "succeeded")
                updated_job = repo.get_job_by_id(created_job.id)
                assert updated_job.status == "succeeded"
                
                # Step 5: Broadcast status update
                broadcast_job_status(created_job.id, {
                    "status": "succeeded",
                    "file_path": final_job.file_path
                })
                
                # Verify WebSocket broadcast
                mock_socketio.emit.assert_called()
    
    def test_error_recovery_workflow(self):
        """Test error recovery in end-to-end workflow."""
        with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
             patch('src.session.manager.redis.Redis') as mock_redis_class, \
             patch('src.realtime.broadcaster.socketio') as mock_socketio:
            
            # Mock Redis for session management
            mock_redis = Mock()
            mock_redis_class.return_value = mock_redis
            mock_redis.exists.return_value = False
            mock_redis.hset.return_value = True
            mock_redis.expire.return_value = True
            
            # Mock API failure
            mock_post.side_effect = Exception("API connection failed")
            
            # Mock WebSocket broadcasting
            mock_socketio.emit = Mock()
            
            # Step 1: Create session
            session_id, session_data = get_or_create_session("***********")
            
            # Step 2: Attempt video generation with error
            params = GenerationParams(
                prompt="test prompt",
                duration=5,
                width=1920,
                height=1080
            )
            
            client = SoraClient()
            failed_job = client.create_video_job(params)
            
            # Verify error handling
            assert failed_job.status == "failed"
            assert "API connection failed" in failed_job.error_message
            
            # Step 3: Update database with error
            with get_db_session() as db_session:
                job = VideoJob(
                    id="test-job-123",
                    prompt=params.prompt,
                    status="failed",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    session_id=session_id,
                    error_message=failed_job.error_message
                )
                
                repo = JobRepository(db_session)
                created_job = repo.create_job(job)
                
                assert created_job.status == "failed"
                assert created_job.error_message is not None
                
                # Step 4: Broadcast error status
                broadcast_job_status(created_job.id, {
                    "status": "failed",
                    "error_message": created_job.error_message
                })
                
                # Verify error broadcast
                mock_socketio.emit.assert_called()


@pytest.mark.integration
@pytest.mark.slow
class TestConcurrentOperations:
    """Test concurrent operations integration."""
    
    def test_concurrent_session_creation(self):
        """Test concurrent session creation handling."""
        with patch('src.session.manager.redis.Redis') as mock_redis_class:
            mock_redis = Mock()
            mock_redis_class.return_value = mock_redis
            mock_redis.exists.return_value = False
            mock_redis.hset.return_value = True
            mock_redis.expire.return_value = True
            
            # Simulate multiple concurrent session creations
            session_ids = []
            for i in range(5):
                session_id, session_data = get_or_create_session(f"192.168.1.{i}")
                session_ids.append(session_id)
            
            # All sessions should have unique IDs
            assert len(set(session_ids)) == 5
            
            # Verify Redis operations for each session
            assert mock_redis.hset.call_count == 5
            assert mock_redis.expire.call_count == 5
    
    def test_concurrent_job_processing(self):
        """Test concurrent job processing simulation."""
        with patch('src.features.sora_integration.http_client.requests.post') as mock_post, \
             patch('src.features.sora_integration.http_client.requests.get') as mock_get:
            
            # Mock successful responses
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "data": [{
                    "id": "gen-123",
                    "object": "video_generation",
                    "created": int(time.time()),
                    "status": "pending",
                    "prompt": "test prompt"
                }]
            }
            
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "data": [{
                    "id": "gen-123",
                    "status": "completed",
                    "video_url": "https://example.com/video.mp4",
                    "progress": 1.0
                }]
            }
            
            client = SoraClient()
            
            # Simulate concurrent job creation
            jobs = []
            for i in range(3):
                params = GenerationParams(
                    prompt=f"test prompt {i}",
                    duration=5,
                    width=1920,
                    height=1080
                )
                job = client.create_video_job(params)
                jobs.append(job)
            
            # All jobs should be created successfully
            assert len(jobs) == 3
            assert all(job.status == "pending" for job in jobs)
            
            # Verify API calls
            assert mock_post.call_count == 3


@pytest.mark.integration
class TestResourceManagement:
    """Test resource management integration."""
    
    def test_file_cleanup_integration(self):
        """Test file cleanup integration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            test_files = []
            for i in range(3):
                file_path = os.path.join(temp_dir, f"test_video_{i}.mp4")
                with open(file_path, 'wb') as f:
                    f.write(b"fake video content")
                test_files.append(file_path)
            
            # Verify files exist
            assert all(os.path.exists(f) for f in test_files)
            
            # Test cleanup (simulated)
            from src.features.sora_integration.file_handler import FileHandler
            
            file_handler = FileHandler()
            
            # Cleanup should remove old files
            for file_path in test_files:
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            # Verify cleanup
            assert not any(os.path.exists(f) for f in test_files)
    
    def test_database_connection_pooling(self):
        """Test database connection pooling integration."""
        from src.database.connection import DatabaseManager
        
        # Test multiple database sessions
        sessions = []
        for i in range(3):
            session = get_db_session()
            sessions.append(session)
        
        # All sessions should be valid
        assert len(sessions) == 3
        
        # Cleanup sessions
        for session in sessions:
            session.close()
    
    def test_redis_connection_handling(self):
        """Test Redis connection handling integration."""
        with patch('redis.Redis') as mock_redis_class:
            mock_redis = Mock()
            mock_redis_class.return_value = mock_redis
            
            # Test Redis connection in different components
            
            # Session manager
            from src.session.manager import SessionManager
            session_manager = SessionManager()
            assert session_manager.redis is mock_redis
            
            # Rate limiter
            from src.rate_limiting.limiter import GlobalRateLimiter
            from src.rate_limiting.strategies.sliding_window import SlidingWindowStrategy
            
            strategy = SlidingWindowStrategy(mock_redis)
            rate_limiter = GlobalRateLimiter(mock_redis, strategy=strategy)
            assert rate_limiter.redis is mock_redis
            assert rate_limiter.strategy.redis is mock_redis