"""End-to-end integration tests with real Azure OpenAI Sora API."""

import os
import time
from pathlib import Path

import pytest
from dotenv import load_dotenv

from src.api.job_repository import JobRepository

# Load environment variables for integration tests
load_dotenv()


@pytest.mark.integration
@pytest.mark.slow
class TestVideoGenerationIntegration:
    """
    Integration tests for complete video generation workflow.

    These tests use the real Azure OpenAI Sora API to verify:
    - Job creation and database persistence
    - Status polling and updates
    - Video file generation and download
    - Complete end-to-end workflow

    Requirements:
    - Valid Azure OpenAI credentials in environment
    - Network connectivity to Azure API
    - Sufficient API quota for video generation
    """

    @pytest.fixture(autouse=True)
    def setup_integration_test(self):
        """
        Set up integration test environment.

        Raises:
            pytest.skip: If Azure credentials are not configured
        """
        # Check for required environment variables
        required_vars = [
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_SORA_DEPLOYMENT",
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            pytest.skip(
                f"Integration test skipped: missing environment variables: {missing_vars}"
            )

        # Initialize job repository for database verification
        self.job_repository = JobRepository()

        # Track created jobs for cleanup
        self.created_job_ids = []

        yield

        # Cleanup: remove test videos and database entries
        self._cleanup_test_artifacts()

    def _cleanup_test_artifacts(self):
        """Clean up test artifacts including files and database entries."""
        # Clean up database entries
        for job_id in self.created_job_ids:
            job = self.job_repository.get_job_by_id(job_id)
            if job:
                # Remove video file if it exists
                if job.file_path and Path(job.file_path).exists():
                    Path(job.file_path).unlink(missing_ok=True)

                # Remove database entry
                self.job_repository.delete_job(job_id)

    def test_complete_video_generation_workflow(self, client):
        """
        Test complete video generation workflow from creation to download.

        Steps:
        1. Create video generation job via API
        2. Verify job is stored in database
        3. Poll job status until completion
        4. Verify video file is created
        5. Test video download endpoint
        6. Verify file cleanup
        """
        # Step 1: Create video generation job
        test_prompt = "A red balloon floating in a blue sky, realistic style"

        response = client.post("/generate", data={"prompt": test_prompt})
        assert response.status_code == 200

        data = response.get_json()
        assert data["success"] is True
        assert "job_id" in data["data"]

        job_id = data["data"]["job_id"]
        self.created_job_ids.append(job_id)

        # Step 2: Verify job is stored in database
        db_job = self.job_repository.get_job_by_id(job_id)
        assert db_job is not None
        assert db_job.prompt == test_prompt
        assert db_job.status in ["pending", "running"]
        assert db_job.generation_id is not None

        # Step 3: Poll job status until completion (with timeout)
        max_wait_time = 300  # 5 minutes timeout
        poll_interval = 10  # Poll every 10 seconds
        start_time = time.time()

        final_status = None
        while time.time() - start_time < max_wait_time:
            response = client.get(f"/status/{job_id}")
            assert response.status_code == 200

            status_data = response.get_json()
            assert status_data["success"] is True

            current_status = status_data["data"]["status"]
            print(f"Job {job_id} status: {current_status}")

            if current_status in ["succeeded", "failed"]:
                final_status = current_status
                break

            time.sleep(poll_interval)

        # Verify job completed successfully
        assert final_status == "succeeded", (
            f"Job failed to complete within {max_wait_time}s. Final status: {final_status}"
        )

        # Step 4: Verify video file is created
        db_job = self.job_repository.get_job_by_id(job_id)
        assert db_job.status == "succeeded"
        assert db_job.file_path is not None
        assert db_job.download_url is not None
        assert db_job.completed_at is not None

        # Check file exists on filesystem
        video_file = Path(db_job.file_path)
        assert video_file.exists(), f"Video file not found at {db_job.file_path}"
        assert video_file.stat().st_size > 0, "Video file is empty"

        # Step 5: Test video streaming endpoint
        response = client.get(f"/video/{job_id}")
        assert response.status_code == 200
        assert response.mimetype == "video/mp4"
        assert len(response.data) > 0

        # Step 6: Test video download endpoint
        response = client.get(f"/download/{job_id}")
        assert response.status_code == 200
        assert response.mimetype == "video/mp4"
        assert "attachment" in response.headers.get("Content-Disposition", "")
        assert len(response.data) > 0

    def test_job_status_polling_accuracy(self, client):
        """
        Test that job status polling accurately reflects Azure API state.

        Verifies:
        - Status transitions are properly mapped
        - Database is updated with latest status
        - Error states are handled correctly
        """
        test_prompt = "A simple animation test for status polling"

        # Create job
        response = client.post("/generate", data={"prompt": test_prompt})
        job_id = response.get_json()["data"]["job_id"]
        self.created_job_ids.append(job_id)

        # Track status transitions
        status_history = []
        max_polls = 30  # Maximum number of status checks
        poll_count = 0

        while poll_count < max_polls:
            response = client.get(f"/status/{job_id}")
            status_data = response.get_json()
            current_status = status_data["data"]["status"]

            if not status_history or status_history[-1] != current_status:
                status_history.append(current_status)
                print(f"Status transition: {current_status}")

            # Verify database consistency
            db_job = self.job_repository.get_job_by_id(job_id)
            assert db_job.status == current_status, (
                "Database status doesn't match API response"
            )

            if current_status in ["succeeded", "failed"]:
                break

            time.sleep(5)
            poll_count += 1

        # Verify valid status progression
        assert len(status_history) > 0
        assert status_history[0] in ["pending", "running"]

        if status_history[-1] == "succeeded":
            # For successful jobs, verify final state
            db_job = self.job_repository.get_job_by_id(job_id)
            assert db_job.completed_at is not None
            assert db_job.file_path is not None

    def test_concurrent_job_processing(self, client):
        """
        Test system handles multiple concurrent video generation jobs.

        Verifies:
        - Multiple jobs can be created simultaneously
        - Each job maintains independent status
        - Database handles concurrent updates
        - Rate limiting doesn't cause failures
        """
        test_prompts = [
            "A cat sitting in a garden",
            "Ocean waves on a beach",
            "City traffic at sunset",
        ]

        # Create multiple jobs concurrently
        job_ids = []
        for prompt in test_prompts:
            response = client.post("/generate", data={"prompt": prompt})
            assert response.status_code == 200

            job_id = response.get_json()["data"]["job_id"]
            job_ids.append(job_id)
            self.created_job_ids.append(job_id)

        # Verify all jobs are created and independent
        for i, job_id in enumerate(job_ids):
            db_job = self.job_repository.get_job_by_id(job_id)
            assert db_job is not None
            assert db_job.prompt == test_prompts[i]
            assert db_job.generation_id is not None

            # Each job should have unique generation ID
            for other_job_id in job_ids:
                if other_job_id != job_id:
                    other_job = self.job_repository.get_job_by_id(other_job_id)
                    assert db_job.generation_id != other_job.generation_id

        # Monitor all jobs until completion (simplified for test speed)
        completion_timeout = 180  # 3 minutes for all jobs
        start_time = time.time()

        while time.time() - start_time < completion_timeout:
            all_completed = True

            for job_id in job_ids:
                response = client.get(f"/status/{job_id}")
                status = response.get_json()["data"]["status"]

                if status not in ["succeeded", "failed"]:
                    all_completed = False
                    break

            if all_completed:
                break

            time.sleep(15)  # Check every 15 seconds

        # Verify at least some jobs completed (Azure API may have quota limits)
        completed_jobs = 0
        for job_id in job_ids:
            response = client.get(f"/status/{job_id}")
            status = response.get_json()["data"]["status"]
            if status == "succeeded":
                completed_jobs += 1

        assert completed_jobs > 0, "No jobs completed successfully"

    def test_error_handling_integration(self, client):
        """
        Test error handling in integration with real API.

        Tests scenarios that might cause real API errors:
        - Very long prompts (boundary testing)
        - Invalid prompt content
        - Network timeout scenarios
        """
        # Test prompt length boundary
        long_prompt = "A detailed scene " * 50  # ~750 characters

        response = client.post("/generate", data={"prompt": long_prompt})

        # Should either succeed or fail gracefully
        if response.status_code == 200:
            job_id = response.get_json()["data"]["job_id"]
            self.created_job_ids.append(job_id)

            # Poll for completion
            for _ in range(10):  # Limited polls for error test
                status_response = client.get(f"/status/{job_id}")
                status = status_response.get_json()["data"]["status"]

                if status in ["succeeded", "failed"]:
                    # Verify error is properly recorded if failed
                    if status == "failed":
                        db_job = self.job_repository.get_job_by_id(job_id)
                        assert db_job.error_message is not None
                    break

                time.sleep(5)
        else:
            # Should be a validation error
            assert response.status_code == 400
            data = response.get_json()
            assert data["success"] is False

    def test_database_consistency_under_load(self, client):
        """
        Test database consistency during concurrent operations.

        Verifies:
        - Database handles concurrent job updates
        - No race conditions in status updates
        - Proper transaction handling
        """
        # Create a job and poll it from multiple "clients" simultaneously
        response = client.post(
            "/generate", data={"prompt": "Database consistency test"}
        )
        job_id = response.get_json()["data"]["job_id"]
        self.created_job_ids.append(job_id)

        # Simulate concurrent status polling
        import queue
        import threading

        results_queue = queue.Queue()

        def poll_status():
            """Poll job status and record results."""
            for _ in range(5):  # 5 polls per thread
                response = client.get(f"/status/{job_id}")
                if response.status_code == 200:
                    status = response.get_json()["data"]["status"]
                    results_queue.put(status)
                time.sleep(2)

        # Start multiple polling threads
        threads = []
        for _ in range(3):  # 3 concurrent polling threads
            thread = threading.Thread(target=poll_status)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Collect all status results
        statuses = []
        while not results_queue.empty():
            statuses.append(results_queue.get())

        # Verify we got consistent results
        assert len(statuses) > 0

        # All statuses should be valid
        valid_statuses = {"pending", "running", "succeeded", "failed"}
        for status in statuses:
            assert status in valid_statuses

        # Final database state should be consistent
        final_job = self.job_repository.get_job_by_id(job_id)
        assert final_job is not None
        assert final_job.status in valid_statuses
