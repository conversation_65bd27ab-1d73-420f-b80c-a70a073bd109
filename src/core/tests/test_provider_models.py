"""Tests for provider-aware core models."""

from datetime import datetime

import pytest
from pydantic import ValidationError

from src.core.models import VideoJob


class TestVideoJobProviderSupport:
    """Test VideoJob Pydantic model with provider support."""

    def test_create_job_with_azure_provider(self):
        """Test creating a job with Azure Sora provider."""
        job = VideoJob(
            id="test-job-azure-123",
            prompt="Test Azure prompt",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="azure_sora",
            input_image_path=None,
            audio_generated=False,
        )

        assert job.api_provider == "azure_sora"
        assert job.input_image_path is None
        assert job.audio_generated is False

    def test_create_job_with_veo3_provider(self):
        """Test creating a job with Google Veo3 provider."""
        job = VideoJob(
            id="test-job-veo3-456",
            prompt="Test Veo3 prompt",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            input_image_path="/path/to/image.jpg",
            audio_generated=True,
        )

        assert job.api_provider == "google_veo3"
        assert job.input_image_path == "/path/to/image.jpg"
        assert job.audio_generated is True

    def test_default_provider_values(self):
        """Test default values for provider fields."""
        job = VideoJob(
            id="test-job-defaults-789",
            prompt="Test default values",
            status="pending",
            created_at=datetime.utcnow(),
        )

        assert job.api_provider == "azure_sora"  # Default provider
        assert job.input_image_path is None
        assert job.audio_generated is False

    def test_invalid_provider_validation(self):
        """Test validation fails for invalid provider."""
        with pytest.raises(ValidationError) as exc_info:
            VideoJob(
                id="test-invalid-provider",
                prompt="Test invalid provider",
                status="pending",
                created_at=datetime.utcnow(),
                api_provider="invalid_provider",
            )

        error = exc_info.value.errors()[0]
        assert "api_provider" in str(error)
        assert "azure_sora" in str(error) or "google_veo3" in str(error)

    def test_provider_literal_type_enforcement(self):
        """Test that api_provider field enforces Literal type."""
        # Valid providers should work
        for provider in ["azure_sora", "google_veo3"]:
            job = VideoJob(
                id=f"test-{provider}",
                prompt="Test provider",
                status="pending",
                created_at=datetime.utcnow(),
                api_provider=provider,
            )
            assert job.api_provider == provider

        # Invalid provider should fail
        with pytest.raises(ValidationError):
            VideoJob(
                id="test-invalid",
                prompt="Test invalid",
                status="pending",
                created_at=datetime.utcnow(),
                api_provider="openai_dall_e",
            )

    def test_provider_validator_function(self):
        """Test the api_provider field validator."""
        # Test valid providers
        job = VideoJob(
            id="test-validator-1",
            prompt="Test validator",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="azure_sora",
        )
        assert job.api_provider == "azure_sora"

        job = VideoJob(
            id="test-validator-2",
            prompt="Test validator",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
        )
        assert job.api_provider == "google_veo3"

        # Test invalid provider
        with pytest.raises(ValidationError) as exc_info:
            VideoJob(
                id="test-validator-invalid",
                prompt="Test validator",
                status="pending",
                created_at=datetime.utcnow(),
                api_provider="unknown_provider",
            )

        assert "Input should be 'azure_sora' or 'google_veo3'" in str(exc_info.value)

    def test_input_image_path_validation(self):
        """Test input_image_path field validation."""
        # Valid paths
        valid_paths = [
            None,
            "/path/to/image.jpg",
            "/uploads/user123/input.png",
            "s3://bucket/image.jpeg",
            "",  # Empty string should be allowed
        ]

        for path in valid_paths:
            job = VideoJob(
                id=f"test-path-{hash(str(path))}",
                prompt="Test image path",
                status="pending",
                created_at=datetime.utcnow(),
                api_provider="google_veo3",
                input_image_path=path,
            )
            assert job.input_image_path == path

    def test_audio_generated_boolean_validation(self):
        """Test audio_generated field boolean validation."""
        # Valid boolean values
        for audio_flag in [True, False]:
            job = VideoJob(
                id=f"test-audio-{audio_flag}",
                prompt="Test audio flag",
                status="pending",
                created_at=datetime.utcnow(),
                api_provider="google_veo3",
                audio_generated=audio_flag,
            )
            assert job.audio_generated == audio_flag

    def test_model_serialization_with_provider_fields(self):
        """Test model serialization includes provider fields."""
        job = VideoJob(
            id="test-serialization-123",
            prompt="Test serialization",
            status="succeeded",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            input_image_path="/serialization/image.png",
            audio_generated=True,
        )

        # Test model_dump
        job_dict = job.model_dump()
        assert job_dict["api_provider"] == "google_veo3"
        assert job_dict["input_image_path"] == "/serialization/image.png"
        assert job_dict["audio_generated"] is True

        # Test model_dump_json
        job_json = job.model_dump_json()
        assert '"api_provider":"google_veo3"' in job_json
        assert '"input_image_path":"/serialization/image.png"' in job_json
        assert '"audio_generated":true' in job_json

    def test_model_deserialization_with_provider_fields(self):
        """Test model deserialization includes provider fields."""
        job_data = {
            "id": "test-deserialization-456",
            "prompt": "Test deserialization",
            "status": "running",
            "created_at": datetime.utcnow(),
            "api_provider": "azure_sora",
            "input_image_path": "/deserialization/image.jpg",
            "audio_generated": False,
        }

        job = VideoJob(**job_data)
        assert job.api_provider == "azure_sora"
        assert job.input_image_path == "/deserialization/image.jpg"
        assert job.audio_generated is False

    def test_backward_compatibility(self):
        """Test that existing jobs without provider fields still work."""
        # Old job data without provider fields
        old_job_data = {
            "id": "old-job-123",
            "prompt": "Old job without provider fields",
            "status": "succeeded",
            "created_at": datetime.utcnow(),
            "session_id": "session-456",
            "priority": 1,
        }

        # Should create successfully with defaults
        job = VideoJob(**old_job_data)
        assert job.api_provider == "azure_sora"  # Default
        assert job.input_image_path is None
        assert job.audio_generated is False

    def test_provider_field_combinations(self):
        """Test various combinations of provider fields."""
        test_cases = [
            # Azure Sora typical usage
            {
                "api_provider": "azure_sora",
                "input_image_path": None,
                "audio_generated": False,
            },
            # Google Veo3 with image input
            {
                "api_provider": "google_veo3",
                "input_image_path": "/uploads/input.jpg",
                "audio_generated": False,
            },
            # Google Veo3 with audio generation
            {
                "api_provider": "google_veo3",
                "input_image_path": "/uploads/input.png",
                "audio_generated": True,
            },
            # Edge case: Azure with image path (should be allowed)
            {
                "api_provider": "azure_sora",
                "input_image_path": "/some/path.jpg",
                "audio_generated": True,
            },
        ]

        for i, case in enumerate(test_cases):
            job = VideoJob(
                id=f"test-combination-{i}",
                prompt=f"Test combination {i}",
                status="pending",
                created_at=datetime.utcnow(),
                **case,
            )

            assert job.api_provider == case["api_provider"]
            assert job.input_image_path == case["input_image_path"]
            assert job.audio_generated == case["audio_generated"]

    def test_field_constraints(self):
        """Test field constraints are maintained with provider fields."""
        # Test prompt validation still works
        with pytest.raises(ValidationError):
            VideoJob(
                id="test-constraints-1",
                prompt="",  # Empty prompt should fail
                status="pending",
                created_at=datetime.utcnow(),
                api_provider="azure_sora",
            )

        # Test priority constraints still work
        job = VideoJob(
            id="test-constraints-2",
            prompt="Test constraints",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            priority=5,  # Valid priority
        )
        assert job.priority == 5

    def test_model_copy_with_provider_fields(self):
        """Test model copying includes provider fields."""
        original_job = VideoJob(
            id="original-job-789",
            prompt="Original job",
            status="pending",
            created_at=datetime.utcnow(),
            api_provider="google_veo3",
            input_image_path="/original/image.png",
            audio_generated=True,
        )

        # Copy with updates
        updated_job = original_job.model_copy(
            update={
                "status": "running",
                "api_provider": "azure_sora",
                "audio_generated": False,
            }
        )

        # Original should be unchanged
        assert original_job.status == "pending"
        assert original_job.api_provider == "google_veo3"
        assert original_job.audio_generated is True

        # Updated should have changes
        assert updated_job.status == "running"
        assert updated_job.api_provider == "azure_sora"
        assert updated_job.audio_generated is False
        assert updated_job.input_image_path == "/original/image.png"  # Unchanged
