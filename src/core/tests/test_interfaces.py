"""
Tests for core interfaces module.

Comprehensive tests for all abstract interfaces to ensure proper contract
definition and edge case handling for dependency injection.
"""

from abc import ABC
from typing import Any, Dict, List, Optional
from unittest.mock import MagicMock

import pytest

from src.core.interfaces import (
    FileHandlerInterface,
    HealthCheckInterface,
    JobRepositoryInterface,
    QueueManagerInterface,
    SessionManagerInterface,
)
from src.core.models import VideoJob


class TestJobRepositoryInterface:
    """Test JobRepositoryInterface abstract methods and contract."""

    def test_interface_is_abstract(self):
        """Test that JobRepositoryInterface cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            JobRepositoryInterface()

    def test_interface_methods_are_abstract(self):
        """Test that all interface methods are properly marked as abstract."""
        # Verify all methods have __isabstractmethod__ attribute set to True
        abstract_methods = [
            "get_job_by_id",
            "get_job_stats",
            "get_pending_jobs",
            "get_running_jobs",
            "get_jobs_by_status",
            "get_jobs_by_session",
            "get_jobs_by_session_and_status",
            "delete_job",
            "create_job",
            "update_job",
            "get_active_jobs_by_session",
        ]

        for method_name in abstract_methods:
            method = getattr(JobRepositoryInterface, method_name)
            assert getattr(method, "__isabstractmethod__", False), (
                f"{method_name} should be abstract"
            )

    def test_concrete_implementation_must_implement_all_methods(self):
        """Test that concrete implementations must implement all abstract methods."""

        class IncompleteRepository(JobRepositoryInterface):
            """Incomplete implementation missing most methods."""

            def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
                return None

        # Should raise TypeError for missing abstract methods
        with pytest.raises(TypeError):
            IncompleteRepository()

    def test_complete_concrete_implementation_works(self):
        """Test that complete implementation can be instantiated."""

        class CompleteRepository(JobRepositoryInterface):
            """Complete implementation of all interface methods."""

            def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
                return None

            def get_job_stats(self) -> Dict[str, Any]:
                return {}

            def get_pending_jobs(self) -> List[VideoJob]:
                return []

            def get_running_jobs(self) -> List[VideoJob]:
                return []

            def get_jobs_by_status(
                self, status: str, limit: int = 100
            ) -> List[VideoJob]:
                return []

            def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                return []

            def get_jobs_by_session_and_status(
                self, session_id: str, status: str
            ) -> List[VideoJob]:
                return []

            def delete_job(self, job_id: str) -> bool:
                return True

            def create_job(self, job: VideoJob) -> Optional[VideoJob]:
                return job

            def update_job(self, job: VideoJob) -> Optional[VideoJob]:
                return job

            def get_active_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                return []

        # Should be able to instantiate
        repo = CompleteRepository()
        assert isinstance(repo, JobRepositoryInterface)

    def test_method_signatures_type_hints(self):
        """Test that method signatures have proper type hints."""
        import inspect

        # Check key method signatures
        get_job_by_id = JobRepositoryInterface.get_job_by_id
        sig = inspect.signature(get_job_by_id)

        assert "job_id" in sig.parameters
        assert sig.parameters["job_id"].annotation == str

        # Check return type annotation
        assert sig.return_annotation != inspect.Signature.empty

    def test_edge_case_method_parameters(self):
        """Test edge cases for method parameters."""

        class TestRepository(JobRepositoryInterface):
            """Test repository for parameter validation."""

            def __init__(self):
                self.calls = []

            def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
                self.calls.append(("get_job_by_id", job_id))
                # Test with None/empty job_id
                if not job_id:
                    return None
                return None

            def get_job_stats(self) -> Dict[str, Any]:
                return {
                    "total": 0,
                    "pending": 0,
                    "running": 0,
                    "completed": 0,
                    "failed": 0,
                }

            def get_pending_jobs(self) -> List[VideoJob]:
                return []

            def get_running_jobs(self) -> List[VideoJob]:
                return []

            def get_jobs_by_status(
                self, status: str, limit: int = 100
            ) -> List[VideoJob]:
                self.calls.append(("get_jobs_by_status", status, limit))
                # Test edge cases for limit
                if limit <= 0:
                    return []
                return []

            def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                self.calls.append(("get_jobs_by_session", session_id))
                return []

            def get_jobs_by_session_and_status(
                self, session_id: str, status: str
            ) -> List[VideoJob]:
                self.calls.append(
                    ("get_jobs_by_session_and_status", session_id, status)
                )
                return []

            def delete_job(self, job_id: str) -> bool:
                self.calls.append(("delete_job", job_id))
                # Test with invalid job_id
                if not job_id:
                    return False
                return True

            def create_job(self, job: VideoJob) -> Optional[VideoJob]:
                self.calls.append(("create_job", job))
                # Test with None job
                if job is None:
                    return None
                return job

            def update_job(self, job: VideoJob) -> Optional[VideoJob]:
                self.calls.append(("update_job", job))
                return job

            def get_active_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                self.calls.append(("get_active_jobs_by_session", session_id))
                return []

        repo = TestRepository()

        # Test edge cases
        assert repo.get_job_by_id("") is None
        assert repo.get_job_by_id(None) is None
        assert repo.get_jobs_by_status("pending", 0) == []
        assert repo.get_jobs_by_status("pending", -1) == []
        assert repo.delete_job("") is False
        assert repo.create_job(None) is None


class TestQueueManagerInterface:
    """Test QueueManagerInterface abstract methods and contract."""

    def test_interface_is_abstract(self):
        """Test that QueueManagerInterface cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            QueueManagerInterface()

    def test_interface_methods_are_abstract(self):
        """Test that all interface methods are properly marked as abstract."""
        abstract_methods = [
            "assign_queue_position",
            "get_queue_status",
            "get_session_queue_status",
            "get_queue_statistics",
            "can_submit_job",
            "get_estimated_wait_time",
        ]

        for method_name in abstract_methods:
            method = getattr(QueueManagerInterface, method_name)
            assert getattr(method, "__isabstractmethod__", False), (
                f"{method_name} should be abstract"
            )

    def test_complete_implementation_works(self):
        """Test complete QueueManager implementation."""

        class TestQueueManager(QueueManagerInterface):
            """Test implementation of QueueManagerInterface."""

            def assign_queue_position(self, job_id: str, session_id: str) -> int:
                return 1

            def get_queue_status(self, session_id: str) -> Dict[str, Any]:
                return {"position": 1, "estimated_wait": 5}

            def get_session_queue_status(self, session_id: str) -> Dict[str, Any]:
                return {"active_jobs": 2, "queued_jobs": 1}

            def get_queue_statistics(self) -> Dict[str, Any]:
                return {"total_jobs": 10, "processing": 2, "waiting": 3}

            def can_submit_job(self, session_id: str) -> tuple[bool, str]:
                return True, "OK"

            def get_estimated_wait_time(self, session_id: str) -> int:
                return 5

        manager = TestQueueManager()
        assert isinstance(manager, QueueManagerInterface)

        # Test method calls
        assert manager.assign_queue_position("job1", "session1") == 1
        assert manager.can_submit_job("session1") == (True, "OK")
        assert manager.get_estimated_wait_time("session1") == 5

    def test_edge_case_return_types(self):
        """Test edge cases for return types."""

        class EdgeCaseQueueManager(QueueManagerInterface):
            """Test edge cases for queue manager."""

            def assign_queue_position(self, job_id: str, session_id: str) -> int:
                # Edge case: invalid inputs
                if not job_id or not session_id:
                    return -1
                return 1

            def get_queue_status(self, session_id: str) -> Dict[str, Any]:
                if not session_id:
                    return {}
                return {"position": 1}

            def get_session_queue_status(self, session_id: str) -> Dict[str, Any]:
                return {"active_jobs": 0, "queued_jobs": 0}

            def get_queue_statistics(self) -> Dict[str, Any]:
                return {"total_jobs": 0}

            def can_submit_job(self, session_id: str) -> tuple[bool, str]:
                if not session_id:
                    return False, "Invalid session ID"
                return True, "OK"

            def get_estimated_wait_time(self, session_id: str) -> int:
                if not session_id:
                    return 0
                return 1

        manager = EdgeCaseQueueManager()

        # Test edge cases
        assert manager.assign_queue_position("", "session1") == -1
        assert manager.assign_queue_position("job1", "") == -1
        assert manager.get_queue_status("") == {}
        assert manager.can_submit_job("") == (False, "Invalid session ID")
        assert manager.get_estimated_wait_time("") == 0


class TestSessionManagerInterface:
    """Test SessionManagerInterface abstract methods and contract."""

    def test_interface_is_abstract(self):
        """Test that SessionManagerInterface cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            SessionManagerInterface()

    def test_complete_implementation_works(self):
        """Test complete SessionManager implementation."""

        class TestSessionManager(SessionManagerInterface):
            """Test implementation of SessionManagerInterface."""

            def __init__(self):
                self.sessions = {}

            def validate_session(self, session_id: str) -> tuple[bool, str]:
                if session_id in self.sessions:
                    return True, "Valid session"
                return False, "Session not found"

            def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
                return self.sessions.get(session_id)

            def create_session(self, client_ip: str) -> tuple[str, Dict[str, Any]]:
                session_id = f"session_{len(self.sessions)}"
                session_data = {"client_ip": client_ip, "created_at": "2024-01-01"}
                self.sessions[session_id] = session_data
                return session_id, session_data

            def cleanup_expired_sessions(self) -> int:
                # Mock cleanup
                return 0

        manager = TestSessionManager()

        # Test session creation
        session_id, session_data = manager.create_session("127.0.0.1")
        assert session_id.startswith("session_")
        assert session_data["client_ip"] == "127.0.0.1"

        # Test session validation
        is_valid, message = manager.validate_session(session_id)
        assert is_valid is True
        assert message == "Valid session"

        # Test invalid session
        is_valid, message = manager.validate_session("invalid")
        assert is_valid is False
        assert message == "Session not found"

    def test_session_edge_cases(self):
        """Test edge cases for session management."""

        class EdgeSessionManager(SessionManagerInterface):
            """Test edge cases for session management."""

            def validate_session(self, session_id: str) -> tuple[bool, str]:
                if not session_id:
                    return False, "Empty session ID"
                if len(session_id) < 10:
                    return False, "Session ID too short"
                return True, "Valid"

            def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
                if not session_id:
                    return None
                return {"mock": "data"}

            def create_session(self, client_ip: str) -> tuple[str, Dict[str, Any]]:
                if not client_ip or client_ip == "0.0.0.0":
                    return "", {}
                return "valid_session_id", {"client_ip": client_ip}

            def cleanup_expired_sessions(self) -> int:
                return 5  # Mock cleanup count

        manager = EdgeSessionManager()

        # Test edge cases
        assert manager.validate_session("") == (False, "Empty session ID")
        assert manager.validate_session("short") == (False, "Session ID too short")
        assert manager.get_session_data("") is None
        assert manager.create_session("0.0.0.0") == ("", {})
        assert manager.cleanup_expired_sessions() == 5


class TestHealthCheckInterface:
    """Test HealthCheckInterface abstract methods and contract."""

    def test_interface_is_abstract(self):
        """Test that HealthCheckInterface cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            HealthCheckInterface()

    def test_complete_implementation_works(self):
        """Test complete HealthCheck implementation."""

        class TestHealthCheck(HealthCheckInterface):
            """Test implementation of HealthCheckInterface."""

            def check_database_health(self) -> Dict[str, Any]:
                return {"status": "healthy", "response_time": 50}

            def check_azure_health(self) -> Dict[str, Any]:
                return {"status": "healthy", "api_accessible": True}

            def check_job_queue_health(self) -> Dict[str, Any]:
                return {"status": "healthy", "pending_jobs": 5}

            def get_overall_health(self) -> Dict[str, Any]:
                return {
                    "status": "healthy",
                    "components": {
                        "database": self.check_database_health(),
                        "azure": self.check_azure_health(),
                        "queue": self.check_job_queue_health(),
                    },
                }

        health = TestHealthCheck()

        # Test individual health checks
        db_health = health.check_database_health()
        assert db_health["status"] == "healthy"

        azure_health = health.check_azure_health()
        assert azure_health["status"] == "healthy"

        queue_health = health.check_job_queue_health()
        assert queue_health["status"] == "healthy"

        # Test overall health
        overall = health.get_overall_health()
        assert overall["status"] == "healthy"
        assert "components" in overall

    def test_health_check_failure_scenarios(self):
        """Test health check failure scenarios."""

        class FailingHealthCheck(HealthCheckInterface):
            """Test failing health checks."""

            def check_database_health(self) -> Dict[str, Any]:
                return {"status": "unhealthy", "error": "Connection timeout"}

            def check_azure_health(self) -> Dict[str, Any]:
                return {"status": "unhealthy", "error": "API key invalid"}

            def check_job_queue_health(self) -> Dict[str, Any]:
                return {"status": "unhealthy", "error": "Redis connection failed"}

            def get_overall_health(self) -> Dict[str, Any]:
                components = {
                    "database": self.check_database_health(),
                    "azure": self.check_azure_health(),
                    "queue": self.check_job_queue_health(),
                }

                # System unhealthy if any component is unhealthy
                overall_status = "healthy"
                for comp in components.values():
                    if comp.get("status") == "unhealthy":
                        overall_status = "unhealthy"
                        break

                return {"status": overall_status, "components": components}

        health = FailingHealthCheck()

        # Test failure scenarios
        db_health = health.check_database_health()
        assert db_health["status"] == "unhealthy"
        assert "error" in db_health

        overall = health.get_overall_health()
        assert overall["status"] == "unhealthy"


class TestFileHandlerInterface:
    """Test FileHandlerInterface abstract methods and contract."""

    def test_interface_is_abstract(self):
        """Test that FileHandlerInterface cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            FileHandlerInterface()

    def test_complete_implementation_works(self):
        """Test complete FileHandler implementation."""

        class TestFileHandler(FileHandlerInterface):
            """Test implementation of FileHandlerInterface."""

            def check_disk_space(self) -> Dict[str, Any]:
                return {
                    "total_bytes": **********,
                    "available_bytes": 500000000,
                    "used_bytes": 500000000,
                    "usage_percent": 50.0,
                }

            def cleanup_old_files(self, max_age_hours: int = 24) -> int:
                # Mock cleanup - return number of files cleaned
                return 5

            def validate_file_path(self, file_path: str) -> bool:
                if not file_path:
                    return False
                if ".." in file_path:  # Path traversal check
                    return False
                if file_path.startswith("/"):  # Absolute path check
                    return False
                return True

        handler = TestFileHandler()

        # Test disk space check
        disk_info = handler.check_disk_space()
        assert "total_bytes" in disk_info
        assert "available_bytes" in disk_info
        assert disk_info["usage_percent"] == 50.0

        # Test file cleanup
        cleaned_count = handler.cleanup_old_files(24)
        assert cleaned_count == 5

        # Test file path validation
        assert handler.validate_file_path("uploads/video.mp4") is True
        assert handler.validate_file_path("../etc/passwd") is False
        assert handler.validate_file_path("/absolute/path") is False
        assert handler.validate_file_path("") is False

    def test_file_handler_edge_cases(self):
        """Test edge cases for file handler."""

        class EdgeFileHandler(FileHandlerInterface):
            """Test edge cases for file handler."""

            def check_disk_space(self) -> Dict[str, Any]:
                # Edge case: disk full
                return {
                    "total_bytes": **********,
                    "available_bytes": 0,
                    "used_bytes": **********,
                    "usage_percent": 100.0,
                }

            def cleanup_old_files(self, max_age_hours: int = 24) -> int:
                # Edge cases for cleanup
                if max_age_hours <= 0:
                    return 0  # Invalid age, no cleanup
                if max_age_hours > 8760:  # More than a year
                    return 0  # Too aggressive, no cleanup
                return max(1, max_age_hours // 24)  # Mock cleanup count

            def validate_file_path(self, file_path: str) -> bool:
                if not file_path:
                    return False

                # Check for various security issues
                dangerous_patterns = ["../", "..\\", "/etc/", "/root/", "~", "file://"]
                for pattern in dangerous_patterns:
                    if pattern in file_path.lower():
                        return False

                # Check file extension
                allowed_extensions = [".mp4", ".avi", ".mov", ".mkv"]
                if not any(
                    file_path.lower().endswith(ext) for ext in allowed_extensions
                ):
                    return False

                return True

        handler = EdgeFileHandler()

        # Test edge cases
        disk_info = handler.check_disk_space()
        assert disk_info["usage_percent"] == 100.0
        assert disk_info["available_bytes"] == 0

        # Test cleanup edge cases
        assert handler.cleanup_old_files(0) == 0
        assert handler.cleanup_old_files(-1) == 0
        assert handler.cleanup_old_files(10000) == 0  # Too aggressive
        assert handler.cleanup_old_files(48) == 2  # 48 hours = 2 days

        # Test file validation edge cases
        assert handler.validate_file_path("video.mp4") is True
        assert handler.validate_file_path("video.txt") is False  # Wrong extension
        assert handler.validate_file_path("../video.mp4") is False  # Path traversal
        assert handler.validate_file_path("/etc/passwd") is False  # System file
        assert handler.validate_file_path("file://video.mp4") is False  # File URL


class TestInterfaceInheritance:
    """Test inheritance and polymorphism aspects of interfaces."""

    def test_interfaces_are_abc_subclasses(self):
        """Test that all interfaces properly inherit from ABC."""
        interfaces = [
            JobRepositoryInterface,
            QueueManagerInterface,
            SessionManagerInterface,
            HealthCheckInterface,
            FileHandlerInterface,
        ]

        for interface in interfaces:
            assert issubclass(interface, ABC), (
                f"{interface.__name__} should inherit from ABC"
            )

    def test_interface_polymorphism(self):
        """Test polymorphic behavior of interface implementations."""

        class MockJobRepository(JobRepositoryInterface):
            """Mock implementation for polymorphism testing."""

            def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
                return None

            def get_job_stats(self) -> Dict[str, Any]:
                return {}

            def get_pending_jobs(self) -> List[VideoJob]:
                return []

            def get_running_jobs(self) -> List[VideoJob]:
                return []

            def get_jobs_by_status(
                self, status: str, limit: int = 100
            ) -> List[VideoJob]:
                return []

            def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                return []

            def get_jobs_by_session_and_status(
                self, session_id: str, status: str
            ) -> List[VideoJob]:
                return []

            def delete_job(self, job_id: str) -> bool:
                return True

            def create_job(self, job: VideoJob) -> Optional[VideoJob]:
                return job

            def update_job(self, job: VideoJob) -> Optional[VideoJob]:
                return job

            def get_active_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                return []

        def use_repository(repo: JobRepositoryInterface) -> Dict[str, Any]:
            """Function that accepts any JobRepositoryInterface implementation."""
            return repo.get_job_stats()

        # Test polymorphic usage
        mock_repo = MockJobRepository()
        result = use_repository(mock_repo)
        assert isinstance(result, dict)

        # Test isinstance check
        assert isinstance(mock_repo, JobRepositoryInterface)
        assert isinstance(mock_repo, ABC)

    def test_interface_method_documentation(self):
        """Test that interface methods have proper docstrings."""
        # Check that key methods have docstrings
        assert JobRepositoryInterface.get_job_by_id.__doc__ is not None
        assert "job_id" in JobRepositoryInterface.get_job_by_id.__doc__.lower()
        assert "returns" in JobRepositoryInterface.get_job_by_id.__doc__.lower()

        assert QueueManagerInterface.can_submit_job.__doc__ is not None
        assert "session_id" in QueueManagerInterface.can_submit_job.__doc__.lower()

        assert HealthCheckInterface.get_overall_health.__doc__ is not None
        assert "health" in HealthCheckInterface.get_overall_health.__doc__.lower()


@pytest.mark.integration
class TestInterfaceIntegration:
    """Integration tests for interface patterns."""

    def test_dependency_injection_pattern(self):
        """Test dependency injection using interfaces."""

        class MockJobRepository(JobRepositoryInterface):
            """Mock repository for dependency injection testing."""

            def __init__(self):
                self.jobs = {}
                self.call_count = 0

            def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
                self.call_count += 1
                return self.jobs.get(job_id)

            def create_job(self, job: VideoJob) -> Optional[VideoJob]:
                self.call_count += 1
                self.jobs[job.id] = job
                return job

            # Implement other abstract methods with minimal functionality
            def get_job_stats(self) -> Dict[str, Any]:
                return {"total": len(self.jobs)}

            def get_pending_jobs(self) -> List[VideoJob]:
                return []

            def get_running_jobs(self) -> List[VideoJob]:
                return []

            def get_jobs_by_status(
                self, status: str, limit: int = 100
            ) -> List[VideoJob]:
                return []

            def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                return []

            def get_jobs_by_session_and_status(
                self, session_id: str, status: str
            ) -> List[VideoJob]:
                return []

            def delete_job(self, job_id: str) -> bool:
                return True

            def update_job(self, job: VideoJob) -> Optional[VideoJob]:
                return job

            def get_active_jobs_by_session(self, session_id: str) -> List[VideoJob]:
                return []

        class JobService:
            """Service that depends on JobRepositoryInterface."""

            def __init__(self, repository: JobRepositoryInterface):
                self.repository = repository

            def process_job(self, job_id: str) -> Optional[VideoJob]:
                return self.repository.get_job_by_id(job_id)

        # Test dependency injection
        mock_repo = MockJobRepository()
        service = JobService(mock_repo)

        # Test that service uses injected repository
        result = service.process_job("test-job")
        assert result is None  # Job doesn't exist
        assert mock_repo.call_count == 1

        # Create a job and test again
        from datetime import datetime

        test_job = VideoJob(
            id="test-job",
            prompt="Test prompt",
            status="pending",
            created_at=datetime.now(),
        )
        mock_repo.create_job(test_job)

        result = service.process_job("test-job")
        assert result is not None
        assert result.id == "test-job"
        assert mock_repo.call_count == 3  # create_job + get_job_by_id

    def test_interface_composition(self):
        """Test composition of multiple interfaces."""

        class SystemManager:
            """System manager that composes multiple interfaces."""

            def __init__(
                self,
                job_repo: JobRepositoryInterface,
                queue_manager: QueueManagerInterface,
                health_check: HealthCheckInterface,
            ):
                self.job_repo = job_repo
                self.queue_manager = queue_manager
                self.health_check = health_check

            def get_system_status(self) -> Dict[str, Any]:
                """Get overall system status using all interfaces."""
                return {
                    "job_stats": self.job_repo.get_job_stats(),
                    "queue_stats": self.queue_manager.get_queue_statistics(),
                    "health": self.health_check.get_overall_health(),
                }

        # Create mock implementations
        mock_repo = MagicMock(spec=JobRepositoryInterface)
        mock_repo.get_job_stats.return_value = {"total": 10}

        mock_queue = MagicMock(spec=QueueManagerInterface)
        mock_queue.get_queue_statistics.return_value = {"pending": 5}

        mock_health = MagicMock(spec=HealthCheckInterface)
        mock_health.get_overall_health.return_value = {"status": "healthy"}

        # Test composition
        system = SystemManager(mock_repo, mock_queue, mock_health)
        status = system.get_system_status()

        assert status["job_stats"]["total"] == 10
        assert status["queue_stats"]["pending"] == 5
        assert status["health"]["status"] == "healthy"

        # Verify all interfaces were called
        mock_repo.get_job_stats.assert_called_once()
        mock_queue.get_queue_statistics.assert_called_once()
        mock_health.get_overall_health.assert_called_once()
