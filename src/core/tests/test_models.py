"""Tests for core data models."""

from datetime import datetime

import pytest
from pydantic import ValidationError

from src.core.models import (
    APIResponse,
    GenerationParamsFactory,
    VideoJob,
)


@pytest.mark.unit
class TestVideoJob:
    """Test VideoJob model validation and functionality."""

    def test_valid_video_job(self):
        """Test creation of valid VideoJob."""
        job = VideoJob(
            id="test-job-123",
            prompt="A cat playing piano",
            status="pending",
            created_at=datetime.now(),
        )
        assert job.id == "test-job-123"
        assert job.prompt == "A cat playing piano"
        assert job.status == "pending"

    def test_prompt_validation(self):
        """Test prompt validation logic."""
        # Test empty prompt
        with pytest.raises(ValidationError):
            VideoJob(
                id="test-job-123",
                prompt="",
                status="pending",
                created_at=datetime.now(),
            )

        # Test whitespace-only prompt
        with pytest.raises(ValidationError):
            VideoJob(
                id="test-job-123",
                prompt="   ",
                status="pending",
                created_at=datetime.now(),
            )

    def test_prompt_length_validation(self):
        """Test prompt length validation."""
        # Test prompt too long
        long_prompt = "x" * 501
        with pytest.raises(ValidationError):
            VideoJob(
                id="test-job-123",
                prompt=long_prompt,
                status="pending",
                created_at=datetime.now(),
            )

    def test_status_validation(self):
        """Test status field validation."""
        # Test invalid status
        with pytest.raises(ValidationError):
            VideoJob(
                id="test-job-123",
                prompt="A cat playing piano",
                status="invalid_status",
                created_at=datetime.now(),
            )


@pytest.mark.unit
class TestGenerationParams:
    """Test GenerationParams model validation and functionality."""

    def test_valid_generation_params(self):
        """Test creation of valid GenerationParams via factory."""
        params = GenerationParamsFactory.create_with_defaults(
            prompt="A cat playing piano",
            width=720,
            height=720,
            duration=5,
            model="sora",
        )
        assert params.prompt == "A cat playing piano"
        assert params.width == 720
        assert params.height == 720
        assert params.duration == 5

    def test_default_values(self):
        """Test factory provides configuration-driven defaults."""
        params = GenerationParamsFactory.create_with_defaults("Test prompt")
        assert params.width == 1280  # From configuration
        assert params.height == 720  # From configuration
        assert params.duration == 2  # From configuration (fixed!)
        assert params.model == "sora"

    def test_dimension_validation(self):
        """Test width and height validation via factory."""
        # Test width too small
        with pytest.raises(ValidationError):
            GenerationParamsFactory.create_with_defaults("Test prompt", width=400)

        # Test height too large
        with pytest.raises(ValidationError):
            GenerationParamsFactory.create_with_defaults("Test prompt", height=2000)

    def test_duration_validation(self):
        """Test duration validation via factory."""
        # Test duration too short
        with pytest.raises(ValidationError):
            GenerationParamsFactory.create_with_defaults("Test prompt", duration=0)

        # Test duration too long
        with pytest.raises(ValidationError):
            GenerationParamsFactory.create_with_defaults("Test prompt", duration=25)

    def test_to_api_dict(self):
        """Test conversion to API dictionary format."""
        params = GenerationParamsFactory.create_with_defaults(
            prompt="A cat playing piano",
            width=1280,
            height=720,
            duration=10,
            model="sora",
        )

        api_dict = params.to_api_dict()
        expected = {
            "prompt": "A cat playing piano",
            "width": 1280,
            "height": 720,
            "n_seconds": 10,
            "model": "sora",
        }
        assert api_dict == expected


@pytest.mark.unit
class TestGenerationParamsFactory:
    """Test GenerationParamsFactory functionality."""

    def test_create_with_defaults(self):
        """Test creating params with configuration defaults."""
        params = GenerationParamsFactory.create_with_defaults("test prompt")

        assert params.prompt == "test prompt"
        assert params.width == 1280  # From config
        assert params.height == 720  # From config
        assert params.duration == 2  # From config (FIXED!)
        assert params.model == "sora"

    def test_create_with_overrides(self):
        """Test creating params with specific overrides."""
        params = GenerationParamsFactory.create_with_defaults(
            prompt="test prompt", width=1920, height=1080, duration=10
        )

        assert params.width == 1920
        assert params.height == 1080
        assert params.duration == 10
        assert params.model == "sora"  # Default maintained

    def test_create_from_ui_request(self):
        """Test creating params from UI request data."""
        ui_params = {"width": 1920, "duration": 8}

        params = GenerationParamsFactory.create_from_ui_request(
            "test prompt", ui_params
        )

        assert params.width == 1920
        assert params.duration == 8
        # height should use default since not specified
        assert params.height == 720

    def test_create_for_testing(self):
        """Test creating params for testing scenarios."""
        params = GenerationParamsFactory.create_for_testing()

        assert params.prompt == "test prompt"
        assert params.width > 0
        assert params.height > 0

    def test_sd_resolution_preset_validation(self):
        """Test SD resolution (848x480) validation - regression test for HTML5 step fix."""
        # Test the specific SD resolution that was fixed (848x480)
        params = GenerationParamsFactory.create_with_defaults(
            "test prompt", width=848, height=480
        )

        assert params.width == 848
        assert params.height == 480
        assert params.prompt == "test prompt"

        # Test that 848 is divisible by 16 (HTML5 step validation requirement)
        assert 848 % 16 == 0, (
            "SD width must be divisible by 16 for HTML5 step validation"
        )

    def test_all_preset_resolutions_validation(self):
        """Test all preset resolutions from dropdown work correctly."""
        test_cases = [
            {"name": "SD", "width": 848, "height": 480},
            {"name": "HD", "width": 1280, "height": 720},
            {"name": "Full HD", "width": 1920, "height": 1080},
        ]

        for case in test_cases:
            params = GenerationParamsFactory.create_with_defaults(
                f"test prompt for {case['name']}",
                width=case["width"],
                height=case["height"],
            )

            assert params.width == case["width"]
            assert params.height == case["height"]

            # Ensure all widths are compatible with HTML5 step="16" validation
            assert case["width"] % 16 == 0, (
                f"{case['name']} width must be divisible by 16"
            )
        assert params.duration > 0


@pytest.mark.unit
class TestAPIResponse:
    """Test APIResponse model validation and functionality."""

    def test_success_response(self):
        """Test successful API response."""
        response = APIResponse(
            success=True, message="Operation successful", data={"result": "test"}
        )
        assert response.success is True
        assert response.message == "Operation successful"
        assert response.data == {"result": "test"}
        assert response.error is None

    def test_error_response(self):
        """Test error API response."""
        response = APIResponse(
            success=False, message="Operation failed", error="Invalid input"
        )
        assert response.success is False
        assert response.message == "Operation failed"
        assert response.error == "Invalid input"
        assert response.data is None
