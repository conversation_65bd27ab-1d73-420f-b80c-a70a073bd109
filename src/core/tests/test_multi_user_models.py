"""Unit tests for multi-user core data models.

Tests session management, queue-aware video jobs, and queue status
models that support the multi-user video generation system.
"""

from datetime import datetime, timezone

import pytest
from pydantic import ValidationError

from src.core.models import Queued<PERSON>ideoJob, Queue<PERSON>tatus, UserSession


@pytest.mark.unit
class TestUserSession:
    """Test UserSession data model.

    Tests session creation, validation, serialization,
    and default value handling for user sessions.
    """

    def test_user_session_creation_minimal(self):
        """Test UserSession creation with minimal required fields."""
        session = UserSession(session_id="test-session-123")

        assert session.session_id == "test-session-123"
        assert session.job_count == 0
        assert session.total_jobs_submitted == 0
        assert session.client_ip is None
        assert session.is_active is True
        assert isinstance(session.created_at, datetime)
        assert isinstance(session.last_active, datetime)

    def test_user_session_creation_full(self):
        """Test UserSession creation with all fields."""
        now = datetime.now(timezone.utc)

        session = UserSession(
            session_id="test-session-456",
            created_at=now,
            last_active=now,
            job_count=5,
            total_jobs_submitted=10,
            client_ip="*************",
            is_active=True,
        )

        assert session.session_id == "test-session-456"
        assert session.created_at == now
        assert session.last_active == now
        assert session.job_count == 5
        assert session.total_jobs_submitted == 10
        assert session.client_ip == "*************"
        assert session.is_active is True

    def test_user_session_validation_session_id_required(self):
        """Test that session_id is required."""
        with pytest.raises(ValidationError) as exc_info:
            UserSession()

        errors = exc_info.value.errors()
        session_id_error = next(
            (e for e in errors if e["loc"] == ("session_id",)), None
        )
        assert session_id_error is not None
        assert session_id_error["type"] == "missing"

    def test_user_session_defaults(self):
        """Test UserSession default values."""
        session = UserSession(session_id="test")

        # Verify defaults are applied
        assert session.job_count == 0
        assert session.total_jobs_submitted == 0
        assert session.is_active is True
        assert session.client_ip is None

        # Verify datetime defaults are recent
        time_diff = datetime.now(timezone.utc) - session.created_at
        assert time_diff.total_seconds() < 1  # Created within last second

        time_diff = datetime.now(timezone.utc) - session.last_active
        assert time_diff.total_seconds() < 1  # Active within last second

    def test_user_session_serialization(self):
        """Test UserSession serialization to dict."""
        session = UserSession(
            session_id="test-session", job_count=3, client_ip="*************"
        )

        session_dict = session.model_dump()

        assert session_dict["session_id"] == "test-session"
        assert session_dict["job_count"] == 3
        assert session_dict["client_ip"] == "*************"
        assert "created_at" in session_dict
        assert "last_active" in session_dict


@pytest.mark.unit
class TestQueuedVideoJob:
    """Test cases for QueuedVideoJob model."""

    @pytest.fixture
    def base_job_data(self):
        """Base data for creating VideoJob instances."""
        return {
            "id": "job-123",
            "prompt": "test video prompt",
            "status": "pending",
            "created_at": datetime.now(timezone.utc),
        }

    def test_queued_video_job_creation_minimal(self, base_job_data):
        """Test QueuedVideoJob creation with minimal required fields."""
        queued_job = QueuedVideoJob(session_id="session-456", **base_job_data)

        assert queued_job.session_id == "session-456"
        assert queued_job.id == "job-123"
        assert queued_job.prompt == "test video prompt"
        assert queued_job.status == "pending"

        # Check default values
        assert queued_job.queue_position is None
        assert queued_job.estimated_completion is None
        assert queued_job.priority == 0
        assert queued_job.retry_count == 0
        assert queued_job.worker_id is None

    def test_queued_video_job_creation_full(self, base_job_data):
        """Test QueuedVideoJob creation with all fields."""
        completion_time = datetime.now(timezone.utc)

        queued_job = QueuedVideoJob(
            session_id="session-789",
            queue_position=3,
            estimated_completion=completion_time,
            priority=5,
            retry_count=2,
            worker_id="worker-123",
            **base_job_data,
        )

        assert queued_job.session_id == "session-789"
        assert queued_job.queue_position == 3
        assert queued_job.estimated_completion == completion_time
        assert queued_job.priority == 5
        assert queued_job.retry_count == 2
        assert queued_job.worker_id == "worker-123"

    def test_queued_video_job_inherits_video_job(self, base_job_data):
        """Test that QueuedVideoJob inherits VideoJob functionality."""
        queued_job = QueuedVideoJob(session_id="session-test", **base_job_data)

        # Should have all VideoJob fields
        assert hasattr(queued_job, "id")
        assert hasattr(queued_job, "prompt")
        assert hasattr(queued_job, "status")
        assert hasattr(queued_job, "created_at")
        assert hasattr(queued_job, "completed_at")
        assert hasattr(queued_job, "generation_id")
        assert hasattr(queued_job, "error_message")
        assert hasattr(queued_job, "file_path")
        assert hasattr(queued_job, "download_url")

    def test_queued_video_job_priority_validation_valid(self, base_job_data):
        """Test priority validation with valid values."""
        # Test valid priority values
        for priority in [0, 1, 5, 9, 10]:
            queued_job = QueuedVideoJob(
                session_id="session-test", priority=priority, **base_job_data
            )
            assert queued_job.priority == priority

    def test_queued_video_job_priority_validation_invalid(self, base_job_data):
        """Test priority validation with invalid values."""
        # Test invalid priority values
        invalid_priorities = [-1, 11, 15, 100]

        for priority in invalid_priorities:
            with pytest.raises(ValidationError) as exc_info:
                QueuedVideoJob(
                    session_id="session-test", priority=priority, **base_job_data
                )

            errors = exc_info.value.errors()
            priority_error = next(
                (e for e in errors if e["loc"] == ("priority",)), None
            )
            assert priority_error is not None
            assert "Priority must be between 0 and 10" in priority_error["msg"]

    def test_queued_video_job_session_id_required(self, base_job_data):
        """Test that session_id is required."""
        with pytest.raises(ValidationError) as exc_info:
            QueuedVideoJob(**base_job_data)

        errors = exc_info.value.errors()
        session_id_error = next(
            (e for e in errors if e["loc"] == ("session_id",)), None
        )
        assert session_id_error is not None
        assert session_id_error["type"] == "missing"

    def test_queued_video_job_serialization(self, base_job_data):
        """Test QueuedVideoJob serialization."""
        queued_job = QueuedVideoJob(
            session_id="session-serialize",
            queue_position=2,
            priority=7,
            retry_count=1,
            **base_job_data,
        )

        job_dict = queued_job.model_dump()

        # Check inherited fields
        assert job_dict["id"] == "job-123"
        assert job_dict["prompt"] == "test video prompt"
        assert job_dict["status"] == "pending"

        # Check queue-specific fields
        assert job_dict["session_id"] == "session-serialize"
        assert job_dict["queue_position"] == 2
        assert job_dict["priority"] == 7
        assert job_dict["retry_count"] == 1
        assert job_dict["estimated_completion"] is None
        assert job_dict["worker_id"] is None


@pytest.mark.unit
class TestQueueStatus:
    """Test cases for QueueStatus model."""

    def test_queue_status_creation_minimal(self):
        """Test QueueStatus creation with minimal required fields."""
        status = QueueStatus(
            total_jobs=10, position=3, estimated_wait_minutes=15, active_workers=2
        )

        assert status.total_jobs == 10
        assert status.position == 3
        assert status.estimated_wait_minutes == 15
        assert status.active_workers == 2

        # Check default values
        assert status.session_jobs == 0
        assert status.can_submit_more is True
        assert status.max_jobs_per_session == 3

    def test_queue_status_creation_full(self):
        """Test QueueStatus creation with all fields."""
        status = QueueStatus(
            total_jobs=25,
            position=8,
            estimated_wait_minutes=30,
            active_workers=5,
            session_jobs=2,
            can_submit_more=False,
            max_jobs_per_session=5,
        )

        assert status.total_jobs == 25
        assert status.position == 8
        assert status.estimated_wait_minutes == 30
        assert status.active_workers == 5
        assert status.session_jobs == 2
        assert status.can_submit_more is False
        assert status.max_jobs_per_session == 5

    def test_queue_status_position_validation_valid(self):
        """Test position validation with valid values."""
        # Test valid positions
        for position in [1, 2, 10, 100]:
            status = QueueStatus(
                total_jobs=10,
                position=position,
                estimated_wait_minutes=5,
                active_workers=2,
            )
            assert status.position == position

    def test_queue_status_position_validation_invalid(self):
        """Test position validation with invalid values."""
        # Test invalid positions
        invalid_positions = [0, -1, -10]

        for position in invalid_positions:
            with pytest.raises(ValidationError) as exc_info:
                QueueStatus(
                    total_jobs=10,
                    position=position,
                    estimated_wait_minutes=5,
                    active_workers=2,
                )

            errors = exc_info.value.errors()
            position_error = next(
                (e for e in errors if e["loc"] == ("position",)), None
            )
            assert position_error is not None
            assert "Queue position must be 1 or greater" in position_error["msg"]

    def test_queue_status_required_fields(self):
        """Test that all required fields are enforced."""
        # Missing all required fields
        with pytest.raises(ValidationError) as exc_info:
            QueueStatus()

        errors = exc_info.value.errors()
        required_fields = {
            "total_jobs",
            "position",
            "estimated_wait_minutes",
            "active_workers",
        }
        error_fields = {
            error["loc"][0] for error in errors if error["type"] == "missing"
        }

        assert required_fields.issubset(error_fields)

    def test_queue_status_serialization(self):
        """Test QueueStatus serialization."""
        status = QueueStatus(
            total_jobs=15,
            position=4,
            estimated_wait_minutes=20,
            active_workers=3,
            session_jobs=1,
            can_submit_more=True,
            max_jobs_per_session=4,
        )

        status_dict = status.model_dump()

        assert status_dict["total_jobs"] == 15
        assert status_dict["position"] == 4
        assert status_dict["estimated_wait_minutes"] == 20
        assert status_dict["active_workers"] == 3
        assert status_dict["session_jobs"] == 1
        assert status_dict["can_submit_more"] is True
        assert status_dict["max_jobs_per_session"] == 4


@pytest.mark.unit
class TestMultiUserModelsIntegration:
    """Integration tests for multi-user models working together."""

    def test_session_and_queued_job_relationship(self):
        """Test relationship between UserSession and QueuedVideoJob."""
        # Create a session
        session = UserSession(
            session_id="session-integration-test", job_count=2, total_jobs_submitted=5
        )

        # Create jobs for this session
        job1 = QueuedVideoJob(
            id="job-1",
            prompt="First video",
            status="pending",
            created_at=datetime.now(timezone.utc),
            session_id=session.session_id,
            queue_position=1,
            priority=5,
        )

        job2 = QueuedVideoJob(
            id="job-2",
            prompt="Second video",
            status="running",
            created_at=datetime.now(timezone.utc),
            session_id=session.session_id,
            queue_position=3,
            priority=0,
        )

        # Verify relationship
        assert job1.session_id == session.session_id
        assert job2.session_id == session.session_id

        # Verify jobs have different priorities and positions
        assert job1.priority > job2.priority  # job1 has higher priority
        assert job1.queue_position < job2.queue_position  # job1 is ahead in queue

    def test_queue_status_reflects_session_state(self):
        """Test that QueueStatus can represent session state."""
        # Create queue status representing current state
        status = QueueStatus(
            total_jobs=20,
            position=5,
            estimated_wait_minutes=25,
            active_workers=4,
            session_jobs=2,  # User has 2 jobs
            can_submit_more=True,  # Under limit
            max_jobs_per_session=3,  # Limit is 3
        )

        # Verify queue state logic
        assert status.session_jobs < status.max_jobs_per_session
        assert status.can_submit_more is True

        # Calculate expected wait based on position and workers
        # If position is 5 and there are 4 workers, user is effectively 2nd in line
        effective_position = max(1, status.position - status.active_workers + 1)
        assert effective_position <= status.position

    def test_complete_multi_user_workflow_models(self):
        """Test complete workflow using all multi-user models."""
        # 1. User creates session
        session = UserSession(session_id="workflow-session", client_ip="*************")

        # 2. User submits first job
        job1 = QueuedVideoJob(
            id="workflow-job-1",
            prompt="Workflow test video",
            status="pending",
            created_at=datetime.now(timezone.utc),
            session_id=session.session_id,
            queue_position=1,
            priority=0,
        )

        # 3. Check queue status
        initial_status = QueueStatus(
            total_jobs=1,
            position=1,
            estimated_wait_minutes=5,
            active_workers=1,
            session_jobs=1,
            can_submit_more=True,
            max_jobs_per_session=3,
        )

        # 4. Job starts processing
        job1.status = "running"
        job1.worker_id = "worker-alpha"

        # 5. User submits second job while first is running
        job2 = QueuedVideoJob(
            id="workflow-job-2",
            prompt="Second workflow video",
            status="pending",
            created_at=datetime.now(timezone.utc),
            session_id=session.session_id,
            queue_position=1,  # First in queue since job1 is running
            priority=0,
        )

        # 6. Update session activity
        session.job_count = 2  # One running, one pending
        session.total_jobs_submitted = 2
        session.last_active = datetime.now(timezone.utc)

        # 7. Updated queue status
        updated_status = QueueStatus(
            total_jobs=1,  # Only pending jobs counted
            position=1,
            estimated_wait_minutes=5,
            active_workers=1,
            session_jobs=2,  # Total jobs for session
            can_submit_more=True,  # Still under limit of 3
            max_jobs_per_session=3,
        )

        # Verify workflow consistency
        assert job1.session_id == job2.session_id == session.session_id
        assert job1.status == "running"
        assert job2.status == "pending"
        assert session.job_count == 2
        assert updated_status.session_jobs == 2
        assert updated_status.can_submit_more is True  # Can still submit one more

    def test_model_field_consistency(self):
        """Test that related fields across models are consistent."""
        session_id = "consistency-test-session"

        # Create models with same session ID
        session = UserSession(session_id=session_id)

        job = QueuedVideoJob(
            id="consistency-job",
            prompt="Consistency test",
            status="pending",
            created_at=datetime.now(timezone.utc),
            session_id=session_id,
        )

        status = QueueStatus(
            total_jobs=1,
            position=1,
            estimated_wait_minutes=1,
            active_workers=1,
            session_jobs=1,
        )

        # Verify consistency
        assert session.session_id == job.session_id
        assert status.session_jobs >= 1  # At least the one job we created

        # Test serialization consistency
        session_dict = session.model_dump()
        job_dict = job.model_dump()
        status_dict = status.model_dump()

        assert session_dict["session_id"] == job_dict["session_id"]
        assert isinstance(status_dict["session_jobs"], int)
        assert isinstance(status_dict["can_submit_more"], bool)
