"""Core data models for Sora video generation POC."""

from datetime import datetime, timezone
from typing import TYPE_CHECKING, Literal, Optional

from pydantic import BaseModel, Field, field_validator

# Import at module level to avoid circular imports in validators
from src.config.factory import ConfigurationFactory

if TYPE_CHECKING:
    from src.config.video_config import VideoGenerationConfig

# Module-level cache to avoid repeated config calls in validators
_cached_video_config = None


def get_cached_video_config() -> "VideoGenerationConfig":
    """
    Get cached video configuration to avoid repeated factory calls in validators.

    Returns:
        VideoGenerationConfig: Cached video configuration instance
    """
    global _cached_video_config
    if _cached_video_config is None:
        _cached_video_config = ConfigurationFactory.get_video_config()
    return _cached_video_config


class VideoJob(BaseModel):
    """
    Represents a video generation job with validation.

    Provides type safety and automatic validation for video generation
    workflow state management.
    """

    id: str = Field(..., description="Unique job identifier")
    prompt: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="Text prompt for video generation",
    )
    status: Literal["pending", "running", "preprocessing", "succeeded", "failed"] = (
        Field(..., description="Current job status")
    )
    created_at: datetime = Field(..., description="Job creation timestamp")
    completed_at: Optional[datetime] = Field(
        None, description="Job completion timestamp"
    )
    generation_id: Optional[str] = Field(None, description="Azure API generation ID")
    error_message: Optional[str] = Field(
        None, description="Error message if job failed"
    )
    file_path: Optional[str] = Field(
        None, description="Local file path for generated video"
    )
    download_url: Optional[str] = Field(None, description="Download URL for video file")

    # Multi-user support fields
    session_id: Optional[str] = Field(None, description="User session identifier")
    priority: int = Field(default=0, description="Job priority (higher = more urgent)")
    queue_position: Optional[int] = Field(None, description="Position in queue")
    retry_count: int = Field(default=0, description="Number of retry attempts")

    # Dual-provider support fields
    api_provider: Literal["azure_sora", "google_veo3"] = Field(
        default="azure_sora", description="API provider for video generation"
    )
    input_image_path: Optional[str] = Field(None, description="Veo3 image input path")
    audio_generated: bool = Field(default=False, description="Audio generation flag")

    # C3 Provider-aware extensions
    provider_job_id: Optional[str] = Field(
        None, description="Provider-specific job identifier"
    )
    provider_metadata: Optional[dict] = Field(
        None, description="Provider-specific metadata"
    )
    provider_queue_position: Optional[int] = Field(
        None, description="Position in provider-specific queue"
    )
    provider_estimated_completion: Optional[datetime] = Field(
        None, description="Provider-specific ETA"
    )
    provider_health_status: str = Field(
        default="healthy", description="Provider health when job was submitted"
    )

    @field_validator("prompt")
    @classmethod
    def validate_prompt(cls, v: str) -> str:
        """
        Validate and sanitize prompt input.

        Args:
            v (str): The prompt string to validate

        Returns:
            str: Cleaned and validated prompt

        Raises:
            ValueError: If prompt is empty or invalid
        """
        if not v or not v.strip():
            raise ValueError("Prompt cannot be empty")
        return v.strip()

    @field_validator("api_provider")
    @classmethod
    def validate_api_provider(cls, v: str) -> str:
        """
        Validate API provider value.

        Args:
            v (str): The API provider to validate

        Returns:
            str: Validated API provider

        Raises:
            ValueError: If provider is not supported
        """
        if v not in ["azure_sora", "google_veo3"]:
            raise ValueError("API provider must be 'azure_sora' or 'google_veo3'")
        return v


class GenerationParams(BaseModel):
    """
    Parameters for video generation with Azure Sora API.

    NOTE: Defaults are now provided via factory pattern from configuration.
    Do not add default values to Field() definitions.

    Validates input parameters and provides API-compatible serialization.
    """

    prompt: str = Field(..., min_length=1, description="Text prompt")
    width: int = Field(..., description="Video width in pixels")
    height: int = Field(..., description="Video height in pixels")
    duration: int = Field(..., description="Video duration in seconds")
    model: str = Field(..., description="AI model name")

    @field_validator("width")
    @classmethod
    def validate_width(cls, v: int) -> int:
        """
        Validate width against configuration constraints.

        Args:
            v (int): Width value to validate

        Returns:
            int: Validated width

        Raises:
            ValueError: If width is outside allowed range
        """
        video_config = get_cached_video_config()

        if not (video_config.min_width <= v <= video_config.max_width):
            raise ValueError(
                f"Width must be between {video_config.min_width} and {video_config.max_width}"
            )
        return v

    @field_validator("height")
    @classmethod
    def validate_height(cls, v: int) -> int:
        """
        Validate height against configuration constraints.

        Args:
            v (int): Height value to validate

        Returns:
            int: Validated height

        Raises:
            ValueError: If height is outside allowed range
        """
        video_config = get_cached_video_config()

        if not (video_config.min_height <= v <= video_config.max_height):
            raise ValueError(
                f"Height must be between {video_config.min_height} and {video_config.max_height}"
            )
        return v

    @field_validator("duration")
    @classmethod
    def validate_duration(cls, v: int) -> int:
        """
        Validate duration against configuration constraints.

        Args:
            v (int): Duration value to validate

        Returns:
            int: Validated duration

        Raises:
            ValueError: If duration is outside allowed range
        """
        video_config = get_cached_video_config()

        if not (video_config.min_duration <= v <= video_config.max_duration):
            raise ValueError(
                f"Duration must be between {video_config.min_duration} and {video_config.max_duration}"
            )
        return v

    @field_validator("prompt")
    @classmethod
    def validate_prompt(cls, v: str) -> str:
        """
        Validate and sanitize prompt input.

        Args:
            v (str): The prompt string to validate

        Returns:
            str: Cleaned and validated prompt

        Raises:
            ValueError: If prompt is empty or invalid
        """
        video_config = get_cached_video_config()

        if not v or not v.strip():
            raise ValueError("Prompt cannot be empty")

        if len(v) > video_config.max_prompt_length:
            raise ValueError(
                f"Prompt must be {video_config.max_prompt_length} characters or less"
            )

        return v.strip()

    def to_api_dict(self) -> dict:
        """
        Convert to Azure API format.

        Returns:
            dict: API-compatible parameters dictionary
        """
        return {
            "prompt": self.prompt,
            "width": self.width,
            "height": self.height,
            "n_seconds": self.duration,
            "model": self.model,
        }


class GenerationParamsFactory:
    """
    Factory for creating GenerationParams with configuration-driven defaults.

    Supports UI parameter overrides while maintaining environment-based defaults.
    This factory enables the single source of truth pattern for video generation
    parameters and prepares the system for UI customization.
    """

    @staticmethod
    def create_with_defaults(
        prompt: str,
        width: Optional[int] = None,
        height: Optional[int] = None,
        duration: Optional[int] = None,
        model: Optional[str] = None,
        config_name: Optional[str] = None,
    ) -> GenerationParams:
        """
        Create GenerationParams with configuration defaults and optional overrides.

        Args:
            prompt (str): Text prompt for video generation
            width (Optional[int]): Video width override (uses config default if None)
            height (Optional[int]): Video height override (uses config default if None)
            duration (Optional[int]): Video duration override (uses config default if None)
            model (Optional[str]): AI model override (uses config default if None)
            config_name (Optional[str]): Configuration environment override (for testing)

        Returns:
            GenerationParams: Validated parameters with environment-aware defaults
        """
        # Get environment-based defaults
        defaults = ConfigurationFactory.create_generation_params_defaults(config_name)

        # Apply overrides or use defaults
        params = GenerationParams(
            prompt=prompt,
            width=width if width is not None else defaults["width"],
            height=height if height is not None else defaults["height"],
            duration=duration if duration is not None else defaults["duration"],
            model=model if model is not None else defaults["model"],
        )

        return params

    @staticmethod
    def create_from_ui_request(
        prompt: str, ui_parameters: Optional[dict] = None
    ) -> GenerationParams:
        """
        Create GenerationParams from UI request data.

        Designed for Phase 2 UI enhancement where users can customize parameters.

        Args:
            prompt (str): Text prompt for video generation
            ui_parameters (Optional[dict]): Optional UI parameter overrides
                                          Format: {'width': int, 'height': int, 'duration': int, 'model': str}

        Returns:
            GenerationParams: Validated parameters with UI overrides

        Example:
            >>> # Standard request (uses config defaults)
            >>> params = GenerationParamsFactory.create_from_ui_request("A sunset over mountains")

            >>> # Custom request with UI overrides
            >>> ui_params = {'width': 1920, 'height': 1080, 'duration': 10}
            >>> params = GenerationParamsFactory.create_from_ui_request("Ocean waves", ui_params)
            >>> print(params.width)  # 1920
        """
        ui_params = ui_parameters or {}

        return GenerationParamsFactory.create_with_defaults(
            prompt=prompt,
            width=ui_params.get("width"),
            height=ui_params.get("height"),
            duration=ui_params.get("duration"),
            model=ui_params.get("model"),
        )

    @staticmethod
    def create_for_testing(
        prompt: str = "test prompt", **overrides
    ) -> GenerationParams:
        """
        Create GenerationParams for testing with stable defaults.

        Args:
            prompt (str): Test prompt (default: "test prompt")
            **overrides: Parameter overrides for specific test scenarios

        Returns:
            GenerationParams: Parameters suitable for testing
        """
        # Use testing configuration
        defaults = ConfigurationFactory.create_generation_params_defaults("testing")

        # Apply test overrides
        test_params = {**defaults, **overrides}

        return GenerationParams(prompt=prompt, **test_params)


class UserSession(BaseModel):
    """
    User session model for isolation without authentication.

    Provides session tracking and user data isolation in a multi-user
    environment without requiring user authentication.
    """

    session_id: str = Field(..., description="Unique session identifier")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_active: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    job_count: int = Field(default=0, description="Number of active jobs")
    total_jobs_submitted: int = Field(
        default=0, description="Total jobs ever submitted"
    )
    client_ip: Optional[str] = Field(None, description="Client IP address")
    is_active: bool = Field(default=True, description="Whether session is active")


class QueuedVideoJob(VideoJob):
    """
    Extended VideoJob for queue management and multi-user support.

    Adds session tracking, queue position, priority, and retry logic
    to the base VideoJob model for multi-user queue processing.
    """

    session_id: str = Field(..., description="User session identifier")
    queue_position: Optional[int] = Field(
        None, description="Position in queue (1-indexed)"
    )
    estimated_completion: Optional[datetime] = Field(
        None, description="Estimated completion time"
    )
    priority: int = Field(default=0, description="Job priority (higher = more urgent)")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    worker_id: Optional[str] = Field(None, description="Processing worker identifier")

    @field_validator("priority")
    @classmethod
    def validate_priority(cls, v: int) -> int:
        """
        Validate priority is within acceptable range.

        Args:
            v (int): Priority value to validate

        Returns:
            int: Validated priority

        Raises:
            ValueError: If priority is outside valid range
        """
        if not (0 <= v <= 10):
            raise ValueError("Priority must be between 0 and 10")
        return v


class QueueStatus(BaseModel):
    """
    Queue status information for user feedback.

    Provides comprehensive queue state information for real-time
    user interface updates and wait time estimation.
    """

    total_jobs: int = Field(..., description="Total jobs in queue")
    position: int = Field(..., description="User's position in queue")
    estimated_wait_minutes: int = Field(
        ..., description="Estimated wait time in minutes"
    )
    active_workers: int = Field(..., description="Number of active workers")
    session_jobs: int = Field(default=0, description="Number of jobs for this session")
    can_submit_more: bool = Field(
        default=True, description="Whether user can submit more jobs"
    )
    max_jobs_per_session: int = Field(
        default=3, description="Maximum jobs allowed per session"
    )

    @field_validator("position")
    @classmethod
    def validate_position(cls, v: int) -> int:
        """
        Validate queue position is positive.

        Args:
            v (int): Position value to validate

        Returns:
            int: Validated position

        Raises:
            ValueError: If position is not positive
        """
        if v < 1:
            raise ValueError("Queue position must be 1 or greater")
        return v


class APIResponse(BaseModel):
    """
    Standard API response format.

    Provides consistent structure for all API responses.
    """

    success: bool = Field(..., description="Whether request was successful")
    message: str = Field(..., description="Response message")
    data: Optional[dict] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error details if failed")
