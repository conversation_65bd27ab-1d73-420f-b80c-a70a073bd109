"""
Core interfaces for dependency injection and vertical slice isolation.

This module defines abstract interfaces that enable proper dependency injection
and maintain vertical slice architecture boundaries by preventing direct
cross-feature imports.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from src.core.models import VideoJob


class JobRepositoryInterface(ABC):
    """
    Abstract interface for job repository operations.

    This interface defines the contract for job data access operations,
    allowing different layers to depend on abstractions rather than
    concrete implementations.
    """

    @abstractmethod
    def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
        """
        Retrieve a job by its ID.

        Args:
            job_id (str): Unique job identifier

        Returns:
            Optional[VideoJob]: Job if found, None otherwise
        """
        pass

    @abstractmethod
    def get_job_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive job statistics.

        Returns:
            Dict[str, Any]: Job statistics including counts by status
        """
        pass

    @abstractmethod
    def get_pending_jobs(self) -> List[VideoJob]:
        """
        Get all jobs in pending status.

        Returns:
            List[VideoJob]: List of pending jobs
        """
        pass

    @abstractmethod
    def get_running_jobs(self) -> List[VideoJob]:
        """
        Get all jobs in running status.

        Returns:
            List[VideoJob]: List of running jobs
        """
        pass

    @abstractmethod
    def get_jobs_by_status(self, status: str, limit: int = 100) -> List[VideoJob]:
        """
        Get jobs filtered by status.

        Args:
            status (str): Job status to filter by
            limit (int): Maximum number of jobs to return

        Returns:
            List[VideoJob]: List of jobs with specified status
        """
        pass

    @abstractmethod
    def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
        """
        Get all jobs for a specific session.

        Args:
            session_id (str): Session identifier

        Returns:
            List[VideoJob]: List of jobs for the session
        """
        pass

    @abstractmethod
    def get_jobs_by_session_and_status(
        self, session_id: str, status: str
    ) -> List[VideoJob]:
        """
        Get jobs for a session filtered by status.

        Args:
            session_id (str): Session identifier
            status (str): Job status to filter by

        Returns:
            List[VideoJob]: List of jobs matching criteria
        """
        pass

    @abstractmethod
    def delete_job(self, job_id: str) -> bool:
        """
        Delete a job by its ID.

        Args:
            job_id (str): Job identifier

        Returns:
            bool: True if successfully deleted, False otherwise
        """
        pass

    @abstractmethod
    def create_job(self, job: VideoJob) -> Optional[VideoJob]:
        """
        Create a new job.

        Args:
            job (VideoJob): Job to create

        Returns:
            Optional[VideoJob]: Created job or None if failed
        """
        pass

    @abstractmethod
    def update_job(self, job: VideoJob) -> Optional[VideoJob]:
        """
        Update an existing job.

        Args:
            job (VideoJob): Job with updated data

        Returns:
            Optional[VideoJob]: Updated job or None if failed
        """
        pass

    @abstractmethod
    def get_active_jobs_by_session(self, session_id: str) -> List[VideoJob]:
        """
        Get active jobs (pending or running) for a specific session.

        Args:
            session_id (str): Session identifier

        Returns:
            List[VideoJob]: List of active jobs for the session
        """
        pass


class QueueManagerInterface(ABC):
    """
    Abstract interface for queue management operations.

    This interface defines the contract for queue operations,
    enabling proper separation between API layer and queue implementation.
    """

    @abstractmethod
    def assign_queue_position(self, job_id: str, session_id: str) -> int:
        """
        Assign a queue position to a job.

        Args:
            job_id (str): Job identifier
            session_id (str): Session identifier

        Returns:
            int: Queue position assigned
        """
        pass

    @abstractmethod
    def get_queue_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get queue status for a session.

        Args:
            session_id (str): Session identifier

        Returns:
            Dict[str, Any]: Queue status information
        """
        pass

    @abstractmethod
    def get_session_queue_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get detailed queue status for a session.

        Args:
            session_id (str): Session identifier

        Returns:
            Dict[str, Any]: Detailed queue status
        """
        pass

    @abstractmethod
    def get_queue_statistics(self) -> Dict[str, Any]:
        """
        Get overall queue statistics.

        Returns:
            Dict[str, Any]: Queue statistics and metrics
        """
        pass

    @abstractmethod
    def can_submit_job(self, session_id: str) -> tuple[bool, str]:
        """
        Check if a session can submit more jobs.

        Args:
            session_id (str): Session identifier

        Returns:
            tuple[bool, str]: (can_submit, reason_if_not)
        """
        pass

    @abstractmethod
    def get_estimated_wait_time(self, session_id: str) -> int:
        """
        Get estimated wait time for a session.

        Args:
            session_id (str): Session identifier

        Returns:
            int: Estimated wait time in minutes
        """
        pass


class SessionManagerInterface(ABC):
    """
    Abstract interface for session management operations.

    This interface defines the contract for session operations,
    enabling proper separation between different layers and session management.
    """

    @abstractmethod
    def validate_session(self, session_id: str) -> tuple[bool, str]:
        """
        Validate a session ID.

        Args:
            session_id (str): Session identifier to validate

        Returns:
            tuple[bool, str]: (is_valid, reason/message)
        """
        pass

    @abstractmethod
    def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session data.

        Args:
            session_id (str): Session identifier

        Returns:
            Optional[Dict[str, Any]]: Session data or None if not found
        """
        pass

    @abstractmethod
    def create_session(self, client_ip: str) -> tuple[str, Dict[str, Any]]:
        """
        Create a new session.

        Args:
            client_ip (str): Client IP address

        Returns:
            tuple[str, Dict[str, Any]]: (session_id, session_data)
        """
        pass

    @abstractmethod
    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions.

        Returns:
            int: Number of sessions cleaned up
        """
        pass


class HealthCheckInterface(ABC):
    """
    Abstract interface for health check operations.

    This interface defines the contract for health monitoring,
    enabling proper separation between health checks and other components.
    """

    @abstractmethod
    def check_database_health(self) -> Dict[str, Any]:
        """
        Check database health.

        Returns:
            Dict[str, Any]: Database health status
        """
        pass

    @abstractmethod
    def check_azure_health(self) -> Dict[str, Any]:
        """
        Check Azure API health.

        Returns:
            Dict[str, Any]: Azure API health status
        """
        pass

    @abstractmethod
    def check_job_queue_health(self) -> Dict[str, Any]:
        """
        Check job queue health.

        Returns:
            Dict[str, Any]: Job queue health status
        """
        pass

    @abstractmethod
    def get_overall_health(self) -> Dict[str, Any]:
        """
        Get overall system health.

        Returns:
            Dict[str, Any]: Overall health status
        """
        pass


class VideoProviderInterface(ABC):
    """
    Abstract interface for video generation providers.

    This interface defines the contract for video generation services,
    enabling support for multiple providers (Azure Sora, Google Veo3)
    with consistent interface regardless of underlying implementation.
    """

    @abstractmethod
    async def generate_video(
        self,
        prompt: str,
        width: int = 1280,
        height: int = 720,
        duration: int = 5,
        image_path: Optional[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Generate a video based on the provided parameters.

        Args:
            prompt (str): Text description for video generation
            width (int): Video width in pixels
            height (int): Video height in pixels
            duration (int): Video duration in seconds
            image_path (Optional[str]): Path to input image for image-to-video generation
            **kwargs: Additional provider-specific parameters

        Returns:
            Dict[str, Any]: Generation response with generation_id and status
        """
        pass

    @abstractmethod
    async def get_generation_status(self, generation_id: str) -> Dict[str, Any]:
        """
        Get the status of a video generation operation.

        Args:
            generation_id (str): Unique generation identifier

        Returns:
            Dict[str, Any]: Generation status with progress and completion info
        """
        pass

    @abstractmethod
    async def cancel_generation(self, generation_id: str) -> Dict[str, Any]:
        """
        Cancel an ongoing video generation operation.

        Args:
            generation_id (str): Unique generation identifier

        Returns:
            Dict[str, Any]: Cancellation response
        """
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """
        Get the name of the video provider.

        Returns:
            str: Provider name (e.g., "azure_sora", "google_veo3", "mock_veo3")
        """
        pass

    @abstractmethod
    def get_provider_capabilities(self) -> Dict[str, Any]:
        """
        Get provider-specific capabilities and features.

        Returns:
            Dict[str, Any]: Provider capabilities including supported features
        """
        pass


class FileHandlerInterface(ABC):
    """
    Abstract interface for file handling operations.

    This interface defines the contract for file operations,
    enabling proper separation between file handling and other components.
    """

    @abstractmethod
    def check_disk_space(self) -> Dict[str, Any]:
        """
        Check disk space availability.

        Returns:
            Dict[str, Any]: Disk space information
        """
        pass

    @abstractmethod
    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old files.

        Args:
            max_age_hours (int): Maximum age in hours for files to keep

        Returns:
            int: Number of files cleaned up
        """
        pass

    @abstractmethod
    def validate_file_path(self, file_path: str) -> bool:
        """
        Validate a file path for security.

        Args:
            file_path (str): File path to validate

        Returns:
            bool: True if path is safe
        """
        pass
