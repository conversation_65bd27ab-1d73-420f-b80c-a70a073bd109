# Core Domain Models Module

Clean architecture domain layer with Pydantic v2 models, abstract interfaces, and type-safe validation patterns for the multi-user video generation system.

## Architecture Overview

**Domain-Driven Design**: Pure domain models with no external dependencies, providing type-safe data validation and business logic encapsulation.

**Dependency Injection**: Abstract interfaces enable loose coupling between layers and support vertical slice architecture.

**Configuration-Driven Validation**: Pydantic v2 models with cached configuration for performance and environment-aware defaults.

## System Architecture

Vertical slice architecture with tests co-located with code. Production-ready multi-user implementation with distributed queue management, real-time WebSocket updates, session isolation, and comprehensive monitoring for 15+ concurrent users.

```
src/
    core/                       # Core domain models → See src/core/CLAUDE.md
    database/                   # Database persistence → See src/database/CLAUDE.md
    config/                     # Configuration management → See src/config/CLAUDE.md
    monitoring/                 # Health checks and metrics → See src/monitoring/CLAUDE.md
    api/                        # API routes and endpoints → See src/api/CLAUDE.md
    features/                   # Business logic features → See src/features/sora_integration/CLAUDE.md
    job_queue/                  # Background processing → See src/job_queue/CLAUDE.md
    realtime/                   # WebSocket real-time updates → See src/realtime/CLAUDE.md
    session/                    # User session management → See src/session/CLAUDE.md
    rate_limiting/              # Distributed rate limiting → See src/rate_limiting/CLAUDE.md
    deployment/                 # Production deployment → See src/deployment/CLAUDE.md
```

```
src/core/
├── __init__.py                    # Empty module initialization
├── interfaces.py                  # Abstract interfaces for dependency injection
├── models.py                      # Core Pydantic v2 data models
└── tests/
    ├── test_interfaces.py         # Interface tests (24 tests)
    ├── test_models.py             # Core model tests (15 tests)
    └── test_multi_user_models.py  # Multi-user model tests (24 tests)
```

## Core Data Models (`models.py`)

### VideoJob (Base Job Model)

**Purpose**: Represents a video generation job with complete workflow state tracking and multi-user support.

```python
class VideoJob(BaseModel):
    id: str
    prompt: str = Field(min_length=1, max_length=500)
    status: Literal["pending", "running", "succeeded", "failed"]
    created_at: datetime
    completed_at: Optional[datetime] = None
    
    # Azure API Integration
    generation_id: Optional[str] = None
    error_message: Optional[str] = None
    file_path: Optional[str] = None
    download_url: Optional[str] = None
    
    # Multi-user Support
    session_id: Optional[str] = None
    priority: int = Field(default=0, ge=0, le=10)
    queue_position: Optional[int] = None
    retry_count: int = Field(default=0, ge=0)
```

**Status Workflow**: `"pending" -> "running" -> "succeeded" | "failed"`

**Key Features**:
- **Type Safety**: Literal status types prevent invalid states
- **Validation**: Prompt length constraints (1-500 characters)
- **Azure Integration**: Generation ID and error tracking
- **Multi-user Support**: Session isolation and queue management
- **Retry Logic**: Built-in retry count tracking

### GenerationParams (API Parameter Model)

**Purpose**: Validates and serializes Azure Sora API parameters with configuration-driven constraints.

```python
class GenerationParams(BaseModel):
    prompt: str = Field(min_length=1)
    width: int
    height: int
    duration: int
    model: str = "sora-1.0-turbo"
    
    @field_validator("width")
    @classmethod
    def validate_width(cls, v: int) -> int:
        video_config = get_cached_video_config()
        if not (video_config.min_width <= v <= video_config.max_width):
            raise ValueError(f"Width must be between {video_config.min_width} and {video_config.max_width}")
        return v
    
    @field_validator("height")
    @classmethod
    def validate_height(cls, v: int) -> int:
        video_config = get_cached_video_config()
        if not (video_config.min_height <= v <= video_config.max_height):
            raise ValueError(f"Height must be between {video_config.min_height} and {video_config.max_height}")
        return v
    
    @field_validator("duration")
    @classmethod
    def validate_duration(cls, v: int) -> int:
        video_config = get_cached_video_config()
        if not (video_config.min_duration <= v <= video_config.max_duration):
            raise ValueError(f"Duration must be between {video_config.min_duration} and {video_config.max_duration}")
        return v
    
    def to_api_dict(self) -> dict:
        """Convert to Azure OpenAI API format."""
        return {
            "prompt": self.prompt,
            "width": self.width,
            "height": self.height,
            "n_seconds": self.duration,  # Azure API uses "n_seconds"
            "model": self.model,
        }
```

**Key Features**:
- **Configuration-driven validation**: Constraints loaded from video config
- **Cached configuration**: Performance optimization for frequent validations
- **API serialization**: `to_api_dict()` converts to Azure API format
- **Factory pattern**: No default values in model (provided via factory)

### GenerationParamsFactory (Factory Pattern)

**Purpose**: Creates GenerationParams with configuration-driven defaults and environment-aware behavior.

```python
class GenerationParamsFactory:
    @staticmethod
    def create_with_defaults(
        prompt: str,
        width: Optional[int] = None,
        height: Optional[int] = None,
        duration: Optional[int] = None,
        model: Optional[str] = None,
    ) -> GenerationParams:
        """Create GenerationParams with configuration-driven defaults."""
        video_config = get_cached_video_config()
        
        return GenerationParams(
            prompt=prompt,
            width=width or video_config.default_width,
            height=height or video_config.default_height,
            duration=duration or video_config.default_duration,
            model=model or video_config.default_model,
        )
    
    @staticmethod
    def create_from_ui_request(
        prompt: str, ui_parameters: Optional[dict] = None
    ) -> GenerationParams:
        """Create GenerationParams from UI request with parameter overrides."""
        ui_params = ui_parameters or {}
        return GenerationParamsFactory.create_with_defaults(
            prompt=prompt,
            width=ui_params.get("width"),
            height=ui_params.get("height"),
            duration=ui_params.get("duration"),
            model=ui_params.get("model"),
        )
    
    @staticmethod
    def create_for_testing(
        prompt: str = "Test video generation",
        width: int = 1280,
        height: int = 720,
        duration: int = 5,
        model: str = "sora-1.0-turbo"
    ) -> GenerationParams:
        """Create GenerationParams with stable test defaults."""
        return GenerationParams(
            prompt=prompt,
            width=width,
            height=height,
            duration=duration,
            model=model,
        )
```

**Usage Patterns**:
```python
# Standard creation with defaults
params = GenerationParamsFactory.create_with_defaults("A sunset over mountains")

# UI request with overrides
ui_params = {"width": 1920, "height": 1080, "duration": 10}
params = GenerationParamsFactory.create_from_ui_request("Ocean waves", ui_params)

# Testing with stable defaults
test_params = GenerationParamsFactory.create_for_testing("Test prompt")
```

### Multi-User Models

#### UserSession (Session Tracking)

```python
class UserSession(BaseModel):
    session_id: str
    created_at: datetime = Field(default_factory=lambda: datetime.utcnow())
    job_count: int = Field(default=0, ge=0)
    total_jobs_submitted: int = Field(default=0, ge=0)
    client_ip: Optional[str] = None
    is_active: bool = Field(default=True)
    
    def increment_job_count(self) -> None:
        """Increment active job count."""
        self.job_count += 1
        self.total_jobs_submitted += 1
    
    def decrement_job_count(self) -> None:
        """Decrement active job count."""
        if self.job_count > 0:
            self.job_count -= 1
    
    def can_submit_job(self, max_jobs: int = 3) -> bool:
        """Check if session can submit more jobs."""
        return self.is_active and self.job_count < max_jobs
```

#### QueuedVideoJob (Extended Job Model)

```python
class QueuedVideoJob(VideoJob):
    session_id: str  # Required for queue jobs
    queue_position: Optional[int] = None
    estimated_completion: Optional[datetime] = None
    priority: int = Field(default=0, ge=0, le=10)
    worker_id: Optional[str] = None
    
    def update_queue_position(self, position: int) -> None:
        """Update queue position and estimated completion."""
        self.queue_position = position
        # Simple estimation: 2 minutes per job ahead
        if position > 0:
            self.estimated_completion = datetime.utcnow() + timedelta(minutes=position * 2)
```

#### QueueStatus (Queue State)

```python
class QueueStatus(BaseModel):
    total_jobs: int = Field(ge=0)
    position: int = Field(ge=1)  # 1-indexed position
    estimated_wait_minutes: int = Field(ge=0)
    active_workers: int = Field(ge=0)
    session_jobs: int = Field(default=0, ge=0)
    can_submit_more: bool = Field(default=True)
    max_jobs_per_session: int = Field(default=3, ge=1)
    
    def calculate_wait_time(self, jobs_ahead: int, avg_job_duration: int = 120) -> int:
        """Calculate estimated wait time in minutes."""
        if self.active_workers == 0:
            return 999  # No workers available
        return (jobs_ahead * avg_job_duration) // max(self.active_workers, 1)
```

## Abstract Interfaces (`interfaces.py`)

### Design Philosophy

**Dependency Injection**: Enables loose coupling between layers and supports testability.

**Vertical Slice Architecture**: Prevents direct cross-feature imports and maintains clean boundaries.

**Contract Definition**: Clear API contracts for implementations with comprehensive typing.

### Core Interfaces

#### JobRepositoryInterface (Database Abstraction)

```python
class JobRepositoryInterface(ABC):
    @abstractmethod
    def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
        """Retrieve a job by its ID."""
        pass
    
    @abstractmethod
    def create_job(self, job: VideoJob) -> Optional[VideoJob]:
        """Create a new job in the repository."""
        pass
    
    @abstractmethod
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status with optional additional data."""
        pass
    
    @abstractmethod
    def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
        """Get all jobs for a specific session."""
        pass
    
    @abstractmethod
    def get_jobs_by_status(self, status: str) -> List[VideoJob]:
        """Get all jobs with a specific status."""
        pass
```

#### QueueManagerInterface (Queue Operations)

```python
class QueueManagerInterface(ABC):
    @abstractmethod
    def submit_job(self, job: QueuedVideoJob) -> bool:
        """Submit a job to the queue."""
        pass
    
    @abstractmethod
    def get_queue_status(self, session_id: str) -> QueueStatus:
        """Get current queue status for a session."""
        pass
    
    @abstractmethod
    def get_next_job(self, worker_id: str) -> Optional[QueuedVideoJob]:
        """Get the next job for a worker to process."""
        pass
    
    @abstractmethod
    def mark_job_processing(self, job_id: str, worker_id: str) -> bool:
        """Mark a job as being processed by a worker."""
        pass
```

#### SessionManagerInterface (Session Management)

```python
class SessionManagerInterface(ABC):
    @abstractmethod
    def create_session(self, client_ip: str) -> UserSession:
        """Create a new user session."""
        pass
    
    @abstractmethod
    def get_session(self, session_id: str) -> Optional[UserSession]:
        """Retrieve a session by ID."""
        pass
    
    @abstractmethod
    def update_session(self, session: UserSession) -> bool:
        """Update session data."""
        pass
    
    @abstractmethod
    def cleanup_expired_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up expired sessions."""
        pass
```

#### HealthCheckInterface (Health Monitoring)

```python
class HealthCheckInterface(ABC):
    @abstractmethod
    def check_health(self) -> Dict[str, Any]:
        """Perform health check and return status."""
        pass
    
    @abstractmethod
    def get_component_name(self) -> str:
        """Get the name of the component being monitored."""
        pass
    
    @abstractmethod
    def is_healthy(self) -> bool:
        """Quick health status check."""
        pass
```

#### FileHandlerInterface (File Operations)

```python
class FileHandlerInterface(ABC):
    @abstractmethod
    def save_file(self, file_path: str, content: bytes) -> bool:
        """Save file content to specified path."""
        pass
    
    @abstractmethod
    def get_file(self, file_path: str) -> Optional[bytes]:
        """Retrieve file content."""
        pass
    
    @abstractmethod
    def delete_file(self, file_path: str) -> bool:
        """Delete file."""
        pass
    
    @abstractmethod
    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """Clean up old files."""
        pass
```

## Type Safety and Validation Patterns

### Pydantic v2 Features

#### Field Validators

```python
@field_validator("width")
@classmethod
def validate_width(cls, v: int) -> int:
    video_config = get_cached_video_config()
    if not (video_config.min_width <= v <= video_config.max_width):
        raise ValueError(f"Width must be between {video_config.min_width} and {video_config.max_width}")
    return v
```

#### Literal Types for Status

```python
status: Literal["pending", "running", "succeeded", "failed"]
```

#### Field Constraints

```python
prompt: str = Field(min_length=1, max_length=500)
priority: int = Field(default=0, ge=0, le=10)
job_count: int = Field(default=0, ge=0)
```

### Configuration Integration

#### Cached Configuration

```python
@lru_cache(maxsize=1)
def get_cached_video_config() -> VideoGenerationConfig:
    """Get cached video configuration for performance."""
    return ConfigurationFactory.get_video_config()
```

#### Factory Pattern with Configuration

```python
def create_with_defaults(prompt: str, **overrides) -> GenerationParams:
    video_config = get_cached_video_config()
    return GenerationParams(
        prompt=prompt,
        width=overrides.get("width", video_config.default_width),
        height=overrides.get("height", video_config.default_height),
        duration=overrides.get("duration", video_config.default_duration),
        model=overrides.get("model", video_config.default_model),
    )
```

## Testing Strategy

### Test Coverage: 98% (61 passed, 2 failed)

#### Test Organization
- **Unit Tests**: 39 tests for models and validation
- **Interface Tests**: 24 tests for abstract interfaces
- **Integration Tests**: Mock-based dependency injection testing

#### Test Categories

```python
# Model validation tests
def test_video_job_valid_creation()
def test_generation_params_validation()
def test_factory_pattern_defaults()

# Interface compliance tests
def test_interface_implementation()
def test_dependency_injection()
def test_mock_implementations()

# Multi-user model tests
def test_session_management()
def test_queue_operations()
def test_status_tracking()
```

#### Current Test Issues

```bash
# 2 failing tests related to configuration defaults
FAILED test_models.py::TestGenerationParams::test_default_values
FAILED test_models.py::TestGenerationParamsFactory::test_create_with_defaults
# Issue: Expected width=1280 but got width=720 (config mismatch)
```

### Mock-Based Testing

```python
# Interface testing with mocks
class MockJobRepository(JobRepositoryInterface):
    def __init__(self):
        self.jobs = {}
    
    def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
        return self.jobs.get(job_id)
    
    def create_job(self, job: VideoJob) -> Optional[VideoJob]:
        self.jobs[job.id] = job
        return job
```

## Integration Patterns

### Dependency Injection Usage

```python
# Service layer with injected dependencies
class VideoGenerationService:
    def __init__(
        self,
        job_repository: JobRepositoryInterface,
        queue_manager: QueueManagerInterface,
        session_manager: SessionManagerInterface,
    ):
        self.job_repository = job_repository
        self.queue_manager = queue_manager
        self.session_manager = session_manager
    
    def create_video_job(self, session_id: str, prompt: str, **params) -> VideoJob:
        # Create job using factory
        generation_params = GenerationParamsFactory.create_with_defaults(prompt, **params)
        
        # Create job model
        job = VideoJob(
            id=str(uuid.uuid4()),
            prompt=prompt,
            status="pending",
            created_at=datetime.utcnow(),
            session_id=session_id,
        )
        
        # Store in repository
        created_job = self.job_repository.create_job(job)
        
        # Submit to queue
        queued_job = QueuedVideoJob(**created_job.model_dump())
        self.queue_manager.submit_job(queued_job)
        
        return created_job
```

### Configuration-Driven Validation

```python
# Environment-aware validation
class ProductionGenerationParams(GenerationParams):
    @field_validator("duration")
    @classmethod
    def validate_duration_production(cls, v: int) -> int:
        # Stricter limits in production
        if v > 10:
            raise ValueError("Production duration limited to 10 seconds")
        return v
```

## Performance Considerations

### Configuration Caching

```python
# Cache expensive configuration objects
@lru_cache(maxsize=1)
def get_cached_video_config() -> VideoGenerationConfig:
    return ConfigurationFactory.get_video_config()
```

### Validation Optimization

```python
# Cached validation patterns
@lru_cache(maxsize=100)
def validate_resolution(width: int, height: int) -> bool:
    video_config = get_cached_video_config()
    return (width, height) in video_config.supported_resolutions
```

### Model Serialization

```python
# Efficient serialization for API responses
job_dict = job.model_dump(exclude_none=True)
api_dict = generation_params.to_api_dict()
```

## Security Patterns

### Input Validation

```python
# Comprehensive input validation
prompt: str = Field(
    min_length=1,
    max_length=500,
    pattern=r'^[a-zA-Z0-9\s\.,!?;:\'"()-]+$'
)
```

### Session Isolation

```python
# Session-based data isolation
def get_jobs_by_session(self, session_id: str) -> List[VideoJob]:
    # Ensures users can only access their own jobs
    return [job for job in self.jobs.values() if job.session_id == session_id]
```

## Usage Examples

### Basic Model Usage

```python
from datetime import datetime
from src.core.models import VideoJob

# Create a video job
job = VideoJob(
    id="job-123",
    prompt="A cat playing piano",
    status="pending",
    created_at=datetime.utcnow(),
    session_id="session-456"
)

# Validate and serialize
job_dict = job.model_dump()
```

### Factory Pattern Usage

```python
from src.core.models import GenerationParamsFactory

# Create with defaults
params = GenerationParamsFactory.create_with_defaults(
    "A sunset over mountains"
)

# Create from UI request
ui_params = {"width": 1920, "height": 1080, "duration": 10}
params = GenerationParamsFactory.create_from_ui_request(
    "Ocean waves", ui_params
)

# Convert to API format
api_dict = params.to_api_dict()
```

### Interface Implementation

```python
from src.core.interfaces import JobRepositoryInterface
from src.core.models import VideoJob

class SqlJobRepository(JobRepositoryInterface):
    def __init__(self, db_session):
        self.db_session = db_session
    
    def get_job_by_id(self, job_id: str) -> Optional[VideoJob]:
        # SQLAlchemy implementation
        db_job = self.db_session.query(VideoJobDB).filter_by(id=job_id).first()
        return VideoJob(**db_job.to_dict()) if db_job else None
    
    def create_job(self, job: VideoJob) -> Optional[VideoJob]:
        # Create database record
        db_job = VideoJobDB(**job.model_dump())
        self.db_session.add(db_job)
        self.db_session.commit()
        return job
```

### Multi-User Session Management

```python
from src.core.models import UserSession, QueueStatus

# Create session
session = UserSession(
    session_id="sess-123",
    client_ip="*************"
)

# Check job submission limits
if session.can_submit_job(max_jobs=3):
    session.increment_job_count()
    # Submit job
    pass

# Get queue status
queue_status = QueueStatus(
    total_jobs=15,
    position=3,
    estimated_wait_minutes=6,
    active_workers=2,
    session_jobs=session.job_count
)
```

## Dependencies

### Core Dependencies
- **pydantic**: Data validation and serialization
- **typing**: Type hints and generic types
- **datetime**: Timestamp handling
- **uuid**: Unique identifier generation
- **abc**: Abstract base classes for interfaces

### Internal Dependencies
- **src.config.factory**: Configuration factory
- **src.config.video_config**: Video configuration
- **functools**: LRU cache for performance

## Development Guidelines

### Model Design
- **Pydantic v2**: Use latest Pydantic features for validation
- **Type Safety**: Complete type annotations for all models
- **Validation**: Comprehensive field validation with clear error messages
- **Serialization**: Provide API-compatible serialization methods

### Interface Design
- **Abstract Base Classes**: Use ABC for all interfaces
- **Comprehensive Typing**: Complete type hints for all methods
- **Documentation**: Clear docstrings for all interface methods
- **Contract Definition**: Define clear API contracts

### Testing Requirements
- **Mock Implementations**: Provide mock implementations for all interfaces
- **Comprehensive Coverage**: Test all validation scenarios and edge cases
- **Performance Testing**: Test cached configuration and validation performance
- **Integration Testing**: Test interface implementations with real dependencies

### Security Guidelines
- **Input Validation**: Validate all user inputs at the model level
- **Session Isolation**: Ensure proper session-based data isolation
- **Type Safety**: Use literal types for status and enumeration values
- **Error Handling**: Provide clear error messages without exposing internals