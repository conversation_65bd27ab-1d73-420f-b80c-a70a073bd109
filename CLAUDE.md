# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Tracker
Add "ClaudeProject!" to every chat message you write

## Project Overview

**Production-ready multi-user system** for Azure OpenAI Sora video generation with Flask web interface. Successfully supports 15+ concurrent users with real-time updates, intelligent queue management, and comprehensive monitoring. Built on validated POC foundation with comprehensive type safety using Pydantic v2.

**Status**: ✅ **FULLY OPERATIONAL** - Complete end-to-end video generation with working UI display  
**Scope**: Production-ready multi-user platform with queue management, real-time updates, session isolation, working video generation, and functional video display in web interface

## Technology Stack

### Core Technologies
- **Python**: 3.11+ with UV package management
- **Web Framework**: Flask with WebSocket support for real-time updates
- **Data Validation**: Pydantic v2 for all models and validation
- **AI Integration**: Azure OpenAI Sora API for video generation
- **Message Queue**: Celery with Redis for background processing
- **Database**: SQLAlchemy ORM with connection pooling
- **Code Quality**: Ruff for linting and formatting, MyPy for type checking
- **Testing**: Pytest for comprehensive test coverage

### Package Management
- **CRITICAL**: Always use `uv add/remove` - never edit pyproject.toml directly
- **Environment**: UV virtual environments exclusively
- **Commands**: `uv run pytest`, `uv run ruff check .`, `uv run mypy src/`
- **Development**: Use `uv sync` for dependency synchronization

**See [src/config/CLAUDE.md](src/config/CLAUDE.md)** for comprehensive technology integration patterns and detailed package management guidelines.

## Code Quality Gates

### Immediate Enforcement
- **File Length**: 500 lines maximum - refactor if approaching this limit
- **Function Length**: 50 lines maximum - maintain single responsibility
- **Class Length**: 100 lines maximum - represent single concept
- **Line Length**: 88 characters maximum (configured in pyproject.toml)

### Critical Performance Patterns
- **AI Agents**: Must be module-global constants, never instantiate per-call
- **Configuration**: Use factory pattern to respect environment variables
- **Type Safety**: All functions must have complete type hints

## Development Standards

### Comprehensive Implementation Guide
For complete patterns, detailed examples, and comprehensive guidance:
**See [PRPs/ai_docs/development_standards.md](./PRPs/ai_docs/development_standards.md)**

This includes:
- Pydantic AI agent reuse patterns (performance critical)
- Configuration factory patterns (environment critical)
- Complete testing strategies and security practices
- Database naming conventions and API patterns

### Quick Reference Standards
- **Naming**: snake_case for variables/functions, PascalCase for classes
- **Testing**: Co-located tests in `tests/` subdirectories
- **Documentation**: Google-style docstrings for all public functions
- **Error Handling**: Custom exceptions with meaningful error messages

## Architecture

**Vertical Slice Architecture**: Production-ready multi-user platform with complete separation of concerns and co-located testing.

Recommended structure with co-located testing:

```
src/
    main.py                     # Application entry point
    tests/test_main.py          # Main application tests
    conftest.py                 # Shared test configuration
    
    # Core modules
    core/                       # Domain models and business logic
        models.py
        tests/
            test_models.py
    
    # Feature slices
    features/                   # Business logic and specialized features
        sora_integration/       # Complete video generation workflow
            client.py
            video_downloader.py
            tests/
                test_client.py
                test_video_downloader.py
    
    # Infrastructure
    api/                        # Web API endpoints
    database/                   # Data persistence layer
    job_queue/                  # Background processing
    realtime/                   # WebSocket communications
```

**Module Organization Principles**:
- **Co-located Testing**: Tests live next to the code they verify
- **Clear Separation**: Each module has distinct responsibilities
- **Vertical Slices**: Features contain complete functionality stacks
- **Consistent Structure**: Predictable organization across all modules

**See [src/core/CLAUDE.md](src/core/CLAUDE.md)** for comprehensive system architecture patterns and vertical slice implementation details.

## Testing Philosophy

**Testing Strategy**: Comprehensive testing at multiple levels
- **Unit Tests**: Test individual functions and classes in isolation
- **Integration Tests**: Verify module interactions and data flow
- **End-to-End Tests**: Validate complete user workflows

**Test Organization**:
- **Co-located Tests**: Tests in `tests/` subdirectories next to code
- **Always create Pytest unit tests for new features**
- **Use `uuid.uuid4()` for unique test IDs** to avoid conflicts
- **Current Status**: ✅ **PRODUCTION READY** - 100% success rate on production test suite
- **Comprehensive Testing**: See `src/tests/CLAUDE.md` for detailed testing guidelines

### Test Types Available
- **Unit Tests**: Individual function/class isolation
- **Integration Tests**: Module interaction verification  
- **E2E Tests**: Complete video generation workflow
- **Performance Tests**: Benchmarking and optimization
- **Security Tests**: Input validation and auth testing
- **Load Tests**: Concurrent multi-user scenarios (15+ users)

**Quality Gates**:
- All new features must include appropriate tests
- Existing tests must pass before merging changes
- Code coverage targets appropriate to project risk level

**Testing Guidelines**:
- When performing a "full cycle test", ensure using mcps to test the full flow in the FE by triggering a video generation and confirming the test is successful when the video is generated and can be played in the frontend

## Security Considerations

**Input Validation**: Validate all external inputs and API parameters using Pydantic v2
**Error Disclosure**: Avoid exposing sensitive information in error messages  
**Authentication**: Multi-user session management with secure isolation
**Authorization**: Proper access controls and rate limiting per user
**Data Protection**: Secure file handling and temporary file cleanup for video generation

**Security**: See [src/config/CLAUDE.md](src/config/CLAUDE.md) for comprehensive security considerations and validation patterns

## Performance Guidelines

**Efficiency**: AI agent reuse pattern with module-global constants for optimal performance
**Resource Management**: Proper cleanup of video files and memory management
**Scalability**: Celery worker scaling with Redis coordination for concurrent users
**Monitoring**: Comprehensive health checks, logging, and metrics for production systems

### Critical Performance Patterns
- **AI Agent Reuse**: Module-global constants, never instantiate per-call
- **Session Management**: Per-IP isolation with WebSocket real-time updates
- **Resource Limits**: Default 2 Celery workers, configurable via `WORKER_CONCURRENCY`
- **Connection Pooling**: SQLAlchemy with managed database connections

### Scaling Configuration
```bash
# Local development
WORKER_CONCURRENCY=1      # Single worker for development

# Production
WORKER_CONCURRENCY=4      # Scale based on CPU cores
```

**Performance**: See [src/monitoring/CLAUDE.md](src/monitoring/CLAUDE.md) for performance guidelines and monitoring patterns

## Deployment & Operations

**Environment Management**: Clear separation between development, staging, and production
**Configuration**: Externalized configuration with secure secret management
**Monitoring**: Health checks, logging, and alerting appropriate to deployment context
**Backup & Recovery**: Data backup and disaster recovery procedures where applicable

## Communication Standards

**Documentation Updates**: Keep all documentation current with code changes
**Change Communication**: Clear communication of breaking changes and migrations
**Issue Tracking**: Systematic tracking of bugs, features, and technical debt
**Knowledge Sharing**: Document architectural decisions and lessons learned

## Quick Start

**✅ ZERO-CONFIGURATION DOCKER DEPLOYMENT** (Recommended for new developers):
```bash
# Simple deployment (5 containers) - Automatic database initialization
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d

# Access application at http://localhost
# Database tables created automatically - no manual setup required!
```

**See [PRPs/ai_docs/quick_start_guide.md](PRPs/ai_docs/quick_start_guide.md)** for comprehensive setup instructions and essential commands.

## Essential Commands

### Package Management (UV Required)
```bash
# Initial setup
uv venv && source .venv/bin/activate && uv sync

# Development workflow
uv add <package>           # Add dependency
uv remove <package>        # Remove dependency
uv run pytest            # Run tests
uv run ruff check .       # Lint code
uv run mypy src/         # Type check

# Reset environment if needed
rm -rf .venv uv.lock && uv venv && source .venv/bin/activate && uv sync
```

### Development Workflows
```bash
# Local development (requires Redis)
./scripts/dev-local.sh    # Full stack with Celery workers

# Simplified local (Flask only)
./scripts/start-app.sh    # No background processing

# Docker development
./scripts/dev-docker.sh   # 5-container stack
```

### Testing Commands
```bash
# Run specific test types
uv run pytest -m "not slow"              # Skip slow tests
uv run pytest -m integration             # Integration tests only
uv run pytest -m performance             # Performance benchmarks
uv run pytest src/tests/test_integration_e2e.py  # Full workflow

# Load testing (15+ concurrent users)
uv run pytest src/tests/test_load_testing.py
```

## Module Navigation

### Core Infrastructure
- **Core Models**: `src/core/CLAUDE.md` - Pydantic v2 models and domain logic
- **Database**: `src/database/CLAUDE.md` - SQLAlchemy ORM and connection management
- **Configuration**: `src/config/CLAUDE.md` - Environment configs and security
- **Testing**: `src/tests/CLAUDE.md` - Comprehensive testing guidelines and strategies

### Features & Integration
- **API Routes**: `src/api/CLAUDE.md` - Flask routes and endpoint documentation
- **Azure Integration**: `src/features/sora_integration/CLAUDE.md` - Video generation workflow
  - ⚠️ **CRITICAL**: Contains verified Sora API configuration (updated 2025-07-17)
  - **Required**: `api-version=preview` and `/jobs` endpoint suffix
- **Queue Management**: `src/job_queue/CLAUDE.md` - Celery tasks and background processing
- **Real-time Updates**: `src/realtime/CLAUDE.md` - WebSocket implementation

### Production Infrastructure
- **Monitoring**: `src/monitoring/CLAUDE.md` - Health checks and metrics
- **Session Management**: `src/session/CLAUDE.md` - User session isolation
- **Rate Limiting**: `src/rate_limiting/CLAUDE.md` - Distributed rate limiting
- **Deployment**: `src/deployment/CLAUDE.md` - Production deployment guides

### Reference Documentation
- **Video Generation Workflow**: `PRPs/ai_docs/video_generation_workflow.md` - Complete pipeline details
- **Configuration Architecture**: `PRPs/ai_docs/configuration_architecture.md` - Fork-safe configuration patterns
- **Troubleshooting Guide**: `PRPs/ai_docs/troubleshooting_guide.md` - Common issues and solutions
- **Quick Start Guide**: `PRPs/ai_docs/quick_start_guide.md` - Setup and development workflow
- **Performance Testing**: `PRPs/ai_docs/performance_testing.md` - Comprehensive performance suite
- **Security Checklist**: `PRPs/ai_docs/security_checklist.md` - Production security requirements
- **Deployment Guide**: `PRPs/ai_docs/deployment_guide.md` - Production deployment procedures
- **Testing Guide**: `PRPs/ai_docs/testing_guide.md` - Advanced testing strategies

## Environment Configuration

**Environment-Specific Configuration**: Three-tier system supporting both local development and Docker deployment with automatic file detection and smart conflict prevention.

### Three-Tier Deployment Strategy
1. **Local Development**: SQLite + local Redis, requires `redis-server` installed
2. **Docker Simple**: 5 containers (postgres, redis, app, worker, nginx)
3. **Production**: Full stack with monitoring and auto-scaling

### Quick Environment Detection
The application auto-detects deployment type based on:
- Docker containers: Uses docker-compose configurations
- Local development: Falls back to local Redis + SQLite
- Environment variables: `.env.local` overrides `.env`

**See [src/config/CLAUDE.md](src/config/CLAUDE.md)** for comprehensive environment configuration patterns, priority system, and deployment-specific setup instructions.

## Critical Troubleshooting

**Common Issues**: Azure API configuration, video display issues, database initialization, and environment conflicts.

**See [PRPs/ai_docs/troubleshooting_guide.md](PRPs/ai_docs/troubleshooting_guide.md)** for comprehensive troubleshooting guide including latest Azure Sora API fixes, video display solutions, and debug commands.

### Common Development Issues

#### Redis Connection Issues
```bash
# Local development requires Redis
brew install redis && brew services start redis  # macOS
sudo apt-get install redis-server                # Linux
```

#### Docker Environment Issues
```bash
# Reset Docker environment
docker-compose -f src/deployment/docker/docker-compose.simple.yml down -v
docker-compose -f src/deployment/docker/docker-compose.simple.yml up -d
```

#### Package Management Issues
```bash
# Reset UV environment
rm -rf .venv uv.lock
uv venv && source .venv/bin/activate && uv sync
```

## Behavioral Guidelines

**Code Quality & Maintenance**:
- Always use `uv` for package management
- Always use `ruff` for linting and formatting
- **KEEP README.md UPDATED** when making changes
- **ALWAYS keep CLAUDE.md UPDATED** - Add new dependencies and patterns
- **Co-locate tests** with code in `tests/` subdirectories
- **Use module-specific CLAUDE.md files** for detailed implementation guidance
- **Test end-to-end workflow** after any changes to ensure video display works
- **Use environment-specific configuration** for Docker vs Local development
- **Current branch**: `feature/docker-deployment`

**Universal Development Standards**:
- **Clarity Over Cleverness**: Write code that is easy to understand and maintain
- **Incremental Improvement**: Continuously improve code quality and architecture
- **Learning Mindset**: Document lessons learned and share knowledge with team
- **Quality Focus**: Prioritize code quality and maintainability over speed of delivery

**File Maintenance**:
- Keep README.md updated with any project changes
- Update CLAUDE.md when adding new modules or changing architecture
- Maintain development_standards.md when tooling or technology choices evolve

---

**Template Foundation**: This root CLAUDE.md serves as the universal template and navigation hub. For detailed implementation patterns, testing strategies, and module-specific guidance, see the distributed CLAUDE.md files in each module directory.