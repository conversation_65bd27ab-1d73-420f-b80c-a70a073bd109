# Multi-User Architecture Analysis and Recommendations

## Executive Summary

This document provides comprehensive architecture recommendations for transforming the current single-user Sora POC into a multi-user production system capable of handling 10+ concurrent users with enterprise-grade features.

## Current Architecture Analysis

### Strengths of Existing Codebase
- **Vertical Slice Architecture**: Well-organized feature-based structure
- **Comprehensive Testing**: 275 tests with 89% pass rate
- **Configuration Management**: Factory pattern with environment-specific configs
- **Database Integration**: SQLAlchemy ORM with migration support
- **Health Monitoring**: Built-in health checks and metrics
- **Security Foundation**: Path traversal protection, input validation, API key sanitization

### Current Limitations for Multi-User
- **No User Authentication**: Single-user system without identity management
- **No Session Management**: No user session tracking or isolation
- **No Rate Limiting per User**: Global rate limiting only
- **No Background Processing**: Synchronous job processing
- **No Multi-Tenancy**: Shared database without user isolation
- **No Quota Management**: No per-user resource limits

## Multi-User Architecture Recommendations

### 1. User Authentication & Authorization System

#### Database Schema Additions
```sql
-- User Management Tables
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    subscription_tier VARCHAR(50) DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    INDEX idx_users_email (email),
    INDEX idx_users_subscription (subscription_tier)
);

-- User Sessions
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sessions_token (session_token),
    INDEX idx_sessions_user_id (user_id),
    INDEX idx_sessions_expires (expires_at)
);

-- API Keys (for programmatic access)
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    key_prefix VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    permissions JSON,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_api_keys_user_id (user_id),
    INDEX idx_api_keys_prefix (key_prefix)
);
```

#### Enhanced VideoJob Schema
```sql
-- Update existing video_jobs table
ALTER TABLE video_jobs 
ADD COLUMN user_id UUID NOT NULL,
ADD COLUMN visibility VARCHAR(20) DEFAULT 'private',
ADD COLUMN shared_token VARCHAR(255),
ADD FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add indexes for multi-user queries
CREATE INDEX idx_video_jobs_user_id ON video_jobs(user_id);
CREATE INDEX idx_video_jobs_user_status ON video_jobs(user_id, status);
CREATE INDEX idx_video_jobs_shared_token ON video_jobs(shared_token);
```

#### User Management Models
```python
# src/core/models.py additions

class User(BaseModel):
    """User account model with subscription management."""
    
    id: str = Field(..., description="Unique user identifier")
    email: str = Field(..., description="User email address")
    first_name: Optional[str] = Field(None, description="User first name")
    last_name: Optional[str] = Field(None, description="User last name")
    is_active: bool = Field(True, description="User account status")
    is_verified: bool = Field(False, description="Email verification status")
    subscription_tier: Literal["free", "pro", "enterprise"] = Field("free", description="Subscription tier")
    created_at: datetime = Field(..., description="Account creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    
    @field_validator("email")
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email format."""
        import re
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', v):
            raise ValueError("Invalid email format")
        return v.lower()

class UserSession(BaseModel):
    """User session model for authentication tracking."""
    
    id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    session_token: str = Field(..., description="Session token")
    expires_at: datetime = Field(..., description="Session expiration")
    created_at: datetime = Field(..., description="Session creation timestamp")
    last_accessed: datetime = Field(..., description="Last access timestamp")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    user_agent: Optional[str] = Field(None, description="Client user agent")

class APIKey(BaseModel):
    """API key model for programmatic access."""
    
    id: str = Field(..., description="API key identifier")
    user_id: str = Field(..., description="User identifier")
    key_name: str = Field(..., description="Human-readable key name")
    key_prefix: str = Field(..., description="Key prefix for identification")
    is_active: bool = Field(True, description="Key active status")
    permissions: dict = Field(default_factory=dict, description="Key permissions")
    expires_at: Optional[datetime] = Field(None, description="Key expiration")
    created_at: datetime = Field(..., description="Creation timestamp")
    last_used: Optional[datetime] = Field(None, description="Last usage timestamp")
```

### 2. Background Job Processing System

#### Redis Queue Integration
```python
# src/queue/manager.py

import redis
from rq import Queue, Worker
from typing import Optional, Dict, Any
import logging

class JobQueueManager:
    """Redis-based job queue for background video processing."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_conn = redis.from_url(redis_url)
        self.video_queue = Queue('video_generation', connection=self.redis_conn)
        self.priority_queue = Queue('priority_video', connection=self.redis_conn)
        self.logger = logging.getLogger(__name__)
    
    def enqueue_video_job(
        self, 
        user_id: str, 
        job_id: str, 
        prompt: str, 
        parameters: Dict[str, Any],
        priority: bool = False
    ) -> str:
        """Enqueue video generation job."""
        
        queue = self.priority_queue if priority else self.video_queue
        
        job = queue.enqueue(
            'src.job_queue.workers.process_video_generation',
            user_id=user_id,
            job_id=job_id,
            prompt=prompt,
            parameters=parameters,
            job_timeout='15m',
            job_id=f"video_{job_id}"
        )
        
        self.logger.info(f"Enqueued video job {job_id} for user {user_id}")
        return job.id
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job status from queue."""
        try:
            job = self.video_queue.fetch_job(f"video_{job_id}")
            if not job:
                job = self.priority_queue.fetch_job(f"video_{job_id}")
            
            if job:
                return {
                    'id': job.id,
                    'status': job.get_status(),
                    'progress': job.meta.get('progress', 0),
                    'created_at': job.created_at.isoformat() if job.created_at else None,
                    'started_at': job.started_at.isoformat() if job.started_at else None,
                    'ended_at': job.ended_at.isoformat() if job.ended_at else None,
                    'result': job.result,
                    'exc_info': job.exc_info
                }
            return None
        except Exception as e:
            self.logger.error(f"Error getting job status: {e}")
            return None
```

#### Background Worker
```python
# src/queue/workers.py

import logging
from typing import Dict, Any
from src.features.sora_integration.client import SoraClient
from src.api.job_repository import JobRepository
from src.core.models import VideoJob

def process_video_generation(
    user_id: str,
    job_id: str,
    prompt: str,
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """Background worker function for video generation."""
    
    logger = logging.getLogger(__name__)
    job_repository = JobRepository()
    
    try:
        # Update job status to running
        job_repository.update_job_status(job_id, "running")
        
        # Initialize Sora client
        client = SoraClient()
        
        # Create video job
        video_job = client.create_video_job(prompt, parameters)
        
        # Poll for completion
        while video_job.status in ["pending", "running"]:
            time.sleep(5)  # Poll every 5 seconds
            video_job = client.poll_job_status(job_id, video_job.generation_id)
            
            # Update progress in queue metadata
            job = get_current_job()
            if job:
                job.meta['progress'] = 50 if video_job.status == "running" else 90
                job.save_meta()
        
        # Update final status
        updated_job = job_repository.update_job(video_job)
        
        logger.info(f"Video generation completed for job {job_id}")
        return {
            'job_id': job_id,
            'status': video_job.status,
            'file_path': video_job.file_path,
            'download_url': video_job.download_url
        }
        
    except Exception as e:
        logger.error(f"Video generation failed for job {job_id}: {e}")
        job_repository.update_job_status(job_id, "failed", str(e))
        raise
```

### 3. Rate Limiting and Quota Management

#### User-Based Rate Limiting
```python
# src/middleware/rate_limiter.py

import redis
import time
from typing import Dict, Optional, Tuple
from flask import request, jsonify, g
from functools import wraps

class UserRateLimiter:
    """Redis-based rate limiter with per-user quotas."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/1"):
        self.redis_conn = redis.from_url(redis_url)
        
        # Rate limits by subscription tier
        self.rate_limits = {
            'free': {
                'requests_per_minute': 5,
                'requests_per_hour': 20,
                'requests_per_day': 50,
                'max_concurrent_jobs': 1
            },
            'pro': {
                'requests_per_minute': 15,
                'requests_per_hour': 100,
                'requests_per_day': 500,
                'max_concurrent_jobs': 3
            },
            'enterprise': {
                'requests_per_minute': 50,
                'requests_per_hour': 1000,
                'requests_per_day': 5000,
                'max_concurrent_jobs': 10
            }
        }
    
    def check_rate_limit(self, user_id: str, subscription_tier: str) -> Tuple[bool, Dict[str, Any]]:
        """Check if user has exceeded rate limits."""
        
        limits = self.rate_limits.get(subscription_tier, self.rate_limits['free'])
        current_time = int(time.time())
        
        # Check different time windows
        windows = [
            ('minute', 60, limits['requests_per_minute']),
            ('hour', 3600, limits['requests_per_hour']),
            ('day', 86400, limits['requests_per_day'])
        ]
        
        for window_name, window_seconds, limit in windows:
            key = f"rate_limit:{user_id}:{window_name}:{current_time // window_seconds}"
            current_count = self.redis_conn.get(key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= limit:
                return False, {
                    'error': f'Rate limit exceeded for {window_name}',
                    'limit': limit,
                    'current': current_count,
                    'window': window_name,
                    'reset_time': (current_time // window_seconds + 1) * window_seconds
                }
        
        return True, {}
    
    def increment_usage(self, user_id: str, subscription_tier: str) -> None:
        """Increment usage counters for user."""
        
        current_time = int(time.time())
        windows = [
            ('minute', 60),
            ('hour', 3600),
            ('day', 86400)
        ]
        
        pipeline = self.redis_conn.pipeline()
        for window_name, window_seconds in windows:
            key = f"rate_limit:{user_id}:{window_name}:{current_time // window_seconds}"
            pipeline.incr(key)
            pipeline.expire(key, window_seconds)
        
        pipeline.execute()

def require_rate_limit(f):
    """Decorator to apply rate limiting to endpoints."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'current_user'):
            return jsonify({'error': 'Authentication required'}), 401
        
        user = g.current_user
        rate_limiter = UserRateLimiter()
        
        allowed, error_info = rate_limiter.check_rate_limit(
            user.id, 
            user.subscription_tier
        )
        
        if not allowed:
            return jsonify(error_info), 429
        
        # Increment usage after successful check
        rate_limiter.increment_usage(user.id, user.subscription_tier)
        
        return f(*args, **kwargs)
    
    return decorated_function
```

### 4. Enhanced API Endpoints

#### Authentication Endpoints
```python
# src/api/auth.py

from flask import Blueprint, request, jsonify, g
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import uuid
from datetime import datetime, timedelta

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register new user account."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if user already exists
        existing_user = user_repository.get_user_by_email(data['email'])
        if existing_user:
            return jsonify({'error': 'User already exists'}), 409
        
        # Create new user
        user = User(
            id=str(uuid.uuid4()),
            email=data['email'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # Hash password
        password_hash = generate_password_hash(data['password'])
        
        # Save user
        saved_user = user_repository.create_user(user, password_hash)
        
        # Generate verification token
        verification_token = generate_verification_token(user.id)
        
        # Send verification email (implement email service)
        send_verification_email(user.email, verification_token)
        
        return jsonify({
            'message': 'User registered successfully',
            'user_id': user.id,
            'verification_required': True
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """User login with session creation."""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        if not email or not password:
            return jsonify({'error': 'Email and password required'}), 400
        
        # Get user by email
        user = user_repository.get_user_by_email(email)
        if not user:
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Check password
        if not check_password_hash(user.password_hash, password):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Check if user is active
        if not user.is_active:
            return jsonify({'error': 'Account is deactivated'}), 401
        
        # Create session
        session_token = str(uuid.uuid4())
        session = UserSession(
            id=str(uuid.uuid4()),
            user_id=user.id,
            session_token=session_token,
            expires_at=datetime.now() + timedelta(hours=24),
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        session_repository.create_session(session)
        
        # Update last login
        user_repository.update_last_login(user.id)
        
        return jsonify({
            'message': 'Login successful',
            'session_token': session_token,
            'user': {
                'id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'subscription_tier': user.subscription_tier
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/logout', methods=['POST'])
@require_authentication
def logout():
    """User logout with session cleanup."""
    try:
        session_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        # Invalidate session
        session_repository.invalidate_session(session_token)
        
        return jsonify({'message': 'Logged out successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
```

#### Enhanced Video Generation Endpoints
```python
# src/api/routes.py updates

@api_bp.route("/generate", methods=["POST"])
@require_authentication
@require_rate_limit
def generate_video():
    """Create video generation job with user context."""
    try:
        user = g.current_user
        data = request.get_json()
        
        prompt = data.get('prompt', '').strip()
        if not prompt:
            return jsonify({'error': 'Prompt is required'}), 400
        
        # Check concurrent job limit
        concurrent_jobs = job_repository.get_user_active_jobs_count(user.id)
        limits = rate_limiter.rate_limits[user.subscription_tier]
        
        if concurrent_jobs >= limits['max_concurrent_jobs']:
            return jsonify({
                'error': 'Maximum concurrent jobs exceeded',
                'limit': limits['max_concurrent_jobs'],
                'current': concurrent_jobs
            }), 429
        
        # Extract parameters
        ui_parameters = {
            'duration': data.get('duration'),
            'width': data.get('width'),
            'height': data.get('height'),
            'model': data.get('model')
        }
        
        # Create job
        job_id = str(uuid.uuid4())
        job = VideoJob(
            id=job_id,
            user_id=user.id,
            prompt=prompt,
            status="pending",
            created_at=datetime.now()
        )
        
        # Save job to database
        saved_job = job_repository.create_job(job)
        
        # Enqueue background processing
        queue_manager = JobQueueManager()
        queue_job_id = queue_manager.enqueue_video_job(
            user_id=user.id,
            job_id=job_id,
            prompt=prompt,
            parameters=ui_parameters,
            priority=(user.subscription_tier == 'enterprise')
        )
        
        return jsonify({
            'message': 'Video generation job created',
            'job_id': job_id,
            'status': 'pending',
            'queue_job_id': queue_job_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route("/jobs", methods=["GET"])
@require_authentication
def get_user_jobs():
    """Get user's video generation jobs with pagination."""
    try:
        user = g.current_user
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status')
        
        # Validate pagination limits
        per_page = min(per_page, 100)  # Max 100 items per page
        
        jobs = job_repository.get_user_jobs(
            user_id=user.id,
            page=page,
            per_page=per_page,
            status=status
        )
        
        return jsonify({
            'jobs': [job.model_dump() for job in jobs['items']],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': jobs['total'],
                'pages': jobs['pages']
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route("/jobs/<job_id>/share", methods=["POST"])
@require_authentication
def share_job(job_id: str):
    """Create shareable link for video job."""
    try:
        user = g.current_user
        
        # Get job and verify ownership
        job = job_repository.get_job_by_id(job_id)
        if not job or job.user_id != user.id:
            return jsonify({'error': 'Job not found'}), 404
        
        if job.status != 'succeeded':
            return jsonify({'error': 'Job not completed'}), 400
        
        # Generate share token
        share_token = str(uuid.uuid4())
        
        # Update job with share token
        job_repository.update_job_share_token(job_id, share_token)
        
        share_url = f"{request.host_url}share/{share_token}"
        
        return jsonify({
            'message': 'Share link created',
            'share_url': share_url,
            'share_token': share_token
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
```

### 5. Database Migration Strategy

#### Migration Plan
```python
# migrations/versions/001_add_user_system.py

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

def upgrade():
    # Create users table
    op.create_table('users',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('email', sa.String(255), nullable=False, unique=True),
        sa.Column('password_hash', sa.String(255), nullable=False),
        sa.Column('first_name', sa.String(100)),
        sa.Column('last_name', sa.String(100)),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('is_verified', sa.Boolean(), default=False),
        sa.Column('subscription_tier', sa.String(50), default='free'),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('last_login', sa.DateTime()),
    )
    
    # Create indexes
    op.create_index('idx_users_email', 'users', ['email'])
    op.create_index('idx_users_subscription', 'users', ['subscription_tier'])
    
    # Create user_sessions table
    op.create_table('user_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_token', sa.String(255), nullable=False, unique=True),
        sa.Column('expires_at', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('last_accessed', sa.DateTime(), default=sa.func.now()),
        sa.Column('ip_address', sa.String(45)),
        sa.Column('user_agent', sa.Text()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    )
    
    # Create indexes for sessions
    op.create_index('idx_sessions_token', 'user_sessions', ['session_token'])
    op.create_index('idx_sessions_user_id', 'user_sessions', ['user_id'])
    op.create_index('idx_sessions_expires', 'user_sessions', ['expires_at'])
    
    # Update video_jobs table
    op.add_column('video_jobs', sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column('video_jobs', sa.Column('visibility', sa.String(20), default='private'))
    op.add_column('video_jobs', sa.Column('shared_token', sa.String(255)))
    
    # Create foreign key constraint
    op.create_foreign_key('fk_video_jobs_user_id', 'video_jobs', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    
    # Create indexes for video_jobs
    op.create_index('idx_video_jobs_user_id', 'video_jobs', ['user_id'])
    op.create_index('idx_video_jobs_user_status', 'video_jobs', ['user_id', 'status'])
    op.create_index('idx_video_jobs_shared_token', 'video_jobs', ['shared_token'])

def downgrade():
    # Remove indexes
    op.drop_index('idx_video_jobs_shared_token', 'video_jobs')
    op.drop_index('idx_video_jobs_user_status', 'video_jobs')
    op.drop_index('idx_video_jobs_user_id', 'video_jobs')
    
    # Remove foreign key constraint
    op.drop_constraint('fk_video_jobs_user_id', 'video_jobs', type_='foreignkey')
    
    # Remove columns from video_jobs
    op.drop_column('video_jobs', 'shared_token')
    op.drop_column('video_jobs', 'visibility')
    op.drop_column('video_jobs', 'user_id')
    
    # Drop sessions table
    op.drop_table('user_sessions')
    
    # Drop users table
    op.drop_table('users')
```

### 6. Component Integration Architecture

#### System Architecture Diagram
```
┌─────────────────────────────────────────────────────────────────┐
│                        Load Balancer                            │
│                     (NGINX/HAProxy)                             │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────┴───────────────────────────────────────┐
│                    Flask Application                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Authentication │  │   Rate Limiter  │  │   API Gateway   │ │
│  │   Middleware    │  │   Middleware    │  │   Middleware    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   API Routes    │  │   Auth Routes   │  │  Admin Routes   │ │
│  │   (Video Gen)   │  │   (User Mgmt)   │  │   (Analytics)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────┬───────────────────────────────────────┘
                          │
        ┌─────────────────┴─────────────────┐
        │                                   │
        ▼                                   ▼
┌─────────────────┐                ┌─────────────────┐
│  Redis Queue    │                │   PostgreSQL    │
│  (Background    │                │   (User Data,   │
│   Jobs)         │                │   Video Jobs)   │
└─────────────────┘                └─────────────────┘
        │                                   │
        ▼                                   ▼
┌─────────────────┐                ┌─────────────────┐
│  Queue Workers  │                │   File Storage  │
│  (Video Gen     │                │   (Videos,      │
│   Processing)   │                │   Thumbnails)   │
└─────────────────┘                └─────────────────┘
        │
        ▼
┌─────────────────┐
│  Azure Sora API │
│  (Video Gen     │
│   Service)      │
└─────────────────┘
```

#### Data Flow Diagram
```
User Request → Authentication → Rate Limiting → Queue Job → Background Processing → File Storage → Response
     │              │                │              │              │                    │            │
     │              ▼                ▼              ▼              ▼                    ▼            │
     │         User Session     Rate Limit      Job Queue     Sora API          Video File       │
     │           Database        Counter         Redis        Processing        Storage           │
     │              │                │              │              │                    │            │
     └──────────────┴────────────────┴──────────────┴──────────────┴────────────────────┴────────────┘
```

### 7. Security Enhancements

#### Enhanced Security Configuration
```python
# src/config/security.py updates

class SecurityConfig:
    """Enhanced security configuration for multi-user system."""
    
    # Session security
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Password requirements
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_NUMBERS = True
    PASSWORD_REQUIRE_SPECIAL = True
    
    # Rate limiting
    RATE_LIMIT_STORAGE_URL = "redis://localhost:6379/1"
    
    # API security
    API_KEY_PREFIX_LENGTH = 8
    API_KEY_TOTAL_LENGTH = 32
    
    # File upload security
    ALLOWED_FILE_TYPES = {'.mp4', '.mov', '.avi'}
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    # CORS settings
    CORS_ORIGINS = ["http://localhost:3000", "https://yourdomain.com"]
    
    @staticmethod
    def validate_password(password: str) -> bool:
        """Validate password against security requirements."""
        if len(password) < SecurityConfig.PASSWORD_MIN_LENGTH:
            return False
        
        if SecurityConfig.PASSWORD_REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
            return False
        
        if SecurityConfig.PASSWORD_REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
            return False
        
        if SecurityConfig.PASSWORD_REQUIRE_NUMBERS and not re.search(r'\d', password):
            return False
        
        if SecurityConfig.PASSWORD_REQUIRE_SPECIAL and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False
        
        return True
    
    @staticmethod
    def generate_api_key() -> tuple[str, str]:
        """Generate API key with prefix and full key."""
        import secrets
        import string
        
        # Generate random prefix
        prefix = ''.join(secrets.choice(string.ascii_letters + string.digits) 
                        for _ in range(SecurityConfig.API_KEY_PREFIX_LENGTH))
        
        # Generate full key
        full_key = ''.join(secrets.choice(string.ascii_letters + string.digits) 
                          for _ in range(SecurityConfig.API_KEY_TOTAL_LENGTH))
        
        return f"sk-{prefix}", f"sk-{full_key}"
```

### 8. Monitoring and Analytics

#### User Analytics
```python
# src/monitoring/analytics.py

class UserAnalytics:
    """User behavior and system analytics."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/2"):
        self.redis_conn = redis.from_url(redis_url)
        self.logger = logging.getLogger(__name__)
    
    def track_user_action(self, user_id: str, action: str, metadata: dict = None):
        """Track user action for analytics."""
        event = {
            'user_id': user_id,
            'action': action,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        # Store in Redis for real-time analytics
        self.redis_conn.lpush(f"user_actions:{user_id}", json.dumps(event))
        self.redis_conn.expire(f"user_actions:{user_id}", 86400 * 30)  # 30 days
        
        # Aggregate metrics
        self._update_metrics(user_id, action)
    
    def _update_metrics(self, user_id: str, action: str):
        """Update aggregated metrics."""
        today = datetime.now().strftime('%Y-%m-%d')
        
        # Daily metrics
        self.redis_conn.hincrby(f"daily_metrics:{today}", action, 1)
        self.redis_conn.hincrby(f"daily_user_metrics:{user_id}:{today}", action, 1)
        
        # Global counters
        self.redis_conn.hincrby("global_metrics", action, 1)
        self.redis_conn.hincrby(f"user_metrics:{user_id}", action, 1)
    
    def get_user_analytics(self, user_id: str) -> dict:
        """Get analytics for specific user."""
        return {
            'total_actions': self.redis_conn.hgetall(f"user_metrics:{user_id}"),
            'recent_actions': [
                json.loads(action) 
                for action in self.redis_conn.lrange(f"user_actions:{user_id}", 0, 10)
            ]
        }
    
    def get_system_analytics(self) -> dict:
        """Get system-wide analytics."""
        today = datetime.now().strftime('%Y-%m-%d')
        
        return {
            'global_metrics': self.redis_conn.hgetall("global_metrics"),
            'today_metrics': self.redis_conn.hgetall(f"daily_metrics:{today}"),
            'active_users': self.redis_conn.scard("active_users_today")
        }
```

## Implementation Roadmap

### Phase 1: Core Multi-User Foundation (Weeks 1-2)
- [ ] User authentication system
- [ ] Database schema migration
- [ ] Session management
- [ ] Basic rate limiting
- [ ] User-specific job isolation

### Phase 2: Background Processing (Weeks 3-4)
- [ ] Redis queue integration
- [ ] Background workers
- [ ] Job status tracking
- [ ] Concurrent job limits
- [ ] Priority queues

### Phase 3: Advanced Features (Weeks 5-6)
- [ ] Advanced rate limiting
- [ ] Quota management
- [ ] File sharing system
- [ ] User analytics
- [ ] Admin dashboard

### Phase 4: Production Hardening (Weeks 7-8)
- [ ] Security enhancements
- [ ] Performance optimization
- [ ] Monitoring and alerting
- [ ] Load testing
- [ ] Documentation

## Resource Requirements

### Infrastructure
- **Database**: PostgreSQL 14+ with connection pooling
- **Cache/Queue**: Redis 6+ with persistence
- **Storage**: Cloud storage (S3/Azure Blob) for video files
- **Monitoring**: Prometheus + Grafana for metrics
- **Load Balancer**: NGINX or cloud load balancer

### Development
- **Backend**: Current Flask stack + Redis Queue
- **Frontend**: React/Vue.js for user dashboard
- **API**: RESTful API with OpenAPI documentation
- **Testing**: Extended test suite for multi-user scenarios

## Success Metrics

### Performance Targets
- **Concurrent Users**: 10+ active users simultaneously
- **Response Time**: < 500ms for API endpoints
- **Job Processing**: < 30 seconds queue time
- **System Uptime**: 99.9% availability

### User Experience
- **Registration**: < 2 minutes to create account
- **Job Submission**: < 5 seconds to queue job
- **Status Updates**: Real-time job progress
- **File Access**: < 3 seconds to load video

This architecture provides a robust foundation for scaling the Sora POC into a production-ready multi-user system while maintaining the existing code quality and patterns.