# Multi-User Sora Video Generation - Developer Guide

This comprehensive developer guide provides everything needed to set up, develop, test, and contribute to the multi-user Azure OpenAI Sora video generation system supporting 10+ concurrent users.

## Table of Contents

- [Development Environment Setup](#development-environment-setup)
- [Architecture Overview](#architecture-overview)
- [Development Workflow](#development-workflow)
- [Testing Procedures](#testing-procedures)
- [Multi-User Testing](#multi-user-testing)
- [Code Examples](#code-examples)
- [Configuration Reference](#configuration-reference)
- [API Integration Patterns](#api-integration-patterns)
- [Performance Testing](#performance-testing)
- [Debugging Guide](#debugging-guide)
- [Contributing Guidelines](#contributing-guidelines)
- [Best Practices](#best-practices)

## Development Environment Setup

### Prerequisites

**System Requirements:**
- **OS**: macOS, Linux, or Windows with WSL2
- **Python**: 3.9+ (recommended: 3.12)
- **Node.js**: 18+ (for frontend development and testing tools)
- **Docker**: 24.0+ and Docker Compose 2.20+
- **Memory**: 16GB RAM minimum (32GB recommended)
- **Storage**: 50GB free space

**Required Accounts:**
- Azure OpenAI account with Sora access
- Redis (for local development, or use Docker)
- PostgreSQL (for production-like testing, or use Docker)

### Initial Setup

1. **Clone and Setup Repository:**
   ```bash
   # Clone the repository
   git clone <repository-url>
   cd sora-poc
   
   # Create and activate virtual environment
   uv venv
   source .venv/bin/activate  # On Unix/macOS
   # .venv\Scripts\activate  # On Windows
   
   # Install dependencies
   uv sync
   
   # Install development tools
   uv add --dev pytest-asyncio locust selenium beautifulsoup4
   ```

2. **Environment Configuration:**
   ```bash
   # Create development environment file
   cp .env.example .env.development
   
   # Edit .env.development with your settings
   nano .env.development
   ```

   **Development Environment Variables:**
   ```bash
   # Development Configuration
   FLASK_ENV=development
   DEBUG=true
   SECRET_KEY=dev-secret-key-change-in-production
   
   # Database (SQLite for quick development)
   DATABASE_URL=sqlite:///sora_dev.db
   
   # Redis (local instance or Docker)
   CELERY_BROKER_URL=redis://localhost:6379/0
   CELERY_RESULT_BACKEND=redis://localhost:6379/0
   
   # Azure OpenAI (use your development credentials)
   AZURE_OPENAI_API_KEY=your-development-api-key
   AZURE_OPENAI_ENDPOINT=https://your-dev-resource.openai.azure.com/
   AZURE_OPENAI_API_VERSION=2024-02-15-preview
   AZURE_OPENAI_SORA_DEPLOYMENT=sora
   
   # Development Settings
   MAX_CONCURRENT_JOBS_PER_SESSION=3
   FILE_CLEANUP_ENABLED=false
   LOG_LEVEL=DEBUG
   RATE_LIMIT_ENABLED=false
   
   # Testing Configuration
   TESTING_MODE=true
   MOCK_AZURE_API=false
   TEST_VIDEO_GENERATION=true
   ```

3. **Database Setup:**
   ```bash
   # Initialize database migrations
   uv run flask --app src.main:create_app db init
   
   # Create initial migration
   uv run flask --app src.main:create_app db migrate -m "Initial migration"
   
   # Apply migrations
   uv run flask --app src.main:create_app db upgrade
   ```

4. **Install Development Services:**
   ```bash
   # Start Redis and PostgreSQL for development
   docker-compose -f docker-compose.dev.yml up -d redis postgres
   
   # Or install locally:
   # macOS:
   brew install redis postgresql
   brew services start redis
   brew services start postgresql
   
   # Ubuntu:
   sudo apt-get install redis-server postgresql postgresql-contrib
   sudo systemctl start redis-server
   sudo systemctl start postgresql
   ```

### Development Docker Setup

Create `docker-compose.dev.yml` for development services:

```yaml
version: '3.8'

services:
  # Development Redis
  redis:
    image: redis:7.2-alpine
    container_name: sora-dev-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --save 60 1 --loglevel notice

  # Development PostgreSQL
  postgres:
    image: postgres:15.4-alpine
    container_name: sora-dev-postgres
    environment:
      - POSTGRES_DB=sora_development
      - POSTGRES_USER=sora_dev
      - POSTGRES_PASSWORD=dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data

  # Development Celery Worker
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: sora-dev-worker
    command: celery -A src.job_queue.celery_app worker --loglevel=debug
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - DATABASE_URL=************************************************/sora_development
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    volumes:
      - .:/app
      - ./uploads:/app/uploads
    depends_on:
      - redis
      - postgres

  # Development Flower (Celery monitoring)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: sora-dev-flower
    command: celery -A src.job_queue.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      - redis

volumes:
  redis_dev_data:
  postgres_dev_data:
```

### IDE Configuration

#### VS Code Setup

Create `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["src"],
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "ruff",
    "editor.formatOnSave": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        "htmlcov": true,
        "*.egg-info": true
    },
    "python.testing.autoTestDiscoverOnSaveEnabled": true
}
```

Create `.vscode/launch.json`:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Flask App",
            "type": "python",
            "request": "launch",
            "program": "src/main.py",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            },
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Celery Worker",
            "type": "python",
            "request": "launch",
            "module": "celery",
            "args": ["-A", "src.job_queue.celery_app", "worker", "--loglevel=debug"],
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Pytest",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": ["src/", "-v"],
            "console": "integratedTerminal",
            "justMyCode": false
        }
    ]
}
```

## Architecture Overview

### Multi-User Architecture Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Client Layer                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Browser 1     │   Browser 2     │   Browser N             │
│   (Session A)   │   (Session B)   │   (Session X)           │
│   WebSocket +   │   WebSocket +   │   WebSocket +           │
│   HTTP Client   │   HTTP Client   │   HTTP Client           │
└─────────────────┴─────────────────┴─────────────────────────┘
         │                  │                  │
         ▼                  ▼                  ▼
┌─────────────────────────────────────────────────────────────┐
│                Application Layer                             │
├─────────────────┬─────────────────┬─────────────────────────┤
│  Session Mgmt   │  WebSocket Hub  │  API Endpoints          │
│  - User isolation│ - Real-time    │ - Video generation      │
│  - Rate limiting│   updates       │ - Queue management      │
│  - Session auth │ - Job progress  │ - Health checks         │
└─────────────────┴─────────────────┴─────────────────────────┘
         │                  │                  │
         ▼                  ▼                  ▼
┌─────────────────────────────────────────────────────────────┐
│                Queue & Processing Layer                      │
├─────────────────┬─────────────────┬─────────────────────────┤
│  Job Queue      │  Worker Pool    │  Rate Limiter           │
│  - Fair queuing │ - Scalable      │ - Global limits         │
│  - Priorities   │ - Auto-scaling  │ - Per-session limits    │
│  - Persistence  │ - Health checks │ - Azure API compliance  │
└─────────────────┴─────────────────┴─────────────────────────┘
         │                  │                  │
         ▼                  ▼                  ▼
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Database      │   Message Bus   │   External APIs         │
│   - Job tracking│   - Redis       │   - Azure OpenAI       │
│   - Sessions    │   - Pub/Sub     │   - File Storage        │
│   - Persistence │   - Rate limits │   - Monitoring          │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### Key Design Patterns

1. **Session-Based User Isolation**
   - Cryptographically secure session IDs
   - Per-session resource limits
   - Data isolation without authentication

2. **Event-Driven Architecture**
   - WebSocket real-time updates
   - Redis pub/sub for worker communication
   - Celery task queues for async processing

3. **Distributed Rate Limiting**
   - Redis-based global rate limiter
   - Sliding window algorithm
   - Fair resource allocation

4. **Vertical Slice Architecture**
   - Feature-based code organization
   - Co-located tests and business logic
   - Minimal cross-feature dependencies

## Development Workflow

### Starting Development Environment

```bash
# Terminal 1: Start infrastructure services
docker-compose -f docker-compose.dev.yml up redis postgres

# Terminal 2: Start Flask application
uv run python src/main.py

# Terminal 3: Start Celery worker
uv run celery -A src.job_queue.celery_app worker --loglevel=debug

# Terminal 4: Start Flower monitoring (optional)
uv run celery -A src.job_queue.celery_app flower --port=5555

# Terminal 5: Run tests continuously
uv run pytest-watch -- src/ -v
```

### Development Commands

```bash
# Code quality checks
uv run ruff format .                    # Format code
uv run ruff check .                     # Lint code
uv run mypy src/                        # Type checking

# Testing
uv run pytest src/ -v                   # Run all tests
uv run pytest src/ --cov=src            # Run with coverage
uv run pytest -m "unit" -v              # Run unit tests only
uv run pytest -m "integration" -v       # Run integration tests only

# Database management
uv run flask --app src.main:create_app db migrate -m "Description"
uv run flask --app src.main:create_app db upgrade
uv run flask --app src.main:create_app db downgrade

# Development workflow (run before committing)
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest

# Start development server with hot reload
uv run flask --app src.main:create_app run --debug --host=0.0.0.0 --port=5001
```

### Git Workflow

```bash
# Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add your feature description

- Specific change 1
- Specific change 2
- Any breaking changes noted"

# Push and create pull request
git push -u origin feature/your-feature-name
# Create PR via GitHub/GitLab UI

# After review approval, merge via platform
# Delete feature branch
git checkout develop
git pull origin develop
git branch -d feature/your-feature-name
```

## Testing Procedures

### Test Structure

```
src/
├── api/
│   ├── routes.py
│   ├── job_repository.py
│   └── tests/
│       ├── test_routes.py
│       ├── test_job_repository.py
│       └── conftest.py
├── job_queue/
│   ├── tasks.py
│   ├── manager.py
│   └── tests/
│       ├── test_tasks.py
│       ├── test_manager.py
│       └── conftest.py
└── realtime/
    ├── websocket.py
    ├── broadcaster.py
    └── tests/
        ├── test_websocket.py
        ├── test_broadcaster.py
        └── conftest.py
```

### Unit Testing Examples

1. **Session Management Tests:**
   ```python
   # src/session/tests/test_manager.py
   import pytest
   from unittest.mock import Mock, patch
   from src.session.manager import SessionManager
   from src.core.models import UserSession
   
   @pytest.fixture
   def session_manager():
       return SessionManager()
   
   def test_create_session(session_manager):
       """Test session creation with unique ID."""
       session = session_manager.create_session()
       
       assert isinstance(session, UserSession)
       assert session.session_id is not None
       assert len(session.session_id) == 64  # 32 bytes hex
       assert session.job_count == 0
   
   def test_session_isolation(session_manager):
       """Test that different sessions are isolated."""
       session1 = session_manager.create_session()
       session2 = session_manager.create_session()
       
       assert session1.session_id != session2.session_id
       
       # Simulate job submission for session1
       session_manager.increment_job_count(session1.session_id)
       
       # Session2 should be unaffected
       session2_updated = session_manager.get_session(session2.session_id)
       assert session2_updated.job_count == 0
   
   @pytest.mark.asyncio
   async def test_session_cleanup(session_manager):
       """Test automatic session cleanup."""
       # Create expired session
       with patch('src.session.manager.datetime') as mock_datetime:
           mock_datetime.utcnow.return_value = datetime(2024, 1, 1, 12, 0, 0)
           session = session_manager.create_session()
           
           # Move time forward 5 hours
           mock_datetime.utcnow.return_value = datetime(2024, 1, 1, 17, 0, 0)
           
           # Cleanup should remove expired session
           cleaned = await session_manager.cleanup_expired_sessions()
           assert cleaned == 1
           assert session_manager.get_session(session.session_id) is None
   ```

2. **Queue Management Tests:**
   ```python
   # src/job_queue/tests/test_manager.py
   import pytest
   from unittest.mock import AsyncMock, patch
   from src.job_queue.manager import QueueManager
   from src.core.models import QueuedVideoJob, QueueStatus
   
   @pytest.fixture
   def queue_manager():
       return QueueManager()
   
   @pytest.mark.asyncio
   async def test_job_submission(queue_manager):
       """Test job submission and queue position assignment."""
       job1 = QueuedVideoJob(
           session_id="session1",
           prompt="Test video 1",
           duration=5
       )
       job2 = QueuedVideoJob(
           session_id="session2", 
           prompt="Test video 2",
           duration=8
       )
       
       # Submit jobs
       queued_job1 = await queue_manager.submit_job(job1)
       queued_job2 = await queue_manager.submit_job(job2)
       
       # Check queue positions
       assert queued_job1.queue_position == 1
       assert queued_job2.queue_position == 2
   
   @pytest.mark.asyncio
   async def test_fair_queuing(queue_manager):
       """Test fair queuing algorithm with session limits."""
       # Submit multiple jobs from same session
       jobs = []
       for i in range(5):
           job = QueuedVideoJob(
               session_id="session1",
               prompt=f"Test video {i+1}",
               duration=5
           )
           queued_job = await queue_manager.submit_job(job)
           jobs.append(queued_job)
       
       # Should respect per-session limits
       active_jobs = [j for j in jobs if j.status == "processing"]
       assert len(active_jobs) <= 3  # MAX_CONCURRENT_JOBS_PER_SESSION
   
   @pytest.mark.asyncio
   async def test_priority_queuing(queue_manager):
       """Test priority-based job ordering."""
       # Submit normal priority job
       normal_job = QueuedVideoJob(
           session_id="session1",
           prompt="Normal priority",
           priority=0
       )
       
       # Submit high priority job
       high_priority_job = QueuedVideoJob(
           session_id="session2",
           prompt="High priority",
           priority=10
       )
       
       await queue_manager.submit_job(normal_job)
       await queue_manager.submit_job(high_priority_job)
       
       # High priority should be processed first
       next_job = await queue_manager.get_next_job()
       assert next_job.priority == 10
   ```

3. **WebSocket Communication Tests:**
   ```python
   # src/realtime/tests/test_websocket.py
   import pytest
   from unittest.mock import Mock, patch
   from src.realtime.websocket import socketio
   from src.main import create_app
   
   @pytest.fixture
   def app():
       app = create_app(testing=True)
       return app
   
   @pytest.fixture
   def client(app):
       return socketio.test_client(app)
   
   def test_websocket_connection(client):
       """Test WebSocket connection and session room joining."""
       with patch('src.session.manager.get_session_id', return_value='test-session-123'):
           # Connect to WebSocket
           received = client.get_received()
           
           # Should receive connection confirmation
           assert len(received) == 1
           assert received[0]['name'] == 'connected'
           assert received[0]['args'][0]['data']['session_id'] == 'test-session-123'
   
   def test_job_subscription(client):
       """Test job update subscription and isolation."""
       with patch('src.session.manager.get_session_id', return_value='test-session-123'):
           # Subscribe to job updates
           client.emit('subscribe_job_updates', {'job_id': 'job-456'})
           
           # Should receive subscription confirmation
           received = client.get_received()
           subscription_msg = next(
               (msg for msg in received if msg['name'] == 'subscription_confirmed'), 
               None
           )
           assert subscription_msg is not None
           assert subscription_msg['args'][0]['job_id'] == 'job-456'
   
   def test_session_isolation(app):
       """Test that WebSocket messages are isolated by session."""
       client1 = socketio.test_client(app)
       client2 = socketio.test_client(app)
       
       with patch('src.session.manager.get_session_id', side_effect=['session-1', 'session-2']):
           # Both clients connect
           client1.get_received()  # Clear connection messages
           client2.get_received()
           
           # Broadcast job update for session-1 only
           socketio.emit('job_status_update', {
               'job_id': 'job-123',
               'status': 'completed'
           }, room='session_session-1')
           
           # Only client1 should receive the message
           received1 = client1.get_received()
           received2 = client2.get_received()
           
           assert len(received1) == 1
           assert len(received2) == 0
   ```

### Integration Testing

1. **End-to-End Video Generation Test:**
   ```python
   # src/tests/test_integration_e2e.py
   import pytest
   import asyncio
   from unittest.mock import patch, Mock
   from src.main import create_app
   from src.job_queue.tasks import process_video_generation
   
   @pytest.fixture
   def app():
       app = create_app(testing=True)
       with app.app_context():
           yield app
   
   @pytest.fixture
   def client(app):
       return app.test_client()
   
   @pytest.mark.integration
   @patch('src.features.sora_integration.client.SoraClient.create_video_job')
   async def test_complete_video_generation_workflow(mock_sora_client, client):
       """Test complete workflow from submission to completion."""
       # Mock Azure API response
       mock_sora_client.return_value = Mock(
           status="succeeded",
           file_path="/tmp/test_video.mp4",
           download_url="https://example.com/video.mp4"
       )
       
       # Create session
       session_response = client.get('/api/session/create')
       assert session_response.status_code == 200
       session_data = session_response.get_json()
       session_id = session_data['data']['session_id']
       
       # Submit video generation job
       job_response = client.post('/api/generate', 
           json={
               'prompt': 'A beautiful sunset over mountains',
               'duration': 5,
               'width': 1920,
               'height': 1080
           },
           headers={'X-Session-ID': session_id}
       )
       
       assert job_response.status_code == 200
       job_data = job_response.get_json()
       job_id = job_data['data']['job_id']
       
       # Process job (simulate worker)
       result = await process_video_generation.apply_async(
           args=[session_id, job_id, {
               'prompt': 'A beautiful sunset over mountains',
               'duration': 5,
               'width': 1920,
               'height': 1080
           }]
       )
       
       assert result.successful()
       
       # Check job status
       status_response = client.get(f'/api/status/{job_id}',
           headers={'X-Session-ID': session_id}
       )
       
       assert status_response.status_code == 200
       status_data = status_response.get_json()
       assert status_data['data']['status'] == 'succeeded'
   
   @pytest.mark.integration
   async def test_queue_fairness_with_multiple_users(client):
       """Test queue fairness with multiple concurrent users."""
       # Create multiple sessions
       sessions = []
       for i in range(5):
           response = client.get('/api/session/create')
           session_data = response.get_json()
           sessions.append(session_data['data']['session_id'])
       
       # Submit jobs from all sessions
       jobs = []
       for i, session_id in enumerate(sessions):
           job_response = client.post('/api/generate',
               json={
                   'prompt': f'Test video from user {i+1}',
                   'duration': 5
               },
               headers={'X-Session-ID': session_id}
           )
           
           job_data = job_response.get_json()
           jobs.append((job_data['data']['job_id'], session_id))
       
       # Check queue fairness
       queue_response = client.get('/api/queue/status',
           headers={'X-Session-ID': sessions[0]}
       )
       
       queue_data = queue_response.get_json()
       assert queue_data['data']['global_queue']['total_jobs'] == 5
   ```

## Multi-User Testing

### Load Testing with Locust

Create `tests/load_testing/locustfile.py`:

```python
from locust import HttpUser, task, between
import random
import json
import time

class SoraVideoUser(HttpUser):
    wait_time = between(1, 5)
    
    def on_start(self):
        """Called when a user starts - create session."""
        response = self.client.get("/api/session/create")
        if response.status_code == 200:
            self.session_id = response.json()["data"]["session_id"]
            self.client.headers.update({"X-Session-ID": self.session_id})
        else:
            self.session_id = None
    
    @task(3)
    def generate_video(self):
        """Submit video generation job."""
        if not self.session_id:
            return
        
        prompts = [
            "A majestic eagle soaring over mountain peaks",
            "Waves crashing on a rocky coastline at sunset",
            "A bustling city street with neon lights at night",
            "A peaceful forest with sunlight filtering through trees",
            "A lightning storm over a vast desert landscape"
        ]
        
        payload = {
            "prompt": random.choice(prompts),
            "duration": random.randint(3, 10),
            "width": random.choice([1280, 1920]),
            "height": random.choice([720, 1080])
        }
        
        with self.client.post("/api/generate", 
                             json=payload, 
                             catch_response=True) as response:
            if response.status_code == 200:
                job_data = response.json()
                job_id = job_data["data"]["job_id"]
                
                # Store job for status checking
                if not hasattr(self, 'active_jobs'):
                    self.active_jobs = []
                self.active_jobs.append(job_id)
                
                response.success()
            else:
                response.failure(f"Video generation failed: {response.text}")
    
    @task(2)
    def check_job_status(self):
        """Check status of submitted jobs."""
        if not hasattr(self, 'active_jobs') or not self.active_jobs:
            return
        
        job_id = random.choice(self.active_jobs)
        
        with self.client.get(f"/api/status/{job_id}", 
                            catch_response=True) as response:
            if response.status_code == 200:
                status_data = response.json()
                status = status_data["data"]["status"]
                
                if status in ["succeeded", "failed"]:
                    # Remove completed job from active list
                    self.active_jobs.remove(job_id)
                
                response.success()
            else:
                response.failure(f"Status check failed: {response.text}")
    
    @task(1)
    def check_queue_status(self):
        """Check queue status."""
        if not self.session_id:
            return
        
        with self.client.get("/api/queue/status", 
                            catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Queue status failed: {response.text}")
    
    @task(1)
    def check_health(self):
        """Check system health."""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.text}")

class WebSocketUser(HttpUser):
    """User that tests WebSocket connections."""
    wait_time = between(2, 8)
    
    def on_start(self):
        """Setup WebSocket connection."""
        # Note: Locust doesn't natively support WebSocket
        # Use socketio client for WebSocket testing
        pass
    
    @task
    def simulate_realtime_updates(self):
        """Simulate receiving real-time updates."""
        # This would require additional WebSocket testing framework
        # Consider using separate WebSocket testing tools
        pass
```

### Multi-User Test Scripts

1. **Concurrent User Simulation:**
   ```python
   # tests/multi_user/test_concurrent_users.py
   import asyncio
   import aiohttp
   import pytest
   from typing import List
   import time
   
   class ConcurrentUserSimulator:
       def __init__(self, base_url: str = "http://localhost:5001"):
           self.base_url = base_url
           self.sessions = []
       
       async def create_user_session(self, session: aiohttp.ClientSession) -> str:
           """Create a new user session."""
           async with session.get(f"{self.base_url}/api/session/create") as resp:
               data = await resp.json()
               return data["data"]["session_id"]
       
       async def submit_video_job(self, session: aiohttp.ClientSession, 
                                 session_id: str, prompt: str) -> dict:
           """Submit a video generation job."""
           headers = {"X-Session-ID": session_id}
           payload = {
               "prompt": prompt,
               "duration": 5,
               "width": 1920,
               "height": 1080
           }
           
           async with session.post(f"{self.base_url}/api/generate",
                                  json=payload, headers=headers) as resp:
               return await resp.json()
       
       async def simulate_user(self, user_id: int, num_jobs: int = 3) -> List[dict]:
           """Simulate a single user's activity."""
           results = []
           
           async with aiohttp.ClientSession() as session:
               # Create session
               session_id = await self.create_user_session(session)
               
               # Submit multiple jobs
               for job_num in range(num_jobs):
                   prompt = f"User {user_id} video {job_num + 1}: A beautiful landscape"
                   
                   try:
                       job_result = await self.submit_video_job(session, session_id, prompt)
                       results.append({
                           "user_id": user_id,
                           "job_num": job_num,
                           "success": job_result.get("success", False),
                           "job_id": job_result.get("data", {}).get("job_id"),
                           "queue_position": job_result.get("data", {}).get("queue_position")
                       })
                   except Exception as e:
                       results.append({
                           "user_id": user_id,
                           "job_num": job_num,
                           "success": False,
                           "error": str(e)
                       })
                   
                   # Small delay between jobs
                   await asyncio.sleep(0.5)
           
           return results
       
       async def run_concurrent_test(self, num_users: int = 15, 
                                   jobs_per_user: int = 2) -> dict:
           """Run concurrent user test."""
           start_time = time.time()
           
           # Create tasks for all users
           tasks = [
               self.simulate_user(user_id, jobs_per_user)
               for user_id in range(num_users)
           ]
           
           # Run all users concurrently
           results = await asyncio.gather(*tasks, return_exceptions=True)
           
           end_time = time.time()
           
           # Analyze results
           total_jobs = 0
           successful_jobs = 0
           failed_jobs = 0
           
           for user_results in results:
               if isinstance(user_results, Exception):
                   failed_jobs += jobs_per_user
                   continue
               
               for job_result in user_results:
                   total_jobs += 1
                   if job_result.get("success"):
                       successful_jobs += 1
                   else:
                       failed_jobs += 1
           
           return {
               "num_users": num_users,
               "jobs_per_user": jobs_per_user,
               "total_jobs": total_jobs,
               "successful_jobs": successful_jobs,
               "failed_jobs": failed_jobs,
               "success_rate": successful_jobs / total_jobs if total_jobs > 0 else 0,
               "duration_seconds": end_time - start_time,
               "results": results
           }
   
   @pytest.mark.asyncio
   @pytest.mark.slow
   async def test_15_concurrent_users():
       """Test system with 15 concurrent users."""
       simulator = ConcurrentUserSimulator()
       results = await simulator.run_concurrent_test(num_users=15, jobs_per_user=2)
       
       # Assert minimum performance requirements
       assert results["success_rate"] >= 0.95, f"Success rate too low: {results['success_rate']}"
       assert results["duration_seconds"] < 300, f"Test took too long: {results['duration_seconds']}s"
       
       print(f"Test Results:")
       print(f"- Users: {results['num_users']}")
       print(f"- Total jobs: {results['total_jobs']}")
       print(f"- Success rate: {results['success_rate']:.2%}")
       print(f"- Duration: {results['duration_seconds']:.1f}s")
   
   @pytest.mark.asyncio
   async def test_queue_fairness():
       """Test that queue fairly distributes jobs across users."""
       simulator = ConcurrentUserSimulator()
       
       # Run with fewer users but more jobs per user
       results = await simulator.run_concurrent_test(num_users=5, jobs_per_user=4)
       
       # Analyze queue positions for fairness
       queue_positions = []
       for user_results in results["results"]:
           if not isinstance(user_results, Exception):
               for job_result in user_results:
                   if job_result.get("queue_position"):
                       queue_positions.append(job_result["queue_position"])
       
       # Check that queue positions are distributed fairly
       # (not all jobs from one user get processed first)
       unique_positions = len(set(queue_positions))
       assert unique_positions >= len(queue_positions) * 0.8, "Queue not fair - positions too clustered"
   ```

2. **WebSocket Stress Testing:**
   ```python
   # tests/multi_user/test_websocket_stress.py
   import asyncio
   import socketio
   import pytest
   from typing import List
   import time
   
   class WebSocketStressTester:
       def __init__(self, base_url: str = "http://localhost:5001"):
           self.base_url = base_url
           self.clients = []
           self.message_counts = {}
       
       async def create_websocket_client(self, user_id: int) -> socketio.AsyncClient:
           """Create and connect a WebSocket client."""
           client = socketio.AsyncClient()
           
           @client.event
           async def connect():
               print(f"User {user_id} connected")
           
           @client.event
           async def job_status_update(data):
               if user_id not in self.message_counts:
                   self.message_counts[user_id] = 0
               self.message_counts[user_id] += 1
           
           @client.event
           async def disconnect():
               print(f"User {user_id} disconnected")
           
           # Connect with session authentication
           await client.connect(self.base_url, 
                               auth={"session_id": f"test-session-{user_id}"})
           return client
       
       async def stress_test_websockets(self, num_connections: int = 50) -> dict:
           """Stress test WebSocket connections."""
           start_time = time.time()
           
           # Create multiple WebSocket connections
           tasks = []
           for i in range(num_connections):
               task = asyncio.create_task(self.create_websocket_client(i))
               tasks.append(task)
           
           try:
               clients = await asyncio.gather(*tasks)
               self.clients = clients
               
               # Keep connections alive for a period
               await asyncio.sleep(30)
               
               # Simulate job updates
               for client in clients[:10]:  # Simulate updates for first 10 clients
                   await client.emit('subscribe_job_updates', 
                                   {'job_id': f'test-job-{clients.index(client)}'})
               
               await asyncio.sleep(10)
               
               # Disconnect all clients
               disconnect_tasks = [client.disconnect() for client in clients]
               await asyncio.gather(*disconnect_tasks, return_exceptions=True)
               
           except Exception as e:
               print(f"Error during stress test: {e}")
               return {"success": False, "error": str(e)}
           
           end_time = time.time()
           
           return {
               "success": True,
               "num_connections": num_connections,
               "duration_seconds": end_time - start_time,
               "message_counts": self.message_counts,
               "total_messages": sum(self.message_counts.values())
           }
   
   @pytest.mark.asyncio
   @pytest.mark.slow
   async def test_websocket_stress():
       """Test WebSocket connections under stress."""
       tester = WebSocketStressTester()
       results = await tester.stress_test_websockets(num_connections=25)
       
       assert results["success"], f"Stress test failed: {results.get('error')}"
       assert results["duration_seconds"] < 60, "Stress test took too long"
       
       print(f"WebSocket Stress Test Results:")
       print(f"- Connections: {results['num_connections']}")
       print(f"- Duration: {results['duration_seconds']:.1f}s")
       print(f"- Total messages: {results['total_messages']}")
   ```

### Running Load Tests

```bash
# Install load testing dependencies
uv add --dev locust websocket-client

# Run Locust load test
uv run locust -f tests/load_testing/locustfile.py --host=http://localhost:5001

# Run concurrent user tests
uv run pytest tests/multi_user/test_concurrent_users.py -v -s

# Run WebSocket stress tests
uv run pytest tests/multi_user/test_websocket_stress.py -v -s

# Run full performance test suite
uv run pytest -m "slow" -v --tb=short
```

## Configuration Reference

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `FLASK_ENV` | `development` | Application environment |
| `DEBUG` | `false` | Enable debug mode |
| `SECRET_KEY` | Required | Flask secret key |
| `DATABASE_URL` | `sqlite:///sora.db` | Database connection string |
| `CELERY_BROKER_URL` | `redis://localhost:6379/0` | Celery message broker |
| `CELERY_RESULT_BACKEND` | `redis://localhost:6379/0` | Celery result backend |
| `AZURE_OPENAI_API_KEY` | Required | Azure OpenAI API key |
| `AZURE_OPENAI_ENDPOINT` | Required | Azure OpenAI endpoint URL |
| `AZURE_OPENAI_API_VERSION` | `2024-02-15-preview` | Azure API version |
| `AZURE_OPENAI_SORA_DEPLOYMENT` | `sora` | Sora model deployment name |
| `MAX_CONCURRENT_JOBS_PER_SESSION` | `5` | Per-session job limit |
| `GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND` | `10` | Global rate limit |
| `RATE_LIMIT_ENABLED` | `true` | Enable rate limiting |
| `FILE_CLEANUP_ENABLED` | `true` | Enable automatic file cleanup |
| `FILE_MAX_AGE_HOURS` | `24` | File cleanup age threshold |
| `LOG_LEVEL` | `INFO` | Logging level |
| `HEALTH_CHECK_ENABLED` | `true` | Enable health checks |
| `METRICS_ENABLED` | `true` | Enable metrics collection |

### Configuration Classes

```python
# src/config/environments.py
class DevelopmentConfig(BaseConfig):
    """Development environment configuration."""
    DEBUG = True
    TESTING = False
    
    # Database
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///sora_dev.db')
    
    # Security (relaxed for development)
    WTF_CSRF_ENABLED = False
    SESSION_COOKIE_SECURE = False
    
    # Rate limiting (disabled for development)
    RATE_LIMIT_ENABLED = False
    
    # File cleanup (disabled for development)
    FILE_CLEANUP_ENABLED = False
    
    # Logging
    LOG_LEVEL = 'DEBUG'

class TestingConfig(BaseConfig):
    """Testing environment configuration."""
    TESTING = True
    DEBUG = True
    
    # In-memory database for fast tests
    DATABASE_URL = 'sqlite:///:memory:'
    
    # Disable external services
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True
    
    # Mock Azure API for tests
    MOCK_AZURE_API = True
    
    # Disable rate limiting for tests
    RATE_LIMIT_ENABLED = False

class ProductionConfig(BaseConfig):
    """Production environment configuration."""
    DEBUG = False
    TESTING = False
    
    # Database
    DATABASE_URL = os.getenv('DATABASE_URL')
    if not DATABASE_URL:
        raise ValueError("DATABASE_URL environment variable required")
    
    # Security
    WTF_CSRF_ENABLED = True
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    
    # Rate limiting
    RATE_LIMIT_ENABLED = True
    
    # Logging
    LOG_LEVEL = 'INFO'
    
    # Health checks
    HEALTH_CHECK_ENABLED = True
```

## API Integration Patterns

### Azure OpenAI Integration

```python
# src/features/sora_integration/client.py
import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any
from src.rate_limiting.limiter import GlobalRateLimiter
from src.core.models import VideoGenerationResult

class SoraClient:
    """Production-ready Azure OpenAI Sora client with multi-user support."""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_key = config['AZURE_OPENAI_API_KEY']
        self.endpoint = config['AZURE_OPENAI_ENDPOINT']
        self.api_version = config['AZURE_OPENAI_API_VERSION']
        self.deployment = config['AZURE_OPENAI_SORA_DEPLOYMENT']
        
        # Rate limiting
        self.rate_limiter = GlobalRateLimiter()
        
        # HTTP session with connection pooling
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        connector = aiohttp.TCPConnector(
            limit=10,  # Connection pool size
            limit_per_host=5,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=300)  # 5 minute timeout
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'api-key': self.api_key,
                'Content-Type': 'application/json'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def create_video_job(self, prompt: str, **kwargs) -> VideoGenerationResult:
        """
        Create video generation job with distributed rate limiting.
        
        Args:
            prompt: Text prompt for video generation
            **kwargs: Additional parameters (duration, width, height, etc.)
        
        Returns:
            VideoGenerationResult with job status and file information
        
        Raises:
            RateLimitExceeded: When rate limit is hit
            AzureAPIError: When Azure API returns an error
        """
        # Apply rate limiting
        await self.rate_limiter.acquire()
        
        url = f"{self.endpoint}/openai/deployments/{self.deployment}/videos/generations"
        
        payload = {
            'prompt': prompt,
            'model': 'sora',
            **kwargs
        }
        
        try:
            async with self.session.post(
                url,
                json=payload,
                params={'api-version': self.api_version}
            ) as response:
                
                if response.status == 429:
                    raise RateLimitExceeded("Azure API rate limit exceeded")
                
                response.raise_for_status()
                result_data = await response.json()
                
                return VideoGenerationResult(
                    status="succeeded",
                    job_id=result_data.get('id'),
                    file_path=await self._download_video(result_data['url']),
                    download_url=result_data['url'],
                    metadata=result_data
                )
        
        except aiohttp.ClientError as e:
            logging.error(f"Azure API error: {e}")
            raise AzureAPIError(f"API request failed: {e}")
    
    async def _download_video(self, video_url: str) -> str:
        """Download video file from Azure storage."""
        import aiofiles
        import os
        from pathlib import Path
        
        # Create unique filename
        filename = f"video_{uuid.uuid4().hex[:8]}.mp4"
        filepath = Path(self.upload_folder) / filename
        
        async with self.session.get(video_url) as response:
            response.raise_for_status()
            
            async with aiofiles.open(filepath, 'wb') as f:
                async for chunk in response.content.iter_chunked(8192):
                    await f.write(chunk)
        
        return str(filepath.absolute())
```

### Rate Limiting Implementation

```python
# src/rate_limiting/limiter.py
import asyncio
import time
import redis.asyncio as redis
from typing import Optional
import logging

class GlobalRateLimiter:
    """Redis-based distributed rate limiter for Azure API compliance."""
    
    def __init__(self, 
                 redis_client: redis.Redis,
                 requests_per_second: int = 10,
                 window_size: int = 1):
        self.redis = redis_client
        self.rate_limit = requests_per_second
        self.window_size = window_size
        self.key_prefix = "rate_limit:global"
    
    async def acquire(self, timeout: int = 30) -> bool:
        """
        Acquire rate limit token using sliding window algorithm.
        
        Args:
            timeout: Maximum wait time in seconds
        
        Returns:
            True if token acquired, False if timeout
        
        Raises:
            RateLimitExceeded: If unable to acquire token within timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self._try_acquire():
                return True
            
            # Exponential backoff with jitter
            wait_time = min(1.0, 0.1 * (2 ** ((time.time() - start_time) / 5)))
            await asyncio.sleep(wait_time)
        
        raise RateLimitExceeded(f"Could not acquire rate limit token within {timeout}s")
    
    async def _try_acquire(self) -> bool:
        """Try to acquire a single token."""
        current_time = time.time()
        window_start = current_time - self.window_size
        
        # Use Redis pipeline for atomic operations
        pipe = self.redis.pipeline()
        
        # Remove expired entries
        pipe.zremrangebyscore(self.key_prefix, 0, window_start)
        
        # Count current requests
        pipe.zcard(self.key_prefix)
        
        # Add current request
        pipe.zadd(self.key_prefix, {str(current_time): current_time})
        
        # Set expiration
        pipe.expire(self.key_prefix, self.window_size + 1)
        
        results = await pipe.execute()
        current_count = results[1]
        
        if current_count < self.rate_limit:
            logging.debug(f"Rate limit token acquired ({current_count + 1}/{self.rate_limit})")
            return True
        else:
            # Remove the request we just added since we can't process it
            await self.redis.zrem(self.key_prefix, str(current_time))
            logging.debug(f"Rate limit exceeded ({current_count}/{self.rate_limit})")
            return False
    
    async def get_remaining_capacity(self) -> int:
        """Get remaining capacity in current window."""
        current_time = time.time()
        window_start = current_time - self.window_size
        
        # Clean up expired entries and count current
        pipe = self.redis.pipeline()
        pipe.zremrangebyscore(self.key_prefix, 0, window_start)
        pipe.zcard(self.key_prefix)
        
        results = await pipe.execute()
        current_count = results[1]
        
        return max(0, self.rate_limit - current_count)
```

This developer guide provides comprehensive information for setting up, developing, testing, and contributing to the multi-user system. The examples show real working code patterns that developers can use as templates for implementing new features.