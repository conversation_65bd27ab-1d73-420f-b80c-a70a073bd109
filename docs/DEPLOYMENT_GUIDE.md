# Multi-User Sora Video Generation - Production Deployment Guide

This guide provides comprehensive instructions for deploying the multi-user Azure OpenAI Sora video generation system in production environments, supporting 10+ concurrent users with high availability and scalability.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Infrastructure Setup](#infrastructure-setup)
- [Database Configuration](#database-configuration)
- [Redis Configuration](#redis-configuration)
- [Application Deployment](#application-deployment)
- [Celery Worker Scaling](#celery-worker-scaling)
- [Load Balancing](#load-balancing)
- [Monitoring & Logging](#monitoring--logging)
- [Security Configuration](#security-configuration)
- [Backup & Recovery](#backup--recovery)
- [Troubleshooting](#troubleshooting)
- [Maintenance Procedures](#maintenance-procedures)

## Architecture Overview

### Production Architecture Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Load Balancer │    │  Monitoring      │    │  Log Management │
│   (Nginx)       │    │  (Prometheus/    │    │  (ELK Stack)    │
│                 │    │   Grafana)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Application Layer                            │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Flask App 1   │   Flask App 2   │   Flask App 3               │
│   (Port 5001)   │   (Port 5002)   │   (Port 5003)               │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Worker Layer                                  │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Celery Worker  │  Celery Worker  │  Celery Worker              │
│      Pool 1     │      Pool 2     │      Pool 3                 │
│   (4 workers)   │   (4 workers)   │   (4 workers)               │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                          │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   PostgreSQL    │   Redis Cluster │   File Storage              │
│   (Primary +    │   (Master +     │   (Shared Volume/           │
│    Replicas)    │    Replicas)    │    Object Storage)          │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### Key Design Principles
- **High Availability**: Multiple application instances with load balancing
- **Horizontal Scaling**: Worker pools can be scaled independently
- **Data Persistence**: PostgreSQL with replication and Redis clustering
- **Monitoring**: Comprehensive metrics collection and alerting
- **Security**: Environment isolation, secrets management, and secure communications

## Prerequisites

### System Requirements

**Minimum Production Environment:**
- **CPU**: 16 cores (recommended: 32 cores)
- **RAM**: 32GB (recommended: 64GB)
- **Storage**: 500GB SSD (recommended: 1TB+ for video storage)
- **Network**: 1Gbps connection with low latency to Azure regions

**Software Requirements:**
- Docker 24.0+ and Docker Compose 2.20+
- Linux distribution (Ubuntu 22.04 LTS recommended)
- SSL certificates for HTTPS termination
- Azure OpenAI account with Sora access
- Domain name with DNS management

### Azure OpenAI Setup

1. **Create Azure OpenAI Resource**:
   ```bash
   az cognitiveservices account create \
     --name sora-production \
     --resource-group production-rg \
     --kind OpenAI \
     --sku S0 \
     --location eastus2
   ```

2. **Deploy Sora Model**:
   ```bash
   az cognitiveservices account deployment create \
     --name sora-production \
     --resource-group production-rg \
     --deployment-name sora \
     --model-name sora \
     --model-version "1.0" \
     --sku-capacity 100
   ```

3. **Configure Rate Limits**:
   - Set appropriate TPM (Tokens Per Minute) limits
   - Configure quota allocation for peak usage
   - Set up monitoring alerts for quota consumption

## Infrastructure Setup

### Docker Swarm Setup (Recommended for Production)

1. **Initialize Swarm Manager**:
   ```bash
   # On the manager node
   docker swarm init --advertise-addr <manager-ip>
   
   # Save the join token for worker nodes
   docker swarm join-token worker
   ```

2. **Add Worker Nodes**:
   ```bash
   # On each worker node
   docker swarm join --token <worker-token> <manager-ip>:2377
   ```

3. **Create Overlay Network**:
   ```bash
   docker network create \
     --driver overlay \
     --subnet 10.0.0.0/16 \
     sora-production-network
   ```

### Environment Variables Configuration

Create production environment file:

```bash
# /opt/sora/production/.env
# Never commit this file to version control

# Application Configuration
FLASK_ENV=production
SECRET_KEY=your-cryptographically-secure-32-byte-key
DEBUG=false
FORCE_HTTPS=true

# Database Configuration  
DATABASE_URL=postgresql://sora_user:${DB_PASSWORD}@postgres-primary:5432/sora_production
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30

# Redis Configuration
CELERY_BROKER_URL=redis://redis-primary:6379/0
CELERY_RESULT_BACKEND=redis://redis-primary:6379/0
REDIS_SENTINEL_HOSTS=redis-sentinel-1:26379,redis-sentinel-2:26379,redis-sentinel-3:26379

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_SORA_DEPLOYMENT=sora

# Video Generation Settings
MAX_PROMPT_LENGTH=500
DEFAULT_VIDEO_DURATION=5
MAX_VIDEO_DURATION=20
MAX_CONCURRENT_JOBS_PER_SESSION=5

# File Storage Configuration
UPLOAD_FOLDER=/app/storage/uploads
MAX_CONTENT_LENGTH=104857600
FILE_CLEANUP_ENABLED=true
FILE_MAX_AGE_HOURS=24

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=10

# Monitoring & Health Checks
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
LOG_LEVEL=INFO
SQL_DEBUG=false

# Security Configuration
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
CSRF_ENABLED=true
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true

# Monitoring Credentials
GRAFANA_PASSWORD=your-secure-grafana-password
FLOWER_USERNAME=admin
FLOWER_PASSWORD=your-secure-flower-password
PROMETHEUS_RETENTION_DAYS=30

# Backup Configuration
BACKUP_S3_BUCKET=sora-production-backups
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key
```

### SSL Certificate Setup

1. **Generate SSL Certificates** (using Let's Encrypt):
   ```bash
   # Install certbot
   sudo apt-get update
   sudo apt-get install certbot

   # Generate certificates
   sudo certbot certonly --standalone \
     -d your-domain.com \
     -d www.your-domain.com \
     --email <EMAIL> \
     --agree-tos --non-interactive

   # Copy certificates to deployment directory
   sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/sora/ssl/
   sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/sora/ssl/
   sudo chown -R sora:sora /opt/sora/ssl/
   sudo chmod 600 /opt/sora/ssl/*
   ```

2. **Setup Certificate Renewal**:
   ```bash
   # Create renewal script
   cat > /opt/sora/scripts/renew-ssl.sh << 'EOF'
   #!/bin/bash
   certbot renew --quiet
   cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/sora/ssl/
   cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/sora/ssl/
   docker service update --force sora-nginx
   EOF

   # Make executable and add to cron
   chmod +x /opt/sora/scripts/renew-ssl.sh
   echo "0 2 * * 0 /opt/sora/scripts/renew-ssl.sh" | sudo crontab -
   ```

## Database Configuration

### PostgreSQL Production Setup

1. **PostgreSQL Configuration** (`/opt/sora/postgres/postgresql.conf`):
   ```ini
   # Connection Settings
   listen_addresses = '*'
   port = 5432
   max_connections = 200
   
   # Memory Settings
   shared_buffers = 4GB
   effective_cache_size = 12GB
   work_mem = 64MB
   maintenance_work_mem = 512MB
   
   # Checkpoint Settings
   checkpoint_completion_target = 0.7
   wal_buffers = 16MB
   default_statistics_target = 100
   
   # Query Planner
   random_page_cost = 1.1
   effective_io_concurrency = 200
   
   # Write-Ahead Logging
   wal_level = replica
   archive_mode = on
   archive_command = 'cp %p /var/lib/postgresql/archive/%f'
   max_wal_senders = 3
   wal_keep_segments = 32
   
   # Logging
   log_destination = 'csvlog'
   logging_collector = on
   log_directory = '/var/log/postgresql'
   log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
   log_min_duration_statement = 1000
   log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
   
   # Performance Monitoring
   track_activities = on
   track_counts = on
   track_functions = all
   ```

2. **Database Initialization Script**:
   ```sql
   -- /opt/sora/postgres/init/01-init-production.sql
   CREATE DATABASE sora_production;
   CREATE USER sora_user WITH ENCRYPTED PASSWORD 'your-secure-password';
   GRANT ALL PRIVILEGES ON DATABASE sora_production TO sora_user;
   
   -- Connect to the sora_production database
   \c sora_production;
   
   -- Create extensions
   CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
   CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
   
   -- Grant permissions
   GRANT ALL ON SCHEMA public TO sora_user;
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO sora_user;
   GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO sora_user;
   
   -- Create monitoring user for metrics collection
   CREATE USER monitoring WITH PASSWORD 'monitoring-password';
   GRANT pg_monitor TO monitoring;
   ```

3. **Database Backup Script**:
   ```bash
   #!/bin/bash
   # /opt/sora/scripts/backup-database.sh
   
   set -e
   
   BACKUP_DIR="/opt/sora/backups/database"
   DATE=$(date +%Y%m%d_%H%M%S)
   BACKUP_FILE="sora_production_${DATE}.sql.gz"
   
   # Create backup directory
   mkdir -p "$BACKUP_DIR"
   
   # Perform backup
   docker exec sora-postgres pg_dump \
     -U sora_user \
     -d sora_production \
     --verbose \
     --format=custom \
     --no-owner \
     --no-privileges | gzip > "$BACKUP_DIR/$BACKUP_FILE"
   
   # Upload to S3 (optional)
   if [ -n "$BACKUP_S3_BUCKET" ]; then
     aws s3 cp "$BACKUP_DIR/$BACKUP_FILE" "s3://$BACKUP_S3_BUCKET/database/"
   fi
   
   # Clean old backups (keep last 7 days)
   find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete
   
   echo "Database backup completed: $BACKUP_FILE"
   ```

### Database Monitoring

1. **Performance Monitoring Queries**:
   ```sql
   -- Monitor active connections
   SELECT count(*) as active_connections 
   FROM pg_stat_activity 
   WHERE state = 'active';
   
   -- Monitor slow queries
   SELECT query, mean_time, calls, total_time
   FROM pg_stat_statements 
   ORDER BY mean_time DESC 
   LIMIT 10;
   
   -- Monitor database size
   SELECT pg_size_pretty(pg_database_size('sora_production')) as database_size;
   
   -- Monitor table sizes
   SELECT 
     schemaname,
     tablename,
     pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
   FROM pg_tables 
   WHERE schemaname = 'public' 
   ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
   ```

## Redis Configuration

### Redis Production Setup

1. **Redis Configuration** (`/opt/sora/redis/redis.conf`):
   ```ini
   # Network
   bind 0.0.0.0
   port 6379
   protected-mode yes
   requirepass your-redis-password
   
   # Memory Management
   maxmemory 2gb
   maxmemory-policy allkeys-lru
   
   # Persistence
   save 900 1
   save 300 10
   save 60 10000
   stop-writes-on-bgsave-error yes
   rdbcompression yes
   rdbchecksum yes
   dbfilename dump.rdb
   dir /data
   
   # Append Only File
   appendonly yes
   appendfilename "appendonly.aof"
   appendfsync everysec
   no-appendfsync-on-rewrite no
   auto-aof-rewrite-percentage 100
   auto-aof-rewrite-min-size 64mb
   
   # Logging
   loglevel notice
   logfile "/var/log/redis/redis-server.log"
   
   # Performance
   tcp-keepalive 300
   timeout 0
   tcp-backlog 511
   
   # Security
   rename-command FLUSHDB ""
   rename-command FLUSHALL ""
   rename-command KEYS ""
   rename-command CONFIG "CONFIG_SORA_PROD"
   
   # Monitoring
   latency-monitor-threshold 100
   ```

2. **Redis Sentinel Configuration** (for high availability):
   ```ini
   # /opt/sora/redis/sentinel.conf
   port 26379
   sentinel monitor sora-master redis-primary 6379 2
   sentinel auth-pass sora-master your-redis-password
   sentinel down-after-milliseconds sora-master 5000
   sentinel parallel-syncs sora-master 1
   sentinel failover-timeout sora-master 10000
   
   # Notification script
   sentinel notification-script sora-master /opt/sora/scripts/redis-notify.sh
   ```

3. **Redis Backup Script**:
   ```bash
   #!/bin/bash
   # /opt/sora/scripts/backup-redis.sh
   
   set -e
   
   BACKUP_DIR="/opt/sora/backups/redis"
   DATE=$(date +%Y%m%d_%H%M%S)
   
   mkdir -p "$BACKUP_DIR"
   
   # Trigger background save
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" BGSAVE
   
   # Wait for save to complete
   while [ "$(docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" LASTSAVE)" = "$(docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" LASTSAVE)" ]; do
     sleep 1
   done
   
   # Copy dump file
   docker cp sora-redis:/data/dump.rdb "$BACKUP_DIR/redis_${DATE}.rdb"
   
   # Compress and upload
   gzip "$BACKUP_DIR/redis_${DATE}.rdb"
   
   if [ -n "$BACKUP_S3_BUCKET" ]; then
     aws s3 cp "$BACKUP_DIR/redis_${DATE}.rdb.gz" "s3://$BACKUP_S3_BUCKET/redis/"
   fi
   
   # Clean old backups
   find "$BACKUP_DIR" -name "*.rdb.gz" -mtime +7 -delete
   
   echo "Redis backup completed: redis_${DATE}.rdb.gz"
   ```

## Application Deployment

### Deployment Script

Create the main deployment script:

```bash
#!/bin/bash
# /opt/sora/scripts/deploy.sh

set -e

DEPLOYMENT_DIR="/opt/sora"
BACKUP_DIR="/opt/sora/backups"
LOG_FILE="/opt/sora/logs/deployment.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "Starting Sora Multi-User deployment..."

# Create necessary directories
mkdir -p "$DEPLOYMENT_DIR"/{config,scripts,logs,backups,ssl,storage}
mkdir -p "$BACKUP_DIR"/{database,redis,application}

# Set permissions
chown -R sora:sora "$DEPLOYMENT_DIR"
chmod 755 "$DEPLOYMENT_DIR"/scripts/*.sh

# Pull latest images
log "Pulling latest Docker images..."
docker-compose -f docker-compose.production.yml pull

# Backup current state
log "Creating backup of current deployment..."
if docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
    "$DEPLOYMENT_DIR/scripts/backup-database.sh"
    "$DEPLOYMENT_DIR/scripts/backup-redis.sh"
fi

# Run database migrations
log "Running database migrations..."
docker-compose -f docker-compose.production.yml run --rm app1 \
    flask --app src.main:create_app db upgrade

# Update services with zero-downtime deployment
log "Updating application services..."
for service in app1 app2 app3; do
    log "Updating $service..."
    docker-compose -f docker-compose.production.yml up -d --no-deps "$service"
    
    # Wait for health check
    sleep 30
    
    # Verify service health
    if ! docker-compose -f docker-compose.production.yml exec "$service" curl -f http://localhost:5001/health; then
        log "ERROR: $service failed health check after update"
        exit 1
    fi
    
    log "$service updated successfully"
done

# Update worker services
log "Updating worker services..."
for worker in worker1 worker2 worker3 worker4; do
    log "Updating $worker..."
    docker-compose -f docker-compose.production.yml up -d --no-deps "$worker"
    sleep 10
done

# Restart supporting services if needed
log "Checking supporting services..."
docker-compose -f docker-compose.production.yml up -d scheduler flower

# Verify overall system health
log "Verifying system health..."
sleep 60

if ! curl -f http://localhost/health; then
    log "ERROR: System health check failed after deployment"
    exit 1
fi

# Clean up old Docker images
log "Cleaning up old Docker images..."
docker image prune -f

log "Deployment completed successfully!"

# Send notification (optional)
if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"Sora Multi-User deployment completed successfully"}' \
        "$SLACK_WEBHOOK_URL"
fi
```

### Environment-Specific Configurations

1. **Production Environment** (`docker-compose.production.yml`):
   ```yaml
   version: '3.8'
   
   services:
     nginx:
       image: nginx:1.25-alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
         - ./ssl:/etc/nginx/ssl:ro
       deploy:
         replicas: 2
         placement:
           constraints:
             - node.role == manager
   
     app1: &app-template
       image: sora-multiuser:latest
       environment:
         - FLASK_ENV=production
       deploy:
         replicas: 1
         resources:
           limits:
             memory: 2G
             cpus: '1.0'
           reservations:
             memory: 1G
             cpus: '0.5'
   
     # ... other services
   ```

2. **Staging Environment** (`docker-compose.staging.yml`):
   ```yaml
   version: '3.8'
   
   services:
     app1:
       image: sora-multiuser:staging
       environment:
         - FLASK_ENV=staging
         - DATABASE_URL=*********************************************/sora_staging
       deploy:
         replicas: 1
         resources:
           limits:
             memory: 1G
             cpus: '0.5'
   
     # ... reduced resource allocation for staging
   ```

## Celery Worker Scaling

### Dynamic Worker Scaling

1. **Worker Auto-Scaling Script**:
   ```bash
   #!/bin/bash
   # /opt/sora/scripts/auto-scale-workers.sh
   
   set -e
   
   # Configuration
   MIN_WORKERS=2
   MAX_WORKERS=8
   QUEUE_THRESHOLD_HIGH=10
   QUEUE_THRESHOLD_LOW=3
   SCALE_UP_COOLDOWN=300  # 5 minutes
   SCALE_DOWN_COOLDOWN=600  # 10 minutes
   
   LAST_SCALE_FILE="/tmp/last_worker_scale"
   LOG_FILE="/opt/sora/logs/auto-scale.log"
   
   log() {
       echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
   }
   
   # Get current queue depth
   QUEUE_DEPTH=$(docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" llen celery)
   
   # Get current worker count
   CURRENT_WORKERS=$(docker-compose -f docker-compose.production.yml ps | grep -c "sora-worker")
   
   log "Queue depth: $QUEUE_DEPTH, Current workers: $CURRENT_WORKERS"
   
   # Check if we should scale up
   if [ "$QUEUE_DEPTH" -gt "$QUEUE_THRESHOLD_HIGH" ] && [ "$CURRENT_WORKERS" -lt "$MAX_WORKERS" ]; then
       if [ ! -f "$LAST_SCALE_FILE" ] || [ $(($(date +%s) - $(cat $LAST_SCALE_FILE))) -gt $SCALE_UP_COOLDOWN ]; then
           NEW_WORKER_COUNT=$((CURRENT_WORKERS + 1))
           log "Scaling up to $NEW_WORKER_COUNT workers (queue depth: $QUEUE_DEPTH)"
           
           # Start new worker
           docker-compose -f docker-compose.production.yml up -d --scale worker=$NEW_WORKER_COUNT
           
           # Record scale time
           date +%s > "$LAST_SCALE_FILE"
           
           # Send notification
           curl -X POST -H 'Content-type: application/json' \
               --data "{\"text\":\"Scaled up to $NEW_WORKER_COUNT workers (queue: $QUEUE_DEPTH)\"}" \
               "$SLACK_WEBHOOK_URL" || true
       fi
   fi
   
   # Check if we should scale down
   if [ "$QUEUE_DEPTH" -lt "$QUEUE_THRESHOLD_LOW" ] && [ "$CURRENT_WORKERS" -gt "$MIN_WORKERS" ]; then
       if [ ! -f "$LAST_SCALE_FILE" ] || [ $(($(date +%s) - $(cat $LAST_SCALE_FILE))) -gt $SCALE_DOWN_COOLDOWN ]; then
           NEW_WORKER_COUNT=$((CURRENT_WORKERS - 1))
           log "Scaling down to $NEW_WORKER_COUNT workers (queue depth: $QUEUE_DEPTH)"
           
           # Stop one worker gracefully
           WORKER_TO_STOP=$(docker-compose -f docker-compose.production.yml ps | grep "sora-worker" | tail -1 | awk '{print $1}')
           docker exec "$WORKER_TO_STOP" celery -A src.job_queue.celery_app control shutdown
           
           # Wait for graceful shutdown
           sleep 30
           
           # Remove the container
           docker-compose -f docker-compose.production.yml up -d --scale worker=$NEW_WORKER_COUNT
           
           # Record scale time
           date +%s > "$LAST_SCALE_FILE"
           
           # Send notification
           curl -X POST -H 'Content-type: application/json' \
               --data "{\"text\":\"Scaled down to $NEW_WORKER_COUNT workers (queue: $QUEUE_DEPTH)\"}" \
               "$SLACK_WEBHOOK_URL" || true
       fi
   fi
   ```

2. **Worker Health Monitoring**:
   ```bash
   #!/bin/bash
   # /opt/sora/scripts/monitor-workers.sh
   
   set -e
   
   LOG_FILE="/opt/sora/logs/worker-health.log"
   
   log() {
       echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
   }
   
   # Check worker health
   for worker in $(docker-compose -f docker-compose.production.yml ps | grep "sora-worker" | awk '{print $1}'); do
       # Check if worker is responding
       if ! docker exec "$worker" celery -A src.job_queue.celery_app inspect ping >/dev/null 2>&1; then
           log "WARNING: Worker $worker is not responding"
           
           # Try to restart the worker
           log "Restarting worker $worker"
           docker-compose -f docker-compose.production.yml restart "$worker"
           
           # Send alert
           curl -X POST -H 'Content-type: application/json' \
               --data "{\"text\":\"Worker $worker restarted due to health check failure\"}" \
               "$SLACK_WEBHOOK_URL" || true
       fi
       
       # Check worker memory usage
       MEMORY_USAGE=$(docker stats --no-stream --format "{{.MemUsage}}" "$worker" | awk '{print $1}' | sed 's/[A-Za-z]*//g')
       MEMORY_LIMIT=2048  # 2GB limit
       
       if [ "$(echo "$MEMORY_USAGE > $MEMORY_LIMIT" | bc)" -eq 1 ]; then
           log "WARNING: Worker $worker memory usage: ${MEMORY_USAGE}MB (limit: ${MEMORY_LIMIT}MB)"
           
           # Gracefully restart worker
           docker exec "$worker" celery -A src.job_queue.celery_app control shutdown
           sleep 30
           docker-compose -f docker-compose.production.yml restart "$worker"
           
           # Send alert
           curl -X POST -H 'Content-type: application/json' \
               --data "{\"text\":\"Worker $worker restarted due to high memory usage: ${MEMORY_USAGE}MB\"}" \
               "$SLACK_WEBHOOK_URL" || true
       fi
   done
   ```

3. **Setup Cron Jobs for Monitoring**:
   ```bash
   # Add to crontab
   # Auto-scale workers every 2 minutes
   */2 * * * * /opt/sora/scripts/auto-scale-workers.sh
   
   # Monitor worker health every 5 minutes
   */5 * * * * /opt/sora/scripts/monitor-workers.sh
   
   # Backup database daily at 2 AM
   0 2 * * * /opt/sora/scripts/backup-database.sh
   
   # Backup Redis daily at 3 AM
   0 3 * * * /opt/sora/scripts/backup-redis.sh
   
   # Clean up old video files every hour
   0 * * * * /opt/sora/scripts/cleanup-files.sh
   ```

## Load Balancing

### Nginx Configuration

1. **Main Nginx Configuration** (`/opt/sora/nginx/nginx.conf`):
   ```nginx
   user nginx;
   worker_processes auto;
   worker_rlimit_nofile 65535;
   
   error_log /var/log/nginx/error.log warn;
   pid /var/run/nginx.pid;
   
   events {
       worker_connections 4096;
       use epoll;
       multi_accept on;
   }
   
   http {
       include /etc/nginx/mime.types;
       default_type application/octet-stream;
       
       # Logging format
       log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       'rt=$request_time uct="$upstream_connect_time" '
                       'uht="$upstream_header_time" urt="$upstream_response_time"';
       
       access_log /var/log/nginx/access.log main;
       
       # Performance settings
       sendfile on;
       tcp_nopush on;
       tcp_nodelay on;
       keepalive_timeout 65;
       types_hash_max_size 2048;
       client_max_body_size 100M;
       
       # Gzip compression
       gzip on;
       gzip_vary on;
       gzip_min_length 1024;
       gzip_proxied any;
       gzip_comp_level 6;
       gzip_types
           text/plain
           text/css
           text/xml
           text/javascript
           application/json
           application/javascript
           application/xml+rss
           application/atom+xml
           image/svg+xml;
       
       # Rate limiting
       limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
       limit_req_zone $binary_remote_addr zone=video:10m rate=2r/s;
       
       # Upstream configuration
       upstream sora_app {
           least_conn;
           server app1:5001 max_fails=3 fail_timeout=30s;
           server app2:5002 max_fails=3 fail_timeout=30s;
           server app3:5003 max_fails=3 fail_timeout=30s;
           keepalive 32;
       }
       
       # WebSocket upstream
       upstream sora_websocket {
           ip_hash;  # Sticky sessions for WebSocket
           server app1:5001;
           server app2:5002;
           server app3:5003;
       }
       
       # SSL configuration
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;
       ssl_session_cache shared:SSL:10m;
       ssl_session_timeout 10m;
       
       # Security headers
       add_header X-Frame-Options DENY always;
       add_header X-Content-Type-Options nosniff always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header Referrer-Policy strict-origin-when-cross-origin always;
       add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
       
       # Main server block
       server {
           listen 80;
           server_name your-domain.com www.your-domain.com;
           return 301 https://$server_name$request_uri;
       }
       
       server {
           listen 443 ssl http2;
           server_name your-domain.com www.your-domain.com;
           
           ssl_certificate /etc/nginx/ssl/fullchain.pem;
           ssl_certificate_key /etc/nginx/ssl/privkey.pem;
           
           # Main application
           location / {
               limit_req zone=api burst=20 nodelay;
               
               proxy_pass http://sora_app;
               proxy_set_header Host $http_host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;
               
               # Connection settings
               proxy_connect_timeout 30s;
               proxy_send_timeout 30s;
               proxy_read_timeout 30s;
               
               # Buffer settings
               proxy_buffering on;
               proxy_buffer_size 4k;
               proxy_buffers 8 4k;
               proxy_busy_buffers_size 8k;
           }
           
           # WebSocket endpoint
           location /socket.io/ {
               proxy_pass http://sora_websocket;
               proxy_http_version 1.1;
               proxy_set_header Upgrade $http_upgrade;
               proxy_set_header Connection "upgrade";
               proxy_set_header Host $http_host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;
               
               # WebSocket specific settings
               proxy_connect_timeout 10s;
               proxy_send_timeout 10s;
               proxy_read_timeout 3600s;  # 1 hour for long connections
           }
           
           # Video streaming with rate limiting
           location ~ ^/api/(video|download)/ {
               limit_req zone=video burst=5 nodelay;
               
               proxy_pass http://sora_app;
               proxy_set_header Host $http_host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;
               
               # Large file settings
               proxy_connect_timeout 30s;
               proxy_send_timeout 300s;
               proxy_read_timeout 300s;
               proxy_buffering off;
               proxy_request_buffering off;
           }
           
           # Static files with caching
           location /static/ {
               alias /var/www/static/;
               expires 1y;
               add_header Cache-Control "public, immutable";
               add_header X-Frame-Options DENY;
               add_header X-Content-Type-Options nosniff;
           }
           
           # Health check endpoint (no rate limiting)
           location /health {
               proxy_pass http://sora_app;
               proxy_set_header Host $http_host;
               access_log off;
           }
           
           # Block sensitive paths
           location ~ /\. {
               deny all;
               access_log off;
               log_not_found off;
           }
           
           location ~ /(\.env|docker-compose|Dockerfile) {
               deny all;
               access_log off;
               log_not_found off;
           }
       }
   }
   ```

This deployment guide provides comprehensive instructions for production deployment with high availability, monitoring, and scalability. The next sections will cover monitoring, security, and troubleshooting procedures.