# Universal Architectural Interpretation Rules

**Purpose**: Technology-agnostic architectural interpretation rules that prevent component proliferation and ensure consistent mapping from system concepts to implementation patterns across all technology stacks.

## Core Framework Philosophy

The PRP framework uses **progressive architectural refinement** with clear interpretation rules to prevent ambiguous implementation decisions.

### Architectural Layer Hierarchy

```yaml
# Layer 1: System Architecture (Logical Components)
system_components:
  description: "High-level logical functions the system performs"
  examples: ["Memory Agent System", "Data Processing System", "User Interface System"]
  mapping_rule: "One system component = one implementation module maximum"

# Layer 2: Implementation Architecture (Code Organization) 
implementation_modules:
  description: "Code organization units that implement system components"
  examples: ["src/memory_agent/", "src/data_processor/", "src/ui_components/"]
  mapping_rule: "One module per system component, organized by technology patterns"

# Layer 3: Capability Architecture (Component Functions)
component_capabilities:
  description: "Functions and capabilities within implementation modules"
  examples: ["assess_relevance", "store_data", "render_interface"]
  mapping_rule: "Capabilities become methods/tools/functions within modules"

# Layer 4: Feature Architecture (Internal Logic)
internal_features:
  description: "Algorithmic features and implementation details"
  examples: ["validation_logic", "caching_strategy", "error_handling"]
  mapping_rule: "Features become internal functions within capability implementations"
```

## Universal Interpretation Rules

### Rule 1: Component Consolidation Principle
**"Related capabilities belong to one implementation unit"**

```yaml
consolidation_logic:
  if_capabilities_serve_same_system_component:
    action: "Group into single implementation module"
    pattern: "One module with multiple methods/tools/functions"
  
  if_capabilities_serve_different_system_components:
    action: "Create separate implementation modules"
    pattern: "Multiple modules with clear interfaces"

examples:
  memory_system_capabilities: ["assess_relevance", "store_episode", "search_memories"]
  result: "Single memory_agent module with three methods/tools"
  
  separate_system_components: ["memory_system", "harvester_system"]
  result: "Two modules: memory_agent/ and harvester/"
```

### Rule 2: Implementation Unit Minimization
**"Avoid unnecessary component proliferation"**

```yaml
proliferation_prevention:
  before_creating_new_component:
    check: "Does this require a separate system boundary?"
    if_no: "Add as capability to existing component"
    if_yes: "Create new implementation module"
  
  warning_signs:
    - "More implementation units than logical system components"
    - "Single capability requiring separate component"
    - "Capabilities that share all dependencies"

validation_questions:
  - "Could this be a method/tool instead of a separate component?"
  - "Does this capability serve a different system function?"
  - "Would combining this reduce system complexity?"
```

### Rule 3: Technology-Agnostic Mapping
**"Universal patterns adapt to technology-specific implementations"**

```yaml
universal_patterns:
  system_component: 
    python_ai: "Agent class with @tool methods"
    nodejs_api: "Service class with methods"
    go_service: "Package with exported functions"
    java_enterprise: "Service bean with methods"
    react_app: "Component with hooks/methods"
    mobile_app: "Manager/Service with methods"
  
  capability_implementation:
    python_ai: "@agent.tool decorated methods"
    nodejs_api: "Class methods or route handlers"
    go_service: "Exported functions in package"
    java_enterprise: "Public methods in service"
    react_app: "Component methods or custom hooks"
    mobile_app: "Methods in manager/service class"
```

## Application Guidelines for Commands

### For Architecture Analysis Commands (1-3)
Use consistent architectural terminology:

```yaml
required_terminology:
  system_level: "Use 'System Component' for logical functions"
  implementation_level: "Use 'Implementation Module' for code organization"
  capability_level: "Use 'Component Capability' for functions within modules"
  feature_level: "Use 'Internal Feature' for algorithmic details"

avoid_ambiguous_terms:
  instead_of: ["component", "service", "agent", "module"]
  use_specific: ["system component", "implementation module", "capability", "feature"]
```

### For Project Planning Commands (4-5)
Apply consolidation principles:

```yaml
consolidation_validation:
  step_1: "Count system components from architecture analysis"
  step_2: "Ensure implementation modules <= system components"
  step_3: "Group related capabilities under appropriate modules"
  step_4: "Validate no unnecessary component proliferation"

mapping_enforcement:
  system_component: "→ implementation_module (max 1:1 ratio)"
  capability_list: "→ methods_within_module (grouped by system function)"
  feature_list: "→ internal_functions (implementation details)"
```

### For Implementation Commands (6-7)
Respect established architectural decisions:

```yaml
implementation_constraints:
  module_structure: "Must follow established architectural boundaries"
  capability_organization: "Must implement as methods/tools within modules"
  feature_implementation: "Must remain internal to capability functions"
  
technology_adaptation:
  detect_context: "Identify technology stack from project specifications"
  apply_patterns: "Use appropriate technology-specific implementation patterns"
  maintain_boundaries: "Preserve architectural decisions regardless of technology"
```

## Quality Gates and Validation

### Architectural Consistency Checks

```yaml
validation_rules:
  component_count_check:
    rule: "implementation_modules.count <= system_components.count"
    violation: "Too many implementation modules for system complexity"
    check_logic: "if modules > components: flag_proliferation_warning"
  
  capability_grouping_check:
    rule: "related_capabilities grouped in same module"
    violation: "Unnecessarily separated related functionality"
    check_logic: "if capabilities_serve_same_system_function: must_be_in_same_module"
  
  terminology_consistency_check:
    rule: "consistent architectural terminology across documents"
    violation: "Ambiguous or inconsistent component references"
    check_logic: "scan for 'agent', 'service', 'component' without 'system_' qualifier"

  technology_appropriateness_check:
    rule: "implementation patterns match detected technology stack"
    violation: "Technology-inappropriate implementation suggestions"
    check_logic: "if python_ai_project: expect_agent_with_tools, if nodejs_api: expect_service_with_methods"

automated_checks:
  - "Count system components vs implementation modules (ratio validation)"
  - "Verify capability grouping follows system boundaries"
  - "Check for component proliferation anti-patterns"
  - "Validate technology-appropriate implementation patterns"
  - "Scan for ambiguous architectural terminology"
  - "Detect capability explosion patterns (multiple components for single system function)"
```

### Common Anti-Patterns to Prevent

```yaml
anti_patterns:
  capability_explosion:
    description: "Creating separate components for each capability"
    example: "memory_agent + conversation_agent + relevance_agent"
    solution: "Single memory_agent with three capabilities"
  
  functionality_fragmentation:
    description: "Splitting related functionality across components"
    example: "data_reader + data_validator + data_processor (all for same system)"
    solution: "Single data_system with three internal capabilities"
  
  technology_assumption:
    description: "Assuming specific technology patterns apply universally"
    example: "Creating AI agents for non-AI projects"
    solution: "Technology-agnostic architectural analysis first"
```

## Framework Integration

### Command Enhancement Requirements

All commands must:
1. **Reference these interpretation rules** in their analysis sections
2. **Apply consolidation principles** during architectural analysis
3. **Validate against anti-patterns** before generating outputs
4. **Use consistent terminology** throughout analysis and planning

### Success Metrics

- **Architectural Clarity**: Clear distinction between system components and capabilities
- **Implementation Efficiency**: Minimal component count while maintaining clear boundaries
- **Technology Flexibility**: Patterns work across all technology stacks
- **Consistency**: Same architectural decisions regardless of command execution order

---

**Framework Status**: Universal architectural interpretation rules established for consistent component mapping and proliferation prevention across all technology domains.