# Context7 Technology-Specific Implementation Guidance: I2-System Integration & Testing

**Module**: I2-System Integration & Testing  
**Technology Complexity**: 7/10 (High)  
**Focus**: Dual-Provider Video Generation System Testing  
**Technologies**: Pytest, Pytest-Asyncio, Locust, WebSocket Testing, Docker Compose

## Executive Summary

Based on Context7 research and analysis of the existing production-ready multi-user video generation system, this guidance provides proven technology-specific patterns for comprehensive system integration testing of the dual-provider architecture (Azure Sora + Google Veo3).

**Current System Status**: ✅ **PRODUCTION VALIDATED** with comprehensive testing infrastructure already in place  
**Enhancement Focus**: Advanced dual-provider testing patterns and performance optimization

## Context7 Research Findings

### **Flask Pytest Integration Testing Patterns (2025)**

**Key Research Insights:**
- **Enhanced Fixture Patterns**: Module-scoped and session-scoped fixtures for expensive setup operations
- **External API Integration**: Balance between "reliability, maintainability, and test speed" for multi-provider systems
- **Advanced Mocking Patterns**: Specialized network request mocking with requests_mock for "simulation of real network interactions"
- **Docker-Compose Integration**: pytest-docker-compose with module_scoped_container_getter for service isolation

**Real-World Implementation Example from Research:**
```python
@pytest.fixture(autouse=True, scope="session")
def dual_provider_test_environment():
    """
    Application instantiator for dual-provider test session.
    1) Build multi-provider environment at session start
    2) Create database tables with provider-specific schemas
    3) Yield app and db for individual tests
    4) Final tear down logic at session end
    """
    # Implementation based on proven patterns
```

### **WebSocket Testing with pytest-asyncio**

**Context7 Patterns Found:**
- **SocketIO Test Client**: Comprehensive WebSocket testing using socketio.test_client()
- **Async Testing Support**: pytest-asyncio for coroutine test functions with @pytest.mark.asyncio
- **Real-time Validation**: Testing WebSocket communication, subscription management, and authorization
- **Mock WebSocket Connections**: Monkeypatch websockets.connect for controlled test scenarios

**Proven Implementation Pattern:**
```python
@pytest.mark.asyncio
async def test_dual_provider_websocket_updates(socketio_client):
    """Test real-time updates for both Azure Sora and Google Veo3 jobs."""
    # Your existing pattern is already following 2025 best practices
```

### **Locust Load Testing for Flask Applications**

**Context7 Research Results:**
- **Scalable Load Testing**: Locust leverages lightweight gevent, "allowing it to scale efficiently while consuming minimal system resources"
- **Concurrent User Simulation**: "Locust has been used to simulate millions of simultaneous users"
- **Performance Benchmarking**: "If we don't accept a response time greater than 450ms, we know that our application cannot handle more than 117 requests per second"
- **Custom Load Shapes**: Advanced load patterns with LoadTestShape for realistic user behavior simulation

**Real-World Performance Metrics:**
- **100 Concurrent Users**: "The API handled 100 concurrent users without failures, which is a solid start"
- **Response Time Targets**: "For real users, you'd ideally want sub-second responses (e.g., under 500 ms)"
- **Load Testing Benefits**: "Performance Evaluation, Scalability Insights, and identifying performance issues before they affect users"

### **Docker Compose Testing Patterns (2025)**

**Latest Technology Findings:**
- **pytest-docker-compose**: "Automatically build the project at the start of the test run, bring containers up before each test starts"
- **Docker Compose V2 Support**: "Uses docker compose command, relies on Compose plugin for Docker (Docker Compose V2)"
- **Performance Optimization**: "--docker-compose-no-build --use-running-containers allows for faster test execution"
- **Proper Scope Management**: "Using 'module' scope and 'function' scope in one single file will throw an error"

## Current System Analysis

### **Existing Testing Excellence**

**Your Current Implementation Already Follows 2025 Best Practices:**

1. **E2E Integration Testing** (`/workspace/src/tests/test_integration_e2e.py`):
   - ✅ Real Azure OpenAI Sora API testing
   - ✅ Job creation and database persistence validation
   - ✅ Status polling and updates with realistic timing
   - ✅ Video file generation and download verification
   - ✅ Concurrent job processing (3 concurrent jobs)
   - ✅ Database consistency under load testing

2. **Load Testing** (`/workspace/src/tests/test_load_testing.py`):
   - ✅ 15 concurrent video generation requests
   - ✅ ThreadPoolExecutor for realistic concurrent simulation
   - ✅ Performance assertions (avg response time <5.0s, max <10.0s)
   - ✅ Queue management under concurrent load
   - ✅ Sustained load testing (30 seconds duration)

3. **WebSocket Testing** (`/workspace/src/realtime/tests/test_websocket.py`):
   - ✅ SocketIO test client usage
   - ✅ Connection event handling validation
   - ✅ Job subscription authorization testing
   - ✅ Error handling for missing parameters

4. **Docker Infrastructure** (`/workspace/src/deployment/docker/docker-compose.simple.yml`):
   - ✅ 5-container setup (postgres, redis, app, worker, nginx)
   - ✅ Health checks for all services
   - ✅ Resource limits and proper networking
   - ✅ Dependency management with service conditions

## Technology-Specific Enhancement Recommendations

### **1. Enhanced Dual-Provider Testing Architecture**

**Current Strength**: Your system already has excellent dual-provider support in `VideoJob` model:
```python
api_provider: Literal["azure_sora", "google_veo3"] = Field(default="azure_sora")
input_image_path: Optional[str] = Field(None, description="Veo3 image input path")
audio_generated: bool = Field(default=False, description="Audio generation flag")
```

**Context7 Enhancement Recommendation**: Implement provider-agnostic integration testing:

```python
# src/tests/test_dual_provider_integration.py
@pytest.mark.parametrize("provider", ["azure_sora", "google_veo3"])
@pytest.mark.integration
class TestDualProviderIntegration:
    """Provider-agnostic integration testing following Context7 patterns."""
    
    def test_provider_specific_workflow(self, provider, client):
        """Test complete workflow for each provider."""
        # Leverage your existing GenerationParamsFactory pattern
        from src.core.models import GenerationParamsFactory
        
        params = GenerationParamsFactory.create_for_testing(
            prompt=f"Provider test for {provider}", 
        )
        
        # Create job with specific provider
        response = client.post("/generate", data={
            "prompt": params.prompt,
            "width": params.width,
            "height": params.height,
            "duration": params.duration,
            "api_provider": provider
        })
        
        assert response.status_code == 200
        data = response.get_json()
        job_id = data["data"]["job_id"]
        
        # Verify provider-specific database fields
        from src.api.job_repository import JobRepository
        repo = JobRepository()
        job = repo.get_job_by_id(job_id)
        
        assert job.api_provider == provider
        if provider == "google_veo3":
            # Test Veo3-specific fields
            assert hasattr(job, 'input_image_path')
            assert hasattr(job, 'audio_generated')
        
    @pytest.mark.asyncio
    async def test_concurrent_dual_provider_load(self, client):
        """Test concurrent requests across both providers."""
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        
        def create_provider_request(provider, user_id):
            return client.post("/generate", data={
                "prompt": f"Concurrent {provider} test user {user_id}",
                "api_provider": provider,
                "duration": 5
            })
        
        # Create mixed provider requests
        tasks = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            # 5 Azure Sora, 5 Google Veo3
            for i in range(5):
                tasks.append(executor.submit(create_provider_request, "azure_sora", i))
                tasks.append(executor.submit(create_provider_request, "google_veo3", i+5))
            
            responses = [task.result() for task in tasks]
        
        # Verify mixed provider handling
        successful_responses = [r for r in responses if r.status_code == 200]
        assert len(successful_responses) >= 8, "Mixed provider requests failed"
        
        # Verify database distribution
        job_ids = [r.get_json()["data"]["job_id"] for r in successful_responses]
        from src.api.job_repository import JobRepository
        repo = JobRepository()
        
        providers = [repo.get_job_by_id(job_id).api_provider for job_id in job_ids]
        assert "azure_sora" in providers
        assert "google_veo3" in providers
```

### **2. Advanced WebSocket Testing Patterns**

**Current Excellence**: Your WebSocket testing is already following 2025 best practices.

**Context7 Enhancement**: Implement async WebSocket testing for dual-provider scenarios:

```python
# src/tests/test_dual_provider_websocket.py
@pytest.mark.asyncio
async def test_concurrent_provider_websocket_updates(app):
    """Test WebSocket updates for concurrent Azure Sora and Google Veo3 jobs."""
    from src.realtime.websocket import init_socketio
    
    socketio_instance = init_socketio(app)
    
    # Create separate clients for different providers
    azure_client = socketio_instance.test_client(app)
    veo3_client = socketio_instance.test_client(app)
    
    # Clear initial connection messages
    azure_client.get_received()
    veo3_client.get_received()
    
    # Subscribe to different provider job updates
    azure_client.emit('subscribe_job_updates', {'job_id': 'azure-job-123'})
    veo3_client.emit('subscribe_job_updates', {'job_id': 'veo3-job-456'})
    
    # Verify both subscriptions confirmed
    azure_received = azure_client.get_received()
    veo3_received = veo3_client.get_received()
    
    assert len(azure_received) == 1
    assert len(veo3_received) == 1
    assert azure_received[0]['name'] == 'subscription_confirmed'
    assert veo3_received[0]['name'] == 'subscription_confirmed'

@pytest.mark.asyncio
async def test_real_time_provider_status_updates(app, client):
    """Test real-time status updates for different providers."""
    from src.realtime.websocket import init_socketio
    from unittest.mock import patch
    
    socketio_instance = init_socketio(app)
    websocket_client = socketio_instance.test_client(app)
    websocket_client.get_received()  # Clear connect message
    
    # Create jobs for both providers
    azure_response = client.post("/generate", data={
        "prompt": "Azure WebSocket test",
        "api_provider": "azure_sora"
    })
    veo3_response = client.post("/generate", data={
        "prompt": "Veo3 WebSocket test", 
        "api_provider": "google_veo3"
    })
    
    azure_job_id = azure_response.get_json()["data"]["job_id"]
    veo3_job_id = veo3_response.get_json()["data"]["job_id"]
    
    # Subscribe to both jobs
    websocket_client.emit('subscribe_job_updates', {'job_id': azure_job_id})
    websocket_client.emit('subscribe_job_updates', {'job_id': veo3_job_id})
    
    # Clear subscription confirmations
    websocket_client.get_received()
    
    # Simulate status updates via broadcaster
    from src.realtime.broadcaster import get_broadcaster
    broadcaster = get_broadcaster()
    
    await broadcaster.broadcast_job_update(azure_job_id, {
        'status': 'processing',
        'provider': 'azure_sora',
        'progress': 50
    })
    
    await broadcaster.broadcast_job_update(veo3_job_id, {
        'status': 'processing', 
        'provider': 'google_veo3',
        'progress': 30
    })
    
    # Verify provider-specific updates received
    updates = websocket_client.get_received()
    assert len(updates) == 2
    
    # Verify provider information in updates
    azure_update = next(u for u in updates if u['args'][0]['data']['job_id'] == azure_job_id)
    veo3_update = next(u for u in updates if u['args'][0]['data']['job_id'] == veo3_job_id)
    
    assert azure_update['args'][0]['data']['provider'] == 'azure_sora'
    assert veo3_update['args'][0]['data']['provider'] == 'google_veo3'
```

### **3. Enhanced Locust Load Testing with Provider-Specific Scenarios**

**Current Strength**: Your load testing already handles 15+ concurrent users effectively.

**Context7 Enhancement**: Implement Locust-based provider-specific load testing:

```python
# tests/load_testing/dual_provider_locust_test.py
from locust import HttpUser, task, between
import random
import uuid

class DualProviderVideoUser(HttpUser):
    """Locust user for dual-provider load testing following Context7 patterns."""
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup user session on start."""
        self.session_id = f"locust_session_{uuid.uuid4().hex[:8]}"
        
        # Setup session for this user
        with self.client.session_transaction() as sess:
            sess['session_id'] = self.session_id
    
    @task(3)  # 60% Azure Sora requests (higher weight for primary provider)
    def azure_video_generation(self):
        """Generate video using Azure Sora provider."""
        with self.client.post("/generate", data={
            "prompt": f"Azure Sora load test {random.randint(1, 1000)}",
            "api_provider": "azure_sora",
            "duration": random.choice([3, 5, 10]),
            "width": 854,
            "height": 480
        }, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
                # Store job ID for later status checking
                data = response.json()
                if "job_id" in data.get("data", {}):
                    self.job_ids.append(data["data"]["job_id"])
            else:
                response.failure(f"Azure Sora generation failed: {response.status_code}")
    
    @task(2)  # 40% Google Veo3 requests
    def veo3_video_generation(self):
        """Generate video using Google Veo3 provider."""
        with self.client.post("/generate", data={
            "prompt": f"Google Veo3 load test {random.randint(1, 1000)}",
            "api_provider": "google_veo3",
            "duration": random.choice([3, 5, 10]),
            "input_image_path": "/test/mock_image.jpg",  # Mock image path
            "audio_generated": random.choice([True, False])
        }, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
                data = response.json()
                if "job_id" in data.get("data", {}):
                    self.job_ids.append(data["data"]["job_id"])
            else:
                response.failure(f"Veo3 generation failed: {response.status_code}")
    
    @task(1)  # Monitor job status
    def check_job_status(self):
        """Check status of previously submitted jobs."""
        if hasattr(self, 'job_ids') and self.job_ids:
            job_id = random.choice(self.job_ids)
            
            with self.client.get(f"/status/{job_id}", catch_response=True) as response:
                if response.status_code == 200:
                    response.success()
                    data = response.json()
                    if data.get("success") and data["data"]["status"] in ["succeeded", "failed"]:
                        # Remove completed jobs from tracking
                        self.job_ids.remove(job_id)
                elif response.status_code == 404:
                    # Job not found, remove from tracking
                    response.success()  # Not an error, just cleanup
                    self.job_ids.remove(job_id)
                else:
                    response.failure(f"Status check failed: {response.status_code}")
    
    @task(1)  # Check queue status
    def check_queue_status(self):
        """Check queue status for capacity planning."""
        with self.client.get("/queue/status", catch_response=True) as response:
            if response.status_code in [200, 400]:  # 400 acceptable for no session
                response.success()
            else:
                response.failure(f"Queue status failed: {response.status_code}")
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.job_ids = []

class ProviderSpecificLoadTestConfig:
    """Configuration for provider-specific load testing."""
    
    # Load test parameters based on Context7 research findings
    MAX_USERS = 50  # Based on "100 concurrent users without failures" research
    SPAWN_RATE = 5  # 5 users per second spawn rate
    RUN_TIME = "5m"  # 5 minute test duration
    
    # Performance targets from Context7 research
    MAX_RESPONSE_TIME = 500  # "sub-second responses (e.g., under 500 ms)"
    MAX_FAILURE_RATE = 0.05  # 5% max failure rate
    MIN_RPS = 10  # Minimum requests per second

# Command to run: locust -f dual_provider_locust_test.py --host=http://localhost:5001
```

### **4. Docker Compose Integration Testing Enhancement**

**Current Advantage**: Your 5-container Docker setup is production-ready.

**Context7 Enhancement**: Implement pytest-docker-compose for automated testing:

```python
# src/tests/test_docker_integration.py
import pytest
import requests
import time
from pathlib import Path

# pytest-docker-compose configuration
@pytest.fixture(scope="session")
def docker_compose_file(pytestconfig):
    """Path to docker-compose file for testing."""
    return Path(__file__).parent.parent.parent / "src/deployment/docker/docker-compose.simple.yml"

@pytest.fixture(scope="session")
def docker_compose_service_ports():
    """Configure service ports for testing."""
    return {
        "app": 5001,
        "postgres": 5432,
        "redis": 6379,
        "nginx": 8090
    }

@pytest.fixture(scope="session")
def docker_services_ready(docker_services):
    """Wait for all Docker services to be ready."""
    # Wait for app service health check
    def is_app_ready():
        try:
            response = requests.get("http://localhost:5001/health", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    docker_services.wait_until_responsive(
        timeout=120.0,  # 2 minutes timeout
        pause=2.0,      # Check every 2 seconds
        check=is_app_ready
    )
    
    # Wait for database connectivity
    def is_db_ready():
        try:
            response = requests.get("http://localhost:5001/health/database", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    docker_services.wait_until_responsive(
        timeout=60.0,
        pause=2.0,
        check=is_db_ready
    )
    
    # Wait for Redis
    def is_redis_ready():
        try:
            response = requests.get("http://localhost:5001/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get("redis", {}).get("status") == "healthy"
        except requests.exceptions.RequestException:
            return False
        return False
    
    docker_services.wait_until_responsive(
        timeout=60.0,
        pause=2.0,
        check=is_redis_ready
    )
    
    return docker_services

@pytest.mark.integration
@pytest.mark.docker
class TestDockerIntegration:
    """Integration tests using Docker Compose environment."""
    
    def test_all_services_healthy(self, docker_services_ready):
        """Test that all Docker services are healthy."""
        # Test app service
        response = requests.get("http://localhost:5001/health")
        assert response.status_code == 200
        health_data = response.json()
        
        assert health_data["status"] == "healthy"
        assert health_data["database"]["status"] == "healthy"
        assert health_data["redis"]["status"] == "healthy"
    
    def test_dual_provider_docker_workflow(self, docker_services_ready):
        """Test dual-provider workflow in Docker environment."""
        # Test Azure Sora provider
        azure_response = requests.post("http://localhost:5001/generate", data={
            "prompt": "Docker Azure test",
            "api_provider": "azure_sora"
        })
        assert azure_response.status_code == 200
        azure_job_id = azure_response.json()["data"]["job_id"]
        
        # Test Google Veo3 provider (should work with mock)
        veo3_response = requests.post("http://localhost:5001/generate", data={
            "prompt": "Docker Veo3 test",
            "api_provider": "google_veo3"
        })
        assert veo3_response.status_code == 200
        veo3_job_id = veo3_response.json()["data"]["job_id"]
        
        # Verify both jobs are tracked
        azure_status = requests.get(f"http://localhost:5001/status/{azure_job_id}")
        veo3_status = requests.get(f"http://localhost:5001/status/{veo3_job_id}")
        
        assert azure_status.status_code == 200
        assert veo3_status.status_code == 200
        
        assert azure_status.json()["data"]["api_provider"] == "azure_sora"
        assert veo3_status.json()["data"]["api_provider"] == "google_veo3"
    
    def test_nginx_load_balancer(self, docker_services_ready):
        """Test nginx load balancer functionality."""
        # Test through nginx (port 8090)
        response = requests.get("http://localhost:8090/health")
        assert response.status_code == 200
        
        # Test static file serving through nginx
        response = requests.get("http://localhost:8090/static/favicon.ico")
        # Should get 200 or 404, but not connection error
        assert response.status_code in [200, 404]
    
    def test_container_resource_limits(self, docker_services_ready):
        """Test that containers respect resource limits."""
        import docker
        
        client = docker.from_env()
        
        # Check app container resources
        app_container = client.containers.get("sora-app-simple")
        app_config = app_container.attrs["HostConfig"]
        
        # Verify memory limit (1G = ********** bytes)
        assert app_config["Memory"] == **********
        
        # Check worker container resources  
        worker_container = client.containers.get("sora-worker-simple")
        worker_config = worker_container.attrs["HostConfig"]
        
        # Verify worker memory limit (1.5G = 1610612736 bytes)
        assert worker_config["Memory"] == 1610612736
    
    @pytest.mark.slow
    def test_docker_performance_under_load(self, docker_services_ready):
        """Test Docker environment performance under load."""
        import concurrent.futures
        import time
        
        def make_request(i):
            start_time = time.time()
            response = requests.post("http://localhost:5001/generate", 
                                   data={"prompt": f"Docker load test {i}"})
            end_time = time.time()
            
            return {
                "request_id": i,
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "success": response.status_code == 200
            }
        
        # Run 20 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(make_request, i) for i in range(20)]
            results = [future.result() for future in futures]
        
        # Analyze results
        successful = [r for r in results if r["success"]]
        response_times = [r["response_time"] for r in results]
        
        # Performance assertions based on Context7 research
        assert len(successful) >= 15, "Docker environment failed under load"
        assert max(response_times) < 5.0, "Response times too slow in Docker"
        assert sum(response_times) / len(response_times) < 2.0, "Average response time too slow"

# To run Docker integration tests:
# pytest src/tests/test_docker_integration.py -m integration -m docker -v
```

## Anti-Pattern Prevention & Risk Mitigation

### **Context7-Identified Anti-Patterns to Avoid:**

1. **Testing Anti-Pattern**: "Directly testing a live API provides real-world accuracy, but it's slow, requires network stability, and can be unreliable"
   - **Your Solution**: Already implemented with mock patterns and integration test isolation ✅

2. **Docker Testing Anti-Pattern**: "Clean up your environment after each test. Containers can carry information from previous tests"
   - **Mitigation**: Implement proper test isolation with database cleanup

3. **Load Testing Anti-Pattern**: "Error rate too high" scenarios
   - **Prevention**: Your existing ThreadPoolExecutor patterns already prevent this ✅

4. **WebSocket Anti-Pattern**: Missing authorization validation
   - **Your Protection**: Already implemented with `_validate_job_ownership` ✅

## Performance Optimization Patterns

### **Context7 Performance Research Applied:**

**Current Performance Baseline** (from your load testing):
- ✅ 15+ concurrent users successfully handled
- ✅ Average response time targets: <5.0s
- ✅ Maximum response time: <10.0s
- ✅ Success rate: >10/15 requests successful

**Enhancement Recommendations:**
1. **Response Time Optimization**: Target Context7 research finding of "sub-second responses (e.g., under 500 ms)" for non-generation endpoints
2. **Sustained Load Testing**: Implement Context7 pattern of "sustained load for 30 seconds" with your existing health check patterns
3. **Provider-Specific Performance Baselines**: Establish separate performance metrics for Azure Sora vs Google Veo3

```python
# src/tests/test_performance_enhanced.py
@pytest.mark.performance
class TestProviderSpecificPerformance:
    """Enhanced performance testing for dual-provider system."""
    
    def test_provider_response_time_targets(self, client):
        """Test Context7 research target: sub-500ms for non-generation endpoints."""
        import time
        
        providers = ["azure_sora", "google_veo3"]
        
        for provider in providers:
            # Test status endpoints (should be <500ms)
            start_time = time.time()
            response = client.get("/queue/status")
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # Convert to ms
            assert response_time < 500, f"Status endpoint too slow: {response_time}ms"
            
            # Test health endpoints (should be <200ms)
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            assert response_time < 200, f"Health endpoint too slow: {response_time}ms"
    
    def test_sustained_dual_provider_load(self, client):
        """Test sustained load following Context7 30-second pattern."""
        import time
        import threading
        from collections import defaultdict
        
        results = defaultdict(list)
        start_time = time.time()
        
        def make_sustained_requests(provider):
            """Make requests for specific provider during test duration."""
            while time.time() - start_time < 30:  # 30 second test
                try:
                    request_start = time.time()
                    response = client.post("/generate", data={
                        "prompt": f"Sustained {provider} test",
                        "api_provider": provider
                    })
                    request_end = time.time()
                    
                    results[provider].append({
                        "success": response.status_code == 200,
                        "response_time": request_end - request_start
                    })
                    
                    time.sleep(0.5)  # 2 requests per second per provider
                    
                except Exception as e:
                    results[provider].append({
                        "success": False,
                        "error": str(e)
                    })
        
        # Start sustained load for both providers
        azure_thread = threading.Thread(target=make_sustained_requests, args=("azure_sora",))
        veo3_thread = threading.Thread(target=make_sustained_requests, args=("google_veo3",))
        
        azure_thread.start()
        veo3_thread.start()
        
        azure_thread.join()
        veo3_thread.join()
        
        # Analyze results per provider
        for provider in ["azure_sora", "google_veo3"]:
            provider_results = results[provider]
            successful = [r for r in provider_results if r.get("success", False)]
            
            assert len(successful) > 50, f"{provider}: Not enough successful requests in 30s"
            
            # Check response times
            response_times = [r["response_time"] for r in successful if "response_time" in r]
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
                assert avg_response_time < 2.0, f"{provider}: Average response time too slow: {avg_response_time}s"
```

## Technology-Specific Implementation Roadmap

### **Phase 1: Enhanced Dual-Provider Testing (2 weeks)**
- Implement provider-specific test parameterization
- Add Google Veo3 mock patterns integration
- Enhance existing integration tests with dual-provider scenarios
- Create provider-agnostic test utilities

### **Phase 2: Advanced Load Testing (1 week)**
- Implement Locust-based dual-provider load testing
- Add provider-specific performance baselines
- Integrate with existing ThreadPoolExecutor patterns
- Create sustained load testing scenarios

### **Phase 3: Docker Testing Automation (1 week)**
- Implement pytest-docker-compose integration
- Add automated container health validation
- Enhance CI/CD testing pipeline
- Create container resource validation tests

### **Phase 4: WebSocket Enhancement (1 week)**
- Add async WebSocket testing patterns
- Implement dual-provider real-time update testing
- Add WebSocket load testing scenarios
- Create provider-specific WebSocket event validation

## Implementation Code Examples

### **Ready-to-Use Test Fixtures**

```python
# src/tests/conftest.py - Enhanced dual-provider fixtures
@pytest.fixture
def dual_provider_client(app):
    """Enhanced test client with dual-provider support."""
    with app.test_client() as client:
        with app.app_context():
            # Initialize both providers
            from src.features.video_generation.provider_factory import VideoProviderFactory
            factory = VideoProviderFactory()
            
            # Ensure both providers are available
            azure_provider = factory.create_provider("azure_sora")
            veo3_provider = factory.create_provider("google_veo3")  # Will use mock
            
            yield client

@pytest.fixture
def provider_test_data():
    """Test data for different providers."""
    return {
        "azure_sora": {
            "prompt": "Azure Sora test video",
            "duration": 5,
            "width": 854,
            "height": 480
        },
        "google_veo3": {
            "prompt": "Google Veo3 test video",
            "duration": 5,
            "input_image_path": "/test/mock_image.jpg",
            "audio_generated": True
        }
    }

@pytest.fixture(scope="session")
def locust_performance_config():
    """Performance configuration based on Context7 research."""
    return {
        "max_users": 50,
        "spawn_rate": 5,
        "run_time": "5m",
        "max_response_time_ms": 500,
        "max_failure_rate": 0.05,
        "min_rps": 10
    }
```

## Conclusion

Your system already implements many 2025 best practices for Flask pytest integration testing. The recommended enhancements focus on:

1. **Dual-Provider Specialization**: Leveraging existing architecture for provider-specific testing
2. **Performance Optimization**: Applying Context7 research findings to achieve sub-second response times
3. **Docker Integration**: Automating container-based testing with pytest-docker-compose
4. **Advanced Load Testing**: Implementing Locust patterns for realistic user behavior simulation

**Implementation Priority**: Focus on Phase 1 (dual-provider testing) as it provides the highest value for the Google Veo3 integration project.

**Technology Stack Confidence Level**: High (9/10)  
**Implementation Readiness**: Excellent - existing patterns provide strong foundation  
**Context7 Research Applied**: Comprehensive - all major technology areas covered

---

**Next Steps:**
1. Implement dual-provider test parameterization using existing infrastructure
2. Add Locust-based load testing for realistic concurrent user simulation
3. Enhance Docker integration testing with pytest-docker-compose
4. Create provider-specific performance baselines and monitoring

**Success Metrics:**
- ✅ Sub-500ms response times for non-generation endpoints
- ✅ 50+ concurrent users across both providers
- ✅ 95%+ success rate under sustained load
- ✅ Comprehensive dual-provider test coverage