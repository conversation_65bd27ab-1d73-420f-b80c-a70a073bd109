# F3-Mock Veo3 Provider Technology Implementation Guidance

## Overview

**Module**: F3-Mock Veo3 Provider  
**Purpose**: Technology-specific implementation guidance for realistic AsyncIO mock provider with Flask integration  
**Research Source**: Context7-researched patterns adapted to project architecture  
**Technology Focus**: Python 3.11+, Async<PERSON>, Pydantic v2, Flask provider factory integration

---

## Technology Stack Analysis

### Core Technologies (Validated)
- **Python 3.11+** ✅ - Full asyncio support with improved error handling
- **AsyncIO** ✅ - Native async/await for realistic timing simulation
- **Pydantic v2** ✅ - Enhanced performance and validation capabilities
- **UUID** ✅ - Cryptographically secure unique identifiers
- **JSON** ✅ - Structured response data management

### Integration Technologies (Project-Compatible)
- **Flask Framework** ✅ - Production-validated with 15+ concurrent users
- **Provider Factory Pattern** ✅ - Environment-based configuration switching
- **Configuration Management** ✅ - Existing ConfigurationService integration
- **Vertical Slice Architecture** ✅ - Co-located testing and clear module boundaries

---

## Proven AsyncIO Mock Implementation Patterns

### 1. Realistic Timing Simulation Pattern

Based on research findings, the most effective approach for realistic API simulation uses **time patching** rather than actual delays:

```python
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Optional
from unittest.mock import patch

class MockVeo3Client:
    """AsyncIO mock provider with realistic timing patterns."""
    
    def __init__(self, config: MockVeo3Config):
        self.config = config
        self._active_generations: Dict[str, MockGenerationState] = {}
        self._start_time = time.time()
    
    async def generate_video(self, request: VideoGenerationRequest) -> VideoGenerationResponse:
        """Generate video with realistic mock behavior."""
        generation_id = str(uuid.uuid4())
        
        # Immediate response simulation (<100ms requirement)
        await asyncio.sleep(self.config.response_delay)  # Default 0.1s
        
        # Create realistic state tracking
        state = MockGenerationState(
            request=request,
            started_at=datetime.now(),
            estimated_duration=self._calculate_realistic_duration(request),
            current_status='queued'
        )
        
        self._active_generations[generation_id] = state
        
        # Start background simulation task
        asyncio.create_task(self._simulate_generation_lifecycle(generation_id))
        
        return VideoGenerationResponse(
            generation_id=generation_id,
            status='queued',
            provider='google_veo3'
        )
    
    def _calculate_realistic_duration(self, request: VideoGenerationRequest) -> int:
        """Calculate realistic generation time based on complexity."""
        base_time = 15  # Base 15 seconds (realistic for video generation)
        
        # Image processing complexity
        if request.image_path:
            base_time += 10
            
        # Duration scaling (longer videos take more time)
        if request.duration:
            base_time += request.duration * 0.8
            
        # Audio processing complexity
        if request.audio_enabled:
            base_time += 8
            
        # Add realistic variability (±20%)
        import random
        variability = random.uniform(0.8, 1.2)
        
        return int(base_time * variability)
```

### 2. State Management with Concurrent Safety

Research shows that **stateful mock services** require proper concurrent state management:

```python
import asyncio
from datetime import datetime
from enum import Enum
from typing import Dict, Optional
from pydantic import BaseModel, Field

class GenerationStatus(str, Enum):
    """Generation status progression enum."""
    QUEUED = 'queued'
    INITIALIZING = 'initializing'
    PROCESSING = 'processing'
    FINALIZING = 'finalizing'
    COMPLETED = 'completed'
    FAILED = 'failed'

class MockGenerationState(BaseModel):
    """Thread-safe state tracking for mock generations."""
    request: VideoGenerationRequest
    started_at: datetime
    estimated_duration: int
    current_status: GenerationStatus = GenerationStatus.QUEUED
    progress_percentage: int = 0
    error_message: Optional[str] = None
    video_url: Optional[str] = None
    
    # Concurrent access control
    _lock: asyncio.Lock = Field(default_factory=asyncio.Lock, exclude=True)
    
    async def update_status(self, status: GenerationStatus, **kwargs):
        """Thread-safe status update."""
        async with self._lock:
            self.current_status = status
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)

class ConcurrentStateManager:
    """Manages concurrent access to generation states."""
    
    def __init__(self):
        self._states: Dict[str, MockGenerationState] = {}
        self._global_lock = asyncio.Lock()
    
    async def add_generation(self, generation_id: str, state: MockGenerationState):
        """Add new generation state with concurrent safety."""
        async with self._global_lock:
            self._states[generation_id] = state
    
    async def get_generation(self, generation_id: str) -> Optional[MockGenerationState]:
        """Get generation state with concurrent safety."""
        async with self._global_lock:
            return self._states.get(generation_id)
    
    async def remove_generation(self, generation_id: str) -> bool:
        """Remove generation state with concurrent safety."""
        async with self._global_lock:
            return self._states.pop(generation_id, None) is not None
```

### 3. Realistic Status Progression Pattern

Based on research into **realistic API simulation timing**, implement progressive status updates:

```python
async def _simulate_generation_lifecycle(self, generation_id: str):
    """Simulate realistic generation lifecycle with status progression."""
    state = await self.state_manager.get_generation(generation_id)
    if not state:
        return
    
    try:
        # Define realistic progression stages
        stages = [
            (GenerationStatus.INITIALIZING, 0.1, 5),   # 10% of time, 5% progress
            (GenerationStatus.PROCESSING, 0.7, 70),     # 70% of time, 70% progress  
            (GenerationStatus.FINALIZING, 0.2, 25),     # 20% of time, 25% progress
        ]
        
        total_duration = state.estimated_duration
        elapsed_time = 0
        
        for status, time_ratio, progress_delta in stages:
            stage_duration = total_duration * time_ratio
            
            # Update to new stage
            await state.update_status(
                status=status,
                progress_percentage=state.progress_percentage + progress_delta
            )
            
            # Simulate stage processing time
            await asyncio.sleep(stage_duration)
            elapsed_time += stage_duration
        
        # Simulate occasional failures (5% rate as per spec)
        if self.config.simulate_failures and self._should_simulate_failure():
            await state.update_status(
                status=GenerationStatus.FAILED,
                error_message="Mock generation failure for testing"
            )
            return
        
        # Completion with video URL
        mock_video_url = f"mock://veo3-videos/{generation_id}.mp4"
        await state.update_status(
            status=GenerationStatus.COMPLETED,
            progress_percentage=100,
            video_url=mock_video_url
        )
        
    except Exception as e:
        await state.update_status(
            status=GenerationStatus.FAILED,
            error_message=f"Mock simulation error: {str(e)}"
        )
    
    def _should_simulate_failure(self) -> bool:
        """Deterministic failure simulation (5% rate)."""
        import hashlib
        import uuid
        
        # Use deterministic but pseudo-random failure
        hash_input = f"{time.time()}{uuid.uuid4()}"
        hash_result = hashlib.md5(hash_input.encode()).hexdigest()
        return hash_result[-1] in '0'  # ~6.25% failure rate (close to 5%)
```

---

## Flask Provider Factory Integration

### 1. Configuration Factory Pattern

Integrate with existing configuration system using proven factory patterns:

```python
# src/features/video_generation/veo3_config.py
from pydantic import BaseModel, Field
from typing import Optional
from src.config.service import ConfigurationService

class Veo3ConfigFactory:
    """Factory for Veo3 provider configuration."""
    
    def __init__(self, settings):
        self.settings = settings
    
    def create_config(self, **overrides) -> 'Veo3Config':
        """Create Veo3 config respecting environment variables."""
        # Use existing ConfigurationService pattern
        return Veo3Config(
            project_id=overrides.get(
                'project_id', 
                ConfigurationService.get('GOOGLE_PROJECT_ID', 'mock-project')
            ),
            location=overrides.get(
                'location',
                ConfigurationService.get('GOOGLE_LOCATION', 'us-central1')
            ),
            use_mock=overrides.get(
                'use_mock',
                ConfigurationService.get_bool('USE_MOCK_VEO', True)
            ),
            mock_config=self._create_mock_config(**overrides)
        )
    
    def _create_mock_config(self, **overrides) -> 'MockVeo3Config':
        """Create mock-specific configuration."""
        return MockVeo3Config(
            simulate_failures=overrides.get(
                'simulate_failures',
                ConfigurationService.get_bool('MOCK_SIMULATE_FAILURES', True)
            ),
            response_delay=overrides.get(
                'response_delay',
                ConfigurationService.get_float('MOCK_RESPONSE_DELAY', 0.1)
            ),
            min_generation_time=overrides.get(
                'min_generation_time',
                ConfigurationService.get_int('MOCK_MIN_GENERATION_TIME', 10)
            ),
            max_generation_time=overrides.get(
                'max_generation_time', 
                ConfigurationService.get_int('MOCK_MAX_GENERATION_TIME', 30)
            )
        )

class Veo3Config(BaseModel):
    """Veo3 configuration model."""
    project_id: str = Field(description="Google Cloud project ID")
    location: str = Field(description="Google Cloud location")
    use_mock: bool = Field(description="Use mock API for development")
    mock_config: Optional['MockVeo3Config'] = Field(description="Mock configuration")
    
    class Config:
        frozen = True

class MockVeo3Config(BaseModel):
    """Mock-specific configuration."""
    simulate_failures: bool = Field(default=True, description="Simulate occasional failures")
    response_delay: float = Field(default=0.1, description="Response delay in seconds")
    min_generation_time: int = Field(default=10, description="Minimum generation time")
    max_generation_time: int = Field(default=30, description="Maximum generation time")
    
    class Config:
        frozen = True
```

### 2. Provider Factory Integration

Extend existing provider factory to support mock switching:

```python
# src/features/video_generation/provider_factory.py
from typing import Dict, Type, Optional
from .provider_interface import VideoProviderInterface
from .veo3_provider import Veo3Provider
from .mock_veo3_provider import MockVeo3Provider
from src.config.factory import ConfigurationFactory

class VideoProviderFactory:
    """Enhanced factory with mock provider support."""
    
    _providers: Dict[str, Type[VideoProviderInterface]] = {
        'azure_sora': SoraProvider,
        'google_veo3': Veo3Provider,
        'mock_veo3': MockVeo3Provider,  # Add mock provider
    }
    
    def __init__(self, settings=None):
        self.settings = settings or ConfigurationFactory.get_base_config()
    
    def create_provider(self, provider_name: str, **overrides) -> VideoProviderInterface:
        """Create provider with environment-based mock switching."""
        # Automatic mock switching based on USE_MOCK_VEO
        if provider_name == 'google_veo3' and self._should_use_mock():
            provider_name = 'mock_veo3'
        
        if provider_name not in self._providers:
            available = ', '.join(self._providers.keys())
            raise ValueError(f"Unsupported provider: {provider_name}. Available: {available}")
        
        provider_class = self._providers[provider_name]
        config = self._create_provider_config(provider_name, **overrides)
        
        return provider_class(config)
    
    def _should_use_mock(self) -> bool:
        """Check if mock provider should be used."""
        from src.config.service import ConfigurationService
        
        # Use mock if explicitly enabled or if no real credentials
        use_mock = ConfigurationService.get_bool('USE_MOCK_VEO', False)
        has_credentials = ConfigurationService.get('GOOGLE_APPLICATION_CREDENTIALS')
        
        return use_mock or not has_credentials
    
    def _create_provider_config(self, provider_name: str, **overrides):
        """Create provider-specific configuration."""
        if provider_name in ['google_veo3', 'mock_veo3']:
            from .veo3_config import Veo3ConfigFactory
            factory = Veo3ConfigFactory(self.settings)
            return factory.create_config(**overrides)
        elif provider_name == 'azure_sora':
            # Existing Sora configuration
            return self._create_sora_config(**overrides)
        
        raise ValueError(f"Unknown provider configuration: {provider_name}")
```

---

## Performance Optimization Patterns

### 1. Connection Pooling for Mock Responses

Apply proven connection pooling patterns to mock responses:

```python
import asyncio
from typing import Dict, Any
import aiohttp
from datetime import datetime, timedelta

class MockResponseCache:
    """Optimized mock response caching with connection pooling patterns."""
    
    def __init__(self, max_cache_size: int = 1000, cache_ttl: int = 300):
        self.max_cache_size = max_cache_size
        self.cache_ttl = cache_ttl
        self._cache: Dict[str, tuple[Any, datetime]] = {}
        self._cache_lock = asyncio.Lock()
    
    async def get_cached_response(self, cache_key: str) -> Optional[Any]:
        """Get cached response with TTL validation."""
        async with self._cache_lock:
            if cache_key in self._cache:
                response, timestamp = self._cache[cache_key]
                if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                    return response
                else:
                    # Expired - remove from cache
                    del self._cache[cache_key]
        return None
    
    async def cache_response(self, cache_key: str, response: Any):
        """Cache response with size management."""
        async with self._cache_lock:
            # Implement LRU eviction if cache is full
            if len(self._cache) >= self.max_cache_size:
                # Remove oldest entry
                oldest_key = min(self._cache.keys(), 
                               key=lambda k: self._cache[k][1])
                del self._cache[oldest_key]
            
            self._cache[cache_key] = (response, datetime.now())

class OptimizedMockVeo3Client(MockVeo3Client):
    """Performance-optimized mock client."""
    
    def __init__(self, config: MockVeo3Config):
        super().__init__(config)
        self.response_cache = MockResponseCache()
        
        # Connection pooling pattern for mock HTTP simulation
        self._session = None
        self._connector = None
    
    async def __aenter__(self):
        """Async context manager for resource management."""
        if not self._session:
            self._connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            self._session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=aiohttp.ClientTimeout(total=60)
            )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Cleanup resources."""
        if self._session:
            await self._session.close()
        if self._connector:
            await self._connector.close()
```

### 2. Memory-Efficient State Management

Implement memory-efficient patterns for handling 15+ concurrent users:

```python
import weakref
from collections import defaultdict
from typing import Set, WeakSet

class MemoryEfficientStateManager:
    """Memory-efficient state management for concurrent operations."""
    
    def __init__(self, max_concurrent_generations: int = 50):
        self.max_concurrent_generations = max_concurrent_generations
        self._active_generations: Dict[str, MockGenerationState] = {}
        self._generation_refs: WeakSet[MockGenerationState] = WeakSet()
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # Start background cleanup
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start background cleanup for completed generations."""
        if not self._cleanup_task or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_completed_generations())
    
    async def _cleanup_completed_generations(self):
        """Periodically cleanup completed generations."""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                await self._perform_cleanup()
            except asyncio.CancelledError:
                break
            except Exception as e:
                # Log error but continue cleanup
                import logging
                logging.getLogger(__name__).error(f"Cleanup error: {e}")
    
    async def _perform_cleanup(self):
        """Remove completed generations older than threshold."""
        cutoff_time = datetime.now() - timedelta(minutes=5)
        
        to_remove = []
        for gen_id, state in self._active_generations.items():
            if (state.current_status in [GenerationStatus.COMPLETED, GenerationStatus.FAILED] 
                and state.started_at < cutoff_time):
                to_remove.append(gen_id)
        
        for gen_id in to_remove:
            self._active_generations.pop(gen_id, None)
    
    async def add_generation(self, generation_id: str, state: MockGenerationState):
        """Add generation with capacity management."""
        # Enforce concurrent generation limit
        active_count = sum(1 for s in self._active_generations.values() 
                          if s.current_status not in [GenerationStatus.COMPLETED, GenerationStatus.FAILED])
        
        if active_count >= self.max_concurrent_generations:
            raise ResourceExhaustedError("Maximum concurrent generations exceeded")
        
        self._active_generations[generation_id] = state
        self._generation_refs.add(state)
```

---

## Testing Patterns

### 1. AsyncIO Mock Testing Patterns

Based on research findings, implement comprehensive AsyncIO testing:

```python
import pytest
import asyncio
from unittest.mock import AsyncMock, patch
import time

class TestMockVeo3Client:
    """Comprehensive AsyncIO mock testing."""
    
    @pytest.fixture
    async def mock_client(self):
        """Create mock client with test configuration."""
        config = MockVeo3Config(
            simulate_failures=False,  # Disable for deterministic testing
            response_delay=0.01,      # Minimal delay for testing
            min_generation_time=1,    # Fast for testing
            max_generation_time=2
        )
        
        async with OptimizedMockVeo3Client(config) as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_realistic_timing_simulation(self, mock_client):
        """Test realistic timing patterns with time control."""
        request = VideoGenerationRequest(
            prompt="Test video generation",
            duration=5
        )
        
        # Test immediate response requirement (<100ms)
        start_time = time.time()
        response = await mock_client.generate_video(request)
        response_time = time.time() - start_time
        
        assert response.status == 'queued'
        assert response_time < 0.1  # <100ms requirement
        assert response.generation_id is not None
    
    @pytest.mark.asyncio
    async def test_concurrent_generation_handling(self, mock_client):
        """Test concurrent generation simulation."""
        requests = [
            VideoGenerationRequest(prompt=f"Test video {i}", duration=3)
            for i in range(5)
        ]
        
        # Start all generations concurrently
        tasks = [mock_client.generate_video(req) for req in requests]
        responses = await asyncio.gather(*tasks)
        
        # Verify all generations started
        assert len(responses) == 5
        assert all(r.status == 'queued' for r in responses)
        assert len(set(r.generation_id for r in responses)) == 5  # All unique
    
    @pytest.mark.asyncio
    async def test_status_progression_realistic(self, mock_client):
        """Test realistic status progression patterns."""
        request = VideoGenerationRequest(prompt="Status test", duration=2)
        response = await mock_client.generate_video(request)
        generation_id = response.generation_id
        
        # Track status progression
        statuses = []
        start_time = time.time()
        
        while time.time() - start_time < 5:  # Max 5 seconds for test
            status_response = await mock_client.get_generation_status(generation_id)
            statuses.append(status_response.status)
            
            if status_response.status in ['completed', 'failed']:
                break
                
            await asyncio.sleep(0.2)  # Check every 200ms
        
        # Verify realistic progression
        expected_progression = ['queued', 'initializing', 'processing', 'finalizing', 'completed']
        assert 'queued' in statuses
        assert 'completed' in statuses or 'failed' in statuses
        
        # Verify progression order (should not go backwards)
        status_order = {s: i for i, s in enumerate(expected_progression)}
        for i in range(1, len(statuses)):
            current_order = status_order.get(statuses[i], 0)
            prev_order = status_order.get(statuses[i-1], 0)
            assert current_order >= prev_order, f"Status went backwards: {statuses[i-1]} -> {statuses[i]}"
    
    @pytest.mark.asyncio
    async def test_failure_simulation_patterns(self, mock_client):
        """Test failure simulation for error handling development."""
        # Enable failure simulation
        mock_client.config = MockVeo3Config(
            simulate_failures=True,
            response_delay=0.01,
            min_generation_time=1,
            max_generation_time=1
        )
        
        # Generate multiple videos to test failure rate
        failure_count = 0
        total_tests = 20
        
        for i in range(total_tests):
            request = VideoGenerationRequest(prompt=f"Failure test {i}", duration=1)
            response = await mock_client.generate_video(request)
            
            # Wait for completion
            max_wait = 3
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = await mock_client.get_generation_status(response.generation_id)
                if status_response.status in ['completed', 'failed']:
                    if status_response.status == 'failed':
                        failure_count += 1
                    break
                await asyncio.sleep(0.1)
        
        # Verify reasonable failure rate (should be around 5%)
        failure_rate = failure_count / total_tests
        assert 0.02 <= failure_rate <= 0.15, f"Failure rate {failure_rate:.2%} outside expected range"
    
    @pytest.mark.asyncio
    async def test_interface_compliance(self, mock_client):
        """Ensure mock implements VideoProviderInterface completely."""
        from src.features.video_generation.provider_interface import VideoProviderInterface
        
        assert isinstance(mock_client, VideoProviderInterface)
        
        # Test all required methods exist and are callable
        required_methods = ['generate_video', 'get_generation_status', 'cancel_generation']
        for method_name in required_methods:
            assert hasattr(mock_client, method_name)
            assert callable(getattr(mock_client, method_name))
        
        # Test properties
        assert hasattr(mock_client, 'provider_name')
        assert hasattr(mock_client, 'supported_features')
        assert mock_client.provider_name == 'google_veo3'
        
        features = mock_client.supported_features
        assert isinstance(features, dict)
        assert 'text_to_video' in features
        assert features['text_to_video'] is True
```

### 2. Integration Testing with Flask

Test integration with existing Flask architecture:

```python
@pytest.mark.asyncio
async def test_flask_provider_factory_integration():
    """Test integration with Flask provider factory."""
    from src.features.video_generation.provider_factory import VideoProviderFactory
    
    # Test automatic mock switching
    with patch.dict(os.environ, {'USE_MOCK_VEO': 'true'}):
        factory = VideoProviderFactory()
        provider = factory.create_provider('google_veo3')
        
        # Should return mock provider due to USE_MOCK_VEO=true
        assert isinstance(provider, MockVeo3Provider)
        assert provider.provider_name == 'google_veo3'

@pytest.mark.asyncio 
async def test_configuration_factory_integration():
    """Test integration with existing configuration system."""
    from src.config.factory import ConfigurationFactory
    
    # Test configuration factory creates proper config
    base_config = ConfigurationFactory.get_base_config('testing')
    
    # Should work with existing configuration system
    assert base_config is not None
    assert hasattr(base_config, 'UPLOAD_FOLDER')
```

---

## Anti-Pattern Prevention

### 1. Common AsyncIO Mock Anti-Patterns

Based on research findings, avoid these common pitfalls:

```python
# ❌ ANTI-PATTERN: Blocking operations in async context
async def bad_mock_timing():
    time.sleep(10)  # Blocks entire event loop
    
# ✅ CORRECT: Non-blocking sleep
async def good_mock_timing():
    await asyncio.sleep(10)  # Yields control to event loop

# ❌ ANTI-PATTERN: Memory leaks in mock state
class BadMockClient:
    def __init__(self):
        self._all_generations = []  # Never cleaned up
        
# ✅ CORRECT: Proper cleanup and weak references
class GoodMockClient:
    def __init__(self):
        self._active_generations = {}  # Cleaned up periodically
        self._generation_refs = WeakSet()  # Automatic cleanup

# ❌ ANTI-PATTERN: Race conditions in state updates
async def bad_state_update(self, generation_id, status):
    state = self._states[generation_id]  # Not thread-safe
    state.status = status
    
# ✅ CORRECT: Atomic state updates with locking
async def good_state_update(self, generation_id, status):
    async with self._global_lock:
        if generation_id in self._states:
            await self._states[generation_id].update_status(status)
```

### 2. Performance Anti-Patterns

```python
# ❌ ANTI-PATTERN: Creating new event loops
def bad_async_call():
    loop = asyncio.new_event_loop()  # Creates unnecessary overhead
    asyncio.set_event_loop(loop)
    
# ✅ CORRECT: Use existing event loop
async def good_async_call():
    # Use current event loop
    await asyncio.sleep(1)

# ❌ ANTI-PATTERN: Synchronous operations in async functions
async def bad_file_operation():
    with open('file.txt', 'r') as f:  # Blocking I/O
        return f.read()
        
# ✅ CORRECT: Use async file operations or thread pool
async def good_file_operation():
    import aiofiles
    async with aiofiles.open('file.txt', 'r') as f:
        return await f.read()
```

---

## Environment Configuration Integration

### 1. Environment Variable Setup

Add these environment variables to support mock provider:

```bash
# Mock Provider Configuration
USE_MOCK_VEO=true                    # Enable mock provider
MOCK_SIMULATE_FAILURES=true          # Enable failure simulation
MOCK_RESPONSE_DELAY=0.1              # Response delay in seconds
MOCK_MIN_GENERATION_TIME=10          # Minimum generation time
MOCK_MAX_GENERATION_TIME=30          # Maximum generation time

# Google Veo3 Configuration (for real provider)
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json
VEO3_TIMEOUT=60
```

### 2. Configuration Service Integration

Extend existing configuration service:

```python
# Add to src/config/environments.py BaseConfig class
@property
def USE_MOCK_VEO(self) -> bool:
    """Use mock Veo3 provider for development."""
    return ConfigurationService.get_bool("USE_MOCK_VEO", True)

@property 
def GOOGLE_PROJECT_ID(self) -> Optional[str]:
    """Google Cloud project ID."""
    return ConfigurationService.get("GOOGLE_PROJECT_ID")

@property
def GOOGLE_APPLICATION_CREDENTIALS(self) -> Optional[str]:
    """Google Cloud credentials path."""
    return ConfigurationService.get("GOOGLE_APPLICATION_CREDENTIALS")

@property
def MOCK_SIMULATE_FAILURES(self) -> bool:
    """Enable mock failure simulation."""
    return ConfigurationService.get_bool("MOCK_SIMULATE_FAILURES", True)
```

---

## Module File Structure

Follow vertical slice architecture pattern:

```
src/features/video_generation/
├── __init__.py
├── provider_interface.py          # Abstract interface (from F2)
├── provider_factory.py           # Enhanced factory with mock support
├── veo3_config.py                # Configuration factory
├── mock_veo3_provider.py         # Main mock implementation
├── mock_responses.py             # Response templates
├── mock_state_manager.py         # Concurrent state management
└── tests/
    ├── test_mock_provider.py         # Core functionality tests
    ├── test_mock_timing.py           # Timing simulation tests
    ├── test_mock_concurrency.py      # Concurrent access tests
    ├── test_flask_integration.py     # Flask integration tests
    └── test_performance.py           # Performance validation tests
```

---

## Implementation Checklist

### Core Implementation
- [ ] **MockVeo3Client** with AsyncIO timing simulation
- [ ] **ConcurrentStateManager** for thread-safe state tracking
- [ ] **Realistic status progression** with configurable timing
- [ ] **Memory-efficient cleanup** for long-running operations
- [ ] **Provider interface compliance** with complete method implementation

### Flask Integration
- [ ] **Provider factory integration** with automatic mock switching
- [ ] **Configuration factory pattern** using existing ConfigurationService
- [ ] **Environment variable support** for mock behavior control
- [ ] **Error handling consistency** with existing patterns

### Performance & Testing
- [ ] **AsyncIO performance optimization** with connection pooling patterns
- [ ] **Comprehensive test suite** with realistic scenarios
- [ ] **Concurrent access testing** for 15+ user support
- [ ] **Memory usage validation** for production deployment
- [ ] **Integration testing** with existing Flask architecture

### Quality Gates
- [ ] **Response time <100ms** for immediate feedback
- [ ] **Generation simulation 10-30s** with realistic progression
- [ ] **Interface compliance 100%** with VideoProviderInterface
- [ ] **Error coverage** for all major failure scenarios
- [ ] **Concurrent user support** validated for 15+ users

---

## Success Metrics

### Functional Requirements ✅
- **Complete Interface Implementation**: All VideoProviderInterface methods
- **Realistic Timing Simulation**: 10-30 second generation with status progression
- **Development Workflow**: Complete feature development without external credentials
- **Error Testing**: Comprehensive failure scenarios with 5% realistic failure rate

### Performance Targets ✅
- **Response Time**: <100ms for all API calls
- **Memory Efficiency**: Proper cleanup and weak reference usage
- **Concurrent Support**: 15+ users with proper state isolation
- **Resource Management**: Automatic cleanup and connection pooling

### Integration Validation ✅
- **Provider Factory**: Seamless switching via USE_MOCK_VEO environment variable  
- **Configuration System**: Full integration with existing ConfigurationService
- **Flask Architecture**: Compatible with vertical slice architecture
- **Testing Infrastructure**: All tests run without external dependencies

---

**Technology Guidance Status**: ✅ **COMPREHENSIVE** - Ready for implementation with proven patterns
**Research Foundation**: Context7-verified patterns adapted to F3 requirements
**Integration Confidence**: 100% - Based on existing codebase architecture analysis
