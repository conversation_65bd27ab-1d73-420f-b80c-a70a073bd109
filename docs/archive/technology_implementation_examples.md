# Technology Implementation Examples

**Purpose**: Demonstrate how universal architectural interpretation rules apply across different technology stacks while maintaining framework consistency.

## Framework Principle Application

All examples follow the same universal architectural interpretation rules:
- **Component Consolidation**: Related capabilities belong to one implementation unit
- **Implementation Minimization**: Avoid unnecessary component proliferation  
- **Technology-Agnostic Mapping**: Universal patterns adapt to technology-specific implementations

## Technology Stack Examples

### Python AI Systems (Pydantic AI + FastAPI)

**System Architecture**: Memory Agent System with capabilities: assess_relevance, store_episode, search_memories

**CORRECT Implementation** (Follows consolidation rules):
```python
# Single agent with consolidated capabilities
from pydantic_ai import Agent
from typing import Dict, List, Any

# Module-global agent instance (development standards compliance)
memory_agent = Agent(
    model="claude-3-sonnet-20240229",
    system_prompt="You are the Memory Agent...",
    deps_type=MemoryAgentDependencies
)

@memory_agent.tool
async def assess_relevance(
    ctx: RunContext[MemoryAgentDependencies],
    content: str,
    context: Dict[str, Any]
) -> Dict[str, Any]:
    """Capability: Assess memory relevance for storage decisions."""
    return {"should_store": True, "confidence": 0.85}

@memory_agent.tool
async def store_episode(
    ctx: RunContext[MemoryAgentDependencies],
    episode_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Capability: Store memory episode in knowledge graph."""
    graphiti_client = ctx.deps.graphiti_client
    episode_id = await graphiti_client.add_episode(episode_data)
    return {"episode_id": episode_id, "success": True}

@memory_agent.tool
async def search_memories(
    ctx: RunContext[MemoryAgentDependencies],
    query: str,
    limit: int = 10
) -> List[Dict[str, Any]]:
    """Capability: Search memories using semantic similarity."""
    graphiti_client = ctx.deps.graphiti_client
    results = await graphiti_client.search(query)
    return results[:limit]
```

**WRONG Implementation** (Component proliferation anti-pattern):
```python
# ❌ AVOID: Separate agents for each capability
memory_intelligence_agent = Agent(...)  # Assesses relevance
conversation_analysis_agent = Agent(...) # Analyzes conversations  
relevance_assessment_agent = Agent(...)  # Duplicate functionality

# This violates consolidation rules - 3 components for 1 system function
```

### Node.js API Services (Express + TypeScript)

**System Architecture**: Same Memory Agent System with same capabilities

**CORRECT Implementation** (Service with methods):
```typescript
// Single service class with consolidated capabilities
import { GraphitiClient } from './graphiti-client';
import { MemoryEpisode, SearchResult } from './types';

export class MemoryAgentService {
  private graphitiClient: GraphitiClient;
  
  constructor(graphitiClient: GraphitiClient) {
    this.graphitiClient = graphitiClient;
  }

  // Capability: Assess memory relevance
  async assessRelevance(
    content: string, 
    context: Record<string, any>
  ): Promise<{shouldStore: boolean; confidence: number}> {
    // Intelligence logic here
    return { shouldStore: true, confidence: 0.85 };
  }

  // Capability: Store memory episode
  async storeEpisode(episodeData: MemoryEpisode): Promise<{episodeId: string; success: boolean}> {
    const episodeId = await this.graphitiClient.addEpisode(episodeData);
    return { episodeId, success: true };
  }

  // Capability: Search memories
  async searchMemories(query: string, limit: number = 10): Promise<SearchResult[]> {
    const results = await this.graphitiClient.search(query);
    return results.slice(0, limit);
  }
}

// Express route integration
app.post('/memory/assess', async (req, res) => {
  const result = await memoryService.assessRelevance(req.body.content, req.body.context);
  res.json(result);
});
```

**WRONG Implementation** (Service proliferation anti-pattern):
```typescript
// ❌ AVOID: Separate services for each capability
class MemoryIntelligenceService { ... }
class ConversationAnalysisService { ... }
class RelevanceAssessmentService { ... }

// This violates consolidation rules - 3 services for 1 system function
```

### Go Microservices (Gin + GORM)

**System Architecture**: Same Memory Agent System with same capabilities

**CORRECT Implementation** (Package with functions):
```go
// Single package with consolidated capabilities
package memoryagent

import (
    "context"
    "github.com/your-org/graphiti-client"
)

type MemoryAgent struct {
    graphitiClient *graphiti.Client
    logger         *log.Logger
}

func NewMemoryAgent(client *graphiti.Client) *MemoryAgent {
    return &MemoryAgent{
        graphitiClient: client,
        logger:         log.New(os.Stdout, "memory-agent: ", log.LstdFlags),
    }
}

// Capability: Assess memory relevance
func (ma *MemoryAgent) AssessRelevance(
    ctx context.Context, 
    content string, 
    contextData map[string]interface{},
) (*RelevanceResult, error) {
    // Intelligence logic here
    return &RelevanceResult{
        ShouldStore: true,
        Confidence:  0.85,
    }, nil
}

// Capability: Store memory episode
func (ma *MemoryAgent) StoreEpisode(
    ctx context.Context,
    episode *MemoryEpisode,
) (*StoreResult, error) {
    episodeID, err := ma.graphitiClient.AddEpisode(ctx, episode)
    if err != nil {
        return nil, err
    }
    return &StoreResult{EpisodeID: episodeID, Success: true}, nil
}

// Capability: Search memories
func (ma *MemoryAgent) SearchMemories(
    ctx context.Context,
    query string,
    limit int,
) ([]*SearchResult, error) {
    results, err := ma.graphitiClient.Search(ctx, query)
    if err != nil {
        return nil, err
    }
    
    if len(results) > limit {
        results = results[:limit]
    }
    return results, nil
}
```

**WRONG Implementation** (Package proliferation anti-pattern):
```go
// ❌ AVOID: Separate packages for each capability
package memoryintelligence  // Separate package for assessment
package conversationanalysis // Separate package for analysis
package relevanceassessment  // Separate package for relevance

// This violates consolidation rules - 3 packages for 1 system function
```

### Java Enterprise (Spring Boot + JPA)

**System Architecture**: Same Memory Agent System with same capabilities

**CORRECT Implementation** (Service bean with methods):
```java
// Single service with consolidated capabilities
@Service
@Transactional
public class MemoryAgentService {
    
    private final GraphitiClient graphitiClient;
    private final Logger logger = LoggerFactory.getLogger(MemoryAgentService.class);
    
    @Autowired
    public MemoryAgentService(GraphitiClient graphitiClient) {
        this.graphitiClient = graphitiClient;
    }
    
    // Capability: Assess memory relevance
    public RelevanceResult assessRelevance(String content, Map<String, Object> context) {
        // Intelligence logic here
        return RelevanceResult.builder()
            .shouldStore(true)
            .confidence(0.85)
            .build();
    }
    
    // Capability: Store memory episode
    public StoreResult storeEpisode(MemoryEpisode episodeData) throws MemoryStorageException {
        try {
            String episodeId = graphitiClient.addEpisode(episodeData);
            return StoreResult.builder()
                .episodeId(episodeId)
                .success(true)
                .build();
        } catch (Exception e) {
            throw new MemoryStorageException("Failed to store episode", e);
        }
    }
    
    // Capability: Search memories
    public List<SearchResult> searchMemories(String query, int limit) {
        List<SearchResult> results = graphitiClient.search(query);
        return results.stream()
            .limit(limit)
            .collect(Collectors.toList());
    }
}

// REST Controller integration
@RestController
@RequestMapping("/api/memory")
public class MemoryController {
    
    @Autowired
    private MemoryAgentService memoryService;
    
    @PostMapping("/assess")
    public ResponseEntity<RelevanceResult> assessRelevance(@RequestBody AssessmentRequest request) {
        RelevanceResult result = memoryService.assessRelevance(request.getContent(), request.getContext());
        return ResponseEntity.ok(result);
    }
}
```

**WRONG Implementation** (Service proliferation anti-pattern):
```java
// ❌ AVOID: Separate services for each capability
@Service class MemoryIntelligenceService { ... }
@Service class ConversationAnalysisService { ... }
@Service class RelevanceAssessmentService { ... }

// This violates consolidation rules - 3 services for 1 system function
```

### React Frontend Applications (TypeScript + Hooks)

**System Architecture**: UI Memory System with capabilities: display_memories, search_interface, settings_management

**CORRECT Implementation** (Component with hooks):
```typescript
// Single component with consolidated capabilities using custom hooks
import React, { useState, useCallback } from 'react';
import { useMemorySearch, useMemoryDisplay, useMemorySettings } from './hooks';

export const MemoryInterface: React.FC = () => {
  // Consolidated state management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMemory, setSelectedMemory] = useState(null);
  
  // Custom hooks for each capability (not separate components)
  const { memories, searchMemories, isSearching } = useMemorySearch();
  const { displayedMemories, formatMemory } = useMemoryDisplay(memories);
  const { settings, updateSettings } = useMemorySettings();

  // Capability: Search interface
  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query);
    await searchMemories(query);
  }, [searchMemories]);

  // Capability: Display memories
  const renderMemoryList = () => {
    return displayedMemories.map(memory => (
      <MemoryCard 
        key={memory.id}
        memory={formatMemory(memory)}
        onSelect={() => setSelectedMemory(memory)}
      />
    ));
  };

  // Capability: Settings management
  const handleSettingsUpdate = (newSettings: MemorySettings) => {
    updateSettings(newSettings);
  };

  return (
    <div className="memory-interface">
      <SearchBar onSearch={handleSearch} isLoading={isSearching} />
      <MemoryList>{renderMemoryList()}</MemoryList>
      <SettingsPanel settings={settings} onUpdate={handleSettingsUpdate} />
    </div>
  );
};
```

**WRONG Implementation** (Component proliferation anti-pattern):
```typescript
// ❌ AVOID: Separate components for each capability
<MemorySearchComponent />     // Separate component for search
<MemoryDisplayComponent />    // Separate component for display  
<MemorySettingsComponent />   // Separate component for settings

// This violates consolidation rules - 3 components for 1 UI system function
```

### Mobile Applications (React Native + TypeScript)

**System Architecture**: Memory Management System with capabilities: local_storage, sync_management, offline_access

**CORRECT Implementation** (Manager with methods):
```typescript
// Single manager with consolidated capabilities
export class MemoryManager {
  private storage: AsyncStorage;
  private syncService: SyncService;
  private offlineQueue: OfflineQueue;

  constructor() {
    this.storage = AsyncStorage;
    this.syncService = new SyncService();
    this.offlineQueue = new OfflineQueue();
  }

  // Capability: Local storage management
  async storeLocalMemory(memory: MemoryEpisode): Promise<void> {
    try {
      const key = `memory_${memory.id}`;
      await this.storage.setItem(key, JSON.stringify(memory));
    } catch (error) {
      throw new StorageError('Failed to store memory locally', error);
    }
  }

  // Capability: Sync management
  async syncWithServer(): Promise<SyncResult> {
    const pendingMemories = await this.offlineQueue.getPending();
    const syncResults = await this.syncService.syncMemories(pendingMemories);
    await this.offlineQueue.clearSynced(syncResults.successful);
    return syncResults;
  }

  // Capability: Offline access
  async getOfflineMemories(limit: number = 50): Promise<MemoryEpisode[]> {
    const keys = await this.storage.getAllKeys();
    const memoryKeys = keys.filter(key => key.startsWith('memory_'));
    const memories = await Promise.all(
      memoryKeys.slice(0, limit).map(async key => {
        const data = await this.storage.getItem(key);
        return JSON.parse(data);
      })
    );
    return memories;
  }
}

// Usage in React Native component
const MemoryScreen: React.FC = () => {
  const [memoryManager] = useState(() => new MemoryManager());
  
  const handleStoreMemory = useCallback(async (memory: MemoryEpisode) => {
    await memoryManager.storeLocalMemory(memory);
  }, [memoryManager]);

  // Component implementation...
};
```

## Cross-Technology Consistency Patterns

### Universal Implementation Mapping

```yaml
architectural_pattern_consistency:
  system_component: "Memory Agent System"
  
  implementation_mapping:
    python_ai: "Agent class with @tool methods"
    nodejs_api: "Service class with methods"
    go_service: "Struct with methods in single package"
    java_enterprise: "Service bean with methods"
    react_frontend: "Component with hooks/methods"
    mobile_app: "Manager class with methods"
    
  capability_implementation:
    python_ai: "@agent.tool decorated async functions"
    nodejs_api: "Class methods with async/await"
    go_service: "Receiver methods on struct"
    java_enterprise: "Public methods in service class"
    react_frontend: "Component methods or custom hooks"
    mobile_app: "Manager methods with async/await"
```

### Technology Detection Rules

```yaml
technology_detection:
  python_ai_indicators:
    - "pydantic_ai" in dependencies
    - "Agent" class references
    - "@tool" decorator patterns
    
  nodejs_api_indicators:
    - "express" in dependencies
    - "FastAPI" equivalent patterns
    - REST/GraphQL API requirements
    
  go_service_indicators:
    - "gin", "echo", "fiber" frameworks
    - Package-based architecture
    - Microservice patterns
    
  java_enterprise_indicators:
    - "Spring Boot" framework
    - "@Service", "@Component" annotations
    - Enterprise integration patterns
```

### Framework Command Integration

Commands should detect technology context and apply appropriate patterns:

```yaml
command_integration:
  detection_phase:
    - "Scan project specifications for technology indicators"
    - "Identify primary technology stack and patterns"
    - "Validate technology-appropriate consolidation rules"
    
  application_phase:
    - "Apply universal consolidation principles"
    - "Map to technology-specific implementation patterns"
    - "Generate technology-appropriate code organization"
    
  validation_phase:
    - "Ensure implementation follows detected technology patterns"
    - "Validate consolidation rules are maintained"
    - "Check for technology-inappropriate suggestions"
```

## Quality Validation Examples

### Consolidation Validation Across Technologies

**Python AI Project**:
- ✅ CORRECT: 1 Memory Agent with 3 @tools
- ❌ WRONG: 3 separate Agents for capabilities

**Node.js API Project**:
- ✅ CORRECT: 1 MemoryService with 3 methods
- ❌ WRONG: 3 separate Services for capabilities

**Go Microservice Project**:
- ✅ CORRECT: 1 memory package with 3 functions
- ❌ WRONG: 3 separate packages for capabilities

**Java Enterprise Project**:
- ✅ CORRECT: 1 @Service with 3 methods
- ❌ WRONG: 3 separate @Services for capabilities

## Success Metrics

- **Architectural Consistency**: Same logical structure across all technology stacks
- **Technology Appropriateness**: Implementation patterns match technology conventions
- **Consolidation Compliance**: No capability explosion regardless of technology
- **Framework Universality**: Rules work across any technology combination

---

**Framework Status**: Comprehensive technology examples demonstrate universal architectural interpretation rules across all major technology stacks while maintaining consistent consolidation principles.