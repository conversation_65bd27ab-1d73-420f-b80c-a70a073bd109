# Context7 Technology Detection Engine

**Purpose**: Smart technology stack detection for Context7 MCP server integration, enabling framework-appropriate architectural pattern validation and anti-pattern prevention.

## Detection Architecture

### Technology Stack Categories

```yaml
supported_technologies:
  python_ai:
    frameworks: ["pydantic_ai", "langchain", "autogen", "crewai"]
    consolidation_pattern: "Single agent with @tool methods"
    anti_pattern: "Multiple agents for single system component"
    
  python_web:
    frameworks: ["fastapi", "django", "flask", "starlette"]
    consolidation_pattern: "Service class with methods"
    anti_pattern: "Service proliferation for single logical component"
    
  nodejs_api:
    frameworks: ["express", "fastify", "koa", "nest"]
    consolidation_pattern: "Service class with methods"
    anti_pattern: "Route handler proliferation"
    
  go_service:
    frameworks: ["gin", "echo", "fiber", "chi"]
    consolidation_pattern: "Package with exported functions"
    anti_pattern: "Package proliferation for single system"
    
  java_enterprise:
    frameworks: ["spring_boot", "spring_mvc", "quarkus"]
    consolidation_pattern: "Service bean with methods"
    anti_pattern: "Bean proliferation for single component"
    
  react_frontend:
    frameworks: ["react", "next", "gatsby"]
    consolidation_pattern: "Component with hooks/methods"
    anti_pattern: "Component explosion for single UI system"
    
  mobile_app:
    frameworks: ["react_native", "flutter", "ionic"]
    consolidation_pattern: "Manager/Service class with methods"
    anti_pattern: "Manager proliferation for single app function"
```

## Detection Logic Implementation

### Multi-Layer Detection Strategy

```python
class TechnologyDetector:
    """Smart technology stack detection for Context7 integration"""
    
    detection_patterns = {
        "python_ai": {
            "primary_indicators": ["pydantic_ai", "from pydantic_ai import Agent"],
            "secondary_indicators": ["@agent.tool", "RunContext", "Agent("],
            "file_patterns": ["*requirements*.txt", "pyproject.toml", "Pipfile"],
            "import_patterns": ["pydantic_ai", "langchain", "autogen"],
            "context7_libraries": [
                "/pydantic/pydantic-ai",
                "/context7/ai_pydantic_dev"
            ],
            "consolidation_topic": "agent tools consolidation patterns",
            "confidence_indicators": {
                "high": ["Agent", "@tool", "RunContext"],
                "medium": ["pydantic_ai", "LLM", "AI agent"],
                "low": ["artificial intelligence", "machine learning"]
            }
        },
        
        "python_web": {
            "primary_indicators": ["fastapi", "from fastapi import"],
            "secondary_indicators": ["@app.route", "APIRouter", "FastAPI()"],
            "file_patterns": ["*requirements*.txt", "pyproject.toml"],
            "import_patterns": ["fastapi", "django", "flask"],
            "context7_libraries": [
                "/tiangolo/fastapi",
                "/django/django",
                "/pallets/flask"
            ],
            "consolidation_topic": "service architecture patterns",
            "confidence_indicators": {
                "high": ["FastAPI", "@app.route", "APIRouter"],
                "medium": ["REST API", "web service"],
                "low": ["HTTP", "web application"]
            }
        },
        
        "nodejs_api": {
            "primary_indicators": ["express", "app.use", "require('express')"],
            "secondary_indicators": ["router", "middleware", "app.listen"],
            "file_patterns": ["package.json", "yarn.lock"],
            "import_patterns": ["express", "fastify", "koa"],
            "context7_libraries": [
                "/expressjs/express",
                "/fastify/fastify"
            ],
            "consolidation_topic": "service class patterns",
            "confidence_indicators": {
                "high": ["app.use", "app.listen", "express()"],
                "medium": ["REST", "API", "middleware"],
                "low": ["Node.js", "JavaScript"]
            }
        },
        
        "go_service": {
            "primary_indicators": ["gin", "echo", "import \"github.com/gin-gonic/gin\""],
            "secondary_indicators": ["http.Handler", "func main", "r.GET"],
            "file_patterns": ["go.mod", "go.sum"],
            "import_patterns": ["gin-gonic", "echo", "fiber"],
            "context7_libraries": [
                "/gin-gonic/gin",
                "/labstack/echo"
            ],
            "consolidation_topic": "package architecture patterns",
            "confidence_indicators": {
                "high": ["gin.Default()", "echo.New()", "r.GET"],
                "medium": ["HTTP handler", "Go service"],
                "low": ["golang", "microservice"]
            }
        },
        
        "java_enterprise": {
            "primary_indicators": ["@Service", "@Component", "Spring"],
            "secondary_indicators": ["@RestController", "@Autowired", "SpringBoot"],
            "file_patterns": ["pom.xml", "build.gradle"],
            "import_patterns": ["springframework", "spring-boot"],
            "context7_libraries": [
                "/spring-projects/spring-boot",
                "/spring-projects/spring-framework"
            ],
            "consolidation_topic": "service bean patterns",
            "confidence_indicators": {
                "high": ["@Service", "@Component", "@RestController"],
                "medium": ["Spring", "Bean", "Dependency Injection"],
                "low": ["Java", "Enterprise"]
            }
        },
        
        "react_frontend": {
            "primary_indicators": ["react", "import React", "jsx"],
            "secondary_indicators": ["useState", "useEffect", "Component"],
            "file_patterns": ["package.json", "tsconfig.json"],
            "import_patterns": ["react", "next", "gatsby"],
            "context7_libraries": [
                "/facebook/react",
                "/vercel/next.js"
            ],
            "consolidation_topic": "component architecture patterns",
            "confidence_indicators": {
                "high": ["useState", "useEffect", "jsx"],
                "medium": ["Component", "React", "Frontend"],
                "low": ["JavaScript", "UI"]
            }
        }
    }
    
    async def detect_technology_stack(self, project_context: str) -> TechnologyDetectionResult:
        """
        Detect technology stack from project context with confidence scoring.
        
        Args:
            project_context: Project description, file contents, or requirements
            
        Returns:
            TechnologyDetectionResult with detected stack and confidence
        """
        detection_scores = {}
        
        for tech_name, patterns in self.detection_patterns.items():
            score = await self._calculate_technology_score(project_context, patterns)
            if score > 0:
                detection_scores[tech_name] = score
        
        # Determine primary technology
        if detection_scores:
            primary_tech = max(detection_scores.items(), key=lambda x: x[1])
            return TechnologyDetectionResult(
                primary_technology=primary_tech[0],
                confidence_score=primary_tech[1],
                all_scores=detection_scores,
                context7_libraries=self.detection_patterns[primary_tech[0]]["context7_libraries"],
                consolidation_topic=self.detection_patterns[primary_tech[0]]["consolidation_topic"]
            )
        else:
            return TechnologyDetectionResult(
                primary_technology="technology_agnostic",
                confidence_score=0.0,
                all_scores={},
                context7_libraries=[],
                consolidation_topic="universal architectural patterns"
            )
    
    async def _calculate_technology_score(self, context: str, patterns: dict) -> float:
        """Calculate confidence score for a specific technology."""
        score = 0.0
        context_lower = context.lower()
        
        # Primary indicators (high weight)
        for indicator in patterns["primary_indicators"]:
            if indicator.lower() in context_lower:
                score += 3.0
        
        # Secondary indicators (medium weight)
        for indicator in patterns["secondary_indicators"]:
            if indicator.lower() in context_lower:
                score += 1.5
        
        # Import patterns (medium weight)
        for pattern in patterns["import_patterns"]:
            if pattern.lower() in context_lower:
                score += 1.0
        
        # Confidence indicators (variable weight)
        for confidence_level, indicators in patterns["confidence_indicators"].items():
            weight = {"high": 2.0, "medium": 1.0, "low": 0.5}[confidence_level]
            for indicator in indicators:
                if indicator.lower() in context_lower:
                    score += weight
        
        return min(score, 10.0)  # Cap at 10.0


class TechnologyDetectionResult:
    """Result of technology stack detection"""
    
    def __init__(
        self,
        primary_technology: str,
        confidence_score: float,
        all_scores: dict,
        context7_libraries: list,
        consolidation_topic: str
    ):
        self.primary_technology = primary_technology
        self.confidence_score = confidence_score
        self.all_scores = all_scores
        self.context7_libraries = context7_libraries
        self.consolidation_topic = consolidation_topic
        self.is_confident = confidence_score >= 5.0
        self.has_fallback = confidence_score < 3.0
    
    def get_consolidation_pattern(self) -> str:
        """Get the expected consolidation pattern for detected technology"""
        patterns = {
            "python_ai": "Single Agent with @tool methods",
            "python_web": "Service class with methods", 
            "nodejs_api": "Service class with methods",
            "go_service": "Package with exported functions",
            "java_enterprise": "Service bean with methods",
            "react_frontend": "Component with hooks/methods",
            "mobile_app": "Manager class with methods",
            "technology_agnostic": "Single implementation unit with multiple capabilities"
        }
        return patterns.get(self.primary_technology, patterns["technology_agnostic"])
    
    def get_anti_pattern_warning(self) -> str:
        """Get anti-pattern warning for detected technology"""
        warnings = {
            "python_ai": "Avoid multiple agents for single system component (e.g., memory_agent + conversation_agent + relevance_agent)",
            "python_web": "Avoid service proliferation for single logical component",
            "nodejs_api": "Avoid route handler proliferation for single system function",
            "go_service": "Avoid package proliferation for single system",
            "java_enterprise": "Avoid bean proliferation for single component",
            "react_frontend": "Avoid component explosion for single UI system",
            "mobile_app": "Avoid manager proliferation for single app function",
            "technology_agnostic": "Avoid component proliferation - group related capabilities in single implementation units"
        }
        return warnings.get(self.primary_technology, warnings["technology_agnostic"])


# Detection usage patterns for framework commands
class FrameworkTechnologyDetection:
    """Integration helpers for framework commands"""
    
    @staticmethod
    async def detect_from_project_context(project_description: str, 
                                         file_contents: str = "", 
                                         requirements_info: str = "") -> TechnologyDetectionResult:
        """Detect technology from comprehensive project information"""
        detector = TechnologyDetector()
        
        # Combine all available context
        full_context = f"{project_description}\n{file_contents}\n{requirements_info}"
        
        return await detector.detect_technology_stack(full_context)
    
    @staticmethod
    async def get_context7_query_params(detection_result: TechnologyDetectionResult) -> dict:
        """Get Context7 query parameters based on detection result"""
        if not detection_result.context7_libraries:
            return {
                "library_id": None,
                "topic": "universal architectural patterns",
                "fallback_mode": True
            }
        
        return {
            "library_id": detection_result.context7_libraries[0],  # Use primary library
            "topic": detection_result.consolidation_topic,
            "fallback_mode": False,
            "confidence": detection_result.confidence_score
        }
```

## Integration Validation Rules

### Technology-Specific Consolidation Validation

```yaml
consolidation_validation_rules:
  python_ai:
    expected_pattern: "Single Agent class with multiple @tool decorated methods"
    validation_checks:
      - "Count of Agent classes <= count of system components"
      - "Capabilities implemented as @tool methods, not separate agents"
      - "RunContext used for dependency injection"
    anti_patterns:
      - "Multiple Agent classes for single system component"
      - "Separate agents for capabilities (memory_agent + conversation_agent)"
    
  nodejs_api:
    expected_pattern: "Single service class with multiple methods"
    validation_checks:
      - "Count of service classes <= count of system components"
      - "Capabilities implemented as class methods"
      - "Dependency injection through constructor"
    anti_patterns:
      - "Service proliferation for single logical component"
      - "Route handler explosion"
    
  go_service:
    expected_pattern: "Single package with multiple exported functions"
    validation_checks:
      - "Count of packages <= count of system components"
      - "Capabilities as exported functions within package"
      - "Struct-based dependency management"
    anti_patterns:
      - "Package proliferation for single system"
      - "Handler function explosion"
```

## Usage in Framework Commands

### Command Integration Pattern

```python
# Usage in framework commands
async def validate_architecture_with_context7(project_context: str, 
                                            proposed_architecture: dict) -> ValidationResult:
    """Standard pattern for Context7 architectural validation in commands"""
    
    # 1. Detect technology stack
    detection = await FrameworkTechnologyDetection.detect_from_project_context(project_context)
    
    # 2. Get Context7 query parameters
    query_params = await FrameworkTechnologyDetection.get_context7_query_params(detection)
    
    # 3. Validate against detected technology patterns
    if query_params["library_id"]:
        # Query Context7 for real patterns
        patterns = await query_context7_patterns(query_params)
        validation = validate_consolidation_compliance(proposed_architecture, patterns, detection)
    else:
        # Fallback to universal rules
        validation = validate_universal_consolidation(proposed_architecture)
    
    return validation
```

## Success Metrics

### Detection Accuracy Targets
- **Primary technology detection**: >90% accuracy for well-defined projects
- **Confidence scoring**: >85% accuracy for high-confidence detections (score ≥ 5.0)
- **False positive prevention**: <5% incorrect technology assignments
- **Fallback handling**: 100% graceful degradation for unknown technologies

### Integration Benefits
- **Anti-pattern prevention**: Automatic detection of capability explosion patterns
- **Technology-appropriate guidance**: Framework-specific implementation recommendations
- **Real-world validation**: Patterns validated against Context7 proven examples
- **Consistent consolidation**: Same principles applied across all technology stacks

---

**Status**: Technology Detection Engine ready for Context7 integration across PRP framework commands.