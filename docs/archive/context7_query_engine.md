# Context7 Query Engine

**Purpose**: Intelligent query engine for Context7 MCP server integration, providing real-world architectural pattern validation and anti-pattern detection for PRP framework commands.

## Query Engine Architecture

### Core Query Engine Implementation

```python
import asyncio
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum


class ValidationSeverity(Enum):
    """Severity levels for architectural validation issues"""
    CRITICAL = "critical"  # Prevents implementation success
    WARNING = "warning"    # Suggests improvement
    INFO = "info"         # Educational guidance


@dataclass
class ArchitecturalPattern:
    """Represents a validated architectural pattern from Context7"""
    pattern_name: str
    technology_stack: str
    consolidation_compliance: bool
    code_example: str
    confidence_score: float
    source_library: str
    anti_patterns: List[str]


@dataclass
class ValidationIssue:
    """Represents an architectural validation issue"""
    issue_type: str
    severity: ValidationSeverity
    description: str
    recommendation: str
    real_example: Optional[str] = None


@dataclass
class ConsolidationValidationResult:
    """Result of architectural consolidation validation"""
    is_valid: bool
    confidence_score: float
    primary_pattern: Optional[ArchitecturalPattern]
    issues: List[ValidationIssue]
    recommendations: List[str]
    real_examples: List[str]
    technology_appropriate: bool
    consolidation_compliant: bool


class Context7QueryEngine:
    """
    Intelligent query engine for Context7 MCP server integration.
    Provides architectural pattern validation and anti-pattern detection.
    """
    
    def __init__(self):
        self.query_cache = {}
        self.fallback_patterns = self._load_fallback_patterns()
    
    async def validate_architectural_consolidation(
        self,
        technology_stack: str,
        system_components: List[str],
        proposed_modules: List[Dict[str, Any]],
        capabilities: List[str],
        context7_libraries: List[str],
        consolidation_topic: str
    ) -> ConsolidationValidationResult:
        """
        Validate architectural consolidation against Context7 real-world patterns.
        
        This is the main entry point for architectural validation in framework commands.
        """
        try:
            # 1. Query Context7 for relevant patterns
            patterns = await self._query_consolidation_patterns(
                context7_libraries, consolidation_topic, technology_stack
            )
            
            # 2. Perform consolidation validation
            validation_result = await self._validate_consolidation_compliance(
                system_components, proposed_modules, capabilities, patterns, technology_stack
            )
            
            # 3. Enhance with real examples
            validation_result.real_examples = self._extract_code_examples(patterns)
            
            return validation_result
            
        except Exception as e:
            # Graceful fallback to universal rules
            return await self._fallback_validation(
                system_components, proposed_modules, capabilities, technology_stack
            )
    
    async def _query_consolidation_patterns(
        self,
        context7_libraries: List[str],
        topic: str,
        technology_stack: str
    ) -> List[ArchitecturalPattern]:
        """Query Context7 for architectural consolidation patterns"""
        patterns = []
        
        for library_id in context7_libraries:
            try:
                # Check cache first
                cache_key = f"{library_id}:{topic}:{technology_stack}"
                if cache_key in self.query_cache:
                    patterns.extend(self.query_cache[cache_key])
                    continue
                
                # Query Context7 MCP server
                docs = await self._query_context7_mcp(library_id, topic)
                
                # Parse architectural patterns from docs
                library_patterns = self._parse_architectural_patterns(docs, technology_stack, library_id)
                patterns.extend(library_patterns)
                
                # Cache results
                self.query_cache[cache_key] = library_patterns
                
            except Exception as e:
                # Continue with other libraries if one fails
                continue
        
        return patterns
    
    async def _query_context7_mcp(self, library_id: str, topic: str) -> str:
        """Make actual MCP call to Context7 server"""
        try:
            # This would be the actual MCP call in the command context
            # For now, showing the pattern that commands would use
            
            # Example MCP call pattern:
            # result = await mcp__Context7__get_library_docs(
            #     context7CompatibleLibraryID=library_id,
            #     topic=topic,
            #     tokens=2000
            # )
            # return result
            
            # Placeholder for MCP integration
            return f"Context7 docs for {library_id} on {topic}"
            
        except Exception as e:
            raise Exception(f"Context7 MCP query failed for {library_id}: {e}")
    
    def _parse_architectural_patterns(
        self, 
        docs: str, 
        technology_stack: str, 
        source_library: str
    ) -> List[ArchitecturalPattern]:
        """Parse architectural patterns from Context7 documentation"""
        patterns = []
        
        # Pattern recognition logic based on technology stack
        if technology_stack == "python_ai":
            patterns.extend(self._parse_python_ai_patterns(docs, source_library))
        elif technology_stack == "nodejs_api":
            patterns.extend(self._parse_nodejs_patterns(docs, source_library))
        elif technology_stack == "go_service":
            patterns.extend(self._parse_go_patterns(docs, source_library))
        else:
            patterns.extend(self._parse_universal_patterns(docs, source_library))
        
        return patterns
    
    def _parse_python_ai_patterns(self, docs: str, source_library: str) -> List[ArchitecturalPattern]:
        """Parse Python AI specific patterns from Context7 docs"""
        patterns = []
        
        # Look for single agent with tools pattern
        if "@agent.tool" in docs and "Agent(" in docs:
            # Check for consolidation compliance
            consolidation_compliant = True
            anti_patterns = []
            
            # Detect anti-patterns
            if docs.count("Agent(") > 1 and "system component" in docs.lower():
                consolidation_compliant = False
                anti_patterns.append("Multiple agents for single system component")
            
            patterns.append(ArchitecturalPattern(
                pattern_name="Single Agent with Tools",
                technology_stack="python_ai",
                consolidation_compliance=consolidation_compliant,
                code_example=self._extract_code_example(docs, "@agent.tool"),
                confidence_score=9.0 if consolidation_compliant else 3.0,
                source_library=source_library,
                anti_patterns=anti_patterns
            ))
        
        return patterns
    
    def _parse_nodejs_patterns(self, docs: str, source_library: str) -> List[ArchitecturalPattern]:
        """Parse Node.js specific patterns from Context7 docs"""
        patterns = []
        
        # Look for service class patterns
        if "class" in docs and "method" in docs and ("express" in docs or "fastify" in docs):
            consolidation_compliant = "service" in docs.lower() and docs.count("class") <= 2
            
            patterns.append(ArchitecturalPattern(
                pattern_name="Service Class with Methods",
                technology_stack="nodejs_api",
                consolidation_compliance=consolidation_compliant,
                code_example=self._extract_code_example(docs, "class"),
                confidence_score=8.0 if consolidation_compliant else 4.0,
                source_library=source_library,
                anti_patterns=["Service proliferation", "Route handler explosion"]
            ))
        
        return patterns
    
    def _parse_go_patterns(self, docs: str, source_library: str) -> List[ArchitecturalPattern]:
        """Parse Go specific patterns from Context7 docs"""
        patterns = []
        
        # Look for package with functions pattern
        if "func " in docs and "package " in docs:
            consolidation_compliant = docs.count("package ") <= 2
            
            patterns.append(ArchitecturalPattern(
                pattern_name="Package with Functions",
                technology_stack="go_service",
                consolidation_compliance=consolidation_compliant,
                code_example=self._extract_code_example(docs, "func "),
                confidence_score=8.0 if consolidation_compliant else 4.0,
                source_library=source_library,
                anti_patterns=["Package proliferation", "Handler explosion"]
            ))
        
        return patterns
    
    def _parse_universal_patterns(self, docs: str, source_library: str) -> List[ArchitecturalPattern]:
        """Parse technology-agnostic patterns from Context7 docs"""
        patterns = []
        
        # Universal consolidation pattern
        patterns.append(ArchitecturalPattern(
            pattern_name="Single Implementation Unit",
            technology_stack="universal",
            consolidation_compliance=True,
            code_example="// Group related capabilities in single implementation unit",
            confidence_score=7.0,
            source_library=source_library,
            anti_patterns=["Component proliferation", "Capability explosion"]
        ))
        
        return patterns
    
    async def _validate_consolidation_compliance(
        self,
        system_components: List[str],
        proposed_modules: List[Dict[str, Any]],
        capabilities: List[str],
        patterns: List[ArchitecturalPattern],
        technology_stack: str
    ) -> ConsolidationValidationResult:
        """Validate proposed architecture against consolidation principles and real patterns"""
        
        issues = []
        recommendations = []
        confidence_score = 7.0  # Base confidence
        
        # 1. Component count validation
        implementation_module_count = len(proposed_modules)
        system_component_count = len(system_components)
        
        if implementation_module_count > system_component_count:
            issues.append(ValidationIssue(
                issue_type="component_proliferation",
                severity=ValidationSeverity.CRITICAL,
                description=f"Too many implementation modules ({implementation_module_count}) for system components ({system_component_count})",
                recommendation="Consolidate related capabilities into fewer implementation modules"
            ))
            confidence_score -= 3.0
        
        # 2. Technology-specific pattern validation
        if patterns:
            compliant_patterns = [p for p in patterns if p.consolidation_compliance]
            if compliant_patterns:
                confidence_score += 2.0
                primary_pattern = max(compliant_patterns, key=lambda p: p.confidence_score)
                
                recommendations.append(f"Follow {primary_pattern.pattern_name} pattern for {technology_stack}")
                
                # Check for anti-patterns
                for pattern in patterns:
                    for anti_pattern in pattern.anti_patterns:
                        if self._detect_anti_pattern_in_modules(anti_pattern, proposed_modules):
                            issues.append(ValidationIssue(
                                issue_type="anti_pattern_detected",
                                severity=ValidationSeverity.WARNING,
                                description=f"Detected anti-pattern: {anti_pattern}",
                                recommendation=f"Use {pattern.pattern_name} instead",
                                real_example=pattern.code_example
                            ))
                            confidence_score -= 1.0
            else:
                primary_pattern = None
        else:
            primary_pattern = None
        
        # 3. Capability consolidation validation
        capability_issues = self._validate_capability_consolidation(capabilities, proposed_modules)
        issues.extend(capability_issues)
        if capability_issues:
            confidence_score -= len(capability_issues) * 0.5
        
        return ConsolidationValidationResult(
            is_valid=len([i for i in issues if i.severity == ValidationSeverity.CRITICAL]) == 0,
            confidence_score=max(confidence_score, 0.0),
            primary_pattern=primary_pattern,
            issues=issues,
            recommendations=recommendations,
            real_examples=[],  # Will be filled by caller
            technology_appropriate=bool(patterns),
            consolidation_compliant=implementation_module_count <= system_component_count
        )
    
    def _detect_anti_pattern_in_modules(self, anti_pattern: str, proposed_modules: List[Dict[str, Any]]) -> bool:
        """Detect specific anti-patterns in proposed module structure"""
        
        # Check for capability explosion patterns
        if "multiple agents" in anti_pattern.lower():
            agent_modules = [m for m in proposed_modules if "agent" in m.get("name", "").lower()]
            return len(agent_modules) > 1
        
        if "service proliferation" in anti_pattern.lower():
            service_modules = [m for m in proposed_modules if "service" in m.get("name", "").lower()]
            return len(service_modules) > len(set(m.get("system_component", "") for m in service_modules))
        
        if "package proliferation" in anti_pattern.lower():
            return len(proposed_modules) > 3  # Heuristic for Go packages
        
        return False
    
    def _validate_capability_consolidation(
        self, 
        capabilities: List[str], 
        proposed_modules: List[Dict[str, Any]]
    ) -> List[ValidationIssue]:
        """Validate that capabilities are properly consolidated"""
        issues = []
        
        # Check for single-capability modules (potential over-fragmentation)
        for module in proposed_modules:
            module_capabilities = module.get("capabilities", [])
            if len(module_capabilities) == 1 and len(capabilities) > 3:
                issues.append(ValidationIssue(
                    issue_type="over_fragmentation",
                    severity=ValidationSeverity.WARNING,
                    description=f"Module '{module.get('name')}' has only one capability - consider consolidation",
                    recommendation="Group related capabilities in the same module"
                ))
        
        return issues
    
    def _extract_code_example(self, docs: str, pattern_indicator: str) -> str:
        """Extract relevant code example from documentation"""
        lines = docs.split('\n')
        example_lines = []
        in_code_block = False
        
        for line in lines:
            if pattern_indicator in line:
                # Found pattern, look for nearby code block
                in_code_block = True
                example_lines.append(line)
            elif in_code_block:
                example_lines.append(line)
                if len(example_lines) > 10:  # Limit example size
                    break
        
        return '\n'.join(example_lines[:10]) if example_lines else f"// {pattern_indicator} pattern example"
    
    def _extract_code_examples(self, patterns: List[ArchitecturalPattern]) -> List[str]:
        """Extract code examples from patterns"""
        return [pattern.code_example for pattern in patterns if pattern.code_example]
    
    async def _fallback_validation(
        self,
        system_components: List[str],
        proposed_modules: List[Dict[str, Any]],
        capabilities: List[str],
        technology_stack: str
    ) -> ConsolidationValidationResult:
        """Fallback validation when Context7 is unavailable"""
        
        issues = []
        recommendations = ["Apply universal consolidation principles"]
        
        # Basic consolidation check
        if len(proposed_modules) > len(system_components):
            issues.append(ValidationIssue(
                issue_type="component_proliferation",
                severity=ValidationSeverity.CRITICAL,
                description="Too many implementation modules for system components",
                recommendation="Consolidate related capabilities into fewer modules"
            ))
        
        return ConsolidationValidationResult(
            is_valid=len(issues) == 0,
            confidence_score=5.0,  # Medium confidence without Context7
            primary_pattern=None,
            issues=issues,
            recommendations=recommendations,
            real_examples=["// Universal: Group related capabilities in single implementation unit"],
            technology_appropriate=False,
            consolidation_compliant=len(proposed_modules) <= len(system_components)
        )
    
    def _load_fallback_patterns(self) -> Dict[str, ArchitecturalPattern]:
        """Load fallback patterns for when Context7 is unavailable"""
        return {
            "universal": ArchitecturalPattern(
                pattern_name="Universal Consolidation",
                technology_stack="universal",
                consolidation_compliance=True,
                code_example="// Group related capabilities in single implementation unit",
                confidence_score=5.0,
                source_library="fallback",
                anti_patterns=["Component proliferation", "Capability explosion"]
            )
        }


# Integration helpers for framework commands
class CommandIntegrationHelpers:
    """Helper functions for integrating Context7 validation into framework commands"""
    
    @staticmethod
    async def validate_module_specifications(
        technology_detection_result,
        system_components: List[str],
        proposed_module_specs: List[Dict[str, Any]],
        capabilities: List[str]
    ) -> ConsolidationValidationResult:
        """
        Standard validation pattern for framework commands.
        
        Usage in commands:
        validation = await CommandIntegrationHelpers.validate_module_specifications(
            detection_result, system_components, module_specs, capabilities
        )
        """
        query_engine = Context7QueryEngine()
        
        return await query_engine.validate_architectural_consolidation(
            technology_stack=technology_detection_result.primary_technology,
            system_components=system_components,
            proposed_modules=proposed_module_specs,
            capabilities=capabilities,
            context7_libraries=technology_detection_result.context7_libraries,
            consolidation_topic=technology_detection_result.consolidation_topic
        )
    
    @staticmethod
    def format_validation_report(validation_result: ConsolidationValidationResult) -> str:
        """Format validation result for command output"""
        
        report_lines = []
        
        # Header
        status = "✅ VALID" if validation_result.is_valid else "❌ ISSUES DETECTED"
        report_lines.append(f"## Architectural Consolidation Validation: {status}")
        report_lines.append(f"**Confidence Score**: {validation_result.confidence_score:.1f}/10.0")
        report_lines.append("")
        
        # Primary pattern
        if validation_result.primary_pattern:
            report_lines.append(f"**Recommended Pattern**: {validation_result.primary_pattern.pattern_name}")
            report_lines.append(f"**Technology Stack**: {validation_result.primary_pattern.technology_stack}")
            report_lines.append("")
        
        # Issues
        if validation_result.issues:
            report_lines.append("### Issues Detected")
            for issue in validation_result.issues:
                severity_icon = {"critical": "🚫", "warning": "⚠️", "info": "ℹ️"}[issue.severity.value]
                report_lines.append(f"- {severity_icon} **{issue.issue_type}**: {issue.description}")
                report_lines.append(f"  - *Recommendation*: {issue.recommendation}")
                if issue.real_example:
                    report_lines.append(f"  - *Example*: {issue.real_example}")
            report_lines.append("")
        
        # Recommendations
        if validation_result.recommendations:
            report_lines.append("### Recommendations")
            for rec in validation_result.recommendations:
                report_lines.append(f"- {rec}")
            report_lines.append("")
        
        # Real examples
        if validation_result.real_examples:
            report_lines.append("### Real Implementation Examples")
            for example in validation_result.real_examples:
                report_lines.append(f"```")
                report_lines.append(example)
                report_lines.append(f"```")
                report_lines.append("")
        
        return "\n".join(report_lines)
```

## Usage Patterns in Framework Commands

### Standard Integration Pattern

```python
# Example usage in complex-5-create-project-plan command
async def enhanced_module_specification_with_context7():
    """Enhanced module specification with Context7 validation"""
    
    # 1. Detect technology stack
    detection_result = await FrameworkTechnologyDetection.detect_from_project_context(
        project_context, file_contents, requirements_info
    )
    
    # 2. Extract architectural information
    system_components = extract_system_components_from_architecture()
    proposed_modules = create_proposed_module_specifications()
    capabilities = extract_capabilities_from_requirements()
    
    # 3. Validate with Context7
    validation = await CommandIntegrationHelpers.validate_module_specifications(
        detection_result, system_components, proposed_modules, capabilities
    )
    
    # 4. Apply validation results
    if not validation.is_valid:
        # Report issues and provide recommendations
        validation_report = CommandIntegrationHelpers.format_validation_report(validation)
        print(validation_report)
        
        # Optionally halt or request revision
        if any(issue.severity == ValidationSeverity.CRITICAL for issue in validation.issues):
            raise ArchitecturalValidationError("Critical consolidation issues detected")
    
    # 5. Use validation insights to improve specifications
    if validation.primary_pattern:
        apply_recommended_pattern(validation.primary_pattern)
    
    return enhanced_module_specifications
```

## Performance & Caching

### Query Optimization
- **Intelligent caching**: Cache Context7 responses by library + topic + technology
- **Batch queries**: Group related queries for efficiency
- **Graceful degradation**: Fallback to universal rules if Context7 unavailable
- **Performance monitoring**: Track query times and success rates

### Error Handling
- **Network resilience**: Handle Context7 MCP server unavailability
- **Partial failures**: Continue with available patterns if some queries fail
- **User transparency**: Clear reporting of Context7 influence on validation
- **Fallback quality**: Ensure fallback validation maintains quality standards

---

**Status**: Context7 Query Engine ready for integration into PRP framework commands with comprehensive architectural validation and anti-pattern detection capabilities.