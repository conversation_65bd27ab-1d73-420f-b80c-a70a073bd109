# Development Commands - Complete Development Workflow Reference

## Overview

Comprehensive reference for all development commands, workflows, and procedures used in the multi-user video generation system. This guide covers setup, testing, deployment, maintenance, and troubleshooting commands for efficient development workflows.

## Environment Setup Commands

### Initial Project Setup
```bash
# Clone repository
git clone <repository-url>
cd sora-poc

# Create and activate virtual environment with uv
uv venv
source .venv/bin/activate  # On Unix/macOS
# .venv\Scripts\activate   # On Windows

# Install all dependencies
uv sync

# Install package in development mode
uv pip install -e .

# Create environment file
cp .env.example .env
# Edit .env with your configuration
```

### UV Package Management
```bash
# Install new package
uv add requests
uv add pytest --dev  # Development dependency

# Remove package
uv remove requests

# Update all packages
uv sync --upgrade

# Install from lockfile (production)
uv sync --frozen

# Run commands with uv
uv run python script.py
uv run pytest
uv run flask --app src.main:create_app run

# Install editable package
uv pip install -e .

# Show package information
uv pip show package-name

# List installed packages
uv pip list

# Generate requirements file
uv pip freeze > requirements.txt
```

## Multi-Service Development

### Service Startup Commands

#### Complete Multi-Service Startup (Required for Production Mode)
```bash
# Terminal 1: Start Redis server (required for queue and rate limiting)
redis-server
# Alternative with Docker:
docker run -p 6379:6379 redis:alpine

# Terminal 2: Start Celery worker (required for background processing)
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4

# Terminal 3: Start Flask application (with WebSocket support)
uv run python src/main.py
# Alternative with Flask CLI:
uv run flask --app src.main:create_app run --host=0.0.0.0 --port=5001

# Terminal 4: Optional - Start Celery monitoring (Web UI at http://localhost:5555)
uv run celery -A src.job_queue.celery_app flower
```

#### Single Service Testing (Development Only)
```bash
# Start just Flask app for API testing (limited functionality)
uv run flask --app src.main:create_app run --debug

# Start with custom configuration
FLASK_ENV=development uv run python src/main.py

# Start with specific port
uv run flask --app src.main:create_app run --port=5002
```

### Service Health Checks
```bash
# Check Flask application health
curl http://localhost:5001/health

# Check specific components
curl http://localhost:5001/health/database
curl http://localhost:5001/health/redis
curl http://localhost:5001/health/azure

# Check system metrics
curl http://localhost:5001/metrics

# Check queue status
curl http://localhost:5001/queue/status
curl http://localhost:5001/queue/stats

# Check Redis connectivity
redis-cli ping

# Check Celery workers
uv run celery -A src.job_queue.celery_app inspect active
uv run celery -A src.job_queue.celery_app inspect stats
```

## Database Management

### Database Operations
```bash
# Initialize database (one-time setup)
uv run flask --app src.main:create_app db init

# Create new migration
uv run flask --app src.main:create_app db migrate -m "Description of changes"

# Apply migrations
uv run flask --app src.main:create_app db upgrade

# Rollback migration
uv run flask --app src.main:create_app db downgrade

# Show current migration version
uv run flask --app src.main:create_app db current

# Show migration history
uv run flask --app src.main:create_app db history

# Stamp database with specific revision
uv run flask --app src.main:create_app db stamp <revision_id>

# Show help for db commands
uv run flask --app src.main:create_app db --help
```

### Database Development Utilities
```bash
# Create database backup
pg_dump $DATABASE_URL > backup.sql

# Restore database from backup
psql $DATABASE_URL < backup.sql

# Connect to database directly
psql $DATABASE_URL

# Reset database (development only)
uv run flask --app src.main:create_app db downgrade base
uv run flask --app src.main:create_app db upgrade

# Create test data
uv run python scripts/create_test_data.py

# Database performance analysis
uv run python scripts/analyze_db_performance.py
```

## Testing Commands

### Production Test Suite (RECOMMENDED)
```bash
# Run complete production validation
python3 run_all_production_tests.py

# Run specific production test suites
python3 test_api_endpoints_comprehensive.py       # API endpoint tests
python3 test_integration_workflow.py              # Integration workflow tests
python3 test_production_validation.py             # Production validation tests
python3 test_runner_minimal.py                    # Core logic tests

# Run production tests with verbose output
python3 run_all_production_tests.py --verbose

# Run production tests with coverage
python3 run_all_production_tests.py --coverage
```

### Legacy Test Suite (If Available)
```bash
# Run all tests
uv run pytest

# Run tests with coverage
uv run pytest --cov=src --cov-report=html --cov-report=term

# Run specific test categories
uv run pytest -m "unit"                    # Unit tests only
uv run pytest -m "integration"             # Integration tests only
uv run pytest -m "performance"             # Performance tests only
uv run pytest -m "not slow"                # Skip slow tests

# Run tests for specific modules
uv run pytest src/database/tests/ -v       # Database tests
uv run pytest src/api/tests/ -v           # API tests
uv run pytest src/core/tests/ -v          # Core model tests
uv run pytest src/features/sora_integration/tests/ -v  # Azure integration tests
uv run pytest src/job_queue/tests/ -v                   # Queue system tests
uv run pytest src/session/tests/ -v                     # Session management tests
uv run pytest src/realtime/tests/ -v                    # WebSocket system tests
uv run pytest src/rate_limiting/tests/ -v               # Rate limiting tests
uv run pytest src/monitoring/tests/ -v                  # Monitoring tests

# Run specific test files
uv run pytest src/tests/test_main.py -v
uv run pytest src/api/tests/test_routes.py::TestVideoRoutes::test_generate_video -v

# Run tests with debugging
uv run pytest -v -s                        # Verbose with output
uv run pytest --pdb                        # Drop into debugger on failure
uv run pytest --lf                         # Run last failed tests
uv run pytest --ff                         # Run failed tests first

# Run tests with specific markers
uv run pytest -m "not slow and not integration"
uv run pytest -k "test_video"              # Run tests matching pattern
```

### Performance Testing
```bash
# Run comprehensive performance tests
uv run pytest src/tests/test_performance.py -v

# Run specific performance test categories
uv run pytest::TestMemoryUsagePerformance -v
uv run pytest::TestResponseTimeBenchmarks -v
uv run pytest::TestDatabasePerformance -v
uv run pytest::TestConcurrentRequestHandling -v

# Run performance tests with extended timeout
uv run pytest src/tests/test_performance.py --timeout=600

# Run performance tests without slow tests
uv run pytest -m "performance and not slow" -v

# Memory profiling tests
uv run pytest::TestMemoryUsagePerformance::test_memory_leak_detection -v -s

# Load testing simulation
uv run pytest::TestConcurrentRequestHandling::test_concurrent_user_simulation -v -s
```

### Test Coverage Analysis
```bash
# Generate HTML coverage report
uv run pytest --cov=src --cov-report=html
open htmlcov/index.html  # View coverage report

# Generate XML coverage report (for CI)
uv run pytest --cov=src --cov-report=xml

# Coverage for specific modules
uv run pytest --cov=src/api --cov-report=term-missing
uv run pytest --cov=src/database --cov-report=term-missing

# Coverage with branch analysis
uv run pytest --cov=src --cov-branch --cov-report=term-missing

# Fail if coverage below threshold
uv run pytest --cov=src --cov-fail-under=80
```

## Code Quality Commands

### Linting and Formatting
```bash
# Format code with ruff
uv run ruff format .
uv run ruff format src/                    # Format specific directory
uv run ruff format src/main.py            # Format specific file

# Check code with ruff linter
uv run ruff check .
uv run ruff check --fix .                 # Auto-fix issues
uv run ruff check src/ --show-source      # Show source code context

# Type checking with mypy
uv run mypy src/
uv run mypy src/main.py                   # Check specific file
uv run mypy --strict src/                 # Strict type checking

# Complete quality check (run before committing)
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest
```

### Security Analysis
```bash
# Check for debug endpoints (CRITICAL for production)
grep -r "/debug/" src/api/routes.py

# Check for hardcoded secrets
grep -r -E "(sk-|password.*=|secret.*=)" src/ --exclude-dir=tests

# Run security tests
uv run pytest src/tests/test_security.py -v

# Bandit security analysis (if available)
bandit -r src/ -f json -o security_report.json

# Safety dependency check (if available)
safety check --json --output security_deps.json
```

### Code Metrics
```bash
# Line count analysis
find src/ -name "*.py" -exec wc -l {} + | sort -n

# Complexity analysis (if installed)
radon cc src/ -s           # Cyclomatic complexity
radon mi src/ -s           # Maintainability index

# Find large files (>500 lines - should be refactored)
find src/ -name "*.py" -exec wc -l {} + | awk '$1 > 500'

# Find long functions (>50 lines - should be refactored)
grep -n "^def " src/**/*.py | while read line; do
    file=$(echo $line | cut -d: -f1)
    line_num=$(echo $line | cut -d: -f2)
    # Check function length logic would go here
done
```

## Docker Development

### Docker Commands
```bash
# Build development image
docker build -t sora-poc:dev .

# Build worker image
docker build -t sora-poc-worker:dev -f Dockerfile.worker .

# Run with Docker Compose (development)
docker-compose up
docker-compose up -d                      # Detached mode
docker-compose up --build                 # Rebuild images

# Run specific services
docker-compose up web worker
docker-compose up db redis               # Just infrastructure

# View logs
docker-compose logs
docker-compose logs web                  # Specific service
docker-compose logs -f worker            # Follow logs

# Execute commands in running containers
docker-compose exec web bash
docker-compose exec db psql -U postgres -d sora_dev

# Stop and clean up
docker-compose down
docker-compose down -v                   # Remove volumes
docker-compose down --rmi all            # Remove images
```

### Docker Development Utilities
```bash
# Build for production
docker build -t sora-poc:prod -f Dockerfile.prod .

# Run single container for testing
docker run -p 5001:5001 --env-file .env sora-poc:dev

# Inspect container
docker inspect sora-poc:dev
docker exec -it <container_id> bash

# Check container resources
docker stats
docker system df                         # Disk usage
docker system prune                      # Clean up unused data
```

## Git Workflow Commands

### Branch Management
```bash
# Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/descriptive-name

# Create fix branch
git checkout -b fix/issue-description

# Create documentation branch
git checkout -b docs/what-is-changing

# Create refactoring branch
git checkout -b refactor/what-is-changing

# Switch branches
git checkout develop
git checkout main
git checkout feature/my-feature

# List branches
git branch                               # Local branches
git branch -r                            # Remote branches
git branch -a                            # All branches
```

### Commit Workflow
```bash
# Check status
git status
git diff                                 # Unstaged changes
git diff --staged                        # Staged changes

# Stage changes
git add .
git add src/specific_file.py
git add -p                               # Interactive staging

# Commit with conventional commit format
git commit -m "feat: add video generation endpoint"
git commit -m "fix: resolve database connection issue"
git commit -m "docs: update API documentation"
git commit -m "test: add integration tests for video workflow"
git commit -m "refactor: improve error handling in client"

# Commit with detailed message using heredoc
git commit -m "$(cat <<'EOF'
feat: implement real-time video generation status updates

- Add WebSocket support for job status broadcasting
- Implement session-based room management
- Add comprehensive error handling for connection failures
- Update frontend to display real-time progress

🤖 Generated with Claude Code

Co-Authored-By: Claude <<EMAIL>>
EOF
)"

# Amend last commit
git commit --amend -m "Updated commit message"
git commit --amend --no-edit              # Keep message, add changes
```

### Push and Pull Operations
```bash
# Push to remote
git push origin feature/my-feature
git push -u origin feature/my-feature    # Set upstream

# Pull latest changes
git pull origin develop
git pull --rebase origin develop         # Rebase instead of merge

# Sync with upstream
git fetch origin
git merge origin/develop

# Push tags
git push origin --tags
```

### Advanced Git Operations
```bash
# Interactive rebase (clean up commits)
git rebase -i HEAD~3                     # Last 3 commits
git rebase -i develop                    # Rebase onto develop

# Cherry-pick commits
git cherry-pick <commit-hash>

# Stash changes
git stash
git stash push -m "Work in progress"
git stash pop
git stash list
git stash apply stash@{0}

# Reset operations
git reset --soft HEAD~1                  # Undo last commit, keep changes staged
git reset --mixed HEAD~1                 # Undo last commit, unstage changes
git reset --hard HEAD~1                  # Undo last commit, discard changes

# View history
git log --oneline --graph --decorate
git log --grep="video"                   # Search commit messages
git log --author="Your Name"
git log --since="2 weeks ago"
```

## API Testing Commands

### Manual API Testing
```bash
# Health checks
curl http://localhost:5001/health
curl http://localhost:5001/health/database
curl http://localhost:5001/health/azure

# Video generation workflow
curl -X POST http://localhost:5001/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A cat playing in a garden", "duration": 5}'

# Check job status
curl http://localhost:5001/status/job-id-here

# Download video
curl -O http://localhost:5001/download/job-id-here

# Queue management
curl http://localhost:5001/queue/status
curl http://localhost:5001/queue/stats

# Session information
curl http://localhost:5001/session/info

# System metrics
curl http://localhost:5001/metrics
curl http://localhost:5001/metrics/jobs
```

### API Testing with Authentication
```bash
# Test with session (if implemented)
curl -X POST http://localhost:5001/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{"prompt": "Test video", "duration": 3}'

# Test rate limiting
for i in {1..10}; do
  curl -s -o /dev/null -w "%{http_code}\n" http://localhost:5001/health
done
```

### Load Testing Commands
```bash
# Simple load test with curl
for i in {1..100}; do
  curl -s http://localhost:5001/health &
done
wait

# Apache Bench (if available)
ab -n 1000 -c 10 http://localhost:5001/health

# wrk load testing (if available)
wrk -t12 -c400 -d30s http://localhost:5001/health
```

## Monitoring and Debugging

### Log Analysis
```bash
# View application logs
tail -f logs/app.log
tail -f logs/error.log

# Search logs for specific patterns
grep "ERROR" logs/app.log
grep "video_generation" logs/app.log | tail -20

# Follow logs for debugging
tail -f logs/app.log | grep -i "error\|exception\|failed"

# Log rotation (if needed)
logrotate /etc/logrotate.d/sora-poc
```

### Performance Monitoring
```bash
# Monitor system resources
top
htop                                     # If available
iostat 1                                 # I/O statistics
free -h                                  # Memory usage
df -h                                    # Disk usage

# Monitor Python process
ps aux | grep python
pgrep -f "python src/main.py"

# Monitor network connections
netstat -tulpn | grep :5001
ss -tulpn | grep :5001                  # Modern alternative

# Monitor Redis
redis-cli info
redis-cli monitor                       # Live command monitoring
redis-cli info memory
redis-cli info clients
```

### Database Monitoring
```bash
# PostgreSQL monitoring
psql $DATABASE_URL -c "SELECT * FROM pg_stat_activity;"
psql $DATABASE_URL -c "SELECT * FROM pg_stat_database;"

# Check database size
psql $DATABASE_URL -c "SELECT pg_size_pretty(pg_database_size(current_database()));"

# Check table sizes
psql $DATABASE_URL -c "
SELECT schemaname, tablename, 
       pg_size_pretty(size) as size,
       pg_size_pretty(total_size) as total_size
FROM (
    SELECT schemaname, tablename,
           pg_relation_size(schemaname||'.'||tablename) as size,
           pg_total_relation_size(schemaname||'.'||tablename) as total_size
    FROM pg_tables
) t
ORDER BY total_size DESC;
"

# Monitor slow queries (if pg_stat_statements enabled)
psql $DATABASE_URL -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
"
```

## Maintenance Commands

### Cleanup Operations
```bash
# Clean Python cache
find . -type d -name "__pycache__" -exec rm -rf {} +
find . -name "*.pyc" -delete

# Clean test artifacts
rm -rf .pytest_cache/
rm -rf htmlcov/
rm .coverage

# Clean Docker resources
docker system prune
docker volume prune
docker image prune

# Clean log files
truncate -s 0 logs/*.log
# Or rotate logs
mv logs/app.log logs/app.log.$(date +%Y%m%d)
touch logs/app.log
```

### File Management
```bash
# Find large files
find . -type f -size +10M -exec ls -lh {} +

# Clean old upload files
find uploads/ -type f -mtime +7 -delete

# Check disk usage by directory
du -sh *
du -sh src/*/

# Archive old logs
tar -czf logs/archive_$(date +%Y%m%d).tar.gz logs/*.log.????-??-??
```

### Service Maintenance
```bash
# Restart services (development)
pkill -f "celery.*worker"
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4 &

# Clear Redis cache (development only)
redis-cli flushall

# Restart Flask app
pkill -f "python src/main.py"
uv run python src/main.py &

# Check service status
pgrep -f "python src/main.py" && echo "Flask running" || echo "Flask not running"
pgrep -f "celery.*worker" && echo "Celery running" || echo "Celery not running"
redis-cli ping && echo "Redis running" || echo "Redis not running"
```

## Troubleshooting Commands

### Common Issues

#### Port Already in Use
```bash
# Find process using port 5001
lsof -i :5001
netstat -tulpn | grep :5001

# Kill process using port
kill $(lsof -t -i:5001)
pkill -f "python src/main.py"
```

#### Database Connection Issues
```bash
# Test database connectivity
psql $DATABASE_URL -c "SELECT 1;"

# Check database process
ps aux | grep postgres

# Test with different connection
PGPASSWORD=password psql -h localhost -p 5432 -U postgres -d sora_dev -c "SELECT 1;"
```

#### Redis Connection Issues
```bash
# Test Redis connectivity
redis-cli ping

# Check Redis process
ps aux | grep redis

# Test with different connection
redis-cli -h localhost -p 6379 ping
```

#### Celery Worker Issues
```bash
# Check Celery workers
uv run celery -A src.job_queue.celery_app inspect active
uv run celery -A src.job_queue.celery_app inspect stats

# Restart workers
pkill -f "celery.*worker"
uv run celery -A src.job_queue.celery_app worker --loglevel=debug

# Check Celery broker connection
uv run celery -A src.job_queue.celery_app inspect ping
```

### Debug Mode Commands
```bash
# Run Flask in debug mode
FLASK_DEBUG=1 uv run python src/main.py

# Run with verbose logging
LOG_LEVEL=DEBUG uv run python src/main.py

# Run with profiling
python -m cProfile -o profile_output.prof src/main.py

# Analyze profile
python -c "
import pstats
p = pstats.Stats('profile_output.prof')
p.sort_stats('time').print_stats(20)
"
```

## Development Workflow Summary

### Daily Development Workflow
```bash
# 1. Start development session
cd sora-poc
source .venv/bin/activate

# 2. Update dependencies and pull latest changes
uv sync
git pull origin develop

# 3. Start services
# Terminal 1: Redis
redis-server

# Terminal 2: Celery worker
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=4

# Terminal 3: Flask app
uv run python src/main.py

# 4. Run tests before making changes
python3 run_all_production_tests.py

# 5. Make changes and test
uv run ruff format . && uv run ruff check . && uv run mypy src/

# 6. Run specific tests for changed components
uv run pytest src/path/to/changed/module/tests/ -v

# 7. Commit changes
git add .
git commit -m "feat: description of changes"

# 8. Push changes
git push origin feature/branch-name
```

### Pre-commit Checklist
```bash
# Code quality
uv run ruff format .
uv run ruff check .
uv run mypy src/

# Security checks
grep -r "/debug/" src/api/routes.py || echo "✅ No debug endpoints"
grep -r -E "(sk-|password.*=|secret.*=)" src/ --exclude-dir=tests || echo "✅ No hardcoded secrets"

# Tests
python3 run_all_production_tests.py
uv run pytest -m "not slow" --cov=src --cov-fail-under=80

# Documentation
# Update CLAUDE.md if adding new features
# Update README.md if changing setup procedures
```

This comprehensive development commands reference provides all the tools needed for efficient development, testing, and maintenance of the multi-user video generation system.