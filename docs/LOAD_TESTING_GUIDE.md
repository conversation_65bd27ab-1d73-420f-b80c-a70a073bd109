# Multi-User Sora Video Generation - Load Testing Guide

This comprehensive guide provides procedures for load testing the multi-user Azure OpenAI Sora video generation system to validate performance under concurrent user loads and ensure system stability at the target 15+ concurrent users.

## Table of Contents

- [Overview](#overview)
- [Load Testing Requirements](#load-testing-requirements)
- [Test Environment Setup](#test-environment-setup)
- [Load Testing Tools](#load-testing-tools)
- [Test Scenarios](#test-scenarios)
- [Performance Metrics](#performance-metrics)
- [Load Testing Procedures](#load-testing-procedures)
- [Monitoring During Tests](#monitoring-during-tests)
- [Performance Validation](#performance-validation)
- [Results Analysis](#results-analysis)
- [Optimization Recommendations](#optimization-recommendations)

## Overview

### Performance Goals

Based on the PRP requirements, the system must achieve:

- **Concurrent Users**: 10+ minimum, 15+ target
- **Queue Wait Time**: <3 minutes average, <5 minutes peak
- **System Uptime**: 99.5%
- **Task Success Rate**: 95%
- **API Response Time**: <3 seconds
- **Real-time Updates**: <2 seconds latency

### Test Strategy

1. **Baseline Testing**: Single user performance
2. **Incremental Load**: Gradually increase concurrent users
3. **Peak Load Testing**: 15+ concurrent users
4. **Stress Testing**: Beyond normal capacity
5. **Endurance Testing**: Sustained load over time
6. **Failure Recovery**: System resilience testing

## Load Testing Requirements

### Hardware Requirements

**Minimum Test Environment:**
- **CPU**: 8 cores (16 cores recommended)
- **RAM**: 16GB (32GB recommended)
- **Network**: 1Gbps connection
- **Storage**: 100GB free space

**Load Generation Client:**
- **CPU**: 4 cores minimum
- **RAM**: 8GB minimum
- **Network**: Dedicated connection (separate from test target)

### Software Requirements

```bash
# Install load testing tools
uv add --dev locust pytest-asyncio aiohttp websocket-client

# Install monitoring tools
pip install psutil prometheus-client grafana-client

# Install system monitoring
sudo apt-get install htop iotop netstat-nat systat
```

## Test Environment Setup

### 1. Production-Like Environment

```bash
# Create test environment directory
mkdir -p /opt/sora-test
cd /opt/sora-test

# Copy production configuration
cp -r /opt/sora/config ./
cp -r /opt/sora/docker-compose.production.yml ./docker-compose.test.yml

# Modify for testing
sed -i 's/production/test/g' docker-compose.test.yml
sed -i 's/sora-prod/sora-test/g' docker-compose.test.yml
```

### 2. Test Data Preparation

```python
# tests/load_testing/test_data_generator.py
import random
import json
from typing import List, Dict

class TestDataGenerator:
    """Generate realistic test data for load testing."""
    
    VIDEO_PROMPTS = [
        "A majestic eagle soaring over snow-capped mountains at sunset",
        "Waves crashing against rocky cliffs during a thunderstorm",
        "A bustling city street with neon lights reflecting on wet pavement",
        "A peaceful forest clearing with sunlight filtering through ancient trees",
        "Lightning illuminating a vast desert landscape at night",
        "A graceful ballet dancer performing in an empty theater",
        "Steam rising from a hot spring in a snowy landscape",
        "A vintage train crossing a stone bridge over a river",
        "Fireflies dancing in a meadow under a starry sky",
        "A surfer riding a massive wave at golden hour",
        "Cherry blossoms falling in a Japanese garden",
        "A lighthouse beacon cutting through fog on a rocky coast",
        "A herd of wild horses running across an open plain",
        "Northern lights dancing over a frozen lake",
        "A butterfly emerging from its cocoon in slow motion"
    ]
    
    DURATIONS = [3, 5, 8, 10, 15]
    RESOLUTIONS = [
        {"width": 1280, "height": 720},
        {"width": 1920, "height": 1080},
        {"width": 854, "height": 480}
    ]
    
    @classmethod
    def generate_video_request(cls) -> Dict:
        """Generate a realistic video generation request."""
        resolution = random.choice(cls.RESOLUTIONS)
        
        return {
            "prompt": random.choice(cls.VIDEO_PROMPTS),
            "duration": random.choice(cls.DURATIONS),
            "width": resolution["width"],
            "height": resolution["height"],
            "priority": random.randint(0, 2)
        }
    
    @classmethod
    def generate_user_session(cls, user_id: int) -> Dict:
        """Generate user session data."""
        return {
            "user_id": user_id,
            "session_id": f"load_test_session_{user_id}_{random.randint(1000, 9999)}",
            "requests_per_session": random.randint(2, 5),
            "think_time": random.uniform(1.0, 5.0)  # seconds between requests
        }
    
    @classmethod
    def generate_load_test_scenario(cls, num_users: int) -> List[Dict]:
        """Generate complete load test scenario."""
        return [cls.generate_user_session(i) for i in range(num_users)]
```

### 3. Environment Configuration

```bash
# Load test environment variables
cat > /opt/sora-test/.env.test << EOF
# Test Environment Configuration
FLASK_ENV=testing
DATABASE_URL=**************************************************/sora_test
CELERY_BROKER_URL=redis://redis:6379/1
AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}

# Load testing specific
LOAD_TEST_MODE=true
MOCK_VIDEO_GENERATION=false
RATE_LIMIT_ENABLED=true
MAX_CONCURRENT_JOBS_PER_SESSION=5

# Performance tuning for testing
DATABASE_POOL_SIZE=50
WORKER_CONCURRENCY=4
LOG_LEVEL=INFO
EOF
```

## Load Testing Tools

### 1. Locust Configuration

```python
# tests/load_testing/locustfile.py
from locust import HttpUser, task, between, events
import random
import json
import time
import websocket
import threading
from test_data_generator import TestDataGenerator

class SoraVideoUser(HttpUser):
    """Locust user class for Sora video generation testing."""
    
    wait_time = between(1, 5)
    
    def on_start(self):
        """Initialize user session."""
        # Create session
        response = self.client.get("/api/session/create")
        if response.status_code == 200:
            data = response.json()
            self.session_id = data["data"]["session_id"]
            self.client.headers.update({"X-Session-ID": self.session_id})
            
            # Setup WebSocket connection
            self.setup_websocket()
        else:
            self.session_id = None
    
    def setup_websocket(self):
        """Setup WebSocket connection for real-time updates."""
        try:
            self.ws = websocket.WebSocketApp(
                f"ws://{self.host.replace('http://', '')}/socket.io/?EIO=4&transport=websocket",
                on_message=self.on_websocket_message,
                on_error=self.on_websocket_error,
                on_close=self.on_websocket_close
            )
            
            # Start WebSocket in background thread
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
        except Exception as e:
            print(f"WebSocket setup failed: {e}")
            self.ws = None
    
    def on_websocket_message(self, ws, message):
        """Handle WebSocket messages."""
        try:
            data = json.loads(message)
            # Record WebSocket message latency
            events.request.fire(
                request_type="WebSocket",
                name="real_time_update",
                response_time=0,  # Would need to calculate actual latency
                response_length=len(message),
                exception=None,
                context={}
            )
        except Exception as e:
            events.request.fire(
                request_type="WebSocket",
                name="real_time_update",
                response_time=0,
                response_length=0,
                exception=e,
                context={}
            )
    
    def on_websocket_error(self, ws, error):
        """Handle WebSocket errors."""
        events.request.fire(
            request_type="WebSocket",
            name="connection_error",
            response_time=0,
            response_length=0,
            exception=error,
            context={}
        )
    
    def on_websocket_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close."""
        pass
    
    @task(3)
    def generate_video(self):
        """Submit video generation request."""
        if not self.session_id:
            return
        
        request_data = TestDataGenerator.generate_video_request()
        
        start_time = time.time()
        
        with self.client.post("/api/generate", 
                             json=request_data, 
                             catch_response=True) as response:
            
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    job_id = data["data"]["job_id"]
                    queue_position = data["data"].get("queue_position", 0)
                    
                    # Store job for status checking
                    if not hasattr(self, 'active_jobs'):
                        self.active_jobs = []
                    
                    self.active_jobs.append({
                        "job_id": job_id,
                        "submitted_at": time.time(),
                        "queue_position": queue_position
                    })
                    
                    # Subscribe to WebSocket updates if available
                    if self.ws and self.ws.sock:
                        self.ws.send(json.dumps({
                            "event": "subscribe_job_updates",
                            "data": {"job_id": job_id}
                        }))
                    
                    response.success()
                    
                    # Record queue position as custom metric
                    events.request.fire(
                        request_type="Queue",
                        name="queue_position",
                        response_time=queue_position,
                        response_length=0,
                        exception=None,
                        context={"queue_position": queue_position}
                    )
                    
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text}")
    
    @task(2)
    def check_job_status(self):
        """Check status of submitted jobs."""
        if not hasattr(self, 'active_jobs') or not self.active_jobs:
            return
        
        job = random.choice(self.active_jobs)
        job_id = job["job_id"]
        
        start_time = time.time()
        
        with self.client.get(f"/api/status/{job_id}", 
                            catch_response=True) as response:
            
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    status = data["data"]["status"]
                    
                    # Remove completed jobs
                    if status in ["succeeded", "failed"]:
                        self.active_jobs.remove(job)
                        
                        # Calculate total processing time
                        total_time = time.time() - job["submitted_at"]
                        
                        # Record processing time as custom metric
                        events.request.fire(
                            request_type="Processing",
                            name="video_generation_time",
                            response_time=total_time * 1000,
                            response_length=0,
                            exception=None,
                            context={"status": status, "processing_time": total_time}
                        )
                    
                    response.success()
                    
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text}")
    
    @task(1)
    def check_queue_status(self):
        """Check overall queue status."""
        with self.client.get("/api/queue/status", 
                            catch_response=True) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    queue_data = data["data"]
                    
                    # Record queue metrics
                    events.request.fire(
                        request_type="Queue",
                        name="global_queue_depth",
                        response_time=queue_data["global_queue"]["total_jobs"],
                        response_length=0,
                        exception=None,
                        context=queue_data["global_queue"]
                    )
                    
                    response.success()
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text}")
    
    @task(1)
    def health_check(self):
        """Perform health check."""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: HTTP {response.status_code}")
    
    def on_stop(self):
        """Cleanup when user stops."""
        if hasattr(self, 'ws') and self.ws:
            self.ws.close()

# Custom event handlers for metrics collection
@events.init.add_listener
def on_locust_init(environment, **kwargs):
    """Initialize custom metrics."""
    environment.queue_positions = []
    environment.processing_times = []
    environment.websocket_latencies = []

@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Collect custom metrics."""
    if request_type == "Queue" and name == "queue_position":
        environment.queue_positions.append(response_time)
    elif request_type == "Processing" and name == "video_generation_time":
        environment.processing_times.append(response_time / 1000)  # Convert to seconds
    elif request_type == "WebSocket":
        environment.websocket_latencies.append(response_time)
```

### 2. Async Load Testing with aiohttp

```python
# tests/load_testing/async_load_test.py
import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict, Tuple
from test_data_generator import TestDataGenerator

class AsyncLoadTester:
    """Async load tester for high-concurrency testing."""
    
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url
        self.session_cache = {}
        self.results = {
            "requests": [],
            "errors": [],
            "queue_positions": [],
            "processing_times": [],
            "websocket_events": []
        }
    
    async def create_session(self, user_id: int) -> str:
        """Create user session."""
        if user_id in self.session_cache:
            return self.session_cache[user_id]
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/api/session/create") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    session_id = data["data"]["session_id"]
                    self.session_cache[user_id] = session_id
                    return session_id
                else:
                    raise Exception(f"Failed to create session: {resp.status}")
    
    async def submit_video_job(self, session_id: str, request_data: Dict) -> Dict:
        """Submit video generation job."""
        headers = {"X-Session-ID": session_id}
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/api/generate",
                                   json=request_data, 
                                   headers=headers) as resp:
                
                response_time = (time.time() - start_time) * 1000
                
                if resp.status == 200:
                    data = await resp.json()
                    
                    self.results["requests"].append({
                        "endpoint": "/api/generate",
                        "response_time": response_time,
                        "status_code": resp.status,
                        "timestamp": start_time
                    })
                    
                    if "queue_position" in data["data"]:
                        self.results["queue_positions"].append(data["data"]["queue_position"])
                    
                    return data["data"]
                else:
                    error_msg = f"HTTP {resp.status}: {await resp.text()}"
                    self.results["errors"].append({
                        "endpoint": "/api/generate",
                        "error": error_msg,
                        "timestamp": start_time
                    })
                    raise Exception(error_msg)
    
    async def check_job_status(self, session_id: str, job_id: str) -> Dict:
        """Check job status."""
        headers = {"X-Session-ID": session_id}
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/api/status/{job_id}",
                                  headers=headers) as resp:
                
                response_time = (time.time() - start_time) * 1000
                
                if resp.status == 200:
                    data = await resp.json()
                    
                    self.results["requests"].append({
                        "endpoint": "/api/status",
                        "response_time": response_time,
                        "status_code": resp.status,
                        "timestamp": start_time
                    })
                    
                    return data["data"]
                else:
                    error_msg = f"HTTP {resp.status}: {await resp.text()}"
                    self.results["errors"].append({
                        "endpoint": "/api/status",
                        "error": error_msg,
                        "timestamp": start_time
                    })
                    raise Exception(error_msg)
    
    async def simulate_user(self, user_id: int, num_jobs: int = 3) -> List[Dict]:
        """Simulate a single user's activity."""
        user_results = []
        
        try:
            # Create session
            session_id = await self.create_session(user_id)
            
            # Submit multiple jobs
            for job_num in range(num_jobs):
                request_data = TestDataGenerator.generate_video_request()
                
                try:
                    job_data = await self.submit_video_job(session_id, request_data)
                    job_id = job_data["job_id"]
                    
                    user_results.append({
                        "user_id": user_id,
                        "job_id": job_id,
                        "submitted_at": time.time(),
                        "queue_position": job_data.get("queue_position", 0),
                        "status": "submitted"
                    })
                    
                    # Wait before next submission
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    user_results.append({
                        "user_id": user_id,
                        "error": str(e),
                        "status": "failed"
                    })
            
            # Monitor job progress
            active_jobs = [job for job in user_results if job.get("status") == "submitted"]
            
            while active_jobs:
                for job in active_jobs[:]:  # Copy list to avoid modification during iteration
                    try:
                        status_data = await self.check_job_status(session_id, job["job_id"])
                        
                        if status_data["status"] in ["succeeded", "failed"]:
                            processing_time = time.time() - job["submitted_at"]
                            job["processing_time"] = processing_time
                            job["final_status"] = status_data["status"]
                            
                            self.results["processing_times"].append(processing_time)
                            active_jobs.remove(job)
                    
                    except Exception as e:
                        job["error"] = str(e)
                        active_jobs.remove(job)
                
                if active_jobs:
                    await asyncio.sleep(5)  # Check every 5 seconds
        
        except Exception as e:
            user_results.append({
                "user_id": user_id,
                "error": str(e),
                "status": "session_failed"
            })
        
        return user_results
    
    async def run_load_test(self, num_users: int, jobs_per_user: int = 2) -> Dict:
        """Run concurrent load test."""
        start_time = time.time()
        
        # Create tasks for all users
        tasks = [
            self.simulate_user(user_id, jobs_per_user)
            for user_id in range(num_users)
        ]
        
        # Run all users concurrently
        user_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        
        # Analyze results
        successful_jobs = 0
        failed_jobs = 0
        total_users = 0
        
        for result in user_results:
            if isinstance(result, Exception):
                failed_jobs += jobs_per_user
                continue
            
            total_users += 1
            for job in result:
                if job.get("final_status") == "succeeded":
                    successful_jobs += 1
                elif job.get("status") in ["failed", "session_failed"] or "error" in job:
                    failed_jobs += 1
        
        return {
            "test_duration": end_time - start_time,
            "num_users": num_users,
            "jobs_per_user": jobs_per_user,
            "successful_jobs": successful_jobs,
            "failed_jobs": failed_jobs,
            "success_rate": successful_jobs / (successful_jobs + failed_jobs) if (successful_jobs + failed_jobs) > 0 else 0,
            "avg_queue_position": statistics.mean(self.results["queue_positions"]) if self.results["queue_positions"] else 0,
            "avg_processing_time": statistics.mean(self.results["processing_times"]) if self.results["processing_times"] else 0,
            "avg_response_time": statistics.mean([r["response_time"] for r in self.results["requests"]]) if self.results["requests"] else 0,
            "error_rate": len(self.results["errors"]) / len(self.results["requests"]) if self.results["requests"] else 0,
            "results": user_results,
            "detailed_metrics": self.results
        }

# Test execution functions
async def run_baseline_test():
    """Run baseline single-user test."""
    tester = AsyncLoadTester()
    return await tester.run_load_test(num_users=1, jobs_per_user=3)

async def run_incremental_test():
    """Run incremental load test."""
    results = {}
    
    for num_users in [2, 5, 10, 15, 20]:
        print(f"Testing with {num_users} users...")
        tester = AsyncLoadTester()
        results[num_users] = await tester.run_load_test(num_users=num_users, jobs_per_user=2)
        
        # Wait between tests
        await asyncio.sleep(30)
    
    return results

async def run_stress_test():
    """Run stress test beyond normal capacity."""
    tester = AsyncLoadTester()
    return await tester.run_load_test(num_users=25, jobs_per_user=3)

# Example usage
if __name__ == "__main__":
    async def main():
        print("Running load tests...")
        
        # Baseline test
        print("\n1. Baseline Test (1 user)")
        baseline = await run_baseline_test()
        print(f"Baseline Success Rate: {baseline['success_rate']:.2%}")
        print(f"Baseline Avg Response Time: {baseline['avg_response_time']:.2f}ms")
        
        # Incremental test
        print("\n2. Incremental Load Test")
        incremental = await run_incremental_test()
        
        for users, result in incremental.items():
            print(f"{users} users: {result['success_rate']:.2%} success, "
                  f"{result['avg_response_time']:.2f}ms avg response")
        
        # Stress test
        print("\n3. Stress Test (25 users)")
        stress = await run_stress_test()
        print(f"Stress Test Success Rate: {stress['success_rate']:.2%}")
        print(f"Stress Test Avg Response Time: {stress['avg_response_time']:.2f}ms")
    
    asyncio.run(main())
```

## Test Scenarios

### 1. Baseline Performance Test

```bash
# Single user baseline
locust -f tests/load_testing/locustfile.py --headless -u 1 -r 1 -t 300s --host http://localhost:5001

# Expected Results:
# - Success Rate: >99%
# - Response Time: <1 second
# - Queue Position: 1
```

### 2. Incremental Load Test

```bash
# Test script for incremental load
#!/bin/bash
# tests/load_testing/incremental_test.sh

USERS=(2 5 10 15 20)
DURATION=300  # 5 minutes per test
HOST="http://localhost:5001"

for USER_COUNT in "${USERS[@]}"; do
    echo "Testing with $USER_COUNT users..."
    
    locust -f tests/load_testing/locustfile.py \
           --headless \
           -u $USER_COUNT \
           -r 1 \
           -t ${DURATION}s \
           --host $HOST \
           --html reports/load_test_${USER_COUNT}_users.html
    
    echo "Waiting 60 seconds before next test..."
    sleep 60
done
```

### 3. Peak Load Test (15+ Users)

```bash
# Target load test
locust -f tests/load_testing/locustfile.py \
       --headless \
       -u 15 \
       -r 2 \
       -t 600s \
       --host http://localhost:5001 \
       --html reports/peak_load_test.html

# Success Criteria:
# - Success Rate: >95%
# - Avg Queue Wait: <3 minutes
# - Max Queue Wait: <5 minutes
# - System Stability: No crashes
```

### 4. Stress Test (Beyond Capacity)

```bash
# Stress test to find breaking point
locust -f tests/load_testing/locustfile.py \
       --headless \
       -u 25 \
       -r 3 \
       -t 900s \
       --host http://localhost:5001 \
       --html reports/stress_test.html
```

### 5. Endurance Test

```bash
# Long-running test
locust -f tests/load_testing/locustfile.py \
       --headless \
       -u 10 \
       -r 1 \
       -t 3600s \
       --host http://localhost:5001 \
       --html reports/endurance_test.html
```

## Performance Metrics

### Key Performance Indicators

1. **Response Time Metrics**
   - API Response Time (target: <3s)
   - Video Generation Time (baseline: 30-60s)
   - Queue Wait Time (target: <3min avg, <5min max)

2. **Throughput Metrics**
   - Requests per Second
   - Jobs Completed per Hour
   - Concurrent Active Jobs

3. **Error Metrics**
   - Error Rate (target: <5%)
   - Timeout Rate
   - Queue Overflow Rate

4. **Resource Metrics**
   - CPU Usage
   - Memory Usage
   - Database Connection Pool Usage
   - Redis Memory Usage

### Custom Metrics Collection

```python
# tests/load_testing/metrics_collector.py
import psutil
import docker
import redis
import psycopg2
import time
from typing import Dict, List

class MetricsCollector:
    """Collect system metrics during load testing."""
    
    def __init__(self):
        self.docker_client = docker.from_env()
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.metrics_history = []
    
    def collect_system_metrics(self) -> Dict:
        """Collect system-level metrics."""
        return {
            "timestamp": time.time(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "network_io": psutil.net_io_counters()._asdict(),
            "load_average": psutil.getloadavg()
        }
    
    def collect_docker_metrics(self) -> Dict:
        """Collect Docker container metrics."""
        containers = {}
        
        for container in self.docker_client.containers.list():
            if 'sora' in container.name:
                stats = container.stats(stream=False)
                
                # Calculate CPU usage
                cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                           stats['precpu_stats']['cpu_usage']['total_usage']
                system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                              stats['precpu_stats']['system_cpu_usage']
                cpu_percent = (cpu_delta / system_delta) * 100.0 if system_delta > 0 else 0
                
                # Calculate memory usage
                memory_usage = stats['memory_stats']['usage']
                memory_limit = stats['memory_stats']['limit']
                memory_percent = (memory_usage / memory_limit) * 100.0
                
                containers[container.name] = {
                    "cpu_percent": cpu_percent,
                    "memory_usage_mb": memory_usage / 1024 / 1024,
                    "memory_percent": memory_percent,
                    "network_rx_bytes": stats['networks']['eth0']['rx_bytes'],
                    "network_tx_bytes": stats['networks']['eth0']['tx_bytes']
                }
        
        return containers
    
    def collect_application_metrics(self) -> Dict:
        """Collect application-specific metrics."""
        try:
            # Queue metrics
            queue_depth = self.redis_client.llen('celery')
            
            # Database metrics
            conn = psycopg2.connect(
                host='localhost',
                database='sora_production',
                user='sora_user',
                password='password'  # Use actual password
            )
            
            with conn.cursor() as cur:
                # Active connections
                cur.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
                active_connections = cur.fetchone()[0]
                
                # Job statistics
                cur.execute("""
                    SELECT 
                        status,
                        count(*) as count,
                        avg(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_processing_time
                    FROM video_jobs 
                    WHERE created_at > NOW() - INTERVAL '1 hour'
                    GROUP BY status
                """)
                job_stats = {row[0]: {"count": row[1], "avg_time": row[2]} 
                           for row in cur.fetchall()}
            
            conn.close()
            
            return {
                "queue_depth": queue_depth,
                "active_db_connections": active_connections,
                "job_statistics": job_stats
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def collect_all_metrics(self) -> Dict:
        """Collect all metrics."""
        metrics = {
            "system": self.collect_system_metrics(),
            "docker": self.collect_docker_metrics(),
            "application": self.collect_application_metrics()
        }
        
        self.metrics_history.append(metrics)
        return metrics
    
    def start_monitoring(self, interval: int = 10):
        """Start continuous monitoring."""
        import threading
        
        def monitor():
            while True:
                self.collect_all_metrics()
                time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        return monitor_thread
```

## Monitoring During Tests

### Real-Time Monitoring Setup

```bash
#!/bin/bash
# tests/load_testing/setup_monitoring.sh

# Start monitoring tools
echo "Setting up monitoring for load test..."

# Start system monitoring
htop &
iotop &

# Start Docker stats
docker stats &

# Start custom metrics collection
python tests/load_testing/metrics_collector.py &

# Monitor log files
tail -f /opt/sora/logs/app/*.log &
tail -f /opt/sora/logs/workers/*.log &

echo "Monitoring setup complete. Press Ctrl+C to stop all monitoring."
wait
```

### Grafana Dashboard for Load Testing

```json
{
  "dashboard": {
    "title": "Sora Load Testing Dashboard",
    "panels": [
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[1m])",
            "legendFormat": "Requests/sec"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[1m]) / rate(http_requests_total[1m])",
            "legendFormat": "Error Rate"
          }
        ]
      },
      {
        "title": "Queue Depth",
        "type": "graph",
        "targets": [
          {
            "expr": "redis_list_length{list=\"celery\"}",
            "legendFormat": "Queue Depth"
          }
        ]
      },
      {
        "title": "Active Workers",
        "type": "stat",
        "targets": [
          {
            "expr": "count(up{job=\"celery-workers\"})",
            "legendFormat": "Active Workers"
          }
        ]
      }
    ]
  }
}
```

## Performance Validation

### Automated Validation Script

```python
# tests/load_testing/validate_performance.py
import json
import statistics
from typing import Dict, List, Tuple

class PerformanceValidator:
    """Validate load test results against PRP requirements."""
    
    # PRP Requirements
    TARGET_CONCURRENT_USERS = 15
    MAX_AVG_QUEUE_WAIT_MINUTES = 3
    MAX_PEAK_QUEUE_WAIT_MINUTES = 5
    MIN_SUCCESS_RATE = 0.95
    MAX_API_RESPONSE_TIME_SECONDS = 3
    MAX_REALTIME_UPDATE_LATENCY_SECONDS = 2
    
    def __init__(self, test_results: Dict):
        self.results = test_results
        self.validation_results = {}
    
    def validate_concurrent_users(self) -> bool:
        """Validate system supports target concurrent users."""
        max_users = max(self.results.keys()) if self.results else 0
        passed = max_users >= self.TARGET_CONCURRENT_USERS
        
        self.validation_results["concurrent_users"] = {
            "requirement": f">= {self.TARGET_CONCURRENT_USERS} users",
            "actual": max_users,
            "passed": passed
        }
        
        return passed
    
    def validate_queue_wait_times(self) -> bool:
        """Validate queue wait times meet requirements."""
        wait_times = []
        
        for user_count, result in self.results.items():
            if "avg_queue_wait_minutes" in result:
                wait_times.append(result["avg_queue_wait_minutes"])
        
        if not wait_times:
            return False
        
        avg_wait = statistics.mean(wait_times)
        max_wait = max(wait_times)
        
        avg_passed = avg_wait <= self.MAX_AVG_QUEUE_WAIT_MINUTES
        max_passed = max_wait <= self.MAX_PEAK_QUEUE_WAIT_MINUTES
        
        self.validation_results["queue_wait_times"] = {
            "avg_requirement": f"<= {self.MAX_AVG_QUEUE_WAIT_MINUTES} minutes",
            "avg_actual": avg_wait,
            "avg_passed": avg_passed,
            "max_requirement": f"<= {self.MAX_PEAK_QUEUE_WAIT_MINUTES} minutes",
            "max_actual": max_wait,
            "max_passed": max_passed,
            "overall_passed": avg_passed and max_passed
        }
        
        return avg_passed and max_passed
    
    def validate_success_rate(self) -> bool:
        """Validate success rate meets requirements."""
        success_rates = []
        
        for user_count, result in self.results.items():
            if "success_rate" in result:
                success_rates.append(result["success_rate"])
        
        if not success_rates:
            return False
        
        min_success_rate = min(success_rates)
        avg_success_rate = statistics.mean(success_rates)
        
        passed = min_success_rate >= self.MIN_SUCCESS_RATE
        
        self.validation_results["success_rate"] = {
            "requirement": f">= {self.MIN_SUCCESS_RATE:.0%}",
            "min_actual": min_success_rate,
            "avg_actual": avg_success_rate,
            "passed": passed
        }
        
        return passed
    
    def validate_response_times(self) -> bool:
        """Validate API response times meet requirements."""
        response_times = []
        
        for user_count, result in self.results.items():
            if "avg_response_time" in result:
                # Convert from ms to seconds
                response_times.append(result["avg_response_time"] / 1000)
        
        if not response_times:
            return False
        
        max_response_time = max(response_times)
        avg_response_time = statistics.mean(response_times)
        
        passed = max_response_time <= self.MAX_API_RESPONSE_TIME_SECONDS
        
        self.validation_results["response_times"] = {
            "requirement": f"<= {self.MAX_API_RESPONSE_TIME_SECONDS} seconds",
            "max_actual": max_response_time,
            "avg_actual": avg_response_time,
            "passed": passed
        }
        
        return passed
    
    def validate_all(self) -> Tuple[bool, Dict]:
        """Run all validations and return overall result."""
        validations = [
            self.validate_concurrent_users(),
            self.validate_queue_wait_times(),
            self.validate_success_rate(),
            self.validate_response_times()
        ]
        
        overall_passed = all(validations)
        
        self.validation_results["overall"] = {
            "passed": overall_passed,
            "total_validations": len(validations),
            "passed_validations": sum(validations)
        }
        
        return overall_passed, self.validation_results
    
    def generate_report(self) -> str:
        """Generate human-readable validation report."""
        if not self.validation_results:
            self.validate_all()
        
        report = "=== Performance Validation Report ===\n\n"
        
        for validation, result in self.validation_results.items():
            if validation == "overall":
                continue
            
            status = "✅ PASS" if result.get("passed", False) else "❌ FAIL"
            report += f"{validation.replace('_', ' ').title()}: {status}\n"
            
            if "requirement" in result:
                report += f"  Requirement: {result['requirement']}\n"
                
            if "actual" in result:
                report += f"  Actual: {result['actual']}\n"
            elif "avg_actual" in result:
                report += f"  Average: {result['avg_actual']:.3f}\n"
                if "max_actual" in result:
                    report += f"  Maximum: {result['max_actual']:.3f}\n"
            
            report += "\n"
        
        overall = self.validation_results.get("overall", {})
        overall_status = "✅ PASS" if overall.get("passed", False) else "❌ FAIL"
        
        report += f"Overall Result: {overall_status}\n"
        report += f"Validations Passed: {overall.get('passed_validations', 0)}/{overall.get('total_validations', 0)}\n"
        
        return report

# Example usage
def validate_load_test_results(results_file: str):
    """Validate load test results from file."""
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    validator = PerformanceValidator(results)
    passed, validation_results = validator.validate_all()
    
    print(validator.generate_report())
    
    # Save detailed results
    with open(f"{results_file}.validation.json", 'w') as f:
        json.dump(validation_results, f, indent=2)
    
    return passed
```

## Results Analysis

### Load Test Report Generator

```python
# tests/load_testing/report_generator.py
import json
import matplotlib.pyplot as plt
import pandas as pd
from typing import Dict, List
import statistics

class LoadTestReportGenerator:
    """Generate comprehensive load test reports."""
    
    def __init__(self, test_results: Dict):
        self.results = test_results
    
    def generate_summary_statistics(self) -> Dict:
        """Generate summary statistics."""
        stats = {
            "test_scenarios": len(self.results),
            "max_concurrent_users": max(self.results.keys()) if self.results else 0,
            "overall_success_rate": statistics.mean([r.get("success_rate", 0) for r in self.results.values()]),
            "peak_response_time": max([r.get("avg_response_time", 0) for r in self.results.values()]),
            "peak_queue_depth": max([r.get("avg_queue_position", 0) for r in self.results.values()])
        }
        
        return stats
    
    def plot_performance_trends(self, output_dir: str = "reports"):
        """Generate performance trend plots."""
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # Extract data for plotting
        users = sorted(self.results.keys())
        success_rates = [self.results[u].get("success_rate", 0) * 100 for u in users]
        response_times = [self.results[u].get("avg_response_time", 0) for u in users]
        queue_positions = [self.results[u].get("avg_queue_position", 0) for u in users]
        
        # Success Rate vs Users
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.plot(users, success_rates, marker='o', linewidth=2, markersize=8)
        plt.axhline(y=95, color='r', linestyle='--', label='Target (95%)')
        plt.xlabel('Concurrent Users')
        plt.ylabel('Success Rate (%)')
        plt.title('Success Rate vs Concurrent Users')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Response Time vs Users
        plt.subplot(2, 2, 2)
        plt.plot(users, response_times, marker='s', linewidth=2, markersize=8, color='orange')
        plt.axhline(y=3000, color='r', linestyle='--', label='Target (3s)')
        plt.xlabel('Concurrent Users')
        plt.ylabel('Response Time (ms)')
        plt.title('Response Time vs Concurrent Users')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Queue Position vs Users
        plt.subplot(2, 2, 3)
        plt.plot(users, queue_positions, marker='^', linewidth=2, markersize=8, color='green')
        plt.xlabel('Concurrent Users')
        plt.ylabel('Average Queue Position')
        plt.title('Queue Position vs Concurrent Users')
        plt.grid(True, alpha=0.3)
        
        # Summary metrics
        plt.subplot(2, 2, 4)
        plt.text(0.1, 0.8, f"Max Users Tested: {max(users)}", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.7, f"Best Success Rate: {max(success_rates):.1f}%", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.6, f"Best Response Time: {min(response_times):.0f}ms", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.5, f"Worst Response Time: {max(response_times):.0f}ms", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.4, f"Peak Queue Position: {max(queue_positions):.1f}", fontsize=12, transform=plt.gca().transAxes)
        plt.title('Test Summary')
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/performance_trends.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Performance trends plot saved to {output_dir}/performance_trends.png")
    
    def generate_html_report(self, output_file: str = "reports/load_test_report.html"):
        """Generate comprehensive HTML report."""
        stats = self.generate_summary_statistics()
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Sora Multi-User Load Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metric {{ margin: 10px 0; }}
                .pass {{ color: green; font-weight: bold; }}
                .fail {{ color: red; font-weight: bold; }}
                .warn {{ color: orange; font-weight: bold; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .chart {{ text-align: center; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Sora Multi-User Load Test Report</h1>
                <p>Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <h2>Executive Summary</h2>
            <div class="metric">Total Test Scenarios: {stats['test_scenarios']}</div>
            <div class="metric">Maximum Concurrent Users: {stats['max_concurrent_users']}</div>
            <div class="metric">Overall Success Rate: <span class="{'pass' if stats['overall_success_rate'] >= 0.95 else 'fail'}">{stats['overall_success_rate']:.1%}</span></div>
            <div class="metric">Peak Response Time: <span class="{'pass' if stats['peak_response_time'] <= 3000 else 'fail'}">{stats['peak_response_time']:.0f}ms</span></div>
            
            <h2>Detailed Results</h2>
            <table>
                <tr>
                    <th>Concurrent Users</th>
                    <th>Success Rate</th>
                    <th>Avg Response Time (ms)</th>
                    <th>Avg Queue Position</th>
                    <th>Error Rate</th>
                    <th>Status</th>
                </tr>
        """
        
        for users in sorted(self.results.keys()):
            result = self.results[users]
            success_rate = result.get("success_rate", 0)
            response_time = result.get("avg_response_time", 0)
            queue_pos = result.get("avg_queue_position", 0)
            error_rate = result.get("error_rate", 0)
            
            # Determine status
            status_class = "pass"
            status_text = "PASS"
            
            if success_rate < 0.95 or response_time > 3000:
                status_class = "fail"
                status_text = "FAIL"
            elif success_rate < 0.98 or response_time > 2000:
                status_class = "warn"
                status_text = "WARN"
            
            html_content += f"""
                <tr>
                    <td>{users}</td>
                    <td>{success_rate:.1%}</td>
                    <td>{response_time:.0f}</td>
                    <td>{queue_pos:.1f}</td>
                    <td>{error_rate:.1%}</td>
                    <td class="{status_class}">{status_text}</td>
                </tr>
            """
        
        html_content += """
            </table>
            
            <h2>Performance Trends</h2>
            <div class="chart">
                <img src="performance_trends.png" alt="Performance Trends" style="max-width: 100%;">
            </div>
            
            <h2>Recommendations</h2>
            <ul>
        """
        
        # Add recommendations based on results
        if stats['overall_success_rate'] < 0.95:
            html_content += "<li>❌ System fails to meet 95% success rate target. Consider scaling workers or optimizing database.</li>"
        else:
            html_content += "<li>✅ System meets success rate requirements.</li>"
        
        if stats['peak_response_time'] > 3000:
            html_content += "<li>❌ Response times exceed 3-second target. Consider load balancing improvements.</li>"
        else:
            html_content += "<li>✅ Response times meet requirements.</li>"
        
        if stats['max_concurrent_users'] >= 15:
            html_content += "<li>✅ System successfully handles 15+ concurrent users.</li>"
        else:
            html_content += "<li>❌ System needs improvement to handle 15+ concurrent users.</li>"
        
        html_content += """
            </ul>
        </body>
        </html>
        """
        
        import os
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w') as f:
            f.write(html_content)
        
        print(f"HTML report generated: {output_file}")
        
        return output_file

# Example usage
def generate_comprehensive_report(results_file: str):
    """Generate comprehensive load test report."""
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    generator = LoadTestReportGenerator(results)
    
    # Generate plots
    generator.plot_performance_trends()
    
    # Generate HTML report
    html_file = generator.generate_html_report()
    
    # Generate validation report
    from validate_performance import PerformanceValidator
    validator = PerformanceValidator(results)
    validation_report = validator.generate_report()
    
    print("\n" + "="*50)
    print("LOAD TEST REPORT GENERATED")
    print("="*50)
    print(validation_report)
    print(f"\nDetailed HTML report: {html_file}")
    print("Performance trends plot: reports/performance_trends.png")
```

This comprehensive load testing guide provides all the tools and procedures needed to validate the multi-user system against the PRP requirements. The testing framework includes realistic user simulation, comprehensive metrics collection, automated validation, and detailed reporting to ensure the system meets its performance targets.