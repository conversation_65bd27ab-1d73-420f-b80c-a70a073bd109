# Multi-User Sora Video Generation - Troubleshooting Guide

This comprehensive troubleshooting guide provides solutions for common issues encountered in the multi-user Azure OpenAI Sora video generation system supporting 10+ concurrent users.

## Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Common Issues](#common-issues)
- [System Health Monitoring](#system-health-monitoring)
- [Performance Issues](#performance-issues)
- [Database Problems](#database-problems)
- [Redis Issues](#redis-issues)
- [Celery Worker Problems](#celery-worker-problems)
- [WebSocket Connection Issues](#websocket-connection-issues)
- [Azure API Integration Problems](#azure-api-integration-problems)
- [Google Veo3 API Integration Problems](#google-veo3-api-integration-problems)
- [Load Balancing Issues](#load-balancing-issues)
- [Security Issues](#security-issues)
- [Emergency Procedures](#emergency-procedures)
- [Log Analysis](#log-analysis)
- [Monitoring and Alerting](#monitoring-and-alerting)

## Quick Diagnostics

### System Status Check Script

```bash
#!/bin/bash
# /opt/sora/scripts/quick-diagnostics.sh

echo "=== Sora Multi-User System Quick Diagnostics ==="
echo "Timestamp: $(date)"
echo

# Check Docker services
echo "--- Docker Services Status ---"
docker-compose -f docker-compose.production.yml ps

echo
echo "--- System Resources ---"
df -h | grep -E "/(|opt|var)"
free -h
uptime

echo
echo "--- Application Health Checks ---"
curl -s http://localhost/health | jq '.' || echo "Health endpoint unreachable"

echo
echo "--- Queue Status ---"
QUEUE_DEPTH=$(docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" llen celery 2>/dev/null || echo "Redis unreachable")
echo "Queue depth: $QUEUE_DEPTH"

echo
echo "--- Worker Status ---"
docker exec sora-worker-1 celery -A src.job_queue.celery_app inspect ping 2>/dev/null || echo "Workers unreachable"

echo
echo "--- Database Connections ---"
docker exec sora-postgres psql -U sora_user -d sora_production -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null || echo "Database unreachable"

echo
echo "--- Recent Errors ---"
echo "Application errors (last 10):"
docker logs sora-app-1 --tail 10 2>/dev/null | grep -i error || echo "No recent errors"

echo
echo "--- Disk Usage ---"
echo "Video storage:"
du -sh /opt/sora/storage/uploads 2>/dev/null || echo "Storage path not found"

echo "=== End Diagnostics ==="
```

### Health Check URLs

```bash
# Application health
curl http://localhost/health

# Database health
curl http://localhost/health/database

# Redis health
curl http://localhost/health/redis

# Queue health
curl http://localhost/api/queue/status

# Worker health via Flower
curl http://localhost:5555/api/workers

# System metrics
curl http://localhost/api/metrics
```

## Common Issues

### 1. Application Won't Start

**Symptoms:**
- Containers fail to start
- Health checks failing
- 502 Bad Gateway errors
- ~~Module naming conflicts (✅ RESOLVED)~~

**Diagnostics:**
```bash
# Check container logs
docker-compose -f docker-compose.production.yml logs app1

# Check environment variables
docker-compose -f docker-compose.production.yml config

# Verify network connectivity
docker network ls
docker network inspect sora-production-network
```

**Solutions:**

1. **Environment Variable Issues:**
   ```bash
   # Verify .env file exists and is readable
   ls -la /opt/sora/.env
   
   # Check for missing required variables
   grep -E "^(AZURE_OPENAI_|DATABASE_URL|SECRET_KEY)" /opt/sora/.env
   
   # Regenerate secret key if needed
   python -c "import secrets; print(secrets.token_urlsafe(32))"
   ```

2. **Database Connection Issues:**
   ```bash
   # Test database connectivity
   docker exec sora-postgres pg_isready -U sora_user -d sora_production
   
   # Check database initialization
   docker exec sora-postgres psql -U sora_user -d sora_production -c "\dt"
   
   # Run migrations if needed
   docker-compose -f docker-compose.production.yml run --rm app1 \
     flask --app src.main:create_app db upgrade
   ```

3. **Port Conflicts:**
   ```bash
   # Check for port conflicts
   netstat -tulpn | grep -E ":(80|443|5432|6379|5001)"
   
   # Update docker-compose.yml if ports are in use
   ```

4. **~~Module Naming Conflicts~~ (✅ RESOLVED):**
   - **Issue**: ~~Flask application failing to start with `AttributeError: module 'queue' has no attribute 'LifoQueue'`~~
   - **Root Cause**: ~~The `src/queue` directory conflicted with Python's built-in `queue` module~~
   - **Solution Applied**: ✅ **FIXED** - Renamed `src/queue` to `src/job_queue` and updated all imports
   - **Status**: Application now starts successfully with full multi-user support
   - **Verification**: All Celery commands now use `src.job_queue.celery_app` module path

### 2. Azure Configuration Issues (⚠️ MOST COMMON - Added 2025-07-29)

**Symptoms:**
- Connection errors when testing Azure API
- 404 or connection refused errors  
- "Invalid hostname" or DNS resolution errors
- Azure API authentication failures

**Root Cause:**
Users copy `.env.example` without replacing placeholder hostnames with real Azure resource names.

**Diagnostics:**
```bash
# Check for placeholder hostnames
grep "your-resource" .env.local .env 2>/dev/null && echo "❌ Found placeholder hostnames"

# Validate Azure endpoint format
echo $AZURE_OPENAI_ENDPOINT | grep -E "^https://[a-zA-Z0-9-]+\.openai\.azure\.com/$" || echo "❌ Invalid endpoint format"

# Test basic connectivity
curl -I "$AZURE_OPENAI_ENDPOINT" --connect-timeout 5 || echo "❌ Cannot connect to endpoint"
```

**Solutions:**

1. **Replace Placeholder Values:**
   ```bash
   # ❌ WRONG - Using placeholder from .env.example
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   
   # ✅ CORRECT - Real Azure resource hostname
   AZURE_OPENAI_ENDPOINT=https://mycompany-eastus.openai.azure.com/
   
   # Find your resource name in Azure Portal → OpenAI → Overview
   ```

2. **Validate Complete Azure Setup:**
   ```bash
   # Create and run validation script
   cat > validate_azure_config.py << 'EOF'
import os
import requests

def validate_azure():
    endpoint = os.getenv('AZURE_OPENAI_ENDPOINT', '')
    api_key = os.getenv('AZURE_OPENAI_API_KEY', '')
    
    if 'your-resource' in endpoint or 'placeholder' in endpoint:
        print("❌ Placeholder hostname detected in AZURE_OPENAI_ENDPOINT")
        return False
    
    if not api_key or 'your-key' in api_key:
        print("❌ Invalid or missing AZURE_OPENAI_API_KEY")
        return False
    
    try:
        test_url = f"{endpoint.rstrip('/')}/openai/deployments?api-version=preview"
        response = requests.get(test_url, headers={'api-key': api_key}, timeout=10)
        
        if response.status_code == 200:
            print("✅ Azure API connection successful")
            return True
        else:
            print(f"❌ Azure API error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    validate_azure()
EOF

   uv run python validate_azure_config.py
   ```

3. **Environment File Priority Check:**
   ```bash
   # Check which environment files exist and their priority
   echo "Environment file priority (highest to lowest):"
   for file in .env.local .env .env.docker; do
       if [ -f "$file" ]; then
           echo "✅ $file (will be used)"
           break
       else
           echo "❌ $file (not found)"
       fi
   done
   ```

### 3. Video Generation Failures

**Symptoms:**
- Jobs stuck in "processing" state
- Azure API errors
- Worker timeout errors

**Diagnostics:**
```bash
# Check worker logs
docker logs sora-worker-1 --tail 50

# Check Azure API connectivity
curl -H "api-key: $AZURE_OPENAI_API_KEY" \
  -H "Content-Type: application/json" \
  "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"

# Check job status in database
docker exec sora-postgres psql -U sora_user -d sora_production -c \
  "SELECT job_id, status, created_at, error_message FROM video_jobs WHERE status IN ('processing', 'failed') ORDER BY created_at DESC LIMIT 10;"
```

**Solutions:**

1. **Azure API Issues:**
   ```bash
   # Verify API credentials
   echo "Endpoint: $AZURE_OPENAI_ENDPOINT"
   echo "API Version: $AZURE_OPENAI_API_VERSION"
   echo "Deployment: $AZURE_OPENAI_SORA_DEPLOYMENT"
   
   # Test API access
   curl -X POST "$AZURE_OPENAI_ENDPOINT/openai/deployments/$AZURE_OPENAI_SORA_DEPLOYMENT/chat/completions?api-version=$AZURE_OPENAI_API_VERSION" \
     -H "Content-Type: application/json" \
     -H "api-key: $AZURE_OPENAI_API_KEY" \
     -d '{"messages":[{"role":"user","content":"test"}],"max_tokens":10}'
   ```

2. **Worker Memory Issues:**
   ```bash
   # Check worker memory usage
   docker stats --no-stream | grep sora-worker
   
   # Restart workers if memory usage is high
   docker-compose -f docker-compose.production.yml restart worker1 worker2 worker3 worker4
   ```

3. **Rate Limiting Issues:**
   ```bash
   # Check rate limit status
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" get rate_limit_window
   
   # Clear rate limit if needed (use cautiously)
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" del rate_limit_window
   ```

### 3. High Memory Usage

**Symptoms:**
- Containers being killed by OOM
- Slow response times
- Worker crashes

**Diagnostics:**
```bash
# Monitor memory usage
docker stats --no-stream

# Check system memory
free -h
cat /proc/meminfo

# Check for memory leaks
docker exec sora-app-1 ps aux --sort=-%mem | head -10
```

**Solutions:**

1. **Increase Container Memory Limits:**
   ```yaml
   # In docker-compose.production.yml
   services:
     app1:
       deploy:
         resources:
           limits:
             memory: 4G  # Increased from 2G
           reservations:
             memory: 2G  # Increased from 1G
   ```

2. **Optimize Database Connections:**
   ```bash
   # Check connection count
   docker exec sora-postgres psql -U sora_user -d sora_production -c \
     "SELECT count(*), state FROM pg_stat_activity GROUP BY state;"
   
   # Reduce connection pool size if needed
   # Update DATABASE_POOL_SIZE environment variable
   ```

3. **Clean Up Old Data:**
   ```bash
   # Clean old video files
   find /opt/sora/storage/uploads -name "*.mp4" -mtime +1 -delete
   
   # Clean old job records
   docker exec sora-postgres psql -U sora_user -d sora_production -c \
     "DELETE FROM video_jobs WHERE status = 'succeeded' AND created_at < NOW() - INTERVAL '7 days';"
   ```

### 4. WebSocket Connection Problems

**Symptoms:**
- Real-time updates not working
- Connection timeouts
- Frequent reconnections

**Diagnostics:**
```bash
# Check WebSocket endpoint
curl -H "Upgrade: websocket" \
     -H "Connection: Upgrade" \
     -H "Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==" \
     -H "Sec-WebSocket-Version: 13" \
     http://localhost/socket.io/

# Check nginx configuration for WebSocket support
docker exec sora-nginx nginx -t

# Monitor WebSocket connections
netstat -an | grep :80 | grep ESTABLISHED | wc -l
```

**Solutions:**

1. **Nginx WebSocket Configuration:**
   ```nginx
   # Ensure WebSocket headers are properly set
   location /socket.io/ {
       proxy_pass http://sora_websocket;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
       proxy_set_header Host $http_host;
       proxy_read_timeout 3600s;
   }
   ```

2. **Session Stickiness:**
   ```nginx
   # Use ip_hash for WebSocket upstream
   upstream sora_websocket {
       ip_hash;
       server app1:5001;
       server app2:5002;
       server app3:5003;
   }
   ```

3. **Client-Side Reconnection:**
   ```javascript
   // Add robust reconnection logic
   const socket = io('wss://your-domain.com', {
       transports: ['websocket'],
       reconnection: true,
       reconnectionDelay: 1000,
       reconnectionAttempts: 5,
       timeout: 20000
   });
   ```

## System Health Monitoring

### Comprehensive Health Check Script

```bash
#!/bin/bash
# /opt/sora/scripts/comprehensive-health-check.sh

set -e

HEALTH_LOG="/opt/sora/logs/health-check.log"
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=85
ALERT_THRESHOLD_DISK=90
ALERT_THRESHOLD_QUEUE=20

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$HEALTH_LOG"
}

check_service_health() {
    local service=$1
    local health_url=$2
    
    if curl -f -s "$health_url" >/dev/null 2>&1; then
        log "✓ $service: Healthy"
        return 0
    else
        log "✗ $service: Unhealthy"
        return 1
    fi
}

check_resource_usage() {
    # CPU usage
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    CPU_NUMERIC=$(echo "$CPU_USAGE" | sed 's/%//')
    
    if [ "$(echo "$CPU_NUMERIC > $ALERT_THRESHOLD_CPU" | bc)" -eq 1 ]; then
        log "⚠ High CPU usage: $CPU_USAGE"
        send_alert "High CPU usage: $CPU_USAGE"
    else
        log "✓ CPU usage: $CPU_USAGE"
    fi
    
    # Memory usage
    MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
    
    if [ "$(echo "$MEMORY_USAGE > $ALERT_THRESHOLD_MEMORY" | bc)" -eq 1 ]; then
        log "⚠ High memory usage: ${MEMORY_USAGE}%"
        send_alert "High memory usage: ${MEMORY_USAGE}%"
    else
        log "✓ Memory usage: ${MEMORY_USAGE}%"
    fi
    
    # Disk usage
    DISK_USAGE=$(df -h /opt/sora | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$DISK_USAGE" -gt "$ALERT_THRESHOLD_DISK" ]; then
        log "⚠ High disk usage: ${DISK_USAGE}%"
        send_alert "High disk usage: ${DISK_USAGE}%"
    else
        log "✓ Disk usage: ${DISK_USAGE}%"
    fi
}

check_queue_health() {
    # Queue depth
    QUEUE_DEPTH=$(docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" llen celery 2>/dev/null || echo "0")
    
    if [ "$QUEUE_DEPTH" -gt "$ALERT_THRESHOLD_QUEUE" ]; then
        log "⚠ High queue depth: $QUEUE_DEPTH jobs"
        send_alert "High queue depth: $QUEUE_DEPTH jobs"
    else
        log "✓ Queue depth: $QUEUE_DEPTH jobs"
    fi
    
    # Failed jobs
    FAILED_JOBS=$(docker exec sora-postgres psql -U sora_user -d sora_production -t -c \
        "SELECT count(*) FROM video_jobs WHERE status = 'failed' AND created_at > NOW() - INTERVAL '1 hour';" 2>/dev/null || echo "0")
    
    if [ "$FAILED_JOBS" -gt 5 ]; then
        log "⚠ High failure rate: $FAILED_JOBS failed jobs in last hour"
        send_alert "High failure rate: $FAILED_JOBS failed jobs in last hour"
    else
        log "✓ Failed jobs (last hour): $FAILED_JOBS"
    fi
}

check_worker_health() {
    local healthy_workers=0
    local total_workers=0
    
    for worker in $(docker-compose -f docker-compose.production.yml ps | grep "sora-worker" | awk '{print $1}'); do
        total_workers=$((total_workers + 1))
        
        if docker exec "$worker" celery -A src.job_queue.celery_app inspect ping >/dev/null 2>&1; then
            healthy_workers=$((healthy_workers + 1))
        else
            log "⚠ Worker $worker is not responding"
            send_alert "Worker $worker is not responding"
        fi
    done
    
    log "✓ Workers: $healthy_workers/$total_workers healthy"
    
    if [ "$healthy_workers" -eq 0 ]; then
        log "🚨 CRITICAL: No healthy workers available"
        send_alert "CRITICAL: No healthy workers available"
    fi
}

send_alert() {
    local message=$1
    
    # Send to Slack
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 Sora Alert: $message\"}" \
            "$SLACK_WEBHOOK_URL" || true
    fi
    
    # Send email (if configured)
    if [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "Sora System Alert" "$ALERT_EMAIL" || true
    fi
    
    # Log to system log
    logger "Sora Alert: $message"
}

main() {
    log "=== Starting comprehensive health check ==="
    
    # Check service health
    check_service_health "Application" "http://localhost/health"
    check_service_health "Database" "http://localhost/health/database"
    check_service_health "Redis" "http://localhost/health/redis"
    
    # Check resource usage
    check_resource_usage
    
    # Check queue health
    check_queue_health
    
    # Check worker health
    check_worker_health
    
    log "=== Health check completed ==="
}

main "$@"
```

## Performance Issues

### High Latency Diagnosis

```bash
#!/bin/bash
# /opt/sora/scripts/diagnose-latency.sh

echo "=== Latency Diagnosis ==="

# Application response time
echo "--- Application Response Times ---"
for i in {1..5}; do
    curl -w "Response time: %{time_total}s\n" -o /dev/null -s http://localhost/health
    sleep 1
done

# Database response time
echo "--- Database Response Times ---"
for i in {1..5}; do
    time docker exec sora-postgres psql -U sora_user -d sora_production -c "SELECT 1;" >/dev/null
done

# Redis response time
echo "--- Redis Response Times ---"
for i in {1..5}; do
    time docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" ping >/dev/null
done

# Azure API response time
echo "--- Azure API Response Times ---"
for i in {1..3}; do
    curl -w "Azure API time: %{time_total}s\n" -o /dev/null -s \
        -H "api-key: $AZURE_OPENAI_API_KEY" \
        "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"
    sleep 2
done

# Network connectivity
echo "--- Network Connectivity ---"
ping -c 4 *******
dig google.com @*******

echo "=== End Latency Diagnosis ==="
```

### Performance Optimization

1. **Database Query Optimization:**
   ```sql
   -- Identify slow queries
   SELECT query, mean_time, calls, total_time
   FROM pg_stat_statements 
   WHERE mean_time > 1000  -- queries taking more than 1 second
   ORDER BY mean_time DESC 
   LIMIT 10;
   
   -- Check for missing indexes
   SELECT schemaname, tablename, attname, n_distinct, correlation
   FROM pg_stats
   WHERE schemaname = 'public'
   AND n_distinct > 100
   AND correlation < 0.1;
   
   -- Add indexes for video_jobs table
   CREATE INDEX CONCURRENTLY idx_video_jobs_session_status 
   ON video_jobs(session_id, status);
   
   CREATE INDEX CONCURRENTLY idx_video_jobs_created_at_status 
   ON video_jobs(created_at, status) 
   WHERE status IN ('queued', 'processing');
   ```

2. **Redis Performance Tuning:**
   ```bash
   # Monitor Redis performance
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" info stats
   
   # Check slow log
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" slowlog get 10
   
   # Monitor memory usage
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" info memory
   
   # Optimize Redis configuration
   # In redis.conf:
   # maxmemory-policy allkeys-lru
   # tcp-keepalive 300
   # timeout 0
   ```

3. **Application Performance:**
   ```bash
   # Profile application
   docker exec sora-app-1 python -m cProfile -o profile.stats src/main.py
   
   # Monitor garbage collection
   docker exec sora-app-1 python -X dev -c "
   import gc
   print('GC stats:', gc.get_stats())
   "
   
   # Check for memory leaks
   docker exec sora-app-1 python -c "
   import psutil
   import os
   process = psutil.Process(os.getpid())
   print('Memory usage:', process.memory_info().rss / 1024 / 1024, 'MB')
   "
   ```

## Database Problems

### Connection Pool Exhaustion

**Symptoms:**
- "too many connections" errors
- Application timeouts
- Database refusing connections

**Solutions:**

1. **Check Connection Usage:**
   ```sql
   -- Current connections
   SELECT count(*), state, application_name 
   FROM pg_stat_activity 
   GROUP BY state, application_name;
   
   -- Idle connections
   SELECT count(*) as idle_connections 
   FROM pg_stat_activity 
   WHERE state = 'idle';
   
   -- Long-running queries
   SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
   FROM pg_stat_activity 
   WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
   ```

2. **Optimize Connection Pool:**
   ```bash
   # Update environment variables
   DATABASE_POOL_SIZE=10
   DATABASE_MAX_OVERFLOW=20
   DATABASE_POOL_TIMEOUT=30
   DATABASE_POOL_RECYCLE=3600
   ```

3. **Kill Idle Connections:**
   ```sql
   -- Kill idle connections older than 1 hour
   SELECT pg_terminate_backend(pid)
   FROM pg_stat_activity
   WHERE state = 'idle'
   AND now() - state_change > interval '1 hour';
   ```

### Database Locks

**Symptoms:**
- Queries hanging
- Deadlock errors
- High wait times

**Diagnosis:**
```sql
-- Check for locks
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity 
    ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity 
    ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;
```

**Solutions:**

1. **Kill Blocking Queries:**
   ```sql
   -- Kill specific blocking process
   SELECT pg_terminate_backend(blocking_pid);
   
   -- Kill all long-running queries
   SELECT pg_terminate_backend(pid)
   FROM pg_stat_activity
   WHERE state = 'active'
   AND now() - query_start > interval '10 minutes';
   ```

2. **Optimize Queries:**
   ```sql
   -- Add appropriate indexes
   -- Use smaller transactions
   -- Avoid long-running operations during peak hours
   ```

## Redis Issues

### Memory Issues

**Symptoms:**
- Redis running out of memory
- Slow performance
- Connection timeouts

**Diagnosis:**
```bash
# Check memory usage
docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" info memory

# Check key distribution
docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" --bigkeys

# Monitor memory usage over time
docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" monitor | grep -E "(SET|DEL|EXPIRE)"
```

**Solutions:**

1. **Clean Up Old Keys:**
   ```bash
   # Clean expired session keys
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" eval "
   local keys = redis.call('keys', 'session:*')
   local deleted = 0
   for i=1,#keys do
       local ttl = redis.call('ttl', keys[i])
       if ttl == -1 then
           redis.call('del', keys[i])
           deleted = deleted + 1
       end
   end
   return deleted
   " 0
   
   # Clean old rate limit data
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" eval "
   local keys = redis.call('keys', 'rate_limit:*')
   for i=1,#keys do
       redis.call('zremrangebyscore', keys[i], 0, $(date +%s)-3600)
   end
   " 0
   ```

2. **Optimize Memory Settings:**
   ```ini
   # In redis.conf
   maxmemory 2gb
   maxmemory-policy allkeys-lru
   maxmemory-samples 5
   
   # Enable compression
   hash-max-ziplist-entries 512
   hash-max-ziplist-value 64
   list-max-ziplist-size -2
   set-max-intset-entries 512
   zset-max-ziplist-entries 128
   zset-max-ziplist-value 64
   ```

### Connection Issues

**Symptoms:**
- "Connection refused" errors
- Timeout errors
- High connection count

**Solutions:**

1. **Check Connection Limits:**
   ```bash
   # Check current connections
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" info clients
   
   # Increase connection limit if needed
   # In redis.conf: tcp-backlog 511
   ```

2. **Connection Pooling:**
   ```python
   # Optimize Redis connection pool in application
   import redis.asyncio as redis
   
   pool = redis.ConnectionPool(
       host='redis-primary',
       port=6379,
       password='redis-password',
       max_connections=20,
       socket_keepalive=True,
       socket_keepalive_options={},
       retry_on_timeout=True,
       socket_connect_timeout=5,
       socket_timeout=5
   )
   ```

## Celery Worker Problems

### Worker Not Processing Jobs

**Symptoms:**
- Jobs stuck in queue
- Workers appear offline
- No job progress updates

**Diagnosis:**
```bash
# Check worker status
docker exec sora-worker-1 celery -A src.job_queue.celery_app inspect active
docker exec sora-worker-1 celery -A src.job_queue.celery_app inspect reserved
docker exec sora-worker-1 celery -A src.job_queue.celery_app inspect stats

# Check worker logs
docker logs sora-worker-1 --tail 50

# Check queue status
docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" llen celery
docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" lrange celery 0 10
```

**Solutions:**

1. **Restart Workers:**
   ```bash
   # Graceful restart
   docker exec sora-worker-1 celery -A src.job_queue.celery_app control shutdown
   docker-compose -f docker-compose.production.yml restart worker1
   
   # Force restart all workers
   docker-compose -f docker-compose.production.yml restart worker1 worker2 worker3 worker4
   ```

2. **Clear Queue:**
   ```bash
   # Purge failed jobs (use cautiously)
   docker exec sora-worker-1 celery -A src.job_queue.celery_app purge
   
   # Remove specific job
   docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" lrem celery 1 "job_data"
   ```

3. **Scale Workers:**
   ```bash
   # Add more workers temporarily
   docker-compose -f docker-compose.production.yml up -d --scale worker=6
   
   # Monitor worker performance
   docker stats | grep worker
   ```

### Worker Memory Leaks

**Symptoms:**
- Workers consuming increasing memory
- Out of memory errors
- Worker crashes

**Solutions:**

1. **Restart Workers Periodically:**
   ```bash
   # Set max tasks per worker
   # In docker-compose.yml:
   environment:
     - WORKER_MAX_TASKS_PER_CHILD=100
   ```

2. **Monitor Worker Memory:**
   ```bash
   #!/bin/bash
   # Monitor worker memory and restart if needed
   for worker in worker1 worker2 worker3 worker4; do
       MEMORY_MB=$(docker stats --no-stream --format "{{.MemUsage}}" sora-$worker | cut -d'/' -f1 | sed 's/[^0-9.]//g')
       if (( $(echo "$MEMORY_MB > 1500" | bc -l) )); then
           echo "Restarting $worker due to high memory usage: ${MEMORY_MB}MB"
           docker-compose -f docker-compose.production.yml restart $worker
       fi
   done
   ```

## Emergency Procedures

### Complete System Recovery

```bash
#!/bin/bash
# /opt/sora/scripts/emergency-recovery.sh

set -e

echo "=== EMERGENCY RECOVERY PROCEDURE ==="
echo "This will restart all services and may cause downtime"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
fi

# Stop all services
echo "Stopping all services..."
docker-compose -f docker-compose.production.yml down

# Clean up resources
echo "Cleaning up Docker resources..."
docker system prune -f
docker volume prune -f

# Restart infrastructure services first
echo "Starting infrastructure services..."
docker-compose -f docker-compose.production.yml up -d postgres redis

# Wait for infrastructure to be ready
echo "Waiting for infrastructure..."
sleep 30

# Check database connectivity
until docker exec sora-postgres pg_isready -U sora_user -d sora_production; do
    echo "Waiting for database..."
    sleep 5
done

# Check Redis connectivity
until docker exec sora-redis redis-cli -a "$REDIS_PASSWORD" ping; do
    echo "Waiting for Redis..."
    sleep 5
done

# Start application services
echo "Starting application services..."
docker-compose -f docker-compose.production.yml up -d app1 app2 app3

# Wait for applications to be ready
sleep 30

# Start workers
echo "Starting workers..."
docker-compose -f docker-compose.production.yml up -d worker1 worker2 worker3 worker4

# Start supporting services
echo "Starting supporting services..."
docker-compose -f docker-compose.production.yml up -d scheduler flower nginx

# Verify system health
echo "Verifying system health..."
sleep 60

if curl -f http://localhost/health; then
    echo "✓ Emergency recovery completed successfully"
else
    echo "✗ Emergency recovery failed - manual intervention required"
    exit 1
fi
```

### Data Recovery

```bash
#!/bin/bash
# /opt/sora/scripts/restore-from-backup.sh

BACKUP_DATE=${1:-latest}
BACKUP_DIR="/opt/sora/backups"

if [ "$BACKUP_DATE" = "latest" ]; then
    DB_BACKUP=$(ls -t "$BACKUP_DIR/database/"*.sql.gz | head -1)
    REDIS_BACKUP=$(ls -t "$BACKUP_DIR/redis/"*.rdb.gz | head -1)
else
    DB_BACKUP="$BACKUP_DIR/database/sora_production_${BACKUP_DATE}.sql.gz"
    REDIS_BACKUP="$BACKUP_DIR/redis/redis_${BACKUP_DATE}.rdb.gz"
fi

echo "=== DATA RECOVERY ==="
echo "Database backup: $DB_BACKUP"
echo "Redis backup: $REDIS_BACKUP"
read -p "Continue with restore? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
fi

# Stop services
docker-compose -f docker-compose.production.yml stop app1 app2 app3 worker1 worker2 worker3 worker4

# Restore database
echo "Restoring database..."
gunzip -c "$DB_BACKUP" | docker exec -i sora-postgres pg_restore -U sora_user -d sora_production --clean --if-exists

# Restore Redis
echo "Restoring Redis..."
docker-compose -f docker-compose.production.yml stop redis
gunzip -c "$REDIS_BACKUP" > /tmp/dump.rdb
docker cp /tmp/dump.rdb sora-redis:/data/dump.rdb
docker-compose -f docker-compose.production.yml start redis
rm /tmp/dump.rdb

# Restart services
echo "Restarting services..."
docker-compose -f docker-compose.production.yml start app1 app2 app3 worker1 worker2 worker3 worker4

echo "✓ Data recovery completed"
```

## Log Analysis

### Centralized Logging Commands

```bash
# Application logs
docker logs sora-app-1 --tail 100 --follow

# Worker logs
docker logs sora-worker-1 --tail 100 --follow

# Database logs
docker exec sora-postgres tail -f /var/log/postgresql/postgresql-*.log

# Nginx access logs
docker exec sora-nginx tail -f /var/log/nginx/access.log

# Nginx error logs
docker exec sora-nginx tail -f /var/log/nginx/error.log

# Search for errors across all logs
docker-compose -f docker-compose.production.yml logs | grep -i error

# Search for specific patterns
docker-compose -f docker-compose.production.yml logs | grep -E "(timeout|failed|exception)"
```

### Log Analysis Scripts

```bash
#!/bin/bash
# /opt/sora/scripts/analyze-logs.sh

LOG_DIR="/opt/sora/logs"
DATE=${1:-$(date +%Y-%m-%d)}

echo "=== Log Analysis for $DATE ==="

# Error summary
echo "--- Error Summary ---"
grep -h "$DATE" "$LOG_DIR"/*.log 2>/dev/null | grep -i error | sort | uniq -c | sort -nr | head -10

# Performance issues
echo "--- Performance Issues ---"
grep -h "$DATE" "$LOG_DIR"/*.log 2>/dev/null | grep -E "(slow|timeout|high|memory)" | wc -l

# User activity
echo "--- User Activity ---"
grep -h "$DATE" "$LOG_DIR"/*.log 2>/dev/null | grep -E "(POST /generate|session)" | wc -l

# Queue statistics
echo "--- Queue Statistics ---"
grep -h "$DATE" "$LOG_DIR"/*.log 2>/dev/null | grep -E "queue" | tail -10

echo "=== End Log Analysis ==="
```

## Google Veo3 API Integration Problems

### Google Veo3 Operation Status Polling 400/404 Errors

**Issue**: Google Veo3 provider fails during operation status polling with HTTP 400 Bad Request or HTTP 404 Not Found errors.

**Symptoms:**
- Video generation submission succeeds (HTTP 200 OK)
- Operation status polling fails with 400 Bad Request or 404 Not Found
- Error messages like "Operation status check failed: 400" or "Operation status check failed: 404"
- Complete video generation workflow fails during polling phase

**Root Cause Analysis:**

**400 Bad Request**: Incorrect headers sent to Google Cloud Operations API
- `Content-Type: application/json` header sent on GET requests (incorrect)
- Authentication headers improperly formatted for Operations API
- Mixed API Key and OAuth2 authentication

**404 Not Found**: Expected behavior for Vertex AI model operations
- Vertex AI model operations may not support direct HTTP status queries
- This is normal behavior, not an error - requires graceful handling

**Diagnostics:**

```bash
# 1. Check Google Cloud credentials
echo "GOOGLE_APPLICATION_CREDENTIALS: $GOOGLE_APPLICATION_CREDENTIALS"
ls -la "$GOOGLE_APPLICATION_CREDENTIALS" 2>/dev/null || echo "❌ Credentials file missing"

# 2. Test OAuth2 authentication
gcloud auth application-default print-access-token > /dev/null 2>&1 && echo "✅ OAuth2 working" || echo "❌ OAuth2 failed"

# 3. Check Veo3 provider logs for specific error
grep -E "(google_veo3|Veo3|400|404|Operation status check)" logs/celery.log | tail -10

# 4. Test basic API connectivity
curl -H "Authorization: Bearer $(gcloud auth application-default print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/YOUR_PROJECT/locations/us-central1/models" \
  && echo "✅ API accessible" || echo "❌ API connection failed"
```

**Solutions:**

**For 400 Bad Request Errors:**

1. **Update Authentication Headers** (Fixed in provider_adapter.py):
   ```python
   # ✅ CORRECT - Dedicated method for operations API
   async def _get_operation_auth_headers(self) -> Dict[str, str]:
       access_token = await self._get_access_token()
       return {
           "Authorization": f"Bearer {access_token}",
           # No Content-Type header for GET requests
       }
   ```

2. **Verify OAuth2 Setup**:
   ```bash
   # Ensure service account has proper permissions
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:YOUR_SERVICE_ACCOUNT@YOUR_PROJECT.iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"
   
   # Test authentication
   gcloud auth activate-service-account --key-file="$GOOGLE_APPLICATION_CREDENTIALS"
   gcloud auth application-default login
   ```

**For 404 Not Found Errors:**

1. **Graceful 404 Handling** (Implemented in provider_adapter.py):
   ```python
   # ✅ CORRECT - Handle expected 404 responses
   if response.status_code == 404:
       # Expected behavior for Vertex AI - return reasonable status
       return {
           "done": False,
           "name": operation_name,
           "metadata": {
               "progressMessage": "Video generation in progress via Vertex AI"
           }
       }
   ```

2. **Alternative Status Monitoring**:
   - Consider webhook-based status updates for production
   - Implement exponential backoff for polling
   - Use timeout-based completion detection

**Prevention:**

1. **Always use dedicated headers for GET requests** - Never include Content-Type
2. **Force OAuth2 for Operations API** - API keys often fail for operations
3. **Expect 404 responses from Vertex AI** - Handle gracefully
4. **Test end-to-end after setup** - Use browser E2E testing to validate

**Validation Commands:**

```bash
# Test complete Veo3 workflow
./scripts/dev-local.sh &  # Start development environment
sleep 10
curl -X POST http://localhost:5001/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt":"Test video","provider":"google_veo3"}' \
  && echo "✅ Veo3 submission working" || echo "❌ Veo3 submission failed"

# Monitor for 400/404 errors
tail -f logs/celery.log | grep -E "(400|404|Operation status check)" &
```

**Related Issues:**
- Authentication setup: [Google Cloud Authentication Guide](https://cloud.google.com/docs/authentication)
- Service account permissions: [AI Platform IAM](https://cloud.google.com/vertex-ai/docs/general/access-control)
- Vertex AI Veo3 documentation: [Video Generation API](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/video-generation)

---

This troubleshooting guide provides comprehensive solutions for common issues in the multi-user system. For any issues not covered here, check the system logs and consider consulting the monitoring dashboards for additional insights.