# Multi-User Sora Video Generation - Monitoring & Alerting Setup

This guide provides comprehensive monitoring and alerting setup for the multi-user Azure OpenAI Sora video generation system, ensuring visibility into system performance, early detection of issues, and proactive maintenance.

## Table of Contents

- [Monitoring Architecture](#monitoring-architecture)
- [Prometheus Configuration](#prometheus-configuration)
- [Grafana Dashboards](#grafana-dashboards)
- [Log Aggregation (ELK Stack)](#log-aggregation-elk-stack)
- [Health Checks](#health-checks)
- [Alerting Rules](#alerting-rules)
- [Performance Metrics](#performance-metrics)
- [Operational Procedures](#operational-procedures)
- [Troubleshooting](#troubleshooting)

## Monitoring Architecture

### Components Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Monitoring Stack                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Prometheus    │    Grafana      │    ELK Stack            │
│   - Metrics     │    - Dashboards │    - Logs               │
│   - Alerts      │    - Analytics  │    - Search             │
│   - Storage     │    - Users      │    - Analysis           │
└─────────────────┴─────────────────┴─────────────────────────┘
         │                  │                  │
         ▼                  ▼                  ▼
┌─────────────────────────────────────────────────────────────┐
│                  Data Collection                             │
├─────────────────┬─────────────────┬─────────────────────────┤
│  Node Exporter  │ App Metrics     │  Log Exporters          │
│  - System       │ - API metrics   │  - Application logs     │
│  - Hardware     │ - Queue stats   │  - Container logs       │
│  - Network      │ - DB metrics    │  - System logs          │
└─────────────────┴─────────────────┴─────────────────────────┘
         │                  │                  │
         ▼                  ▼                  ▼
┌─────────────────────────────────────────────────────────────┐
│                Application Services                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│  Flask Apps     │  Celery Workers │  Infrastructure         │
│  - Health       │  - Job metrics  │  - PostgreSQL           │
│  - Performance  │  - Queue stats  │  - Redis                │
│  - Errors       │  - Failures     │  - Nginx                │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### Monitoring Objectives

1. **Performance Monitoring**
   - Response times and throughput
   - Queue performance and wait times
   - Resource utilization (CPU, memory, disk)

2. **Availability Monitoring**
   - Service health and uptime
   - External API connectivity
   - Database and cache availability

3. **Business Metrics**
   - Video generation success rates
   - User session patterns
   - Azure API usage and costs

4. **Security Monitoring**
   - Failed authentication attempts
   - Unusual traffic patterns
   - Certificate expiration

## Prometheus Configuration

### Main Configuration

Create `/opt/sora/monitoring/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'sora-production'
    environment: 'production'

rule_files:
  - "rules/sora_alerts.yml"
  - "rules/infrastructure_alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Sora application instances
  - job_name: 'sora-app'
    static_configs:
      - targets: 
          - 'app1:5001'
          - 'app2:5001' 
          - 'app3:5001'
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Celery workers
  - job_name: 'celery-workers'
    static_configs:
      - targets:
          - 'worker1:8080'
          - 'worker2:8080'
          - 'worker3:8080'
          - 'worker4:8080'
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # Node metrics (system-level)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

# Storage configuration
storage:
  tsdb:
    path: /prometheus/data
    retention.time: 30d
    retention.size: 10GB
```

### Alert Rules

Create `/opt/sora/monitoring/rules/sora_alerts.yml`:

```yaml
groups:
  - name: sora_application
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
          service: sora-app
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.instance }}"

      # Slow response times
      - alert: SlowResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 3
        for: 5m
        labels:
          severity: warning
          service: sora-app
        annotations:
          summary: "Slow response times detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.instance }}"

      # Queue depth too high
      - alert: HighQueueDepth
        expr: redis_list_length{list="celery"} > 25
        for: 5m
        labels:
          severity: warning
          service: queue
        annotations:
          summary: "Queue depth is high"
          description: "Queue depth is {{ $value }} jobs"

      # No active workers
      - alert: NoActiveWorkers
        expr: sum(up{job="celery-workers"}) == 0
        for: 1m
        labels:
          severity: critical
          service: workers
        annotations:
          summary: "No active workers"
          description: "All Celery workers are down"

      # High job failure rate
      - alert: HighJobFailureRate
        expr: rate(video_jobs_failed_total[10m]) / rate(video_jobs_total[10m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: video-generation
        annotations:
          summary: "High video generation failure rate"
          description: "Failure rate is {{ $value | humanizePercentage }}"

      # Azure API connectivity issues
      - alert: AzureAPIDown
        expr: up{job="azure-api"} == 0
        for: 2m
        labels:
          severity: critical
          service: azure-api
        annotations:
          summary: "Azure OpenAI API is unreachable"
          description: "Cannot connect to Azure OpenAI API"

  - name: sora_infrastructure
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      # Low disk space
      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"

      # Database connection issues
      - alert: DatabaseConnectionHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "High database connections"
          description: "Database connection usage is {{ $value }}%"

      # Redis memory usage high
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value }}%"
```

### Application Metrics Instrumentation

Add metrics to the Flask application:

```python
# src/monitoring/metrics_exporter.py
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from flask import Response
import time
import functools

# Define metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

QUEUE_DEPTH = Gauge(
    'queue_depth',
    'Current queue depth',
    ['queue_name']
)

VIDEO_JOBS_TOTAL = Counter(
    'video_jobs_total',
    'Total video generation jobs',
    ['status']
)

VIDEO_JOBS_DURATION = Histogram(
    'video_jobs_duration_seconds',
    'Video generation job duration',
    buckets=[30, 60, 120, 300, 600, 1200]
)

ACTIVE_SESSIONS = Gauge(
    'active_sessions_total',
    'Number of active user sessions'
)

AZURE_API_REQUESTS = Counter(
    'azure_api_requests_total',
    'Total Azure API requests',
    ['status']
)

AZURE_API_DURATION = Histogram(
    'azure_api_duration_seconds',
    'Azure API request duration'
)

def track_requests(f):
    """Decorator to track HTTP requests."""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            response = f(*args, **kwargs)
            status = getattr(response, 'status_code', 200)
        except Exception as e:
            status = 500
            raise
        finally:
            duration = time.time() - start_time
            
            # Record metrics
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.endpoint or 'unknown',
                status=status
            ).inc()
            
            REQUEST_DURATION.labels(
                method=request.method,
                endpoint=request.endpoint or 'unknown'
            ).observe(duration)
        
        return response
    
    return wrapper

def update_queue_metrics():
    """Update queue depth metrics."""
    try:
        import redis
        r = redis.Redis(host='redis-primary', port=6379, db=0)
        
        # Update queue depth
        celery_queue_depth = r.llen('celery')
        QUEUE_DEPTH.labels(queue_name='celery').set(celery_queue_depth)
        
        # Update active sessions count
        session_keys = r.keys('session:*')
        ACTIVE_SESSIONS.set(len(session_keys))
        
    except Exception as e:
        print(f"Error updating queue metrics: {e}")

def track_video_job(status: str, duration: float = None):
    """Track video job metrics."""
    VIDEO_JOBS_TOTAL.labels(status=status).inc()
    
    if duration is not None:
        VIDEO_JOBS_DURATION.observe(duration)

def track_azure_api(status: str, duration: float):
    """Track Azure API metrics."""
    AZURE_API_REQUESTS.labels(status=status).inc()
    AZURE_API_DURATION.observe(duration)

def metrics_endpoint():
    """Prometheus metrics endpoint."""
    # Update dynamic metrics
    update_queue_metrics()
    
    return Response(
        generate_latest(),
        mimetype='text/plain'
    )
```

### Worker Metrics

Add metrics to Celery workers:

```python
# src/queue/metrics.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server
from celery.signals import task_prerun, task_postrun, task_failure
import time
import threading

# Worker metrics
WORKER_TASKS_TOTAL = Counter(
    'celery_tasks_total',
    'Total Celery tasks',
    ['task_name', 'status']
)

WORKER_TASK_DURATION = Histogram(
    'celery_task_duration_seconds',
    'Celery task duration',
    ['task_name']
)

WORKER_ACTIVE_TASKS = Gauge(
    'celery_active_tasks',
    'Currently active tasks',
    ['worker_name']
)

# Store task start times
task_start_times = {}

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Record task start time."""
    task_start_times[task_id] = time.time()
    
    # Update active tasks gauge
    worker_name = task.request.hostname
    WORKER_ACTIVE_TASKS.labels(worker_name=worker_name).inc()

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    """Record task completion."""
    start_time = task_start_times.pop(task_id, None)
    
    if start_time:
        duration = time.time() - start_time
        task_name = task.name if task else 'unknown'
        
        WORKER_TASK_DURATION.labels(task_name=task_name).observe(duration)
        WORKER_TASKS_TOTAL.labels(task_name=task_name, status='completed').inc()
    
    # Update active tasks gauge
    worker_name = task.request.hostname if task else 'unknown'
    WORKER_ACTIVE_TASKS.labels(worker_name=worker_name).dec()

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, einfo=None, **kwds):
    """Record task failure."""
    start_time = task_start_times.pop(task_id, None)
    
    if start_time:
        duration = time.time() - start_time
        task_name = sender.name if sender else 'unknown'
        
        WORKER_TASK_DURATION.labels(task_name=task_name).observe(duration)
        WORKER_TASKS_TOTAL.labels(task_name=task_name, status='failed').inc()

def start_metrics_server(port=8080):
    """Start Prometheus metrics server for worker."""
    start_http_server(port)
    print(f"Worker metrics server started on port {port}")
```

## Grafana Dashboards

### Main Dashboard Configuration

Create `/opt/sora/monitoring/grafana/dashboards/sora_main_dashboard.json`:

```json
{
  "dashboard": {
    "id": null,
    "title": "Sora Multi-User System Dashboard",
    "tags": ["sora", "production"],
    "timezone": "UTC",
    "refresh": "30s",
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "panels": [
      {
        "id": 1,
        "title": "System Overview",
        "type": "stat",
        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0},
        "targets": [
          {
            "expr": "sum(up{job=~'sora-app|celery-workers'})",
            "legendFormat": "Active Services"
          },
          {
            "expr": "sum(active_sessions_total)",
            "legendFormat": "Active Sessions"
          },
          {
            "expr": "sum(queue_depth)",
            "legendFormat": "Queue Depth"
          },
          {
            "expr": "rate(video_jobs_total[5m]) * 60",
            "legendFormat": "Jobs/min"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {"mode": "palette-classic"},
            "unit": "short"
          }
        }
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4},
        "targets": [
          {
            "expr": "sum(rate(http_requests_total[5m])) by (status)",
            "legendFormat": "{{status}} requests/sec"
          }
        ],
        "yAxes": [
          {"label": "Requests/sec", "min": 0}
        ]
      },
      {
        "id": 3,
        "title": "Response Time",
        "type": "graph", 
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4},
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {"label": "Response Time (s)", "min": 0}
        ]
      },
      {
        "id": 4,
        "title": "Queue Metrics",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12},
        "targets": [
          {
            "expr": "queue_depth{queue_name='celery'}",
            "legendFormat": "Queue Depth"
          },
          {
            "expr": "sum(celery_active_tasks)",
            "legendFormat": "Active Tasks"
          }
        ],
        "yAxes": [
          {"label": "Count", "min": 0}
        ]
      },
      {
        "id": 5,
        "title": "Video Generation Metrics",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12},
        "targets": [
          {
            "expr": "rate(video_jobs_total{status='succeeded'}[5m]) * 60",
            "legendFormat": "Successful jobs/min"
          },
          {
            "expr": "rate(video_jobs_total{status='failed'}[5m]) * 60",
            "legendFormat": "Failed jobs/min"
          }
        ],
        "yAxes": [
          {"label": "Jobs/min", "min": 0}
        ]
      },
      {
        "id": 6,
        "title": "System Resources",
        "type": "graph",
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 20},
        "targets": [
          {
            "expr": "100 - (avg(rate(node_cpu_seconds_total{mode='idle'}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          },
          {
            "expr": "(1 - (node_filesystem_avail_bytes{mountpoint='/'} / node_filesystem_size_bytes{mountpoint='/'})) * 100",
            "legendFormat": "Disk Usage %"
          }
        ],
        "yAxes": [
          {"label": "Percentage", "min": 0, "max": 100}
        ]
      }
    ]
  }
}
```

### Worker Dashboard

Create `/opt/sora/monitoring/grafana/dashboards/sora_workers_dashboard.json`:

```json
{
  "dashboard": {
    "title": "Sora Workers Dashboard",
    "panels": [
      {
        "title": "Worker Health",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(up{job='celery-workers'})",
            "legendFormat": "Active Workers"
          }
        ]
      },
      {
        "title": "Task Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(celery_tasks_total{status='completed'}[5m])",
            "legendFormat": "Completed Tasks/sec"
          },
          {
            "expr": "rate(celery_tasks_total{status='failed'}[5m])",
            "legendFormat": "Failed Tasks/sec"
          }
        ]
      },
      {
        "title": "Task Duration",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(celery_task_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(celery_task_duration_seconds_bucket[5m]))",
            "legendFormat": "Median"
          }
        ]
      }
    ]
  }
}
```

## Log Aggregation (ELK Stack)

### Elasticsearch Configuration

Create `/opt/sora/elasticsearch/elasticsearch.yml`:

```yaml
cluster.name: sora-logs
node.name: elasticsearch-1
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

network.host: 0.0.0.0
http.port: 9200

discovery.type: single-node

# Security
xpack.security.enabled: true
xpack.security.authc.basic_auth.enabled: true

# Performance
indices.memory.index_buffer_size: 512mb
thread_pool.write.queue_size: 1000
```

### Logstash Pipeline

Create `/opt/sora/logstash/pipeline/sora.conf`:

```ruby
input {
  file {
    path => "/var/log/app/*.log"
    start_position => "beginning"
    tags => ["application"]
    codec => json
  }
  
  file {
    path => "/var/log/workers/*.log"
    start_position => "beginning"  
    tags => ["worker"]
    codec => json
  }
  
  file {
    path => "/var/log/nginx/access.log"
    start_position => "beginning"
    tags => ["nginx", "access"]
  }
  
  file {
    path => "/var/log/nginx/error.log"
    start_position => "beginning"
    tags => ["nginx", "error"]
  }
}

filter {
  # Parse application logs
  if "application" in [tags] {
    json {
      source => "message"
    }
    
    date {
      match => ["timestamp", "ISO8601"]
    }
    
    # Extract session ID from logs
    grok {
      match => {
        "message" => "session_id=%{DATA:session_id}"
      }
      tag_on_failure => ["_grokparsefailure_session"]
    }
    
    # Extract job ID from logs
    grok {
      match => {
        "message" => "job_id=%{DATA:job_id}"
      }
      tag_on_failure => ["_grokparsefailure_job"]
    }
  }
  
  # Parse worker logs
  if "worker" in [tags] {
    json {
      source => "message"
    }
    
    # Extract task information
    if [task_name] {
      mutate {
        add_field => { "log_type" => "celery_task" }
      }
    }
  }
  
  # Parse Nginx access logs
  if "nginx" in [tags] and "access" in [tags] {
    grok {
      match => {
        "message" => "%{NGINXACCESS}"
      }
    }
    
    date {
      match => ["time_local", "dd/MMM/yyyy:HH:mm:ss Z"]
    }
    
    # Parse response time
    mutate {
      convert => {
        "response_time" => "float"
        "request_time" => "float"
        "body_bytes_sent" => "integer"
      }
    }
  }
  
  # Parse Nginx error logs
  if "nginx" in [tags] and "error" in [tags] {
    grok {
      match => {
        "message" => "(?<timestamp>%{YEAR}[./-]%{MONTHNUM}[./-]%{MONTHDAY}[- ]%{TIME}) \[%{LOGLEVEL:severity}\] %{POSINT:pid}#%{NUMBER:tid}: %{GREEDYDATA:error_message}"
      }
    }
  }
  
  # Add common fields
  mutate {
    add_field => {
      "environment" => "production"
      "service" => "sora"
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    user => "elastic"
    password => "${ELASTIC_PASSWORD}"
    index => "sora-logs-%{+YYYY.MM.dd}"
  }
  
  # Debug output (remove in production)
  stdout {
    codec => rubydebug
  }
}
```

### Kibana Configuration

Create `/opt/sora/kibana/kibana.yml`:

```yaml
server.name: kibana
server.host: 0.0.0.0
server.port: 5601

elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: "elastic"
elasticsearch.password: "${ELASTIC_PASSWORD}"

# Security
server.ssl.enabled: false
elasticsearch.ssl.verificationMode: none

# Performance
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000

# Logging
logging.dest: /var/log/kibana.log
logging.verbose: false
```

### Log Analysis Queries

Common Kibana queries for troubleshooting:

```kql
# Error logs in last hour
level:ERROR AND @timestamp:[now-1h TO now]

# Failed video generation jobs
status:failed AND log_type:celery_task

# Slow API requests
response_time:>3 AND tags:nginx

# High queue depth events
message:"queue depth" AND queue_depth:>20

# Session-specific errors
session_id:"sess_abc123" AND level:ERROR

# Worker failures
tags:worker AND (level:ERROR OR status:failed)

# Azure API errors
message:"Azure API" AND (level:ERROR OR level:WARNING)
```

## Health Checks

### Application Health Endpoints

The application provides comprehensive health check endpoints:

```python
# src/api/health.py
from flask import Blueprint, jsonify
import time
import redis
import psycopg2
import requests
from src.config.environments import get_config

health_bp = Blueprint('health', __name__)

@health_bp.route('/health')
def overall_health():
    """Overall system health check."""
    start_time = time.time()
    
    checks = {
        'database': check_database(),
        'redis': check_redis(),
        'azure_api': check_azure_api(),
        'queue': check_queue_health(),
        'workers': check_worker_health(),
        'storage': check_storage()
    }
    
    # Determine overall status
    failed_checks = [name for name, check in checks.items() if not check['healthy']]
    overall_healthy = len(failed_checks) == 0
    
    response = {
        'healthy': overall_healthy,
        'timestamp': time.time(),
        'response_time_ms': (time.time() - start_time) * 1000,
        'checks': checks,
        'failed_checks': failed_checks
    }
    
    status_code = 200 if overall_healthy else 503
    return jsonify(response), status_code

@health_bp.route('/health/database')
def database_health():
    """Database-specific health check."""
    return jsonify(check_database())

@health_bp.route('/health/redis')
def redis_health():
    """Redis-specific health check."""
    return jsonify(check_redis())

@health_bp.route('/health/azure')
def azure_health():
    """Azure API health check."""
    return jsonify(check_azure_api())

def check_database():
    """Check database connectivity and performance."""
    try:
        config = get_config()
        start_time = time.time()
        
        conn = psycopg2.connect(config.DATABASE_URL)
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
            cur.fetchone()
            
            # Check connection count
            cur.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            active_connections = cur.fetchone()[0]
            
        conn.close()
        response_time = (time.time() - start_time) * 1000
        
        return {
            'healthy': True,
            'response_time_ms': response_time,
            'active_connections': active_connections,
            'details': 'Database is responsive'
        }
        
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'details': 'Database connection failed'
        }

def check_redis():
    """Check Redis connectivity and performance."""
    try:
        start_time = time.time()
        
        r = redis.Redis(host='redis-primary', port=6379, db=0)
        r.ping()
        
        # Get basic info
        info = r.info()
        memory_usage = info.get('used_memory_human', 'unknown')
        connected_clients = info.get('connected_clients', 0)
        
        response_time = (time.time() - start_time) * 1000
        
        return {
            'healthy': True,
            'response_time_ms': response_time,
            'memory_usage': memory_usage,
            'connected_clients': connected_clients,
            'details': 'Redis is responsive'
        }
        
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'details': 'Redis connection failed'
        }

def check_azure_api():
    """Check Azure OpenAI API connectivity."""
    try:
        config = get_config()
        start_time = time.time()
        
        # Test with a simple API call
        headers = {
            'api-key': config.AZURE_OPENAI_API_KEY,
            'Content-Type': 'application/json'
        }
        
        url = f"{config.AZURE_OPENAI_ENDPOINT}/openai/deployments"
        params = {'api-version': config.AZURE_OPENAI_API_VERSION}
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response_time = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            return {
                'healthy': True,
                'response_time_ms': response_time,
                'status_code': response.status_code,
                'details': 'Azure API is accessible'
            }
        else:
            return {
                'healthy': False,
                'status_code': response.status_code,
                'error': response.text,
                'details': 'Azure API returned error'
            }
            
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'details': 'Azure API connection failed'
        }

def check_queue_health():
    """Check queue system health."""
    try:
        r = redis.Redis(host='redis-primary', port=6379, db=0)
        
        # Check queue depth
        queue_depth = r.llen('celery')
        
        # Check for stuck jobs (jobs in processing state for too long)
        # This would require checking the database for jobs in 'processing' state
        # for more than a reasonable time
        
        healthy = queue_depth < 50  # Arbitrary threshold
        
        return {
            'healthy': healthy,
            'queue_depth': queue_depth,
            'details': f'Queue depth: {queue_depth} jobs'
        }
        
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'details': 'Queue health check failed'
        }

def check_worker_health():
    """Check Celery worker health."""
    try:
        from celery import Celery
        
        # This would need to be properly configured
        celery_app = Celery('sora')
        
        # Get worker statistics
        stats = celery_app.control.inspect().stats()
        active = celery_app.control.inspect().active()
        
        if stats:
            worker_count = len(stats)
            active_tasks = sum(len(tasks) for tasks in active.values()) if active else 0
            
            return {
                'healthy': worker_count > 0,
                'worker_count': worker_count,
                'active_tasks': active_tasks,
                'details': f'{worker_count} workers, {active_tasks} active tasks'
            }
        else:
            return {
                'healthy': False,
                'details': 'No workers responding'
            }
            
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'details': 'Worker health check failed'
        }

def check_storage():
    """Check storage space and accessibility."""
    try:
        import shutil
        import os
        
        upload_folder = '/app/storage/uploads'
        
        # Check if directory exists and is writable
        if not os.path.exists(upload_folder):
            return {
                'healthy': False,
                'details': 'Upload directory does not exist'
            }
        
        if not os.access(upload_folder, os.W_OK):
            return {
                'healthy': False,
                'details': 'Upload directory is not writable'
            }
        
        # Check available space
        total, used, free = shutil.disk_usage(upload_folder)
        free_gb = free // (1024**3)
        used_percent = (used / total) * 100
        
        healthy = free_gb > 10 and used_percent < 90  # At least 10GB free and less than 90% used
        
        return {
            'healthy': healthy,
            'free_space_gb': free_gb,
            'used_percent': round(used_percent, 1),
            'details': f'{free_gb}GB free, {used_percent:.1f}% used'
        }
        
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'details': 'Storage health check failed'
        }
```

## Alerting Rules

### Alertmanager Configuration

Create `/opt/sora/monitoring/alertmanager.yml`:

```yaml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'

route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'default'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#sora-alerts'
        title: 'Sora System Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}: {{ .Annotations.description }}{{ end }}'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: Sora System Alert'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Time: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#sora-critical'
        title: '🚨 CRITICAL: Sora System Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}: {{ .Annotations.description }}{{ end }}'

  - name: 'warning-alerts'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#sora-alerts'
        title: '⚠️ WARNING: Sora System Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}: {{ .Annotations.description }}{{ end }}'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']
```

## Operational Procedures

### Daily Health Check Routine

```bash
#!/bin/bash
# /opt/sora/scripts/daily-health-check.sh

echo "=== Daily Sora Health Check - $(date) ==="

# Overall system health
echo "1. System Health:"
curl -s http://localhost/health | jq '.'

# Resource usage
echo -e "\n2. Resource Usage:"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')"
echo "Memory Usage: $(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2 }')"
echo "Disk Usage: $(df -h /opt/sora | awk 'NR==2 {print $5}')"

# Queue status
echo -e "\n3. Queue Status:"
curl -s http://localhost/api/queue/status | jq '.data.global_queue'

# Worker status
echo -e "\n4. Worker Status:"
docker ps | grep sora-worker | wc -l | xargs echo "Active workers:"

# Recent errors
echo -e "\n5. Recent Errors (last hour):"
docker logs sora-app-1 --since=1h 2>&1 | grep -i error | wc -l | xargs echo "Application errors:"
docker logs sora-worker-1 --since=1h 2>&1 | grep -i error | wc -l | xargs echo "Worker errors:"

# SSL certificate status
echo -e "\n6. SSL Certificate:"
if [[ -f /opt/sora/ssl/fullchain.pem ]]; then
    openssl x509 -in /opt/sora/ssl/fullchain.pem -noout -enddate
else
    echo "SSL certificate not found"
fi

echo -e "\n=== Health Check Complete ==="
```

### Weekly Performance Report

```bash
#!/bin/bash
# /opt/sora/scripts/weekly-performance-report.sh

REPORT_DATE=$(date +%Y-%m-%d)
REPORT_FILE="/opt/sora/reports/weekly-report-${REPORT_DATE}.txt"

mkdir -p /opt/sora/reports

{
    echo "=== Weekly Performance Report - Week ending $REPORT_DATE ==="
    echo
    
    # Query Prometheus for weekly stats
    echo "1. Performance Metrics (7 days):"
    
    # Average response time
    curl -s "http://localhost:9090/api/v1/query?query=avg_over_time(histogram_quantile(0.95,rate(http_request_duration_seconds_bucket[5m]))[7d])" | \
        jq -r '.data.result[0].value[1]' | \
        awk '{printf "Average 95th percentile response time: %.2fs\n", $1}'
    
    # Total requests
    curl -s "http://localhost:9090/api/v1/query?query=increase(http_requests_total[7d])" | \
        jq -r '.data.result[] | select(.metric.status=="200") | .value[1]' | \
        awk '{sum+=$1} END {printf "Total successful requests: %.0f\n", sum}'
    
    # Video generation stats
    curl -s "http://localhost:9090/api/v1/query?query=increase(video_jobs_total[7d])" | \
        jq -r '.data.result[] | "\(.metric.status): \(.value[1])"'
    
    echo
    echo "2. System Resource Usage:"
    
    # Average CPU usage
    curl -s "http://localhost:9090/api/v1/query?query=avg_over_time((100-(avg(rate(node_cpu_seconds_total{mode=\"idle\"}[5m]))*100))[7d])" | \
        jq -r '.data.result[0].value[1]' | \
        awk '{printf "Average CPU usage: %.1f%%\n", $1}'
    
    # Average memory usage
    curl -s "http://localhost:9090/api/v1/query?query=avg_over_time(((1-(node_memory_MemAvailable_bytes/node_memory_MemTotal_bytes))*100)[7d])" | \
        jq -r '.data.result[0].value[1]' | \
        awk '{printf "Average memory usage: %.1f%%\n", $1}'
    
    echo
    echo "3. Incidents and Alerts:"
    # This would query alertmanager for fired alerts in the past week
    echo "Alert summary would go here"
    
    echo
    echo "=== End of Report ==="
    
} > "$REPORT_FILE"

echo "Weekly report generated: $REPORT_FILE"

# Send report via email if configured
if [[ -n "$REPORT_EMAIL" ]]; then
    mail -s "Sora Weekly Performance Report - $REPORT_DATE" "$REPORT_EMAIL" < "$REPORT_FILE"
fi
```

This comprehensive monitoring setup provides complete visibility into the multi-user Sora system, enabling proactive maintenance and rapid issue resolution.