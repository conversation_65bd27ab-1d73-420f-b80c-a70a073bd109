# Developer Onboarding Guide

Welcome to the **Sora Video Generation POC** - a Flask web application demonstrating Azure OpenAI's Sora video generation integration with comprehensive multi-user architecture.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Repository Structure](#repository-structure)
3. [Getting Started](#getting-started)
4. [Key Components](#key-components)
5. [Development Workflow](#development-workflow)
6. [Architecture Decisions](#architecture-decisions)
7. [Common Tasks](#common-tasks)
8. [Potential Gotchas](#potential-gotchas)
9. [Documentation and Resources](#documentation-and-resources)
10. [Next Steps](#next-steps)

## Project Overview

### What is this project?
A **working proof-of-concept** Flask application that demonstrates text-to-video generation using Azure OpenAI's Sora model. The application has been **successfully validated** with end-to-end video generation, database persistence, and health monitoring.

### Tech Stack
- **Backend**: Flask 3.0+ with Python 3.8+
- **Database**: SQLAlchemy 2.0+ (SQLite for development, PostgreSQL for production)
- **Data Validation**: Pydantic v2 for type safety and validation
- **Cloud Integration**: Azure OpenAI Sora API
- **Frontend**: Bootstrap UI with JavaScript polling
- **Task Queue**: Celery + Redis (optional, graceful degradation)
- **Testing**: pytest with 275+ tests (89% pass rate)
- **Code Quality**: Ruff (linting/formatting) + MyPy (type checking)

### Architecture Pattern
**Vertical Slice Architecture** - Code is organized by business features rather than technical layers, with tests co-located next to the code they test.

### Key Features
- ✅ **Text-to-Video Generation** - Azure Sora API integration
- ✅ **Multi-User Architecture** - Session management, rate limiting, queue system
- ✅ **Database Persistence** - Job tracking with SQLAlchemy ORM
- ✅ **Real-time Updates** - WebSocket support with polling fallback
- ✅ **Health Monitoring** - Comprehensive health checks and metrics
- ✅ **Type Safety** - Full Pydantic v2 validation throughout
- ✅ **Production Ready** - Security hardening, monitoring, error handling

## Repository Structure

```
sora-poc/
├── src/                           # Main application code
│   ├── main.py                   # Flask app entry point (application factory)
│   ├── conftest.py               # pytest configuration and fixtures
│   ├── tests/                    # Main application tests
│   │   ├── test_main.py         # Flask app initialization tests
│   │   └── test_integration_e2e.py # End-to-end integration tests
│   ├── api/                      # API routes and database operations
│   │   ├── routes.py            # Flask Blueprint with REST endpoints
│   │   ├── job_repository.py    # Database CRUD operations
│   │   └── tests/               # API layer tests
│   ├── core/                     # Core domain models
│   │   ├── models.py            # Pydantic v2 data models
│   │   └── tests/               # Core model tests
│   ├── database/                 # Database persistence layer
│   │   ├── models.py            # SQLAlchemy ORM models
│   │   ├── connection.py        # Database connection management
│   │   └── tests/               # Database tests
│   ├── config/                   # Configuration management
│   │   ├── environments.py      # Environment-specific configurations
│   │   ├── security.py          # Security validation utilities
│   │   ├── factory.py           # Configuration factory pattern
│   │   ├── video_config.py      # Video generation configuration
│   │   └── tests/               # Configuration tests
│   ├── monitoring/               # Health checks and metrics
│   │   ├── health_check.py      # System health monitoring
│   │   ├── metrics.py           # Performance metrics collection
│   │   └── tests/               # Monitoring tests
│   ├── features/                 # Business logic features
│   │   └── sora_integration/    # Azure Sora API integration
│   │       ├── client.py        # Azure API client
│   │       ├── file_handler.py  # File management
│   │       ├── utils.py         # Utility functions
│   │       └── tests/           # Integration tests
│   ├── job_queue/               # Background job processing (renamed from queue)
│   │   ├── celery_app.py        # Celery configuration
│   │   ├── tasks.py             # Background tasks
│   │   ├── manager.py           # Queue management
│   │   └── tests/               # Queue tests
│   ├── realtime/                 # WebSocket support (optional)
│   │   ├── websocket.py         # WebSocket handlers
│   │   ├── broadcaster.py       # Event broadcasting
│   │   └── tests/               # Real-time tests
│   ├── rate_limiting/            # Rate limiting system (optional)
│   │   ├── limiter.py           # Rate limiting implementation
│   │   └── tests/               # Rate limiting tests
│   └── session/                  # Session management (optional)
│       ├── manager.py           # Session tracking
│       ├── isolation.py         # User isolation
│       └── tests/               # Session tests
├── migrations/                   # Database migration files (Alembic)
├── templates/                    # HTML templates
├── static/                       # CSS, JS, and static assets
├── uploads/                      # Temporary video storage (auto-cleanup)
├── docs/                         # Comprehensive documentation
├── tests/                        # Integration and load tests
├── pyproject.toml               # Project configuration
├── .env                         # Environment variables (not in repo)
├── README.md                    # Project overview
├── CLAUDE.md                    # Development guidelines
└── ONBOARDING.md               # This file
```

### Key Organizational Patterns

1. **Co-located Tests**: Every module has its tests in a `tests/` subdirectory
2. **Feature Slices**: Business logic organized by capability (e.g., `sora_integration/`)
3. **Layered Architecture**: Clear separation between API, business logic, and data layers
4. **Optional Components**: Advanced features gracefully degrade if dependencies unavailable

## Getting Started

### Prerequisites
- Python 3.8+ (recommended: 3.12+)
- [UV package manager](https://docs.astral.sh/uv/) 
- Azure OpenAI account with Sora access
- Git
- Optional: Redis (for distributed rate limiting)

### Setup Instructions

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd sora-poc
   ```

2. **Create virtual environment**:
   ```bash
   uv venv
   source .venv/bin/activate  # On Unix/macOS
   # .venv\Scripts\activate   # On Windows
   ```

3. **Install dependencies**:
   ```bash
   uv sync
   ```

4. **Create environment file**:
   ```bash
   # Create .env file with required variables
   touch .env
   ```

5. **Configure environment variables** (edit `.env`):
   ```bash
   # Azure OpenAI Configuration (Required)
   AZURE_OPENAI_API_KEY=your_api_key_here
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_API_VERSION=2024-02-15-preview
   AZURE_OPENAI_SORA_DEPLOYMENT=sora

   # Flask Configuration
   FLASK_ENV=development
   SECRET_KEY=your-secret-key-here
   UPLOAD_FOLDER=uploads
   MAX_CONTENT_LENGTH=*********

   # Database Configuration
   DATABASE_URL=sqlite:///sora_poc.db

   # Video Generation Settings
   MAX_PROMPT_LENGTH=500
   DEFAULT_VIDEO_DURATION=5
   MAX_VIDEO_DURATION=20
   ```

6. **Initialize database**:
   ```bash
   # Initialize database migrations (one-time setup)
   uv run flask --app src.main:create_app db init
   
   # Apply database migrations
   uv run flask --app src.main:create_app db upgrade
   ```

7. **Run the application**:
   ```bash
   uv run python src/main.py
   ```

8. **Verify setup**:
   - Open browser to `http://localhost:5001`
   - Check health endpoint: `curl http://localhost:5001/health`
   - Run tests: `uv run pytest`

## Key Components

### 1. Main Entry Point (`src/main.py`)
**Purpose**: Flask application factory with multi-user infrastructure setup

**Key Functions**:
- `create_app()`: Main application factory function
- `_initialize_multiuser_components()`: Sets up advanced features

**Architecture Pattern**: Application Factory Pattern with dependency injection

### 2. Core Models (`src/core/models.py`)
**Purpose**: Pydantic v2 data models for type safety and validation

**Key Classes**:
- `VideoJob`: Core domain model for video generation workflow
- `GenerationParams`: API parameter validation with Azure compatibility
- `GenerationParamsFactory`: Factory for creating parameters with configuration defaults
- `QueuedVideoJob`: Extended model for multi-user queue management

**Pattern**: Factory Pattern with environment-aware defaults

### 3. API Routes (`src/api/routes.py`)
**Purpose**: Flask Blueprint containing all REST API endpoints

**Key Endpoints**:
- `POST /generate` - Create video generation job
- `GET /status/<job_id>` - Poll job status
- `GET /video/<job_id>` - Stream video file
- `GET /health` - System health check

**Security**: Path traversal protection, comprehensive error handling

### 4. Database Models (`src/database/models.py`)
**Purpose**: SQLAlchemy ORM models for database persistence

**Key Classes**:
- `VideoJobDB`: Main ORM model with multi-user support
- Conversion methods: `to_pydantic()`, `from_pydantic()`

**Pattern**: Clean separation between domain and persistence models

### 5. Azure Integration (`src/features/sora_integration/client.py`)
**Purpose**: Real Azure OpenAI Sora API integration

**Key Features**:
- Rate limiting (10 req/sec Azure limit)
- Authentication (API key + DefaultAzureCredential)
- Comprehensive error handling and logging
- Secure file download and storage

### 6. Configuration Management (`src/config/`)
**Purpose**: Environment-specific configuration with validation

**Key Files**:
- `environments.py`: Environment-specific configs
- `security.py`: Security validation utilities
- `factory.py`: Configuration factory pattern

**Pattern**: Environment-based configuration with strict validation

### 7. Monitoring (`src/monitoring/`)
**Purpose**: Health checks and performance metrics

**Key Components**:
- `HealthCheck`: Multi-component health monitoring
- `MetricsCollector`: Performance metrics with thread-safe collection

**Features**: Database health, Azure API health, disk space, job queue status

## Development Workflow

### Git Workflow
This project uses a **develop → main** branching strategy:
- `main`: Production branch with stable releases
- `develop`: Integration branch for features (not currently used)
- Feature branches: `feature/descriptive-name`

### Development Commands

```bash
# Start Redis (required for multi-user features)
redis-server --daemonize yes

# Start Celery worker (optional, enables background processing)
uv run celery -A src.job_queue.celery_app worker --loglevel=info --concurrency=2 &

# Start development
uv run python src/main.py

# Run tests
uv run pytest                              # All tests
uv run pytest --cov=src                   # With coverage
uv run pytest -m "not slow"               # Skip slow tests
uv run pytest src/api/tests/ -v           # Specific module

# Database management
uv run flask --app src.main:create_app db migrate -m "Description"
uv run flask --app src.main:create_app db upgrade

# Code quality
uv run ruff format .                       # Format code
uv run ruff check .                        # Lint code
uv run mypy src/                           # Type checking

# Complete workflow (run before committing)
uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest
```

### Testing Strategy
- **275+ tests** with **89% pass rate** (MVP ready)
- **Co-located tests**: Tests live next to code they test
- **Test markers**: `unit`, `integration`, `slow` for selective running
- **Comprehensive coverage**: All critical functionality tested

### Code Style
- **Line length**: 88 characters (configured in pyproject.toml)
- **Type hints**: Required for all functions (MyPy strict mode)
- **Docstrings**: Google-style docstrings for all public functions
- **Validation**: Pydantic v2 for all data models

## Architecture Decisions

### 1. Vertical Slice Architecture
**Decision**: Organize code by business features rather than technical layers

**Why**: 
- Easier to understand and maintain
- Clear feature boundaries
- Tests co-located with code
- Supports feature-based development

### 2. Factory Pattern for Configuration
**Decision**: Use factory pattern for configuration-aware object creation

**Why**:
- Single source of truth for all settings
- Environment-aware defaults
- Dependency injection ready
- Easier testing with mocked configs

### 3. Multi-User Architecture (Optional Components)
**Decision**: Build multi-user infrastructure with graceful degradation

**Why**:
- Scalable from single-user POC to multi-user production
- Optional components don't break core functionality
- Production-ready architecture patterns

### 4. Pydantic v2 for Data Validation
**Decision**: Use Pydantic v2 throughout for type safety

**Why**:
- Comprehensive validation with clear error messages
- Excellent IDE support with type hints
- Serialization/deserialization built-in
- Performance optimized

### 5. SQLAlchemy 2.0 + Alembic
**Decision**: Use modern SQLAlchemy with migration support

**Why**:
- Type-safe database operations
- Database schema version control
- Production-ready with connection pooling
- Supports multiple database backends

## Common Tasks

### How to add a new API endpoint

1. **Add route function to `src/api/routes.py`**:
   ```python
   @api_bp.route("/your-endpoint", methods=["POST"])
   def your_endpoint() -> Union[Response, tuple[Response, int]]:
       """Your endpoint description."""
       try:
           # Your logic here
           response = APIResponse(
               success=True,
               message="Success message",
               data={"key": "value"}
           )
           return jsonify(response.dict()), 200
       except Exception as e:
           return handle_error(e, "Your endpoint")
   ```

2. **Add tests in `src/api/tests/test_routes.py`**:
   ```python
   def test_your_endpoint_success(client):
       """Test successful case."""
       response = client.post("/your-endpoint", json={"test": "data"})
       assert response.status_code == 200
       assert response.json["success"] is True
   ```

3. **Update documentation** in relevant files

### How to create a new database model

1. **Add Pydantic model to `src/core/models.py`**:
   ```python
   class YourModel(BaseModel):
       """Your model description."""
       field1: str
       field2: int
       created_at: datetime = Field(default_factory=datetime.utcnow)
   ```

2. **Add SQLAlchemy model to `src/database/models.py`**:
   ```python
   class YourModelDB(db.Model):
       """SQLAlchemy model for your entity."""
       __tablename__ = "your_table"
       
       id = db.Column(db.Integer, primary_key=True)
       field1 = db.Column(db.String(255), nullable=False)
       field2 = db.Column(db.Integer, nullable=False)
       created_at = db.Column(db.DateTime, default=datetime.utcnow)
   ```

3. **Create migration**:
   ```bash
   uv run flask --app src.main:create_app db migrate -m "Add your model"
   uv run flask --app src.main:create_app db upgrade
   ```

### How to add a new test

1. **Create test file** in appropriate `tests/` directory:
   ```python
   def test_your_function():
       """Test your function behavior."""
       # Arrange
       input_data = {"test": "data"}
       
       # Act
       result = your_function(input_data)
       
       # Assert
       assert result.success is True
       assert result.data["key"] == "expected_value"
   ```

2. **Use appropriate fixtures** from `conftest.py`

3. **Run specific test**:
   ```bash
   uv run pytest src/path/to/tests/test_your_module.py::test_your_function -v
   ```

### How to debug common issues

1. **Check logs**: Set `LOG_LEVEL=DEBUG` in `.env`
2. **Health checks**: `curl http://localhost:5001/health`
3. **Database issues**: Check `DATABASE_URL` and run migrations
4. **Azure API issues**: Verify credentials and check `/health/azure`
5. **File path issues**: Ensure `UPLOAD_FOLDER` permissions are correct

## Potential Gotchas

### 1. Environment Variables
**Issue**: Configuration not loading properly
**Solutions**:
- Ensure `.env` file exists and has correct format
- No inline comments in `.env` (use separate lines)
- Check for system environment variables that might override
- Use `safe_int_from_env()` for integer values

### 2. Database Migrations
**Issue**: Migration errors or schema mismatches
**Solutions**:
- Always create migrations after model changes
- Check for existing migrations before creating new ones
- Use `db current` to check migration status
- Test migrations on a copy of production data

### 3. File Path Issues
**Issue**: Video downloads failing with 500 errors
**Solutions**:
- Ensure `UPLOAD_FOLDER` exists and has write permissions
- Check that SoraClient uses absolute paths
- Verify file cleanup settings don't interfere

### 4. Azure API Issues
**Issue**: 400 Bad Request errors with Azure Sora API
**Solutions**:
- Verify resolution requirements (use Azure-compatible presets)
- Check rate limiting (10 req/sec max)
- Validate API credentials and deployment names
- Use debugging endpoint: `/debug/azure-config`

### 5. Testing Issues
**Issue**: Tests failing due to database conflicts
**Solutions**:
- Use `uuid.uuid4()` for unique test IDs
- Properly isolate test environments
- Check for hardcoded values in tests
- Use appropriate test markers

### 6. Port Configuration
**Issue**: Application not accessible on expected port
**Solutions**:
- Default port is 5001 (not 5000)
- Check `PORT` environment variable
- Ensure no port conflicts with other services

### 7. Module Import Conflicts (✅ RESOLVED)
**Issue**: ~~Flask application failing to start with `AttributeError: module 'queue' has no attribute 'LifoQueue'`~~
**Root Cause**: ~~The `src/queue` directory conflicted with Python's built-in `queue` module~~
**Solution Applied**: ✅ **FIXED** - Renamed `src/queue` to `src/job_queue` and updated all imports
**Status**: Application now starts successfully with all multi-user features

### 8. Multi-User Components
**Issue**: Optional components causing startup failures
**Solutions**:
- Redis/Celery are optional - app works without them
- Check graceful degradation patterns
- Verify component initialization order
- Start Redis: `redis-server --daemonize yes`
- Start Celery: `uv run celery -A src.job_queue.celery_app worker --loglevel=info`

## Documentation and Resources

### Existing Documentation
- `README.md`: Project overview and setup instructions
- `CLAUDE.md`: Development guidelines and conventions
- `docs/API_REFERENCE.md`: Comprehensive API documentation
- `docs/DEVELOPER_GUIDE.md`: Development patterns and best practices
- `docs/DEPLOYMENT_GUIDE.md`: Production deployment instructions
- `docs/TROUBLESHOOTING_GUIDE.md`: Common issues and solutions
- `docs/KNOWN_TEST_ISSUES.md`: Test suite status and known issues

### API Documentation
- Health endpoints: `/health`, `/health/database`, `/health/azure`
- Video generation: `/generate`, `/status/<job_id>`, `/video/<job_id>`
- Configuration: `/config` (UI configuration constraints)
- Metrics: `/metrics`, `/metrics/jobs`

### Architecture Documentation
- Vertical slice architecture patterns
- Factory pattern for configuration
- Multi-user infrastructure design
- Security validation patterns

## Next Steps

### New Developer Onboarding Checklist

1. **Environment Setup**
   - [ ] Clone repository
   - [ ] Set up virtual environment with UV
   - [ ] Install dependencies (`uv sync`)
   - [ ] Create `.env` file with Azure credentials
   - [ ] Initialize database (`flask db init` and `flask db upgrade`)

2. **Verify Setup**
   - [ ] Run application (`uv run python src/main.py`)
   - [ ] Access web interface at `http://localhost:5001`
   - [ ] Check health endpoint: `curl http://localhost:5001/health`
   - [ ] Run test suite: `uv run pytest`

3. **Understanding the Codebase**
   - [ ] Read through main entry point (`src/main.py`)
   - [ ] Explore core models (`src/core/models.py`)
   - [ ] Review API routes (`src/api/routes.py`)
   - [ ] Understand database models (`src/database/models.py`)
   - [ ] Check Azure integration (`src/features/sora_integration/client.py`)

4. **Make First Change**
   - [ ] Create feature branch: `git checkout -b feature/your-name-test`
   - [ ] Add simple test in appropriate `tests/` directory
   - [ ] Run tests: `uv run pytest src/path/to/your/test.py -v`
   - [ ] Commit changes with descriptive message
   - [ ] Run full quality check: `uv run ruff format . && uv run ruff check . && uv run mypy src/ && uv run pytest`

5. **Explore Key Features**
   - [ ] Generate a test video through the web interface
   - [ ] Check job status via API: `curl http://localhost:5001/status/<job_id>`
   - [ ] Monitor system health: `curl http://localhost:5001/health`
   - [ ] Review metrics: `curl http://localhost:5001/metrics`

6. **Understand Development Workflow**
   - [ ] Review git workflow in `CLAUDE.md`
   - [ ] Understand testing strategy (co-located tests, markers)
   - [ ] Learn code style requirements (ruff, mypy, type hints)
   - [ ] Practice database migration workflow

7. **Identify Contribution Area**
   - [ ] Review `docs/KNOWN_TEST_ISSUES.md` for test improvements
   - [ ] Check GitHub issues for feature requests
   - [ ] Discuss with team lead about priority areas
   - [ ] Start with small, well-defined tasks

### Recommended First Tasks

1. **Fix a test** from `docs/KNOWN_TEST_ISSUES.md` (low-risk, good learning)
2. **Add new health check** for a system component
3. **Improve error handling** in a specific API endpoint
4. **Add new validation** to existing Pydantic models
5. **Create documentation** for a specific feature

### Getting Help

- **Code Questions**: Review `CLAUDE.md` for development guidelines
- **Architecture Questions**: Check `docs/DEVELOPER_GUIDE.md`
- **Deployment Issues**: See `docs/DEPLOYMENT_GUIDE.md`
- **API Questions**: Reference `docs/API_REFERENCE.md`
- **Troubleshooting**: Check `docs/TROUBLESHOOTING_GUIDE.md`

Welcome to the team! 🚀