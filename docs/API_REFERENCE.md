# API Reference - Complete API Endpoint Documentation

## Overview

Comprehensive API reference for the multi-user video generation system. This document covers all REST endpoints, WebSocket events, request/response formats, authentication, rate limiting, and error handling for the production-ready Flask application.

## Base URL and Versioning

### API Base URL
```
Development: http://localhost:5001
Production: https://your-domain.com
```

### API Versioning
- **Current Version**: v1 (implicit, no version prefix required)
- **Content Type**: `application/json` for all POST/PUT requests
- **Response Format**: JSON for all endpoints

## Authentication

### Session-Based Authentication
The system uses cryptographically secure session-based authentication:

```python
# Session ID format: 43-character URL-safe string
session_id = secrets.token_urlsafe(32)  # 256-bit entropy

# Session validation
def validate_session(session_id: str, client_ip: str) -> bool:
    """Validate session with IP verification."""
    session_data = redis_client.get(f"session:{session_id}")
    if not session_data:
        return False
    
    session = json.loads(session_data)
    return session.get('client_ip') == client_ip
```

### Session Headers
Include session ID in requests:
```http
Cookie: session_id=abc123def456...
# OR
X-Session-ID: abc123def456...
```

## Core Video Generation Endpoints

### Generate Video

Create a new video generation job with Azure OpenAI Sora API.

**Endpoint:** `POST /generate`

**Request Body:**
```json
{
  "prompt": "A cat playing in a garden with colorful flowers",
  "duration": 10,
  "width": 1280,
  "height": 720,
  "model": "sora-1.0"
}
```

**Request Schema:**
```typescript
interface GenerateVideoRequest {
  prompt: string;          // Required: 1-500 characters, no HTML/scripts
  duration?: number;       // Optional: 1-60 seconds, default 5
  width?: number;          // Optional: 480-1920 pixels, default 1280
  height?: number;         // Optional: 480-1080 pixels, default 720
  model?: string;          // Optional: AI model, default "sora-1.0"
}
```

**Response:**
```json
{
  "success": true,
  "job_id": "job_abc123def456",
  "status": "pending",
  "prompt": "A cat playing in a garden with colorful flowers",
  "parameters": {
    "duration": 10,
    "width": 1280,
    "height": 720,
    "model": "sora-1.0"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "estimated_completion": "2024-01-15T10:35:00Z"
}
```

**Supported Resolutions (Azure Sora API Requirements):**
- `480x480` - Square SD
- `854x480` - Widescreen SD  
- `720x720` - Square HD
- `1280x720` - HD (default)
- `1080x1080` - Square Full HD
- `1920x1080` - Full HD

**Error Responses:**
```json
{
  "error": true,
  "type": "validation",
  "message": "Invalid prompt: exceeds maximum length of 500 characters",
  "details": {
    "field": "prompt",
    "provided_length": 650,
    "max_length": 500
  }
}
```

**Rate Limits:**
- **Per Session**: 3 concurrent jobs maximum
- **Global**: 10 requests/second across all users
- **Per IP**: 60 requests/minute

---

### Job Status Polling

Poll the status of a video generation job with real-time updates.

**Endpoint:** `GET /status/{job_id}`

**URL Parameters:**
- `job_id` (string): Unique job identifier from `/generate` response

**Response - Pending/Running:**
```json
{
  "job_id": "job_abc123def456",
  "status": "running",
  "progress": 45,
  "message": "Generating video frames...",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:32:30Z",
  "estimated_completion": "2024-01-15T10:35:00Z"
}
```

**Response - Completed:**
```json
{
  "job_id": "job_abc123def456",
  "status": "succeeded",
  "progress": 100,
  "message": "Video generation completed successfully",
  "file_path": "/uploads/job_abc123def456_video.mp4",
  "file_size": 15728640,
  "duration": 10.5,
  "download_url": "/download/job_abc123def456",
  "stream_url": "/video/job_abc123def456",
  "created_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:34:15Z"
}
```

**Response - Failed:**
```json
{
  "job_id": "job_abc123def456",
  "status": "failed",
  "progress": 0,
  "message": "Video generation failed",
  "error": {
    "type": "external_api_error",
    "message": "Azure API timeout",
    "code": "AZURE_TIMEOUT"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "failed_at": "2024-01-15T10:33:00Z"
}
```

**Status Values:**
- `pending` - Job queued, waiting to start
- `running` - Video generation in progress
- `succeeded` - Video generated successfully
- `failed` - Video generation failed

**Polling Recommendations:**
- **Initial**: Poll every 5 seconds
- **After 30s**: Poll every 10 seconds  
- **After 2 min**: Poll every 15 seconds
- **Maximum**: Poll every 30 seconds

---

### Video Streaming

Stream generated video file for preview/playback.

**Endpoint:** `GET /video/{job_id}`

**URL Parameters:**
- `job_id` (string): Job identifier for completed video

**Response Headers:**
```http
Content-Type: video/mp4
Content-Length: 15728640
Accept-Ranges: bytes
Cache-Control: private, max-age=3600
```

**HTTP Status Codes:**
- `200` - Video stream successful
- `206` - Partial content (for range requests)
- `404` - Video not found or job not completed
- `410` - Video expired and cleaned up

**Range Request Support:**
```http
GET /video/job_abc123def456
Range: bytes=0-1023

HTTP/1.1 206 Partial Content
Content-Range: bytes 0-1023/15728640
Content-Length: 1024
```

---

### Video Download

Download generated video file.

**Endpoint:** `GET /download/{job_id}`

**URL Parameters:**
- `job_id` (string): Job identifier for completed video

**Response Headers:**
```http
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="video_job_abc123def456.mp4"
Content-Length: 15728640
```

**Security Features:**
- **Path Validation**: Prevents directory traversal attacks
- **File Existence Check**: Validates file exists and is accessible
- **Session Validation**: Ensures user has access to the video
- **Rate Limiting**: Prevents abuse of download endpoint

---

## Health Monitoring Endpoints

### Overall System Health

Check comprehensive system health status.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 86400,
  "version": "1.0.0",
  "components": {
    "database": {
      "status": "healthy",
      "response_time_ms": 12,
      "connections": {
        "active": 5,
        "pool_size": 20
      }
    },
    "redis": {
      "status": "healthy",
      "response_time_ms": 3,
      "memory_usage": "45MB",
      "connected_clients": 8
    },
    "azure_api": {
      "status": "healthy",
      "response_time_ms": 150,
      "last_check": "2024-01-15T10:29:45Z"
    },
    "queue": {
      "status": "healthy",
      "active_jobs": 12,
      "pending_jobs": 3,
      "workers": 4
    },
    "disk_space": {
      "status": "healthy",
      "free_space_gb": 45.2,
      "usage_percent": 72
    }
  }
}
```

**Health Status Values:**
- `healthy` - All components operational
- `degraded` - Some components have issues but system functional
- `unhealthy` - Critical components down, system may not function

---

### Database Health

Check database connectivity and performance.

**Endpoint:** `GET /health/database`

**Response:**
```json
{
  "status": "healthy",
  "response_time_ms": 12,
  "connection_pool": {
    "active_connections": 5,
    "pool_size": 20,
    "max_overflow": 30
  },
  "recent_queries": {
    "average_time_ms": 8.5,
    "slow_queries": 0
  },
  "tables": {
    "video_jobs": {
      "row_count": 1547,
      "size_mb": 12.3
    }
  }
}
```

---

### Azure API Health

Check Azure OpenAI API connectivity.

**Endpoint:** `GET /health/azure`

**Response:**
```json
{
  "status": "healthy",
  "response_time_ms": 150,
  "endpoint": "https://your-resource.openai.azure.com/",
  "api_version": "2024-02-15-preview",
  "last_successful_call": "2024-01-15T10:29:45Z",
  "rate_limit": {
    "requests_remaining": 95,
    "reset_time": "2024-01-15T10:31:00Z"
  }
}
```

---

## Queue Management Endpoints

### Queue Status

Get current queue status for user's session.

**Endpoint:** `GET /queue/status`

**Response:**
```json
{
  "session_id": "abc123def456",
  "active_jobs": 2,
  "pending_jobs": 1,
  "max_concurrent_jobs": 3,
  "queue_position": 5,
  "estimated_wait_time_seconds": 120,
  "jobs": [
    {
      "job_id": "job_abc123",
      "status": "running",
      "progress": 45,
      "started_at": "2024-01-15T10:30:00Z"
    },
    {
      "job_id": "job_def456",
      "status": "pending",
      "queue_position": 5,
      "estimated_start": "2024-01-15T10:32:00Z"
    }
  ]
}
```

---

### Global Queue Statistics

Get overall queue performance statistics.

**Endpoint:** `GET /queue/stats`

**Response:**
```json
{
  "global_stats": {
    "total_active_jobs": 45,
    "total_pending_jobs": 12,
    "total_workers": 8,
    "average_processing_time_seconds": 180,
    "queue_throughput_per_hour": 120
  },
  "performance_metrics": {
    "jobs_completed_today": 1247,
    "jobs_failed_today": 23,
    "success_rate_percent": 98.2,
    "average_wait_time_seconds": 45
  },
  "worker_stats": [
    {
      "worker_id": "worker_1",
      "status": "active",
      "current_job": "job_xyz789",
      "jobs_completed": 156
    }
  ]
}
```

---

## Session Management Endpoints

### Session Information

Get current session information and limits.

**Endpoint:** `GET /session/info`

**Response:**
```json
{
  "session_id": "abc123def456",
  "created_at": "2024-01-15T08:30:00Z",
  "expires_at": "2024-01-16T08:30:00Z",
  "client_ip": "*************",
  "limits": {
    "max_concurrent_jobs": 3,
    "current_active_jobs": 2,
    "rate_limit_per_minute": 60,
    "requests_remaining": 58
  },
  "usage_stats": {
    "total_jobs_created": 15,
    "total_videos_generated": 12,
    "total_processing_time_seconds": 1800
  }
}
```

---

## System Metrics Endpoints

### Comprehensive Metrics

Get detailed system performance metrics.

**Endpoint:** `GET /metrics`

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "system": {
    "cpu_usage_percent": 45.2,
    "memory_usage_percent": 72.8,
    "disk_usage_percent": 68.5,
    "load_average": [1.2, 1.1, 1.0]
  },
  "application": {
    "active_sessions": 25,
    "requests_per_second": 12.5,
    "average_response_time_ms": 85,
    "error_rate_percent": 0.2
  },
  "database": {
    "connections_active": 8,
    "queries_per_second": 45,
    "average_query_time_ms": 12
  },
  "queue": {
    "jobs_processed_per_hour": 120,
    "average_processing_time_seconds": 180,
    "worker_utilization_percent": 75
  }
}
```

---

### Job-Specific Metrics

Get detailed job processing metrics.

**Endpoint:** `GET /metrics/jobs`

**Response:**
```json
{
  "job_metrics": {
    "total_jobs_today": 1247,
    "completed_jobs_today": 1224,
    "failed_jobs_today": 23,
    "success_rate_percent": 98.2
  },
  "processing_times": {
    "average_seconds": 180,
    "median_seconds": 165,
    "p95_seconds": 280,
    "p99_seconds": 350
  },
  "failure_analysis": {
    "azure_api_timeouts": 15,
    "validation_errors": 5,
    "system_errors": 3
  },
  "resource_usage": {
    "average_cpu_per_job": 35.5,
    "average_memory_mb_per_job": 245,
    "peak_concurrent_jobs": 67
  }
}
```

---

## UI Configuration Endpoint

### UI Configuration

Get UI configuration constraints and defaults for parameter validation.

**Endpoint:** `GET /config`

**Response:**
```json
{
  "video_constraints": {
    "duration": {
      "min": 1,
      "max": 60,
      "default": 5,
      "unit": "seconds"
    },
    "resolution": {
      "presets": [
        {"name": "SD", "width": 854, "height": 480},
        {"name": "HD", "width": 1280, "height": 720},
        {"name": "Full HD", "width": 1920, "height": 1080}
      ],
      "custom_range": {
        "width": {"min": 480, "max": 1920},
        "height": {"min": 480, "max": 1080}
      }
    },
    "prompt": {
      "min_length": 1,
      "max_length": 500,
      "placeholder": "Describe the video you want to generate..."
    }
  },
  "session_limits": {
    "max_concurrent_jobs": 3,
    "rate_limit_per_minute": 60,
    "session_lifetime_hours": 24
  },
  "ui_settings": {
    "polling_interval_seconds": 5,
    "auto_refresh_enabled": true,
    "progress_animation": true
  }
}
```

---

## WebSocket Real-Time Events

### Connection Setup

Connect to WebSocket for real-time job updates.

**WebSocket URL:** `ws://localhost:5001/socket.io/`

**Connection Event:**
```javascript
// Client-side connection
const socket = io('http://localhost:5001');

socket.on('connect', function() {
    console.log('Connected to WebSocket');
    
    // Join session room for targeted updates
    socket.emit('join_session', {
        session_id: 'abc123def456'
    });
});
```

### Job Status Updates

Receive real-time job status updates.

**Event:** `job_status_update`

**Payload:**
```json
{
  "job_id": "job_abc123def456",
  "status": "running",
  "progress": 65,
  "message": "Processing video frames...",
  "updated_at": "2024-01-15T10:32:45Z",
  "estimated_completion": "2024-01-15T10:34:30Z"
}
```

### Job Completion

Receive notification when job completes.

**Event:** `job_completed`

**Payload:**
```json
{
  "job_id": "job_abc123def456",
  "status": "succeeded",
  "download_url": "/download/job_abc123def456",
  "stream_url": "/video/job_abc123def456",
  "file_size": 15728640,
  "duration": 10.5,
  "completed_at": "2024-01-15T10:34:15Z"
}
```

### Queue Updates

Receive updates about queue position changes.

**Event:** `queue_update`

**Payload:**
```json
{
  "session_id": "abc123def456",
  "queue_position": 3,
  "estimated_wait_time_seconds": 90,
  "ahead_in_queue": 2,
  "updated_at": "2024-01-15T10:33:00Z"
}
```

---

## Error Handling

### HTTP Status Codes

**Success Codes:**
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `206 Partial Content` - Partial content for range requests

**Client Error Codes:**
- `400 Bad Request` - Invalid request format or parameters
- `401 Unauthorized` - Invalid or missing session
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., too many concurrent jobs)
- `413 Payload Too Large` - Request body too large
- `429 Too Many Requests` - Rate limit exceeded

**Server Error Codes:**
- `500 Internal Server Error` - Unexpected server error
- `502 Bad Gateway` - External service error (Azure API)
- `503 Service Unavailable` - Service temporarily unavailable
- `504 Gateway Timeout` - External service timeout

### Error Response Format

**Standard Error Response:**
```json
{
  "error": true,
  "type": "validation",
  "message": "Invalid video duration",
  "details": {
    "field": "duration",
    "provided_value": 120,
    "max_allowed": 60,
    "constraint": "duration must be between 1 and 60 seconds"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123def456"
}
```

**Error Types:**
- `validation` - Input validation failed
- `authentication` - Authentication/session error
- `authorization` - Permission denied
- `rate_limit` - Rate limit exceeded
- `resource_not_found` - Requested resource not found
- `external_api_error` - Azure API error
- `service_unavailable` - Service temporarily down
- `internal_error` - Unexpected system error

### Rate Limiting Headers

Rate limit information included in response headers:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642248600
X-RateLimit-Retry-After: 30
```

---

## Rate Limiting

### Session-Level Limits
- **Concurrent Jobs**: 3 active jobs per session
- **Request Rate**: 60 requests per minute per session
- **Session Lifetime**: 24 hours with automatic renewal

### Global Limits
- **API Rate**: 10 requests per second across all users
- **Worker Capacity**: Maximum concurrent processing based on available workers
- **Resource Limits**: CPU and memory usage thresholds

### Rate Limit Strategies
- **Sliding Window**: Tracks requests over rolling time periods
- **Token Bucket**: Allows burst requests up to bucket capacity
- **Adaptive**: Adjusts limits based on system load and performance

---

## API Usage Examples

### Complete Video Generation Workflow

```javascript
// 1. Generate video
const response = await fetch('/generate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': sessionId
    },
    body: JSON.stringify({
        prompt: 'A cat playing in a garden',
        duration: 10,
        width: 1280,
        height: 720
    })
});

const job = await response.json();
console.log('Job created:', job.job_id);

// 2. Poll for completion
const pollStatus = async (jobId) => {
    const statusResponse = await fetch(`/status/${jobId}`);
    const status = await statusResponse.json();
    
    if (status.status === 'succeeded') {
        console.log('Video ready:', status.download_url);
        return status;
    } else if (status.status === 'failed') {
        console.error('Generation failed:', status.error);
        return status;
    }
    
    // Continue polling
    setTimeout(() => pollStatus(jobId), 5000);
};

pollStatus(job.job_id);

// 3. Download completed video
const downloadVideo = async (jobId) => {
    const downloadResponse = await fetch(`/download/${jobId}`);
    const blob = await downloadResponse.blob();
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `video_${jobId}.mp4`;
    a.click();
};
```

### Real-Time Updates with WebSocket

```javascript
// Connect to WebSocket
const socket = io('http://localhost:5001');

socket.on('connect', () => {
    socket.emit('join_session', { session_id: sessionId });
});

// Handle real-time job updates
socket.on('job_status_update', (data) => {
    updateJobProgress(data.job_id, data.progress, data.message);
});

socket.on('job_completed', (data) => {
    showCompletionNotification(data.job_id, data.download_url);
});

socket.on('queue_update', (data) => {
    updateQueuePosition(data.queue_position, data.estimated_wait_time_seconds);
});
```

### Error Handling Best Practices

```javascript
const handleApiRequest = async (url, options) => {
    try {
        const response = await fetch(url, options);
        
        if (!response.ok) {
            const error = await response.json();
            
            switch (error.type) {
                case 'rate_limit':
                    showMessage('Rate limit exceeded. Please wait before making more requests.');
                    break;
                case 'validation':
                    showValidationError(error.details);
                    break;
                case 'external_api_error':
                    showMessage('Video generation service temporarily unavailable.');
                    break;
                default:
                    showMessage('An unexpected error occurred. Please try again.');
            }
            
            throw new Error(error.message);
        }
        
        return response.json();
    } catch (networkError) {
        showMessage('Network error. Please check your connection.');
        throw networkError;
    }
};
```

This comprehensive API reference provides all necessary information for integrating with the multi-user video generation system, including authentication, rate limiting, real-time updates, and proper error handling.