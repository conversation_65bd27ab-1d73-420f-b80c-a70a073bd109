# Configuration Guide - Environment Variables and System Configuration

## Overview

Comprehensive configuration guide for the multi-user video generation system. This document covers all environment variables, configuration patterns, security settings, and environment-specific configurations for development, testing, and production deployments.

## Environment Configuration Structure

### Configuration Hierarchy
```
Environment Variables (.env) 
    ↓
EnvironmentConfig (Pydantic validation)
    ↓  
ConfigurationFactory (Environment-specific logic)
    ↓
Application Components (Dependency injection)
```

### Factory Pattern Implementation
```python
# Universal configuration management pattern
class ConfigurationFactory:
    """Factory for environment-specific configurations."""
    
    @staticmethod
    def get_environment_config() -> EnvironmentConfig:
        environment = os.getenv('FLASK_ENV', 'development')
        
        if environment == 'production':
            return ProductionConfig()
        elif environment == 'testing':
            return TestingConfig()
        else:
            return DevelopmentConfig()
```

## Core Environment Variables

### Application Configuration
```bash
# Application Environment
FLASK_ENV=development              # development, testing, production
SECRET_KEY=your-secure-secret-key  # 64-character secure random string
DEBUG=false                        # NEVER true in production

# Server Configuration
HOST=0.0.0.0                      # Bind address
PORT=5001                         # Application port
WORKERS=4                         # Number of worker processes
```

### Database Configuration
```bash
# Database Connection
DATABASE_URL=sqlite:///sora_poc.db # Development: SQLite
# DATABASE_URL=postgresql://user:pass@localhost:5432/sora_prod  # Production: PostgreSQL

# Connection Pool Settings
DB_POOL_SIZE=20                   # Connection pool size
DB_MAX_OVERFLOW=30                # Maximum overflow connections
DB_POOL_RECYCLE=3600              # Connection recycle time (seconds)
DB_POOL_PRE_PING=true            # Validate connections before use
SQL_DEBUG=false                   # Enable SQL query logging
```

### Redis Configuration
```bash
# Redis Connection
REDIS_HOST=localhost              # Redis server hostname
REDIS_PORT=6379                   # Redis server port
REDIS_DB=0                        # Redis database number
REDIS_PASSWORD=                   # Redis password (if required)
REDIS_SSL=false                   # Enable SSL/TLS for Redis

# Redis Pool Settings
REDIS_MAX_CONNECTIONS=50          # Maximum Redis connections
REDIS_RETRY_ON_TIMEOUT=true       # Retry on connection timeout
REDIS_SOCKET_TIMEOUT=5            # Socket timeout (seconds)
```

### Azure OpenAI Configuration
```bash
# Azure OpenAI Service
AZURE_OPENAI_API_KEY=your_api_key              # Required: Azure OpenAI API key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/  # Required: Service endpoint
AZURE_OPENAI_API_VERSION=2024-02-15-preview    # API version
AZURE_OPENAI_SORA_DEPLOYMENT=sora              # Sora model deployment name

# API Configuration
AZURE_OPENAI_TIMEOUT=300          # Request timeout (seconds)
AZURE_OPENAI_MAX_RETRIES=3        # Maximum retry attempts
AZURE_OPENAI_RETRY_DELAY=1        # Delay between retries (seconds)
```

### Video Generation Settings
```bash
# Video Constraints
MAX_PROMPT_LENGTH=500             # Maximum prompt length (characters)
DEFAULT_VIDEO_DURATION=5          # Default video duration (seconds)
MAX_VIDEO_DURATION=60             # Maximum video duration (seconds)
MIN_VIDEO_DURATION=1              # Minimum video duration (seconds)

# Resolution Settings
DEFAULT_WIDTH=1280                # Default video width (pixels)
DEFAULT_HEIGHT=720                # Default video height (pixels)
MAX_WIDTH=1920                    # Maximum video width (pixels)
MAX_HEIGHT=1080                   # Maximum video height (pixels)
MIN_WIDTH=480                     # Minimum video width (pixels)
MIN_HEIGHT=480                    # Minimum video height (pixels)

# Supported resolution presets (Azure Sora API requirements)
# (480,480), (854,480), (720,720), (1280,720), (1080,1080), (1920,1080)
```

### Multi-User Configuration
```bash
# Session Management
SESSION_LIFETIME_HOURS=24         # Session expiration time
SESSION_CLEANUP_INTERVAL_HOURS=6  # Session cleanup frequency
MAX_SESSIONS_PER_IP=10            # Maximum sessions per IP address

# Job Queue Management
MAX_CONCURRENT_JOBS_PER_SESSION=3 # Maximum concurrent jobs per user
QUEUE_PRIORITY_ENABLED=true       # Enable job priority queuing
JOB_TIMEOUT_MINUTES=30            # Job processing timeout

# Rate Limiting
RATE_LIMIT_ENABLED=true           # Enable rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60 # User rate limit
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=10  # Global rate limit
```

### Celery Configuration
```bash
# Celery Broker and Backend
CELERY_BROKER_URL=redis://localhost:6379/0      # Message broker URL
CELERY_RESULT_BACKEND=redis://localhost:6379/0  # Result backend URL

# Worker Configuration
CELERY_WORKER_CONCURRENCY=4       # Worker concurrency level
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000  # Tasks before worker restart
CELERY_WORKER_DISABLE_RATE_LIMITS=false # Disable rate limiting

# Task Configuration
CELERY_TASK_SERIALIZER=json       # Task serialization format
CELERY_RESULT_SERIALIZER=json     # Result serialization format
CELERY_ACCEPT_CONTENT=json        # Accepted content types
CELERY_TIMEZONE=UTC               # Celery timezone
CELERY_ENABLE_UTC=true            # Enable UTC timestamps

# Task Routing
CELERY_ROUTES={
    'src.job_queue.tasks.process_video_generation': {'queue': 'video_processing'},
    'src.job_queue.tasks.cleanup_old_files': {'queue': 'maintenance'}
}
```

### File Management Configuration
```bash
# File Storage
UPLOAD_FOLDER=uploads             # Upload directory path
MAX_CONTENT_LENGTH=*********      # Maximum file size (100MB in bytes)
ALLOWED_EXTENSIONS=mp4,avi,mov    # Allowed file extensions

# File Cleanup
FILE_CLEANUP_ENABLED=true        # Enable automatic file cleanup
FILE_MAX_AGE_HOURS=24            # File retention period
CLEANUP_INTERVAL_HOURS=6         # Cleanup frequency

# Cloud Storage (Optional)
USE_CLOUD_STORAGE=false          # Enable cloud storage
CLOUD_STORAGE_PROVIDER=s3        # Cloud storage provider (s3, azure, gcs)
CLOUD_STORAGE_BUCKET=sora-poc    # Storage bucket name
CLOUD_STORAGE_REGION=us-east-1   # Storage region
```

### Monitoring and Logging
```bash
# Logging Configuration
LOG_LEVEL=INFO                   # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_FORMAT=json                  # Log format (json, text)
LOG_FILE=logs/app.log           # Log file path
LOG_MAX_SIZE=10MB               # Maximum log file size
LOG_BACKUP_COUNT=5              # Number of backup log files

# Health Checks
HEALTH_CHECK_ENABLED=true       # Enable health check endpoints
HEALTH_CHECK_TIMEOUT=30         # Health check timeout (seconds)

# Metrics
METRICS_ENABLED=true            # Enable metrics collection
METRICS_PORT=8080               # Metrics server port
METRICS_PATH=/metrics           # Metrics endpoint path

# External Monitoring
SENTRY_DSN=                     # Sentry error tracking DSN
DATADOG_API_KEY=                # Datadog monitoring API key
NEW_RELIC_LICENSE_KEY=          # New Relic monitoring license key
```

### Security Configuration
```bash
# CORS Settings
CORS_ENABLED=true               # Enable CORS
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com  # Allowed origins
CORS_METHODS=GET,POST,PUT,DELETE # Allowed methods
CORS_HEADERS=Content-Type,Authorization  # Allowed headers

# Security Headers
SECURITY_HEADERS_ENABLED=true   # Enable security headers
CSP_ENABLED=true                # Enable Content Security Policy
HSTS_ENABLED=true               # Enable HTTP Strict Transport Security
X_FRAME_OPTIONS=DENY            # X-Frame-Options header

# SSL/TLS Configuration
SSL_DISABLE=false               # Disable SSL verification (development only)
SSL_CERT_PATH=                  # SSL certificate path
SSL_KEY_PATH=                   # SSL private key path

# Authentication
AUTH_ENABLED=false              # Enable authentication (future feature)
JWT_SECRET_KEY=                 # JWT signing key
JWT_EXPIRATION_HOURS=24         # JWT token expiration

# IP Filtering
TRUSTED_PROXIES=               # Trusted proxy IP addresses
BLOCKED_IPS=                   # Blocked IP addresses
ALLOWED_IPS=                   # Allowed IP addresses (if whitelist mode)
```

## Environment-Specific Configurations

### Development Configuration
```bash
# .env.development
FLASK_ENV=development
DEBUG=true
SECRET_KEY=dev-secret-key-not-for-production

# Database
DATABASE_URL=sqlite:///sora_poc.db
SQL_DEBUG=true

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Azure OpenAI (Development)
AZURE_OPENAI_API_KEY=your_dev_api_key
AZURE_OPENAI_ENDPOINT=https://your-dev-resource.openai.azure.com/

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=text

# File Storage
UPLOAD_FOLDER=uploads
FILE_CLEANUP_ENABLED=false

# Rate Limiting (Relaxed)
RATE_LIMIT_REQUESTS_PER_MINUTE=120
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=20

# Sessions (Shorter for testing)
SESSION_LIFETIME_HOURS=2
MAX_SESSIONS_PER_IP=20

# CORS (Development)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

### Testing Configuration
```bash
# .env.testing
FLASK_ENV=testing
DEBUG=false
SECRET_KEY=test-secret-key

# Database (In-memory)
DATABASE_URL=sqlite:///:memory:
SQL_DEBUG=false

# Redis (Test instance)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1

# Azure OpenAI (Mock or test instance)
AZURE_OPENAI_API_KEY=test_api_key
AZURE_OPENAI_ENDPOINT=https://test.openai.azure.com/

# Logging (Minimal)
LOG_LEVEL=WARNING
LOG_FORMAT=json

# File Storage (Temporary)
UPLOAD_FOLDER=/tmp/test_uploads
FILE_CLEANUP_ENABLED=true
FILE_MAX_AGE_HOURS=1

# Rate Limiting (Disabled for testing)
RATE_LIMIT_ENABLED=false

# Sessions (Short-lived)
SESSION_LIFETIME_HOURS=1
MAX_SESSIONS_PER_IP=50

# Celery (Eager execution)
CELERY_TASK_ALWAYS_EAGER=true
CELERY_TASK_EAGER_PROPAGATES=true
```

### Production Configuration
```bash
# .env.production
FLASK_ENV=production
DEBUG=false
SECRET_KEY=${PRODUCTION_SECRET_KEY}  # From secrets management

# Database (PostgreSQL)
DATABASE_URL=${PRODUCTION_DATABASE_URL}
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600
SQL_DEBUG=false

# Redis (Cluster or managed service)
REDIS_HOST=${REDIS_CLUSTER_ENDPOINT}
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_SSL=true

# Azure OpenAI (Production)
AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}

# Logging (Structured)
LOG_LEVEL=INFO
LOG_FORMAT=json
SENTRY_DSN=${SENTRY_DSN}

# File Storage (Cloud)
USE_CLOUD_STORAGE=true
CLOUD_STORAGE_PROVIDER=s3
CLOUD_STORAGE_BUCKET=${S3_BUCKET_NAME}
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}

# Rate Limiting (Strict)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
GLOBAL_RATE_LIMIT_REQUESTS_PER_SECOND=10

# Sessions (Standard)
SESSION_LIFETIME_HOURS=24
MAX_SESSIONS_PER_IP=10

# Security
CORS_ORIGINS=${ALLOWED_ORIGINS}
SECURITY_HEADERS_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/sora.crt
SSL_KEY_PATH=/etc/ssl/private/sora.key

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
DATADOG_API_KEY=${DATADOG_API_KEY}
```

## Configuration Validation

### Safe Environment Variable Parsing
```python
# src/config/environments.py
import os
import logging
from typing import Any, Callable, Union

logger = logging.getLogger(__name__)

def safe_int_from_env(key: str, default: int, min_val: int = None, max_val: int = None) -> int:
    """
    Safely parse integer from environment with validation and logging.
    
    Args:
        key: Environment variable key
        default: Default value if parsing fails
        min_val: Minimum allowed value
        max_val: Maximum allowed value
        
    Returns:
        Validated integer value
    """
    try:
        value = int(os.getenv(key, str(default)))
        
        # Range validation
        if min_val is not None and value < min_val:
            logger.warning(f"{key}={value} below minimum {min_val}, using {min_val}")
            return min_val
            
        if max_val is not None and value > max_val:
            logger.warning(f"{key}={value} above maximum {max_val}, using {max_val}")
            return max_val
            
        return value
    except ValueError as e:
        logger.error(f"Invalid {key} environment variable: {e}, using default {default}")
        return default

def safe_bool_from_env(key: str, default: bool = False) -> bool:
    """Safely parse boolean from environment."""
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')

def safe_float_from_env(key: str, default: float, min_val: float = None, max_val: float = None) -> float:
    """Safely parse float from environment with validation."""
    try:
        value = float(os.getenv(key, str(default)))
        
        if min_val is not None and value < min_val:
            logger.warning(f"{key}={value} below minimum {min_val}, using {min_val}")
            return min_val
            
        if max_val is not None and value > max_val:
            logger.warning(f"{key}={value} above maximum {max_val}, using {max_val}")
            return max_val
            
        return value
    except ValueError as e:
        logger.error(f"Invalid {key} environment variable: {e}, using default {default}")
        return default

def get_list_from_env(key: str, default: list = None, separator: str = ',') -> list:
    """Parse comma-separated list from environment variable."""
    if default is None:
        default = []
    
    value = os.getenv(key, '')
    if not value:
        return default
    
    return [item.strip() for item in value.split(separator) if item.strip()]
```

### Configuration Classes
```python
# src/config/environments.py
from pydantic import BaseModel, Field, validator
from typing import Optional, List

class BaseConfig(BaseModel):
    """Base configuration with common settings."""
    
    # Application
    flask_env: str = Field(default="development")
    secret_key: str = Field(..., min_length=32)
    debug: bool = Field(default=False)
    
    # Database
    database_url: str = Field(...)
    db_pool_size: int = Field(default=5, ge=1, le=100)
    db_max_overflow: int = Field(default=10, ge=0, le=100)
    sql_debug: bool = Field(default=False)
    
    # Redis
    redis_host: str = Field(default="localhost")
    redis_port: int = Field(default=6379, ge=1, le=65535)
    redis_db: int = Field(default=0, ge=0, le=15)
    redis_password: Optional[str] = None
    
    # Azure OpenAI
    azure_openai_api_key: str = Field(...)
    azure_openai_endpoint: str = Field(...)
    azure_openai_api_version: str = Field(default="2024-02-15-preview")
    
    # Video Generation
    max_prompt_length: int = Field(default=500, ge=1, le=2000)
    default_video_duration: int = Field(default=5, ge=1, le=60)
    max_video_duration: int = Field(default=60, ge=1, le=300)
    
    # Multi-User
    max_concurrent_jobs_per_session: int = Field(default=3, ge=1, le=10)
    session_lifetime_hours: int = Field(default=24, ge=1, le=168)
    max_sessions_per_ip: int = Field(default=10, ge=1, le=100)
    
    # Rate Limiting
    rate_limit_enabled: bool = Field(default=True)
    rate_limit_requests_per_minute: int = Field(default=60, ge=1, le=1000)
    
    # File Management
    upload_folder: str = Field(default="uploads")
    max_content_length: int = Field(default=*********, ge=1024)  # 100MB
    file_cleanup_enabled: bool = Field(default=True)
    file_max_age_hours: int = Field(default=24, ge=1, le=8760)
    
    # Logging
    log_level: str = Field(default="INFO")
    log_format: str = Field(default="json")
    
    @validator('azure_openai_endpoint')
    def validate_endpoint_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Azure endpoint must be a valid URL')
        return v.rstrip('/')
    
    @validator('secret_key')
    def validate_secret_key_length(cls, v):
        if len(v) < 32:
            raise ValueError('Secret key must be at least 32 characters long')
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()

class DevelopmentConfig(BaseConfig):
    """Development environment configuration."""
    
    flask_env: str = "development"
    debug: bool = True
    sql_debug: bool = True
    log_level: str = "DEBUG"
    file_cleanup_enabled: bool = False
    rate_limit_requests_per_minute: int = 120  # More relaxed

class TestingConfig(BaseConfig):
    """Testing environment configuration."""
    
    flask_env: str = "testing"
    debug: bool = False
    database_url: str = "sqlite:///:memory:"
    redis_db: int = 1  # Separate Redis DB for tests
    log_level: str = "WARNING"
    file_cleanup_enabled: bool = True
    file_max_age_hours: int = 1
    rate_limit_enabled: bool = False
    session_lifetime_hours: int = 1

class ProductionConfig(BaseConfig):
    """Production environment configuration."""
    
    flask_env: str = "production"
    debug: bool = False
    sql_debug: bool = False
    db_pool_size: int = 20
    db_max_overflow: int = 30
    log_level: str = "INFO"
    rate_limit_enabled: bool = True
```

### Configuration Loading
```python
# src/config/factory.py
import os
from typing import Type, Dict, Any
from .environments import BaseConfig, DevelopmentConfig, TestingConfig, ProductionConfig

class ConfigurationFactory:
    """Factory for creating environment-specific configurations."""
    
    _config_classes: Dict[str, Type[BaseConfig]] = {
        'development': DevelopmentConfig,
        'testing': TestingConfig,
        'production': ProductionConfig
    }
    
    @classmethod
    def create_config(cls, environment: str = None) -> BaseConfig:
        """
        Create configuration for specified environment.
        
        Args:
            environment: Environment name (development, testing, production)
            
        Returns:
            Configuration instance for the environment
        """
        if environment is None:
            environment = os.getenv('FLASK_ENV', 'development')
        
        config_class = cls._config_classes.get(environment, DevelopmentConfig)
        
        # Load from environment variables
        config_data = cls._load_from_environment()
        
        try:
            return config_class(**config_data)
        except Exception as e:
            raise ValueError(f"Invalid configuration for {environment}: {e}")
    
    @classmethod
    def _load_from_environment(cls) -> Dict[str, Any]:
        """Load configuration values from environment variables."""
        return {
            # Application
            'flask_env': os.getenv('FLASK_ENV', 'development'),
            'secret_key': os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production'),
            'debug': safe_bool_from_env('DEBUG', False),
            
            # Database
            'database_url': os.getenv('DATABASE_URL', 'sqlite:///sora_poc.db'),
            'db_pool_size': safe_int_from_env('DB_POOL_SIZE', 5, 1, 100),
            'db_max_overflow': safe_int_from_env('DB_MAX_OVERFLOW', 10, 0, 100),
            'sql_debug': safe_bool_from_env('SQL_DEBUG', False),
            
            # Redis
            'redis_host': os.getenv('REDIS_HOST', 'localhost'),
            'redis_port': safe_int_from_env('REDIS_PORT', 6379, 1, 65535),
            'redis_db': safe_int_from_env('REDIS_DB', 0, 0, 15),
            'redis_password': os.getenv('REDIS_PASSWORD'),
            
            # Azure OpenAI
            'azure_openai_api_key': os.getenv('AZURE_OPENAI_API_KEY', ''),
            'azure_openai_endpoint': os.getenv('AZURE_OPENAI_ENDPOINT', ''),
            'azure_openai_api_version': os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview'),
            
            # Video Generation
            'max_prompt_length': safe_int_from_env('MAX_PROMPT_LENGTH', 500, 1, 2000),
            'default_video_duration': safe_int_from_env('DEFAULT_VIDEO_DURATION', 5, 1, 60),
            'max_video_duration': safe_int_from_env('MAX_VIDEO_DURATION', 60, 1, 300),
            
            # Multi-User
            'max_concurrent_jobs_per_session': safe_int_from_env('MAX_CONCURRENT_JOBS_PER_SESSION', 3, 1, 10),
            'session_lifetime_hours': safe_int_from_env('SESSION_LIFETIME_HOURS', 24, 1, 168),
            'max_sessions_per_ip': safe_int_from_env('MAX_SESSIONS_PER_IP', 10, 1, 100),
            
            # Rate Limiting
            'rate_limit_enabled': safe_bool_from_env('RATE_LIMIT_ENABLED', True),
            'rate_limit_requests_per_minute': safe_int_from_env('RATE_LIMIT_REQUESTS_PER_MINUTE', 60, 1, 1000),
            
            # File Management
            'upload_folder': os.getenv('UPLOAD_FOLDER', 'uploads'),
            'max_content_length': safe_int_from_env('MAX_CONTENT_LENGTH', *********, 1024),
            'file_cleanup_enabled': safe_bool_from_env('FILE_CLEANUP_ENABLED', True),
            'file_max_age_hours': safe_int_from_env('FILE_MAX_AGE_HOURS', 24, 1, 8760),
            
            # Logging
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            'log_format': os.getenv('LOG_FORMAT', 'json')
        }

# Cached configuration getter
@lru_cache(maxsize=1)
def get_cached_config() -> BaseConfig:
    """Get cached configuration instance."""
    return ConfigurationFactory.create_config()
```

## Configuration Validation Scripts

### Environment Validation Script
```python
#!/usr/bin/env python3
# scripts/validate_config.py
"""Configuration validation script."""

import os
import sys
from typing import List, Dict, Any

class ConfigValidator:
    """Validate configuration for different environments."""
    
    REQUIRED_VARS = {
        'all': [
            'SECRET_KEY',
            'AZURE_OPENAI_API_KEY',
            'AZURE_OPENAI_ENDPOINT'
        ],
        'production': [
            'DATABASE_URL',
            'REDIS_PASSWORD',
            'SENTRY_DSN'
        ],
        'development': [
            'DATABASE_URL'
        ]
    }
    
    def validate(self, environment: str = None) -> Dict[str, List[str]]:
        """Validate configuration for environment."""
        environment = environment or os.getenv('FLASK_ENV', 'development')
        
        results = {
            'missing': [],
            'invalid': [],
            'warnings': []
        }
        
        # Check required variables
        required_vars = self.REQUIRED_VARS['all'] + self.REQUIRED_VARS.get(environment, [])
        
        for var in required_vars:
            if not os.getenv(var):
                results['missing'].append(var)
        
        # Validate specific settings
        self._validate_secret_key(results)
        self._validate_database_url(results)
        self._validate_azure_config(results)
        self._validate_security_settings(results, environment)
        
        return results
    
    def _validate_secret_key(self, results: Dict[str, List[str]]):
        """Validate secret key strength."""
        secret_key = os.getenv('SECRET_KEY', '')
        
        if len(secret_key) < 32:
            results['invalid'].append('SECRET_KEY too short (minimum 32 characters)')
        
        if secret_key in ['dev-secret-key', 'change-me', 'your-secret-key']:
            results['invalid'].append('SECRET_KEY is using default/example value')
    
    def _validate_database_url(self, results: Dict[str, List[str]]):
        """Validate database URL format."""
        db_url = os.getenv('DATABASE_URL', '')
        
        if not db_url:
            return
        
        if not db_url.startswith(('sqlite:///', 'postgresql://', 'mysql://')):
            results['invalid'].append('DATABASE_URL has invalid format')
        
        if 'localhost' in db_url and os.getenv('FLASK_ENV') == 'production':
            results['warnings'].append('Using localhost database in production')
    
    def _validate_azure_config(self, results: Dict[str, List[str]]):
        """Validate Azure OpenAI configuration."""
        api_key = os.getenv('AZURE_OPENAI_API_KEY', '')
        endpoint = os.getenv('AZURE_OPENAI_ENDPOINT', '')
        
        if api_key and not api_key.startswith('sk-'):
            results['warnings'].append('AZURE_OPENAI_API_KEY format may be incorrect')
        
        if endpoint and not endpoint.startswith('https://'):
            results['invalid'].append('AZURE_OPENAI_ENDPOINT must use HTTPS')
    
    def _validate_security_settings(self, results: Dict[str, List[str]], environment: str):
        """Validate security settings."""
        if environment == 'production':
            if os.getenv('DEBUG', '').lower() == 'true':
                results['invalid'].append('DEBUG=true in production environment')
            
            if not os.getenv('REDIS_PASSWORD'):
                results['warnings'].append('Redis password not set in production')
            
            if not os.getenv('CORS_ORIGINS'):
                results['warnings'].append('CORS origins not configured')

def main():
    """Main validation function."""
    validator = ConfigValidator()
    environment = sys.argv[1] if len(sys.argv) > 1 else None
    
    print(f"🔧 Configuration Validation for {environment or 'current environment'}")
    print("=" * 60)
    
    results = validator.validate(environment)
    
    if results['missing']:
        print("❌ Missing required variables:")
        for var in results['missing']:
            print(f"   - {var}")
        print()
    
    if results['invalid']:
        print("🚨 Invalid configuration:")
        for issue in results['invalid']:
            print(f"   - {issue}")
        print()
    
    if results['warnings']:
        print("⚠️  Configuration warnings:")
        for warning in results['warnings']:
            print(f"   - {warning}")
        print()
    
    if not any(results.values()):
        print("✅ Configuration validation passed!")
        return 0
    
    if results['missing'] or results['invalid']:
        print("❌ Configuration validation failed!")
        return 1
    
    print("⚠️  Configuration has warnings but is valid.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
```

### Configuration Generation Script
```bash
#!/bin/bash
# scripts/generate_config.sh
"""Generate secure configuration files."""

set -e

ENVIRONMENT=${1:-development}
OUTPUT_FILE=".env.${ENVIRONMENT}"

echo "🔧 Generating configuration for ${ENVIRONMENT} environment"

# Generate secure secrets
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(64))")
REDIS_PASSWORD=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")

# Create configuration file
cat > ${OUTPUT_FILE} << EOF
# ${ENVIRONMENT^} Environment Configuration
# Generated on $(date)

# Application Configuration
FLASK_ENV=${ENVIRONMENT}
SECRET_KEY=${SECRET_KEY}
DEBUG=$([ "$ENVIRONMENT" = "development" ] && echo "true" || echo "false")

# Database Configuration
DATABASE_URL=sqlite:///sora_poc.db
$([ "$ENVIRONMENT" = "production" ] && echo "# DATABASE_URL=postgresql://user:password@localhost:5432/sora_prod")

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
$([ "$ENVIRONMENT" = "production" ] && echo "REDIS_PASSWORD=${REDIS_PASSWORD}")

# Azure OpenAI Configuration (REQUIRED)
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Multi-User Configuration
MAX_CONCURRENT_JOBS_PER_SESSION=3
SESSION_LIFETIME_HOURS=24
MAX_SESSIONS_PER_IP=10

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# File Management
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=*********
FILE_CLEANUP_ENABLED=true
FILE_MAX_AGE_HOURS=24

# Logging
LOG_LEVEL=$([ "$ENVIRONMENT" = "development" ] && echo "DEBUG" || echo "INFO")
LOG_FORMAT=json

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
EOF

echo "✅ Configuration file created: ${OUTPUT_FILE}"
echo "📝 Please update the following variables with actual values:"
echo "   - AZURE_OPENAI_API_KEY"
echo "   - AZURE_OPENAI_ENDPOINT"
if [ "$ENVIRONMENT" = "production" ]; then
    echo "   - DATABASE_URL (for production database)"
    echo "   - CORS_ORIGINS"
    echo "   - SENTRY_DSN"
fi

echo ""
echo "🔒 Secure secrets have been generated automatically."
echo "⚠️  Keep this file secure and never commit it to version control!"
```

## Configuration Best Practices

### Environment Variable Security
1. **Never commit `.env` files** to version control
2. **Use different keys per environment** (dev, staging, prod)
3. **Rotate secrets regularly** in production
4. **Use secrets management** services in production (AWS Secrets Manager, Azure Key Vault)
5. **Validate configuration** on application startup

### Configuration Management
1. **Use factory pattern** for environment-specific configs
2. **Validate all inputs** with Pydantic models
3. **Provide sensible defaults** for optional settings
4. **Document all variables** and their purposes
5. **Use type hints** for all configuration classes

### Production Security
1. **Use strong random secrets** (64+ characters)
2. **Enable security headers** and SSL/TLS
3. **Configure CORS properly** for your domain
4. **Set up monitoring** and alerting
5. **Use managed services** for databases and caches

This comprehensive configuration guide ensures proper setup and security across all environments while maintaining flexibility for different deployment scenarios.